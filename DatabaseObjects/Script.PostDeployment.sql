/*
Post-Deployment Script Template							
--------------------------------------------------------------------------------------
 This file contains SQL statements that will be appended to the build script.		
 Use SQLCMD syntax to include a file in the post-deployment script.			
 Example:      :r .\myfile.sql								
 Use SQLCMD syntax to reference a variable in the post-deployment script.		
 Example:      :setvar TableName MyTable							
               SELECT * FROM [$(TableName)]					
--------------------------------------------------------------------------------------
*/
:r .\Data\Default\CompanyDocumentDefaultFields.sql
:r .\Data\Default\ControlsAndVariances.sql
:r .\Data\Default\DarwinColumnMap.sql
:r .\Data\Default\DarwiNetCodes.sql
:r .\Data\Default\DarwiNetNotifications.sql
:r .\Data\Default\DarwinTableMap.sql
:r .\Data\Default\Languages.sql
:r .\Data\Default\Library.sql
:r .\Data\Default\Menuitems.sql
:r .\Data\Default\OnBoardingDocumentDefaultFields.sql
:r .\Data\Default\OnBoardingDocumentMapping.sql
:r .\Data\Default\OnBoardingDocuments.sql
:r .\Data\Default\OnBoardingFields.sql
:r .\Data\Default\OnBoardingFinalizeSections.sql
:r .\Data\Default\OnBoardingNotifications.sql
:r .\Data\Default\OnBoardingStateW4Rules.sql
:r .\Data\Default\OnBoardingTasks.sql
:r .\Data\Default\OpenEnrollmentDataFields.sql
:r .\Data\Default\ProjectSetup.sql
:r .\Data\Default\ReportTables.sql
:r .\Data\Default\SecurityQuestions.sql
:r .\Data\Default\SmartTags.sql
:r .\Data\Default\SSNDisplayTypes.sql
:r .\Data\Default\SSRSReports.sql
:r .\Data\Default\TallyCalendar.sql
:r .\Data\Default\TaxFilingAdditionals.sql
:r .\Data\Default\TimeSheetColumnDefaults.sql
:r .\Data\Default\Translations.sql
:r .\Data\Default\UserRoleMenuAccess.sql
:r .\Data\Default\Users.sql
/* Always keep this script at the bottom of the list as it needs to run last */
:r .\Data\UpdateData.sql