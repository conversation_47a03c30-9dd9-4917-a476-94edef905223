CREATE TABLE [dbo].[OBEmployeeLocalTaxes] (
    [CompanyID]                  INT             NOT NULL,
    [EmployeeID]                 NVARCHAR (15)   NOT NULL,
    [LocalTax]                   NVARCHAR (6)    NOT NULL,
    [LocalFilingStatus]          NVARCHAR (6)    NULL,
    [AutomaticLocalTax]          BIT             CONSTRAINT [DF__OBEmploye__Autom__1FF970CB] DEFAULT ((0)) NOT NULL,
    [NumberOfExemptions]         INT             NULL,
    [Inactive]                   BIT             CONSTRAINT [DF__OBEmploye__Inact__20ED9504] DEFAULT ((0)) NOT NULL,
    [AdditionalLocalWithholding] DECIMAL (19, 5) CONSTRAINT [DF__OBEmploye__Addit__21E1B93D] DEFAULT ((0)) NOT NULL,
    [PRIMARYTAX]                 BIT             CONSTRAINT [DF__OBEmploye__PRIMA__22D5DD76] DEFAULT ((0)) NOT NULL,
    [ReciprocalTax]              BIT             CONSTRAINT [DF__OBEmploye__Recip__23CA01AF] DEFAULT ((0)) NOT NULL,
    [SourcePercent]              INT             NULL,
    [DestinationPercent]         INT             NULL,
    [AssignToHomeDepartment]     BIT             CONSTRAINT [DF__OBEmploye__Assig__24BE25E8] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_OBEmployeeLocalTaxes] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [LocalTax] ASC) WITH (FILLFACTOR = 80)
);

