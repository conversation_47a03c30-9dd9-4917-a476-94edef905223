CREATE TABLE [dbo].[OBImportEmployees] (
    [CompanyID]        INT            NOT NULL,
    [ID]               INT            IDENTITY (1, 1) NOT NULL,
    [ImportID]         INT            NULL,
    [SetupID]          INT            NULL,
    [ClientID]         NVARCHAR (15)  NULL,
    [LoginCode]        NVARCHAR (15)  NULL,
    [EmployeeID]       NVARCHAR (15)  NULL,
    [EmployeeClass]    NVARCHAR (15)  NULL,
    [FirstName]        NVARCHAR (15)  NULL,
    [LastName]         NVARCHAR (20)  NULL,
    [SSN]              NVARCHAR (15)  NULL,
    [BirthDate]        DATETIME       NULL,
    [Email]            NVARCHAR (255) NULL,
    [DivisionID]       NVARCHAR (15)  NULL,
    [Department]       NVARCHAR (6)   NULL,
    [Position]         NVARCHAR (6)   NULL,
    [EmploymentStatus] SMALLINT       NULL,
    [PayPeriod]        SMALLINT       NULL,
    [WorkState]        NVARCHAR (2)   NULL,
    [RoleID]           NVARCHAR (50)  NULL,
    [DueDate]          DAT<PERSON>IME       NULL,
    [WarningDate]      DATETIME       NULL,
    [Completed]        BIT            CONSTRAINT [DF_OBImportEmployees_Completed] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_OBImportEmployees] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ID] ASC) WITH (FILLFACTOR = 80)
);

