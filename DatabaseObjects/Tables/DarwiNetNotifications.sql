CREATE TABLE [dbo].[DarwiNetNotifications] (
    [CompanyID]    INT            NOT NULL,
    [Type]         SMALLINT       NOT NULL,
    [Enabled]      BIT            CONSTRAINT [DF_DarwiNetNotifications_Enabled] DEFAULT ((0)) NOT NULL,
    [EmailSubject] NVARCHAR (100) NULL,
    [EmailBody]    NVARCHAR (MAX) NULL,
    [<PERSON><PERSON>Filter] BIT            CONSTRAINT [DF_DarwiNetNotifications_ClientFilter] DEFAULT ((0)) NOT NULL,
    [SystemFilter] BIT            CONSTRAINT [DF_DarwiNetNotifications_SystemFilter] DEFAULT ((0)) NOT NULL,
    [EEAssign]     BIT            CONSTRAINT [DF_DarwiNetNotifications_EEAssign] DEFAULT ((0)) NOT NULL,
    [CCAssign]     BIT            CONSTRAINT [DF_DarwiNetNotifications_CCAssign] DEFAULT ((0)) NOT NULL,
    [SYAssign]     BIT            CONSTRAINT [DF_DarwiNetNotifications_SYAssign] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_DarwiNetNotifications_1] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [Type] ASC) WITH (FILLFACTOR = 80)
);

