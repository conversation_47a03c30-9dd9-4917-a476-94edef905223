CREATE TABLE [dbo].[ACHProfiles](
    [ACHProfileID]                   NVARCHAR (20)  NOT NULL,
    [ImmediateOrigin]                NVARCHAR (10)  NOT NULL,
    [ImmediateOriginName]            NVARCHAR (23)  NOT NULL,
    [ImmediateDestination]           NVARCHAR (10)  NOT NULL,
    [ImmediateDestinationName]       NVARCHAR (23)  NOT NULL,
    [NextBatchID]                    INT            NOT NULL,
    [NextExportID]                   INT            NOT NULL,
    [ACHFileLocation]                NVARCHAR (200) NULL,
    [ACHFileName]                    NVARCHAR (150) NULL,
    [ACHFileAutoIncrement]           BIT            CONSTRAINT [DF_ACHProfiles_ACHFileAutoIncrement] DEFAULT ((0)) NOT NULL,
    [GenerateWBDHdrTrailRecords]     BIT            CONSTRAINT [DF_ACHProfiles_GenerateWBDHdrTrailRecords] DEFAULT ((0)) NOT NULL,
    [WBDCustomerID]                  NVARCHAR (8)   NULL,
    [WBDAppID]                       NVARCHAR (10)  NULL,
    [WBDSequence]                    NVARCHAR (2)   NULL,
    [AutoRemovePreNoteFlag]          BIT            CONSTRAINT [DF_ACHProfiles_AutoRemovePreNoteFlag] DEFAULT ((0)) NOT NULL,
    [DepPayrollOffsetRecCheckbookID] NVARCHAR (15)  NULL,
    [RecOffsetRecordCheckbookID]     NVARCHAR (15)  NULL,
    [PayrollKey]                     NVARCHAR (1)   NULL,
    [ReceivableKey]                  NVARCHAR (1)   NULL,
    [PNCFormat]                      BIT            CONSTRAINT [DF_ACHProfiles_PNCFormat] DEFAULT ((0)) NOT NULL,
    [ChaseBank]                      BIT            CONSTRAINT [DF_ACHProfiles_ChaseBank] DEFAULT ((0)) NOT NULL,
    [ChaseAccount]                   NVARCHAR (20)  NULL,
	CONSTRAINT [PK_ACHProfiles] PRIMARY KEY CLUSTERED ([ACHProfileID] ASC) WITH (FILLFACTOR = 80)
);

GO
