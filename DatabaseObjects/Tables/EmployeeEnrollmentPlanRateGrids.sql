CREATE TABLE [dbo].[EmployeeEnrollmentPlanRateGrids] (
    [CompanyID]            INT             NOT NULL,
    [EmployeeID]           NVARCHAR (15)   NOT NULL,
    [PlanName]             NVARCHAR (50)   NOT NULL,
    [YEAR]                 SMALLINT        NOT NULL,
    [ClientID]             NVARCHAR (15)   NOT NULL,
    [PlanCategoriesNumber] INT             NOT NULL,
    [PlanAmount]           DECIMAL (19, 5) CONSTRAINT [DF__EmployeeE__PlanA__19FFD4FC] DEFAULT ((0.00)) NOT NULL,
    [PlanAgencyAmount]     DECIMAL (19, 5) CONSTRAINT [DF__EmployeeE__PlanA__1AF3F935] DEFAULT ((0.00)) NOT NULL,
    [PlanMonthlyCost]      DECIMAL (19, 5) CONSTRAINT [DF__EmployeeE__PlanM__1BE81D6E] DEFAULT ((0.00)) NOT NULL,
    [CoverageLevel]        NVARCHAR (50)   NULL,
    [Selected]             BIT             CONSTRAINT [DF__EmployeeE__Selec__1CDC41A7] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_EmployeeEnrollmentPlanRateGrids] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [PlanName] ASC, [YEAR] ASC, [ClientID] ASC, [PlanCategoriesNumber] ASC) WITH (FILLFACTOR = 80)
);

