CREATE TABLE [dbo].[EmployeeCustomInfo] (
    [CompanyID]     INT            NOT NULL,
    [EmployeeID]    NVARCHAR (15)  NOT NULL,
    [CustomSection] NVARCHAR (30)  NOT NULL,
    [FldName]       NVARCHAR (100) NOT NULL,
    [RecordID]      INT            NOT NULL,
    [FldLabel]      NVARCHAR (500) NULL,
    [FldType]       SMALLINT       NULL,
    [FldValue]      NVARCHAR (500) NULL,
    [TaskType]      SMALLINT       NULL,
    [Key1]          NVARCHAR (50)  NULL,
    [Key2]          NVARCHAR (50)  NULL,
    [Key3]          NVARCHAR (50)  NULL,
    [Key4]          NVARCHAR (50)  NULL,
    [Key5]          NVARCHAR (50)  NULL,
    CONSTRAINT [PK_EmployeeCustomInfo] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [CustomSection] ASC, [FldName] ASC, [RecordID] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_EmployeeCustomInfo_Employees] FOREIGN KEY ([CompanyID], [EmployeeID]) REFERENCES [dbo].[Employees] ([CompanyID], [EmployeeID])
);


GO
ALTER TABLE [dbo].[EmployeeCustomInfo] NOCHECK CONSTRAINT [FK_EmployeeCustomInfo_Employees];

