CREATE TABLE [dbo].[PayrollApprovalEmailTokens] (
    [PayrollApprovalEmailTokenID]    INT            IDENTITY (1, 1) NOT NULL,
    [PayrollApprovalSetupSequenceID] INT            NOT NULL,
    [PayrollNumber]                  NVARCHAR (255) NOT NULL,
    [Token]                          NVARCHAR (255) NOT NULL,
    [DateCreated]                    DATETIME       NOT NULL,
    [Expires]                        DATETIME       NOT NULL,
    [DateUsed]                       DATETIME       NULL,
    CONSTRAINT [PK_PayrollApprovalEmailTokens] PRIMARY KEY CLUSTERED ([PayrollApprovalEmailTokenID] ASC),
    CONSTRAINT [FK_PayrollApprovalEmailTokens_PayrollApprovalSetupSequences] FOREIGN KEY ([PayrollApprovalSetupSequenceID]) REFERENCES [dbo].[PayrollApprovalSetupSequences] ([PayrollApprovalSetupSequenceID]),
    CONSTRAINT [UX_PayrollApprovalEmailTokens] UNIQUE NONCLUSTERED ([PayrollApprovalSetupSequenceID] ASC, [PayrollNumber] ASC, [Token] ASC)
);

