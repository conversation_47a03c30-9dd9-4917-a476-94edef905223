CREATE TABLE [dbo].[OBEmployeeDependents] (
    [CompanyID]            INT            NOT NULL,
    [EmployeeID]           NVARCHAR (15)  NOT NULL,
    [SSN]                  NVARCHAR (15)  NOT NULL,
    [FirstName]            NVARCHAR (15)  NULL,
    [MiddleName]           NVARCHAR (15)  NULL,
    [LastName]             NVARCHAR (20)  NULL,
    [BirthDate]            DATETIME       NULL,
    [Gender]               TINYINT        NULL,
    [EmployeeRelationship] TINYINT        NULL,
    [LivesWithEmployee]    BIT            CONSTRAINT [DF__OBEmploye__Lives__3A785CDD] DEFAULT ((0)) NOT NULL,
    [OverageStudent]       BIT            CONSTRAINT [DF__OBEmploye__Overa__3B6C8116] DEFAULT ((0)) NOT NULL,
    [Disabled]             BIT            CONSTRAINT [DF__OBEmploye__Disab__3C60A54F] DEFAULT ((0)) NOT NULL,
    [Comments]             NVARCHAR (45)  NULL,
    [Smoker]               BIT            CONSTRAINT [DF__OBEmploye__Smoke__3D54C988] DEFAULT ((0)) NOT NULL,
    [Wellness]             BIT            CONSTRAINT [DF__OBEmploye__Welln__3E48EDC1] DEFAULT ((0)) NOT NULL,
    [Other1]               BIT            CONSTRAINT [DF__OBEmploye__Other__3F3D11FA] DEFAULT ((0)) NOT NULL,
    [Other2]               BIT            CONSTRAINT [DF__OBEmploye__Other__40313633] DEFAULT ((0)) NOT NULL,
    [Other3]               BIT            CONSTRAINT [DF__OBEmploye__Other__41255A6C] DEFAULT ((0)) NOT NULL,
    [Other4]               BIT            CONSTRAINT [DF__OBEmploye__Other__42197EA5] DEFAULT ((0)) NOT NULL,
    [Address1]             NVARCHAR (61)  NULL,
    [Address2]             NVARCHAR (61)  NULL,
    [City]                 NVARCHAR (35)  NULL,
    [State]                NVARCHAR (3)   NULL,
    [Zip]                  NVARCHAR (11)  NULL,
    [Phone1]               NVARCHAR (21)  NULL,
    [Phone2]               NVARCHAR (21)  NULL,
    [Email]                NVARCHAR (255) NULL,
    [AdditionalComment]    NTEXT          NULL,
    CONSTRAINT [PK_OBEmployeeDependents] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [SSN] ASC) WITH (FILLFACTOR = 80)
);

