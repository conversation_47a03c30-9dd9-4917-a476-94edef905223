CREATE TABLE [dbo].[EmployeePayrateHistory] (
    [CompanyID]         INT             NOT NULL,
    [EmployeeID]        NVARCHAR (15)   NOT NULL,
    [PayRecord]         NVARCHAR (6)    NOT NULL,
    [EffectiveDate]     DATETIME        NOT NULL,
    [PayRateAmountOld]  DECIMAL (19, 5) NULL,
    [PayRateChangeType] TINYINT         NULL,
    [PayRateAmount]     DECIMAL (19, 5) NULL,
    [PayRateChangeRate] DECIMAL (19, 5) NULL,
    [ChangeReason]      NVARCHAR (30)   NULL,
    [UserID]            NVARCHAR (15)   NULL,
    [Date]              DATETIME        NULL,
    CONSTRAINT [PK_EmployeePayrateHistory] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [PayRecord] ASC, [EffectiveDate] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_EmployeePayrateHistory_Employees] FOREIGN KEY ([CompanyID], [EmployeeID]) REFERENCES [dbo].[Employees] ([CompanyID], [EmployeeID])
);


GO
ALTER TABLE [dbo].[EmployeePayrateHistory] NOCHECK CONSTRAINT [FK_EmployeePayrateHistory_Employees];

