CREATE TABLE [dbo].[Employee401kPlanDetails] (
    [ID]                    INT             IDENTITY (1, 1) NOT NULL,
    [CompanyID]             INT             NOT NULL,
    [EmployeeID]            NVARCHAR (15)   NOT NULL,
    [PlanName]              NVARCHAR (15)   NOT NULL,
    [Deduction]             NVARCHAR (6)    NOT NULL,
    [DeductionPercent1]     DECIMAL (19, 5) NULL,
    [DeductionPercent2]     DECIMAL (19, 5) NULL,
    [DeductionPercent3]     DECIMAL (19, 5) NULL,
    [DeductionPercent4]     DECIMAL (19, 5) NULL,
    [DeductionPercent5]     DECIMAL (19, 5) NULL,
    [DeductionPayPeriodMax] DECIMAL (19, 5) NULL,
    [DeductionMonthMax]     DECIMAL (19, 5) NULL,
    [DeductionQtrMax]       DECIMAL (19, 5) NULL,
    [DeductionYearMax]      DECIMAL (19, 5) NULL,
    [DeductionLifetimeMax]  DECIMAL (19, 5) NULL,
    [LimitPerCheckDate]     BIT             CONSTRAINT [DF_Employee401kPlanDetails_LimitPerCheckDate] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_Employee401kPlanDetails] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [PlanName] ASC, [Deduction] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_Employee401kPlanDetails_Employee401kPlans] FOREIGN KEY ([CompanyID], [EmployeeID], [PlanName]) REFERENCES [dbo].[Employee401kPlans] ([CompanyID], [EmployeeID], [PlanName])
);


GO
ALTER TABLE [dbo].[Employee401kPlanDetails] NOCHECK CONSTRAINT [FK_Employee401kPlanDetails_Employee401kPlans];

