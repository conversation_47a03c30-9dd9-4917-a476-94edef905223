CREATE TABLE [dbo].[EmployeePayrollNotes] (
    [CompanyID]    INT             NOT NULL,
    [EmployeeID]   NVARCHAR (15)   NOT NULL,
    [NotesSubject] NVARCHAR (45)   NOT NULL,
    [CreatedDate]  DATETIME        NOT NULL,
    [CreatedTime]  DATETIME        NOT NULL,
    [CreatedBy]    NVARCHAR (15)   NOT NULL,
    [Valid<PERSON>rom]    DATETIME        NULL,
    [ValidTo]      DATETIME        NULL,
    [Inactive]     BIT             CONSTRAINT [DF__EmployeeP__Inact__78DED853] DEFAULT ((0)) NOT NULL,
    [ChangedBy]    NVARCHAR (15)   NULL,
    [ChangedDate]  DATETIME        NOT NULL,
    [ChangedTime]  DATETIME        NOT NULL,
    [InfoOnlyFlag] BIT             CONSTRAINT [DF__EmployeeP__InfoO__79D2FC8C] DEFAULT ((0)) NOT NULL,
    [RecordNumber] DECIMAL (19, 5) NOT NULL,
    [NotesText]    NVARCHAR (500)  NULL,
    CONSTRAINT [PK_EmployeePayrollNotes_1] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [RecordNumber] ASC) WITH (FILLFACTOR = 80)
);

