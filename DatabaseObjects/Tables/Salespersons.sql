CREATE TABLE [dbo].[Salespersons] (
    [CompanyID]                    INT             NOT NULL,
    [SalespersonID]                NVARCHAR (15)   NOT NULL,
    [Inactive]                     BIT             CONSTRAINT [DF_Table_1_Inactive] DEFAULT ((0)) NOT NULL,
    [ScaleID]                      NVARCHAR (15)   NULL,
    [CommissionMethod]             INT             NULL,
    [CommissionPercent]            SMALLINT NULL,
    [EmployeeID]                   NVARCHAR (15)   NULL,
    [VendorID]                     NVARCHAR (15)   NULL,
    [FirstName]                    NVARCHAR (15)   NULL,
    [MiddleName]                   NVARCHAR (15)   NULL,
    [LastName]                     NVARCHAR (20)   NULL,
    [Address1]                     NVARCHAR (60)   NULL,
    [Address2]                     NVARCHAR (60)   NULL,
    [Address3]                     NVARCHAR (60)   NULL,
    [City]                         NVARCHAR (35)   NULL,
    [State]                        NVARCHAR (30)   NULL,
    [Zip]                          NVARCHAR (10)   NULL,
    [Country]                      NVARCHAR (60)   NULL,
    [Phone1]                       NVARCHAR (14)   NULL,
    [Phone2]                       NVARCHAR (14)   NULL,
    [Phone3]                       NVARCHAR (14)   NULL,
    [Fax]                          NVARCHAR (14)   NULL,
    [SalesTerritory]               NVARCHAR (15)   NULL,
    [CommissionCode]               NVARCHAR (6)    NULL,
    [StandardCommissionPercent]    SMALLINT NULL,
    [CommissionAppliedTo]          INT             NULL,
    [CostToDate]                   DECIMAL (19, 5) NULL,
    [CostLastYear]                 DECIMAL (19, 5) NULL,
    [TotalCommissionsToDate]       DECIMAL (19, 5) NULL,
    [TotalCommissionsLastYear]     DECIMAL (19, 5) NULL,
    [CommissionedSalesToDate]      DECIMAL (19, 5) NULL,
    [CommissionedSalesLastYear]    DECIMAL (19, 5) NULL,
    [NonCommissionedSalesToDate]   DECIMAL (19, 5) NULL,
    [NonCommissionedSalesLastYear] DECIMAL (19, 5) NULL,
    [KeepCalendarHistory]          BIT             CONSTRAINT [DF_Table_1_KeepCalendarHistory] DEFAULT ((0)) NOT NULL,
    [KeepPeriodHistory]            BIT             CONSTRAINT [DF_Table_1_KeepPeriodHistory] DEFAULT ((0)) NOT NULL,
    [ModifiedDate]                 DATE            NULL,
    [CreatedDate]                  DATE            NULL,
    [CommissionDestination]        INT             NULL,
    [HoursWorkedDefault]           INT             NULL,
    CONSTRAINT [PK_Table_1_1] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [SalespersonID] ASC)
);

