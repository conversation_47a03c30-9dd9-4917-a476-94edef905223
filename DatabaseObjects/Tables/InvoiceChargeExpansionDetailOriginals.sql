CREATE TABLE [dbo].[InvoiceChargeExpansionDetailOriginals] (
    [DarwinInvoiceNumber] INT             NOT NULL,
    [ClientID]            NVARCHAR (15)   NOT NULL,
    [DivisionID]          NVARCHAR (15)   NOT NULL,
    [ChargeType]          NVARCHAR (31)   NOT NULL,
    [Index]               SMALLINT        NOT NULL,
    [ChargeAmount]        NUMERIC (19, 5) NULL,
    [Detail1]             NVARCHAR (61)   NULL,
    [Detail2]             NVARCHAR (61)   NULL,
    [MergedInvoiceNumber] INT           NOT NULL CONSTRAINT [DF_InvoiceChargeExpansionDetailOriginals_MergedInvoiceNumber] DEFAULT ((0))
    CONSTRAINT [PK_InvoiceChargeExpansionDetailOriginals] PRIMARY KEY CLUSTERED ([DarwinInvoiceNumber] ASC, [ClientID] ASC, [DivisionID] ASC, [ChargeType] ASC, [Index] ASC, [MergedInvoiceNumber] ASC) WITH (FILLFACTOR = 80)
);

