CREATE TABLE [dbo].[401kPlans] (
    [CompanyID]                 INT             NOT NULL,
    [PlanName]                  NVARCHAR (15)   NOT NULL,
    [Year]                      INT             NOT NULL,
    [PlanType]                  NVARCHAR (15)   NULL,
    [Inactive]                  BIT             CONSTRAINT [DF_401kPlans_Inactive] DEFAULT ((0)) NOT NULL,
    [PolicyNumber]              NVARCHAR (20)   NULL,
    [VendorID]                  NVARCHAR (15)   NULL,
    [StartDate]                 DATETIME        NULL,
    [EndDate]                   DATETIME        NULL,
    [Website]                   NVARCHAR (200)  NULL,
    [AdministratorName]         NVARCHAR (30)   NULL,
    [AdministratorID]           NVARCHAR (15)   NULL,
    [EmployeeOfEmployee]        BIT             CONSTRAINT [DF_401kPlans_EmployeeOfEmployee] DEFAULT ((0)) NOT NULL,
    [Address1]                  NVARCHAR (60)   NULL,
    [Address2]                  NVARCHAR (60)   NULL,
    [Address3]                  NVARCHAR (60)   NULL,
    [City]                      NVARCHAR (35)   NULL,
    [State]                     NVARCHAR (29)   NULL,
    [Zip]                       NVARCHAR (10)   NULL,
    [Phone1]                    NVARCHAR (14)   NULL,
    [Fax]                       NVARCHAR (14)   NULL,
    [Email]                     NVARCHAR (255)  NULL,
    [DaysToWait]                INT             NULL,
    [HoursWorked]               INT             NULL,
    [WaitMode]                  TINYINT         NULL,
    [NotifyUser]                BIT             CONSTRAINT [DF_401kPlans_NotifyUser] DEFAULT ((0)) NOT NULL,
    [BeginUsageBy]              TINYINT         NULL,
    [EligibleDate]              DATETIME        NULL,
    [DeductionPayPeriodMax]     DECIMAL (19, 5) NULL,
    [DeductionMonthMax]         DECIMAL (19, 5) NULL,
    [DeductionQtrMax]           DECIMAL (19, 5) NULL,
    [DeductionYearMax]          DECIMAL (19, 5) NULL,
    [DeductionLifetimeMax]      DECIMAL (19, 5) NULL,
    [MatchPctArray1]            INT             NULL,
    [MatchPctArray2]            INT             NULL,
    [MatchPctArray3]            INT             NULL,
    [MatchPctArray4]            INT             NULL,
    [MatchPctArray5]            INT             NULL,
    [ContributionPctArray1]     INT             NULL,
    [ContributionPctArray2]     INT             NULL,
    [ContributionPctArray3]     INT             NULL,
    [ContributionPctArray4]     INT             NULL,
    [ContributionPctArray5]     INT             NULL,
    [ApplyAnnual401kMatchLimit] BIT             CONSTRAINT [DF_401kPlans_ApplyAnnual401kMatchLimit] DEFAULT ((0)) NOT NULL,
    [401kAnnualWagePctLimit]    INT             NULL,
    [Benefit]                   NVARCHAR (6)    NULL,
    [IncludeClientLevel]        BIT             CONSTRAINT [DF_401kPlans_IncludeClientLevel] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_401kPlans] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [PlanName] ASC, [Year] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_401kPlans_Companies] FOREIGN KEY ([CompanyID]) REFERENCES [dbo].[Companies] ([CompanyID])
);


GO
ALTER TABLE [dbo].[401kPlans] NOCHECK CONSTRAINT [FK_401kPlans_Companies];

