CREATE TABLE [dbo].[CustomReports](
	[ReportID] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NOT NULL,
	[ClientID] [nvarchar](15) NOT NULL,
	[ReportName] [nvarchar](50) NOT NULL,
	[ReportStatus] SMALLINT NOT NULL DEFAULT ((0)),
	[ReportType] [smallint]  CONSTRAINT [DF_CustomReports_ReportType]  DEFAULT ((0))  NOT NULL,
	[PageSize] [smallint]  CONSTRAINT [DF_CustomReports_PageSize]  DEFAULT ((20))  NOT NULL,
	[ReportQuery] [nvarchar](max) NULL,
	[TotalQuery] [nvarchar](max) NULL,
	[Creator] [nvarchar](20) NULL,
	[Created] [datetime] NULL,
	[ReportDocumentID] [int] NULL,
	[IsPublic] BIT NOT NULL DEFAULT ((0)), 
	[ParentID] [int] NULL,
    [SectionQuery] NVARCHAR(MAX) NULL, 
    CONSTRAINT [PK_CustomReports] PRIMARY KEY CLUSTERED 
(
	[ReportID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

