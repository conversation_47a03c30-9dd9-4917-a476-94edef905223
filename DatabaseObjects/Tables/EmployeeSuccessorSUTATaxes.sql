CREATE TABLE [dbo].[EmployeeSuccessorSUTATaxes] (
    [CompanyID]  INT             NOT NULL,
    [Year]       INT             NOT NULL,
    [EmployeeID] NVARCHAR (15)   NOT NULL,
    [Date]       DATETIME        NOT NULL,
    [SUTAState]  NVARCHAR (2)    NOT NULL,
    [SUT<PERSON>mount] DECIMAL (19, 5) NULL,
    CONSTRAINT [PK_EmployeeSuccessorSUTATaxes] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [Year] ASC, [EmployeeID] ASC, [Date] ASC, [SUTAState] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_EmployeeSuccessorSUTATaxes_EmployeeSuccessorTaxes] FOREIGN KEY ([CompanyID], [Year], [EmployeeID], [Date]) REFERENCES [dbo].[EmployeeSuccessorTaxes] ([CompanyID], [Year], [EmployeeID], [Date])
);


GO
ALTER TABLE [dbo].[EmployeeSuccessorSUTATaxes] NOCHECK CONSTRAINT [FK_EmployeeSuccessorSUTATaxes_EmployeeSuccessorTaxes];

