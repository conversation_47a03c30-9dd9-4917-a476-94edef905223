CREATE TABLE [dbo].[OBClientSetupNotifications] (
    [CompanyID]    INT            NOT NULL,
    [SetupID]      INT            NOT NULL,
    [Type]         SMALLINT       NOT NULL,
    [Assigned]     SMALLINT       NOT NULL,
    [Enabled]      BIT            CONSTRAINT [DF_OBClientSetupNotifications_Enabled] DEFAULT ((0)) NOT NULL,
    [UseDefault]   BIT            CONSTRAINT [DF_OBClientSetupNotifications_UseDefault] DEFAULT ((0)) NOT NULL,
    [CCList]       NVARCHAR (MAX) NULL,
    [BCCList]      NVARCHAR (MAX) NULL,
    [EmailSubject] NVARCHAR (100) NULL,
    [EmailBody]    NVARCHAR (MAX) NULL,
    CONSTRAINT [PK_OBClientSetupNotificationDetails] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [SetupID] ASC, [Type] ASC, [Assigned] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_OBClientSetupNotifications_OBClientSetup] FOREIGN KEY ([CompanyID], [SetupID]) REFERENCES [dbo].[OBClientSetup] ([CompanyID], [SetupID])
);


GO
ALTER TABLE [dbo].[OBClientSetupNotifications] NOCHECK CONSTRAINT [FK_OBClientSetupNotifications_OBClientSetup];

