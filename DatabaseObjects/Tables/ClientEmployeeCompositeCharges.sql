CREATE TABLE [dbo].[ClientEmployeeCompositeCharges] (
    [CompanyID]     INT             NOT NULL,
    [CompositeType] INT             NOT NULL,
    [CompositeCode] NVARCHAR (15)   NOT NULL,
    [DivisionID]    NVARCHAR (15)   NOT NULL,
    [ItemNumber]    NVARCHAR (30)   NOT NULL,
    [ComponentType] TINYINT         NOT NULL,
    [ComponentCode] NVARCHAR (30)   NOT NULL,
    [Percentage]    DECIMAL (8, 8)  NULL,
    [Limited]       BIT             CONSTRAINT [DF_ClientEmployeeCompositeCharges_TWLimited] DEFAULT ((0)) NOT NULL,
    [BillingLimit]  DECIMAL (19, 5) NULL,
    CONSTRAINT [PK_ClientEmployeeCompositeCharges] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [CompositeType] ASC, [CompositeCode] ASC, [DivisionID] ASC, [ItemNumber] ASC, [ComponentType] ASC, [ComponentCode] ASC) WITH (FILLFACTOR = 80)
);

