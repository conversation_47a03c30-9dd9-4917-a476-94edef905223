CREATE TABLE [dbo].[PayrollWorkHeadersDivisionalOriginal] (
    [CompanyID]                           INT             NOT NULL,
    [UserID]                              NVARCHAR (15)   NULL,
    [PayRunType]                          TINYINT         NOT NULL,
    [ClientID]                            NVARCHAR (15)   NOT NULL,
    [DivisionID]                          NVARCHAR (15)   NOT NULL,
    [EmployeeID]                          NVARCHAR (15)   NOT NULL,
    [Posted]                              BIT             NOT NULL,
    [PaySubjectToLocalTaxExists]          BIT             NOT NULL,
    [CheckNumber]                         NVARCHAR (20)   NULL,
    [CheckDate]                           DATETIME        NULL,
    [PayRunStartDate]                     DATETIME        NULL,
    [PayRunEndDate]                       DATETIME        NULL,
    [CheckbookID]                         NVARCHAR (15)   NULL,
    [DepositCheckbook]                    NVARCHAR (15)   NULL,
    [PaymentAdjustmentNumber]             INT             NOT NULL,
    [AccountIndex]                        INT             NULL,
    [AdditionalFederalWithholding]        DECIMAL (19, 5) NULL,
    [EstimatedFederalWithholding]         DECIMAL (19, 5) NULL,
    [HoursWorkedForVacationAccrual]       DECIMAL (19, 5) NULL,
    [HoursWorkedForSickAccrual]           DECIMAL (19, 5) NULL,
    [VacationTimeAccrued]                 DECIMAL (19, 5) NULL,
    [SickTimeAccrued]                     DECIMAL (19, 5) NULL,
    [VacationTimeLiability]               DECIMAL (19, 5) NULL,
    [SickTimeLiability]                   DECIMAL (19, 5) NULL,
    [VacationAvailable]                   INT             NULL,
    [SickTimeAvailable]                   INT             NULL,
    [GrossWagesPayRun]                    DECIMAL (19, 5) NULL,
    [NetWagesPayRun]                      DECIMAL (19, 5) NULL,
    [FederalWageArray1]                   DECIMAL (19, 5) NULL,
    [FederalWageArray2]                   DECIMAL (19, 5) NULL,
    [FederalWageArray3]                   DECIMAL (19, 5) NULL,
    [FederalWageArray4]                   DECIMAL (19, 5) NULL,
    [FederalWageArray5]                   DECIMAL (19, 5) NULL,
    [FederalWageArray6]                   DECIMAL (19, 5) NULL,
    [FederalWageArray7]                   DECIMAL (19, 5) NULL,
    [FederalWageArray8]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray1]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray2]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray3]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray4]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray5]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray6]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray7]                   DECIMAL (19, 5) NULL,
    [FederalTipsArray8]                   DECIMAL (19, 5) NULL,
    [FederalWagesPayRun]                  DECIMAL (19, 5) NULL,
    [FederalTipsPayRun]                   DECIMAL (19, 5) NULL,
    [AnnualizedFederalWithholding]        DECIMAL (19, 5) NULL,
    [FederalWithholdingPayRun]            DECIMAL (19, 5) NULL,
    [FederalTaxOnTips]                    DECIMAL (19, 5) NULL,
    [FICASocialSecurityWagesPayRun]       DECIMAL (19, 5) NULL,
    [FICASSTipsPayRun]                    DECIMAL (19, 5) NULL,
    [FICASocialSecurityWithholdingPayRun] DECIMAL (19, 5) NULL,
    [FICASSTaxOnTips]                     DECIMAL (19, 5) NULL,
    [UncollectedFICASSTaxPayRun]          DECIMAL (19, 5) NULL,
    [FICAMedicareWagesPayRun]             DECIMAL (19, 5) NULL,
    [FICAMedTipsPayRun]                   DECIMAL (19, 5) NULL,
    [FICAMedicareWithholdingPayRun]       DECIMAL (19, 5) NULL,
    [FICAMedTaxOnTips]                    DECIMAL (19, 5) NULL,
    [UncollectedFICAMedTaxPayRun]         DECIMAL (19, 5) NULL,
    [EICWagesPayRun]                      DECIMAL (19, 5) NULL,
    [EmployerFICASocialSecurity]          DECIMAL (19, 5) NULL,
    [EmployerFICAMedicare]                DECIMAL (19, 5) NULL,
    [ChargedReceipts]                     DECIMAL (19, 5) NULL,
    [ReportedReceipts]                    DECIMAL (19, 5) NULL,
    [HoursWorkedForMinimumWage]           DECIMAL (19, 5) NULL,
    [MinWageBalApplicablePay]             DECIMAL (19, 5) NULL,
    [ReportedTips]                        DECIMAL (19, 5) NULL,
    [ChargedTips]                         DECIMAL (19, 5) NULL,
    [AllocatedTips]                       DECIMAL (19, 5) NULL,
    [TipType]                             TINYINT         NULL,
    [PostedDate]                          DATETIME        NULL,
    [SubClient]                           NVARCHAR (15)   NULL,
    [TaxOnReportedTips]                   DECIMAL (19, 5) NULL,
    [UseDepositCheckbook]                 BIT             NOT NULL,
    [MultiEmpIDsPayrun]                   BIT             NOT NULL,
    [UseClientCheckbookInfo]              BIT             NOT NULL,
    [PTOAvailable1]                       INT             NULL,
    [PTOAvailable2]                       INT             NULL,
    [PTOAvailable3]                       INT             NULL,
    [PTOAvailable4]                       INT             NULL,
    [PTOAvailable5]                       INT             NULL,
    [PTOAvailable6]                       INT             NULL,
    [PTOAvailable7]                       INT             NULL,
    [PTOAvailable8]                       INT             NULL,
    [PTOAvailable9]                       INT             NULL,
    [PTOAvailable10]                      INT             NULL,
    [PTOTimeAccrued1]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued2]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued3]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued4]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued5]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued6]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued7]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued8]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued9]                     DECIMAL (19, 5) NULL,
    [PTOTimeAccrued10]                    DECIMAL (19, 5) NULL,
    [PTOHours1]                           DECIMAL (19, 5) NULL,
    [PTOHours2]                           DECIMAL (19, 5) NULL,
    [PTOHours3]                           DECIMAL (19, 5) NULL,
    [PTOHours4]                           DECIMAL (19, 5) NULL,
    [PTOHours5]                           DECIMAL (19, 5) NULL,
    [PTOHours6]                           DECIMAL (19, 5) NULL,
    [PTOHours7]                           DECIMAL (19, 5) NULL,
    [PTOHours8]                           DECIMAL (19, 5) NULL,
    [PTOHours9]                           DECIMAL (19, 5) NULL,
    [PTOHours10]                          DECIMAL (19, 5) NULL,
    [PTOLiability1]                       DECIMAL (19, 5) NULL,
    [PTOLiability2]                       DECIMAL (19, 5) NULL,
    [PTOLiability3]                       DECIMAL (19, 5) NULL,
    [PTOLiability4]                       DECIMAL (19, 5) NULL,
    [PTOLiability5]                       DECIMAL (19, 5) NULL,
    [PTOLiability6]                       DECIMAL (19, 5) NULL,
    [PTOLiability7]                       DECIMAL (19, 5) NULL,
    [PTOLiability8]                       DECIMAL (19, 5) NULL,
    [PTOLiability9]                       DECIMAL (19, 5) NULL,
    [PTOLiability10]                      DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual1]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual2]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual3]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual4]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual5]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual6]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual7]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual8]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual9]                 DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual10]                DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual11]                DECIMAL (19, 5) NULL,
    [HoursForLtdAccrual12]                DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray1]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray2]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray3]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray4]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray5]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray6]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray7]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray8]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray9]                 DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray10]                DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray11]                DECIMAL (19, 5) NULL,
    [PTOPayRateAmtArray12]                DECIMAL (19, 5) NULL,
    [VacationHours]                       INT             NULL,
    [SickTimeHours]                       INT             NULL,
    [PTOTakenArray1]                      INT             NULL,
    [PTOTakenArray2]                      INT             NULL,
    [PTOTakenArray3]                      INT             NULL,
    [PTOTakenArray4]                      INT             NULL,
    [PTOTakenArray5]                      INT             NULL,
    [PTOTakenArray6]                      INT             NULL,
    [PTOTakenArray7]                      INT             NULL,
    [PTOTakenArray8]                      INT             NULL,
    [PTOTakenArray9]                      INT             NULL,
    [PTOTakenArray10]                     INT             NULL,
    [CalcCheckErrors]                     INT             NULL,
    [IncludedInBlendedRate]               BIT             NOT NULL,
    [SkipMinWageCalc]                     BIT             NOT NULL,
    [WaitingPeriodSatisfied]              BIT             NOT NULL,
    [401KCombinedTotal]                   DECIMAL (19, 5) NULL,
    [401KMTDCombinedTotal]                DECIMAL (19, 5) NULL,
    [401KQTDCombinedTotal]                DECIMAL (19, 5) NULL,
    [401KYTDCombinedTotal]                DECIMAL (19, 5) NULL,
    [401KLTDCombinedTotal]                DECIMAL (19, 5) NULL,
    [401KPeriodCombinedLimit]             DECIMAL (19, 5) NULL,
    [401KMonthCombinedLimit]              DECIMAL (19, 5) NULL,
    [401KQtrCombinedLimit]                DECIMAL (19, 5) NULL,
    [401KAnnualCombinedLimit]             DECIMAL (19, 5) NULL,
    [401KLifeCombinedLimit]               DECIMAL (19, 5) NULL,
    [401KMatchingDeficit]                 DECIMAL (19, 5) NULL,
    [401KAssigned]                        BIT             NOT NULL,
    [WageRatio]                           DECIMAL (19, 5) NULL,
    [ProfileID]                           NVARCHAR (15)   NULL,
    [PayrollNumber]                       NVARCHAR (255)  NOT NULL,
    [SnapshotID]                          NVARCHAR (50)   NOT NULL,
    [IsLatest]                            BIT             NOT NULL,
    CONSTRAINT [PK_PayrollWorkHeadersDivisionalOriginal] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [PayRunType] ASC, [ClientID] ASC, [DivisionID] ASC, [EmployeeID] ASC, [PaymentAdjustmentNumber] ASC, [PayrollNumber] ASC, [SnapshotID] ASC)
);

