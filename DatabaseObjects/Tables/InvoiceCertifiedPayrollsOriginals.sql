CREATE TABLE [dbo].[InvoiceCertifiedPayrollsOriginals] (
    [CompanyID]             INT             NOT NULL,
    [ClientID]              NVARCHAR (15)   NOT NULL,
    [DarwinInvoiceNumber]   INT             NOT NULL,
    [Position]              NVARCHAR (6)    NOT NULL,
    [StartDate]             DATETIME        NOT NULL,
    [EmployeeID]            NVARCHAR (15)   NOT NULL,
    [CheckNumber]           NVARCHAR (20)   NULL,
    [NetAmount]             DECIMAL (19, 5) NULL,
    [GrossAmount]           DECIMAL (19, 5) NULL,
    [BenefitAmount]         DECIMAL (19, 5) NULL,
    [FICAMAmount]           DECIMAL (19, 5) NULL,
    [FICASSAmount]          DECIMAL (19, 5) NULL,
    [FUTAAmount]            DECIMAL (19, 5) NULL,
    [SUTAAmount]            DECIMAL (19, 5) NULL,
    [WorkersCompAmount]     DECIMAL (19, 5) NULL,
    [TotalDeductions]       DECIMAL (19, 5) NULL,
    [TotalTaxes]            DECIMAL (19, 5) NULL,
    [DarwinString]          NVARCHAR (20)   NULL,
    [MergedInvoiceNumber] INT             CONSTRAINT [DF_InvoiceCertifiedPayrollsOriginals_MergedInvoiceNumber] DEFAULT ((0)) NOT NULL,
    [CertifiedPayrollNumber] INT NULL,
    CONSTRAINT [PK_InvoiceCertifiedPayrollsOriginals] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [DarwinInvoiceNumber] ASC, [Position] ASC, [StartDate] ASC, [EmployeeID] ASC, [MergedInvoiceNumber] ASC)
);

