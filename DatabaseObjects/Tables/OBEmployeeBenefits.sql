CREATE TABLE [dbo].[OBEmployeeBenefits] (
    [CompanyID]               INT             NOT NULL,
    [EmployeeID]              NVARCHAR (15)   NOT NULL,
    [Benefit]                 NVARCHAR (6)    NOT NULL,
    [Inactive]                BIT             CONSTRAINT [DF__OBEmploye__Inact__61081A19] DEFAULT ((0)) NOT NULL,
    [BenefitBeginDate]        DATETIME        NULL,
    [BenefitEndDate]          DATETIME        NULL,
    [VariableBenefit]         BIT             CONSTRAINT [DF__OBEmploye__Varia__61FC3E52] DEFAULT ((0)) NOT NULL,
    [BenefitFrequency]        TINYINT         NULL,
    [Taxable]                 BIT             CONSTRAINT [DF__OBEmploye__Taxab__62F0628B] DEFAULT ((0)) NOT NULL,
    [SubjectToFederalTax]     BIT             CONSTRAINT [DF__OBEmploye__Subje__63E486C4] DEFAULT ((0)) NOT NULL,
    [SubjectToSocialSecurity] BIT             CONSTRAINT [DF__OBEmploye__Subje__64D8AAFD] DEFAULT ((0)) NOT NULL,
    [SubjectToMedicare]       BIT             CONSTRAINT [DF__OBEmploye__Subje__65CCCF36] DEFAULT ((0)) NOT NULL,
    [SubjectToStateTax]       BIT             CONSTRAINT [DF__OBEmploye__Subje__66C0F36F] DEFAULT ((0)) NOT NULL,
    [SubjectToLocalTax]       BIT             CONSTRAINT [DF__OBEmploye__Subje__67B517A8] DEFAULT ((0)) NOT NULL,
    [SubjectToFUTA]           BIT             CONSTRAINT [DF__OBEmploye__Subje__68A93BE1] DEFAULT ((0)) NOT NULL,
    [SubjectToSUTA]           BIT             CONSTRAINT [DF__OBEmploye__Subje__699D601A] DEFAULT ((0)) NOT NULL,
    [FlatFederalTaxRate]      INT             NULL,
    [FlatStateTaxRate]        INT             NULL,
    [BasedOnRecordType]       TINYINT         NULL,
    [BasedOnRecords]          TINYINT         NULL,
    [BenefitMethod]           TINYINT         NULL,
    [BenefitFormula]          TINYINT         NULL,
    [BenefitPercent1]         DECIMAL (19, 5) NULL,
    [BenefitPercent2]         DECIMAL (19, 5) NULL,
    [BenefitPercent3]         DECIMAL (19, 5) NULL,
    [BenefitPercent4]         DECIMAL (19, 5) NULL,
    [BenefitPercent5]         DECIMAL (19, 5) NULL,
    [BenefitAmount1]          DECIMAL (19, 5) NULL,
    [BenefitAmount2]          DECIMAL (19, 5) NULL,
    [BenefitAmount3]          DECIMAL (19, 5) NULL,
    [BenefitAmount4]          DECIMAL (19, 5) NULL,
    [BenefitAmount5]          DECIMAL (19, 5) NULL,
    [BenefitTierMax1]         DECIMAL (19, 5) NULL,
    [BenefitTierMax2]         DECIMAL (19, 5) NULL,
    [BenefitTierMax3]         DECIMAL (19, 5) NULL,
    [BenefitTierMax4]         DECIMAL (19, 5) NULL,
    [BenefitTierMax5]         DECIMAL (19, 5) NULL,
    [BenefitTierMaxUnits1]    INT             NULL,
    [BenefitTierMaxUnits2]    INT             NULL,
    [BenefitTierMaxUnits3]    INT             NULL,
    [BenefitTierMaxUnits4]    INT             NULL,
    [BenefitTierMaxUnits5]    INT             NULL,
    [BenefitPayPeriodMax]     DECIMAL (19, 5) NULL,
    [BenefitYearMax]          DECIMAL (19, 5) NULL,
    [BenefitLifetimeMax]      DECIMAL (19, 5) NULL,
    [W2BoxNumber]             INT             NULL,
    [W2BoxLabel]              NVARCHAR (6)    NULL,
    [LTDBenefit]              DECIMAL (19, 5) NULL,
    [DataEntryDefault]        BIT             CONSTRAINT [DF__OBEmploye__DataE__6A918453] DEFAULT ((0)) NOT NULL,
    [EmployerMaximumMatch]    DECIMAL (19, 5) NULL,
    [W2BoxNumber2]            INT             NULL,
    [W2BoxLabel2]             NVARCHAR (6)    NULL,
    [W2BoxNumber3]            INT             NULL,
    [W2BoxLabel3]             NVARCHAR (6)    NULL,
    [W2BoxNumber4]            INT             NULL,
    [W2BoxLabel4]             NVARCHAR (6)    NULL,
    [BenefitFiscalMax]        DECIMAL (19, 5) NULL,
    [AgencyType]              TINYINT         NULL,
    [AgencyVendorID]          NVARCHAR (15)   NULL,
    [ClientID]                NVARCHAR (15)   NULL,
    [SingleMultiple]          INT             NULL,
    [FromWages1]              DECIMAL (19, 5) NULL,
    [FromWages2]              DECIMAL (19, 5) NULL,
    [FromWages3]              DECIMAL (19, 5) NULL,
    [FromWages4]              DECIMAL (19, 5) NULL,
    [FromWages5]              DECIMAL (19, 5) NULL,
    [AmountsArray1]           DECIMAL (19, 5) NULL,
    [AmountsArray2]           DECIMAL (19, 5) NULL,
    [AmountsArray3]           DECIMAL (19, 5) NULL,
    [AmountsArray4]           DECIMAL (19, 5) NULL,
    [AmountsArray5]           DECIMAL (19, 5) NULL,
    [PercentArray1]           DECIMAL (19, 5) NULL,
    [PercentArray2]           DECIMAL (19, 5) NULL,
    [PercentArray3]           DECIMAL (19, 5) NULL,
    [PercentArray4]           DECIMAL (19, 5) NULL,
    [PercentArray5]           DECIMAL (19, 5) NULL,
    [AccountIndex]            INT             NULL,
    [TaxScheduleID]           NVARCHAR (15)   NULL,
    [APInfo]                  NVARCHAR (20)   NULL,
    [BenefitMonthMax]         DECIMAL (19, 5) NULL,
    [AutomaticRecapture]      BIT             CONSTRAINT [DF__OBEmploye__Autom__6B85A88C] DEFAULT ((0)) NOT NULL,
    [RecaptureOptions]        TINYINT         NULL,
    [RecaptureAmount]         DECIMAL (19, 5) NULL,
    [AgencyRecaptureAmount]   DECIMAL (19, 5) NULL,
    [TimeRemaining]           INT             NULL,
    [EffectiveDate]           DATETIME        NULL,
    [PlanName]                NVARCHAR (15)   NULL,
    [ClientPlanID]            NVARCHAR (30)   NULL,
    [BenefitQtrMax]           DECIMAL (19, 5) NULL,
    [LimitPerCheckDate]       BIT             CONSTRAINT [DF__OBEmploye__Limit__6C79CCC5] DEFAULT ((0)) NOT NULL,
    [IncludeInYearLimit]      BIT             CONSTRAINT [DF__OBEmploye__Inclu__6D6DF0FE] DEFAULT ((0)) NOT NULL,
    [401KType]                BIT             CONSTRAINT [DF__OBEmploye__401KT__6E621537] DEFAULT ((0)) NOT NULL,
    [ADPLocalTax]             BIT             CONSTRAINT [DF__OBEmploye__ADPLo__6F563970] DEFAULT ((0)) NOT NULL,
    [AssignToHomeDepartment]  BIT             CONSTRAINT [DF__OBEmploye__Assig__704A5DA9] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_OBEmployeeBenefits] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [Benefit] ASC) WITH (FILLFACTOR = 80)
);

