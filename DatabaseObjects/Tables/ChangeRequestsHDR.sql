CREATE TABLE [dbo].[ChangeRequestsHDR] (
    [CompanyID]      INT            NOT NULL,
    [RequestID]      INT            IDENTITY (1, 1) NOT NULL,
    [RequestOrigin]  NVARCHAR (1)   NULL,
    [RequestProcess] BIT            CONSTRAINT [DF_ChangeRequestsHDR_RequestProcess] DEFAULT ((0)) NOT NULL,
    [RequestExport]  BIT            CONSTRAINT [DF_ChangeRequestsHDR_RequestExport] DEFAULT ((0)) NOT NULL,
    [RequestType]    NVARCHAR (1)   NULL,
    [RequestDate]    DATETIME       NULL,
    [RequestTime]    DATETIME       NULL,
    [ClientID]       NVARCHAR (15)  NULL,
    [EmployeeID]     NVARCHAR (15)  NULL,
    [AssignmentType] TINYINT        NULL,
    [TableName]      NVARCHAR (40)  NULL,
    [Key1]           NVARCHAR (255) NULL,
    [Key2]           NVARCHAR (255) NULL,
    [LogonID]        NVARCHAR (200) NULL,
    [TransferUserID] NVARCHAR (15)  NULL,
    [TransferDate]   DATETIME       NULL,
    [TransferTime]   DATETIME       NULL,
    [RequestStatus]  TINYINT        NULL,
    [ProcessUserID]  NVARCHAR (15)  NULL,
    [ProcessDate]    DATETIME       NULL,
    [ProcessTime]    DATETIME       NULL,
    [RequestNote]    NVARCHAR (MAX) NULL,
    [ResponseNote]   NVARCHAR (MAX) NULL,
    [DnetOnly]       BIT            CONSTRAINT [DF_ChangeRequestsHDR_DnetOnly] DEFAULT ((1)) NOT NULL,
    [RequestedBy]    NVARCHAR (20)  NULL,
    [RequestedIP]    NVARCHAR (50)  NULL,
    CONSTRAINT [PK_ChangeRequestsHDR] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [RequestID] ASC) WITH (FILLFACTOR = 80)
);

