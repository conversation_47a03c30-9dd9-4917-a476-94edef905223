CREATE TABLE [dbo].[EmployeeDailyTime] (
    [ID]           INT             IDENTITY (1, 1) NOT NULL,
    [CompanyID]    INT             NULL,
    [EmployeeID]   NVARCHAR (15)   NULL,
    [Day]          DATE            NULL,
    [RegCode]      NVARCHAR (6)    NULL,
    [RegHours]     DECIMAL (19, 5) NULL,
    [RegRate]      DECIMAL (19, 5) NULL,
    [RegShiftCode] NVARCHAR (6)    NULL,
    [RegPremium]   DECIMAL (19, 5) NULL,
    [OTCode]       NVARCHAR (6)    NULL,
    [OTHours]      DECIMAL (19, 5) NULL,
    [OTRate]       DECIMAL (19, 5) NULL,
    [OTShiftCode]  NVARCHAR (6)    NULL,
    [OTPremium]    DECIMAL (19, 5) NULL,
    [VarCode]      NVARCHAR (6)    NULL,
    [VarHours]     DECIMAL (19, 5) NULL,
    [VarRate]      DECIMAL (19, 5) NULL,
    [VarShiftCode] NVARCHAR (6)    NULL,
    [VarPremium]   DECIMAL (19, 5) NULL,
    [Department]   NVARCHAR (6)    NULL,
    [Position]     NVARCHAR (6)    NULL,
    [TimeSheetID]  INT             CONSTRAINT [DF_EmployeeDailyTime_TimeSheetID] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_EmployeeDailyTime] PRIMARY KEY CLUSTERED ([ID] ASC) WITH (FILLFACTOR = 80)
);

