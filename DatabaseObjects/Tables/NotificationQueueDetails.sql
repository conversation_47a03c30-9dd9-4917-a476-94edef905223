CREATE TABLE [dbo].[NotificationQueueDetails] (
    [NotificationQueueDetailID] INT            IDENTITY (1, 1) NOT NULL,
    [NotificationQueueID]       INT            NOT NULL,
    [NotificationID]            INT            NOT NULL,
    [EmailSubject]              NVARCHAR (100) NOT NULL,
    [EmailBody]                 NVARCHAR (MAX) NOT NULL,
    [Created]                   DATETIME       NOT NULL,
    CONSTRAINT [PK_NotificationQueueDetails] PRIMARY KEY CLUSTERED ([NotificationQueueDetailID] ASC),
    CONSTRAINT [FK_NotificationQueueDetails_NotificationQueue] FOREIGN KEY ([NotificationQueueID]) REFERENCES [dbo].[NotificationQueue] ([NotificationQueueID]),
    CONSTRAINT [FK_NotificationQueueDetails_Notifications] FOREIGN KEY ([NotificationID]) REFERENCES [dbo].[Notifications] ([NotificationID])
);

