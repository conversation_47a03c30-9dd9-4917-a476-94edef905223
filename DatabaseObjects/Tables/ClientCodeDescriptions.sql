CREATE TABLE [dbo].[ClientCodeDescriptions] (
    [CompanyID]         INT           NOT NULL,
    [ClientID]          NVARCHAR (15) NOT NULL,
    [PayrollRecordType] INT           NOT NULL,
    [PayrollCode]       NVARCHAR (6)  NOT NULL,
    [Description]       NVARCHAR (30) NULL,
    CONSTRAINT [PK_ClientCodeDescriptions] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [PayrollRecordType] ASC, [PayrollCode] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_ClientCodeDescriptions_Clients] FOREIGN KEY ([CompanyID], [ClientID]) REFERENCES [dbo].[Clients] ([CompanyID], [ClientID])
);


GO
ALTER TABLE [dbo].[ClientCodeDescriptions] NOCHECK CONSTRAINT [FK_ClientCodeDescriptions_Clients];

