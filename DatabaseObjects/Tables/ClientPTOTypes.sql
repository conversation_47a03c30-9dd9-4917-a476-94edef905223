CREATE TABLE [dbo].[ClientPTOTypes] (
    [ID]                          INT           IDENTITY (1, 1) NOT NULL,
    [CompanyID]                   INT           NOT NULL,
    [ClientID]                    NVARCHAR (15) NOT NULL,
    [PTOType]                     TINYINT       NOT NULL,
    [Description]                 NVARCHAR (30) NULL,
    [Accrue]                      BIT           CONSTRAINT [DF_ClientPTOTypes_Accrue] DEFAULT ((0)) NOT NULL,
    [AccrualMethod]               SMALLINT      NULL,
    [AvailableHours]              INT           NULL,
    [AccrualAmount]               INT           NULL,
    [HoursPerYear]                INT           NULL,
    [MaxAccrualYTD]               INT           NULL,
    [MaxAccrualLTD]               INT           NULL,
    [Type]                        SMALLINT      NULL,
    [CarryOver]                   BIT           CONSTRAINT [DF_ClientPTOTypes_CarryOver] DEFAULT ((0)) NOT NULL,
    [MaxCarryOver]                INT           NULL,
    [SuppressPrintTilAvailable]   BIT           CONSTRAINT [DF_ClientPTOTypes_SuppressPrintTilAvailable] DEFAULT ((0)) NOT NULL,
    [ProcessDate]                 DATETIME      NULL,
    [SchedulePeriod1]             SMALLINT      NULL,
    [SchedulePeriod2]             SMALLINT      NULL,
    [FirstOfNextMonth1]           BIT           CONSTRAINT [DF_ClientPTOTypes_FirstOfNextMonth1] DEFAULT ((0)) NOT NULL,
    [FirstOfNextMonth2]           BIT           CONSTRAINT [DF_ClientPTOTypes_FirstOfNextMonth2] DEFAULT ((0)) NOT NULL,
    [WaitPeriodAccrualBeginLimit] INT           NULL,
    [WaitPeriodUsageBeginLimit]   INT           NULL,
    [UseTiers]                    BIT           CONSTRAINT [DF_ClientPTOTypes_UseTiers] DEFAULT ((0)) NOT NULL,
    [AnniversaryMethod]           SMALLINT      NULL,
    [WarnAvailableBelowZero]      BIT           CONSTRAINT [DF_ClientPTOTypes_WarnAvailableBelowZero] DEFAULT ((0)) NOT NULL,
    [Inactive]                    BIT           CONSTRAINT [DF_ClientPTOTypes_Inactive] DEFAULT ((0)) NOT NULL,
    [TiersBasedOn]                SMALLINT      NULL,
    [MaxAccrualBasedOn]           SMALLINT      NULL,
    [Vested]                      BIT           CONSTRAINT [DF_ClientPTOTypes_Vested] DEFAULT ((0)) NOT NULL,
    [VestedDestination]           INT           NULL,
    [VestedSource]                INT           NULL,
    CONSTRAINT [PK_ClientPTOTypes] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [PTOType] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_ClientPTOTypes_Clients] FOREIGN KEY ([CompanyID], [ClientID]) REFERENCES [dbo].[Clients] ([CompanyID], [ClientID])
);


GO
ALTER TABLE [dbo].[ClientPTOTypes] NOCHECK CONSTRAINT [FK_ClientPTOTypes_Clients];

