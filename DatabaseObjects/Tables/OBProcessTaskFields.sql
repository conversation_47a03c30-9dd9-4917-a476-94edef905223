CREATE TABLE [dbo].[OBProcessTaskFields] (
    [CompanyID]     INT             NOT NULL,
    [EmployeeID]    NVARCHAR (15)   NOT NULL,
    [TaskID]        INT             NOT NULL,
    [FName]         NVARCHAR (100)  NOT NULL,
    [SeqNbr]        SMALLINT        NULL,
    [<PERSON>abel]        NVARCHAR (500)  NULL,
    [FType]         SMALLINT        NULL,
    [FSize]         SMALLINT        NULL,
    [DNType]        SMALLINT        NULL,
    [FValueOptions] NVARCHAR (1000) NULL,
    [PermanentTBL]  NVARCHAR (100)  NULL,
    [PermanentFLD]  NVARCHAR (100)  NULL,
    [RelatedTask]   INT             NULL,
    [RelatedFLD]    NVARCHAR (100)  NULL,
    [FRequired]     SMALLINT        NULL,
    [FLocked]       SMALLINT        NULL,
    [CCAccess]      SMALLINT        NULL,
    [EEAccess]      SMALLINT        NULL,
    [FTip]          NVARCHAR (MAX)  NULL,
    CONSTRAINT [PK_OBProcessTaskFields] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [TaskID] ASC, [FName] ASC) WITH (FILLFACTOR = 80)
);

