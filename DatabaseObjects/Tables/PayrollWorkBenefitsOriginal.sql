CREATE TABLE [dbo].[PayrollWorkBenefitsOriginal] (
    [CompanyID]                    INT             NOT NULL,
    [Department]                   NVARCHAR (6)    NULL,
    [JobTitle]                     NVARCHAR (6)    NULL,
    [UserID]                       NVARCHAR (15)   NOT NULL,
    [PayRunType]                   TINYINT         NOT NULL,
    [EmployeeID]                   NVARCHAR (15)   NOT NULL,
    [TRXNumber]                    INT             NOT NULL,
    [Benefit]                      NVARCHAR (6)    NOT NULL,
    [Taxable]                      BIT             NOT NULL,
    [VariableBenefit]              BIT             NOT NULL,
    [VariableDedBenAmount]         DECIMAL (19, 5) NULL,
    [VariableDedBenPercent]        INT             NULL,
    [BenefitFrequency]             TINYINT         NULL,
    [TotalBenefit]                 DECIMAL (19, 5) NULL,
    [AmountBasedOnTips]            DECIMAL (19, 5) NULL,
    [BatchSource]                  NVARCHAR (15)   NULL,
    [PayrollTRXSource]             INT             NULL,
    [LastUser]                     NVARCHAR (15)   NULL,
    [LastDateEdited]               DATETIME        NULL,
    [TRXBeginningDate]             DATETIME        NULL,
    [TRXEndingDate]                DATETIME        NULL,
    [AutomaticRecapture]           BIT             NOT NULL,
    [BuildCheckWarnings]           BIT             NOT NULL,
    [BuildCheckErrors]             BIT             NOT NULL,
    [RecaptureAmount]              DECIMAL (19, 5) NULL,
    [AgencyRecaptureAmount]        DECIMAL (19, 5) NULL,
    [ProcessSequence]              INT             NULL,
    [401KAssigned]                 BIT             NOT NULL,
    [OrigCalcAmount]               DECIMAL (19, 5) NULL,
    [Edited]                       BIT             NOT NULL,
    [Edit]                         BIT             NOT NULL,
    [OrigRecaptureAmount]          DECIMAL (19, 5) NULL,
    [TimeRemaining]                INT             NULL,
    [ProfileID]                    NVARCHAR (15)   NOT NULL,
    [LDLabel1]                     NVARCHAR (30)   NULL,
    [LDLabel2]                     NVARCHAR (30)   NULL,
    [LDLabel3]                     NVARCHAR (30)   NULL,
    [LDLabel4]                     NVARCHAR (30)   NULL,
    [LDLabel5]                     NVARCHAR (30)   NULL,
    [LDLabel6]                     NVARCHAR (30)   NULL,
    [LDLabel7]                     NVARCHAR (30)   NULL,
    [LDLabel8]                     NVARCHAR (30)   NULL,
    [LDValue1]                     NVARCHAR (30)   NULL,
    [LDValue2]                     NVARCHAR (30)   NULL,
    [LDValue3]                     NVARCHAR (30)   NULL,
    [LDValue4]                     NVARCHAR (30)   NULL,
    [LDValue5]                     NVARCHAR (30)   NULL,
    [LDValue6]                     NVARCHAR (30)   NULL,
    [LDValue7]                     NVARCHAR (30)   NULL,
    [LDValue8]                     NVARCHAR (30)   NULL,
    [YTD]                          DECIMAL (19, 5) NULL,
    [BuildCheckWarningReasons]     NVARCHAR (500)  NULL,
    [BuildCheckErrorReasons]       NVARCHAR (500)  NULL,
    [MTD]                          DECIMAL (19, 5) NULL,
    [QTD]                          DECIMAL (19, 5) NULL,
    [LTD]                          DECIMAL (19, 5) NULL,
    [PayrollNumber]                NVARCHAR (255)  NOT NULL,
    [SnapshotID]                   NVARCHAR (50)   NOT NULL,
    [IsLatest]                     BIT             NOT NULL,
    CONSTRAINT [PK_PayrollWorkBenefitOriginal] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [PayRunType] ASC, [EmployeeID] ASC, [TRXNumber] ASC, [Benefit] ASC, [PayrollNumber] ASC, [SnapshotID] ASC)
);

