CREATE TABLE [dbo].[ClientTimeClockImportSetup] (
    [CompanyID]        INT           NOT NULL,
    [ClientID]         NVARCHAR (15) NOT NULL,
    [ImportModel]      NVARCHAR (12) NULL,
    [EmployeePrefix]   NVARCHAR (6)  NULL,
    [EENumber<PERSON>en]      SMALLINT      NULL,
    [EEIDDir]          TINYINT       NULL,
    [OTFactor]         REAL          NULL,
    [UseDeptLocations] BIT           CONSTRAINT [DF_ClientTimeClockImportSetup_UseDeptLocations] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_ClientTimeClockImportSetup] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_ClientTimeClockImportSetup_Clients] FOREIGN KEY ([CompanyID], [ClientID]) REFERENCES [dbo].[Clients] ([CompanyID], [ClientID])
);


GO
ALTER TABLE [dbo].[ClientTimeClockImportSetup] NOCHECK CONSTRAINT [FK_ClientTimeClockImportSetup_Clients];

