CREATE TABLE [dbo].[EmployeeEligibilityActivity] (
    [CompanyID]  INT           NOT NULL,
    [EmployeeID] NVARCHAR (15) NOT NULL,
    [EventTime]  DATETIME      NOT NULL,
    [UserID]     NVARCHAR (20) NULL,
    [UserLevel]  CHAR (1)      NULL,
    [EventDescr] NTEXT         NULL,
    CONSTRAINT [PK_EmployeeEligibilityActivity] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [EmployeeID] ASC, [EventTime] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_EmployeeEligibilityActivity_Employees] FOREIGN KEY ([CompanyID], [EmployeeID]) REFERENCES [dbo].[Employees] ([CompanyID], [EmployeeID])
);


GO
ALTER TABLE [dbo].[EmployeeEligibilityActivity] NOCHECK CONSTRAINT [FK_EmployeeEligibilityActivity_Employees];

