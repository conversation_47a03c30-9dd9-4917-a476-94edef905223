CREATE TABLE [dbo].[InvoiceClientMinimumBillingHistoryOriginals] (
    [CompanyID]               INT             NOT NULL,
    [ClientID]                NVARCHAR (15)   NOT NULL,
    [DarwinInvoiceNumber]     INT             NOT NULL,
    [WorkersCompClientPlanID] NVARCHAR (15)   NOT NULL,
    [OriginalBilledAmount]    DECIMAL (19, 5) NULL,
    [AdditionalWCBillAmount]  DECIMAL (19, 5) NULL,
    [MergedInvoiceNumber]   INT             CONSTRAINT [DF_InvoiceClientMinimumBillingHistoryOriginals_MergedInvoiceNumber] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_InvoiceClientMinimumBillingHistoryOriginals] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [DarwinInvoiceNumber] ASC, [WorkersCompClientPlanID] ASC, [MergedInvoiceNumber] ASC)
);

