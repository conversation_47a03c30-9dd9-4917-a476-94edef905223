CREATE TABLE [dbo].[PayrollProfileSecondCheckOptions] (
    [CompanyID]     INT           NOT NULL,
    [ClientID]      NVARCHAR (15) NOT NULL,
    [ProfileID]     NVARCHAR (15) NOT NULL,
    [SelectionType] NVARCHAR (50) NOT NULL,
    [SelectedCode]  NVARCHAR (15) NOT NULL,
    CONSTRAINT [PK_PayrollBuildSecondCheckOptions_1] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [ProfileID] ASC, [SelectionType] ASC, [SelectedCode] ASC) WITH (FILLFACTOR = 80)
);

