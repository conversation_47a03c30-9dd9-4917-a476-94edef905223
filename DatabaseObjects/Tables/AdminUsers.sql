CREATE TABLE [dbo].[AdminUsers] (
    [CompanyID]        INT            NOT NULL,
    [MasterType]       NVARCHAR (3)   NOT NULL,
    [MasterID]         NVARCHAR (30)  NOT NULL,
    [AddressCode]      NVARCHAR (15)  NOT NULL,
    [INet1]            NVARCHAR (200) NULL,
    [INet2]            NVARCHAR (200) NULL,
    [INet3]            NVARCHAR (200) NULL,
    [INet4]            NVARCHAR (200) NULL,
    [INet5]            NVARCHAR (200) NULL,
    [INet6]            NVARCHAR (200) NULL,
    [INet7]            NVARCHAR (200) NULL,
    [INet8]            NVARCHAR (200) NULL,
    [MessengerAddress] NVARCHAR (200) NULL,
    [INetInfo]         NVARCHAR (MAX) NULL,
    [EmailToAddress]   NVARCHAR (MAX) NULL,
    [EmailCCAddress]   NVARCHAR (MAX) NULL,
    [EmailBCCAddress]  NVARCHAR (MAX) NULL,
    CONSTRAINT [PK_AdminUsers] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [MasterType] ASC, [MasterID] ASC, [AddressCode] ASC) WITH (FILLFACTOR = 80)
);

