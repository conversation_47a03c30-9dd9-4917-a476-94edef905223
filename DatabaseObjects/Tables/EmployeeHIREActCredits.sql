CREATE TABLE [dbo].[EmployeeHIREActCredits] (
    [CompanyID]         INT             NOT NULL,
    [AuditControlCode]  NVARCHAR (13)   NOT NULL,
    [ClientID]          NVARCHAR (15)   NOT NULL,
    [EmployeeID]        NVARCHAR (15)   NOT NULL,
    [CheckNumber]       NVARCHAR (20)   NULL,
    [CheckDate]         DATETIME        NULL,
    [InvoiceNumber]     INT             NULL,
    [FICASSWagesPayRun] DECIMAL (19, 5) NULL,
    [FICASSCredit]      DECIMAL (19, 5) NULL,
    CONSTRAINT [PK_Table24] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [AuditControlCode] ASC, [ClientID] ASC, [EmployeeID] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_EmployeeHIREActCredits_Employees] FOREIGN KEY ([CompanyID], [EmployeeID]) REFERENCES [dbo].[Employees] ([CompanyID], [EmployeeID])
);


GO
ALTER TABLE [dbo].[EmployeeHIREActCredits] NOCHECK CONSTRAINT [FK_EmployeeHIREActCredits_Employees];

