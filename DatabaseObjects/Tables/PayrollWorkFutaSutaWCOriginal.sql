CREATE TABLE [dbo].[PayrollWorkFutaSutaWCOriginal] (
    [CompanyID]               INT             NOT NULL,
    [UserID]                  NVARCHAR (15)   NULL,
    [PayRunType]              TINYINT         NOT NULL,
    [EmployeeID]              NVARCHAR (15)   NOT NULL,
    [SutaState]               NVARCHAR (2)    NOT NULL,
    [Department]              NVARCHAR (6)    NOT NULL,
    [JobTitle]                NVARCHAR (6)    NOT NULL,
    [PayrollRecordType]       TINYINT         NOT NULL,
    [PayrollCode]             NVARCHAR (6)    NOT NULL,
    [ClientID]                NVARCHAR (15)   NULL,
    [PayRecord]               NVARCHAR (6)    NULL,
    [YTD1]                    DECIMAL (19, 5) NULL,
    [YTD2]                    DECIMAL (19, 5) NULL,
    [YTD3]                    DECIMAL (19, 5) NULL,
    [YTD4]                    DECIMAL (19, 5) NULL,
    [YTD5]                    DECIMAL (19, 5) NULL,
    [YTD6]                    DECIMAL (19, 5) NULL,
    [YTD7]                    DECIMAL (19, 5) NULL,
    [YTD8]                    DECIMAL (19, 5) NULL,
    [YTD9]                    DECIMAL (19, 5) NULL,
    [TaxableWages]            DECIMAL (19, 5) NULL,
    [TotalPay]                DECIMAL (19, 5) NULL,
    [LiabilityAmount]         DECIMAL (19, 5) NULL,
    [HoursWorked]             INT             NULL,
    [DaysWorked]              INT             NULL,
    [WeeksWorked]             INT             NULL,
    [MultiEmpIDs]             BIT             NOT NULL,
    [BillingTaxRate]          INT             NULL,
    [LiabilityTaxRate]        INT             NULL,
    [WageLimit]               DECIMAL (19, 5) NULL,
    [WorkersCompPlanID]       NVARCHAR (15)   NULL,
    [SocialSecurityNumber]    NVARCHAR (15)   NULL,
    [GrossWagesPayRun]        DECIMAL (19, 5) NULL,
    [LimitExceeded]           BIT             NOT NULL,
    [AdditionalFUTALiability] DECIMAL (19, 5) NULL,
    [AdditionalFUTABilling]   DECIMAL (19, 5) NULL,
    [ProfileID]               NVARCHAR (15)   NULL,
    [PayrollNumber]           NVARCHAR (255)  NOT NULL,
    [SnapshotID]              NVARCHAR (50)   NOT NULL,
    [BillingAmount]           DECIMAL (19, 5) NULL,
    [IsLatest]                BIT             NOT NULL,
    CONSTRAINT [PK_PayrollWorkFutaSutaWCOriginal] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [PayRunType] ASC, [EmployeeID] ASC, [SutaState] ASC, [Department] ASC, [JobTitle] ASC, [PayrollRecordType] ASC, [PayrollCode] ASC, [PayrollNumber] ASC, [SnapshotID] ASC)
);

