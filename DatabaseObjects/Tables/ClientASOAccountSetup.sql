CREATE TABLE [dbo].[ClientASOAccountSetup] (
    [ID]              INT           IDENTITY (1, 1) NOT NULL,
    [CompanyID]       INT           NOT NULL,
    [ClientID]        NVARCHAR (15) NOT NULL,
    [AccountType]     SMALLINT      NOT NULL,
    [CreditAccountId] INT           NOT NULL,
    [DebitAccountId]  INT           NOT NULL,
    [ASO_Pays]        BIT           CONSTRAINT [DF_ClientASOAccountSetup_ASO_Pays] DEFAULT ((0)) NOT NULL,
    [DivisionID]      NVARCHAR (15) NOT NULL,
    CONSTRAINT [PKTWASOAS] PRIMARY KEY NONCLUSTERED ([CompanyID] ASC, [ClientID] ASC, [AccountType] ASC, [DivisionID] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_ClientASOAccountSetup_ClientDivisions] FOREIGN KEY ([CompanyID], [ClientID], [DivisionID]) REFERENCES [dbo].[ClientDivisions] ([CompanyID], [ClientID], [DivisionID])
);


GO
ALTER TABLE [dbo].[ClientASOAccountSetup] NOCHECK CONSTRAINT [FK_ClientASOAccountSetup_ClientDivisions];

