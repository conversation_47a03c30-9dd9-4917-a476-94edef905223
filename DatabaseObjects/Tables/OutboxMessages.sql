CREATE TABLE [dbo].[OutboxMessages] (
		[Id]						INT IDENTITY(1,1) NOT NULL,
		[ToAddress]					NVARCHAR (100)	NOT NULL,
		[FromAddress]				NVARCHAR (100)	NOT NULL,
		[FromName]					NVARCHAR (100)	NOT NULL,
		[Subject]					NVARCHAR (255)	NULL,
		[Body]						TEXT			NULL,
		[InitialSendTimestamp]		DATETIME		CONSTRAINT [DF_OutboxMessages_InitialSendTimestamp] DEFAULT ((GETDATE())) NOT NULL,
		[RetryInterval]				INT				NOT NULL,
		[MaxRetryInterval]			INT				NOT NULL,
		[Status]					INT				NOT NULL,
		[LastAttempt]				DATETIME		CONSTRAINT [DF_OutboxMessages_LastAttempt] DEFAULT ((GETDATE())) NOT NULL,
		[Expiration]				DATETIME		NULL
	 CONSTRAINT [PK_OutboxMessages] PRIMARY KEY CLUSTERED ([Id] ASC, [ToAddress] ASC) WITH (FILLFACTOR = 80)
	)
GO