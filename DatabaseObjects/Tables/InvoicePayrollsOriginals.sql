CREATE TABLE [dbo].[InvoicePayrollsOriginals] (
    [CompanyID]                   INT            NOT NULL,
    [ClientID]                    NVARCHAR (15)  NOT NULL,
    [InvoiceNumber]               NVARCHAR (30)  NOT NULL,
    [AuditControlCode]            NVARCHAR (13)  NOT NULL,
    [ProcessDirectDeposits]       BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_ProcessDirectDeposits] DEFAULT ((0)) NOT NULL,
    [CheckbookID]                 NVARCHAR (15)  NULL,
    [DepositCheckbook]            NVARCHAR (15)  NULL,
    [IgnoreMissedCodes]           BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_IgnoreMissedCodes] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesSalary]        BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesSalary] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesPension]       BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesPension] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesEIC]           BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesEIC] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesWeekly]        BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesWeekly] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesBiweekly]      BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesBiweekly] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesSemimonthly]   BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesSemimonthly] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesMonthly]       BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesMonthly] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesQuarterly]     BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesQuarterly] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesSemiannually]  BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesSemiannually] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesAnnually]      BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesAnnually] DEFAULT ((0)) NOT NULL,
    [PayRunIncludesDailyMisc]     BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_PayRunIncludesDailyMisc] DEFAULT ((0)) NOT NULL,
    [NoAccruals]                  BIT            CONSTRAINT [DF_InvoicePayrollsOriginals_NoAccruals] DEFAULT ((0)) NOT NULL,
    [DarwinInvoiceNumber]         INT            NOT NULL,
    [PayrollNumber]               NVARCHAR (255) NOT NULL,
    [Status]                      INT            NULL,
    [DivisionID]                  NVARCHAR (15)  NULL,
    [ProfileID]                   NVARCHAR (15)  NULL,
    [InvoiceProcessAssigned]      NVARCHAR (15)  NULL,
    [InvoiceProcessCompletedBy]   NVARCHAR (15)  NULL,
    [InvoiceProcessCompletedDate] DATETIME       NULL,
    [ApprovalsInternal]           BIT            NULL,
    [ApprovalsExternal]           BIT            NULL,
    [InvoiceProcessFinalizedBy]   NVARCHAR (50)  NULL,
    [InvoiceProcessFinalizedDate] DATETIME       NULL,
    [MergedInvoiceNumber]         INT            CONSTRAINT [DF_InvoicePayrollsOriginals_OriginalInvoiceNumber] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_InvoicePayrollsOriginals] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [DarwinInvoiceNumber] ASC, [InvoiceNumber] ASC, [AuditControlCode] ASC, [PayrollNumber] ASC, [MergedInvoiceNumber] ASC)
);

