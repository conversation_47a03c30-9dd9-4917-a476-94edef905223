CREATE TABLE [dbo].[ClientPayrollSchedules] (
    [CompanyID]                   INT            NOT NULL,
    [ClientID]                    NVARCHAR (15)  NOT NULL,
    [ProfileID]                   NVARCHAR (15)  NOT NULL,
    [Schedule_Status]             SMALLINT       NULL,
    [Send_TS_Date]                DATETIME       NULL,
    [Receive_TS_Date]             DATETIME       NULL,
    [Process_Date]                DATETIME       NULL,
    [Completion_Date]             DATETIME       NULL,
    [ScheduleOrigin]              SMALLINT       NULL,
    [Responsible]                 NVARCHAR (50)  NULL,
    [PayPeriod_BeginDate]         DATETIME       NULL,
    [PayPeriod_EndDate]           DATETIME       NULL,
    [CheckDate]                   DATETIME       NULL,
    [BatchNumber]                 NVARCHAR (30)  NULL,
    [AuditControlCode]            NVARCHAR (13)  NULL,
    [Frequency]                   SMALLINT       NULL,
    [Comment]                     NVARCHAR (51)  NULL,
    [Ship_Date]                   DATETIME       NULL,
    [ShippingMethod]              NVARCHAR (15)  NULL,
    [UseClientCheckbookInfo]      BIT            CONSTRAINT [DF_ClientPayrollSchedules_UseClientCheckbookInfo] DEFAULT ((0)) NOT NULL,
    [Division_ID]                 NVARCHAR (15)  NULL,
    [Schedule_ID]                 NVARCHAR (31)  NOT NULL,
    [Description]                 NVARCHAR (31)  NULL,
    [PostedDate]                  DATETIME       NULL,
    [DATE]                        DATETIME       NULL,
    [DarwinInvoiceNumber]         INT            NULL,
    [Send_TS_Time]                DATETIME       NULL,
    [Receive_TS_TIME]             DATETIME       NULL,
    [Ship_Time]                   DATETIME       NULL,
    [Process_Time]                DATETIME       NULL,
    [Schedule_Key]                INT            NOT NULL,
    [PayrollNumber]               NVARCHAR (255) CONSTRAINT [DF_ClientPayrollSchedules_PayrollNumber] DEFAULT ('') NOT NULL,
    [CompanyName]                 NVARCHAR (50)  NULL,
    [ClientName]                  NVARCHAR (50)  NULL,
    [PayrollProcessCompletedBy]   NVARCHAR (50)  NULL,
    [PayrollProcessCompletedDate] DATETIME       NULL,
    [InvoiceProcessAssigned]      NVARCHAR (50)  NULL,
    [FinalizeAssigned]            NVARCHAR (50)  NULL,
    [FinalizeCompletedBy]         NVARCHAR (50)  NULL,
    [FinalizeCompletedDate]       DATETIME       NULL,
    [DNetPayroll]                 BIT            CONSTRAINT [DF_ClientPayrollSchedules_DNetPayroll] DEFAULT ((0)) NOT NULL,
    [OffCyclePayroll]             BIT            CONSTRAINT [DF_ClientPayrollSchedules_OffCyclePayroll] DEFAULT ((0)) NOT NULL,
    [ChecksPrinted]               BIT            CONSTRAINT [DF_ClientPayrollSchedules_ChecksPrinted] DEFAULT ((0)) NOT NULL,
    [DebitDate]                   DATETIME       NULL,
    [Inactive]                    BIT            DEFAULT ((0)) NOT NULL,
    [PriorityPayroll]             BIT            CONSTRAINT [DF_ClientPayrollSchedules_PriorityPayroll] DEFAULT ((0)) NOT NULL,
    [OriginalPayrollNumber]       NVARCHAR (255) NULL,
    [Manual]                      BIT            CONSTRAINT [DF_ClientPayrollSchedules_Manual] DEFAULT ((0)) NOT NULL,
    [ManualFromSchedule]          BIT            CONSTRAINT [DF_ClientPayrollSchedules_ManualFromSchedule] DEFAULT ((0)) NOT NULL,
    [Deleted]                     BIT            CONSTRAINT [DF_ClientPayrollSchedules_Deleted] DEFAULT ((0)) NOT NULL,
    [PayrollType]                 INT            NULL,
    [ManualCheckType]             TINYINT        CONSTRAINT [DF_ClientPayrollSchedules_ManualCHeckType] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_ClientPayrollSchedules] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [ProfileID] ASC, [Schedule_ID] ASC, [Schedule_Key] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_ClientPayrollSchedules_Clients] FOREIGN KEY ([CompanyID], [ClientID]) REFERENCES [dbo].[Clients] ([CompanyID], [ClientID])
);


GO
ALTER TABLE [dbo].[ClientPayrollSchedules] NOCHECK CONSTRAINT [FK_ClientPayrollSchedules_Clients];

