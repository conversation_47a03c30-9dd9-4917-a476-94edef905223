CREATE TABLE [dbo].[Emails] (
    [id]                      INT            IDENTITY (1, 1) NOT NULL,
    [CompanyID]               INT            NULL,
    [ClientID]                NVARCHAR (15)  NULL,
    [Subject]                 NVARCHAR (100) NULL,
    [Body]                    NVARCHAR (MAX) NULL,
    [AddressTo]               NVARCHAR (250) NULL,
    [NameTo]                  NVARCHAR (250) NULL,
    [SentDateTime]            DATETIME       NULL,
    [CCList]                  NVARCHAR (MAX) NULL,
    [BCCList]                 NVARCHAR (MAX) NULL,
    [NotificationAttachments] NVARCHAR (MAX) NULL,
    [SetupID]                 INT            NULL,
    [Section]                 INT            NULL,
    [Assigned]                INT            NULL,
    [Pulled]                  BIT            CONSTRAINT [DF_Emails_Pulled] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_Emails] PRIMARY KEY CLUSTERED ([id] ASC) WITH (FILLFACTOR = 80)
);

