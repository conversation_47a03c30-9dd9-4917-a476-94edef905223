CREATE TABLE [dbo].[InvoiceChargeRateHistory] (
    [CompanyID]             INT           NOT NULL,
    [ChargeType]            NVARCHAR (50) NOT NULL,
    [ClientID]              NVARCHAR (15) NOT NULL,
    [DarwinInvoiceNumber]   INT           NOT NULL,
    [ComponentCode]         NVARCHAR (30) NOT NULL,
    [DarwinString]          NVARCHAR (30) NULL,
    [BillingRate]           INT           NULL,
    [InvoiceNumber]         NVARCHAR (30) NULL,
    [FUTASUTATaxRate]       INT           NULL,
    [MergedInvoiceNumber] INT           CONSTRAINT [DF_InvoiceChargeRateHistory_MergedInvoiceNumber] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_InvoiceChargeRateHistory] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [DarwinInvoiceNumber] ASC, [ChargeType] ASC, [ComponentCode] ASC, [MergedInvoiceNumber] ASC)
);

