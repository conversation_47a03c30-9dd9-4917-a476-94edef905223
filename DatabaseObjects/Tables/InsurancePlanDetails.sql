CREATE TABLE [dbo].[InsurancePlanDetails] (
    [CompanyID]        INT             NOT NULL,
    [Year]             INT             NOT NULL,
    [PlanCode]         NVARCHAR (6)    NOT NULL,
    [PlanName]         NVARCHAR (15)   NULL,
    [PlanCategory]     NVARCHAR (10)   NULL,
    [PlanLabels]       NVARCHAR (9)    NULL,
    [Description]      NVARCHAR (30)   NULL,
    [PlanAmount]       DECIMAL (19, 5) NULL,
    [PlanAgencyAmount] DECIMAL (19, 5) NULL,
    [PolicyNumber]     NVARCHAR (20)   NULL,
    CONSTRAINT [PK_InsurancePlanDetails] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [Year] ASC, [PlanCode] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_InsurancePlanDetails_InsurancePlans] FOREIGN KEY ([CompanyID], [Year], [PlanName]) REFERENCES [dbo].[InsurancePlans] ([CompanyID], [Year], [PlanName])
);


GO
ALTER TABLE [dbo].[InsurancePlanDetails] NOCHECK CONSTRAINT [FK_InsurancePlanDetails_InsurancePlans];

