CREATE TABLE [dbo].[ClientAddresses] (
    [CompanyID]            INT           NOT NULL,
    [ClientID]             NVARCHAR (15) NOT NULL,
    [AddressCode]          NVARCHAR (15) NOT NULL,
    [SalesPersonID]        NVARCHAR (15) NULL,
    [UPSZone]              NVARCHAR (3)  NULL,
    [ShippingMethod]       NVARCHAR (15) NULL,
    [TaxScheduleID]        NVARCHAR (15) NULL,
    [ContactPerson]        NVARCHAR (61) NULL,
    [Address1]             NVARCHAR (61) NULL,
    [Address2]             NVARCHAR (61) NULL,
    [Address3]             NVARCHAR (61) NULL,
    [Country]              NVARCHAR (35) NULL,
    [City]                 NVARCHAR (30) NULL,
    [State]                NVARCHAR (11) NULL,
    [Zip]                  NVARCHAR (21) NULL,
    [Phone1]               NVARCHAR (21) NULL,
    [Phone2]               NVARCHAR (21) NULL,
    [Phone3]               NVARCHAR (21) NULL,
    [Fax]                  NVARCHAR (21) NULL,
    [ModifiedDate]         DATETIME      NULL,
    [CreatedDate]          DATETIME      NULL,
    [GPSFOIntegrationID]   NVARCHAR (31) NULL,
    [IntegrationSource]    SMALLINT      NULL,
    [IntegrationID]        NVARCHAR (31) NULL,
    [CountryCode]          NVARCHAR (7)  NULL,
    [DeclarantID]          NVARCHAR (15) NULL,
    [LocationCode]         NVARCHAR (11) NULL,
    [SalesTerritory]       NVARCHAR (15) NULL,
    [UserDefined1]         NVARCHAR (21) NULL,
    [UserDefined2]         NVARCHAR (21) NULL,
    [ShipToName]           NVARCHAR (65) NULL,
    [Print_Phone_NumberGB] SMALLINT      NULL,
    CONSTRAINT [PK_ClientAddresses] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [AddressCode] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_ClientAddresses_Clients] FOREIGN KEY ([CompanyID], [ClientID]) REFERENCES [dbo].[Clients] ([CompanyID], [ClientID])
);


GO
ALTER TABLE [dbo].[ClientAddresses] NOCHECK CONSTRAINT [FK_ClientAddresses_Clients];

