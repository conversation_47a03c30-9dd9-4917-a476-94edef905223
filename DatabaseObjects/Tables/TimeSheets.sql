CREATE TABLE [dbo].[TimeSheets] (
    [TimeSheetID]                INT            IDENTITY (1, 1) NOT NULL,
    [CompanyID]                  INT            NOT NULL,
    [ClientID]                   NVARCHAR (15)  NOT NULL,
    [TimeSheetType]              SMALLINT       NULL,
    [TimeSheetName]              NVARCHAR (50)  NULL,
    [ProfileID]                  NVARCHAR (12)  NULL,
    [SelectionType]              SMALLINT       NULL,
    [DateFrom]                   DATETIME       NULL,
    [DateTo]                     DATETIME       NULL,
    [Status]                     SMALLINT       NULL,
    [CheckDate]                  DATETIME       NULL,
    [PageSize]                   SMALLINT       NULL,
    [RowsPerEE]                  SMALLINT       NULL,
    [SortBy]                     SMALLINT       NULL,
    [DefDays]                    REAL           NULL,
    [DefWeeks]                   REAL           NULL,
    [DefHours]                   REAL           NULL,
    [MaxRate]                    REAL           NULL,
    [MaxDayHours]                REAL           NULL,
    [MaxEEHours]                 REAL           NULL,
    [MaxEEGrossAmount]           REAL           NULL,
    [PCDecimals]                 SMALLINT       NULL,
    [BenDecimals]                SMALLINT       NULL,
    [DedDecimals]                SMALLINT       NULL,
    [PCSource]                   SMALLINT       NULL,
    [BenSource]                  SMALLINT       NULL,
    [DedSource]                  SMALLINT       NULL,
    [AutoSaveTime]               BIT            CONSTRAINT [DF_TimeSheets_AutoSaveTime] DEFAULT ((0)) NOT NULL,
    [MaskSSN]                    BIT            CONSTRAINT [DF_TimeSheets_MaskSSN] DEFAULT ((0)) NOT NULL,
    [PrintSocSecOnBlankTS]       BIT            CONSTRAINT [DF_TimeSheets_PrintSocSecOnBlankTS] DEFAULT ((0)) NOT NULL,
    [PrintSocSecOnReports]       BIT            CONSTRAINT [DF_TimeSheets_PrintSocSecOnReports] DEFAULT ((0)) NOT NULL,
    [PrintRateOnBlankTS]         BIT            CONSTRAINT [DF_TimeSheets_PrintRateOnBlankTS] DEFAULT ((0)) NOT NULL,
    [PrintRateOnReports]         BIT            CONSTRAINT [DF_TimeSheets_PrintRateOnReports] DEFAULT ((0)) NOT NULL,
    [PrintYTDOnBlankTS]          BIT            CONSTRAINT [DF_TimeSheets_PrintYTDOnBlankTS] DEFAULT ((0)) NOT NULL,
    [PrintYTDOnReports]          BIT            CONSTRAINT [DF_TimeSheets_PrintYTDOnReports] DEFAULT ((0)) NOT NULL,
    [IgnoreWaiting]              BIT            CONSTRAINT [DF_TimeSheets_IgnoreWaiting] DEFAULT ((0)) NOT NULL,
    [ShiftStatus]                SMALLINT       NULL,
    [AllowModifiedRate]          BIT            CONSTRAINT [DF_TimeSheets_AllowModifiedRate] DEFAULT ((0)) NOT NULL,
    [AllowModifiedRatePermanent] BIT            CONSTRAINT [DF_TimeSheets_AllowModifiedRatePermanent] DEFAULT ((0)) NOT NULL,
    [AllowAddEE]                 BIT            CONSTRAINT [DF_TimeSheets_AllowAddEE] DEFAULT ((0)) NOT NULL,
    [AllowAddCode]               BIT            CONSTRAINT [DF_TimeSheets_AllowAddCode] DEFAULT ((0)) NOT NULL,
    [AllowTimeEntry]             BIT            CONSTRAINT [DF_TimeSheets_AllowTimeEntry] DEFAULT ((0)) NOT NULL,
    [EEDeptStatus]               SMALLINT       NULL,
    [EEPositionStatus]           SMALLINT       NULL,
    [Creator]                    NVARCHAR (20)  NULL,
    [User]                       NVARCHAR (20)  NULL,
    [UseJBS]                     BIT            CONSTRAINT [DF_TimeSheets_UseJBS] DEFAULT ((0)) NOT NULL,
    [Comment]                    NVARCHAR (MAX) NULL,
    [Requests]                   NVARCHAR (MAX) NULL,
    [ApprovalType]               SMALLINT       NULL,
    [ApprovalStatus]             SMALLINT       NULL,
    [PEOApprovalRequired]        BIT            CONSTRAINT [DF_TimeSheets_PEOApprovalRequired] DEFAULT ((0)) NOT NULL,
    [UseDepDescr]                BIT            CONSTRAINT [DF_TimeSheets_UseDepDescr] DEFAULT ((0)) NOT NULL,
    [UsePosDescr]                BIT            CONSTRAINT [DF_TimeSheets_UsePosDescr] DEFAULT ((0)) NOT NULL,
    [DateCreated]                DATETIME       NULL,
    [HideOfferedHours]           BIT            CONSTRAINT [DF_TimeSheets_HideOfferedHours] DEFAULT ((0)) NOT NULL,
    [HideCheckNumber]            BIT            CONSTRAINT [DF_TimeSheets_HideCheckNumber] DEFAULT ((0)) NOT NULL,
    [HideWeeks]                  BIT            CONSTRAINT [DF_TimeSheets_HideWeeks] DEFAULT ((0)) NOT NULL,
    [HideDays]                   BIT            CONSTRAINT [DF_TimeSheets_HideDays] DEFAULT ((0)) NOT NULL,
    [LaborDistribution1Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution1Status] DEFAULT ((0)) NOT NULL,
    [LaborDistribution2Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution2Status] DEFAULT ((0)) NOT NULL,
    [LaborDistribution3Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution3Status] DEFAULT ((0)) NOT NULL,
    [LaborDistribution4Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution4Status] DEFAULT ((0)) NOT NULL,
    [LaborDistribution5Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution5Status] DEFAULT ((0)) NOT NULL,
    [LaborDistribution6Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution6Status] DEFAULT ((0)) NOT NULL,
    [LaborDistribution7Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution7Status] DEFAULT ((0)) NOT NULL,
    [LaborDistribution8Status]   SMALLINT       CONSTRAINT [DF_TimeSheets_LaborDistribution8Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution1Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution1Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution2Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution2Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution3Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution3Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution4Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution4Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution5Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution5Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution6Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution6Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution7Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution7Status] DEFAULT ((0)) NOT NULL,
    [EELaborDistribution8Status] SMALLINT       CONSTRAINT [DF_TimeSheets_EELaborDistribution8Status] DEFAULT ((0)) NOT NULL,
    [PayrollProfileID]           NVARCHAR (15)  NULL,
    [PayrollNumber]              NVARCHAR (255) NULL,
    [PayPeriod]                  SMALLINT       NULL,
    [TRXEmployees]               INT            NULL,
    [GroupByJobLevel]            SMALLINT       CONSTRAINT [DF_TimeSheets_GroupByJobLevel] DEFAULT ((0)) NOT NULL,
    [RecurringBatch]             BIT            CONSTRAINT [DF_TimeSheets_RecurringBatch] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_TimeSheets] PRIMARY KEY CLUSTERED ([TimeSheetID] ASC) WITH (FILLFACTOR = 80)
);

