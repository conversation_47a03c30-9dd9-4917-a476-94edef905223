CREATE TABLE [dbo].[ClientJobCostAssignmentDetails] (
    [CompanyID]           INT             NOT NULL,
    [ClientID]            NVARCHAR (15)   NOT NULL,
    [JobCostingName]      NVARCHAR (30)   NOT NULL,
    [JobLevel2]           NVARCHAR (30)   NOT NULL,
    [JobLevel3]           NVARCHAR (30)   NOT NULL,
    [JobLevel4]           NVARCHAR (30)   NOT NULL,
    [JobLevel5]           NVARCHAR (30)   NOT NULL,
    [JobLevel6]           NVARCHAR (30)   NOT NULL,
    [JBSID]               NVARCHAR (6)    NOT NULL,
    [SequenceNumber]      INT             NULL,
    [Description2]        NVARCHAR (50)   NULL,
    [Description3]        NVARCHAR (50)   NULL,
    [Description4]        NVARCHAR (50)   NULL,
    [Description5]        NVARCHAR (50)   NULL,
    [Description6]        NVARCHAR (50)   NULL,
    [EstimatedStartDate]  DATETIME        NULL,
    [EstimatedCompletion] DATETIME        NULL,
    [EstimatedHours]      INT             NULL,
    [Cost]                DECIMAL (19, 5) NULL,
    [ProjectManager]      NVARCHAR (30)   NULL,
    [Phone1]              NVARCHAR (14)   NULL,
    [Email]               NVARCHAR (255)  NULL,
    [WorkersComp]         NVARCHAR (6)    NULL,
    [Department]          NVARCHAR (6)    NULL,
    [DefaultHourlyCode]   NVARCHAR (6)    NULL,
    [DefaultOTCode]       NVARCHAR (6)    NULL,
    [DefaultSalaryCode]   NVARCHAR (6)    NULL,
    [DefaultSalOTCode]    NVARCHAR (6)    NULL,
    [StateCode]           NVARCHAR (2)    NULL,
    [LocalTax]            NVARCHAR (6)    NULL,
    [RegularRate]         DECIMAL (19, 5) NULL,
    [OTRate]              DECIMAL (19, 5) NULL,
    [DefaultSalaryRate]   DECIMAL (19, 5) NULL,
    [DefaultSalaryOTRate] DECIMAL (19, 5) NULL,
    CONSTRAINT [PK_ClientJobCostAssignmentDetails_1] PRIMARY KEY CLUSTERED ([CompanyID] ASC, [ClientID] ASC, [JobCostingName] ASC, [JobLevel2] ASC, [JobLevel3] ASC, [JobLevel4] ASC, [JobLevel5] ASC, [JobLevel6] ASC) WITH (FILLFACTOR = 80),
    CONSTRAINT [FK_ClientJobCostAssignmentDetails_ClientJobCostAssignments] FOREIGN KEY ([CompanyID], [ClientID], [JobCostingName]) REFERENCES [dbo].[ClientJobCostAssignments] ([CompanyID], [ClientID], [JobCostingName])
);


GO
ALTER TABLE [dbo].[ClientJobCostAssignmentDetails] NOCHECK CONSTRAINT [FK_ClientJobCostAssignmentDetails_ClientJobCostAssignments];

