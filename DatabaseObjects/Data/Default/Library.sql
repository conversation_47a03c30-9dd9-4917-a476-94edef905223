SET IDENTITY_INSERT [dbo].[Library] ON 
IF EXISTS(SELECT 1 FROM [dbo].[Library] WHERE [id] = 1)
BEGIN
	UPDATE [dbo].[Library] SET [CompanyID] = 1, [FolderName] = N'PEO Level Documents', [ParentFolder] = 0, [AssignToClientDocs] = 0, [AssignToEEDocs] = 1, [UseSystem] = 1, [UseClient] = 1, [UseEmployee] = 1, [Source] = N'Provider' WHERE [id] = 1
END
ELSE
BEGIN
	INSERT [dbo].[Library] ([id], [CompanyID], [FolderName], [ParentFolder], [AssignToClientDocs], [AssignToEEDocs], [UseSystem], [UseClient], [UseEmployee], [Source]) VALUES (1, 1, N'PEO Level Documents', 0, 0, 1, 1, 1, 1, N'Provider')
END
IF EXISTS(SELECT 1 FROM [dbo].[Library] WHERE [id] = 2)
BEGIN
	UPDATE [dbo].[Library] SET [CompanyID] = 1, [FolderName] = N'Client Level Documents', [ParentFolder] = 0, [AssignToClientDocs] = 1, [AssignToEEDocs] = 0, [UseSystem] = 1, [UseClient] = 1, [UseEmployee] = 1, [Source] = N'Client document' WHERE [id] = 2
END
ELSE
BEGIN
	INSERT [dbo].[Library] ([id], [CompanyID], [FolderName], [ParentFolder], [AssignToClientDocs], [AssignToEEDocs], [UseSystem], [UseClient], [UseEmployee], [Source]) VALUES (2, 1, N'Client Level Documents', 0, 1, 0, 1, 1, 1, N'Client document')
END
IF EXISTS(SELECT 1 FROM [dbo].[Library] WHERE [id] = 3)
BEGIN
	UPDATE [dbo].[Library] SET [CompanyID] = 1, [FolderName] = N'Employee Level Documents', [ParentFolder] = 0, [AssignToClientDocs] = 0, [AssignToEEDocs] = 1, [UseSystem] = 1, [UseClient] = 1, [UseEmployee] = 1, [Source] = N'EE document' WHERE [id] = 3
END
ELSE
BEGIN
	INSERT [dbo].[Library] ([id], [CompanyID], [FolderName], [ParentFolder], [AssignToClientDocs], [AssignToEEDocs], [UseSystem], [UseClient], [UseEmployee], [Source]) VALUES (3, 1, N'Employee Level Documents', 0, 0, 1, 1, 1, 1, N'EE document')
END
IF EXISTS(SELECT 1 FROM [dbo].[Library] WHERE [id] = 4)
BEGIN
	UPDATE [dbo].[Library] SET [CompanyID] = 1, [FolderName] = N'Attachment', [ParentFolder] = 3, [AssignToClientDocs] = 0, [AssignToEEDocs] = 1, [UseSystem] = 1, [UseClient] = 1, [UseEmployee] = 1, [Source] = N'Attachment' WHERE [id] = 4
END
ELSE
BEGIN
	INSERT [dbo].[Library] ([id], [CompanyID], [FolderName], [ParentFolder], [AssignToClientDocs], [AssignToEEDocs], [UseSystem], [UseClient], [UseEmployee], [Source]) VALUES (4, 1, N'Attachments', 3, 0, 1, 1, 1, 1, N'Attachment')
END
IF EXISTS(SELECT 1 FROM [dbo].[Library] WHERE [id] = 5)
BEGIN
	UPDATE [dbo].[Library] SET [CompanyID] = 1, [FolderName] = N'On Boarding', [ParentFolder] = 3, [AssignToClientDocs] = 0, [AssignToEEDocs] = 1, [UseSystem] = 1, [UseClient] = 1, [UseEmployee] = 1, [Source] = N'On Boarding' WHERE [id] = 5
END
ELSE
BEGIN
	INSERT [dbo].[Library] ([id], [CompanyID], [FolderName], [ParentFolder], [AssignToClientDocs], [AssignToEEDocs], [UseSystem], [UseClient], [UseEmployee], [Source]) VALUES (5, 1, N'On Boarding', 3, 0, 1, 1, 1, 1, N'On Boarding')
END
IF EXISTS(SELECT 1 FROM [dbo].[Library] WHERE [id] = 6)
BEGIN
	UPDATE [dbo].[Library] SET [CompanyID] = 1, [FolderName] = N'PEO CC Level Documents', [ParentFolder] = 0, [AssignToClientDocs] = 1, [AssignToEEDocs] = 0, [UseSystem] = 1, [UseClient] = 1, [UseEmployee] = 1, [Source] = N'Provider' WHERE [id] = 6
END
ELSE
BEGIN
	INSERT [dbo].[Library] ([id], [CompanyID], [FolderName], [ParentFolder], [AssignToClientDocs], [AssignToEEDocs], [UseSystem], [UseClient], [UseEmployee], [Source]) VALUES (6, 1, N'PEO CC Level Documents', 0, 1, 0, 1, 1, 0, N'Provider')
END
IF EXISTS(SELECT 1 FROM [dbo].[Library] WHERE [id] = 7)
BEGIN
	UPDATE [dbo].[Library] SET [CompanyID] = 1, [FolderName] = N'Open Enrollment', [ParentFolder] = 3, [AssignToClientDocs] = 0, [AssignToEEDocs] = 1, [UseSystem] = 1, [UseClient] = 1, [UseEmployee] = 1, [Source] = N'Enrollment' WHERE [id] = 7
END
ELSE
BEGIN
	INSERT [dbo].[Library] ([id], [CompanyID], [FolderName], [ParentFolder], [AssignToClientDocs], [AssignToEEDocs], [UseSystem], [UseClient], [UseEmployee], [Source]) VALUES (7, 1, N'OpenEnrollment', 3, 0, 1, 1, 1, 1, N'Enrollment')
END
SET IDENTITY_INSERT [dbo].[Library] OFF
GO

