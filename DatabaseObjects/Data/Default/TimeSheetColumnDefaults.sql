DELETE FROM [dbo].[TimeSheetColumnDefaults]
GO
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 1, N'TimeSheetID', 1, N'Time Sheet ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 2, N'EmployeeID', 2, N'Employee ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 3, N'EmployeeName', 3, N'Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 4, N'EmployeeSSN', 4, N'SSN', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 5, N'CodeSection', 32, N'Code Section', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 6, N'Department', 14, N'Department', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 7, N'Position', 15, N'Position', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 8, N'StateTax', 16, N'State Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 9, N'LocalTax', 17, N'Local Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 10, N'SutaState', 18, N'Suta State', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 11, N'WorkComp', 19, N'Workers Comp', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 12, N'LDSection', 41, N'LD Section', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 13, N'OfferedHours', 13, N'Offered Hours', 2, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 14, N'CheckNbr', 12, N'Check Number', 2, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 15, N'Days', 20, N'Days', 2, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (1, 16, N'Weeks', 21, N'Weeks', 2, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 1, N'TimeSheetID', 1, N'Time Sheet ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 2, N'EmployeeID', 2, N'Employee ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 3, N'EmployeeName', 3, N'Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 4, N'EmployeeSSN', 4, N'SSN', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 5, N'JobName', 6, N'Job Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 6, N'JobLevel2', 7, N'Level 2', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 7, N'CertDaysSection', 43, N'Cert Days Section', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 8, N'Department', 14, N'Department', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 9, N'Position', 15, N'Position', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 10, N'StateTax', 16, N'State Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 11, N'LocalTax', 17, N'Local Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 12, N'SutaState', 18, N'Suta State', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (2, 13, N'WorkComp', 19, N'Workers Comp', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 1, N'TimeSheetID', 1, N'Time Sheet ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 2, N'EmployeeID', 2, N'Employee ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 3, N'EmployeeName', 3, N'Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 4, N'EmployeeSSN', 4, N'SSN', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 5, N'RegCode', 30, N'Pay Code', 2, 0, 1, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 6, N'RegHours', 23, N'Reg Hours', 2, 1, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 7, N'RegRate', 25, N'Reg Rate', -2, 2, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 8, N'RegShift', 26, N'Reg Shift', -4, 0, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 9, N'RegPremium', 27, N'Reg Premium', -2, 0, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 10, N'RegReduction', 29, N'Reg Reduction', -4, 0, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 11, N'OTCode', 31, N'OT Code', 0, 0, 6, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 12, N'OTHours', 23, N'OT Hours', 2, 1, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 13, N'OTRate', 25, N'OT Rate', -2, 2, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 14, N'OTShift', 26, N'OT Shift', -4, 0, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 15, N'OTPremium', 27, N'OT Premium', -2, 0, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 16, N'OTReduction', 29, N'OT Reduction', -4, 0, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 18, N'Department', 14, N'Department', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 19, N'Position', 15, N'Position', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 20, N'JobName', 6, N'Job Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 21, N'JobLevel2', 7, N'Level 2', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 22, N'JobLevel3', 8, N'Level 3', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 23, N'JobLevel4', 9, N'Level 4', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 25, N'JobLevel5', 10, N'Level 5', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 26, N'JobLevel6', 11, N'Level 6', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 27, N'StateTax', 16, N'State Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 28, N'LocalTax', 17, N'Local Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 29, N'SutaState', 18, N'Suta State', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 30, N'WorkComp', 19, N'Workers Comp', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (3, 31, N'SalaryLine', 38, N'Salary Line', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 1, N'TimeSheetID', 1, N'Time Sheet ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 2, N'EmployeeID', 2, N'Employee ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 3, N'EmployeeName', 3, N'Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 4, N'EmployeeSSN', 4, N'SSN', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 5, N'RegCode', 30, N'Pay Code', 2, 0, 1, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 6, N'RegHours', 23, N'Reg Hours', 2, 1, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 7, N'RegRate', 25, N'Reg Rate', -2, 2, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 8, N'RegShift', 26, N'Reg Shift', -4, 0, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 9, N'RegPremium', 27, N'Reg Premium', -2, 0, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 10, N'RegReduction', 29, N'Reg Reduction', -4, 0, 1, N'RegCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 11, N'OTCode', 31, N'OT Code', 0, 0, 6, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 12, N'OTHours', 23, N'OT Hours', 2, 1, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 13, N'OTRate', 25, N'OT Rate', -2, 2, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 14, N'OTShift', 26, N'OT Shift', -4, 0, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 15, N'OTPremium', 27, N'OT Premium', -2, 0, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 16, N'OTReduction', 29, N'OT Reduction', -4, 0, 6, N'OTCode')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 18, N'Department', 14, N'Department', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 19, N'Position', 15, N'Position', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 20, N'JobName', 6, N'Job Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 21, N'JobLevel2', 7, N'Level 2', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 22, N'JobLevel3', 8, N'Level 3', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 23, N'JobLevel4', 9, N'Level 4', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 25, N'JobLevel5', 10, N'Level 5', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 26, N'JobLevel6', 11, N'Level 6', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 27, N'StateTax', 16, N'State Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 28, N'LocalTax', 17, N'Local Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 29, N'SutaState', 18, N'Suta State', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 30, N'WorkComp', 19, N'Workers Comp', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (4, 31, N'SalaryLine', 38, N'Salary Line', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (5, 1, N'TimeEntryID', 1, N'Time Entry ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (5, 2, N'TimeEntryDate', 36, N'Date', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (5, 3, N'CodeSection', 32, N'Code Section', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (5, 4, N'Department', 14, N'Department', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (5, 5, N'Position', 15, N'Position', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 1, N'TimeSheetID', 1, N'Time Sheet ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 2, N'EmployeeID', 2, N'Employee ID', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 3, N'EmployeeName', 3, N'Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 4, N'EmployeeSSN', 4, N'SSN', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 5, N'JobName', 6, N'Job Name', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 6, N'JobLevel2', 7, N'Level 2', 1, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 7, N'CertDaysSection', 43, N'Cert Days Section', 0, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 8, N'Department', 14, N'Department', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 9, N'Position', 15, N'Position', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 10, N'StateTax', 16, N'State Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 11, N'LocalTax', 17, N'Local Tax', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 12, N'SutaState', 18, N'Suta State', 3, 0, 0, N'')
INSERT [dbo].[TimeSheetColumnDefaults] ([TimeSheetType], [ColNbr], [Name], [ColType], [Label], [Status], [ValidationType], [CodeType], [BasedOn]) VALUES (6, 13, N'WorkComp', 19, N'Workers Comp', 3, 0, 0, N'')
GO
