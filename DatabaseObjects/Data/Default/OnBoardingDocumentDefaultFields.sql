DELETE FROM [dbo].[OnBoardingDocumentDefaultFields]
GO
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Additional', N'Employees', N'AdditionalFederalWithholding', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Address 2', N'EmployeeAddresses', N'Address2', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Address 3', N'EmployeeAddresses', N'Address3', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Alien_USCIS Number', N'EmployeeEligibilityVerification', N'AlienNbr', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Amount', N'EmployeeDeductions', N'DeductionAmount1', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Automatic', N'EmployeeLocalTaxes', N'AutomaticLocalTax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Bank Account', N'EmployeeDirectDeposits', N'AccountNumber', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Bank ID', N'EmployeeDirectDeposits', N'BankID', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Based On', N'EmployeeDirectDeposits', N'DirectDepositBasedOnType', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Based On Code', N'EmployeePaycodes', N'BasePayRecord', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Benefit Code', N'EmployeeBenefits', N'Benefit', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Benefit Method', N'EmployeeBenefits', N'BenefitMethod', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Benefit Pay Period', N'EmployeeBenefits', N'BenefitFrequency', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Benefit Percent Rate', N'EmployeeBenefits', N'BenefitPercent1', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Benefit Start Date', N'EmployeeBenefits', N'BenefitBeginDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Birthdate', N'Employees', N'BirthDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Certification Date', N'EmployeeEligibilityVerification', N'CCCertDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Certification IP', N'EmployeeEligibilityVerification', N'CCCertIP', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'City', N'EmployeeAddresses', N'City', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Client', N'Client/PEO', N'Client Name', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Client ID', N'Employees', N'ClientID', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Completion Date', N'EmployeeEligibilityVerification', N'EECompleteDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'CountryOfIssuance', N'EmployeeEligibilityVerification', N'PassportCountry', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Date', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Deduction Code', N'EmployeeBenefits', N'Deduction', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Deduction Method', N'EmployeeDeductions', N'DeductionMethod', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Deduction Pay Period', N'EmployeeDeductions', N'DeductionFrequency', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Deduction Percent Rate', N'EmployeeDeductions', N'DeductionPercent1', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Deduction Start Date', N'EmployeeDeductions', N'DeductionBegDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Default Local', N'Employees', N'LocalTax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Default State', N'Employees', N'StateCode', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Department', N'Employees', N'Department', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Dependent BirthDate', N'EmployeeDependents', N'BirthDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Dependent FirstName', N'EmployeeDependents', N'FirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Dependent Gender', N'EmployeeDependents', N'Gender', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Dependent LastName', N'EmployeeDependents', N'LastName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Dependent MiddleName', N'EmployeeDependents', N'MiddleName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Dependent SSN', N'EmployeeDependents', N'SSN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Dependent SSN#', N'EmployeeDependents', N'SocialSecNumber', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Deposit Option', N'EmployeeDirectDeposits', N'DirectDepositOptions', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Deposit Percent Rate', N'EmployeeDirectDeposits', N'Percentages', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Description', N'EmployeeDeductions', N'Description', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Disabled', N'EmployeeDependents', N'Disabled', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Authority_A1', N'EmployeeEligibilityVerification', N'DocA1Authority', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Authority_A2', N'EmployeeEligibilityVerification', N'DocA2Authority', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Authority_A3', N'EmployeeEligibilityVerification', N'DocA3Authority', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Authority_B', N'EmployeeEligibilityVerification', N'DocBAuthority', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Authority_C', N'EmployeeEligibilityVerification', N'DocCAuthority', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Exp_Date_A1', N'EmployeeEligibilityVerification', N'DocA1ExpDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Exp_Date_A2', N'EmployeeEligibilityVerification', N'DocA2ExpDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Exp_Date_A3', N'EmployeeEligibilityVerification', N'DocA3ExpDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Exp_Date_B', N'EmployeeEligibilityVerification', N'DocBExpDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Exp_Date_C', N'EmployeeEligibilityVerification', N'DocCExpDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Number_A1', N'EmployeeEligibilityVerification', N'DocA1Number', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Number_A2', N'EmployeeEligibilityVerification', N'DocA2Number', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Number_A3', N'EmployeeEligibilityVerification', N'DocA3Number', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Number_B', N'EmployeeEligibilityVerification', N'DocBNumber', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Number_C', N'EmployeeEligibilityVerification', N'DocCNumber', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Title_A1', N'EmployeeEligibilityVerification', N'DocA1Title', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Title_A2', N'EmployeeEligibilityVerification', N'DocA2Title', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Title_A3', N'EmployeeEligibilityVerification', N'DocA3Title', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Title_B', N'EmployeeEligibilityVerification', N'DocBTitle', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Title_C', N'EmployeeEligibilityVerification', N'DocCTitle', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Type_A1', N'EmployeeEligibilityVerification', N'DocA1Type', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Type_A2', N'EmployeeEligibilityVerification', N'DocA2Type', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Type_A3', N'EmployeeEligibilityVerification', N'DocA3Type', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Type_B', N'EmployeeEligibilityVerification', N'DocBType', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Doc_Type_C', N'EmployeeEligibilityVerification', N'DocCType', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE Class', N'Employees', N'EmployeeClass', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE Date', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE First Name', N'Employees', N'FirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE Last Name', N'Employees', N'LastName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE Signature', N'Signature', N'EE Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE Signature Date', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE_DATE', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE_Passport_Number', N'EmployeeEligibilityVerification', N'ForeignPassportNbr', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE_Reg_Exp_Date', N'EmployeeEligibilityVerification', N'WorkExpDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE_Reg_Number', N'EmployeeEligibilityVerification', N'AlienNbr', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EE_Signature', N'Signature', N'EE Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EESignature', N'Signature', N'EE Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Effective Date', N'EmployeeLicensesAndCertifications', N'EffectiveDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EIN', N'Client/PEO', N'EIN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Email', N'Employees', N'Email', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Emergency Contact', N'Employees', N'EmergencyContact', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Emergency Phone', N'Employees', N'EmergencyPhone', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employee First Name', N'Employees', N'FirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employee ID', N'Employees', N'EmployeeID', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employee Last Name', N'Employees', N'LastName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employee Signature', N'Signature', N'EE Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employee Signature Date', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployeeSig', N'Signature', N'EE Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployeeSignature', N'Signature', N'EE Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employer', N'Client/PEO', N'Client Name', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employer Date', N'Signature', N'ER Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employer Signature', N'Signature', N'ER Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employer Signature Date', N'Signature', N'ER Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employer Title', N'EmployeeEligibilityVerification', N'CCTitle', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployerCity', N'Client/PEO', N'Client City', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployerSig', N'Signature', N'ER Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployerSigDate', N'Signature', N'ER Sign Date', NULL)
GO
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployerState', N'Client/PEO', N'Client State', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployerStreet', N'Client/PEO', N'Client Street Address', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'EmployerZIP', N'Client/PEO', N'Client ZIP', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Employment Status', N'Employees', N'TypeOfEmployee', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'ER Date', N'Signature', N'ER Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'ER Signature', N'Signature', N'ER Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'ER Signature Date', N'Signature', N'ER Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'ER_Date', N'Signature', N'ER Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'ER_Signature', N'Signature', N'ER Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Estimated', N'Employees', N'EstimatedFederalWithholding', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Estimated Amount', N'EmployeeStateTaxes', N'EstimatedStateWithholding', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Ethnic Origin', N'Employees', N'EthnicOrigin', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Exemptions', N'Employees', N'FederalExemptions', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Expiration Date', N'EmployeeLicensesAndCertifications', N'ExpirationDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Federal Flat Tax Rate', N'EmployeePaycodes', N'FlatFederalTaxRate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Filing Status', N'Employees', N'FederalFilinglStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'First Name', N'Employees', N'FirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'FirstName', N'Employees', N'FirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Full Time Student', N'EmployeeDependents', N'OverageStudent', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Gender', N'Employees', N'Gender', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'I94_Number', N'EmployeeEligibilityVerification', N'I94Nbr', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Job Category', N'Employees', N'JobCategory', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Last Name', N'Employees', N'LastName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'LastName', N'Employees', N'LastName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'License Description', N'EmployeeLicensesAndCertifications', N'Comments', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'License ID', N'EmployeeLicensesAndCertifications', N'LicenseCertificationID', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'License Number', N'EmployeeLicensesAndCertifications', N'LicenseCertificationNumber', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Lifetime Max', N'EmployeePaycodes', N'PayTypeLifetimeMax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Lives With EE', N'EmployeeDependents', N'LivesWithEmployee', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Local Code', N'EmployeeLocalTaxes', N'LocalTax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Local Filing Status', N'EmployeeLocalTaxes', N'LocalFilingStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Marital Status', N'Employees', N'MaritalStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Middle', N'Employees', N'MiddleName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Middle Name', N'Employees', N'MiddleName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'MiddleName', N'Employees', N'MiddleName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Monthly Max', N'EmployeePaycodes', N'PayTypeMonthMax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'MStatus_Married', N'Employees', N'MaritalStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'MStatus_Single', N'Employees', N'MaritalStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'MStatus_SingleRate', N'Employees', N'MaritalStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Multi Tax Withholding', N'Employees', N'MultiTaxWithholding', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Need Translator', N'EmployeeEligibilityVerification', N'UsePreparer', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Original Hire Date', N'Employees', N'OriginalHireDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Other Names Used (if any)', N'EmployeeEligibilityVerification', N'OtherName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Pay Code', N'EmployeePaycodes', N'PayRecord', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Pay Code Description', N'EmployeePaycodes', N'Description', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Pay Period', N'Employees', N'EmployeePayFrequency', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Pay Period Max', N'EmployeePaycodes', N'PayTypePayPeriodMax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Pay Rate', N'EmployeePaycodes', N'PayRateAmount', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Pay Type', N'EmployeePaycodes', N'PayType', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Pay Unit', N'EmployeePaycodes', N'PayUnit', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'PayCode Pay Period', N'EmployeePaycodes', N'PayUnitPeriod', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Percent Rate', N'EmployeeDeductions', N'DeductionPercent1', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Phone 2', N'EmployeeAddresses', N'Phone2', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Phone Home', N'EmployeeAddresses', N'Phone1', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Position', N'Employees', N'Position', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Primary Code', N'EmployeeLocalTaxes', N'PRIMARYTAX', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'PTO Plan', N'EmployeePTOTypes', N'PTOType', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Reciprocal Code', N'EmployeeLocalTaxes', N'ReciprocalTax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Relationship', N'EmployeeDependents', N'Relationship', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Renewal Date', N'EmployeeTraining', N'RenewalDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Resident Status', N'EmployeeEligibilityVerification', N'ResidentStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'ResidentState', N'Employees', N'ResidentState', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SigDate', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SigDate_Day', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SigDate_Month', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SigDate_Year', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Signature', N'Signature', N'EE Signature', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Signature Date', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SignatureDate', N'Signature', N'EE Sign Date', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SpouseName', N'EmployeeDependents', N'FirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SpouseSSN', N'EmployeeDependents', N'SSN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SSN', N'Employees', N'SSN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SSN#', N'Employees', N'SSN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SSN1', N'Employees', N'SSN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SSN2', N'Employees', N'SSN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SSN3', N'Employees', N'SSN', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Start Date', N'Employees', N'StartDate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'State', N'EmployeeAddresses', N'State', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'State Additional Allowance', N'EmployeeStateTaxes', N'AdditionalAllowances', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'State Additional Amount', N'EmployeeStateTaxes', N'AdditionalStateWithholding', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'State Dependents', N'EmployeeStateTaxes', N'Dependents', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'State Filing Status', N'EmployeeStateTaxes', N'TaxFilingStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'State Flat Tax Rate', N'EmployeePaycodes', N'FlatStateTaxRate', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'State Tax State', N'EmployeeStateTaxes', N'StateCode', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Status', N'EmployeeTraining', N'TrainingStatus', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Street', N'EmployeeAddresses', N'Address1', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Street Address', N'EmployeeAddresses', N'Address1', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'SUTA State', N'Employees', N'SUTAState', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Training Type', N'EmployeeTraining', N'TrainingType', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Translator_First_Name', N'EmployeeEligibilityVerification', N'PrepFirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Translator_Last_Name', N'EmployeeEligibilityVerification', N'PrepLastName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Translator_State', N'EmployeeEligibilityVerification', N'PrepState', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Translator_ZIP', N'EmployeeEligibilityVerification', N'PrepZip', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'WC Code', N'Employees', N'WorkersComp', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Year Max', N'EmployeePaycodes', N'PayTypeYearMax', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Your First Name', N'EmployeeEligibilityVerification', N'CCFirstName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Your Last Name', N'EmployeeEligibilityVerification', N'CCLastName', NULL)
INSERT [dbo].[OnBoardingDocumentDefaultFields] ([PdfName], [DB_Table], [DB_Field], [SpecAction]) VALUES (N'Zip', N'EmployeeAddresses', N'ZIP', NULL)
GO
