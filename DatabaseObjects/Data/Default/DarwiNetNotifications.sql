IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 1)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [Client<PERSON>ilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 1, 1, N'New Timesheet Approval from [CLIENTNAME] [CLIENTID]/[COMPANYNAME] on [TSDATE]', N'<p>[CLIENTNAME] [CLIENTID] has approved and submitted the following timesheet:</p><p>Pay Period Start Date = [PPSTART]</p><p>Pay Period End Date = [PPEND]</p><p>Check Date = [CHECKDATE]</p><p>Timesheet ID = [TIMESHEETID]</p><p>Comments = [TSCOMMENTS]</p><p>UserID/User Name = [TSUSERID]/[TSUSERNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 1
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 2)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 2, 1, N'Login to see your updated Payroll information ', N'<p>Dear [FIRSTNAME],<br /><br />Your online payroll information has been updated. Your pay check for [CHECKDATE] covering the Pay Period of [PPSTART] to [PPEND] is now available in your online payroll portal. </p><p>Please login at [EMPLLINK] [/EMPLLINK] to view this information. </p><p>If you have questions, please contact us at:</p><p>Phone : [PEOPHONE]</p><p>Email : [PEOMAIL]</p><p>Thank you,</p><p>[PEONAME]</p>', 0, 0, 1, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=1, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 2
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 3)
BEGIN
     INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 3, 1, N'DNet Change Request Notification', N'<p>The following [CHRQCATEGORY] regarding Change Request ID [CHRQID] has been processed for the following:</p><p>Company = [COMPANYNAME]</p><p>Client ID = [CLIENTID]</p><p>Client Name = [CLIENTNAME]</p><p>Employee First Name = [FIRSTNAME]</p><p>Employee Last Name = [LASTNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 3
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 4)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 4, 1, N'Password Change Confirmation', N'<p>Hello [USERID],<br /><br />We have processed a password change request for your access to our portal [COMPANYNAME].</p><p>If you did not authorize this change, please contact us immediately at:</p><p>Phone : [PEOPHONE]</p><p>Email : [PEOMAIL]</p><p>Thank you,</p><p>[PEONAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 4
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 5)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 5, 1, N'Your Recent PTO Request has been processed', N'<p>Dear [FIRSTNAME],</p><p>Your recent request for PTO [PTOPLANID] [PTOPLANDESC] has been [PTORQSTATUS].</p><p>The details of the request are as follows:</p><p>PTO Request Start Date&nbsp;= [PTORQSTDATE]</p><p>PTO Request End Date&nbsp;= [PTORQENDDATE]</p><p>PTO Request Comment = [PTORQCOMMENT]</p><p>PTO Hours =&nbsp;[REQHOURS]<br /></p><p>Thank You,</p><p>[CLIENTNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 5
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 6)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 6, 1, N'PTO Request Notification', N'<p>[FIRSTNAME] [LASTNAME] has submitted a PTO Request.</p><p></p><p>The Details of the request are as follows:</p><p></p><p>PTO Plan ID = [PTOPLANID]</p><p>PTO Plan Description =[PTOPLANDESC]</p><p>PTO Request Start Date = [PTORQSTDATE]</p><p>PTO Request End Date = [PTORQENDDATE]</p><p>PTO Request Comment = [PTORQCOMMENT]</p><p>PTO Request Hours =&nbsp;[REQHOURS]</p><p></p><p>Please login [EMPLLINK] to process the request.</p><p></p><p>Thank you,</p><p>[COMPANYNAME]</p>', 0, 0, 0, 1, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=1, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=1, [SYAssign]=0 WHERE [Type] = 6
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 7)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 7, 1, N'New Invoice Preview Approval from [CLIENTNAME] [CLIENTID]/[COMPANYNAME] on [INVOICEAPPRTIME]', N'<p>[CLIENTNAME] [CLIENTID] has [INVOICEAPPROVEDENY] and submitted the following Invoice Preview:</p><p>Invoice Date = [INVOICEDATE]</p><p>Invoice Number =&nbsp;[INVOICENUMBER]</p><p>Check Date = [INVOICECHKDATE]</p><p>Comments = [INVOICECOMMENT]</p><p>UserID/User Name = [INVOICEUSERID]/[INVOICEUSERNAME]</p><p></p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 7
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 8)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 8, 1, N'New Invoice Available', N'<p>Hello [CLIENTNAME],</p><p>A new invoice is now available online.</p><p>Invoice Date = [INVOICEDATE]</p><p>Invoice Number = [INVOICENUMBER]</p><p>Check Date = [INVOICECHKDATE]</p><p></p><p>Please login at [EMPLLINK] to view this information. </p><p></p><p>If you have questions, please contact us at:</p><p>Phone : [PEOPHONE]</p><p>Email : [PEOMAIL]</p><p></p><p>Thank you,</p><p>[COMPANYNAME]</p>', 1, 0, 0, 1, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=1, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=1, [SYAssign]=0 WHERE [Type] = 8
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 9)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 9, 1, N'Timesheet Approval Required for  [CLIENTNAME] [CLIENTID]/[COMPANYNAME] on [TSDATE]', N'<p>Your approval is required for the following timesheet [TIMESHEETID]</p><p></p><p>Client:&nbsp; [CLIENTID]&nbsp;&nbsp; [CLIENTNAME]&nbsp; </p><p>Pay Period:&nbsp; [PPSTART]&nbsp;&nbsp; [PPEND]&nbsp;&nbsp; </p><p>Check Date:&nbsp; [CHECKDATE]&nbsp;</p><p></p><p>Comments&nbsp; [TSCOMMENTS]&nbsp;</p><p>&nbsp;</p>', 1, 0, 0, 1, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=1, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=1, [SYAssign]=0 WHERE [Type] = 9
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 10)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 10, 1, N'Time Change Request', N'<p>[CLIENTNAME] - The time punch change request for [FIRSTNAME] [LASTNAME] ([EMPLOYEEID]) on [TIMERQDATE] has been [TIMERQSTATUS].</p><p>Current Time In: [TIMERQTIMEIN] - Requested Time In: [TIMERQASKIN]</p><p>Current Time Out: [TIMERQTIMEOUT] - Requested Time Out: [TIMERQASKOUT]</p><p>Comment(s): [TIMERQCOMMENT]</p><p>Note(s): [TIMERQEENOTE]</p>', 0, 0, 1, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=1, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 10
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 11)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 11, 1, N'Time Change Request', N'<p>[CLIENTNAME] - [FIRSTNAME] [LASTNAME] ([EMPLOYEEID]) has submitted a new Time Punch Change Request on [TIMERQDATE]</p><p>Current Time In: [TIMERQTIMEIN] - Requested Time In: [TIMERQASKIN]</p><p>Current Time Out: [TIMERQTIMEOUT] - Requested Time Out: [TIMERQASKOUT]</p><p>Status: [TIMERQSTATUS]</p><p>Comment(s): [TIMERQCOMMENT]</p><p>Note(s): [TIMERQEENOTE]</p>', 1, 0, 0, 1, 0)
END 
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=1, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=1, [SYAssign]=0 WHERE [Type] = 11
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 12)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 12, 1, N'New Timesheet available  for  [PPSTART]  to    [PPEND]', N'<p>A new timesheet is available for you.</p><p></p><p>Pay period:&nbsp;&nbsp; [PPSTART]&nbsp;&nbsp;&nbsp; [PPEND]</p><p>&nbsp;Check Date:&nbsp; [CHECKDATE]&nbsp;</p><p>Timesheet ID:&nbsp; [TSCOMMENTS][TIMESHEETID] </p><p>Comments:&nbsp; [TSCOMMENTS]&nbsp;</p><p></p><p></p>', 0, 0, 1, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=1, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 12
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 16)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 16, 1, N'Business Expense Request', N'<p>[CLIENTNAME] - A business expense has been requested from [FIRSTNAME] [LASTNAME] ([EMPLOYEEID])</p>', 1, 0, 0, 1, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=1, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=1, [SYAssign]=0 WHERE [Type] = 16
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 17)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 17, 1, N'Account Setup', N'<p>Dear [FIRSTNAME],<br /><br />Your employee information has been added to our system. </p><p>Please create a new account at [EMPLLINK][/EMPLLINK] by selecting New User Sign On, and then selecting I am an existing employee. I have an employee ID number.. </p><p>If you have questions, please contact us at:</p><p>Phone : [PEOPHONE]</p><p>Email : [PEOMAIL]</p><p>Thank you,</p><p>[PEONAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 17
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 18)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 18, 1, N'Password Setup', N'<p>Welcome to DNET.&nbsp; Your Username is [USERID].&nbsp; Please visit&nbsp;[URL][/URL] to setup your password.</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 18
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 19)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 19, 1, N'Your Authentication Code', N'<p>Your verification code is&nbsp;[AUTHENTICATIONCODE].</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 19
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 20)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 20, 1, N'Text/SMS Authentication Disabled', N'<p>Hello&nbsp;[USERFULLNAME],</p><p></p><p>Your payroll provider has disabled two factor Text/SMS authentication.  With this change, your Text/SMS authentication has been disabled.&nbsp; Please review your two factor authentication settings at [URL][/URL].</p><p></p><p>Regards,</p><p>[COMPANYNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 20
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 21)
BEGIN
   INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 21, 1, N'Email Authentication Disabled', N'<p>Hello&nbsp;[USERFULLNAME],</p><p></p><p>Your payroll provider has disabled two factor authentication email messaging.  With this change, your email authentication has been disabled.&nbsp; Please review your two factor authentication settings at [URL][/URL].</p><p></p><p>Regards,</p><p>[COMPANYNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 21
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 22)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 22, 1, N'Text/SMS Authentication Enabled', N'<p>Congratulations&nbsp;[USERFULLNAME],</p><p></p><p>You have successfully enabled two factor Text/SMS authentication.&nbsp; Please review your two factor authentication settings at [URL][/URL] to make any additional changes.</p><p></p><p>Regards,</p><p>[COMPANYNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 22
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 23)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 23, 1, N'Email Authentication Enabled', N'<p>Congratulations&nbsp;[USERFULLNAME],</p><p></p><p>You have successfully enabled two factor email authentication.&nbsp; Please review your two factor authentication settings at [URL][/URL] to make any additional changes.</p><p></p><p>Regards,</p><p>[COMPANYNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
   UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 23
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 24)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 24, 1, N'Text/SMS Authentication Disabled', N'<p>Hello&nbsp;[USERFULLNAME],</p><p></p><p>You have successfully disabled two factor Text/SMS authentication.&nbsp; Please review your two factor authentication settings at [URL][/URL] to make any additional changes.</p><p></p><p>Regards,</p><p>[COMPANYNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 24
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 25)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 25, 1, N'Email Authentication Disabled', N'<p>Hello&nbsp;[USERFULLNAME],</p><p></p><p>You have successfully disabled two factor email authentication.&nbsp; Please review your two factor authentication settings at [URL][/URL] to make any additional changes.</p><p></p><p>Regards,</p><p>[COMPANYNAME]</p>', 0, 0, 0, 0, 0)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 25
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 26)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 26, 1, N'New DNet To Do item', N'<p>Hello [EMPLOYEENAME]</p><p>Your attention is required for the following To Do Item:</p><p>[TODOMSG]</p><p></p><p>Please login at&nbsp;[EMPLLINK][/EMPLLINK] to view this information</p><p></p><p>Thank you,</p><p>[PEONAME]</p>', 1, 1, 1, 1, 1)
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=1, [SystemFilter]=1, [EEAssign]=1, [CCAssign]=1, [SYAssign]=1 WHERE [Type] = 26
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 28)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 28, 1, N'New document has  assigned to you', N'New document has  assigned to you.
Please verify this document', 0, 0, 0, 0, 0)
END
ELSE  
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 28
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 29)
BEGIN
 	INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 29, 1, N'New document requires your signature.', N'New document requires your signature.
Please sign document', 0, 0, 0, 0, 0)
END
ELSE 
UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=0, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 29
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 31)
BEGIN
 	INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 31, 1, N'Timesheet PEO Approval Required for  [CLIENTNAME] [CLIENTID]/[COMPANYNAME] on [TSDATE]', N'Timesheet PEO Approval Required for  [CLIENTNAME] [CLIENTID]/[COMPANYNAME] on [TSDATE]', 0, 1, 0, 0, 1)
END
ELSE 
UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=1, [EEAssign]=0, [CCAssign]=0, [SYAssign]=1 WHERE [Type] = 31
GO
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 32)
BEGIN
 	INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 32, 1, N'Change to Your Direct Deposit Setup', N'<p>Dear [FIRSTNAME],</p><p></p><p>There has been a change to your Direct Deposit setup information.</p><p></p><p>Please login at [EMPLLINK] [/EMPLLINK] to view this information.</p><p></p><p>If you have questions, please contact us at:</p><p></p><p>Phone : [PEOPHONE]</p><p></p><p>Email : [PEOMAIL]</p><p></p><p>Thank you,</p><p></p><p>[PEONAME]</p>', 0, 0, 1, 0, 0)
END
ELSE 
UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=1, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 32
GO
IF NOT EXISTS(SELECT * FROM [dbo].[DarwiNetNotifications] WHERE [Type] = 40)
BEGIN
    INSERT [dbo].[DarwiNetNotifications] ([CompanyID], [Type], [Enabled], [EmailSubject], [EmailBody], [ClientFilter], [SystemFilter], [EEAssign], [CCAssign], [SYAssign]) VALUES (1, 40, 1, N'Deal Offer', N'<p>Dear [FIRSTNAME],</p><p>The [DEALTITLE] deal has been offered to you for an amount of [DEALAMOUNT].</p><p>[DEALDESCRIPTION]</p>', 0, 0, 0, 0, 0);
END
ELSE 
    UPDATE [dbo].[DarwiNetNotifications] SET [ClientFilter]=0, [SystemFilter]=0, [EEAssign]=1, [CCAssign]=0, [SYAssign]=0 WHERE [Type] = 40
GO
