--function to calculate Easter for a given year:
--most difficult to calculate!
--the first Sunday after the full moon that occurs 
--on or next after the vernal equinox (fixed at March 21) 
--and is therefore celebrated between March 22 and April 25 inclusive.
CREATE FUNCTION [dbo].[fcn_FindEasterSunday](@inYear INT)
  RETURNS DATETIME
AS
BEGIN
  DECLARE @dtNow DATETIME
  DECLARE @inCurDay INT
  DECLARE @inCurMonth INT
  DECLARE @inCurYear INT
  DECLARE @inCurCent INT
  DECLARE @inYear19 INT
  DECLARE @inYearTmp INT
  DECLARE @inTemp2 INT
  DECLARE @inTemp3 INT
  DECLARE @inTemp4 INT
  DECLARE @inEastDay INT
  DECLARE @inEastMonth INT
  DECLARE @dtEasterSunday DATETIME

  SET @dtNow = CONVERT(DATETIME,CAST(@inYear AS CHAR(4))+'-01-01')

  SET @inCurDay=DAY(@dtNow)
  SET @inCurMonth=MONTH(@dtNow)
  SET @inCurYear=YEAR(@dtNow)
  SET @inCurCent=FLOOR(@inCurYear/100)

  SET @inYear19=@inCurYear%19

  SET @inYearTmp=FLOOR((@inCurCent-17)/25)
  SET @inTemp2=(@inCurCent-FLOOR(@inCurCent/4)-FLOOR((@inCurCent-@inYearTmp)/3)+(19*@inYear19)+15)%30
  SET @inTemp2=@inTemp2-FLOOR(@inTemp2/28)*(1 - FLOOR(@inTemp2/28)*FLOOR(29/(@inTemp2+1))*FLOOR((21-@inYear19)/11))

  SET @inTemp3 = (@inCurYear+FLOOR(@inCurYear/4)+@inTemp2+2-@inCurCent+FLOOR(@inCurCent/4))%7
  SET @inTemp4 = @inTemp2-@inTemp3

  SET @inEastMonth = 3+FLOOR((@inTemp4+40)/44)
  SET @inEastDay = @inTemp4+28-31*FLOOR(@inEastMonth/4)
  SET @inEastMonth = @inEastMonth - 1

  SET @dtEasterSunday = CONVERT(DATETIME,CAST(@inCurYear AS VARCHAR(4))+'-'+RIGHT(CAST('00' AS VARCHAR(2))+CAST(@inEastMonth+1 AS VARCHAR(2)),2)+'-'+RIGHT(CAST('00' AS VARCHAR(2))+CAST(@inEastDay AS VARCHAR(2)),2)+' 00:00:00')
  RETURN @dtEasterSunday
END
--#################################################################################################

