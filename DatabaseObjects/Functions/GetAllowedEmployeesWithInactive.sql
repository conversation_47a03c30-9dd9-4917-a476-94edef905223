

-- =============================================
-- Author:		<PERSON>
-- Create date: 2017-04-21
-- Description:	
-- =============================================
CREATE FUNCTION [dbo].[GetAllowedEmployeesWithInactive]
(	
	@company INT,
	@client VARCHAR(15), 
	@userid NVARCHAR(20)
)
RETURNS @EmployeeIDs TABLE
(
	EmployeeID NVARCHAR(15)
)
AS
BEGIN

	DECLARE @systemLevel bit
	SELECT @systemLevel = SystemLevelEnabled FROM Users u WHERE u.UserID = @userid
	DECLARE @IgnoreSecurity bit = 0
	IF @systemLevel <> 1
	SELECT @IgnoreSecurity = NoSecurity FROM UserRoleClientEmployeeAssignment WHERE UserID = @userid AND CompanyID = @company AND ClientID = @client AND EmployeeID = ''
	DECLARE @useDepartmentSecurity bit, @useDivisionSecurity bit
	SELECT @useDepartmentSecurity = UseDepartmentSecurity, @useDivisionSecurity = UseDivisionSecurity FROM DarwinetSetup WHERE CompanyID = @company AND ClientID = @client

	DECLARE @useSupervisorSecurity bit
	SELECT @useSupervisorSecurity = UseSupervisorSecurity FROM DarwinetSetup WHERE CompanyID = @company AND ClientID = @client

	IF @systemLevel = 1 OR (@client IS NULL) OR @useSupervisorSecurity = 0 OR (@IgnoreSecurity = 1)--DG TFS 6795 09/01/2020  
		BEGIN
			INSERT INTO @EmployeeIDs
			SELECT e.EmployeeID
			FROM Employees e
			WHERE e.CompanyID = @company AND (@client IS NULL OR e.ClientID = @client)
		END
	ELSE
		BEGIN
			INSERT INTO @EmployeeIDs
			SELECT e.EmployeeID
			FROM Employees e
				INNER JOIN UserSupervisorSecurity s ON s.CompanyID = @company AND s.ClientID = @client AND s.EmployeeID = e.EmployeeID
			WHERE e.CompanyID = @company AND e.ClientID = @client AND s.UserID = @userid
		END

	RETURN
END
