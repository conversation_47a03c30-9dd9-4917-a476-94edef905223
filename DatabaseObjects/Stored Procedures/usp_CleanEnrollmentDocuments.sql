-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[usp_CleanEnrollmentDocuments]
	-- Add the parameters for the stored procedure here
	@companyid int,
	@employeeid nvarchar(15),
	@year int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DELETE FROM EmployeeEnrollmentPlanDocuments WHERE CompanyID = @companyid AND EmployeeID = @employeeid AND [Year] = @year AND PlanName NOT IN (SELECT PlanName FROM EmployeeEnrollmentEligiblePlans WHERE CompanyID = @companyid AND EmployeeID = @employeeid AND [YEAR] = @year AND [Selected] = 1)
END
