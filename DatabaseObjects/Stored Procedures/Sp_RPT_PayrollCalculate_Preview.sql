-- =============================================
-- Author:		<Dhara Gohel>
-- Create date: <02/01/2024>
-- Description:	<returns the reslt for the Sp_RPT_PayrollCalculate_Preview  report>
-- =============================================

CREATE PROCEDURE [dbo].[Sp_RPT_PayrollCalculate_Preview]
	@company INT=0,
	@client VARCHAR(15) = NULL,
	@userid NVARCHAR(20)=NULL,
	@invoice int=0

AS

BEGIN

CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[CheckNumber] [varchar](50) NULL,
	[CheckDate] [date] NULL,
	--Earnings
	--[PayCode] [nvarchar](50) NULL,
	[Dept] [nvarchar](50) NULL,
	[DivisionID] [nvarchar](50) NULL,
	[Position] [nvarchar](50) NULL,
	[Hours]  [decimal](19,5) NULL,
	[PayRate]  [decimal](19,5) NULL,
	[EarningAmount] [decimal](19,5) NULL,
    [PayType] [tinyint] NULL,
	--Tax
	[Description] [nvarchar](30) NULL,
	[TaxAmount] [decimal](19,5) NULL,
	--Deduction
	[DeductAmount] [decimal](19,5) NULL,
	--Benefit
	[BenefitAmount] [decimal](19,5) NULL,
	[JobCostingName] [nvarchar] (30) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)

CREATE TABLE #EmployeePayExtras(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[CheckNumber] [varchar](50) NULL,
	[GrossPay] [decimal](19,5) NULL,
	[NetPay] [decimal](19,5) NULL
)

-- UserSecurity start -- 

declare @ProfileID nvarchar(max)= ''
select @ProfileID = (select pwm.ProfileID  from InvoicePayrolls b
inner join Invoices a  on  a.CompanyID =b.CompanyID and a.ClientID =b.ClientID 
and a.InvoiceNumber=b.InvoiceNumber
inner join  PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID 
and pwm.AuditControlCode = b.AuditControlCode and pwm.PayrollNumber = b.PayrollNumber
where a.CompanyID=@company and a.ClientID=@client and a.DarwinInvoiceNumber=@invoice)


CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)
INSERT INTO #Departments(DivisionID, Department) VALUES ('','')

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

  
 declare @Sortoption nvarchar(max)= ''
if  exists(select companyid,ProfileID from CalcCheckSortOption
              where CompanyID=@company and  ProfileID =@ProfileID) 
begin
select @Sortoption = (select DISTINCT  (CASE WHEN c.SortChoice = 1 THEN 'pd.EmployeeID' 
	WHEN c.SortChoice = 2 THEN 'LEFT(pd.EmployeeName, CHARINDEX('','',EmployeeName)-1)' 
	WHEN c.SortChoice = 3 THEN 'pd.Dept,pd.EmployeeID'
	WHEN c.SortChoice = 4 THEN 'pd.Position,pd.EmployeeID'
	WHEN c.SortChoice = 5 THEN 'pd.Dept,LEFT(pd.EmployeeName, CHARINDEX('','',EmployeeName)-1)'
	WHEN c.SortChoice = 6 THEN 'pd.Position,LEFT(pd.EmployeeName, CHARINDEX('','',EmployeeName)-1)'
	WHEN c.SortChoice = 7 THEN 'pd.JobCostingName'
	ELSE '' END) as dispStatus from  CalcCheckSortOption c where c.CompanyID=@company and c.ProfileID =@ProfileID)
end
else
begin
	select @Sortoption = 'pd.EmployeeID'
end


-- * * * * *      S E C T I O N  1 :  Earnings     * * * * * * * 
-- Earnings by Payroll Type
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept,DivisionID, Position, [Hours], PayRate, EarningAmount, [Description], Section, Sort, JobCostingName, PayType)
select  pwpc.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), e.SSN EmployeeSSN, pwh.CheckNumber, pwh.CheckDate, pwpc.Department, b.DivisionID, pwpc.JobTitle, SUM(IsNUll(pwpc.UnitsToPay,0)) UnitsToPay, 
		pwpc.PayRateAmount, sum(isnull(pwpc.TotalPay,0)) TotalPay, pwpc.PayRecord, Section=1, Sort=1, pwpc.JobCostingName, pwpc.PayType
from InvoicePayrolls b
inner join Invoices a  on  a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber=b.InvoiceNumber
inner join  PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode 
inner join (select CompanyID, CheckDate, CheckNumber, ProfileID, EmployeeID, PayrollNumber, IsRemoved
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved) pwh on pwh.CompanyID = b.CompanyID and pwh.ProfileID = pwm.ProfileID  and pwh.PayrollNumber =b.PayrollNumber
inner join   Employees e on e.EmployeeID = pwh.EmployeeID and e.CompanyID = b.CompanyID 
inner join (select CompanyID, EmployeeID, PayrollNumber, Department, JobTitle, ProfileID, JobCostingName, PayRateAmount,
					sum(isnull(TotalPay,0)) TotalPay,PayRecord, sum(isnull(UnitsToPay,0)) UnitsToPay,PayType
			FROM PayrollWorkPayCodes pwpc
			GROUP BY CompanyID, EmployeeID, PayrollNumber, PayRecord, Department, JobTitle, ProfileID, JobCostingName, PayType, PayRecord, PayRateAmount) pwpc
	on pwpc.CompanyID=b.CompanyID  and pwpc.ProfileID = pwm.ProfileID and pwpc.EmployeeID = pwh.EmployeeID and pwpc.PayrollNumber = pwh.PayrollNumber
where    a.DarwinInvoiceNumber = @invoice  and b.CompanyID = @company and b.ClientID = @client and pwh.IsRemoved= 0
group by pwpc.EmployeeID, (e.LastName + ', ' + e.FirstName), e.SSN, pwh.CheckNumber, 
pwpc.PayRecord, pwpc.Department, pwpc.JobTitle, pwh.CheckDate, pwpc.PayrollNumber, b.DivisionID, pwpc.JobCostingName, pwpc.PayType, pwpc.PayRecord, pwpc.PayRateAmount

-- * * * * *      S E C T I O N  2 :  Taxes     * * * * * * * 

-- Fedaral Tax
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, JobCostingName, Section, Sort)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), e.SSN EmployeeSSN, h.CheckNumber, h.CheckDate, 'Federal'  [Description], 
	sum(h.FederalWithholdingPayRun + h.FederalTaxOnTips) as TaxAmount, b.DivisionID, e.Department, e.Position, '', Section= 2, Sort = 1
from InvoicePayrolls b  
inner join Invoices a  on  a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join  PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode 
inner join (select CompanyID, CheckDate, CheckNumber, PayrollNumber, ProfileID, EmployeeID, isnull(sum(FederalWithholdingPayRun),0) FederalWithholdingPayRun, isnull(sum(FederalTaxOnTips),0) FederalTaxOnTips, IsRemoved
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved) h on h.CompanyID = b.CompanyID and h.ProfileID = pwm.ProfileID and h.PayrollNumber = b.PayrollNumber
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = b.CompanyID
where   h.CompanyID = @company and e.ClientID = @client and a.DarwinInvoiceNumber= @invoice AND h.IsRemoved = 0
group by h.EmployeeID,  (e.LastName + ', ' + e.FirstName),   e.SSN, h.CheckNumber, h.CheckDate, b.DivisionID, e.Department, e.Position

-- State Tax values.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort, JobCostingName)
select  pwpc.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), e.SSN EmployeeSSN, pwh.CheckNumber, pwh.CheckDate, pwpc.StateCode [Description], sum(pwpc.TotalStateTax) TaxAmount, 
		b.DivisionID, pwpc.Department, e.Position, Section = 2, Sort = 2, ''
from InvoicePayrolls b  
inner join Invoices a  on  a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join  PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode
inner join (select CompanyID, CheckDate, CheckNumber, ProfileID, EmployeeID, PayrollNumber, IsRemoved
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved )pwh on pwh.CompanyID = b.CompanyID and pwh.ProfileID = pwm.ProfileID  and pwh.PayrollNumber =b.PayrollNumber
inner join (select CompanyID, EmployeeID, PayrollNumber, StateCode, ProfileID, isnull(sum(TotalStateTax + StateTaxOnTips),0) TotalStateTax, Department, JobTitle
			FROM PayrollWorkStateTaxes
			where IsNull(PayrollNumber,'') <>''
			GROUP BY CompanyID, EmployeeID, PayrollNumber, StateCode, ProfileID, Department, JobTitle) pwpc
	on pwpc.CompanyID = b.CompanyID and pwpc.ProfileID = pwm.ProfileID and pwpc.PayrollNumber = pwh.PayrollNumber and pwpc.EmployeeID = pwh.EmployeeID
inner join   Employees e on e.EmployeeID = pwpc.EmployeeID and e.CompanyID = b.CompanyID
where a.DarwinInvoiceNumber = @invoice and b.CompanyID = @company and b.ClientID = @client and pwh.IsRemoved = 0 
group by pwpc.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, pwh.CheckNumber, pwh.CheckDate, pwpc.StateCode, pwpc.PayrollNumber,
b.DivisionID, pwpc.Department, e.Position

--Local
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort, JobCostingName)
select  pwpc.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwh.CheckNumber, pwh.CheckDate,
		pwpc.LocalTax, [Local] = sum(pwpc.TotalLocalTax), b.DivisionID, e.Department, e.Position, Section= 2, Sort = 3, ''
from InvoicePayrolls b  
inner join Invoices a  on  a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode 
inner join (select CompanyID, CheckDate, CheckNumber, ProfileID, EmployeeID, PayrollNumber, IsRemoved
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved) pwh on pwh.CompanyID = b.CompanyID and pwh.ProfileID = pwm.ProfileID and pwh.PayrollNumber = b.PayrollNumber
inner join (select CompanyID, EmployeeID, PayrollNumber, LocalTax, ProfileID, sum(isnull(TotalLocalTax + LocalTaxOnTips,0)) TotalLocalTax, Department, JobTitle
			FROM PayrollWorkLocalTaxes
			GROUP BY CompanyID, EmployeeID, PayrollNumber, LocalTax, ProfileID, Department, JobTitle) pwpc
	on pwpc.CompanyID = b.CompanyID and pwpc.ProfileID = pwm.ProfileID and pwpc.PayrollNumber = pwh.PayrollNumber and pwpc.EmployeeID = pwh.EmployeeID
inner join Employees e on e.EmployeeID = pwpc.EmployeeID and e.CompanyID = b.CompanyID
where a.DarwinInvoiceNumber = @invoice and b.CompanyID = @company and b.ClientID = @client and pwh.IsRemoved = 0
group by pwpc.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, pwh.CheckNumber, pwh.CheckDate, pwpc.LocalTax, pwpc.PayrollNumber, b.DivisionID, e.Department, e.Position 

-- FICA SS Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort, JobCostingName)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, h.CheckNumber, h.CheckDate,'FICA SS' [Description], 
	   sum(h.FICASocialSecurityWithholdingPayRun + h.FICASSTaxOnTips), b.DivisionID, e.Department, e.Position, Section = 2,Sort = 4, ''
from InvoicePayrolls b  
inner join Invoices a on a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode 
inner join (select CompanyID, CheckDate, CheckNumber, ProfileID, EmployeeID, PayrollNumber, IsRemoved, isnull(sum(FICASocialSecurityWithholdingPayRun),0) FICASocialSecurityWithholdingPayRun,
				isnull(sum(FICASSTaxOnTips),0) FICASSTaxOnTips
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved) h on h.CompanyID = b.CompanyID and h.ProfileID = pwm.ProfileID  and h.PayrollNumber = b.PayrollNumber
inner join   Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = b.CompanyID
where  h.CompanyID = @company and h.IsRemoved = 0 and e.ClientID = @client and a.DarwinInvoiceNumber = @invoice
GROUP BY h.EmployeeID,  (e.LastName + ', ' + e.FirstName),   e.SSN, h.CheckNumber, h.CheckDate, b.DivisionID, e.Department, e.Position


-- FICA Med Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort, JobCostingName)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, h.CheckNumber, h.CheckDate,'FICA Med' [Description], 
		SUM(ISNULL(h.FICAMedicareWithholdingPayRun,0) + IsNULL(h.FICAMedTaxOnTips,0)) as TaxAmount, b.DivisionID, e.Department, e.Position, Section = 2, Sort = 5, ''
from InvoicePayrolls b  
inner join Invoices a on a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode 
inner join (select CompanyID, CheckDate, CheckNumber, ProfileID, EmployeeID, PayrollNumber, IsRemoved, isnull(sum(FICAMedicareWithholdingPayRun),0) FICAMedicareWithholdingPayRun,
				isnull(sum(FICAMedTaxOnTips),0) FICAMedTaxOnTips
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved) h on h.CompanyID = b.CompanyID and h.ProfileID = pwm.ProfileID and h.PayrollNumber = b.PayrollNumber
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = b.CompanyID
where h.CompanyID = @company and h.IsRemoved = 0 and e.ClientID = @client and a.DarwinInvoiceNumber = @invoice and h.IsRemoved = 0
group by h.EmployeeID, (e.LastName + ', ' + e.FirstName), e.SSN, h.CheckNumber, h.CheckDate, b.DivisionID, e.Department, e.Position

-- * * * * *      S E C T I O N  3 :  Deductions     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], DeductAmount, DivisionID, Dept, Position, Section, Sort, JobCostingName)
select pwd.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, h.CheckNumber, h.CheckDate, pwd.Deduction,
		isnull(sum(pwd.TotalDeduction),0) as TaxAmount, b.DivisionID, e.Department, e.Position, Section = 3, Sort = 1, ''
from InvoicePayrolls b  
inner join Invoices a on a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode 
inner join (select CompanyID, CheckDate, CheckNumber, ProfileID, EmployeeID, PayrollNumber, IsRemoved
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved) h on h.CompanyID = b.CompanyID and h.ProfileID = pwm.ProfileID and h.PayrollNumber = b.PayrollNumber
inner join (select CompanyID, Deduction, ProfileID, EmployeeID, PayrollNumber, isnull(sum(TotalDeduction),0) TotalDeduction, Department, JobTitle
			from PayrollWorkDeductions 
			group by CompanyID, ProfileID, PayrollNumber, EmployeeID, Deduction, Department, JobTitle) pwd 
	on pwd.CompanyID = b.CompanyID and pwd.ProfileID = pwm.ProfileID  and pwd.PayrollNumber = b.PayrollNumber and pwd.EmployeeID = h.EmployeeID
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = b.CompanyID
where  h.CompanyID = @company and e.ClientID = @client and a.DarwinInvoiceNumber = @invoice and h.IsRemoved = 0
GROUP BY  pwd.EmployeeID, (e.LastName + ', ' + e.FirstName), e.SSN, h.CheckNumber, h.CheckDate, pwd.Deduction, b.DivisionID, e.Department, e.Position

-- * * * * *      S E C T I O N  4 :  Benefits     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], BenefitAmount, DivisionID, Dept, Position, Section, Sort, JobCostingName)
select pwb.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, h.CheckNumber, h.CheckDate, pwb.Benefit, sum(isnull(pwb.TotalBenefit,0)) as TaxAmount,
		b.DivisionID, e.Department, e.Position, Section = 4, Sort = 1, ''
from InvoicePayrolls b  
inner join Invoices a on a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID and pwm.AuditControlCode = b.AuditControlCode 
inner join (select CompanyID, CheckDate, CheckNumber, ProfileID, EmployeeID, PayrollNumber, IsRemoved
			from PayrollWorkHeaders 
			group by CheckDate, CheckNumber, CompanyID, ProfileID, PayrollNumber, EmployeeID, IsRemoved)h on h.CompanyID = b.CompanyID and h.ProfileID = pwm.ProfileID and h.PayrollNumber = b.PayrollNumber
inner join (select CompanyID,Benefit,ProfileID,EmployeeID,PayrollNumber,isnull(sum(TotalBenefit),0) TotalBenefit,Department,JobTitle
			from PayrollWorkBenefits 
			group by CompanyID, ProfileID, PayrollNumber, EmployeeID, Benefit, Department, JobTitle) pwb on pwb.CompanyID = b.CompanyID 
										and pwb.ProfileID = pwm.ProfileID and pwb.PayrollNumber = b.PayrollNumber and pwb.EmployeeID = h.EmployeeID
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = b.CompanyID
where h.CompanyID = @company and e.ClientID = @client and a.DarwinInvoiceNumber = @invoice AND h.IsRemoved = 0
GROUP BY pwb.EmployeeID, (e.LastName + ', ' + e.FirstName), e.SSN, h.CheckNumber, h.CheckDate, pwb.Benefit, b.DivisionID, e.Department, e.Position

insert into #EmployeePayExtras(EmployeeID, CheckNumber, NetPay, GrossPay)
select h.EmployeeID, h.CheckNumber, h.NetWagesPayRun, (h.GrossWagesPayRun + h.ReportedTips + h.ChargedTips ) as GrossPay
from InvoicePayrolls b  
inner join Invoices a on a.CompanyID = b.CompanyID and a.ClientID = b.ClientID and a.InvoiceNumber = b.InvoiceNumber
inner join PayrollWorkHeaders h on h.CompanyID = b.CompanyID and h.PayrollNumber = b.PayrollNumber 
left outer join PayrollWorkMasters m on m.CompanyID = h.CompanyID and m.PayrollNumber = h.PayrollNumber and m.AuditControlCode = b.AuditControlCode
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID
where  a.ClientID = @client and a.CompanyID = @company and a.DarwinInvoiceNumber = @invoice and h.IsRemoved=0


-- * * * * *     O U T P U T     * * * * * * * 
declare @sql nvarchar(max)=''
set @sql = 'select pd.Id, pd.EmployeeID, pd.EmployeeName, ''***-**-'' + SUBSTRING(pd.EmployeeSSN, 6, 4) [EmployeeSSN],
				rtrim(ltrim(REPLACE(pd.CheckNumber, NCHAR(0x00A0), ''''))) AS CheckNumber, pd.CheckDate, pd.Dept, pd.Position,
				pd.Hours, pd.PayRate, pd.EarningAmount, [Description], TaxAmount, DeductAmount, BenefitAmount, ex.GrossPay, ex.NetPay,
				pd.Section, pd.Sort, pd.PayType
			from #EmployeePayDetail pd
			INNER JOIN #EmployeeIDs eid ON eid.EmployeeID = pd.EmployeeID 
			INNER JOIN #Divisions d ON pd.DivisionID = d.DivisionID
			left outer join #EmployeePayExtras ex on ex.EmployeeID = pd.EmployeeID and ex.CheckNumber = pd.CheckNumber
			ORDER BY ' +  @Sortoption

exec (@sql)


DROP TABLE #EmployeePayDetail
DROP TABLE #EmployeePayExtras
DROP TABLE #Departments
DROP TABLE #EmployeeIDs
DROP TABLE #Divisions

END
