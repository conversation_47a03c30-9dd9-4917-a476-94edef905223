
/****** Object:  StoredProcedure [dbo].[sp_RPT_DetailedPayRecap]    Script Date: 1/24/2025 12:10:07 PM ******/

/*
*************************************************************************
**** Purpose: Generates data for the SSRS report Detailed Pay Recap.
**** 12/18/2023 | Dhara gohel | Initial design.
****
*************************************************************************
*/

CREATE PROCEDURE [dbo].[sp_RPT_DetailedPayRecap]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@invoice int = 0,
	@userid NVARCHAR(20))
AS	

SET FMTONLY OFF;

CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[Description] [nvarchar](30) NULL,
	[Units] [decimal](19,5) NULL,
	[Rate] [decimal](19,5) NULL,
	[EarnAmount] [decimal](19,5) NULL,
	--EMPLOYER TAXES AND FEES
	[ERFICASS] [decimal](19,5) NULL,
	[ERFICASSTax] [decimal](19,5) NULL,
	[ERFICAMED] [decimal](19,5) NULL,
	[ERFICAMEDTax] [decimal](19,5) NULL,
	[ERFUTA] [decimal](19,5) NULL,
	[ERFUTATax] [decimal](19,5) NULL,
	[TaxableWages] [decimal](19,5) NULL,
	--SUMMARY
	[GROSSWAGES] [decimal](19,5) NULL,
	[FEDERALTAX] [decimal](19,5) NULL,
	[FEDERALTAXABLEWAGES] [decimal](19,5) NULL,
	[FICASS] [decimal](19,5) NULL,
	[FICASSTAXABLEWAGES] [decimal](19,5) NULL,
	[FICAMED] [decimal](19,5) NULL,
	[FICAMEDTAXABLEWAGES] [decimal](19,5) NULL,
	[STATE] [decimal](19,5) NULL,
	[STATETAXABLEWAGES] [decimal](19,5) NULL,
	[LOCAL] [decimal](19,5) NULL,
	[LOCALTAXABLEWAGES] [decimal](19,5) NULL,
	[DEDUCTION] [decimal](19,5) NULL,
	[NETWAGES] [decimal](19,5) NULL,
	[REGHOURS] [decimal](19,5) NULL,
	[OTHOURS] [decimal](19,5) NULL,
	[OTHERHOURS] [decimal](19,5) NULL,
	[NONGROSS] [decimal](19,5) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

CREATE TABLE #Invoices([Id] [int] IDENTITY(1,1) NOT NULL, [Invoice] [int] NOT NULL)
declare @iEnd int = NULL

-- * * * * *      S E C T I O N  1 :  Earnings     * * * * * * * 
-- Earnings by Payroll Type
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [Description], EarnAmount, Units, Rate, Section, Sort)
select  e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwh.PayRecord [Description],
		isnull(pwh.TotalPay,0) EarnAmount, pwh.UnitsToPay , isnull(pwh.PayRateAmount,0) Rate, Section = 1, Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber and b.DivisionID = a.DivisionID
and a.CompanyID=b.CompanyID and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
isnull(sum(TotalPay),0) TotalPay ,PayRecord,
isnull(sum(UnitsToPay),0) UnitsToPay,isnull(sum(PayRateAmount),0) PayRateAmount 
FROM PayrollWorkPayCodes 
GROUP BY CompanyID, EmployeeID, PayRecord,PayrollNumber) pwh
on pwh.CompanyID = a.CompanyID and pwh.EmployeeID=a.EmployeeID and pwh.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID and e.CompanyID = a.CompanyID and e.ClientID=a.ClientID
WHERE a.CompanyID = @company AND a.clientid = @client and (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice)

-- * * * * *      S E C T I O N  2 :  Local Taxes     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [Description], EarnAmount,Section, Sort)
select  e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwlt.LocalTax [Description],
		isnull(pwlt.TotalLocalTax,0) [EarnAmount], Section = 2, Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber and b.DivisionID = a.DivisionID
and a.CompanyID=b.CompanyID and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
isnull(sum(TotalLocalTax + LocalTaxOnTips),0) TotalLocalTax ,LocalTax
FROM PayrollWorkLocalTaxes 
GROUP BY CompanyID, EmployeeID, LocalTax,PayrollNumber) pwlt
on pwlt.CompanyID = a.CompanyID and pwlt.EmployeeID=a.EmployeeID and pwlt.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID and e.CompanyID = a.CompanyID and e.ClientID=a.ClientID
WHERE a.CompanyID = @company AND a.clientid = @client 
 and  (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice)

-- * * * * *      S E C T I O N  3 :  State Taxes     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [Description], EarnAmount, Section, Sort)
select  e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName),  e.SSN, pwst.StateCode [Description],
		isnull(pwst.TotalStateTax,0) [EarnAmount] , Section = 3, Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber and b.DivisionID = a.DivisionID
and a.CompanyID=b.CompanyID and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
isnull(sum(TotalStateTax + StateTaxOnTips),0) TotalStateTax ,StateCode
FROM PayrollWorkStateTaxes 
GROUP BY CompanyID, EmployeeID, StateCode,PayrollNumber) pwst
on pwst.CompanyID = a.CompanyID and pwst.EmployeeID=a.EmployeeID and pwst.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID and e.CompanyID = a.CompanyID and e.ClientID=a.ClientID
WHERE a.CompanyID = @company AND a.clientid = @client 
  AND (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice)

-- * * * * *      S E C T I O N  4 :  Benefits     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [Description], EarnAmount, Section, Sort)
select  e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwb.Benefit [Description],
		isnull(pwb.TotalBenefit,0) [EarnAmount], Section = 4, Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber and b.DivisionID = a.DivisionID
and a.CompanyID=b.CompanyID and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
isnull(sum(TotalBenefit),0) TotalBenefit ,Benefit
FROM PayrollWorkBenefits 
GROUP BY CompanyID, EmployeeID, Benefit,PayrollNumber) pwb
on pwb.CompanyID = a.CompanyID and pwb.EmployeeID=a.EmployeeID and pwb.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID and e.CompanyID = a.CompanyID and e.ClientID=a.ClientID
WHERE a.CompanyID = @company AND a.clientid = @client 
  AND (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice) 

-- * * * * *      S E C T I O N  5 :  Deductions     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN,[Description], EarnAmount, Section, Sort)
select  e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwb.Deduction [Description],
		isnull(pwb.TotalDeduction,0) [EarnAmount], Section = 5, Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber and b.DivisionID = a.DivisionID
and a.CompanyID=b.CompanyID and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
isnull(sum(TotalDeduction),0) TotalDeduction ,Deduction
FROM PayrollWorkDeductions 
GROUP BY CompanyID, EmployeeID, Deduction,PayrollNumber) pwb
on pwb.CompanyID = a.CompanyID and pwb.EmployeeID=a.EmployeeID and pwb.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID and e.CompanyID = a.CompanyID and e.ClientID=a.ClientID
WHERE a.CompanyID = @company AND a.clientid = @client 
 AND (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice)


-- * * * * *     EMPLOYER TAXES AND FEES     * * * * * * * 

-- * * * * *      S E C T I O N  6,19,20 :  Taxable Wages     * * * * * * *
--ERFICASS
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [ERFICASS], ERFICASSTax,
[ERFICAMED],ERFICAMEDTax,[ERFUTA],ERFUTATax,Section,Sort)
select e.EmployeeID, 
	e.LastName + ', ' + e.FirstName [EmployeeName], 
	e.SSN, 
	isnull(pwb.EmployerFICASocialSecurityWithholding,0) as [ERFICASS],
	isnull(pwb.FICASocialSecurityWagesPayRun,0) as [ERFICASSTax], 
	isnull(pwb.EmployerFICAMedicareWithholding,0) as [ERFICAMED],
	isnull(pwb.FICAMedicareWagesPayRun,0) as [ERFICMEDTax],
	isnull(pwc.LiabilityAmount,0) as [ERFUTA],
	isnull(pwc.TaxableWages,0) as [ERFUTATax],
	Section = 6,  
	Sort = 1
from PayrollWorkHeadersDivisional a
	left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber 
		and b.DivisionID = a.DivisionID
		and a.CompanyID=b.CompanyID 
		and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
					isnull(sum(EmployerFICASocialSecurityWithholding),0) EmployerFICASocialSecurityWithholding ,
					isnull(sum(FICASocialSecurityWagesPayRun),0) FICASocialSecurityWagesPayRun,
					isnull(sum(EmployerFICAMedicareWithholding),0) EmployerFICAMedicareWithholding ,
					isnull(sum(FICAMedicareWagesPayRun),0) FICAMedicareWagesPayRun
				FROM PayrollWorkHeaders 
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwb on pwb.CompanyID = a.CompanyID 
		and pwb.EmployeeID=a.EmployeeID 
		and pwb.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
					isnull(sum(TotalPay),0) TotalPay ,
					isnull(sum(LiabilityAmount),0) LiabilityAmount,	
					isnull(sum(TaxableWages),0) TaxableWages
				FROM PayrollWorkFutaSutaWC 
				where PayrollRecordType=2
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwc on pwc.CompanyID = a.CompanyID 
		and pwc.EmployeeID=a.EmployeeID 
		and pwc.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID 
	and e.CompanyID = a.CompanyID 
	and e.ClientID=a.ClientID
WHERE a.CompanyID = @company 
	AND a.clientid = @client
	AND (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice) 

-- * * * * *      S E C T I O N  7 :  Employer SUTA     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [Description], EarnAmount, TaxableWages, Section, Sort)
select e.EmployeeID, 
	e.LastName + ', ' + e.FirstName EmployeeName, 
	e.SSN, 
	pwb.SutaState [Description],
	isnull(pwb.LiabilityAmount,0) as [EarnAmount],
	isnull(pwb.TaxableWages,0) [TaxableWages], 
	Section = 7,  
	Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber and b.DivisionID = a.DivisionID
and a.CompanyID=b.CompanyID and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
					isnull(sum(TotalPay),0) TotalPay ,
					isnull(sum(LiabilityAmount),0) LiabilityAmount ,
					isnull(sum(TaxableWages),0) TaxableWages,SutaState
				FROM PayrollWorkFutaSutaWC 
				where PayrollRecordType=1
				GROUP BY CompanyID, EmployeeID,PayrollNumber,SutaState) pwb
on pwb.CompanyID = a.CompanyID and pwb.EmployeeID=a.EmployeeID and pwb.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID and e.CompanyID = a.CompanyID and e.ClientID=a.ClientID
WHERE a.CompanyID = @company AND a.clientid = @client 
AND (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice) 


-- * * * * *      S E C T I O N  8 :  Employer WC     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [Description], EarnAmount, TaxableWages, Section, Sort)
select  e.EmployeeID, 
	e.LastName + ', ' + e.FirstName [EmployeeName], 
	e.SSN, pwb.PayrollCode [Description],
	isnull(pwb.LiabilityAmount,0) as [EarnAmount],
	isnull(pwb.TaxableWages,0) TaxableWages, 
	Section = 8,  
	Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber 
	and b.DivisionID = a.DivisionID
	and a.CompanyID=b.CompanyID and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber,
						isnull(sum(TotalPay),0) TotalPay ,
						isnull(sum(LiabilityAmount),0) LiabilityAmount ,			
						isnull(sum(TaxableWages),0) TaxableWages,PayrollCode
					FROM PayrollWorkFutaSutaWC 
					where PayrollRecordType=3
					GROUP BY CompanyID, EmployeeID,PayrollNumber,PayrollCode) pwb
on pwb.CompanyID = a.CompanyID and pwb.EmployeeID=a.EmployeeID and pwb.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID and e.CompanyID = a.CompanyID and e.ClientID=a.ClientID
WHERE a.CompanyID = @company AND a.clientid = @client 
AND (b.DarwinInvoiceNumber = @invoice  or b.MergedInvoiceNumber = @invoice)


-- * * * * *      S E C T I O N  9 :  Employer FEES     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, [Description], EarnAmount,Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName [EmployeeName], e.SSN, a.Code [Description],
		isnull(SUM(a.ChargeTotal),0) as [EarnAmount], Section = 9,  Sort = 1
from   InvoicePayrollDetails  a
	INNER JOIN Employees e on e.CompanyID = a.CompanyID and e.EmployeeID = a.EmployeeID
WHERE a.CompanyID = @company AND a.clientid = @client 
 AND (a.DarwinInvoiceNumber = @invoice or a.MergedInvoiceNumber = @invoice) 
and a.ComponentType='ADMIN FEE'
group by e.EmployeeID,a.PayrollNumber,a.CompanyID,a.ClientID,e.LastName + ', ' + e.FirstName,e.SSN,a.Code


-- * * * * *     S E C T I O N  10  : SUMMARY     * * * * * * * 
-- Taxable Wages   (FEDERALTAX,FICASS,FICAMED)
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN,FEDERALTAX,FICASS,FICAMED, [STATE],[LOCAL],FEDERALTAXABLEWAGES,FICASSTAXABLEWAGES,FICAMEDTAXABLEWAGES,STATETAXABLEWAGES,LOCALTAXABLEWAGES,
	GROSSWAGES,[DEDUCTION],[NETWAGES],
	[REGHOURS],[OTHOURS],[OTHERHOURS],[NONGROSS],
	Section, Sort)
select e.EmployeeID, 
	e.LastName + ', ' + e.FirstName [EmployeeName], 
	e.SSN,
	isnull(pwb.FEDERATAX,0) [FEDERALTAX] , 
	isnull(pwb.FICASS,0) [FICASS],
	isnull(pwb.FICAMED,0) [FICAMED],
	isnull(pws.TotalStateTax,0) [STATE],
	isnull(pwl.TotalLocalTax,0) [LOCAL],
	isnull(pwb.FEDERALTAXABLEWAGES,0) [FEDERALTAXABLEWAGES],
	isnull(pwb.FICASSTAXABLEWAGES,0) [FICASSTAXABLEWAGES],
	isnull(pwb.FICAMEDTAXABLEWAGES,0) [FICAMEDTAXABLEWAGES],
	isnull(pws.STATETAXABLEWAGES,0) [STATETAXABLEWAGES],
	isnull(pwl.LOCALTAXABLEWAGES,0) [LOCALTAXABLEWAGES],
	isnull(pwb.GrossWagesPayRun,0) [GROSSWAGES],
	isnull(pwd.TotalDeduction,0) [DEDUCTION],
	isnull(pwb1.NetWagesPayRun,0) [NETWAGES],
	isnull(up.UnitsToPay,0) [REGHOURS],
	isnull(pwpc.UnitsToPay,0) [OTHOURS],
	isnull(pwpc2.UnitsToPay,0) [OTHERHOURS],
	isnull(pwpc3.TotalPay,0) [NONGROSS],
	Section = 10,  
	Sort = 1
from PayrollWorkHeadersDivisional a
left outer join InvoicePayrolls b on b.PayrollNumber = a.PayrollNumber 
	and b.DivisionID = a.DivisionID
	and a.CompanyID=b.CompanyID 
	and a.ClientID=b.ClientID
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(GrossWagesPayRun),0) GrossWagesPayRun,
				isnull(sum(FederalWithholdingPayRun),0) FEDERATAX,
				isnull(SUM(FICASocialSecurityWithholdingPayRun),0) FICASS,
				isnull(sum(FICAMedicareWithholdingPayRun),0) FICAMED,
				isnull(sum(FederalWagesPayRun + FederalTipsPayRun),0) FEDERALTAXABLEWAGES,
				isnull(sum(FICASocialSecurityWagesPayRun + FICASSTipsPayRun),0) FICASSTAXABLEWAGES,
				isnull(sum(FICAMedicareWagesPayRun + FICAMedTipsPayRun),0) FICAMEDTAXABLEWAGES
				FROM PayrollWorkHeaders 
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwb on pwb.CompanyID = a.CompanyID 
					and pwb.EmployeeID=a.EmployeeID 
					and pwb.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(TotalStateTax + StateTaxOnTips),0) TotalStateTax,
				isnull(sum(TaxableWages + TaxableTips),0) STATETAXABLEWAGES
				FROM PayrollWorkStateTaxes 
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pws on pws.CompanyID = a.CompanyID 
					and pws.EmployeeID=a.EmployeeID 
					and pws.PayrollNumber=a.PayrollNumber
left outer join ( select max(data.CompanyID) CompanyID ,max(data.EmployeeID) EmployeeID,max(data.PayrollNumber) PayrollNumber,isnull(sum(data.TotalLocalTax),0) TotalLocalTax,isnull(max(data.LOCALTAXABLEWAGES),0)LOCALTAXABLEWAGES
			from  (select  CompanyID, EmployeeID, PayrollNumber ,isnull(sum(TaxableWages + TaxableTips),0) LOCALTAXABLEWAGES,	isnull(sum(TotalLocalTax + LocalTaxOnTips),0) TotalLocalTax
		    FROM PayrollWorkLocalTaxes
			GROUP BY CompanyID, EmployeeID,PayrollNumber,TRXNumber) data
			group by data.EmployeeID) pwl on pwl.CompanyID = a.CompanyID 
					and pwl.EmployeeID=a.EmployeeID 
					and pwl.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(TotalDeduction),0) TotalDeduction
				FROM PayrollWorkDeductions 
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwd on pwd.CompanyID = a.CompanyID 
					and pwd.EmployeeID=a.EmployeeID 
					and pwd.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(NetWagesPayRun),0) NetWagesPayRun
				FROM PayrollWorkHeaders 
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwb1 on pwb1.CompanyID = a.CompanyID 
					and pwb1.EmployeeID=a.EmployeeID 
					and pwb1.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(UnitsToPay),0) UnitsToPay
				FROM PayrollWorkPayCodes  where
				PayType=1 OR PayType=2
				GROUP BY CompanyID, EmployeeID,PayrollNumber) up on up.CompanyID = a.CompanyID 
					and up.EmployeeID=a.EmployeeID 
					and up.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(UnitsToPay),0) UnitsToPay
				FROM PayrollWorkPayCodes  where
				PayType=6 
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwpc on pwpc.CompanyID = a.CompanyID 
					and pwpc.EmployeeID=a.EmployeeID 
					and pwpc.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(UnitsToPay),0) UnitsToPay
				FROM PayrollWorkPayCodes  where
				(PayType<>1 and PayType<>2  and PayType<>6 )
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwpc2 on pwpc2.CompanyID = a.CompanyID 
					and pwpc2.EmployeeID=a.EmployeeID 
					and pwpc2.PayrollNumber=a.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, 
				isnull(sum(TotalPay),0) TotalPay
				FROM PayrollWorkPayCodes  where
				 (PayType=5)
				GROUP BY CompanyID, EmployeeID,PayrollNumber) pwpc3 on pwpc3.CompanyID = a.CompanyID 
					and pwpc3.EmployeeID=a.EmployeeID 
					and pwpc3.PayrollNumber=a.PayrollNumber
left outer join Employees e on e.EmployeeID = a.EmployeeID 
	and e.CompanyID = a.CompanyID 
	and e.ClientID=a.ClientID
WHERE a.CompanyID = @company 
	AND a.clientid = @client 
	and  (b.DarwinInvoiceNumber = @invoice or b.MergedInvoiceNumber = @invoice) 


-- * * * * *     O U T P U T     * * * * * * * 
select pd.EmployeeID, pd.EmployeeName, '***-**-' + SUBSTRING(pd.EmployeeSSN, 6, 4) [EmployeeSSN], pd.[Description],
	   SUM(pd.Units) Units, pd.Rate, SUM(pd.EarnAmount) EarnAmount,SUM(ERFICASS) ERFICASS,SUM(ERFICASSTax) ERFICASSTax,
	   SUM(ERFICAMED) ERFICAMED,SUM(ERFICAMEDTax) ERFICAMEDTax, SUM(ERFUTA) ERFUTA,SUM(ERFUTATax) ERFUTATax,
	   SUM(pd.TaxableWages) TaxableWages,
	    SUM(GROSSWAGES) GROSSWAGES,SUM(FEDERALTAX) FEDERALTAX,SUM(FICASS) FICASS,SUM(FICAMED) FICAMED,SUM([STATE]) [STATE],SUM([LOCAL]) [LOCAL],
		SUM(FEDERALTAXABLEWAGES) FEDERALTAXABLEWAGES,SUM(FICASSTAXABLEWAGES) FICASSTAXABLEWAGES,SUM(LOCALTAXABLEWAGES) LOCALTAXABLEWAGES,SUM(STATETAXABLEWAGES) STATETAXABLEWAGES,
		SUM(FICAMEDTAXABLEWAGES) FICAMEDTAXABLEWAGES,
		SUM([DEDUCTION]) [DEDUCTION],SUM([NETWAGES]) [NETWAGES],SUM([REGHOURS]) [REGHOURS],SUM([OTHOURS]) [OTHOURS],
		SUM([OTHERHOURS])[OTHERHOURS], SUM([NONGROSS]) [NONGROSS],
	   pd.Section,pd.Sort,@company as Companyid
from #EmployeePayDetail pd

GROUP BY pd.EmployeeID, EmployeeName, EmployeeSSN, pd.[Description], Rate, Section, Sort
order by pd.EmployeeID, Section, Sort, [Description]


OPTION (RECOMPILE)

DROP TABLE #EmployeePayDetail
DROP TABLE #Departments
DROP TABLE #Divisions
DROP TABLE #EmployeeIDs
DROP TABLE #Invoices

SET ANSI_NULLS ON
