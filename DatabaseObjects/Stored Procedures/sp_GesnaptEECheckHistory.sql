/*
********************************************************************
**** Created by <PERSON><PERSON> gohel.
**** Date: 10/05/2021.
**** Purpose: Returns all ee check data.
****
********************************************************************
*/


CREATE PROCEDURE [dbo].[sp_GesnaptEECheckHistory]  
(
	@company INT,
	@eeid VARCHAR(20) = null

)
AS

select	e.CheckDate,e.EmployeeID,e.GrossWagesPayRun,e.TotalDeductions,e.TotalBenefits,
e.NetWagesPayRun,e.TotalTaxes,e.Year,e.AuditControlCode,e.<PERSON><PERSON><PERSON>,e.PaymentAdjustmentNumber,e.<PERSON><PERSON>loyee<PERSON><PERSON>,
e.SSN,e.CheckDate as PayPeriodStart,e.CheckDate as PayPeriodEnd, TotalDeductions as NonGross,TotalDeductions as TotalState,
TotalDeductions as TotalWC,
TotalDeductions as FicaSS,TotalDeductions as FicaM,
TotalDeductions as Futa,e.Voided
from EmployeeCheckHistory e
where e.CompanyID = @company and e.EmployeeID =@eeid 
	
