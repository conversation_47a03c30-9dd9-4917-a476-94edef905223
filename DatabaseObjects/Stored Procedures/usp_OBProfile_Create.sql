-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Creates on-boarding profile, based on default data from system database 
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBProfile_Create]
 
	-- Add the parameters for the stored procedure here
	@SystemDB as nvarchar(50),
	@CompanyID as int,
	@ProfileName as nvarchar(50),
	@EEFinilize as	bit = '0',
	@SignType as smallint = '0',
	@MaskSSN as bit = '0',
	@BankPresentation as smallint = '0',
	@SortOption as smallint = '0',
	@CodeSetup as smallint = '0',
	@ProfileID as int OUTPUT 
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	PRINT @ProfileName
	DECLARE @SQL_Query as nvarchar(1000)
	DECLARE @ErrNum as int = 0
    
    --EXEC dbo.usp_OBProfile_Create
    
	SELECT @SQL_Query = 'INSERT INTO OBProfiles (CompanyID, ProfileName, EEFinalize, Signature, MaskSSNInSign, BankPresentation, SortOption, CodeSetup, AllowCCAddCodes, DayToDueDate, NotifyBeforeDueDate, Completed) VALUES (''' + CAST(@CompanyID as nvarchar(3)) + ''', ''' + @ProfileName + ''', ''' + CAST(@EEFinilize as CHAR(1)) + ''',  ''' + CAST(@SignType as CHAR(1)) + ''',  ''' + CAST(@MaskSSN as CHAR(1)) + ''',  ''' + CAST(@BankPresentation as CHAR(1)) + ''',  ''' + CAST(@SortOption as CHAR(1)) + ''',  ''' + CAST(@CodeSetup as CHAR(1)) + ''', 0, 5, -2, 0)' 
    EXEC(@SQL_Query)
    IF @@ERROR = 0
	BEGIN
		SET @ProfileID = @@IDENTITY
		SELECT @SQL_Query = 'INSERT INTO OBProfileTasks(CompanyID, ProfileID, TaskID, TaskName, TaskType, TaskOrder, TaskAssignment, TaskRequired, TaskLocked, CCInstruction, EEInstruction, InstructionDoc, TipText, TipImage, FormCols, DocumentsUpdated, InstructionsUpdated, FieldsUpdated, TipsUpdated)
				SELECT ''' + CAST(@CompanyID as nvarchar(3)) + ''' as CompanyID, ''' + CAST(@ProfileID as NVARCHAR(10)) + ''' as ProfileID, TaskID, TaskName, TaskType, TaskOrder, TaskAssignment, TaskRequired, TaskLocked, COALESCE(CCInstruction,''''), COALESCE(EEInstruction,''''), COALESCE(DocInstruction,''''), COALESCE(TaskTip,''''), TipImage, 1, 0, 0, 0, 0 FROM [' + @SystemDB + '].[dbo].[OnBoardingTasks]'
		EXEC(@SQL_Query)
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 2
				
		SELECT @SQL_Query = 'INSERT INTO OBProfileTaskFields(CompanyID, ProfileID, TaskID, FName, SeqNbr, FLabel, FType, FSize, DNType, FValueOptions, PermanentTBL, PermanentFLD, RelatedTask, RelatedFLD, FRequired, FLocked, CCAccess, EEAccess, FTip)
				SELECT ''' + CAST(@CompanyID as nvarchar(3)) + ''' as CompanyID, ''' + CAST(@ProfileID as NVARCHAR(10)) + ''' as  ProfileID, TaskID, FName, SeqNbr, FLabel, FType, FSize, DNType, FValueOptions, PermanentTBL, PermanentFLD, RelatedTask, RelatedFLD, FRequired, FLocked, CCAccess, EEAccess, COALESCE(FTip,'''') FROM [' + @SystemDB + '].[dbo].[OnBoardingFields]'
		EXEC(@SQL_Query)
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 4	
	    
		SELECT @SQL_Query = 'INSERT INTO OBProfileNotifications (CompanyID, ProfileID, [Type], Assigned, [Enabled], EmailSubject, EmailBody)
			SELECT ''' + CAST(@CompanyID as nvarchar(3)) + ''' as CompanyID, ''' + CAST(@ProfileID as NVARCHAR(10)) + ''' as  ProfileID, [Type], Assigned, [Enabled],  COALESCE([Subject],''''),  COALESCE([Message],'''') FROM [' + @SystemDB + '].[dbo].[OnBoardingNotifications]'
		EXEC(@SQL_Query)
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 16	
	    
		SELECT @SQL_Query = 'INSERT INTO OBProfileFinalize (CompanyID, ProfileID, Section, Assigned, [Enabled], [Notification])
			SELECT ''' + CAST(@CompanyID as nvarchar(3)) + ''' as CompanyID, ''' + CAST(@ProfileID as NVARCHAR(10)) + ''' as  ProfileID, Section, Assigned, 1, COALESCE([Notification],'''') FROM [' + @SystemDB + '].[dbo].[OnBoardingFinalizeSections]'
		EXEC(@SQL_Query)
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 128	
    END
    ELSE
		SELECT @ErrNum = 1

	UPDATE OBProfileTaskFields SET CCAccess = 5 WHERE ProfileID = @ProfileID  AND CompanyID = @CompanyID AND TaskID = 10 AND FName = 'EmployeeClass' AND CCAccess > 5

	UPDATE OBProfileTaskFields SET EEAccess = 5	WHERE ProfileID = @ProfileID  AND CompanyID = @CompanyID AND TaskID = 10 AND FName = 'EmployeeClass' AND EEAccess > 5

END
