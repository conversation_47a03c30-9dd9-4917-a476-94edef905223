-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Clean process data for reset pending on-boarding employee
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBProcess_RemoveInfo]
	@EmplID as nvarchar(15),
	@CompanyID as int
AS
BEGIN
	DECLARE @ErrNum as int = 0
	
	SET NOCOUNT ON;

	DELETE FROM OBProcessAuditLog WHERE EmployeeID = @EmplID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 512
		
	DELETE FROM OBRequests WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 256
		
	DELETE FROM OBProcessDocumentAssignments WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 128
		
	DELETE FROM OBProcessDocumentUDF WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 64
		
	DELETE FROM OBProcessDocuments WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 32
		
	DELETE FROM OBProcessRecordDetails WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 16
		
	DELETE FROM OBProcessRecords WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 8
		
	DELETE FROM OBProcessTaskFields WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 4
		
	DELETE FROM OBProcessTasks WHERE EmployeeID = @EmplID AND CompanyID = @CompanyID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 2
		
		
	RETURN @ErrNum	
		
END

