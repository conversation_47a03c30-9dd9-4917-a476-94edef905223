-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: 1/16/2023
-- Description:	return data for 'Rapid Pay Employee' export
-- =============================================
CREATE PROCEDURE [dbo].[sp_RapidPay_Employee] 
		@companyid int,
		@clientid nvarchar(15),
		@location nvarchar(30),
		@start datetime,
		@end datetime = null
AS
BEGIN
	SET NOCOUNT ON;
	IF @end = null SET @end = @start

	SELECT e.EmployeeID, e.SSN, e.FirstName, e.LastName, 
			CASE WHEN e.Email <> '' THEN e.Email ELSE CASE WHEN e.DefaultEmailAddress <> '' THEN e.DefaultEmailAddress ELSE CASE WHEN e.DarwiNetEmail <> '' THEN e.DarwinetEmail ELSE '' END END END as [Email],
			'' as [EmployeeNumber], e.Rapid_Pay_LocationID as [LocationID], rp.[Description] as [LocationName], ds.Rapid_Pay_CompanyID as [CompanyID], c.ClientName as [CompanyName],
			e.OriginalHireDate as [HiredDate], e.TerminationDate, e.BirthDate, a.Phone1 as [PhoneNumber], '' as [PolicyID], '' as [PayType], '' as [PaySchedule], 0 as [WagesEligible], 0 as [TipsEligible],
			'' as [DebitCardNumber], '' as [DebitCardExpYear], '' as [DebitCardExpMonth], '' as [BillingStreet], '' as [BillingAddress2], '' as [BillingCity],  '' as [BillingState], '' as [BillingZip],
			IsNull(b.TransitNumber, '') as [BankRouting], IsNull(dh.AccountNumber, '') as [BankAccount], '' as [NickName]
	FROM Employees e
	INNER JOIN Clients c ON c.CompanyID = e.CompanyID AND c.ClientID = e.ClientID
	INNER JOIN DarwinetSetup ds ON ds.CompanyID = e.CompanyID AND ds.ClientID = e.ClientID
	INNER JOIN RapidPaySetup rp ON rp.CompanyID = e.CompanyID AND rp.ClientID = e.ClientID AND rp.Rapid_Pay_LocationID = e.Rapid_Pay_LocationID
	LEFT OUTER JOIN EmployeeAddresses a ON a.CompanyID = e.CompanyID AND a.EmployeeID = e.EmployeeID AND a.AddressCode = e.AddressCode
	INNER JOIN EmployeeCheckHistory h ON h.CompanyID = e.CompanyID AND h.EmployeeID = e.EmployeeID
	LEFT OUTER JOIN EmployeeDirectDepositHistory dh ON dh.CompanyID = h.CompanyID AND dh.EmployeeID = e.EmployeeID AND dh.AuditControlCode = h.AuditControlCode AND dh.PaymentAdjustmentNumber = h.PaymentAdjustmentNumber
	LEFT OUTER JOIN Banks b ON b.CompanyID = dh.CompanyID AND b.BankID = dh.BankID
	WHERE e.CompanyID = @companyid AND e.ClientID = @clientid AND h.CheckDate BETWEEN @start AND @end AND e.Rapid_Pay_LocationID = @location
	ORDER BY e.EmployeeID, h.CheckDate

END
