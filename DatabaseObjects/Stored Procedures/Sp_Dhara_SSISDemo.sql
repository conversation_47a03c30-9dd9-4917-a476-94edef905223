-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
CREATE PROCEDURE [dbo].[Sp_Dhara_SSISDemo] 
	-- Add the parameters for the stored procedure here
	
	AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	select  (e.LastName + ', ' + e.FirstName) as [EmployeeName], e.Employee<PERSON> as [EmployeeID]
,'***-**-' + SUBSTRING(e.SSN, 6, 4) [SSN],
eth.CheckDate [CheckDate],
eth.CheckNumber[CheckNumber],
SUM((case when eth.PayrollRecordType = 1 then eth.TRXAmount else 0.00 end)) as [GrossWages],
0 as [NetWages],
SUM((case when eth.PayrollRecordType = 3 then eth.TRXAmount else 0.00 end)) as Benifits
,SUM((case when eth.PayrollRecordType = 4 then eth.TRXAmount else 0.00 end)) as [StateTaxes]
,SUM((case when eth.PayrollRecordType = 5 then eth.TRXAmount else 0.00 end)) as [LocalTaxes]
,SUM((case when eth.PayrollRecordType <> 2 then eth.TRXAmount else 0.00 end)) as [Total]
,1 as 'CheckboxTrueValue' , 0 as 'CheckboxFalseValue'
from EmployeeTransactionHistory eth
inner join Employees e on e.CompanyID = eth.CompanyID and e.EmployeeID = eth.EmployeeID
left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and ce.ClientID = e.ClientID
LEFT OUTER JOIN EmployeePaycodes ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode
where eth.checkdate 
between '01-01-2020' and '01-31-2020'
group by e.EmployeeID,e.LastName + ', ' +
e.FirstName, e.SSN,eth.CheckNumber,eth.CheckDate
order by e.EmployeeID

	
END
