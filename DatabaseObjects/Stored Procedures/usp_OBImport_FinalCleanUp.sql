-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Remove all not accepted on-boarding records
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBImport_FinalCleanUp]
	@ImportID as int,
	@CompanyID as int
AS
BEGIN
	DECLARE @ErrNum as int = 0
	
	SET NOCOUNT ON;
	
	IF @ImportID > 0 
	BEGIN
		DELETE FROM OBProcessRecordDetails WHERE RecordID IN (SELECT RecordID FROM OBProcessRecords WHERE ImportStatus = @ImportID AND CompanyID = @CompanyID)
		IF @@ERROR <> 0
			SET @ErrNum = @ErrNum + 2
			
		DELETE FROM OBProcessRecords WHERE ImportStatus = @ImportID AND CompanyID = @CompanyID
		IF @@ERROR <> 0
			SET @ErrNum = @ErrNum + 1
	END	
END

