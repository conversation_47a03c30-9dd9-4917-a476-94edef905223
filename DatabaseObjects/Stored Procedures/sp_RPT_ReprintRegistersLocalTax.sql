/*
********************************************************************
**** Created by <PERSON>.
**** Date: 04/27/2015.
**** Purpose: Reprint Local Tax Register Reports data.
****
********************************************************************
*/

-- sp_RPT_ReprintRegistersLocalTax 1, '003', 136
-- sp_RPT_ReprintRegistersLocalTax 1, '021', 19
CREATE   PROCEDURE [dbo].[sp_RPT_ReprintRegistersLocalTax]
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@invoice int = NULL,
	@userid NVARCHAR(20)
)
AS

CREATE TABLE #ReprintPostingRegister(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[AuditControlCode] [nvarchar](13) NULL,
	[Paycode] [nvarchar](30) NULL,
	[Description] [nvarchar](30) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[EmployeeName] [nvarchar](80) NULL,
	[UnitsToPay] [decimal](19,5) NULL,
	[Total] [decimal](19,5) NULL,
	[Report] [int] NULL,
	[CheckDate] [datetime] NULL,
	[MTD] [decimal](19,5) NULL,
	[YTD] [decimal](19,5) NULL)


CREATE TABLE #YTDByPayrollCode(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[ClientId] [nvarchar](15) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[Year] [nvarchar](4) NULL,
	[PayrollCode] [nvarchar](7) NULL,
	[YTD] [decimal](19,5) NULL)

CREATE TABLE #MTDByPayrollCode(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[ClientId] [nvarchar](15) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[Year] [nvarchar](4) NULL,
	[PayrollCode] [nvarchar](7) NULL,
	[Month] [int] NULL,
	[MTD] [decimal](19,5) NULL)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

-- Build YTD temp table to simplify the join.
INSERT INTO #YTDByPayrollCode(CompanyId, EmployeeID, [Year], PayrollCode, YTD)
select tblPivot.CompanyID, tblPivot.EmployeeID, tblpivot.[Year], tblPivot.PayrollCode, sum(tblPivot.Value) YTD
FROM (SELECT e.CompanyID, e.[Year], e.EmployeeID, e.PayrollRecordType, e.PayrollCode,
			MonthToDateWages1 as January, MonthToDateWages2 as February, MonthToDateWages3 as March, MonthToDateWages4 as April, 
			MonthToDateWages5 as May, MonthToDateWages6 as June, MonthToDateWages7 as July, MonthToDateWages8 as August, 
			MonthToDateWages9 as September, MonthToDateWages10 as October, MonthToDateWages11 as November, MonthToDateWages12 as December
	  FROM EmployeeTransactionHistoryHDR e
		INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (January, February, March, April, May, June, July, August, September, October, November, December)) as tblPivot
WHERE PayrollRecordType = 5	-- Local Tax
GROUP BY CompanyID, EmployeeID, [Year], PayrollCode

-- Build MTD temp table to simplify the join.
INSERT INTO #MTDByPayrollCode(CompanyId, EmployeeID, [Month], [Year], PayrollCode, MTD)
select tblPivot.CompanyID, tblPivot.EmployeeID, Month(rtrim(cast(tblPivot.[Month] as varchar(50))) + ' 1 2015') as MM, tblpivot.[Year],
	   tblPivot.PayrollCode, sum(tblPivot.Value) MTD
FROM (SELECT e.CompanyID, e.[Year], e.EmployeeID, e.PayrollRecordType, e.PayrollCode,
			MonthToDateWages1 as January, MonthToDateWages2 as February, MonthToDateWages3 as March, MonthToDateWages4 as April, 
			MonthToDateWages5 as May, MonthToDateWages6 as June, MonthToDateWages7 as July, MonthToDateWages8 as August, 
			MonthToDateWages9 as September, MonthToDateWages10 as October, MonthToDateWages11 as November, MonthToDateWages12 as December
	  FROM EmployeeTransactionHistoryHDR e
		INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (January, February, March, April, May, June, July, August, September, October, November, December)) as tblPivot
WHERE PayrollRecordType = 5	-- Local Tax
GROUP BY CompanyID, EmployeeID, [Year], [Month], PayrollCode

-- Local Tax record type = 5
INSERT INTO #ReprintPostingRegister(AuditControlCode, Paycode, [Description], EmployeeID, EmployeeName, UnitsToPay, Total, Report, CheckDate, YTD, MTD)
select h.AuditControlCode, h.PayrollCode, coalesce(p.[Description],'') [Description], h.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName],
	   h.UnitsToPay [UnitsToPay], h.TRXAmount [PayPeriodDollars], 7, h.CheckDate, tblYTD.YTD, tblMTD.MTD
from EmployeeTransactionHistory h
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = h.EmployeeID
	left outer join Paycodes p on h.PayrollCode = p.PayRecord
	left outer join Employees e on e.CompanyID = h.CompanyID and h.EmployeeID = e.EmployeeID
	left outer join #YTDByPayrollCode tblYTD on tblYTD.CompanyID = h.CompanyID and tblYTD.EmployeeID = h.EmployeeID 
				and tblYTD.PayrollCode = h.PayrollCode and tblYTD.[Year] = year(h.CheckDate) 
	left outer join #MTDByPayrollCode tblMTD on tblMTD.CompanyID = h.CompanyID and tblMTD.EmployeeID = h.EmployeeID
				and tblMTD.PayrollCode = h.PayrollCode and tblMTD.[Year] = Year(h.CheckDate) and tblMTD.[Month] = Month(h.CheckDate)
where h.CompanyID = @company and h.PayrollRecordType = 5
  and h.AuditControlCode IN (
			select AuditControlCode
			from Invoices i
			left outer join InvoicePayrolls cip on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
			where i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
		)
order by h.PayrollCode

select t.* 
from #ReprintPostingRegister t

DROP TABLE #Departments
DROP TABLE #Divisions
DROP TABLE #EmployeeIDs
DROP TABLE #MTDByPayrollCode
DROP TABLE #ReprintPostingRegister
DROP TABLE #YTDByPayrollCode

