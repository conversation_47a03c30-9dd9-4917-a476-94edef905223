CREATE  PROCEDURE [dbo].[sp_RPT_InvoiceRegisterDetailByEEDepartment]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@division NVARCHAR(15) = NULL,
	@invoice int = NULL,
	@invoiceEnd int = NULL,
	@InvoiceDateBegin datetime = NULL,
	@InvoiceDateEnd datetime = NULL,
	@userid NVARCHAR(20))
AS		



CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[CheckNumber] [int] NULL,
	[CheckDate] [date] NULL, 
	[PaymentAdjustmentNumber] [int] NULL,
	[Description] [nvarchar](30) NULL,
	[Units] [decimal](19,5) NULL,
	[Rate] [decimal](19,5) NULL,
	[EarnAmount] [decimal](19,5) NULL,
	[TaxAmount] [decimal](19,5) NULL,
	[DeductAmount] [decimal](19,5) NULL,
	[BenefitAmount] [decimal](19,5) NULL,
	[FeeAmount] [decimal](19,5) NULL,
	[TotalAmount] [decimal](19,5) NULL,
	[Department] [nvarchar](30) NULL,
	[Position] [nvarchar](30) NULL,
	[FeeDesc] [varchar](50) NULL,
	[FeeTotalAmount] [decimal](19,2) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)


CREATE TABLE #EmployeePayExtras(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[PaymentAdjustmentNumber] [int] NULL,
	[NetPay] [decimal](19,5) NULL,
	[DirectDeposits] [decimal](19,5) NULL,
	[OtherDeposits] [decimal](19,5) NULL )

CREATE TABLE #InvoiceFees(
	[Id] [int] IDENTITY(1,1) NOT NULL,	
	[CompanyID] [int] NULL,
	[ClientID] [nvarchar](15) NULL,
	[DarwinInvoiceNumber] [int] NULL,
	[ChargeCode] [nvarchar](30) NULL,
	[TotalFUTAAmount] [decimal](19,5) NULL, 
	[TotalSUTAAmount] [decimal](19,5) NULL, 
	[TotalFICASSAmount] [decimal](19,5) NULL, 
	[TotalFICAMAmount] [decimal](19,5) NULL, 
	[TotalWorkersCompAmount] [decimal](19,5) NULL, 
	[TotalAdminFee] [decimal](19,5) NULL, 
	[TotalFlatFeeAmount] [decimal](19,5) NULL, 
	[TotalCharge] [decimal](19,5) NULL
	)

CREATE TABLE #Fees(
	[Id] [int] IDENTITY(1,1) NOT NULL,	
	[CompanyID] [int] NULL,
	[ClientID] [nvarchar](15) NULL,
	[DarwinInvoiceNumber] [int] NULL,
	[EmployeeID] [nvarchar](15) NOT NULL,
	[Department] [nvarchar](30) NOT NULL,
	[Position] [nvarchar](30) NOT NULL,
	[ChargeCode] [nvarchar](30) NULL,
	[AdminFee] [decimal](19,5) NULL, 
	[FlatFeeAmount] [decimal](19,5) NULL,
	[Adjustment] [decimal](19,5) NULL 
	)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

CREATE TABLE #Invoices([Id] [int] IDENTITY(1,1) NOT NULL, [Invoice] [int] NOT NULL)
CREATE TABLE #MergeInvoices([Id] [int] IDENTITY(1,1) NOT NULL, [Invoice] [int] NOT NULL)
declare @iEnd int = NULL

if (@invoiceEnd is null) 
	select @invoiceEnd = @Invoice
if (@InvoiceDateEnd is null)
	select @InvoiceDateEnd = @InvoiceDateBegin

if (@invoice is not null)
	insert into #Invoices(Invoice) 	
	SELECT distinct DarwinInvoiceNumber 
	from Invoices i
		INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	where DarwinInvoiceNumber between @invoice and @invoiceEnd and i.CompanyID = @company and i.ClientID = @client and (i.DivisionID = @division or @division is null)
else if (@InvoiceDateBegin is not null )
	insert into #Invoices(Invoice)	
	SELECT distinct DarwinInvoiceNumber 
	from Invoices i
		INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	where [Date] between @InvoiceDateBegin and @InvoiceDateEnd and i.CompanyID = @company and i.ClientID = @client and (i.DivisionID = @division or @division is null)

insert into #MergeInvoices select Invoice from #Invoices
insert into #MergeInvoices 
	SELECT distinct i.DarwinInvoiceNumber 
	FROM Invoices i
	WHERE i.CompanyID = @company and i.ClientID = @client and i.MergedInvoiceNumber IN (SELECT Invoice FROM #Invoices)

declare @ShowVoidedChecks bit
select @ShowVoidedChecks = ShowVoidedChecks from DarwinetSetup where CompanyID = @company and ClientID = @client

Insert into #InvoiceFees (CompanyID, ClientID, DarwinInvoiceNumber, ChargeCode, TotalFUTAAmount, TotalSUTAAmount, TotalFICASSAmount, TotalFICAMAmount, TotalWorkersCompAmount, TotalAdminFee, TotalFlatFeeAmount, TotalCharge)
SELECT     ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, icd.FUTA, icd.SUTA, icd.FICASS, icd.FICAM, icd.WC, icd.AdminFee, icd.FlatFeeAmount, ic.Total
FROM         dbo.InvoiceCharges AS ic INNER JOIN
                      dbo.Invoices AS i ON i.CompanyID = ic.CompanyID AND i.ClientID = ic.ClientID AND i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber LEFT OUTER JOIN
                          (SELECT     d.CompanyID, d.ClientID, d.DarwinInvoiceNumber, d.ChargeType AS ChargeCode, SUM(ISNULL(d.FUTAAmount, 0)) AS FUTA, SUM(ISNULL(d.SUTAAmount, 0)) AS SUTA, SUM(ISNULL(d.FICASSAmount, 0)) AS FICASS, 
						  SUM(ISNULL(FICAMAmount, 0)) AS FICAM, SUM(ISNULL(d.WorkersCompAmount, 0)) AS WC, SUM(ISNULL(d.AdminFee, 0)) AS AdminFee, SUM(ISNULL(d.FlatFeeAmount, 0)) AS FlatFeeAmount
                            FROM          dbo.InvoiceEmployeeChargeDetails AS d 
                            GROUP BY d.CompanyID, ClientID, d.DarwinInvoiceNumber, d.ChargeType) AS icd ON icd.CompanyID = ic.CompanyID AND icd.ClientID = ic.ClientID AND 
                      icd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber AND icd.ChargeCode = ic.ChargeType INNER JOIN
                      dbo.ChargeSetup AS s ON s.ChargeCode = ic.ChargeType AND s.CompanyID = ic.CompanyID
WHERE     ic.CompanyID = @company and ic.ClientID = @CLIENT and i.DarwinInvoiceNumber in (select Invoice from #Invoices) and (i.DivisionID = @division or @division IS NULL) AND (ic.ChargeType NOT IN( 'PAYROLL', 'OUTSTANDING BALANCE'))

Insert into #Fees([CompanyID], [ClientID], [DarwinInvoiceNumber], [EmployeeID], [Department], [Position], [ChargeCode], [AdminFee], [FlatFeeAmount], Adjustment)
SELECT   icd.CompanyID, icd.ClientID, icd.DarwinInvoiceNumber, icd.EmployeeID, icd.Department, icd.Position, icd.ChargeType, SUM(ISNULL(icd.AdminFee, 0)), SUM(ISNULL(icd.FlatFeeAmount, 0)),
	case WHEN ISNULL(MAX(f.TotalAdminFee),0) <> 0 THEN case WHEN ISNULL(MAX(f.TotalCharge),0) <> 0 THEN (ISNULL(MAX(f.TotalCharge),0) - ISNULL(MAX(f.TotalFlatFeeAmount),0)) / ISNULL(MAX(f.TotalAdminFee),0) ELSE 1.00 END ELSE 1.00 END
FROM   dbo.InvoiceEmployeeChargeDetails AS icd LEFT OUTER JOIN
	   #InvoiceFees AS f ON icd.DarwinInvoiceNumber = f.DarwinInvoiceNumber AND icd.ChargeType = f.ChargeCode	
WHERE     (icd.ChargeType <> 'PAYROLL') AND (icd.CompanyID = @company) AND (icd.ClientID = @client) AND icd.DarwinInvoiceNumber in (select Invoice from #Invoices) AND icd.ChargeType IN (SELECT DISTINCT ChargeCode FROM ChargeSetup WHERE CompanyID = @company AND ChargeType <> 14)
GROUP BY icd.CompanyID, icd.ClientID, icd.DarwinInvoiceNumber, icd.EmployeeID, icd.Department, icd.Position, icd.ChargeType
 
Insert into #Fees([CompanyID], [ClientID], [DarwinInvoiceNumber], [EmployeeID], [Department], [Position], [ChargeCode], [AdminFee], [FlatFeeAmount], Adjustment)
SELECT   icd.CompanyID, icd.ClientID, icd.DarwinInvoiceNumber, icd.EmployeeID, icd.Department, icd.Position, icd.ChargeType, SUM(ISNULL(icd.AdminFee, 0)), SUM(ISNULL(icd.FlatFeeAmount, 0)),
	case WHEN ISNULL(MAX(f.TotalAdminFee),0) <> 0 THEN case WHEN ISNULL(MAX(f.TotalCharge),0) <> 0 THEN (ISNULL(MAX(f.TotalCharge),0) - ISNULL(MAX(f.TotalFUTAAmount),0) - ISNULL(MAX(f.TotalSUTAAmount),0) - ISNULL(MAX(f.TotalFICASSAmount),0) - ISNULL(MAX(f.TotalFICAMAmount),0) - ISNULL(MAX(f.TotalWorkersCompAmount),0) - ISNULL(MAX(f.TotalFlatFeeAmount),0)) / ISNULL(MAX(f.TotalAdminFee),0) ELSE 1.00 END ELSE 1.00 END
FROM   dbo.InvoiceEmployeeChargeDetails AS icd LEFT OUTER JOIN
	   #InvoiceFees AS f ON icd.DarwinInvoiceNumber = f.DarwinInvoiceNumber AND icd.ChargeType = f.ChargeCode	
WHERE     (icd.ChargeType <> 'PAYROLL') AND (icd.CompanyID = @company) AND (icd.ClientID = @client) AND icd.DarwinInvoiceNumber in (select Invoice from #Invoices) AND icd.ChargeType IN (SELECT DISTINCT ChargeCode FROM ChargeSetup WHERE CompanyID = @company AND ChargeType = 14)
GROUP BY icd.CompanyID, icd.ClientID, icd.DarwinInvoiceNumber, icd.EmployeeID, icd.Department, icd.Position, icd.ChargeType
 
declare @totalCharges decimal(19,5)
select @totalCharges = sum(ISNULL(TotalCharge,0) - ISNULL(TotalFUTAAmount,0) - ISNULL(TotalSUTAAmount,0) - ISNULL(TotalFICASSAmount,0) - ISNULL(TotalFICAMAmount,0) - ISNULL(TotalWorkersCompAmount,0)) from #InvoiceFees

declare @totalFees decimal(19,5)
select @totalFees = sum(ISNULL(AdminFee,0) + ISNULL(FlatFeeAmount,0)) from #Fees

declare @empNum int
select @empNum = count(distinct EmployeeID) from #Fees
if (@totalFees = 0 and @empNum > 0 and @totalCharges <> 0)
	update #Fees SET AdminFee = @totalCharges/@empNum, Adjustment = 1

-- Get Section 3 Benefits.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], BenefitAmount, Section, Sort)
select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, h.Department, h.Position, PayrollCode [Description], SUM(TRXAmount) [Amount], 3 [Section], 1 [Sort]
from EmployeeTransactionHistory h
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = h.EmployeeID
	left outer join InvoicePayrolls cip on cip.CompanyID = h.CompanyID and cip.AuditControlCode = h.AuditControlCode
	left outer join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
	inner join Employees e ON e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID and e.ClientID = i.ClientID
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = h.CompanyID AND ech.AuditControlCode = h.AuditControlCode AND ech.CheckNumber = h.CheckNumber AND ech.EmployeeID = h.EmployeeID --AND ech.Department = h.Department
	inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = h.Department
where cip.CompanyID = @company and cip.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #MergeInvoices) and h.PayrollRecordType = 3
  and (i.DivisionID = @division or @division is null) and ech.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN ech.Voided ELSE 0 END
GROUP BY e.EmployeeID, e.FirstName + ' ' + e.LastName, e.SSN, h.Department, h.Position, PayrollCode
order by EmployeeID

-- Get Section 2 - FICA SS
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, ISNULL(ied.Department, e.Department), ISNULL(ied.Position, e.Position),
	   'FICA SS' [Description], SUM(ied.FICASSAmount) [Amount], 2 [Section], 2 [Sort] -- FICA SS comes fourth. 
from InvoiceEmployeeDetails ied
	INNER JOIN #Departments d ON d.Department = ied.Department
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = ied.EmployeeID
	inner join Employees e ON e.EmployeeID = ied.EmployeeID and e.CompanyID = ied.CompanyID and e.ClientID = ied.ClientID
	left outer join Invoices i on i.CompanyID = ied.CompanyID and i.ClientID = ied.ClientID and i.DarwinInvoiceNumber = ied.DarwinInvoiceNumber
where ied.CompanyID = @company and ied.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #Invoices)
  and (i.DivisionID = @division or @division is null)
GROUP BY e.EmployeeID, e.FirstName + ' ' + e.LastName, e.SSN, e.Department, ied.Department, e.Position, ied.Position

-- Get Section 2 - FICA Med
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, ISNULL(ied.Department, e.Department), ISNULL(ied.Position, e.Position),
	   'FICA Med' [Description], SUM(ied.FICAMAmount), 2 [Section], 1 [Sort] -- FICA Med comes fifth. 
from InvoiceEmployeeDetails ied
	INNER JOIN #Departments d ON d.Department = ied.Department
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = ied.EmployeeID
	inner join Employees e ON e.EmployeeID = ied.EmployeeID and e.CompanyID = ied.CompanyID and e.ClientID = ied.ClientID
	left outer join Invoices i on i.CompanyID = ied.CompanyID and i.ClientID = ied.ClientID and i.DarwinInvoiceNumber = ied.DarwinInvoiceNumber
where ied.CompanyID = @company and ied.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #Invoices) and (i.DivisionID = @division or @division is null)
GROUP BY e.EmployeeID, e.FirstName + ' ' + e.LastName, e.SSN, e.Department, ied.Department, e.Position, ied.Position

-- Get Section 2 - FUTA Tax.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, fsw.Department, fsw.Position, 'FUTA', SUM(fsw.FUTABilling) [Amount], 2, 3
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	inner join EmployeeFutaSutaWorkersCompHistory fsw on fsw.CompanyID = i.CompanyID and fsw.ClientID = i.ClientID and fsw.AuditControlCode = cip.AuditControlCode
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = fsw.EmployeeID
	inner join Employees e on e.EmployeeID = fsw.EmployeeID and e.CompanyID = fsw.CompanyID and e.ClientID = fsw.ClientID
	inner join EmployeeCheckHistory ech on ech.CompanyID = i.CompanyID and ech.AuditControlCode = fsw.AuditControlCode and ech.EmployeeID = fsw.EmployeeID and ech.CheckDate = fsw.CheckDate --and ech.Department = fsw.Department
	inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = ech.Department
	INNER JOIN #Departments d ON d.Department = cdd.Department
where i.CompanyID = @company and i.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #MergeInvoices) and fsw.PayrollRecordType = 2
  and (i.DivisionID = @division or @division is null) and ech.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN ech.Voided ELSE 0 END
GROUP BY e.EmployeeID, e.FirstName + ' ' + e.LastName, e.SSN, fsw.Department, fsw.Position
		
-- Get Section 2 - SUTA Tax.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, fsw.Department, fsw.Position, 'SUTA', SUM(fsw.FUTABilling), 2, 4
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	inner join EmployeeFutaSutaWorkersCompHistory fsw on fsw.CompanyID = i.CompanyID and fsw.ClientID = i.ClientID and fsw.AuditControlCode = cip.AuditControlCode
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = fsw.EmployeeID
	inner join Employees e on e.EmployeeID = fsw.EmployeeID and e.CompanyID = fsw.CompanyID and e.ClientID = fsw.ClientID
	inner join EmployeeCheckHistory ech on ech.CompanyID = i.CompanyID and ech.AuditControlCode = fsw.AuditControlCode and ech.EmployeeID = fsw.EmployeeID and ech.CheckDate = fsw.CheckDate --and ech.Department = fsw.Department
	inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = ech.Department
	INNER JOIN #Departments d ON d.Department = cdd.Department
where i.CompanyID = @company and i.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #MergeInvoices) and fsw.PayrollRecordType = 1
  and (i.DivisionID = @division or @division is null) and ech.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN ech.Voided ELSE 0 END
GROUP BY e.EmployeeID, e.FirstName + ' ' + e.LastName, e.SSN, fsw.Department, fsw.Position

-- Get Section 2 - WC Tax.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, fsw.Department, fsw.Position, 'WC', SUM(fsw.FUTABilling), 2, 5
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	inner join EmployeeFutaSutaWorkersCompHistory fsw on fsw.CompanyID = i.CompanyID and fsw.ClientID = i.ClientID and fsw.AuditControlCode = cip.AuditControlCode
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = fsw.EmployeeID
	inner join Employees e on e.EmployeeID = fsw.EmployeeID and e.CompanyID = fsw.CompanyID and e.ClientID = fsw.ClientID
	inner join EmployeeCheckHistory ech on ech.CompanyID = i.CompanyID and ech.AuditControlCode = fsw.AuditControlCode and ech.EmployeeID = fsw.EmployeeID and ech.CheckDate = fsw.CheckDate --and ech.Department = fsw.Department
	inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = ech.Department
	INNER JOIN #Departments d ON d.Department = cdd.Department
where i.CompanyID = @company and i.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #MergeInvoices) and fsw.PayrollRecordType = 3
  and (i.DivisionID = @division or @division is null) and ech.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN ech.Voided ELSE 0 END
GROUP BY e.EmployeeID, e.FirstName + ' ' + e.LastName, e.SSN, fsw.Department, fsw.Position

-- Get Section 1 - Earnings.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], EarnAmount, Rate, Units, Section, Sort)
select x.EmployeeID, x.EmployeeName, x.SSN, x.Department, x.Position, x.[Description], SUM(x.TRXAmount) [TRXAmount], x.PayRate,
			SUM(x.UnitsToPay) [UnitsToPay], 1 [Section], 1 [Sort]
FROM (select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, h.Department, h.Position, h.PayrollCode [Description],
				h.TRXAmount [TRXAmount], h.PayRate, h.UnitsToPay [UnitsToPay], 1 [Section], 1 [Sort]
	  from EmployeeTransactionHistory h
		INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = h.EmployeeID
		left outer join InvoicePayrolls cip on cip.CompanyID = h.CompanyID and cip.AuditControlCode = h.AuditControlCode
		left outer join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
		inner join Employees e on e.CompanyID = h.CompanyID and e.EmployeeID = h.EmployeeID and e.ClientID = i.ClientID
		left outer join EmployeeCheckHistory ech ON ech.CompanyID = h.CompanyID AND ech.AuditControlCode = h.AuditControlCode AND ech.CheckNumber = h.CheckNumber AND ech.EmployeeID = h.EmployeeID --AND ech.Department = h.Department
	 inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = ech.Department
	 where h.CompanyID = @company and cip.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #MergeInvoices) and h.PayrollRecordType = 1 and h.PayRate <> 0 
	    and (i.DivisionID = @division or @division is null) and ech.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN ech.Voided ELSE 0 END
	 ) x
GROUP BY x.EmployeeID, x.EmployeeName, x.SSN, x.Department, x.Position, x.[Description], x.PayRate

insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], EarnAmount, Rate, Units, Section, Sort)
select x.EmployeeID, x.EmployeeName, x.SSN, x.Department, x.Position, x.[Description], SUM(x.TRXAmount) [TRXAmount], SUM(x.TRXAmount)/SUM(x.UnitsToPay) [Rate], SUM(x.UnitsToPay) [UnitsToPay], 1 [Section], 1 [Sort]
FROM (select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, h.Department, h.Position, h.PayrollCode [Description], h.TRXAmount [TRXAmount], h.PayRate, h.UnitsToPay [UnitsToPay], 1 [Section], 1 [Sort]
	  from EmployeeTransactionHistory h
		INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = h.EmployeeID
		left outer join InvoicePayrolls cip on cip.CompanyID = h.CompanyID and cip.AuditControlCode = h.AuditControlCode
		left outer join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
		inner join Employees e on e.CompanyID = h.CompanyID and e.EmployeeID = h.EmployeeID and e.ClientID = i.ClientID
		left outer join EmployeeCheckHistory ech ON ech.CompanyID = h.CompanyID AND ech.AuditControlCode = h.AuditControlCode AND ech.CheckNumber = h.CheckNumber AND ech.EmployeeID = h.EmployeeID --AND ech.Department = h.Department
	 inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = ech.Department
	 where h.CompanyID = @company and cip.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #MergeInvoices)
	    and h.PayrollRecordType = 1 and h.PayRate = 0 and (i.DivisionID = @division or @division is null) and ech.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN ech.Voided ELSE 0 END
	) x
GROUP BY x.EmployeeID, x.EmployeeName, x.SSN, x.Department, x.Position, x.[Description]

-- Get Section 4 - Fees/Credits.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, [Description], FeeAmount, Section, Sort)
select e.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName], e.SSN, iecd.Department, iecd.Position, iecd.ChargeCode,
			sum(AdminFee * Adjustment + FlatFeeAmount), 4 [Section], 1 [Sort]
from #Fees iecd
	INNER JOIN #Departments d ON d.Department = iecd.Department
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = iecd.EmployeeID
	inner join Invoices i on i.CompanyID = iecd.CompanyID and i.ClientID = iecd.ClientID and i.DarwinInvoiceNumber = iecd.DarwinInvoiceNumber
		   --AND i.InvoiceNumber = iecd.InvoiceNumber
	inner join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	--inner join EmployeeCheckHistory ech on ech.CompanyID = iecd.CompanyID and ech.AuditControlCode = cip.AuditControlCode and ech.EmployeeID = iecd.EmployeeID --and ech.Department = iecd.Department
	inner join Employees e on e.EmployeeID = iecd.EmployeeID and e.CompanyID = i.CompanyID and e.ClientID = i.ClientID
	inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = iecd.Department
where i.CompanyID = @company and i.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #Invoices)
  and (i.DivisionID = @division or @division is null) --and ech.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN ech.Voided ELSE 0 END
GROUP BY e.EmployeeID, e.FirstName + ' ' + e.LastName, e.SSN, iecd.Department, iecd.Position, iecd.ChargeCode


--Section 6 - Report Totals Fees Section
--All charges which is not in InvoiceEmployeeChargeDetails.

insert into #EmployeePayDetail([FeeDesc], FeeTotalAmount, Section, Sort)
select ic.ChargeType,Sum(ic.Total) as FeeTotalAmount,6,1  from InvoiceCharges ic
left outer join (select CompanyID, ClientId, ChargeType, DarwinInvoiceNumber from InvoiceEmployeeChargeDetails iecd
				where AdminFee <> 0
				group by CompanyID, ClientId, ChargeType, DarwinInvoiceNumber) iecd
on iecd.CompanyID = ic.CompanyID and iecd.ClientID = ic.ClientID and iecd.ChargeType = ic.ChargeType and iecd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
where ic.CompanyID=@company and ic.ClientID = @client  and ic.DarwinInvoiceNumber IN (select Invoice from #Invoices) and iecd.ChargeType is null
and ic.ChargeType NOT IN ('OTHER TAXES', 'PAYROLL', 'OUTSTANDING BALANCE', 'ADMINISTRATION FEE')
group by ic.ChargeType




--All Charges which is in InvoiceEmployeedetails and InvoiceCharges Both

insert into #EmployeePayDetail([FeeDesc], FeeTotalAmount, Section, Sort)
select 'Difference-' + pwc.ChargeType,case when (Sum(ic.Total) <> Sum(pwc.AdminFee)) then ( Sum(ic.Total) - Sum(pwc.AdminFee))  end as FeeTotalAmount,
6 Section,1 Sort 
from InvoiceCharges ic
inner join (select ied.CompanyID,ied.ClientID,isnull(sum(ied.AdminFee),0) AdminFee,ied.DarwinInvoiceNumber,ied.ChargeType FROM InvoiceEmployeeChargeDetails ied
where ied.CompanyID = @company and ied.ClientID=@client and  ied.DarwinInvoiceNumber IN (select Invoice from #Invoices) 
GROUP BY ied.CompanyID, ied.ClientID, ied.ChargeType,ied.DarwinInvoiceNumber
) pwc
on pwc.CompanyID = ic.CompanyID and pwc.ClientID=ic.ClientID and pwc.DarwinInvoiceNumber = ic.DarwinInvoiceNumber and pwc.ChargeType = ic.ChargeType
where pwc.CompanyID=@company and pwc.ClientID = @client  and  ic.DarwinInvoiceNumber IN (select Invoice from #Invoices)  and ic.ChargeType NOT IN ('ADMINISTRATION FEE', 'OUTSTANDING BALANCE')
group by ic.CompanyID,ic.ClientID,ic.DarwinInvoiceNumber,ic.ChargeType,pwc.ChargeType


--All Charges which is in InvoiceEmployeeChargeDetails and InvoiceCharges Both then print details as well

insert into #EmployeePayDetail([FeeDesc], FeeTotalAmount, Section, Sort)
select  pwc.ChargeType,case when (Sum(ic.Total) <> Sum(pwc.AdminFee)) then (Sum(pwc.AdminFee))  end as FeeTotalAmount,
6 Section,1 Sort 
from InvoiceCharges ic
inner join (select ied.CompanyID,ied.ClientID,isnull(sum(ied.AdminFee),0) AdminFee,ied.DarwinInvoiceNumber,ied.ChargeType FROM InvoiceEmployeeChargeDetails ied
where ied.CompanyID = @company and ied.ClientID=@client and  ied.DarwinInvoiceNumber IN (select Invoice from #Invoices) 
GROUP BY ied.CompanyID, ied.ClientID, ied.ChargeType,ied.DarwinInvoiceNumber
) pwc
on pwc.CompanyID = ic.CompanyID and pwc.ClientID=ic.ClientID and pwc.DarwinInvoiceNumber = ic.DarwinInvoiceNumber and pwc.ChargeType = ic.ChargeType
where pwc.CompanyID=@company and pwc.ClientID = @client  and  ic.DarwinInvoiceNumber IN (select Invoice from #Invoices)  and ic.ChargeType NOT IN ('ADMINISTRATION FEE', 'OUTSTANDING BALANCE')
group by ic.CompanyID,ic.ClientID,ic.DarwinInvoiceNumber,ic.ChargeType,pwc.ChargeType


--if section 6 add  blank
declare @countnumber int
select @countnumber = isnull(count(*),0) from #EmployeePayDetail where Section = 6

print @countnumber
if(@countnumber = 0)
insert into #EmployeePayDetail(EmployeeID,[FeeDesc], FeeTotalAmount, Section, Sort)
values ('test',null,0,6,1)



-- Get Section 6 - Gross Wage Totals.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, [Description], TotalAmount, CheckDate, Section, Sort)
select EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, 'Gross Wages', sum(EarnAmount) [TotalAmount], CheckDate, 5, 1
from #EmployeePayDetail
where Section = 1
group by EmployeeID, Department, Position, EmployeeName, EmployeeSSN, CheckNumber, CheckDate

-- Get Section 6 - Tax Totals.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, [Description], TotalAmount, CheckDate, Section, Sort)
select EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, 'Total Taxes', sum(TaxAmount) [TotalAmount], CheckDate, 5, 2
from #EmployeePayDetail
where Section = 2
group by EmployeeID, Department, Position, EmployeeName, EmployeeSSN, CheckNumber, CheckDate

-- Get Section 6 - Benefits Totals.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, [Description], TotalAmount, CheckDate, Section, Sort)
select EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, 'Total Benefits', sum(BenefitAmount) [TotalAmount], CheckDate, 5, 3
from #EmployeePayDetail
where Section = 3
group by EmployeeID, Department, Position, EmployeeName, EmployeeSSN, CheckNumber, CheckDate

-- Get Section 6 - Benefits Totals.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, [Description], TotalAmount, CheckDate, Section, Sort)
select EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, 'Total Fees', sum(FeeAmount) [TotalAmount], CheckDate, 5, 4
from #EmployeePayDetail
where Section = 4
group by EmployeeID, Department, Position, EmployeeName, EmployeeSSN, CheckNumber, CheckDate

-- Get Section 6 - Credits Totals.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, Position, CheckNumber, Description, TotalAmount, CheckDate, Section, Sort)
select (select top 1 EmployeeID from  #EmployeePayDetail where Section=2 ), 
(select top 1 EmployeeName from  #EmployeePayDetail where Section=2 ), 
(select top 1 EmployeeSSN from  #EmployeePayDetail where Section=2 ), 
(select top 1 Department from  #EmployeePayDetail where Section=2 ), 
(select top 1 Position from  #EmployeePayDetail where Section=2 ), 
(select top 1 CheckNumber from  #EmployeePayDetail where Section=2 ), 
'Total Credits', 
sum(FeeTotalAmount) [TotalAmount], 
(select top 1 CheckDate from  #EmployeePayDetail where Section=4 ), 5, 4
from #EmployeePayDetail
where Section = 6
group by Department, Position, EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate



-- Get net pay, check and direct deposit amounts.
INSERT INTO #EmployeePayExtras(CompanyID, EmployeeID, PaymentAdjustmentNumber, NetPay, DirectDeposits, OtherDeposits)
select h.CompanyID, h.EmployeeID, h.PaymentAdjustmentNumber, h.NetWagesPayRun,
	   coalesce(sum(case when dd.DirectDepositBasedOnType = 1 then coalesce(dd.Deposits, 0.00) end), 0.00) [DirectDeposits],
	   coalesce(sum(case when dd.DirectDepositBasedOnType in (2,3) then coalesce(dd.Deposits, 0.00) end), 0.00) [OtherDeposits]
from EmployeeCheckHistory h
	INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = h.EmployeeID
	inner join (select h.CompanyID, h.EmployeeID, h.PaymentAdjustmentNumber, h.NetWagesPayRun,
							coalesce(dd.DirectDepositBasedOnType,1) [DirectDepositBasedOnType], coalesce(dd.ActualDeposit, 0.00) [Deposits]
				from EmployeeCheckHistory h
					INNER JOIN #EmployeeIDs e1 on e1.EmployeeID = h.EmployeeID
					left outer join EmployeeDirectDepositHistory dd on dd.CompanyID = h.CompanyID and dd.PaymentAdjustmentNumber = h.PaymentAdjustmentNumber
					left outer join InvoicePayrolls cip on cip.CompanyID = h.CompanyID and cip.AuditControlCode = h.AuditControlCode
					left outer join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
					inner join ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
		   and cdd.Department = h.Department
				where h.CompanyID = @company and cip.ClientID = @client and i.DarwinInvoiceNumber in (select Invoice from #MergeInvoices)
				  and (i.DivisionID = @division or @division is null) and h.Voided = CASE WHEN (@ShowVoidedChecks = 1) THEN h.Voided ELSE 0 END
			   ) dd on dd.CompanyID = h.CompanyID and dd.PaymentAdjustmentNumber = h.PaymentAdjustmentNumber
group by h.CompanyID, h.EmployeeID, h.PaymentAdjustmentNumber, h.NetWagesPayRun

select * from ( select pd.Id, pd.EmployeeID, EmployeeName, '***-**-' + SUBSTRING(EmployeeSSN, 6, 4) [EmployeeSSN], Department, Position, CheckNumber, CheckDate,
	   pd.PaymentAdjustmentNumber, [Description], isnull(Units,0) Units, isnull(Rate,0) Rate, isnull(EarnAmount,0)EarnAmount , isnull(TaxAmount,0)TaxAmount, isnull(DeductAmount,0)DeductAmount , isnull(BenefitAmount,0)BenefitAmount, isnull(FeeAmount,0) FeeAmount, isnull(TotalAmount,0) TotalAmount, isnull(ex.NetPay,0) NetPay,
	   isnull(ex.OtherDeposits,0) OtherDeposits, isnull(ex.DirectDeposits,0)DirectDeposits ,isnull( ex.NetPay - ex.DirectDeposits ,0)[CheckDeposits],  0 FeeTotalAmount,Section, Sort
from #EmployeePayDetail pd
	left outer join #EmployeePayExtras ex on ex.PaymentAdjustmentNumber = pd.PaymentAdjustmentNumber
UNION ALL

select 0,'test',NULL,NULL,NULL,NULL,NULL,NULL,0,FeeDesc,0,0,0,0,0,0,0,0,0,0,0,0,FeeTotalAmount,6,1  from #EmployeePayDetail
where Section = 6 ) data
where ((data.EmployeeID is not NULL)  )

SET ANSI_NULLS ON


drop table #EmployeePayDetail
drop table #EmployeePayExtras
drop table #Invoices
drop table #MergeInvoices
drop table #InvoiceFees
drop table #Fees
drop table #Departments
drop table #Divisions
drop table #EmployeeIDs
