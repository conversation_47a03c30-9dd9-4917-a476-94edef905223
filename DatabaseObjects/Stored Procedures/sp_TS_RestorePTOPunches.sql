-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: 05/10/2017
-- Description:	Restore PTO code, hours, rate for PTO daytype punches
-- =============================================
CREATE PROCEDURE [dbo].[sp_TS_RestorePTOPunches]
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	UPDATE EmployeeTimePunches
	SET   [PTOHours] = [WorkHours],
      [PTOCode] = (SELECT TOP 1 PTOCode FROM PTORequests WHERE id = [EmployeeTimePunches].PTORequestID),
      [PTORate] =(SELECT TOP 1 PayRateAmount FROM EmployeePaycodes WHERE CompanyID = [EmployeeTimePunches].CompanyID AND EmployeeID = [EmployeeTimePunches].EmployeeID AND PayRecord IN (SELECT TOP 1 PTOCode FROM PTORequests WHERE id = [EmployeeTimePunches].PTORequestID))
	WHERE (DayType = 4) and (PTOCode = '') and (PTORequestID > 0)
END
