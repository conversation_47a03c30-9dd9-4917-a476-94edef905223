/*
********************************************************************
**** Created by <PERSON>.
**** Date: 06/10/2015.
**** Purpose: Invoice Register Summary Reports data.
****			Payroll by employee.
**** 1/12/2015 : <PERSON> - added Employee Position
**** 6/28/2018 : <PERSON><PERSON><PERSON> - changed final query
********************************************************************
*/

CREATE PROCEDURE [dbo].[sp_RPT_InvoiceSummaryRegisterByPosition]	
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@division NVARCHAR(15) = NULL,
	@invoice int = NULL,
	@invoiceEnd int = NULL,
	@InvoiceDateBegin datetime = NULL,
	@InvoiceDateEnd datetime = NULL,
	@userid NVARCHAR(20)
)
AS
-- Build temp table to simplify the join.
CREATE TABLE #EmployeeStateTaxSummary(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[ClientID] [nvarchar](30) NULL,
	[DarwinInvoiceNumber] [int] NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[SIT] [decimal](19,5) NULL)



declare @start date
declare @end date

CREATE TABLE #FUTASUTA(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[AuditControlCode] [nvarchar](50) NULL,
	[DarwinInvoiceNumber] [int] NULL,  -- 02/21/2018 DS TFS # 3039
	[EmployeeID] [nvarchar](50) NULL,
	[FUTA] [decimal](19,5) NULL,
	[SUTA] [decimal](19,5) NULL,
	[WC] [decimal](19,5) NULL,
	)

CREATE TABLE #InvoiceFees(
	[Id] [int] IDENTITY(1,1) NOT NULL,	
	[DarwinInvoiceNumber] [int] NULL,
	[ChargeCode] [nvarchar](50) NULL,
	[TotalFUTAAmount] [decimal](19,5) NULL, 
	[TotalSUTAAmount] [decimal](19,5) NULL, 
	[TotalFICASSAmount] [decimal](19,5) NULL, 
	[TotalFICAMAmount] [decimal](19,5) NULL, 
	[TotalWorkersCompAmount] [decimal](19,5) NULL, 
	[TotalAdminFee] [decimal](19,5) NULL, 
	[TotalFlatFeeAmount] [decimal](19,5) NULL, 
	[TotalCharge] [decimal](19,5) NULL
	)

CREATE TABLE #Fees(
	[Id] [int] IDENTITY(1,1) NOT NULL,	
	[DarwinInvoiceNumber] [int] NULL,
	[EmployeeID] [nvarchar](15) NOT NULL,
	[ChargeCode] [nvarchar](50) NULL,
	[AdminFee] [decimal](19,5) NULL, 
	[FlatFeeAmount] [decimal](19,5) NULL,
	[Adjustment] [decimal](19,5) NULL 
	)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

CREATE TABLE #Invoices([Id] [int] IDENTITY(1,1) NOT NULL, [Invoice] [int] NOT NULL)
declare @iEnd int = NULL

if (@invoiceEnd is null) 
	select @invoiceEnd = @Invoice
if (@InvoiceDateEnd is null)
	select @InvoiceDateEnd = @InvoiceDateBegin

if (@invoice is not null)
	insert into #Invoices(Invoice) 	
	SELECT distinct DarwinInvoiceNumber from Invoices i
		INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	where DarwinInvoiceNumber between @invoice and @invoiceEnd and i.CompanyID = @company and i.ClientID = @client
else if (@InvoiceDateBegin is not null )
	insert into #Invoices(Invoice)	
	SELECT distinct DarwinInvoiceNumber from Invoices i
		INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	where [Date] between @InvoiceDateBegin and @InvoiceDateEnd and i.CompanyID = @company and i.ClientID = @client

Insert into #InvoiceFees (DarwinInvoiceNumber, ChargeCode, TotalFUTAAmount, TotalSUTAAmount, TotalFICASSAmount, TotalFICAMAmount, TotalWorkersCompAmount, TotalAdminFee, TotalFlatFeeAmount, TotalCharge)
SELECT     ic.DarwinInvoiceNumber, ic.ChargeType, icd.FUTA, icd.SUTA, icd.FICASS, icd.FICAM, icd.WC, icd.AdminFee, icd.FlatFeeAmount, ic.Total
FROM         dbo.InvoiceCharges AS ic INNER JOIN
                      dbo.Invoices AS i ON i.CompanyID = ic.CompanyID AND i.ClientID = ic.ClientID AND i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber LEFT OUTER JOIN
                          (SELECT     d.CompanyID, d.ClientID, d.DarwinInvoiceNumber, d.InvoiceNumber, d.ChargeType AS ChargeCode, SUM(ISNULL(d.FUTAAmount, 0)) AS FUTA, SUM(ISNULL(d.SUTAAmount, 0)) AS SUTA, SUM(ISNULL(d.FICASSAmount, 0)) AS FICASS, 
						  SUM(ISNULL(FICAMAmount, 0)) AS FICAM, SUM(ISNULL(d.WorkersCompAmount, 0)) AS WC, SUM(ISNULL(d.AdminFee, 0)) AS AdminFee, SUM(ISNULL(d.FlatFeeAmount, 0)) AS FlatFeeAmount
                            FROM          dbo.InvoiceEmployeeChargeDetails AS d 
                            GROUP BY d.CompanyID, ClientID, d.DarwinInvoiceNumber, d.InvoiceNumber, d.ChargeType) AS icd ON icd.CompanyID = ic.CompanyID AND icd.ClientID = ic.ClientID AND 
                      icd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber AND icd.ChargeCode = ic.ChargeType AND icd.InvoiceNumber = i.InvoiceNumber INNER JOIN
                      dbo.ChargeSetup AS s ON s.ChargeCode = ic.ChargeType AND s.CompanyID = ic.CompanyID
WHERE     ic.CompanyID = @company and ic.ClientID = @CLIENT and i.DarwinInvoiceNumber in (select Invoice from #Invoices) and (i.DivisionID = @division or @division IS NULL) AND (ic.ChargeType <> 'PAYROLL') AND (ic.Total <> 0) AND (icd.AdminFee <> 0) 

Insert into #Fees([DarwinInvoiceNumber], [EmployeeID], [ChargeCode], [AdminFee], [FlatFeeAmount], Adjustment)
SELECT   icd.DarwinInvoiceNumber, icd.EmployeeID, icd.ChargeType, SUM(ISNULL(icd.AdminFee, 0)), SUM(ISNULL(icd.FlatFeeAmount, 0)),
	case WHEN ISNULL(MAX(f.TotalAdminFee),0) <> 0 THEN case WHEN ISNULL(MAX(f.TotalCharge),0) <> 0 THEN (ISNULL(MAX(f.TotalCharge),0) - ISNULL(MAX(f.TotalFlatFeeAmount),0)) / ISNULL(MAX(f.TotalAdminFee),0) ELSE 1.00 END ELSE 1.00 END
FROM   dbo.InvoiceEmployeeChargeDetails AS icd LEFT OUTER JOIN
	   #InvoiceFees AS f ON icd.DarwinInvoiceNumber = f.DarwinInvoiceNumber AND icd.ChargeType = f.ChargeCode	
WHERE     (icd.ChargeType <> 'PAYROLL') AND (icd.CompanyID = @company) AND (icd.ClientID = @client) AND icd.DarwinInvoiceNumber in (select Invoice from #Invoices) AND icd.ChargeType IN (SELECT DISTINCT ChargeCode FROM ChargeSetup WHERE CompanyID = @company AND ChargeType <> 14)
GROUP BY icd.CompanyID, icd.ClientID, icd.DarwinInvoiceNumber, icd.InvoiceNumber, icd.EmployeeID, icd.ChargeType
 
Insert into #Fees([DarwinInvoiceNumber], [EmployeeID], [ChargeCode], [AdminFee], [FlatFeeAmount], Adjustment)
SELECT   icd.DarwinInvoiceNumber, icd.EmployeeID, icd.ChargeType, SUM(ISNULL(icd.AdminFee, 0)), SUM(ISNULL(icd.FlatFeeAmount, 0)),
	case WHEN ISNULL(MAX(f.TotalAdminFee),0) <> 0 THEN case WHEN ISNULL(MAX(f.TotalCharge),0) <> 0 THEN (ISNULL(MAX(f.TotalCharge),0) - ISNULL(MAX(f.TotalFUTAAmount),0) - ISNULL(MAX(f.TotalSUTAAmount),0) - ISNULL(MAX(f.TotalFICASSAmount),0) - ISNULL(MAX(f.TotalFICAMAmount),0) - ISNULL(MAX(f.TotalWorkersCompAmount),0) - ISNULL(MAX(f.TotalFlatFeeAmount),0)) / ISNULL(MAX(f.TotalAdminFee),0) ELSE 1.00 END ELSE 1.00 END
FROM   dbo.InvoiceEmployeeChargeDetails AS icd LEFT OUTER JOIN
	   #InvoiceFees AS f ON icd.DarwinInvoiceNumber = f.DarwinInvoiceNumber AND icd.ChargeType = f.ChargeCode	
WHERE     (icd.ChargeType <> 'PAYROLL') AND (icd.CompanyID = @company) AND (icd.ClientID = @client) AND icd.DarwinInvoiceNumber in (select Invoice from #Invoices) AND icd.ChargeType IN (SELECT DISTINCT ChargeCode FROM ChargeSetup WHERE CompanyID = @company AND ChargeType = 14)
GROUP BY icd.CompanyID, icd.ClientID, icd.DarwinInvoiceNumber, icd.InvoiceNumber, icd.EmployeeID, icd.ChargeType
 
-- Get FUTA, SUTA, WC values.
Insert into #FUTASUTA(AuditControlCode, EmployeeID, SUTA, FUTA, WC, DarwinInvoiceNumber)        
select '' as AuditControlCode, iecd.EmployeeID, sum(SUTAAmount) as [SUTA],  sum(FUTAAmount) as [FUTA], sum(WorkersCompAmount) as [WC], i.DarwinInvoiceNumber
from Invoices i
    left outer join InvoiceEmployeeDetails iecd on iecd.CompanyID = i.CompanyID and iecd.ClientID = i.ClientID and iecd.DarwinInvoiceNumber = i.DarwinInvoiceNumber 
    inner join ClientDivisions cdd on cdd.CompanyID = i.CompanyID AND cdd.ClientID = i.ClientID AND cdd.DivisionID = i.DivisionID --and cdd.Department = iecd.Department
    INNER JOIN #Departments d ON d.Department = iecd.Department
    INNER JOIN #EmployeeIDs e ON e.EmployeeID = iecd.EmployeeID
where    iecd.DarwinInvoiceNumber in (select Invoice from #Invoices) and (i.DivisionID = @division or @division IS NULL)
group by i.DarwinInvoiceNumber, i.InvoiceNumber, iecd.EmployeeID

INSERT INTO #EmployeeStateTaxSummary(CompanyId, ClientId, DarwinInvoiceNumber, EmployeeID, SIT)
select	tblPivot.CompanyID, tblPivot.ClientId, tblPivot.DarwinInvoiceNumber, tblPivot.EmployeeID, 
		sum(tblPivot.Value) [SIT]
FROM (SELECT e.CompanyID, e.ClientID, e.DarwinInvoiceNumber, e.EmployeeID, 
			StateTaxWithholdingArray1 as Array1, StateTaxWithholdingArray2 as Array2, 
			StateTaxWithholdingArray3 as Array3, StateTaxWithholdingArray4 as Array4, 
			StateTaxWithholdingArray5 as Array5, StateTaxWithholdingArray6 as Array6, 
			StateTaxWithholdingArray7 as Array7, StateTaxWithholdingArray8 as Array8, 
			StateTaxWithholdingArray9 as Array9, StateTaxWithholdingArray10 as Array10, 
			StateTaxWithholdingArray11 as Array11, StateTaxWithholdingArray12 as Array12,
			StateTaxWithholdingArray13 as Array13, StateTaxWithholdingArray14 as Array14,
			StateTaxWithholdingArray15 as Array15, StateTaxWithholdingArray16 as Array16,
			StateTaxWithholdingArray17 as Array17, StateTaxWithholdingArray18 as Array18,
			StateTaxWithholdingArray19 as Array19, StateTaxWithholdingArray20 as Array20,
			StateTaxWithholdingArray21 as Array21, StateTaxWithholdingArray22 as Array22,
			StateTaxWithholdingArray23 as Array23, StateTaxWithholdingArray24 as Array24,
			StateTaxWithholdingArray25 as Array25
	  FROM EmployeeInvoiceCheckHistory e
		INNER JOIN #Departments d ON d.Department = e.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (Array1, Array2, Array3, Array4, Array5, Array6, Array7, Array8, Array9, Array10, Array11, Array12, Array13, Array14,
								Array15, Array16, Array17, Array18, Array19, Array20, Array21, Array22, Array23, Array24, Array25)) as tblPivot
WHERE CompanyID = @company and ClientID = @client and DarwinInvoiceNumber in (select Invoice from #Invoices)
GROUP BY CompanyID, ClientID, DarwinInvoiceNumber, EmployeeID

-- Build temp table to simplify the join.
CREATE TABLE #EmployeeLocalTaxSummary(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[ClientID] [nvarchar](30) NULL,
	[DarwinInvoiceNumber] [int] NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[LIT] [decimal](19,5) NULL)

INSERT INTO #EmployeeLocalTaxSummary(CompanyId, ClientId, DarwinInvoiceNumber, EmployeeID, LIT)
select	tblPivot.CompanyID, tblPivot.ClientId, tblPivot.DarwinInvoiceNumber, tblPivot.EmployeeID, 
		sum(tblPivot.Value) [LIT]
FROM (SELECT e.CompanyID, e.ClientID, e.DarwinInvoiceNumber, e.EmployeeID, 
			LocalTaxWithholdingArray1 as Array1, LocalTaxWithholdingArray2 as Array2, 
			LocalTaxWithholdingArray3 as Array3, LocalTaxWithholdingArray4 as Array4, 
			LocalTaxWithholdingArray5 as Array5, LocalTaxWithholdingArray6 as Array6, 
			LocalTaxWithholdingArray7 as Array7, LocalTaxWithholdingArray8 as Array8, 
			LocalTaxWithholdingArray9 as Array9, LocalTaxWithholdingArray10 as Array10, 
			LocalTaxWithholdingArray11 as Array11, LocalTaxWithholdingArray12 as Array12,
			LocalTaxWithholdingArray13 as Array13, LocalTaxWithholdingArray14 as Array14,
			LocalTaxWithholdingArray15 as Array15, LocalTaxWithholdingArray16 as Array16,
			LocalTaxWithholdingArray17 as Array17, LocalTaxWithholdingArray18 as Array18,
			LocalTaxWithholdingArray19 as Array19, LocalTaxWithholdingArray20 as Array20,
			LocalTaxWithholdingArray21 as Array21, LocalTaxWithholdingArray22 as Array22,
			LocalTaxWithholdingArray23 as Array23, LocalTaxWithholdingArray24 as Array24,
			LocalTaxWithholdingArray25 as Array25
	  FROM EmployeeInvoiceCheckHistory e
		INNER JOIN #Departments d ON d.Department = e.Department
		INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (Array1, Array2, Array3, Array4, Array5, Array6, Array7, Array8, Array9, Array10, Array11, Array12, Array13, Array14,
								Array15, Array16, Array17, Array18, Array19, Array20, Array21, Array22, Array23, Array24, Array25)) as tblPivot
WHERE CompanyID = @company and ClientID = @client and DarwinInvoiceNumber in (select Invoice from #Invoices)
GROUP BY CompanyID, ClientID, DarwinInvoiceNumber, EmployeeID

select i.CompanyID, i.ClientID, i.DarwinInvoiceNumber, 0 [PaymentAdjustmentNumber], 0 [CheckNumber], NULL [CheckDate], ied.EmployeeID,
	   '***-**-' + right(rtrim(ltrim(REPLACE(e.SSN, NCHAR(0x00A0), ''))) , 4) [SSN], ISNULL(e.FirstName,'') + ' ' + left(ISNULL(e.MiddleName,''), 1) + ' ' + ISNULL(e.LastName,'') [Name],
	   ied.Position, ied.RegularHours / 100.0 [CalculatedRegularHours], ied.RegularEarnings [CalculatedRegularEarnings],
	   ied.OTHours / 100.0 [OTHours], ied.OTEarnings [OTEarnings], ied.Total  [CalculatedGrossWages], Sum(ied.FICASSAmount) [CalculatedFICASS],
	   Sum(ied.FICAMAmount) [CalculatedFICAMed], 0 [TotalDeductions], ied.BenefitAmount [TotalBenefits] , 0 [NetWagesPayRun], st.SIT, loc.LIT, ied.NonGrossAmount [BusinessExpenseEarnings],
	   wc.FUTA, wc.SUTA, wc.WC, '' as AuditControlCode, ied.AgencyCredits [Credits], coalesce(ied.AdminFee, 0.00) [Fees]
from Invoices i
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join (
		select CompanyID, InvoiceNumber, EmployeeID, Position, ClientID, DarwinInvoiceNumber, sum(RegularHours) [RegularHours], sum(RegularEarnings) [RegularEarnings], sum(OTHours) [OTHours], sum(OTEarnings) [OTEarnings], Sum(Total) [Total],
			Sum(FICASSAmount) [FICASSAmount], Sum(FICAMAmount) [FICAMAmount], Sum(BenefitAmount) [BenefitAmount], Sum(NonGrossAmount) [NonGrossAmount], Sum(AgencyCredits) [AgencyCredits], Sum(AdminFee + FlatFeeAmount) [AdminFee]
		from InvoiceEmployeeDetails 
		group by CompanyID, InvoiceNumber, EmployeeID, Position, ClientID, DarwinInvoiceNumber
	) ied on ied.CompanyID = i.CompanyID and ied.ClientID = i.ClientID
				and ied.DarwinInvoiceNumber = i.DarwinInvoiceNumber 
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ied.EmployeeID
	left outer join Employees e on e.CompanyID = ied.CompanyID and e.EmployeeID = ied.EmployeeID
	left outer join #FUTASUTA wc on wc.EmployeeID = ied.EmployeeID and wc.DarwinInvoiceNumber = i.DarwinInvoiceNumber 
	inner join #EmployeeLocalTaxSummary loc on loc.CompanyID = ied.CompanyID and loc.ClientID = ied.ClientID
		   and loc.DarwinInvoiceNumber = ied.DarwinInvoiceNumber and loc.EmployeeID = ied.EmployeeID
	inner join #EmployeeStateTaxSummary st on st.CompanyID = ied.CompanyID and st.ClientID = ied.ClientID
		   and st.DarwinInvoiceNumber = ied.DarwinInvoiceNumber and st.EmployeeID = ied.EmployeeID
where i.CompanyID = @company and i.ClientID = @client and  (i.DivisionID = @division or @division IS NULL)
  and i.DarwinInvoiceNumber in (select Invoice from #Invoices) 
GROUP BY i.CompanyID, i.ClientID, i.DarwinInvoiceNumber, ied.OTHours, ied.OTEarnings,
		 ied.NonGrossAmount, ied.EmployeeID, e.SSN, ied.Position, ied.BenefitAmount, ied.NonGrossAmount, 
		 e.FirstName, e.MiddleName, e.LastName, ied.RegularHours, ied.RegularEarnings, st.SIT, loc.LIT, wc.FUTA, wc.SUTA, wc.WC, ied.Total, ied.AgencyCredits, ied.AdminFee


DROP TABLE #Departments
DROP TABLE #Divisions
DROP TABLE #EmployeeIDs
DROP TABLE #EmployeeLocalTaxSummary
DROP TABLE #EmployeeStateTaxSummary
DROP TABLE #Fees
DROP TABLE #FUTASUTA
DROP TABLE #InvoiceFees
DROP TABLE #Invoices

