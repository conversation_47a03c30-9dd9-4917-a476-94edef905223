-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Add custom task from the current setup to other client setup
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBClientSetupTask_Assign]
	@SetupID as int,
	@TaskID as int,
	@DestSetupID as int
AS
BEGIN
	DECLARE @ErrNum as int = 0
	
	SET NOCOUNT ON;
	INSERT INTO OBClientSetupDocumentAssignments (SetupID, DocumentID, CodeType, CodeID)
		SELECT  @DestSetupID AS SetupID, DocumentID, CodeType, CodeID
		FROM  OBClientSetupDocumentAssignments AS OBClientSetupDocumentAssignments_1 WHERE SetupID = @SetupID AND DocumentID IN (SELECT DocumentID  FROM OBClientSetupDocuments WHERE SetupID = @SetupID AND TaskID = @TaskID) AND DocumentID NOT IN (SELECT DocumentID  FROM OBClientSetupDocuments WHERE SetupID = @DestSetupID)
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 16
		
	INSERT INTO OBClientSetupDocumentMapping (SetupID, DocumentID, FormName, FormFieldType, DB_Table, DB_Field, DB_RecNum, DB_Value, FieldPresentation, FieldStatus, UseInWF, WF_SeqNbr, WF_FieldLabel, WF_FieldType, WF_FieldOptions)
		SELECT  @DestSetupID AS SetupID, DocumentID, FormName, FormFieldType, DB_Table, DB_Field, DB_RecNum, DB_Value, FieldPresentation, FieldStatus, UseInWF, WF_SeqNbr, WF_FieldLabel, WF_FieldType, WF_FieldOptions
		FROM  OBClientSetupDocumentMapping AS OBClientSetupDocumentMapping_1 WHERE SetupID = @SetupID AND DocumentID IN (SELECT DocumentID  FROM OBClientSetupDocuments WHERE SetupID = @SetupID AND TaskID = @TaskID) AND DocumentID NOT IN (SELECT DocumentID  FROM OBClientSetupDocuments WHERE SetupID = @DestSetupID)
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 8
		
	INSERT INTO OBClientSetupDocuments   (SetupID, TaskID, DocumentID, DocumentName, Verification, ManualUpdate, Locked, CLock, VerificationAgreement, UseAssignments)
		SELECT @DestSetupID AS SetupID, TaskID, DocumentID, DocumentName, Verification, ManualUpdate, Locked, CLock, VerificationAgreement, UseAssignments
		FROM OBClientSetupDocuments AS OBClientSetupDocuments_1 WHERE SetupID = @SetupID AND TaskID = @TaskID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 4
		
	INSERT INTO OBClientSetupTaskFields   (SetupID, TaskID, FName, SeqNbr, FLabel, FType, FSize, FValueOptions, PermanentTBL, PermanentFLD, RelatedTask, RelatedFLD, FRequired, FLocked, CCAccess, EEAccess, FTip)
		SELECT @DestSetupID AS SetupID, TaskID, FName, SeqNbr, FLabel, FType, FSize, FValueOptions, PermanentTBL, PermanentFLD, RelatedTask, RelatedFLD, FRequired, FLocked, CCAccess, EEAccess, FTip
		FROM  OBClientSetupTaskFields AS OBClientSetupTaskFields_1 WHERE SetupID = @SetupID AND TaskID = @TaskID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 2
		
	INSERT INTO OBClientSetupTasks  (SetupID, TaskID, TaskName, TaskType, TaskOrder, TaskAssignment, TaskRequired, TaskLocked, CCInstruction, EEInstruction, InstructionDoc, TipText, TipImage, FormCols, DocumentsUpdated, InstructionsUpdated, FieldsUpdated, TipsUpdated, CodeSet)
		SELECT @DestSetupID AS SetupID, TaskID, TaskName, TaskType, TaskOrder, TaskAssignment, TaskRequired, TaskLocked, CCInstruction, EEInstruction, InstructionDoc, TipText, TipImage, FormCols, DocumentsUpdated, InstructionsUpdated, FieldsUpdated, TipsUpdated, CodeSet
		FROM OBClientSetupTasks AS OBClientSetupTasks_1 WHERE SetupID = @SetupID AND TaskID = @TaskID
	IF @@ERROR <> 0
		SET @ErrNum = @ErrNum + 1

	UPDATE OBClientSetupTaskFields
	SET CCAccess = 5
	WHERE TaskID = 10 AND FName = 'EmployeeClass' AND CCAccess > 5

	UPDATE OBClientSetupTaskFields
	SET EEAccess = 5
	WHERE TaskID = 10 AND FName = 'EmployeeClass' AND EEAccess > 5

END
