-- =============================================
-- Author:		<Dhara Gohel>
-- Create date: <11/20/2020>
-- Description:	<returns the reslt for the PayrollCalculate report>
-- =============================================
CREATE PROCEDURE [dbo].[Sp_RPT_PayrollCalculate]
	
	@company INT,
	@client VARCHAR(15) = NULL,
	@PayrollNo nvarchar(max) = NULL,
	@userid NVARCHAR(20)
	

AS
BEGIN

CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[CheckNumber] [varchar](50) NULL,
	[CheckDate] [date] NULL,
	--Earnings
	--[PayCode] [nvarchar](50) NULL,
	[Dept] [nvarchar](50) NULL,
	[DivisionID] [nvarchar](50) NULL,
	[Position] [nvarchar](50) NULL,
	[Hours]  [decimal](19,5) NULL,
	[PayRate]  [decimal](19,5) NULL,
	[EarningAmount] [decimal](19,5) NULL,
    [PayType] [tinyint] NULL,
	--Tax
	[Description] [nvarchar](30) NULL,
	[TaxAmount] [decimal](19,5) NULL,
	--Deduction
	[DeductAmount] [decimal](19,5) NULL,
	--Benefit
	[BenefitAmount] [decimal](19,5) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)

CREATE TABLE #EmployeePayExtras(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[CheckNumber] [varchar](50) NULL,
	[CheckDate] [date] NULL,
	[GrossPay] [decimal](19,5) NULL,
	[NetPay] [decimal](19,5) NULL,
	[DirectDeposits] [decimal](19,5) NULL,
	[OtherDeposits] [decimal](19,5) NULL )

CREATE TABLE #AdditionalReportInfo(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [varchar](50) NULL,
	[CheckNumber] [varchar](50) NULL,
	[CheckDate] [date] NULL,
	[PPBegin] [datetime],
	[PPEnd] [datetime],
	[BuildNo] [varchar](50) NULL
	 )


-- * * * * *      S E C T I O N : Additional    * * * * * * * 
insert into #AdditionalReportInfo(EmployeeID, CheckNumber, CheckDate, PPBegin, PPEnd, BuildNo)
select  pwh.EmployeeID, pwh.CheckNumber, pwh.CheckDate, cast(PayPeriod_BeginDate as date) as PayPeriod_BeginDate ,
cast(PayPeriod_EndDate as date) as PayPeriod_EndDate, c.ProfileID as BuildNo
from ClientPayrollSchedules c
left outer join PayrollWorkHeaders pwh on pwh.CompanyID = c.CompanyID and pwh.PayrollNumber = c.PayrollNumber
where c.CompanyID = @company  and c.ClientID = @client and pwh.IsRemoved = 0 and pwh.PayrollNumber = @PayrollNo


-- * * * * *      S E C T I O N  1 :  Earnings     * * * * * * * 
-- Earnings by Payroll Type
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, Hours, PayRate, EarningAmount, Description,Section, Sort,PayType)
select pwpc.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwh.CheckNumber, pwh.CheckDate,
	pwpc.Department, pwpc.JobTitle, sum(pwpc.UnitsToPay), pwpc.PayRateAmount, sum(pwpc.TotalPay), pwpc.PayRecord, Section = 1, Sort = 1, pwpc.PayType
from PayrollWorkPayCodes pwpc
inner join Employees e on e.EmployeeID = pwpc.EmployeeID and e.CompanyID = pwpc.CompanyID
left outer join PayrollWorkHeaders pwh on pwh.CompanyID = pwpc.CompanyID and pwh.PayrollNumber = pwpc.PayrollNumber and pwpc.EmployeeID = pwh.EmployeeID 
where pwpc.CompanyID = @company and pwh.IsRemoved = 0 and e.ClientID = @client and pwh.PayrollNumber = @PayrollNo
group by pwpc.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, pwh.CheckNumber, 
pwpc.PayRecord, pwpc.Department, pwpc.JobTitle, pwh.CheckDate, pwpc.PayRateAmount, pwpc.PayType
order by pwpc.EmployeeID

-- * * * * *      S E C T I O N  2 :  Taxes     * * * * * * * 

-- Fedaral Tax
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, [Description], TaxAmount, Section, Sort)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, h.CheckNumber,
h.CheckDate,e.Department,e.Position,
'Federal', (h.FederalWithholdingPayRun + h.FederalTaxOnTips) as FederalWithholdingPayRun, Section = 2, Sort = 2
from PayrollWorkHeaders h
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID and h.PayrollNumber = @PayrollNo
where h.CompanyID = @company and h.IsRemoved = 0 and e.ClientID = @client

-- State Tax values.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, [Description], TaxAmount, Section, Sort)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwh.CheckNumber,
pwh.CheckDate,h.Department,h.JobTitle,
h.StateCode, [State] =  isnull(sum(h.TotalStateTax + h.StateTaxOnTips),0), Section = 2, Sort = 3
from PayrollWorkStateTaxes h
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID
left outer join PayrollWorkHeaders pwh on h.CompanyID = pwh.CompanyID and h.PayrollNumber = pwh.PayrollNumber and h.EmployeeID = pwh.EmployeeID
where h.CompanyID = @company and h.ClientID = @client and pwh.IsRemoved = 0 and pwh.PayrollNumber = @PayrollNo 
group by h.EmployeeID, (e.LastName + ', ' + e.FirstName), e.SSN, pwh.CheckNumber,
		pwh.CheckDate, h.StateCode, h.Department, h.JobTitle
order by h.EmployeeID

--Local
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, [Description], TaxAmount, Section, Sort)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwh.CheckNumber,
pwh.CheckDate, h.Department, h.JobTitle,
h.LocalTax, [Local] =  isnull(sum(h.TotalLocalTax + h.LocalTaxOnTips),0), Section= 2, Sort = 4
from PayrollWorkLocalTaxes h
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID
left outer join PayrollWorkHeaders pwh on h.CompanyID = pwh.CompanyID and
h.PayrollNumber = pwh.PayrollNumber and h.EmployeeID = pwh.EmployeeID
where h.CompanyID = @company and h.ClientID = @client and pwh.IsRemoved = 0 and pwh.PayrollNumber = @PayrollNo
group by h.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, pwh.CheckNumber,
		pwh.CheckDate,	h.LocalTax,	h.Department, h.JobTitle
order by h.EmployeeID

-- FICA SS Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, [Description], TaxAmount, Section, Sort)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, h.CheckNumber,
h.CheckDate, e.Department, e.Position,
'FICA SS', (h.FICASocialSecurityWithholdingPayRun + h.FICASSTaxOnTips) as FederalWithholdingPayRun, Section= 2, Sort = 1
from PayrollWorkHeaders h
left outer join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID
where h.CompanyID = @company and h.IsRemoved = 0 and e.ClientID = @client and h.PayrollNumber = @PayrollNo

-- FICA Med Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, [Description], TaxAmount, Section, Sort)
select h.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, h.CheckNumber, h.CheckDate, e.Department, e.Position,
'FICA Med', (h.FICAMedicareWithholdingPayRun + H.FICAMedTaxOnTips) as FederalWithholdingPayRun ,Section= 2, Sort = 1
from PayrollWorkHeaders h
left outer join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID
where h.CompanyID = @company and h.IsRemoved = 0 and e.ClientID = @client and h.PayrollNumber = @PayrollNo

-- * * * * *      S E C T I O N  3 :  Deductions     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, [Description], DeductAmount, Section, Sort)
select pwd.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwh.CheckNumber,
pwh.CheckDate, pwd.Department, pwd.JobTitle, pwd.Deduction, sum(isnull(pwd.TotalDeduction,0)), 3, 1
from PayrollWorkDeductions pwd
inner join Employees e on e.EmployeeID = pwd.EmployeeID and e.CompanyID = pwd.CompanyID
left outer join PayrollWorkHeaders pwh on pwh.CompanyID = pwd.CompanyID and pwh.PayrollNumber = pwd.PayrollNumber and
pwh.EmployeeID = pwd.EmployeeID 
where pwd.CompanyID = @company  and pwh.IsRemoved = 0 and e.ClientID = @client and pwh.PayrollNumber = @PayrollNo
group by pwd.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, pwh.CheckNumber, pwd.Department,
	pwd.Deduction, pwh.CheckDate, pwd.Department, pwd.JobTitle
order by pwd.EmployeeID


-- * * * * *      S E C T I O N  4 :  Benefits     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept, Position, [Description], BenefitAmount, Section, Sort)
select pwd.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), EmployeeSSN = e.SSN, pwh.CheckNumber,
pwh.CheckDate, pwd.Department, pwd.JobTitle, pwd.Benefit,sum(Isnull(pwd.TotalBenefit,0)), 4, 1
from PayrollWorkBenefits pwd
inner join Employees e on e.EmployeeID = pwd.EmployeeID and e.CompanyID = pwd.CompanyID
left outer join PayrollWorkHeaders pwh on pwh.CompanyID = pwd.CompanyID and pwh.PayrollNumber = pwd.PayrollNumber and
pwh.EmployeeID = pwd.EmployeeID 
where pwd.CompanyID = @company and pwh.IsRemoved = 0 and e.ClientID = @client and pwh.PayrollNumber = @PayrollNo
group by pwd.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, pwh.CheckNumber, pwd.Department,
pwd.Benefit, pwh.CheckDate, pwd.Department, pwd.JobTitle
order by pwd.EmployeeID


insert into #EmployeePayExtras(EmployeeID, CheckNumber, CheckDate, NetPay, GrossPay, DirectDeposits, OtherDeposits)
select h.EmployeeID, h.CheckNumber, h.CheckDate, h.NetWagesPayRun, (h.GrossWagesPayRun + h.ReportedTips + h.ChargedTips ) as GrossPay, IsNull(d.DirectDeposit1,0) DirectDeposit1,
	case when h.NetWagesPayRun > IsNull(d.DirectDeposit1,0) then h.NetWagesPayRun - IsNull(d.DirectDeposit1,0) else cast(0 as decimal(19,5)) end as CheckAmount
from PayrollWorkHeaders h
left outer join PayrollWorkMasters m on m.CompanyID = h.CompanyID and m.PayrollNumber = h.PayrollNumber
left outer join (select dd.CompanyID, dd.EmployeeID, dd.PaymentAdjustmentNumber, dd.PayrollNumber, sum(isNull(dd.DirectDepositAmount,0)) as DirectDeposit1
				 from	PayrollWorkDirectDeposits dd
				 group by dd.CompanyID, dd.EmployeeID, dd.PaymentAdjustmentNumber, dd.PayrollNumber) d 
			on d.CompanyID = h.CompanyID and d.EmployeeID = h.EmployeeID and d.PaymentAdjustmentNumber = h.PaymentAdjustmentNumber and d.PayrollNumber = h.PayrollNumber
inner join Employees e on e.EmployeeID = h.EmployeeID and e.CompanyID = h.CompanyID
where h.PayrollNumber = @PayrollNo and e.ClientID = @client and h.CompanyID = @company 


-- * * * * *     O U T P U T     * * * * * * * 
select pd.Id, pd.EmployeeID, pd.EmployeeName, '***-**-' + SUBSTRING(pd.EmployeeSSN, 6, 4) [EmployeeSSN],
 rtrim(ltrim(REPLACE(pd.CheckNumber, NCHAR(0x00A0), ''))) AS CheckNumber,pd.CheckDate
,pd.Dept,pd.Position,pd.Hours,pd.PayRate,pd.EarningAmount,
 [Description],TaxAmount,DeductAmount,BenefitAmount,
 ex.GrossPay,ex.NetPay,ex.DirectDeposits,ex.[OtherDeposits] as CashDeposit,
 r.PPBegin,r.PPEnd,r.BuildNo,
 pd.Section, pd.Sort,PayType
from #EmployeePayDetail pd
left outer join #EmployeePayExtras ex on ex.EmployeeID = pd.EmployeeID and ex.CheckNumber = pd.CheckNumber and ex.CheckDate = pd.CheckDate
left outer join #AdditionalReportInfo r on r.EmployeeID = pd.EmployeeID and r.CheckNumber = pd.CheckNumber and r.CheckDate = pd.CheckDate
and pd.EmployeeID = ex.EmployeeID
order by pd.EmployeeID, pd.CheckNumber, Section, Sort, CheckDate, [Description]



OPTION (RECOMPILE)
DROP TABLE #EmployeePayExtras
DROP TABLE #EmployeePayDetail
DROP TABLE #AdditionalReportInfo

END
