/*
********************************************************************
**** 05/14/24 CH-856 SDJ - updated for charge sequencing
********************************************************************
*/

CREATE PROCEDURE [dbo].[sp_RPT_ComponentNames]  
(
	@company INT,
	@client VARCHAR(15),
	@invoice int,
	@userid NVARCHAR(20)
)
AS
BEGIN
CREATE table #ComponentName (ClientID nvarchar(max) null,
com1 nvarchar(max) null , 
com2 nvarchar(max) null ,
com3 nvarchar(max) null ,
com4 nvarchar(max) null ,
com5 nvarchar(max) null ,
com6 nvarchar(max) null ,
com7 nvarchar(max) null ,
com8 nvarchar(max) null ,
com9 nvarchar(max) null )

CREATE table #ComponentTemp (ClientID nvarchar(100) null,
ChargeDescription nvarchar(200) null ,
SequenceNumber int,
ReportID int)

/* Get PAYROLL details*/
Insert into #ComponentTemp (ClientID, ChargeDescription, SequenceNumber, ReportID)
Select Distinct ipd.ClientID, 
	CASE
		WHEN ipd.ReportID <> 0 THEN
			LTRIM(RTRIM(ipd.ReportLabel))
		ELSE
			ipd.ChargeDescription
	END as ChargeDescription,
	CASE
		WHEN icd.SequenceNumber <> 0 and icd.SequenceNumber < 2147483647 THEN
			icd.SequenceNumber
	END as SequenceNumber, ipd.ReportID
from InvoicePayrollDetails ipd
Left outer join InvoiceChargeDetails icd on icd.CompanyID = ipd.CompanyID and icd.ClientID = ipd.ClientID and icd.DarwinInvoiceNumber = ipd.DarwinInvoiceNumber and
	icd.ChargeType = ipd.ChargeType 
where ipd.CompanyID = @company and ipd.ClientID = @client and ipd.DarwinInvoiceNumber = @invoice and icd.SelectForPrint = 1 and
	ipd.ChargeType = 'PAYROLL' AND ipd.ComponentType <> 'NON-GROSS BUS EXP' AND ((icd.ComponentCode = ipd.ComponentType) or (ipd.ComponentType = 'GROSS WAGES' and icd.ComponentCode = 'GROSS PAYROLL'))

/* Update Sequence */
UPDATE #ComponentTemp
SET SequenceNumber = 0
WHERE SequenceNumber IS NULL and ChargeDescription = 'GROSS WAGES'

UPDATE #ComponentTemp
SET SequenceNumber = ReportID
WHERE SequenceNumber IS NULL and ReportID <> 0

;WITH cte  
     AS(SELECT *,ROW_NUMBER() OVER(ORDER BY SequenceNumber) rr FROM #ComponentTemp WHERE SequenceNumber IS NULL)  
      
     UPDATE c  
     SET SequenceNumber = (SELECT MAX(SequenceNumber) FROM #ComponentTemp) + rr  
     FROM cte c 

/* Get other charges details*/
Insert into #ComponentTemp (ClientID, ChargeDescription, SequenceNumber, ReportID)
Select Distinct ipd.ClientID, LTRIM(RTRIM(ipd.ReportLabel)),
	CASE
		WHEN ic.SequenceNumber <> 0 and ic.SequenceNumber < 2147483647 THEN
			ic.SequenceNumber
	END as SequenceNumber, ipd.ReportID
from InvoicePayrollDetails ipd
Left outer join InvoiceCharges ic on ic.CompanyID = ipd.CompanyID and ic.ClientID = ipd.ClientID and ic.DarwinInvoiceNumber = ipd.DarwinInvoiceNumber and
	ic.ChargeType = ipd.ChargeType 
where ipd.CompanyID = @company and ipd.ClientID = @client and ipd.DarwinInvoiceNumber = @invoice and
	ipd.ChargeType <> 'PAYROLL' and ipd.ReportID <> 0
GROUP BY ipd.ClientID, ipd.ReportID, ipd.ReportLabel, ipd.ChargeDescription, ic.SequenceNumber

/* Update Sequence */
;WITH cte  
     AS(SELECT *,ROW_NUMBER() OVER(ORDER BY SequenceNumber) rr FROM #ComponentTemp WHERE SequenceNumber IS NULL)  
      
     UPDATE c  
     SET SequenceNumber = (SELECT MAX(SequenceNumber) FROM #ComponentTemp) + rr  
     FROM cte c 

/* Create final temp table */
IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	INSERT INTO #ComponentName (ClientID, com1) 
	SELECT TOP 1 ClientID, ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com2 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com3 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com4 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com5 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com6 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com7 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com8 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

IF EXISTS (SELECT TOP 1 * FROM #ComponentTemp)
BEGIN
	UPDATE #ComponentName SET com9 = 
	(SELECT TOP 1 ChargeDescription FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription)

	DELETE t FROM
		(SELECT TOP 1 * FROM #ComponentTemp ORDER BY SequenceNumber, ChargeDescription) t
END

Select *
from #ComponentName

DROP TABLE #ComponentName
DROP TABLE #ComponentTemp

END