
CREATE PROCEDURE [dbo].[Sp_ExpSol_LD_Taxes_FutaSutaWC]
	-- Add the parameters for the stored procedure here
	@company INT=0,
	@client VARCHAR(15) = NULL,
	@Invoice VARCHAR(1000)= null
AS
BEGIN
	SELECT     ech.CompanyID, eich.ClientID, ech.EmployeeID, cdd.DivisionID, ech.Department, ech.Position, ech.CheckDate, eich.DarwinInvoiceNumber AS Invoice, eich.Voided, CASE WHEN PayrollRecordType = 1 THEN 'SUTA' WHEN PayrollRecordType = 2 THEN 'FUTA' ELSE 'WC' END AS [Tax_Type], 
                  SUM(ISNULL(ech.FUTABilling, 0)) AS Amount, ISNULL(ech.PayrollCode, N'') AS [Tax_Code]
FROM        dbo.EmployeeFutaSutaWorkersCompHistory AS ech INNER JOIN
                  dbo.EmployeeCheckHistory AS ech1 ON ech1.CompanyID = ech.CompanyID AND ech1.EmployeeID = ech.EmployeeID AND ech1.AuditControlCode = ech.AuditControlCode INNER JOIN
                  dbo.Employees AS e ON e.CompanyID = ech.CompanyID AND e.EmployeeID = ech.EmployeeID INNER JOIN
                  dbo.EmployeeInvoiceCheckHistory AS eich ON eich.CompanyID = ech.CompanyID AND eich.EmployeeID = ech.EmployeeID AND eich.PaymentAdjustmentNumber = ech1.PaymentAdjustmentNumber INNER JOIN
                  dbo.Invoices AS i ON i.CompanyID = ech.CompanyID AND i.ClientID = eich.ClientID AND i.DarwinInvoiceNumber = eich.DarwinInvoiceNumber INNER JOIN
                  dbo.ClientDivisionDetails AS cdd ON cdd.CompanyID = ech.CompanyID AND cdd.ClientID = eich.ClientID AND cdd.DivisionID = i.DivisionID AND cdd.Department = ech.Department
WHERE     (ech.TotalPay <> 0) AND 
(ech.CompanyID = @company) AND (eich.ClientID =@client) 
					   AND (eich.DarwinInvoiceNumber in (SELECT CAST(Item AS INTEGER)
        FROM [dbo].[SplitString](@Invoice, ',')))
GROUP BY ech.CompanyID, eich.ClientID, ech.EmployeeID, cdd.DivisionID, ech.Department, ech.Position, ech.CheckDate, eich.DarwinInvoiceNumber, eich.Voided, ech.PayrollRecordType, ech.PayrollCode
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	
END
