/*
*************************************************************************
**** Purpose: Generates data for the SSRS report Payroll Register Detail.
**** 06/03/2015 | <PERSON> | Initial design.
**** 09/14/2015	| <PERSON>		| re-factored to use check dates instead 
****							| of invoices and apportionments to 
****							| deductions.
****
*************************************************************************
*/

-- sp_RPT_PayrollDetailRegisterByEEDepartment 1, '001', NULL, '1/1/14', '12/31/14'
-- sp_RPT_PayrollDetailRegisterByEEDepartment 1, '001', '001003', '1/1/14', '12/31/14'

CREATE PROCEDURE [dbo].[sp_RPT_PayrollDetailRegisterByEEDepartment]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@department VARCHAR(15) = NULL,
	@startdate datetime,
	@enddate datetime,
	@userid NVARCHAR(20)
)
AS	
--DECLARE @company INT = 7, @client VARCHAR(15) = 'R10', @department VARCHAR(15) = NULL, @startdate datetime = '10/7/16', @enddate datetime = '10/7/16', @userid NVARCHAR(20) = 'ReachoutALL'

CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[Department] [nvarchar](50) NULL,
	[DepartmentName] [nvarchar](50) NULL,
	[CheckNumber] [nvarchar](50) NULL,
	[PaymentAdjustmentNumber] [int] NULL,
	[CheckDate] [date] NULL,
	[Description] [nvarchar](30) NULL,
	[Units] [decimal](19,5) NULL,
	[Rate] [decimal](19,5) NULL,
	[EarnAmount] [decimal](19,5) NULL,
	[TaxAmount] [decimal](19,5) NULL,
	[DeductAmount] [decimal](19,5) NULL,
	[BenefitAmount] [decimal](19,5) NULL,
	[TotalAmount] [decimal](19,5) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)

CREATE TABLE #EmployeePayExtras(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[Department] [nvarchar](50) NULL,
	[DepartmentName] [nvarchar](50) NULL,
	[CheckNumber] [nvarchar](50) NULL,
	[PaymentAdjustmentNumber] [int] NULL,
	[CheckDate] [date] NULL,
	[NetPay] [decimal](19,5) NULL,
	[DirectDeposits] [decimal](19,5) NULL,
	[OtherDeposits] [decimal](19,5) NULL )

CREATE TABLE #FICASSAllocations(
    [FICASSId] [int] IDENTITY(1,1) NOT NULL,
    [FICASSEmployeeID] [nvarchar](30) NOT NULL,
    [FICASSHomeDepartment] [nvarchar](50) NULL,
    [FICASSCheckNumber] [nvarchar](50) NULL,
	[FICASSPaymentAdjustmentNumber] [int] NULL,
    [FICASSDepartment] [nvarchar](50) NULL,
    [FICASSGrossWageAmount] [decimal](19,5) NULL,
    [FICASSGrossWageTotal] [decimal](19,5) NULL,
    [FICASSGrossWagePct] [decimal](19,5) NULL,
    [FICASSTotal] [decimal](19,5) NULL,
    [FICASSAllocated] [decimal](19,5) NULL,
    [FICASSAllocatedTotal] [decimal](19,5) NULL,
    [FICASSAllocatedDifference] [decimal](19,5) NULL,
    [FICASSAdjusted] [decimal](19,5) NULL )

CREATE TABLE #FICAMedAllocations(
    [FICAMedId] [int] IDENTITY(1,1) NOT NULL,
    [FICAMedEmployeeID] [nvarchar](50) NOT NULL,
    [FICAMedHomeDepartment] [nvarchar](50) NULL,
    [FICAMedCheckNumber] [nvarchar](50) NULL,
	[FICAMedPaymentAdjustmentNumber] [int] NULL,
    [FICAMedDepartment] [nvarchar](50) NULL,
    [FICAMedGrossWageAmount] [decimal](19,5) NULL,
    [FICAMedGrossWageTotal] [decimal](19,5) NULL,
    [FICAMedGrossWagePct] [decimal](19,5) NULL,
    [FICAMedTotal] [decimal](19,5) NULL,
    [FICAMedAllocated] [decimal](19,5) NULL,
    [FICAMedAllocatedTotal] [decimal](19,5) NULL,
    [FICAMedAllocatedDifference] [decimal](19,5) NULL,
    [FICAMedAdjusted] [decimal](19,5) NULL )

CREATE TABLE #FedAllocations(
    [FedId] [int] IDENTITY(1,1) NOT NULL,
    [FedEmployeeID] [nvarchar](50) NOT NULL,
    [FedHomeDepartment] [nvarchar](50) NULL,
    [FedCheckNumber] [nvarchar](50) NULL,
	[FedPaymentAdjustmentNumber] [int] NULL,
    [FedDepartment] [nvarchar](50) NULL,
    [FedGrossWageAmount] [decimal](19,5) NULL,
    [FedGrossWageTotal] [decimal](19,5) NULL,
    [FedGrossWagePct] [decimal](19,5) NULL,
    [FedTotal] [decimal](19,5) NULL,
    [FedAllocated] [decimal](19,5) NULL,
    [FedAllocatedTotal] [decimal](19,5) NULL,
    [FedAllocatedDifference] [decimal](19,5) NULL,
    [FedAdjusted] [decimal](19,5) NULL )

CREATE TABLE #Departments(Department NVARCHAR(6))
INSERT INTO #Departments SELECT Department FROM GetAllowedDepartments(@company, @client, @userid)
INSERT INTO #Departments(Department) VALUES ('')

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

-- * * * * *      S E C T I O N  1 :  Earnings     * * * * * * * 
-- Earnings by Payroll Type
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], EarnAmount, Units, Rate, Section, Sort)
select e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) as CheckDate, eth.PayrollCode, EarnAmount = SUM(eth.TRXAmount), Units = SUM(eth.UnitsToPay)
	, Rate = eth.PayRate, Section = 1, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and e.ClientID = ce.ClientID
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
--	LEFT JOIN EmployeePaycodes epc on eth.CompanyID = epc.CompanyID and eth.EmployeeID = epc.EmployeeID and eth.PayrollCode = epc.PayRecord
where eth.CompanyID = @company and ce.ClientID = @client  and eth.PayrollRecordType = 1 and eth.PayRate <> 0 and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName), e.SSN, dp.Department, dp.[Description], eth.CheckNumber,  eth.PaymentAdjustmentNumber, eth.PayrollCode, eth.PayRate, DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate))
order by EmployeeID, CheckDate, PayrollCode

insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], EarnAmount, Units, Rate, Section, Sort)
select e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) as CheckDate, eth.PayrollCode, EarnAmount = SUM(eth.TRXAmount), Units = SUM(eth.UnitsToPay), Rate = CASE SUM(eth.UnitsToPay) WHEN 0 THEN 0 ELSE SUM(eth.TRXAmount)/SUM(eth.UnitsToPay) END, Section = 1, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and e.ClientID = ce.ClientID
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
--	LEFT JOIN EmployeePaycodes epc on eth.CompanyID = epc.CompanyID and eth.EmployeeID = epc.EmployeeID and eth.PayrollCode = epc.PayRecord
where eth.CompanyID = @company and ce.ClientID = @client  and eth.PayrollRecordType = 1 and eth.PayRate = 0 and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName), e.SSN, dp.Department, dp.[Description], eth.CheckNumber,  eth.PaymentAdjustmentNumber, eth.PayrollCode, DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) --eth.PayRate, eth.CheckDate
order by EmployeeID, CheckDate, PayrollCode

-- * * * * *      S E C T I O N  2 :  Taxes     * * * * * * * 
-- State Tax values.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) as CheckDate, PayrollCode, [State] = SUM((case when PayrollRecordType = 4 then TRXAmount else 0.00 end)), Section = 2,  Sort = 3
from EmployeeTransactionHistory eth 
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and eth.EmployeeID = ce.EmployeeID and e.ClientID = ce.ClientID
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
where eth.CompanyID = @company and ce.ClientID = @client and PayrollRecordType = 4 and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)), PayrollCode
order by EmployeeID, Sort

-- Local Tax values.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) as CheckDate, PayrollCode, [Local] = SUM((case when PayrollRecordType = 5 then TRXAmount else 0.00 end)), Section = 2,  Sort = 4
from  EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and e.ClientID = ce.ClientID
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
where eth.CompanyID = @company and ce.ClientID = @client and PayrollRecordType = 5 and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)), PayrollCode
order by EmployeeID, Sort

-- FICA SS Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], ech.CheckNumber, ech.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) as CheckDate, [Description] = 'FICA SS',  [TaxAmount] = SUM(ech.FICASSWithholdingPayRun + ech.FICASSTaxOnTips), Section = 2, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and e.ClientID = ce.ClientID
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	LEFT OUTER JOIN EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
where ech.CompanyID = @company and ce.ClientID = @client and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], ech.CheckNumber, ech.PaymentAdjustmentNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate))
order by EmployeeID, Sort

insert into #FICASSAllocations(FICASSEmployeeID, FICASSHomeDepartment, FICASSCheckNumber, FICASSPaymentAdjustmentNumber, FICASSDepartment, FICASSGrossWageAmount, FICASSGrossWageTotal, FICASSGrossWagePct, FICASSTotal, FICASSAllocated, FICASSAllocatedTotal, FICASSAllocatedDifference, FICASSAdjusted)
select *, FICASSAllocatedTotal = sum(FICASSAllocated) over (partition by CheckNumber, EmployeeID),
		  FICASSAllocatedDifference = sum(FICASSAllocated) over (partition by CheckNumber, EmployeeID) - FICASSTotal,
		  FICASSAdjusted = 0 
from (select en.EmployeeID, en.HomeDepartment, en.CheckNumber, en.PaymentAdjustmentNumber, en.Department, en.TRXAmount, en.TRXTotal, GrossWagePct, 
           FICASSTotal = (select sum(FICASSWithholdingPayRun + FICASSTaxOnTips) from EmployeeCheckHistory ech where ech.EmployeeID = en.EmployeeID and ech.CheckNumber = en.CheckNumber),
           FICASSAllocated = (select sum(FICASSWithholdingPayRun + FICASSTaxOnTips) * (CASE TRXTotal WHEN 0 THEN 0 ELSE en.TRXAmount / en.TRXTotal END) from EmployeeCheckHistory ech where ech.EmployeeID = en.EmployeeID and ech.CheckNumber = en.CheckNumber)
	from (select e.EmployeeID, e.Department HomeDepartment, eth.CheckNumber, eth.PaymentAdjustmentNumber, eth.Department,
				TRXAmount = SUM(eth.TRXAmount),
				TRXTotal = sum(sum(eth.TRXAmount)) over (partition by eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID),
				GrossWagePct = CASE sum(eth.TRXAmount) WHEN 0 THEN 0 ELSE sum(eth.TRXAmount) / sum(sum(eth.TRXAmount)) over (partition by eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID) END
		  from EmployeeTransactionHistory eth
			INNER JOIN #Departments d ON d.Department = eth.Department
			INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
            inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
		where eth.CompanyID = @company and PayrollRecordType = 1 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
		group by eth.CompanyID, e.EmployeeID, eth.CheckNumber, eth.PaymentAdjustmentNumber, eth.Department, e.Department
	) as en
) as allocations

update #FICASSAllocations SET FICASSAdjusted = CASE WHEN FICASSHomeDepartment = FICASSDepartment THEN FICASSAllocated + FICASSAllocatedDifference ELSE FICASSAllocated END

-- FICA Med Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], ech.CheckNumber, ech.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) as CheckDate, 'FICA Med', SUM(ech.FICAMWithholdingPayRun + ech.FICAMTaxOnTips) as [TaxAmount], Section = 2, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	LEFT OUTER JOIN EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
where eth.CompanyID = @company and ce.ClientID = @client and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], ech.CheckNumber, ech.PaymentAdjustmentNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate))
order by EmployeeID, Sort

insert into #FICAMedAllocations(FICAMedEmployeeID, FICAMedHomeDepartment, FICAMedCheckNumber, FICAMedPaymentAdjustmentNumber, FICAMedDepartment, FICAMedGrossWageAmount, FICAMedGrossWageTotal, FICAMedGrossWagePct, FICAMedTotal, FICAMedAllocated, FICAMedAllocatedTotal, FICAMedAllocatedDifference, FICAMedAdjusted)
select *,
      FICAMedAllocatedTotal = sum(FICAMedAllocated) over (partition by FICAMedCheckNumber, FICAMedEmployeeID),
      FICAMedAllocatedDifference = sum(FICAMedAllocated) over (partition by FICAMedCheckNumber, FICAMedEmployeeID) - FICAMedTotal,
      FICAMedAdjusted = 0 
from (
	select *,
           FICAMedTotal = (select sum(ech.FICAMWithholdingPayRun + ech.FICAMTaxOnTips) from EmployeeCheckHistory ech where ech.EmployeeID = en.FICAMedEmployeeID and ech.CheckNumber = en.FICAMedCheckNumber),
           FICAMedAllocated = (select sum(ech.FICAMWithholdingPayRun + FICAMTaxOnTips) * (CASE FICAMedGrossWageTotal WHEN 0 THEN 0 ELSE en.FICAMedGrossWageAmount / en.FICAMedGrossWageTotal END) from EmployeeCheckHistory ech where ech.EmployeeID = en.FICAMedEmployeeID and ech.CheckNumber = en.FICAMedCheckNumber)
	from (
		select FICAMedEmployeeID = e.EmployeeID,
            FICAMedHomeDepartment = e.Department,
            FICAMedCheckNumber = eth.CheckNumber,
			FICAMedPaymentAdjustmentNumber = eth.PaymentAdjustmentNumber,
            FICAMedDepartment = eth.Department,
            FICAMedGrossWageAmount = SUM(eth.TRXAmount),
            FICAMedGrossWageTotal = sum(sum(eth.TRXAmount)) over (partition by eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID),
            FICAMedGrossWagePct = CASE sum(TRXAmount) WHEN 0 THEN 0 ELSE sum(TRXAmount) / sum(sum(eth.TRXAmount)) over (partition by eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID) END
		from EmployeeTransactionHistory eth
			INNER JOIN #Departments d ON d.Department = eth.Department
			INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
            inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
		where eth.CompanyID = @company and PayrollRecordType = 1 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
		group by eth.CompanyID, e.EmployeeID, eth.CheckNumber, eth.PaymentAdjustmentNumber, eth.Department, e.Department
	) as en
) as allocations

update #FICAMedAllocations SET FICAMedAdjusted = CASE WHEN FICAMedHomeDepartment = FICAMedDepartment THEN FICAMedAllocated + FICAMedAllocatedDifference ELSE FICAMedAllocated END

-- Federal Tax Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) as CheckDate, 'Federal',  SUM(ech.FederalWithholdingPayRun) as [TaxAmount], Section = 2,  Sort = 2
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and e.ClientID = ce.ClientID
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	LEFT OUTER JOIN EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
where ech.CompanyID = @company and ce.ClientID = @client and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate))
order by EmployeeID, Sort

insert into #FedAllocations(FedEmployeeID, FedHomeDepartment, FedCheckNumber, FedPaymentAdjustmentNumber, FedDepartment, FedGrossWageAmount, FedGrossWageTotal, FedGrossWagePct, FedTotal, FedAllocated, FedAllocatedTotal, FedAllocatedDifference, FedAdjusted)
select *,
      FedAllocatedTotal = sum(FedAllocated) over (partition by FedCheckNumber, FedEmployeeID),
      FedAllocatedDifference = sum(FedAllocated) over (partition by FedCheckNumber, FedEmployeeID) - FedTotal,
      FedAdjusted = 0 
from (select *,
            FedTotal = (select sum(ech.FederalWithholdingPayRun) from EmployeeCheckHistory ech where ech.EmployeeID = en.FedEmployeeID and ech.CheckNumber = en.FedCheckNumber),
            FedAllocated = (select sum(ech.FederalWithholdingPayRun) * (CASE FedGrossWageTotal WHEN 0 THEN 0 ELSE en.FedGrossWageAmount / en.FedGrossWageTotal END) from EmployeeCheckHistory ech where ech.EmployeeID = en.FedEmployeeID and ech.CheckNumber = en.FedCheckNumber)
      from (select 
				FedEmployeeID = e.EmployeeID,
				FedHomeDepartment = e.Department,
				FedCheckNumber = eth.CheckNumber,
				FedPaymentAdjustmentNumber = eth.PaymentAdjustmentNumber,
				FedDepartment = eth.Department,
				FedGrossWageAmount = SUM(eth.TRXAmount),
				FedGrossWageTotal = sum(sum(eth.TRXAmount)) over (partition by eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID),
				FedGrossWagePct = CASE sum(TRXAmount) WHEN 0 THEN 0 ELSE sum(TRXAmount) / sum(sum(eth.TRXAmount)) over (partition by eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID) END
            from EmployeeTransactionHistory eth
				INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
				inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
            where eth.CompanyID = @company and PayrollRecordType = 1 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
            group by eth.CompanyID, e.EmployeeID, eth.CheckNumber, eth.PaymentAdjustmentNumber, eth.Department, e.Department
            ) as en
) as allocations

update #FedAllocations SET FedAdjusted = CASE WHEN FedHomeDepartment = FedDepartment THEN FedAllocated + FedAllocatedDifference ELSE FedAllocated END

-- * * * * *      S E C T I O N  3 :  Deductions     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], DeductAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) as CheckDate, eth.PayrollCode, SUM(eth.TRXAmount), Section = 3, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and e.ClientID = ce.ClientID 
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
where eth.CompanyID = @company and ce.ClientID = @client and eth.PayrollRecordType = 2 and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)), PayrollCode
order by EmployeeID, Sort


-- * * * * *      S E C T I O N  4 :  Benefits     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, CheckNumber, PaymentAdjustmentNumber, CheckDate, [Description], BenefitAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber,
	DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) as CheckDate, eth.PayrollCode,  SUM(eth.TRXAmount), Section = 4, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
		inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
		left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and e.ClientID = ce.ClientID
		inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
		left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID --AND ech.Department = eth.Department
where eth.CompanyID = @company and ce.ClientID = @client and eth.PayrollRecordType = 3 and IsNull(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, dp.Department, dp.[Description], eth.CheckNumber, eth.PaymentAdjustmentNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)), PayrollCode
order by EmployeeID, Sort

-- * * * * *     N E T    P A Y    * * * * * * * 
insert into #EmployeePayExtras(CompanyID, EmployeeID, Department, DepartmentName, CheckNumber, CheckDate, NetPay)
select ech.CompanyID, ech.EmployeeID, dp.Department, dp.[Description], ech.CheckNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) as CheckDate, SUM(ech.NetWagesPayRun) NetWagesPayRun
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	left outer join ClientEmployees ce on ech.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and e.ClientID = ce.ClientID
	inner join Departments dp on dp.CompanyID = 1 and dp.Department = e.Department
where ech.CompanyID = @company and ce.ClientID = @client and ISNULL(ech.Voided,0) = 0 and DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) between @startdate and @enddate
group by ech.CompanyID, ech.EmployeeID, dp.Department, dp.[Description], ech.CheckNumber, DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate))


-- * * * * *     O U T P U T     * * * * * * * 
select pd.Id, pd.EmployeeID, EmployeeName, '***-**-' + SUBSTRING(EmployeeSSN, 6, 4) [EmployeeSSN], pd.Department, pd.DepartmentName,
				rtrim(ltrim(REPLACE(pd.CheckNumber, NCHAR(0x00A0), ''))) as CheckNumber, Len(pd.CheckNumber) as CheckNumberLength, pd.CheckDate, 
				[Description], Units, Rate, EarnAmount,
				TaxAmount = CASE pd.Description WHEN 'FICA SS' THEN fss.FICASSAllocated WHEN 'FICA Med' THEN fmd.FICAMedAdjusted WHEN 'Federal' THEN Fed.FedAdjusted ELSE pd.TaxAmount END,
				DeductAmount, BenefitAmount, ex.NetPay, Section, Sort
from #EmployeePayDetail pd
	left join #EmployeePayExtras ex on pd.EmployeeID = ex.EmployeeID and pd.CheckNumber = ex.CheckNumber and pd.Department = ex.Department and pd.CheckDate = ex.CheckDate
	left join #FICASSAllocations fss on pd.EmployeeID = fss.FICASSEmployeeID and pd.CheckNumber = fss.FICASSCheckNumber and pd.Department = fss.FICASSDepartment and pd.Description = 'FICA SS'
	left join #FICAMedAllocations fmd on pd.EmployeeID = fmd.FICAMedEmployeeID and pd.CheckNumber = fmd.FICAMedCheckNumber and pd.Department = fmd.FICAMedDepartment and pd.Description = 'FICA Med'
	left join #FedAllocations fed on pd.EmployeeID = fed.FedEmployeeID and pd.CheckNumber = fed.FedCheckNumber and pd.Department = fed.FedDepartment and pd.Description = 'Federal'
WHERE (pd.Department = @department or @department IS NULL)
ORDER BY pd.EmployeeID, rtrim(ltrim(REPLACE(pd.CheckNumber, NCHAR(0x00A0), '')))

OPTION (RECOMPILE)

DROP TABLE #Departments
DROP TABLE #EmployeePayDetail
DROP TABLE #EmployeePayExtras
DROP TABLE #FedAllocations
DROP TABLE #FICAMedAllocations
DROP TABLE #FICASSAllocations
GO

