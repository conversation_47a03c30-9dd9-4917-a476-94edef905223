-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Move ob document to permanent employee
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBProcess_MoveDocument]
	-- Add the parameters for the stored procedure here
	@CompanyID as int,
	@EmployeeID as nvarchar(15),
	@EmployeeName as nvarchar(50),
	@DocumentID as int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
INSERT INTO  EmployeeOBDocuments (CompanyID, EmployeeID, DocumentID, DocumentName, EmployeeName, EEVerifiedDate, EEVerifiedBy, EEVerifiedIP, CCVerifiedDate, CCVerifiedBy, CCVerifiedIP, 
                      DFile, DPath, DBody, DStatus, ContentType, Download)
SELECT @CompanyID as CompanyID, EmployeeID, DocumentID, DocumentName, @EmployeeName as EmployeeName,EEVerifiedDate, EEVerifiedBy, EEVerifiedIP, CCVerifiedDate, CCVerifiedBy, CCVerifiedIP, DFile, DPath, DBody, DStatus, 
                      ContentType, 0 
FROM        OBProcessDocuments
WHERE EmployeeID = @EmployeeID AND DocumentID = @DocumentID


--INSERT INTO Attachments (CompanyID,[Level], TableName, PrimaryKey, SecondaryKey, EVisible, AttachmentName, AttachmentDescription, ContentType, 
--                      AttachmentBody, Seen, LastDate, Download)
--SELECT CompanyID, 'C', 'Employees', @EmployeeID as PrimaryKey, '', 1, DFile, DocumentName, ContentType, DBody, EEVerified, EEVerifiedDate, 0
--FROM          OBProcessDocuments
--WHERE CompanyID = @CompanyID AND EmployeeID = @EmployeeID AND DocumentID = @DocumentID

END
