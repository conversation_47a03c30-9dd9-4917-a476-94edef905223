/*
********************************************************************
**** Created by <PERSON>.
**** Date: 05/10/2015.
**** Purpose: Generates main dataset for ASO Cash Requirements Report
****
********************************************************************
*/

-- sp_RPT_ASOCashRequirements 1, '004', 16, 'BrianN'

CREATE   PROCEDURE [dbo].[sp_RPT_ASOCashRequirements]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@invoice int = NULL,
	@userid NVARCHAR(20)
)
AS

--DECLARE @company INT = 1, @client VARCHAR(15) = '001', @invoice int = 77, @userid NVARCHAR(20) = 'BrianN'

DECLARE @division nvarchar(50) = NULL
DECLARE @isASO int = NULL
DECLARE @InvoiceorClientSetup int = NULL
DECLARE @WhoPays int
DECLARE @EmployeeLiability [decimal](19,5) = NULL
DECLARE @EmployerLiability [decimal](19,5) = NULL
DECLARE @Flag int
DECLARE @UseDeductionCredit int

CREATE TABLE #WhoPays(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[AccountType] [int] NULL,
	[CreditAccountType] [int] NULL,
	[DebitAccount] [int] NULL,
	[ASOPays] [tinyint] NULL)

CREATE TABLE #OutPut(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Description] [nvarchar](50) NULL,
	[Responsibility] [nvarchar](50) NULL,
	[FirstMoney] [decimal](19,5) NULL,
	[SecondMoney] [decimal](19,5) NULL,
	[ThirdMoney] [decimal](19,5) NULL,
	[FourthMoney] [decimal](19,5) NULL,
	[FirstInt][int] NULL,
	[SecondInt][int] NULL,
	[WhoPays][int] NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL
	)

CREATE TABLE #HoldDedBen(
	[ClientId] [nvarchar](15) NULL,
	[DarwinInvoiceNumber][int] NULL,
	[InvoiceNumber] [nvarchar](50) NULL,
	[AuditControlCode] [nvarchar](50) NULL,
	[Position] [nvarchar](50) NULL,
	[EmployeeId] [nvarchar](50) NULL,
	[Amount] [decimal](19,5) NULL,
	[PayrollCode] [nvarchar](50) NULL,
	[PostingAccountType][int] NULL,
	)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

Select @division = (select DivisionID from Invoices where CompanyID = @company and ClientID = @client and DarwinInvoiceNumber = @invoice)
select @InvoiceorClientSetup = (select count(clientid) from InvoiceASOAccountSetupHistory where CompanyID = @company and ClientID = @client and DarwinInvoiceNumber = @invoice)

if (@InvoiceorClientSetup = 0) 
	BEGIN
		INSERT INTO #WhoPays(AccountType, CreditAccountType, DebitAccount, ASOPays)
		select AccountType, CreditAccountId, DebitAccountId, ASO_Pays 
		from ClientASOAccountSetup 
		where CompanyID = @company and ClientId = @client and DivisionID = @division
	END
else
	BEGIN
		INSERT INTO #WhoPays(AccountType, CreditAccountType, DebitAccount, ASOPays)
		select AccountType, CreditAccountId, DebitAccountId, ASO_Pays 
		from InvoiceASOAccountSetupHistory 
		where  CompanyID = @company and ClientID = @client and DarwinInvoiceNumber = @invoice
	END

SELECT @UseDeductionCredit = (select UseDedCredits from Clients where CompanyID = @company and clientid = @client)

declare @checks int = 0
declare @directDeposits int = 0

select @checks = (select SUM(case when ech.NetWagesPayRun <> IsNUll(dd.Amount,0) then 1 else 0 end) [Checks]
				  from EmployeeCheckHistory ech
					inner join InvoicePayrolls cip on cip.CompanyID = ech.CompanyID and cip.AuditControlCode = ech.AuditControlCode
					inner join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
					LEFT OUTER JOIN (
						select eddh.CompanyID, eddh.AuditControlCode, eddh.EmployeeID, eddh.PaymentAdjustmentNumber, sum(ActualDeposit) [Amount], count(ActualDeposit) [DirectDeposits] 
						from EmployeeDirectDepositHistory eddh
							inner join InvoicePayrolls cip on cip.CompanyID = eddh.CompanyID and cip.AuditControlCode = eddh.AuditControlCode
							inner join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
						where cip.CompanyID = @company and cip.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
						group by eddh.CompanyID, eddh.AuditControlCode, eddh.EmployeeID, eddh.PaymentAdjustmentNumber
					) dd on dd.CompanyID = ech.CompanyID and dd.AuditControlCode = ech.AuditControlCode and dd.EmployeeID = ech.EmployeeID and dd.PaymentAdjustmentNumber = ech.PaymentAdjustmentNumber
				  where cip.CompanyID = @company and cip.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
				 )

select @directDeposits = (select SUM(dd.DirectDeposits) [DirectDeposits]
						  from EmployeeCheckHistory ech
							inner join InvoicePayrolls cip on cip.CompanyID = ech.CompanyID and cip.AuditControlCode = ech.AuditControlCode
							inner join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
							LEFT OUTER JOIN (
								select eddh.CompanyID, eddh.AuditControlCode, eddh.EmployeeID, eddh.PaymentAdjustmentNumber, sum(ActualDeposit) [Amount], count(ActualDeposit) [DirectDeposits] 
								from EmployeeDirectDepositHistory eddh
									inner join InvoicePayrolls cip on cip.CompanyID = eddh.CompanyID and cip.AuditControlCode = eddh.AuditControlCode
									inner join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
								where cip.CompanyID = @company and cip.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
								group by eddh.CompanyID, eddh.AuditControlCode, eddh.EmployeeID, eddh.PaymentAdjustmentNumber
							) dd on dd.CompanyID = ech.CompanyID and dd.AuditControlCode = ech.AuditControlCode and dd.EmployeeID = ech.EmployeeID and dd.PaymentAdjustmentNumber = ech.PaymentAdjustmentNumber
						  where cip.CompanyID = @company and cip.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
						)

INSERT INTO #OutPut([Description], FirstMoney, FirstInt, SecondInt, Section, Sort)
select 'Gross Pay:' [Description], SUM(h.GrossWagesPayRun) [GrossPay], [NumChecks] = @checks, [NumDirectDeps] = @directDeposits, 
		1 [Section], 1 [Sort]
from InvoicePayrolls cip
	left outer join Invoices i on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join EmployeeCheckHistory h on h.CompanyID = cip.CompanyID and h.AuditControlCode = cip.AuditControlCode
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = h.EmployeeID
where i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
group by h.CompanyID--, h.AuditControlCode

INSERT INTO #OutPut([Description], FirstMoney, Section, Sort)
SELECT ic.ChargeType, ic.Total, 3 [Section], 1 [Sort]
FROM InvoiceCharges ic
	LEFT OUTER JOIN InvoiceChargeDetails icd ON icd.CompanyID = ic.CompanyID AND icd.ClientID = ic.ClientID AND icd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber AND icd.ChargeType = ic.ChargeType AND icd.SelectForPrint = 1
WHERE ic.CompanyID = @company and ic.ClientID = @client and ic.DarwinInvoiceNumber = @invoice
	and ic.ChargeType NOT IN ('PAYROLL', 'OTHER TAXES') AND ic.Total != 0

-- Charges Total Line.
INSERT INTO #OutPut([Description], FirstMoney, Section, Sort)
select 'Total Charges', SUM(o.FirstMoney) [Total], 300 [Section], 1 [Sort]
from #OutPut o
where o.Section = 3

-- Section 4 FICA SS.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 2)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
select 'FICA Social Security' [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   sum(total) as [Total], sum(case when icd.ComponentCode = 'FICA-SS LIABILITY' then total else 0 end) as [SS-EE],
	   sum(case when icd.ComponentCode = 'FICA-SOCIAL SECURITY' then total else 0 end) as [SS-ER], @WhoPays, 4 [Section], 1 [Sort]
from InvoiceChargeDetails icd
where CompanyID = @company and ClientID = @client and DarwinInvoiceNumber = @invoice and ChargeType = 'Payroll'
  and ComponentCode in ( 'FICA-SOCIAL SECURITY', 'FICA-SS LIABILITY')
group by CompanyID, ClientID

-- Section 4 FICA Med.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 3)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
select 'FICA Medicare' [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   sum(total) as [Total], sum(case when icd.ComponentCode = 'FICA-MED LIABILITY' then total else 0 end) as [FICA-Med-EE],
	   sum(case when icd.ComponentCode = 'FICA-MEDICARE' then total else 0 end) as [FICA-Med-ER], @WhoPays, 4 [Section], 2 [Sort]
from InvoiceChargeDetails icd
where CompanyID = @company and ClientID = @client and DarwinInvoiceNumber = @invoice and  ChargeType = 'Payroll'
  and ComponentCode in ( 'FICA-MEDICARE', 'FICA-MED LIABILITY')
group by CompanyID, ClientID

-- Section 4 FIT.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 4)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
select 'Federal Withholding' [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   sum(FederalWithholdingPayRun) as [Fed-WH-EE], sum(FederalWithholdingPayRun) as [Total], 0.00 as [Fed-WH-ER], @WhoPays, 4 [Section], 4 [Sort]
from EmployeeCheckHistory h
	inner join InvoicePayrolls cip on cip.CompanyID = h.CompanyID and cip.AuditControlCode = h.AuditControlCode
	inner join Invoices i on i.CompanyID = h.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
WHERE i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
group by h.CompanyID

-- Section 4 FUTA.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 10)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
select 'FUTA' [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays], sum(total) as [Total],
	   0.00 as [FUTA-EE], sum(case when icd.ComponentCode = 'FUTA' then total else 0 end) as [FUTA-ER], @WhoPays, 4 [Section], 4 [Sort]
from InvoiceChargeDetails icd
where CompanyID = @company and ClientID = @client and DarwinInvoiceNumber = @invoice and ChargeType = 'Payroll' and ComponentCode in ('FUTA')

-- Section 4 SUTA.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 9)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
select 'SUTA - ' + PayrollCode [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   sum(FUTABilling) as [Total], 0.00 as [SUTA-EE], sum(FUTABilling) as [SUTA-ER], @WhoPays, 4 [Section], 5 [Sort]
from EmployeeFutaSutaWorkersCompHistory fu
	inner join InvoicePayrolls cip on cip.CompanyID = fu.CompanyID and cip.AuditControlCode = fu.AuditControlCode
	inner join Invoices i on i.CompanyID = fu.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
WHERE i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and PayrollRecordType = 1
group by fu.CompanyID, PayrollCode

-- Section 4 State Tax.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 5)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
select 'State Withholding - ' + PayrollCode [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   sum(TRXAmount) as [Total], sum(TRXAmount) as [State-WH-EE], 0.00 as [State-WH-ER], @WhoPays, 4 [Section], 6 [Sort]
from EmployeeTransactionHistory h
	inner join InvoicePayrolls cip on cip.CompanyID = h.CompanyID and cip.AuditControlCode = h.AuditControlCode
	inner join Invoices i on i.CompanyID = h.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
WHERE i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and PayrollRecordType = 4
group by i.CompanyID, PayrollCode

-- Section 4 Local Tax.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 6)
DECLARE @LocalSum decimal(19,5)
SELECT @LocalSum = (select sum(h.TRXAmount)
					from EmployeeTransactionHistory h
						inner join InvoicePayrolls cip on cip.CompanyID = h.CompanyID and cip.AuditControlCode = h.AuditControlCode
						inner join Invoices i on i.CompanyID = h.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
					WHERE i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and PayrollRecordType = 5
					group by i.CompanyID)

INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
SELECT 'Local Withholding - All' [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   ISNULL(@LocalSum,0) as [Total], ISNULL(@LocalSum,0) as [State-WH-EE], 0.00 as [State-WH-ER], @WhoPays, 4 [Section], 7 [Sort]	

-- Section 4 WC.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 11)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, WhoPays, Section, Sort)
select 'Worker''s Compensation - All'  [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   sum(FUTABilling) as [Total], 0.00 as [SUTA-EE], sum(FUTABilling) as [WC-ER], @WhoPays, 4 [Section], 8 [Sort]
from EmployeeFutaSutaWorkersCompHistory fu
	inner join InvoicePayrolls cip on cip.CompanyID = fu.CompanyID and cip.AuditControlCode = fu.AuditControlCode
	inner join Invoices i on i.CompanyID = fu.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
WHERE i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and PayrollRecordType = 3
group by i.CompanyID, PayrollCode

-- Section 4 State Withholding.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 5)
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, Section, Sort)
select 'State Withholding - ' + PayrollCode  [Description], (case when @WhoPays = 1 then 'Payroll Service Pays' else 'Client Pays' end) as [WhoPays],
	   sum(TRXAmount) as [Total], sum(TRXAmount) as [State-WH-EE], 0.00 as [State-WH-ER], 4 [Section], 8 [Sort]
from EmployeeTransactionHistory 
where PayrollRecordType = 6
group by CompanyID, PayrollCode

-- Section 400 Tax Deposits Totals.
INSERT INTO #OutPut([Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, Section, Sort)
SELECT 'Total Tax Deposits', '', sum(FirstMoney), sum(SecondMoney), sum(ThirdMoney), 400, 1
FROM #OutPut
where section = 4
group by Section

-- Section 5 - Other Deductions
Insert into #HoldDedBen(ClientId, InvoiceNumber, DarwinInvoiceNumber, AuditControlCode, Position, EmployeeId, Amount, PayrollCode, PostingAccountType)
select i.ClientID, i.InvoiceNumber, i.DarwinInvoiceNumber, cip.AuditControlCode, h.Position, h.EmployeeID, h.TRXAmount, AI.PayrollCode, ai.PostingAccountType 
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	left outer join EmployeeTransactionHistory h on h.CompanyID = cip.CompanyID and h.AuditControlCode = cip.AuditControlCode
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = h.EmployeeID
	left outer join InvoiceASOClientPostHistory ai on ai.ClientId = i.ClientID and ai.DarwinInvoiceNumber = i.DarwinInvoiceNumber and ai.PayrollCode = h.PayrollCode
WHERE i.CompanyID = @company and i.ClientId = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and ai.PostingAccountType = 5 and h.PayrollRecordType = 2 and ai.ASOClientCreditType = 0

-- Section 5 - Other Deductions - Credits
Insert into #HoldDedBen(ClientId, InvoiceNumber, DarwinInvoiceNumber, AuditControlCode, Position, EmployeeId, Amount, PayrollCode, PostingAccountType)
select i.ClientID, i.InvoiceNumber, i.DarwinInvoiceNumber, cip.AuditControlCode, h.Position, h.EmployeeID, h.TRXAmount * -1, AI.PayrollCode, ai.PostingAccountType 
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	left outer join EmployeeTransactionHistory h on h.CompanyID = cip.CompanyID and h.AuditControlCode = cip.AuditControlCode
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = h.EmployeeID
	left outer join InvoiceASOClientPostHistory ai on ai.ClientId = i.ClientID and ai.DarwinInvoiceNumber = i.DarwinInvoiceNumber and ai.PayrollCode = h.PayrollCode
WHERE i.CompanyID = @company and i.ClientId = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and ai.PostingAccountType = 5 and h.PayrollRecordType = 2 and ai.ASOClientCreditType = 1 

select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 7)
if (@WhoPays = 0)
Begin
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT PayrollCode, SUM(Amount), 0.0, 5, 1
	FROM #HoldDedBen
	WHERE Amount > 0
	GROUP BY PayrollCode
END
else
BEGIN
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT PayrollCode, 0.0, SUM(Amount), 5, 1
	FROM #HoldDedBen
	WHERE Amount > 0
	GROUP BY PayrollCode
END
INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT PayrollCode, SUM(ABS(Amount)), 0.0, 5, 1
	FROM #HoldDedBen
	WHERE Amount < 0
	GROUP BY PayrollCode

-- Section 5 - Other Deductions - ALL
DECLARE @ASOClientCreditType bit
SELECT @ASOClientCreditType = ASOClientCreditType
FROM InvoiceASOClientPostHistory h
where h.CompanyID = @company and h.ClientId = @client and h.DarwinInvoiceNumber = @invoice AND h.Position = 'ALL' AND h.PayrollCode = 'ALL' and h.PostingAccountType = 5 

DECLARE @OtherDeductionAmt decimal(19,5)
SELECT @OtherDeductionAmt = SUM(e.TRXAmount)
FROM EmployeeTransactionHistory e
	INNER JOIN InvoicePayrolls ip ON ip.CompanyID = e.CompanyID AND ip.AuditControlCode = e.AuditControlCode
	INNER JOIN Invoices i ON i.CompanyID = e.CompanyID AND i.ClientID = ip.ClientID AND i.InvoiceNumber = ip.InvoiceNumber
	LEFT OUTER JOIN InvoiceASOClientPostHistory h ON e.CompanyID = h.CompanyID AND e.AuditControlCode = e.AuditControlCode AND e.PayrollCode = h.PayrollCode
WHERE e.CompanyID = @company AND i.ClientID = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND e.PayrollRecordType = 2 AND h.Id IS NULL

IF (@ASOClientCreditType = 0)
BEGIN
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Other Deductions', 0.0, ISNULL(@OtherDeductionAmt, 0.0), 5, 2
END
ELSE
BEGIN
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Other Deductions', ISNULL(@OtherDeductionAmt, 0.0), 0.0, 5, 2
END

--Clear table.
DELETE FROM #HoldDedBen

-- Section 5 - Other Benefits.
Insert into #HoldDedBen(ClientId, InvoiceNumber, DarwinInvoiceNumber, AuditControlCode, Position, EmployeeId, Amount, PayrollCode, PostingAccountType)
select i.ClientID, i.InvoiceNumber, i.DarwinInvoiceNumber, cip.AuditControlCode, h.Position, h.EmployeeID, h.TRXAmount, AI.PayrollCode, ai.PostingAccountType 
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	left outer join EmployeeTransactionHistory h on h.CompanyID = cip.CompanyID and h.AuditControlCode = cip.AuditControlCode
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = h.EmployeeID
	left outer join InvoiceASOClientPostHistory ai on ai.ClientId = i.ClientID and ai.DarwinInvoiceNumber = i.DarwinInvoiceNumber and ai.PayrollCode = h.PayrollCode
WHERE i.CompanyID = @company and i.ClientId = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and ai.PostingAccountType in (8,10) and ai.ASOClientCreditType = 0 and h.PayrollRecordType = 3


-- Section 5 - Other Benefits - Credits.
Insert into #HoldDedBen(ClientId, InvoiceNumber, DarwinInvoiceNumber, AuditControlCode, Position, EmployeeId, Amount, PayrollCode, PostingAccountType)
select i.ClientID, i.InvoiceNumber, i.DarwinInvoiceNumber, cip.AuditControlCode, h.Position, h.EmployeeID, h.TRXAmount * -1, AI.PayrollCode, ai.PostingAccountType 
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	left outer join EmployeeTransactionHistory h on h.CompanyID = cip.CompanyID and h.AuditControlCode = cip.AuditControlCode
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = h.EmployeeID
	left outer join InvoiceASOClientPostHistory ai on ai.ClientId = i.ClientID and ai.DarwinInvoiceNumber = i.DarwinInvoiceNumber and ai.PayrollCode = h.PayrollCode
WHERE i.CompanyID = @company and i.ClientId = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and ai.PostingAccountType in (10) and ai.ASOClientCreditType = 5 and h.PayrollRecordType = 3

Insert into #HoldDedBen(ClientId, InvoiceNumber, DarwinInvoiceNumber, AuditControlCode, Position, EmployeeId, Amount, PayrollCode, PostingAccountType)
select i.ClientID, i.InvoiceNumber, i.DarwinInvoiceNumber, cip.AuditControlCode, h.Position, h.EmployeeID, h.TRXAmount * -1, AI.PayrollCode, ai.PostingAccountType 
from Invoices i
	left outer join InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
	left outer join EmployeeTransactionHistory h on h.CompanyID = cip.CompanyID and h.AuditControlCode = cip.AuditControlCode
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = h.EmployeeID
	left outer join InvoiceASOClientPostHistory ai on ai.ClientId = i.ClientID and ai.DarwinInvoiceNumber = i.DarwinInvoiceNumber and ai.PayrollCode = h.PayrollCode
WHERE i.CompanyID = @company and i.ClientId = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) and ai.PostingAccountType in (8) and ai.ASOClientCreditType = 4 and h.PayrollRecordType = 3

select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 8)
if (@WhoPays = 0)
Begin
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT PayrollCode, SUM(Amount), 0.0, 5, 3
	FROM #HoldDedBen
	GROUP BY PayrollCode
END
else
BEGIN
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT PayrollCode, 0.0, SUM(Amount), 5, 3
	FROM #HoldDedBen
	GROUP BY PayrollCode
END

DECLARE @OtherBenefitAmt decimal(19,5)
SELECT @OtherBenefitAmt = SUM(e.TRXAmount)
FROM EmployeeTransactionHistory e
	INNER JOIN InvoicePayrolls ip ON ip.CompanyID = e.CompanyID AND ip.AuditControlCode = e.AuditControlCode
	INNER JOIN Invoices i ON i.CompanyID = e.CompanyID AND i.ClientID = ip.ClientID AND i.InvoiceNumber = ip.InvoiceNumber
	LEFT OUTER JOIN InvoiceASOClientPostHistory h ON e.CompanyID = h.CompanyID AND e.AuditControlCode = e.AuditControlCode AND e.PayrollCode = h.PayrollCode
WHERE e.CompanyID = @company AND i.ClientID = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND e.PayrollRecordType = 3 AND h.Id IS NULL

IF (@ASOClientCreditType = 0 and @WhoPays = 1)
BEGIN
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Other Benefits', 0.0, ISNULL(@OtherBenefitAmt, 0.0), 5, 5
END
ELSE
BEGIN
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Other Benefits', ISNULL(@OtherBenefitAmt, 0.0), 0.0, 5, 5
END

-- Section 5 - Recapture Deductions.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 7)
if (@WhoPays = 0)
begin
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Recaptured Deductions', coalesce(sum(RecaptureAmount),0.00), 0.00, 5, 4
	FROM Invoices i
		LEFT OUTER JOIN InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
		INNER JOIN RecaptureHistory rh ON rh.CompanyID = i.CompanyID AND rh.AuditControlCode = cip.AuditControlCode
	WHERE i.CompanyID = @company AND i.ClientID = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND rh.PayrollType = 2
end
else
begin
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Recaptured Deductions', 0.00, coalesce(sum(RecaptureAmount),0.00), 5, 4
	FROM Invoices i
		LEFT OUTER JOIN InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
		INNER JOIN RecaptureHistory rh ON rh.CompanyID = i.CompanyID AND rh.AuditControlCode = cip.AuditControlCode
	WHERE i.CompanyID = @company AND i.ClientID = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND rh.PayrollType = 2
end

-- Section 5 - Recapture Benefits.
SELECT @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 8)
if (@WhoPays = 0)
begin
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Recaptured Benefits', coalesce(sum(RecaptureAmount),0.00), 0.00, 5, 6
	FROM Invoices i
		LEFT OUTER JOIN InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
		INNER JOIN RecaptureHistory rh ON rh.CompanyID = i.CompanyID AND rh.AuditControlCode = cip.AuditControlCode
	WHERE i.CompanyID = @company AND i.ClientID = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND rh.PayrollType = 3
end
else
begin
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Recaptured Benefits', 0.00, coalesce(sum(RecaptureAmount),0.00), 5, 6
	FROM Invoices i
		LEFT OUTER JOIN InvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID = i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
		INNER JOIN RecaptureHistory rh ON rh.CompanyID = i.CompanyID AND rh.AuditControlCode = cip.AuditControlCode
	WHERE i.CompanyID = @company AND i.ClientID = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND rh.PayrollType = 3
end

-- Section 500 Deductions and Benefits Totals.
INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT 'Total Deductions and Benefits', sum(FirstMoney), sum(SecondMoney), 500, 1
FROM #OutPut
where section = 5
group by Section

-- Section 2 Net Checks.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 12)

INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT 'Net Checks' [Description], 
		CASE WHEN @WhoPays = 1 THEN 0.00 ELSE sum(ech.NetWagesPayRun) END [ClientPays], 
		CASE WHEN @WhoPays = 1 THEN sum(ech.NetWagesPayRun) ELSE 0.00 END [Drafted], 
		2 [Section], 1 [Sort]
FROM Invoices I
	LEFT OUTER JOIN InvoicePayrolls ip ON ip.CompanyID = i.CompanyID AND ip.ClientID = i.ClientID AND ip.InvoiceNumber = i.InvoiceNumber
	LEFT OUTER JOIN EmployeeCheckHistory ech ON ech.CompanyID = i.CompanyID AND ech.AuditControlCode = ip.AuditControlCode
	LEFT OUTER JOIN EmployeeDirectDepositHistory dd ON dd.CompanyID = i.CompanyID AND dd.AuditControlCode = ip.AuditControlCode AND dd.EmployeeID = ech.EmployeeID AND dd.ActualDeposit != 0
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = ech.EmployeeID
WHERE i.CompanyID = @company AND i.clientid = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND dd.EmployeeID IS NULL
GROUP BY ip.CompanyID--, ip.AuditControlCode

--IF NO checks record, add one.
select @Flag = (select count(id) from #OutPut where Section = 2 and sort = 1)
if (@Flag = 0)
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Net Checks' [Description], 0.00 [ClientPays], 0.00 [Drafted], 2 [Section], 1 [Sort]

-- Section 2 Direct Deposit.
select @WhoPays = (select ASOPays from #WhoPays x where x.AccountType = 14)
INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT 'Net Direct Deposit' [Description], 
		CASE WHEN @WhoPays = 1 THEN 0.00 ELSE sum(dd.ActualDeposit) END [ClientPays], 
		CASE WHEN @WhoPays = 1 THEN sum(dd.ActualDeposit) ELSE 0.00 END [Drafted], 
		2 [Section], 2 [Sort]
from Invoices I
	LEFT OUTER JOIN InvoicePayrolls ip ON ip.CompanyID = i.CompanyID AND ip.ClientID = i.ClientID AND ip.InvoiceNumber = i.InvoiceNumber
	LEFT OUTER JOIN EmployeeCheckHistory ech ON ech.CompanyID = i.CompanyID AND ech.AuditControlCode = ip.AuditControlCode
	LEFT OUTER JOIN EmployeeDirectDepositHistory dd ON dd.CompanyID = i.CompanyID AND dd.AuditControlCode = ip.AuditControlCode AND dd.EmployeeID = ech.EmployeeID AND dd.ActualDeposit != 0
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = ech.EmployeeID
WHERE i.CompanyID = @company AND i.clientid = @client AND (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice) AND dd.EmployeeID IS NOT NULL
GROUP BY ip.CompanyID, ip.AuditControlCode

--IF NO direct deposit record, add one.
select @Flag = (select count(id) from #OutPut where Section = 2 and sort = 2)
if (@Flag = 0)
	INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
	SELECT 'Net Direct Deposit' [Description], 0.00 [ClientPays], 0.00 [Drafted], 2 [Section], 2 [Sort]

DECLARE @Pay decimal(19,5) = 0.0, @Drafted decimal(19,5) = 0.0
-- Section 2 Tax Deposits(Employee)
-- THIS HAS TO BE DONE AFTER ALL THE SECTION 4 STUFF BECAUSE IT REQUIRES THE RECORDS TO EXIST IN #Output.
SELECT @Pay = SUM(o.SecondMoney) FROM #OutPut o WHERE o.Section = 4 AND o.WhoPays = 0
SELECT @Drafted = SUM(o.SecondMoney) FROM #OutPut o WHERE o.Section = 4 AND o.WhoPays = 1

INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT	'Tax Deposits (Employee)', @Pay [Pay], @Drafted [Drafted], 2 [Section], 4 [Sort]

-- Section 2 Tax Deposits(Employer)
-- THIS HAS TO BE DONE AFTER ALL THE SECTION 4 STUFF BECAUSE IT REQUIRES THE RECORDS TO EXIST IN #Output.
SELECT @Pay = SUM(o.ThirdMoney) FROM #OutPut o WHERE o.Section = 4 AND o.WhoPays = 0
SELECT @Drafted = SUM(o.ThirdMoney) FROM #OutPut o WHERE o.Section = 4 AND o.WhoPays = 1

INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT	'Tax Deposits (Employer)', @Pay [Pay], @Drafted [Drafted], 2 [Section], 6 [Sort]


-- Section 2 Charges
-- THIS HAS TO BE DONE AFTER ALL THE SECTION 4 STUFF BECAUSE IT REQUIRES THE RECORDS TO EXIST IN #Output.
INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT 'Charges', 0.00 [Pay], SUM(o.FirstMoney) [Drafted], 2 [Section], 3 [Sort]
FROM #OutPut o
WHERE o.Section = 3
GROUP BY o.Section

-- Section 2 Deductions and Benefits
-- THIS HAS TO BE DONE AFTER ALL THE SECTION 5 STUFF BECAUSE IT REQUIRES THE RECORDS TO EXIST IN #Output.
INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT 'Deductions and Benefits', SUM(o.FirstMoney) [Pay], SUM(o.SecondMoney) [Drafted], 2 [Section], 5 [Sort]
FROM #OutPut o
WHERE o.Section = 5
GROUP BY o.Section


-- Section 200 Charges
-- THIS HAS TO BE DONE AFTER ALL THE SECTION 2 STUFF BECAUSE IT REQUIRES THE RECORDS TO EXIST IN #Output.
INSERT INTO #OutPut([Description], FirstMoney, SecondMoney, Section, Sort)
SELECT 'Total Cash Requirements', SUM(o.FirstMoney) [Pay], SUM(o.SecondMoney) [Drafted], 200 [Section], 1 [Sort]
FROM #OutPut o
WHERE o.Section = 2
GROUP BY o.Section

------------
-- Output --
------------
select Id, [Description], Responsibility, FirstMoney, SecondMoney, ThirdMoney, FourthMoney, FirstInt, SecondInt, WhoPays, Section, Sort
from #OutPut
order by Section, Sort


DROP TABLE #Departments
DROP TABLE #Divisions
DROP TABLE #EmployeeIDs
DROP TABLE #HoldDedBen
DROP TABLE #OutPut
DROP TABLE #WhoPays

