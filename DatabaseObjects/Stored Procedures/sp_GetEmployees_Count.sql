
/*
********************************************************************
**** Created by <PERSON><PERSON> gohel
**** Date: 11/02/2021
**** Purpose: this will give total list of employee with selected company to display count on dashboard.
****
********************************************************************
*/



CREATE PROCEDURE [dbo].[sp_GetEmployees_Count]  
(
	@company INT
	
)
AS	

select isnull(EmployeeID,'') as Employee<PERSON>, isnull(FirstName,'') as FirstName, isnull(LastName,'') as LastName ,isnull(Inactive,0) as Inactive,
CASE WHEN ISNULL(Email,'') = '' THEN CASE WHEN ISNULL(DefaultEmailAddress,'') = '' THEN CASE WHEN ISNULL(DarwinetEmail,'') = '' THEN '' ELSE DarwinetEmail END ELSE DefaultEmailAddress END ELSE Email END as Email
from Employees e
where	e.CompanyID = @company

