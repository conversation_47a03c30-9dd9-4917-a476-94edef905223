
/*
********************************************************************
**** Created by <PERSON>.
**** Date: 03/11/2015.
**** Purpose: Generates data for the SSRS Report Invoice
****
********************************************************************
*/

-- sp_RPT_InvoiceCustom1 1, '001', 189
-- sp_RPT_InvoiceCustom1 1, '003', 136
-- sp_RPT_InvoiceCustom1 1, '003', 136, NULL
-- sp_RPT_InvoiceCustom1 1, '003', 136, 1
CREATE PROCEDURE [dbo].[sp_RPT_InvoiceCustom1_Izenda]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@invoice INT,
	@section INT = NULL
)
AS

-- Get Client "Main" address in case the divisional address IS NULL.
declare @AddressCode varchar(50)
select @AddressCode = (Select Addresscode from Clients where CompanyID = @company and clientid = @client)

--This is the table that we're going to build to run the report on.
-- We will order by Section so that the report will organize the report areas properly.
-- My first attempt at this will have me applying a filter on report tablix based on the Section column.

CREATE TABLE #CustomInvoice1_Section(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[ClientID] [nvarchar](15) NULL,
	[DarwinInvoiceNumber] [int] NULL,
	[ChargeType] [nvarchar](30) NULL,
	[SequenceNumber] [int] NULL,
	[ChargeBase] [decimal](19,5) NULL,
	[Rate] varchar(50) NULL,--[decimal](19,5) NULL,
	[Total] [decimal](19,5) NULL,
	[DetailChargeType] [nvarchar](30) NULL,
	[ComponentCode] [nvarchar](30) NULL,
	[DetailSequenceNumber] [int] NULL,
	[DetailTotal] [decimal](19,5) NULL,
	[SelectForPrint] [tinyint] NULL,
	[Section] [int] NULL)

-- Build Section 2.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
SELECT	i.CompanyID, i.ClientID, i.DarwinInvoiceNumber, i.ChargeType, i.SequenceNumber, i.ChargeBase [ChargeBase],
		CASE 
			WHEN cd.ComponentCode = 'FICA-MEDICARE' 
				THEN COALESCE((select TaxBracketRate from PayrollTaxTableSetup where TAXCODE in ('eficm') ),145000 )
			WHEN cd.ComponentCode = 'FICA-SOCIAL SECURITY' 
				THEN COALESCE((select TaxBracketRate from PayrollTaxTableSetup where TAXCODE in ('ficasr')), (select TaxBracketRate from PayrollTaxTableSetup where TAXCODE in ('ficas')),620000)
			WHEN cd.ComponentCode = 'FUTA' 
				THEN (select FUTASUTATaxRate from UnemploymentSetup where FUTASUTA = 'fed')
			WHEN cd.ComponentCode = 'SUTA' 
				THEN (select case
						when
							(select distinct count(SUTAState)
							 from (select distinct SUTAState
								   from Invoices i
									 left outer join ClientInvoicePayrolls cip on cip.CompanyID = i.CompanyID and cip.ClientID =i.ClientID and cip.InvoiceNumber = i.InvoiceNumber
									 left outer join EmployeeTransactionHistory h on h.CompanyID = cip.CompanyID and h.AuditControlCode = cip.AuditControlCode
								   where PayrollRecordType = 1 and i.CompanyID = @company and i.ClientID = @client and i.DarwinInvoiceNumber = @invoice
								  ) StateCodes
							) > 1 THEN 9999999	-- Do this conversion in the report to avoid cast(cast(cast(cast))))
						ELSE
							CAST(COALESCE ((select billingsutarate from ClientUnemploymentSetup where CompanyID = @company and clientid = @client), (select futasutatrxrate from ClientUnemploymentSetup where CompanyID = @company and clientid = @client)) AS varchar(50))
						END [Rate]
					 )
			END [RATE1],
		0 [Total], cd.ChargeType [DetailChargeType], cd.ComponentCode [DetailComponentCode], cd.SequenceNumber [DetailSequenceNumber],
		cd.Total [DetailTotal], 2 [Section]
from InvoiceCharges i
left outer join InvoiceChargeDetails cd on cd.CompanyID = i.CompanyID and cd.ClientID = i.ClientID and cd.DarwinInvoiceNumber = i.DarwinInvoiceNumber
			and REPLACE(cd.ChargeType, NCHAR(0x00A0), '') = REPLACE(i.ChargeType, NCHAR(0x00A0), '')
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client and REPLACE(i.ChargeType, NCHAR(0x00A0), '') = 'PAYROLL'
  and cd.SelectForPrint = 1 and REPLACE(cd.ComponentCode, NCHAR(0x00A0),'') in ('FICA-MEDICARE','FICA-SOCIAL SECURITY','FUTA','SUTA') and cd.total <> 0

-- Build Section 4.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	i.CompanyID, i.ClientID, i.DarwinInvoiceNumber, cd.ComponentCode, i.SequenceNumber, i.ChargeBase,
		1 [Rate],	-- Rate to be determined by the psudocode
		cd.total [Total], cd.ChargeType [DetailChargeType], cd.ComponentCode [DetailComponentCode], cd.SequenceNumber [DetailSequenceNumber],
		cd.total [DetailTotal], 4 [Section]
from InvoiceCharges i
	left outer join InvoiceChargeDetails cd on cd.CompanyID = i.CompanyID and cd.ClientID = i.ClientID and cd.DarwinInvoiceNumber = i.DarwinInvoiceNumber
				and cd.ChargeType = i.ChargeType
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client and REPLACE(i.ChargeType, NCHAR(0x00A0), '') = 'PAYROLL'
  and cd.SelectForPrint = 1 and REPLACE(cd.ComponentCode, NCHAR(0x00A0),'') in ('BENEFITS','NON-Gross Bus EXP') and cd.total <> 0

-- Build Section 3 - Agency Credits.		(Made Deductions all caps to be consistent with all others--BRP).
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	i.CompanyID, i.ClientID, i.DarwinInvoiceNumber, 'DEDUCTIONS' [ChargeType], i.SequenceNumber, 0 [ChargeBase],
		0 [Rate],	-- Rate to be determined by the psudocode = 0.
		0 [Total], '' [DetailChargeType], '' [DetailComponentCode], '' [DetailSequenceNumber] , i.Total, 3 [Section]
from InvoiceCharges i
	left outer join InvoiceChargeDetails cd on cd.CompanyID = i.CompanyID and cd.ClientID = i.ClientID and cd.DarwinInvoiceNumber = i.DarwinInvoiceNumber
				and cd.ChargeType = i.ChargeType
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and REPLACE(i.ClientID, NCHAR(0x00A0), '') = REPLACE(@client, NCHAR(0x00A0), '')
  and REPLACE(i.ChargeType, NCHAR(0x00A0), '') = 'Agency Credits'

-- Build Section 3 - The Rest.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	i.CompanyID, i.ClientID, i.DarwinInvoiceNumber, i.ChargeType, i.SequenceNumber, i.ChargeBase,
		0 [Rate],	-- Rate to be determined by the psudocode = 0.
		cd.Total [Total], cd.ChargeType [DetailChargeType], cd.ComponentCode [DetailComponentCode], cd.SequenceNumber [DetailSequenceNumber],
		cd.Total [DetailTotal], 3 [Section]
from InvoiceCharges i
	left outer join InvoiceChargeDetails cd on cd.CompanyID = i.CompanyID and cd.ClientID = i.ClientID and cd.DarwinInvoiceNumber = i.DarwinInvoiceNumber
				and cd.ChargeType = i.ChargeType
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client
  and REPLACE(i.ChargeType, NCHAR(0x00A0), '') in ('Hire Act Tax Credit', 'PTO Credit')

-- Build Section 4 - The Rest.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, 0 [ChargeBase],
		0 [Rate],	-- Rate to be determined by the psudocode = 0.
		0 [Total], '' [DetailChargeType], '' [DetailComponentCode], 0 [DetailSequenceNumber], ic.Total [DetailTotal], 4 [Section]
from Invoices i
left outer join InvoiceCharges ic on i.CompanyID = ic.CompanyID and i.Clientid = REPLACE(ic.ClientID, NCHAR(0x00A0), '') and i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
left outer join ClientCharges cc on cc.CompanyID = i.CompanyID and cc.ClientID = i.ClientID
			and REPLACE(cc.DivisionID, NCHAR(0x00A0), '') = REPLACE(i.DivisionID, NCHAR(0x00A0), '')
			and REPLACE(cc.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') not in ('Payroll', 'Agency Credits', 'Hire Act Tax Credit', 'PTO Credit') and ic.Total <> 0
  and coalesce(EmpChargeBreakdown,0) = 0

-- Build Section 1 - The Rest pt 1.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, ic.ChargeBase,
		ic.ChargeMultiplier [Rate],	-- Rate to be determined by the psudocode = 0.
		ic.total [Total], ic.ChargeType [DetailChargeType], '' [DetailComponentCode], ic.SequenceNumber [DetailSequenceNumber],
		ic.ChargeBase + ic.Total [DetailTotal], 1 [Section]
from Invoices i
	left outer join InvoiceCharges ic on ic.CompanyID = i.CompanyID and REPLACE(ic.ClientID, NCHAR(0x00A0), '') = REPLACE(i.ClientID, NCHAR(0x00A0), '')
				and ic.DarwinInvoiceNumber = i.DarwinInvoiceNumber
	left outer join ClientCharges cc on cc.CompanyID = i.CompanyID and cc.ClientID = i.ClientID
				and REPLACE(cc.DivisionID, NCHAR(0x00A0), '') = REPLACE(i.DivisionID, NCHAR(0x00A0), '') and REPLACE(cc.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') not in ('Payroll', 'Agency Credits', 'Hire Act Tax Credit', 'PTO Credit')
  and ic.Criteria  > 3 and ic.Criteria <> 13 and ic.Criteria not in (21, 22, 23, 24) and ic.Total <> 0 and coalesce(EmpChargeBreakdown,0) = 1

-- Build Section 1 - The Rest pt 2.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType, ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, ic.ChargeBase, 
		ic.ChargeMultiplier [Rate],	-- Rate to be determined by the psudocode = 0.
		ic.Total [Total], ic.ChargeType [DetailChargeType], '' [DetailComponentCode], ic.SequenceNumber [DetailSequenceNumber],
		ic.ChargeBase + ic.Total [DetailTotal], 1 [Section]
from Invoices i
	left outer join InvoiceCharges ic on ic.CompanyID = i.CompanyID and REPLACE(ic.ClientID, NCHAR(0x00A0), '') = REPLACE(i.ClientID, NCHAR(0x00A0), '')
				and ic.DarwinInvoiceNumber = i.DarwinInvoiceNumber
	left outer join ClientCharges cc on cc.CompanyID = i.CompanyID and cc.ClientID = i.ClientID
				and REPLACE(cc.DivisionID, NCHAR(0x00A0), '') = REPLACE(i.DivisionID, NCHAR(0x00A0), '')
				and REPLACE(cc.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') not in ('Payroll', 'Agency Credits', 'Hire Act Tax Credit', 'PTO Credit')
  and (ic.Criteria  < 4 or ic.Criteria in (13, 21, 22, 23, 24)) and ic.Total <> 0 and coalesce(EmpChargeBreakdown,0) = 1

-- Now add Totals to report.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 101 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 1
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 102 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 2
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 103 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 3
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 104 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 4
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 1000 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section in (1,2,3,4)
group by CompanyID

select t.Id, t.CompanyID, t.ClientID, t.DarwinInvoiceNumber, t.ChargeType, t.SequenceNumber, t.ChargeBase, t.Rate, t.Total, t.DetailChargeType,
	   t.ComponentCode, t.DetailSequenceNumber, t.DetailTotal, t.SelectForPrint, t.Section
from #CustomInvoice1_Section t
where t.companyid = @company and t.ClientID = @client and t.DarwinInvoiceNumber = @invoice and (@section is null or t.Section = @section)
		
order by Section

If(OBJECT_ID('tempdb..#CustomInvoice1_Section') Is Not Null)
Begin
    Drop Table #CustomInvoice1_Section
End
