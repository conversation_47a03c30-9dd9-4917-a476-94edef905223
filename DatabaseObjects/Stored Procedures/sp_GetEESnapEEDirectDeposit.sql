/*
********************************************************************
**** Created by <PERSON><PERSON> gohel.
**** Date: 10/05/2021.
**** Purpose: Returns all ee search snapshot benefits records for one ee and this also fetch employees->eelist when you click on ee - directdeposit grid data.
****
********************************************************************
*/


CREATE PROCEDURE [dbo].[sp_GetEESnapEEDirectDeposit]  
(
	@company INT=0,
	@eeid VARCHAR(15) = NULL
)
AS                                             

select e.EmployeeID,e.BankID as Code,p.BankName as [Description],
0.0 as PayRate,0 as Frequency,'' as FrequencyName,cast (0.0 as float) as YTD,cast (0.0 as float) as Exemptions,0.0 as Additional,cast(IsNull(e.Percentages,0) as decimal(19,5)) as [Percent],e.DirectDepositAmount as [Amount],
e.AccountDescription as AccountType,(CASE  WHEN e.DirectDepositOptions = 1 THEN 'Percentage-NET'
WHEN e.DirectDepositOptions = 2 THEN 'Percentage-GROSS'
WHEN e.DirectDepositOptions = 3 THEN 'Dollars'
WHEN e.DirectDepositOptions = 4 THEN 'Remainder'
ELSE ''
END) as [Option],e.PayRecord as payRecord
,e.PayRecord + '|' + e.AccountNumber + '|' + e.AccountDescription + '|' + e.BankID + '|' + CAST(e.DirectDepositBasedOnType as nvarchar(1)) as record_id,'' as FilingStatus,e.Inactive,e.AccountNumber,
'' as Routing,
(CASE  WHEN e.DirectDepositBasedOnType = 1 THEN 'Pay Code'
WHEN e.DirectDepositBasedOnType = 2 THEN 'Deduction'
WHEN e.DirectDepositBasedOnType = 3 THEN 'Benefit'
ELSE ''
END) as [BasedOn]
from EmployeeDirectDeposits e
left outer join Banks p on p.CompanyID=e.CompanyID and p.BankID = e.BankID
left outer join EmployeeTransactionHistoryHDR h on h.CompanyID = e.CompanyID and h.EmployeeID = e.EmployeeID and h.PayrollRecordType = 3 and h.PayrollCode = e.BankID and h.YEAR = YEAR(GETDATE())
where e.CompanyID=@company and e.EmployeeID = @eeid




                      
