
/*
********************************************************************
**** Created by <PERSON>.
**** Date: 04/27/2015.
**** Purpose: Reprint Paycode Register Reports data.
****
********************************************************************
*/

-- sp_RPT_ReprintRegistersPayCode 1, '001', 77, 'BrianN'

CREATE PROCEDURE [dbo].[sp_RPT_ReprintRegistersPayCode]
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@invoice int = NULL,
	@userid NVARCHAR(20)
)
AS

--DECLARE @company INT = 1, @client VARCHAR(15) = '025', @invoice INT = 4, @userid NVARCHAR(20) = 'BrianN'

CREATE TABLE #ReprintPostingRegister(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[AuditControlCode] [nvarchar](13) NULL,
	[Paycode] [nvarchar](30) NULL,
	[Description] [nvarchar](30) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[EmployeeName] [nvarchar](80) NULL,
	[UnitsToPay] [decimal](19,5) NULL,
	[Total] [decimal](19,5) NULL,
	[GrossWagesFlag] bit NULL,
	[Report] [int] NULL,
	[CheckDate] [datetime] NULL,
	[MTD] [decimal](19,5) NULL,
	[YTD] [decimal](19,5) NULL,
	[MTDUnit] [decimal](19,5) NULL,
	[YTDUnit] [decimal](19,5) NULL)

CREATE TABLE #YTDByPayrollCode(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[ClientId] [nvarchar](15) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[Year] [nvarchar](4) NULL,
	[PayrollCode] [nvarchar](7) NULL,
	[YTD] [decimal](19,5) NULL)

CREATE TABLE #YTDByPayrollCodeUnit(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[ClientId] [nvarchar](15) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[Year] [nvarchar](4) NULL,
	[PayrollCode] [nvarchar](7) NULL,
	[YTDUnit] [decimal](19,5) NULL)

CREATE TABLE #MTDByPayrollCode(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[ClientId] [nvarchar](15) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[Year] [nvarchar](4) NULL,
	[PayrollCode] [nvarchar](7) NULL,
	[Month] [int] NULL,
	[MTD] [decimal](19,5) NULL)

CREATE TABLE #MTDByPayrollCodeUnit(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[ClientId] [nvarchar](15) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[Year] [nvarchar](4) NULL,
	[PayrollCode] [nvarchar](7) NULL,
	[Month] [int] NULL,
	[MTDUnit] [decimal](19,5) NULL)

--CREATE INDEX idx_tmp_YTDbPC ON #YTDByPayrollCode(CompanyId, ClientId, EmployeeId, [Year], PayrollCode)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployees(@company, @client, @userid)

-- Build YTD temp table to simplify the join.
INSERT INTO #YTDByPayrollCode(CompanyId, EmployeeID, [Year], PayrollCode, YTD)
select	tblPivot.CompanyID, tblPivot.EmployeeID, tblpivot.[Year], tblPivot.PayrollCode, sum(tblPivot.Value) YTD
FROM (SELECT e.CompanyID, e.[Year], e.EmployeeID, e.PayrollRecordType, e.PayrollCode,
			MonthToDateWages1 as January, MonthToDateWages2 as February, MonthToDateWages3 as March, MonthToDateWages4 as April, 
			MonthToDateWages5 as May, MonthToDateWages6 as June, MonthToDateWages7 as July, MonthToDateWages8 as August, 
			MonthToDateWages9 as September, MonthToDateWages10 as October, MonthToDateWages11 as November, MonthToDateWages12 as December
	  FROM EmployeeTransactionHistoryHDR e
		INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (January, February, March, April, May, June, July, August, September, October, November, December)) as tblPivot
WHERE PayrollRecordType = 1 AND CompanyID = @company
GROUP BY CompanyID, EmployeeID, [Year], PayrollCode

-- Build YTD Unit temp table to simplify the join.
INSERT INTO #YTDByPayrollCodeUnit(CompanyId, EmployeeID, [Year], PayrollCode, YTDUnit)
select	tblPivot.CompanyID, tblPivot.EmployeeID, tblpivot.[Year], tblPivot.PayrollCode, sum(tblPivot.Value) YTD
FROM (SELECT e.CompanyID, e.[Year], e.EmployeeID, e.PayrollRecordType, e.PayrollCode,
			MonthToDateHours1 as January, MonthToDateHours2 as February, MonthToDateHours3 as March, MonthToDateHours4 as April, 
			MonthToDateHours5 as May, MonthToDateHours6 as June, MonthToDateHours7 as July, MonthToDateHours8 as August, 
			MonthToDateHours9 as September, MonthToDateHours10 as October, MonthToDateHours11 as November, MonthToDateHours12 as December
	  FROM EmployeeTransactionHistoryHDR e
		INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (January, February, March, April, May, June, July, August, September, October, November, December)) as tblPivot
WHERE PayrollRecordType = 1 and	 CompanyID = @company
GROUP BY CompanyID, EmployeeID, [Year], PayrollCode

-- Build MTD temp table to simplify the join.
INSERT INTO #MTDByPayrollCode(CompanyId, EmployeeID, [Month], [Year], PayrollCode, MTD)
select	tblPivot.CompanyID, tblPivot.EmployeeID, Month(rtrim(cast(tblPivot.[Month] as varchar(50))) + ' 1 2015') as MM,
		tblpivot.[Year], tblPivot.PayrollCode, sum(tblPivot.Value) MTD
FROM (SELECT e.CompanyID, e.[Year], e.EmployeeID, e.PayrollRecordType, e.PayrollCode,
			MonthToDateWages1 as January, MonthToDateWages2 as February, MonthToDateWages3 as March, MonthToDateWages4 as April, 
			MonthToDateWages5 as May, MonthToDateWages6 as June, MonthToDateWages7 as July, MonthToDateWages8 as August, 
			MonthToDateWages9 as September, MonthToDateWages10 as October, MonthToDateWages11 as November, MonthToDateWages12 as December
	FROM EmployeeTransactionHistoryHDR e
		INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (January, February, March, April, May, June, July, August, September, October, November, December)) as tblPivot
WHERE PayrollRecordType = 1 AND CompanyID = @company
GROUP BY CompanyID, EmployeeID, [Year], [Month], PayrollCode

-- Build MTD Unit temp table to simplify the join.
INSERT INTO #MTDByPayrollCodeUnit(CompanyId, EmployeeID, [Month], [Year], PayrollCode, MTDUnit)
select	tblPivot.CompanyID, tblPivot.EmployeeID, Month(rtrim(cast(tblPivot.[Month] as varchar(50))) + ' 1 2015') as MM, tblpivot.[Year], tblPivot.PayrollCode, --tblPivot.PayrollRecordType,
		sum(tblPivot.Value) MTD
FROM (SELECT e.CompanyID, e.[Year], e.EmployeeID, e.PayrollRecordType, e.PayrollCode,
			MonthToDateHours1 as January, MonthToDateHours2 as February, MonthToDateHours3 as March, MonthToDateHours4 as April, 
			MonthToDateHours5 as May, MonthToDateHours6 as June, MonthToDateHours7 as July, MonthToDateHours8 as August, 
			MonthToDateHours9 as September, MonthToDateHours10 as October, MonthToDateHours11 as November, MonthToDateHours12 as December
	FROM EmployeeTransactionHistoryHDR e
		INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	 )  Months
UNPIVOT (Value for [Month] in (January, February, March, April, May, June, July, August, September, October, November, December)) as tblPivot
WHERE PayrollRecordType = 1 AND CompanyID = @company
GROUP BY CompanyID, EmployeeID, [Year], [Month], PayrollCode


-- Pay Code record type = 1
INSERT INTO #ReprintPostingRegister(AuditControlCode, Paycode, [Description], EmployeeID, EmployeeName,
	UnitsToPay, Total, GrossWagesFlag, Report, CheckDate, YTD, MTD, YTDUnit, MTDUnit)
select distinct h.AuditControlCode, h.PayrollCode, ISNULL(p.[Description],'') [Description], h.EmployeeID, e.FirstName + ' ' + e.LastName [EmployeeName],
	h.UnitsToPay [UnitsToPay], h.TRXAmount [ayPeriodDollars], GrossWagesFlag = CAST(CASE WHEN ep.PayType = 5 THEN ep.ReportAsWages ELSE 1 END AS BIT),
	5 Report, h.CheckDate, tblYTD.YTD, tblMTD.MTD, tblYTDUnit.YTDUnit/100 [YTDUnit], tblMTDUnit.MTDUnit/100 [MTDUnit]
from EmployeeTransactionHistory h
	INNER JOIN #Departments d ON d.Department = h.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = h.EmployeeID
	left outer join Paycodes p on h.PayrollCode = p.PayRecord AND h.CompanyID = p.CompanyID
	left outer join Employees e on e.CompanyID = h.CompanyID and h.EmployeeID = e.EmployeeID
	INNER JOIN EmployeePaycodes ep ON ep.CompanyID = h.CompanyID AND ep.EmployeeID = h.EmployeeID AND ep.PayRecord = h.PayrollCode
	left outer join #YTDByPayrollCode tblYTD on tblYTD.EmployeeID = h.EmployeeID and tblYTD.PayrollCode = h.PayrollCode and tblYTD.[Year] = year(h.CheckDate) 
	left outer join #MTDByPayrollCode tblMTD on tblMTD.EmployeeID = h.EmployeeID and tblMTD.PayrollCode = h.PayrollCode and tblMTD.[Year] = Year(h.CheckDate) and tblMTD.[Month] = Month(h.CheckDate)
	left outer join #YTDByPayrollCodeUnit tblYTDUnit on tblYTDUnit.EmployeeID = h.EmployeeID and tblYTDUnit.PayrollCode = h.PayrollCode and tblYTDUnit.[Year] = year(h.CheckDate) 
	left outer join #MTDByPayrollCodeUnit tblMTDUnit on tblMTDUnit.EmployeeID = h.EmployeeID and tblMTDUnit.PayrollCode = h.PayrollCode and tblMTDUnit.[Year] = Year(h.CheckDate) and tblMTDUnit.[Month] = Month(h.CheckDate)
where h.CompanyID = @company and h.PayrollRecordType = 1 and h.AuditControlCode IN 
		(select AuditControlCode
		 from Invoices i
			left outer join InvoicePayrolls cip on i.CompanyID = cip.CompanyID and i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
		 where i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice))
order by h.PayrollCode

select * from #ReprintPostingRegister

DROP TABLE #Departments
DROP TABLE #MTDByPayrollCode
DROP TABLE #MTDByPayrollCodeUnit
DROP TABLE #ReprintPostingRegister
DROP TABLE #YTDByPayrollCode
DROP TABLE #YTDByPayrollCodeUnit

