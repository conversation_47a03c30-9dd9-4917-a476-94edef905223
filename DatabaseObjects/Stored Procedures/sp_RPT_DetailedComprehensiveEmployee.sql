
/*
*************************************************************************
**** Created by <PERSON> 
**** Date: 10/09/2015.
**** Purpose: Generates data for the SSRS report Detailed Comprehensive Employee
**** Multi-Date with MTD and YTD totals. 
****   YTD Based on end date of date range from beginning of year.
****   MTD based on end date of date range from first of end date's month.
****
*************************************************************************
*/


-- sp_RPT_DetailedComprehensiveEmployee 1, '016', NULL, NULL, '2016-07-01', '2016-11-11', 'BrianN'

CREATE PROCEDURE [dbo].[sp_RPT_DetailedComprehensiveEmployee]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@EmployeeStart VARCHAR(15) = NULL,
	@EmployeeEnd VARCHAR(15) = NULL,
	@startdate DATETIME	= NULL,	
	@enddate DATETIME	= NULL,
	@userid NVARCHAR(20)
)
AS	

CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[CheckNumber] [int] NULL,
	[CheckDate] [date] NULL,
	[PaymentAdjustmentNumber] [int] NULL,
	[Description] [nvarchar](256) NULL,
	[Rate] [decimal](19,5) NULL,
	[EarnAmount] [decimal](19,5) NULL,
	[UnitsLast] [decimal](19,5) NULL,
	[UnitsMTD] [decimal](19,5) NULL,
	[UnitsYTD] [decimal](19,5) NULL,
	[EarningsLast] [decimal](19,5) NULL,
	[EarningsMTD] [decimal](19,5) NULL,
	[EarningsYTD] [decimal](19,5) NULL,
	[NetPay] [decimal](19,5) NULL,
	[TaxAmount] [decimal](19,5) NULL,
	[TaxLast] [decimal](19,5) NULL,
	[TaxMTD] [decimal](19,5) NULL,
	[TaxYTD] [decimal](19,5) NULL,
	[TaxStatus] [nvarchar](30) NULL,
	[Exemptions] [int] NULL,
	[DeductAmount] [decimal](19,5) NULL,
	[DeductionsLast] [decimal](19,5) NULL,
	[DeductionsMTD] [decimal](19,5) NULL,
	[DeductionsYTD] [decimal](19,5) NULL,
	[BenefitAmount] [decimal](19,5) NULL,
	[BenefitsLast] [decimal](19,5) NULL,
	[BenefitsMTD] [decimal](19,5) NULL,
	[BenefitsYTD] [decimal](19,5) NULL,
	[TotalAmount] [decimal](19,5) NULL,
	[NetLast] [decimal](19,5) NULL,
	[NetMTD] [decimal](19,5) NULL,
	[NetYTD] [decimal](19,5) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)

CREATE TABLE #EmployeeHeader(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [int] NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](15) NULL,
	[HireDate] [date] NULL,
	[StartDate] [date] NULL,
	[BirthDate] [date] NULL,
	[Sex] [nvarchar](6) NULL,
	[ACAStatus] [int] NULL,
	[PartTime] [int] NULL,
	[EmploymentStatus] [nvarchar](50) NULL,
	[Position] [nvarchar](6) NULL,
	[Department] [nvarchar](6) NULL,
	[WorkersComp] [nvarchar](6) NULL,
	[SUTAState] [nvarchar](2) NULL,
	[DivisionCode] [nvarchar](6) NULL,
	[Vacation] [decimal](19,5) NULL,
	[Sick] [decimal](19,5) NULL,
	[PTO] [decimal](19,5) NULL,
	[VacationLiability] [decimal](19,5) NULL,
	[SickLiability] [decimal](19,5) NULL,
	[PTOLiability] [decimal](19,5) NULL,
	[Address1] [nvarchar](60) NULL,
	[Address2] [nvarchar](60) NULL,
	[City] [nvarchar](35) NULL,
	[State] [nvarchar](29) NULL,
	[Zip] [nvarchar](10) NULL,
	[Phone] [nvarchar](14) NULL,
	[ClientName] [nvarchar](64) NULL,
	[VacationAvailable] [decimal](19,5) NULL,
	[SickAvailable] [decimal](19,5) NULL, 
	[PTOAvailable] [decimal](19,5) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)

CREATE TABLE #EmployeePayCodeInfo(
	[PayUnitPeriod] [int] NULL,
	[PayPeriodFactor] [int] NULL	-- Number to multiply to annualize.
	)

CREATE TABLE #EmployeePTOInfo(
	[CompanyId] [int] NULL,
	[EmployeeId] [nvarchar](15) NULL,	
	[PTOType] [int] NULL,	
	[Description] [nvarchar](256) NULL,	
	[AvailableHours] [decimal](15,2) NULL,	
	[Inactive] [int] NULL,	
	[PayType] [int] NULL,	
	[PayRecord] [nvarchar](15) NULL,	
	[PayRateAmount] [decimal](15,2) NULL,	
	[PayPeriodFactor] [int] NULL,	
	[Liability] [decimal](15,2) NULL	
	)

CREATE TABLE #Departments(Department NVARCHAR(6))
INSERT INTO #Departments SELECT Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

CREATE TABLE #EmployeePTO(
	[CompanyId] [int] NULL,
	[EmployeeId] [nvarchar](15) NULL,	
	[VacationHours] [decimal](15,2) NULL,	
	[VacationLiability] [decimal](15,2) NULL,
	[SickHours] [decimal](15,2) NULL,	
	[SickLiability] [decimal](15,2) NULL,
	[PTOHours] [decimal](15,2) NULL,	
	[PTOLiability] [decimal](15,2) NULL	
	)
	

-- Build #EmployeePayCodeInfo.
Insert into #EmployeePayCodeInfo(PayUnitPeriod, PayPeriodFactor)
select 1, 52		-- Weekly.
union
select 2, 26		-- BiWeekly.
union
select 3, 24		-- SemiMonthly.
union
select 4, 12		-- Monthly.
union
select 5, 4			-- Quarterly.
union
select 6, 2			-- SemiAnnually.
union
select 7, 1			-- Annually.

-- Process the PTO.
insert into #EmployeePTOInfo(CompanyId, EmployeeId, PTOType, [Description], AvailableHours, Inactive, PayType, PayRecord, PayRateAmount, PayPeriodFactor, Liability)
select ept.CompanyID, ept.EmployeeID, ept.PTOType, ept.[Description], ept.AvailableHours/100, ept.Inactive, ep.PayType, ep.PayRecord, ep.PayRateAmount, i.PayPeriodFactor, 
		case when ep.PayUnit = 'Hourly' THEN ept.AvailableHours * ep.PayRateAmount/100 ELSE ept.AvailableHours * ep.PayRateAmount/100*i.PayPeriodFactor/e.WorkHoursPerYear  END [Liability] 
from EmployeePTOTypes ept
	left outer join DarwiNetCodes dnc1 on dnc1.Value = ept.PTOType and dnc1.CodeType = 'PTOTypes'
	left outer join DarwiNetCodes dnc2 on dnc2.CodeText = dnc1.CodeText and dnc2.CodeType = 'PayTypes'
	left outer join EmployeePaycodes ep on ep.CompanyID = ept.CompanyID and ep.EmployeeID = ept.EmployeeID and ep.PayType = dnc2.Value
	left outer join #EmployeePayCodeInfo i on i.PayUnitPeriod = ep.PayUnitPeriod
	inner join Employees e on e.CompanyID = ept.CompanyID and e.EmployeeID = ept.EmployeeID
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ept.EmployeeID
where ept.PTOType = 1 and ep.Inactive = 0 and e.ClientID = @client

insert into #EmployeePTOInfo(CompanyId, EmployeeId, PTOType, [Description], AvailableHours, Inactive, PayType, PayRecord, PayRateAmount, PayPeriodFactor, Liability)
select ept.CompanyID, ept.EmployeeID, ept.PTOType, ept.[Description], ept.AvailableHours/100, ept.Inactive, ep.PayType, ep.PayRecord, ep.PayRateAmount, i.PayPeriodFactor, 
		case when ep.PayUnit = 'Hourly' THEN ept.AvailableHours * ep.PayRateAmount/100 ELSE ept.AvailableHours * ep.PayRateAmount/100*i.PayPeriodFactor/e.WorkHoursPerYear  END [Liability] 
from EmployeePTOTypes ept
	left outer join DarwiNetCodes dnc1 on dnc1.Value = ept.PTOType and dnc1.CodeType = 'PTOTypes'
	left outer join DarwiNetCodes dnc2 on dnc2.CodeText = dnc1.CodeText and dnc2.CodeType = 'PayTypes'
	left outer join EmployeePaycodes ep on ep.CompanyID = ept.CompanyID and ep.EmployeeID = ept.EmployeeID and ep.PayType = dnc2.Value
	left outer join #EmployeePayCodeInfo i on i.PayUnitPeriod = ep.PayUnitPeriod
	inner join Employees e on e.CompanyID = ept.CompanyID and e.EmployeeID = ept.EmployeeID
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ept.EmployeeID
where ept.PTOType = 2 and ep.Inactive = 0 and e.ClientID = @client

insert into #EmployeePTOInfo(CompanyId, EmployeeId, PTOType, [Description], AvailableHours, PayRateAmount, Liability)
select ept.CompanyID, ept.EmployeeID, 3, ept.[Description], SUM(ept.AvailableHours/100), SUM(ep.PayRateAmount),
		SUM(case when ep.PayUnit = 'Hourly' THEN ept.AvailableHours * ep.PayRateAmount/100 ELSE ept.AvailableHours * ep.PayRateAmount/100*i.PayPeriodFactor/e.WorkHoursPerYear  END) [Liability] 
from EmployeePTOTypes ept
	left outer join DarwiNetCodes dnc1 on dnc1.Value = ept.PTOType and dnc1.CodeType = 'PTOTypes'
	left outer join DarwiNetCodes dnc2 on dnc2.CodeText = dnc1.CodeText and dnc2.CodeType = 'PayTypes'
	left outer join EmployeePaycodes ep on ep.CompanyID = ept.CompanyID and ep.EmployeeID = ept.EmployeeID and ep.PayType = dnc2.Value
	left outer join #EmployeePayCodeInfo i on i.PayUnitPeriod = ep.PayUnitPeriod
	inner join Employees e on e.CompanyID = ept.CompanyID and e.EmployeeID = ept.EmployeeID
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ept.EmployeeID
where ept.PTOType in (3,4,5,6,7,8,9,10,11,12) and ep.Inactive = 0 and e.ClientID = @client
GROUP BY ept.CompanyID, ept.EmployeeID, ept.[Description]

INSERT INTO #EmployeePTO(CompanyId, EmployeeId, VacationHours, VacationLiability, SickHours, SickLiability, PTOHours, PTOLiability )
SELECT t.CompanyId, t.EmployeeId,
		SUM(VacationHours) VacationHours, SUM(VacationLiability) VacationLiability,
		SUM(SickHours) SickHours, SUM(SickLiability) SickLiability,
		SUM(PTOHours) PTOHours, SUM(PTOLiability) PTOLiability
FROM (
SELECT CompanyId, EmployeeId, AvailableHours VacationHours, Liability VacationLiability, 0 SickHours, 0 SickLiability, 0 PTOHours, 0 PTOLiability
FROM #EmployeePTOInfo i	
WHERE i.PTOType = 1
	UNION
SELECT CompanyId, EmployeeId, 0 VacationHours, 0 VacationLiability, AvailableHours SickHours, Liability SickLiability, 0 PTOHours, 0 PTOLiability
FROM #EmployeePTOInfo i	
WHERE i.PTOType = 2
	UNION
SELECT CompanyId, EmployeeId, 0 VacationHours, 0 VacationLiability, 0 SickHours, 0 SickLiability, AvailableHours PTOHours, Liability PTOLiability
FROM #EmployeePTOInfo i	
WHERE i.PTOType = 3
) t
GROUP BY t.CompanyId, t.EmployeeId

IF (@EmployeeEnd is null and @EmployeeStart IS NOT NULL)
	select @Employeeend = @EmployeeStart

-- * * * * *      S E C T I O N  1 :  Earnings     * * * * * * * 
-- Earnings by Payroll Type

insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, [Description], Rate, EarnAmount, UnitsLast, UnitsMTD, UnitsYTD, EarningsLast, EarningsMTD, EarningsYTD, Section, Sort)
select e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName as EmployeeName, e.SSN, eth.PayrollCode [Description], MAX(eth.PayRate), SUM(eth.TRXAmount),
		UnitsLast = ISNULL((SELECT SUM(UnitsToPay)
							FROM EmployeeTransactionHistory
							WHERE CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 1
							and CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
							ORDER BY CheckDate DESC)),0),
							  --and AuditControlCode = (SELECT TOP 1 AuditControlCode FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID
							--ORDER BY CheckDate DESC, CheckNumber DESC) AND CheckDate <= @enddate),0),
		UnitsMTD = (select ISNULL(sum(UnitsToPay),0)
					from EmployeeTransactionHistory 
					where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 1 and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),

		UnitsYTD = (select ISNULL(sum(UnitsToPay),0)
					from EmployeeTransactionHistory 
					where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 1 and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),

		EarningsLast = ISNULL((SELECT TOP 1 SUM(TRXAmount)
							   FROM EmployeeTransactionHistory
							   WHERE CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 1
								AND CheckDate = (SELECT TOP 1 CheckDate  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),0),

		EarningsMTD = (	select ISNULL(sum(TRXAmount),0)
						from EmployeeTransactionHistory 
						where CompanyID = @company and EmployeeID = e.EmployeeID and  PayrollCode = eth.PayrollCode and PayrollRecordType = 1 and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),

		EarningsYTD = (select ISNULL(sum(TRXAmount),0)
					   from EmployeeTransactionHistory 
					   where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and  PayrollRecordType = 1 and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0,  CheckDate)) <= @enddate), 1 [Section], 1 [Sort]
from	EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where eth.CompanyID = @company and ce.ClientID = @client and eth.PayrollRecordType = 1 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, PayrollCode
order by EmployeeID, Sort

-- * * * * *      S E C T I O N  2 :  Taxes     * * * * * * * 
-- State Tax values.
insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, [Description], TaxAmount, TaxLast, TaxMTD, TaxYTD, TaxStatus, Exemptions, Section, Sort)
select e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName, e.SSN, PayrollCode,
		SUM((case when PayrollRecordType = 4 then TRXAmount else 0.00 end)) as [State],
		TaxLast = (SELECT ISNULL(SUM(TRXAmount),0) 
				   from EmployeeTransactionHistory 
				   where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 4 
						AND CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
		TaxMTD = (select ISNULL(sum(TRXAmount),0)
				  from EmployeeTransactionHistory 
				  where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 4 and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		TaxYTD = (Select ISNULL(sum(TRXAmount),0)
				  from EmployeeTransactionHistory 
				  where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 4 and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		TaxStatus = (SELECT TaxFilingStatus
					 FROM EmployeeStateTaxes est
					 WHERE est.CompanyID = @company AND est.EmployeeID = e.EmployeeID AND est.StateCode = PayrollCode),
		Exemptions = (SELECT Dependents
					  FROM EmployeeStateTaxes est
					  WHERE est.CompanyID = @company AND est.EmployeeID = e.EmployeeID AND est.StateCode = PayrollCode),
		5 [Section], 1 [Sort]	
from EmployeeTransactionHistory eth 
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where eth.CompanyID = @company and ce.ClientID = @client and PayrollRecordType = 4 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, PayrollCode
order by EmployeeID, Sort

-- Local Tax values.
insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, [Description], TaxAmount, TaxLast, TaxMTD, TaxYTD, Section, Sort)
select e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName, e.SSN, PayrollCode,
		SUM((case when PayrollRecordType = 5 then TRXAmount else 0.00 end)) as [Local],
		TaxLast = (select IsNull(SUM(TRXAmount),0) 
				   from EmployeeTransactionHistory 
				   where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 5 
						AND CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
		TaxMTD = (select ISNULL(sum(TRXAmount),0)
				  from EmployeeTransactionHistory 
				  where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 5 and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		TaxYTD = (select ISNULL(sum(TRXAmount),0)
				  from EmployeeTransactionHistory 
				  where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 5 and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		6 [Section], 1 [Sort]	
from EmployeeTransactionHistory eth 
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where eth.CompanyID = @company and ce.ClientID = @client and PayrollRecordType = 5 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, PayrollCode
order by EmployeeID, Sort

-- FICA SS Witholding
insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, [Description], TaxAmount, TaxLast, TaxMTD, TaxYTD, Section, Sort)
select 
	e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName, e.SSN, 'FICA SS', 
	SUM(ech.EmployerFICASSWithholding) as [TaxAmount],
	TaxLast = (select IsNull(sum(EmployerFICASSWithholding),0) 
				from EmployeeCheckHistory 
				where EmployeeID = e.EmployeeID and
					 CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
	TaxMTD = (select ISNULL(sum(EmployerFICASSWithholding),0)
			  from EmployeeCheckHistory 
			  where EmployeeID = e.EmployeeID and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate), 
	TaxYTD = (select ISNULL(sum(EmployerFICASSWithholding),0)
			  from EmployeeCheckHistory 
			  where EmployeeID = e.EmployeeID and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
	4 [Section], 2 [Sort]	
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = ech.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where ech.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN
order by EmployeeID, Sort

-- FICA Med Witholding
insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, [Description], TaxAmount, TaxLast, TaxMTD, TaxYTD, Section, Sort)
select 
	e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName, e.SSN, 'FICA Med', 
	SUM(ech.EmployerFICAMWithholding) as [TaxAmount],
	TaxLast = (select IsNull(sum(EmployerFICAMWithholding),0) 
				from EmployeeCheckHistory 
				where EmployeeID = e.EmployeeID 
				AND CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
	TaxMTD = (select ISNULL(sum(EmployerFICAMWithholding),0)
			  from EmployeeCheckHistory 
			  where EmployeeID = e.EmployeeID and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and CheckDate <= @enddate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) > @startdate ), 
	TaxYTD = (select ISNULL(sum(EmployerFICAMWithholding),0)
			  from EmployeeCheckHistory 
			  where EmployeeID = e.EmployeeID and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate ), 
	4 [Section], 3 [Sort]	
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = ech.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where ech.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN
order by EmployeeID, Sort

-- Federal Tax Witholding
insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, [Description], TaxAmount, TaxLast, TaxMTD, TaxYTD, TaxStatus, Exemptions, Section, Sort)
select 
	e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName, e.SSN, 'Federal', 
	SUM(ech.FederalWithholdingPayRun) as [TaxAmount],
	TaxLast = (select IsNull(sum(FederalWithholdingPayRun),0) 
				from EmployeeCheckHistory 
				where EmployeeID = e.EmployeeID and
					 CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
	TaxMTD = (select ISNULL(sum(FederalWithholdingPayRun),0)
			  from EmployeeCheckHistory 
			  where EmployeeID = e.EmployeeID and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate ), 
	TaxYTD = (select ISNULL(sum(FederalWithholdingPayRun),0)
			  from EmployeeCheckHistory 
			  where EmployeeID = e.EmployeeID and Year(CheckDate) = Year(@enddate) and  DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate ),
	TaxStatus =  e.FederalFilingStatus, Exemptions = e.FederalExemptions, 4 [Section], 1 [Sort]	
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = ech.CompanyID and  ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where ech.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, e.FederalFilingStatus, e.FederalExemptions
order by EmployeeID, Sort

-- * * * * *      S E C T I O N  3 :  Deductions     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, Description, DeductAmount, DeductionsLast, DeductionsMTD, DeductionsYTD, Section, Sort)
select e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName as EmployeeName, e.SSN, eth.PayrollCode [Description], SUM(eth.TRXAmount), 
		DeductionsLast = (select IsNull(sum(TRXAmount),0) 
						  from EmployeeTransactionHistory 
						  where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 2 
							AND CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
		DeductionsMTD = (select ISNULL(sum(TRXAmount),0)
						 from EmployeeTransactionHistory 
						 where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 2 and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		DeductionsYTD = (select ISNULL(sum(TRXAmount),0)
						 from EmployeeTransactionHistory 
						 where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 2 and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		2 [Section], 1 [Sort]
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where eth.CompanyID = @company and ce.ClientID = @client and eth.PayrollRecordType = 2 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, PayrollCode
order by EmployeeID, Sort


-- * * * * *      S E C T I O N  4 :  Benefits     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, Description, BenefitAmount, BenefitsLast, BenefitsMTD, BenefitsYTD, Section, Sort)
select e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName as EmployeeName, e.SSN, eth.PayrollCode [Description], SUM(eth.TRXAmount),
		BenefitsLast = (select IsNull(sum(TRXAmount),0) 
						from EmployeeTransactionHistory 
						where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 3 
						AND CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
		BenefitsMTD = (select ISNULL(sum(TRXAmount),0)
					   from EmployeeTransactionHistory 
					   where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 3 and Month(CheckDate) = Month(@enddate) and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		BenefitsYTD = (select ISNULL(sum(TRXAmount),0)
					   from EmployeeTransactionHistory 
					   where CompanyID = @company and EmployeeID = e.EmployeeID and PayrollCode = eth.PayrollCode and PayrollRecordType = 3 and Year(CheckDate) = Year(@enddate) and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate),
		3 [Section], 1 [Sort]
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
where eth.CompanyID = @company and ce.ClientID = @client and eth.PayrollRecordType = 3 and DATEADD(dd, 0, DATEDIFF(dd, 0, eth.CheckDate)) between @startdate and @enddate
group by e.CompanyID, e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, PayrollCode
order by EmployeeID, Sort

INSERT INTO #EmployeePayDetail(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, [Description], NetLast, NetMTD, NetYTD, Section, Sort)
SELECT e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName, e.SSN, 'Net', 
	NetLast = (SELECT ISNULL(SUM(NetWagesPayRun),0) FROM EmployeeCheckHistory WHERE EmployeeID = e.EmployeeID 
							AND CheckDate = (SELECT TOP 1 DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate))  FROM EmployeeTransactionHistory WHERE EmployeeID = e.EmployeeID and CheckDate >= @startdate and DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate
								ORDER BY CheckDate DESC)),
	NetMTD  = (SELECT ISNULL(SUM(NetWagesPayRun),0) FROM EmployeeCheckHistory WHERE EmployeeID = e.EmployeeID AND Month(CheckDate) = Month(@enddate) AND Year(CheckDate) = Year(@enddate) AND DATEADD(dd, 0, DATEDIFF(dd, 0,  CheckDate)) <= @enddate ),
	NetYTD  = (SELECT ISNULL(SUM(NetWagesPayRun),0) FROM EmployeeCheckHistory WHERE EmployeeID = e.EmployeeID AND Year(CheckDate) = Year(@enddate) AND DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) <= @enddate ),
	4 [Section], 1 [Sort]	
FROM EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	INNER JOIN Employees e ON e.EmployeeID = ech.EmployeeID
	LEFT OUTER JOIN ClientEmployees ce ON ce.CompanyID = ech.CompanyID AND ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
WHERE ech.CompanyID = @company AND ce.ClientID = @client AND DATEADD(dd, 0, DATEDIFF(dd, 0, ech.CheckDate)) BETWEEN @startdate AND @enddate
GROUP BY e.CompanyID, e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, e.FederalFilingStatus, e.FederalExemptions
ORDER BY EmployeeID, Sort

-- * * * * *      S E C T I O N  0 :  Header Data     * * * * * * * 
INSERT INTO #EmployeeHeader(EmployeeID, CompanyId, EmployeeName, EmployeeSSN, Phone, 
							HireDate, StartDate, BirthDate, Sex, 
							EmploymentStatus, Position, Department, WorkersComp, SUTAState, DivisionCode, 
							Vacation, Sick, PTO, 
							Address1, Address2, City, State, Zip, ClientName,
							[VacationAvailable], [SickAvailable], PTOAvailable,
							VacationLiability, SickLiability, PTOLiability
						   )

select e.EmployeeID, e.CompanyID, e.LastName + ', ' + e.FirstName as EmployeeName, '***-**-' + substring(e.SSN, 6,4) [SSN], ISNULL(ea.Phone1,'') [Phone],
		CONVERT(VARCHAR(10), e.OriginalHireDate, 101) AS OriginalHireDate,
		CONVERT(VARCHAR(10), e.StartDate, 101) AS StartDate,
		CONVERT(VARCHAR(10), e.BirthDate, 101) AS BirthDate,
		case when e.Gender = 1 then 'Male' when e.Gender = 2 then 'Female' else 'N/A' end [Gender], 
		dc.CodeText [Status], e.Position, e.Department, e.WorkersComp, e.SUTAState, e.DivisionCode,
		VacationAccrualAmount = (SELECT ISNULL(SUM(VacationTimeAccrued),'0.00')
								 FROM EmployeeAdditionalTransactionHistory
								 WHERE CompanyID = e.CompanyID AND EmployeeID = e.EmployeeID
								 GROUP BY CompanyID, EmployeeID
								),
		SickTimeAccrualAmount = (SELECT ISNULL(SUM(SickTimeAccrued),'0.00')
								 FROM EmployeeAdditionalTransactionHistory
								 WHERE CompanyID = e.CompanyID AND EmployeeID = e.EmployeeID
								 GROUP BY CompanyID, EmployeeID
								),
		[PTO1-10] = (SELECT ISNULL(SUM(PTOTimeAccrued01 + PTOTimeAccrued02 + PTOTimeAccrued03 + PTOTimeAccrued04 + PTOTimeAccrued05 +
								PTOTimeAccrued06 + PTOTimeAccrued07 + PTOTimeAccrued08 + PTOTimeAccrued09 + PTOTimeAccrued10),'0.00')
					 FROM EmployeeAdditionalTransactionHistory
					 WHERE CompanyID = e.CompanyID AND EmployeeID = e.EmployeeID
					 GROUP BY CompanyID, EmployeeID
					),
		ea.Address1, ea.Address2, ea.City, ea.[State], ea.Zip, c.ClientName, ISNULL(i.VacationHours,0) [VacationAvailable],
		ISNULL(i.SickHours,0) [SickAvailable], ISNULL(i.PTOHours,0) [PTOAvailable], ISNULL(i.VacationLiability,0) [LiabilityVacation],
		ISNULL(i.SickLiability,0) [LiabilitySick], ISNULL(i.PTOLiability,0) [LiabilityPTO]
from Employees e
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = e.EmployeeID
	inner join EmployeeAddresses ea on ea.CompanyID = e.CompanyID and ea.EmployeeID = e.EmployeeID and e.AddressCode = ea.AddressCode
	inner join DarwiNetCodes dc on dc.Value = e.PartTime and dc.CodeType = 'EmploymentType'
	inner join Clients c on c.CompanyID = e.CompanyID and c.ClientID = e.ClientID
	left outer join #EmployeePTO i on i.CompanyId = e.CompanyID and i.EmployeeId = e.EmployeeID
where  e.CompanyID = @company and e.ClientID = @client

select pd.CompanyId, pd.Id, CheckNumber, CheckDate, pd.PaymentAdjustmentNumber, [Description], Rate, UnitsLast, UnitsMTD, UnitsYTD, EarningsLast,
	   EarningsMTD, EarningsYTD, TaxLast, TaxMTD, TaxYTD, TaxStatus, Exemptions, DeductionsLast, DeductionsMTD, DeductionsYTD, BenefitsLast,
	   BenefitsMTD, BenefitsYTD, NetLast, NetMTD, NetYTD, pd.Section, pd.Sort, eh.EmployeeName, eh.EmployeeID, eh.EmployeeSSN [EmployeeSSN],
	   eh.Phone, eh.ClientName, eh.HireDate, eh.StartDate, eh.BirthDate, eh.Sex, eh.EmploymentStatus, eh.Position, eh.Department, eh.WorkersComp,
	   eh.SUTAState, eh.DivisionCode, eh.Vacation, eh.VacationAvailable, eh.VacationLiability, eh.Sick, eh.SickAvailable, eh.SickLiability, eh.PTO,
	   eh.PTOAvailable, eh.PTOLiability, eh.Address1, eh.Address2, eh.City, eh.[State], eh.Zip
from #EmployeePayDetail pd
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = pd.EmployeeID
	inner join #EmployeeHeader eh on eh.CompanyId = pd.CompanyId and eh.EmployeeID = pd.EmployeeID
WHERE (pd.CompanyID = @company) and (pd.EmployeeID between @EmployeeStart and @EmployeeEnd OR @EmployeeStart IS NULL)
order by pd.EmployeeID, pd.Section, pd.Sort, [Description]

OPTION (RECOMPILE)

