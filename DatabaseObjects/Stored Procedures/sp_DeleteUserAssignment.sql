-- =============================================
-- Author:		<PERSON>
-- Create date: 4/15/2016
-- Description:	Use to delete Role Access Assignments from Client/Employee users. 
-- =============================================
CREATE PROCEDURE [dbo].[sp_DeleteUserAssignment]
	-- Add the parameters for the stored procedure here
	@Company int,
	@Client nvarchar(15),
	@Employee nvarchar(15),
	@UserID nvarchar(20)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
    -- Insert statements for procedure here
	Delete from UserRoleDashBoard where UserID = @UserID AND CompanyID = @Company AND ClientID = @Client
	Delete from UserRoleMenuAccessOverride where UserID = @UserID AND CompanyID = @Company AND ClientID = @Client and EmployeeID = @Employee
	Delete from UserToDoItems where UserID = @UserID AND CompanyID = @Company AND ClientID = @Client and EmployeeID = @Employee
	Update Users Set DefaultAccess = NULL WHERE DefaultAccess IN (SELECT id from UserRoleClientEmployeeAssignment WHERE UserID = @UserID AND CompanyID = @Company AND ClientID = @Client and EmployeeID = @Employee) 
	Delete from UserRoleClientEmployeeAssignment where UserID = @UserID AND CompanyID = @Company AND ClientID = @Client and EmployeeID = @Employee
END
