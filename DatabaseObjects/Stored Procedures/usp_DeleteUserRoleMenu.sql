-- =============================================
-- Author:		<PERSON>
-- Create date: 1/5/16
-- Description:	Delete existing UserRoleMenueAccessOverride Records
-- =============================================
CREATE PROCEDURE [dbo].[usp_DeleteUserRoleMenu]
	-- Add the parameters for the stored procedure here
	@UserID nvarchar(20),
	@RoleID nvarchar(50),
	@CompanyID int,
	@ClientID nvarchar(15),
	@EmployeeID nvarchar(15)
	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
    -- Insert statements for procedure here
	Delete from UserRoleMenuAccessOverride 
		where UserID = @UserID 
			and RoleID = @RoleID
			and CompanyID = @CompanyID 
			and ClientID = @ClientID 
			and EmployeeID = @EmployeeID
END

