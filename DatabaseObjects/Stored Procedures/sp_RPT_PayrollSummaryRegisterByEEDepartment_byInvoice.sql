

/*
*************************************************************************
**** Created by <PERSON> & <PERSON>, Jr.
**** Date: 11/12/2015.
**** Purpose: Invoice-driven version of Payroll Summary Register by Employee / Department
**** 
****
*************************************************************************
*/

-- sp_RPT_PayrollSummaryRegisterByEEDepartment_byInvoice 1, 'RP1', 8, 'BrianN'

CREATE PROCEDURE [dbo].[sp_RPT_PayrollSummaryRegisterByEEDepartment_byInvoice]  
(
	@company INT,
	@client VARCHAR(15),
	@invoice int,
	@userid NVARCHAR(20)
)
AS	

DECLARE @companyInternal INT = @company,
		@clientInternal VARCHAR(15) = @client,
		@invoiceInternal int = @invoice,
		@useridInternal NVARCHAR(20) = @userid

IF 1=0 BEGIN
	SET FMTONLY OFF
END

CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[Department] [nvarchar](50) NULL,
	[DepartmentName] [nvarchar](50) NULL,
	[Description] [nvarchar](30) NULL,
	[Units] [decimal](19,5) NULL,
	[Rate] [decimal](19,5) NULL,
	[EarnAmount] [decimal](19,5) NULL,
	[GrossWagesFlag] bit NULL,
	[NetPay] [decimal](19,5) NULL,
	[TaxAmount] [decimal](19,5) NULL,
	[DeductAmount] [decimal](19,5) NULL,
	[BenefitAmount] [decimal](19,5) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)

CREATE TABLE #EmployeePayExtras(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[NetPay] [decimal](19,5) NULL
	)

CREATE TABLE #FICASSAllocations(
    [Id] [int] IDENTITY(1,1) NOT NULL,
    [EmployeeID] [varchar](50) NOT NULL,
    [HomeDepartment] [varchar](50) NULL,
    [CheckNumber] [varchar](50) NULL,
	[FICASSPaymentAdjustmentNumber] [int] NULL,
    [Department] [varchar](50) NULL,
    [GrossWageAmount] [decimal](19,5) NULL,
    [GrossWageTotal] [decimal](19,5) NULL,
    [GrossWagePct] [decimal](19,5) NULL,
    [FICASSTotal] [decimal](19,5) NULL,
    [FICASSAllocated] [decimal](19,5) NULL,
    [FICASSAllocatedTotal] [decimal](19,5) NULL,
    [FICASSAllocatedDifference] [decimal](19,5) NULL,
    [FICASSAdjusted] [decimal](19,5) NULL )

CREATE TABLE #FICAMedAllocations(
    [FICAMedId] [int] IDENTITY(1,1) NOT NULL,
    [FICAMedEmployeeID] [varchar](50) NOT NULL,
    [FICAMedHomeDepartment] [varchar](50) NULL,
    [FICAMedCheckNumber] [varchar](50) NULL,
	[FICAMedPaymentAdjustmentNumber] [int] NULL,
    [FICAMedDepartment] [varchar](50) NULL,
    [FICAMedGrossWageAmount] [decimal](19,5) NULL,
    [FICAMedGrossWageTotal] [decimal](19,5) NULL,
    [FICAMedGrossWagePct] [decimal](19,5) NULL,
    [FICAMedTotal] [decimal](19,5) NULL,
    [FICAMedAllocated] [decimal](19,5) NULL,
    [FICAMedAllocatedTotal] [decimal](19,5) NULL,
    [FICAMedAllocatedDifference] [decimal](19,5) NULL,
    [FICAMedAdjusted] [decimal](19,5) NULL )

CREATE TABLE #FedAllocations(
    [FedId] [int] IDENTITY(1,1) NOT NULL,
    [FedEmployeeID] [varchar](50) NOT NULL,
    [FedHomeDepartment] [varchar](50) NULL,
    [FedCheckNumber] [varchar](50) NULL,
	[FedPaymentAdjustmentNumber] [int] NULL,
    [FedDepartment] [varchar](50) NULL,
    [FedGrossWageAmount] [decimal](19,5) NULL,
    [FedGrossWageTotal] [decimal](19,5) NULL,
    [FedGrossWagePct] [decimal](19,5) NULL,
    [FedTotal] [decimal](19,5) NULL,
    [FedAllocated] [decimal](19,5) NULL,
    [FedAllocatedTotal] [decimal](19,5) NULL,
    [FedAllocatedDifference] [decimal](19,5) NULL,
    [FedAdjusted] [decimal](19,5) NULL )

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@companyInternal, @clientInternal, @useridInternal)
INSERT INTO #Departments(DivisionID, Department) VALUES ('','')

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@companyInternal, @clientInternal, @useridInternal)

declare @iEnd int = NULL

-- * * * * *      S E C T I O N  1 :  Earnings     * * * * * * * 
-- Earnings by Payroll Type
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, [Description], Units, Rate, EarnAmount, GrossWagesFlag, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, dp.[Description], eth.PayrollCode, SUM(eth.UnitsToPay),
	eth.PayRate, SUM(eth.TRXAmount), GrossWagesFlag = CAST(CASE WHEN ep.PayType = 5 THEN ep.ReportAsWages ELSE 1 END AS BIT), Section = 1, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join  ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
	inner join Departments dp on dp.CompanyID = @companyInternal and dp.Department = eth.Department
	left outer join InvoicePayrolls ip on ip.CompanyID = eth.CompanyID and ip.AuditControlCode = eth.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
	INNER JOIN EmployeePaycodes ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode
where eth.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoiceInternal) and eth.PayrollRecordType = 1 and eth.PayRate <> 0
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, eth.Department, PayrollCode, dp.[Description], ip.InvoiceNumber, eth.PayRate, ep.PayType, ep.ReportAsWages
order by EmployeeID, Sort

insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, DepartmentName, [Description], Units, Rate, EarnAmount, GrossWagesFlag, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, dp.[Description], eth.PayrollCode, SUM(eth.UnitsToPay),
	CASE SUM(eth.UnitsToPay) WHEN 0 THEN 0 ELSE SUM(eth.TRXAmount)/SUM(eth.UnitsToPay) END, SUM(eth.TRXAmount), GrossWagesFlag = CAST(CASE WHEN ep.PayType = 5 THEN ep.ReportAsWages ELSE 1 END AS BIT), Section = 1, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join  ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
	inner join Departments dp on dp.CompanyID = @companyInternal and dp.Department = eth.Department
	left outer join InvoicePayrolls ip on ip.CompanyID = eth.CompanyID and ip.AuditControlCode = eth.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
	INNER JOIN EmployeePaycodes ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode
where eth.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal) and eth.PayrollRecordType = 1 and eth.PayRate = 0
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, eth.Department, PayrollCode, dp.[Description], ip.InvoiceNumber, ep.PayType, ep.ReportAsWages
order by EmployeeID, Sort
-- * * * * *      S E C T I O N  2 :  Taxes     * * * * * * * 
-- State Tax values.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, PayrollCode,
	[State] = SUM((case when PayrollRecordType = 4 then TRXAmount else 0.00 end)), Section = 2, Sort = 1
from EmployeeTransactionHistory eth 
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and ce.ClientID = e.ClientID
	left outer join InvoicePayrolls ip on ip.CompanyID = eth.CompanyID and ip.AuditControlCode = eth.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where eth.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal) and PayrollRecordType = 4
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, PayrollCode, ip.InvoiceNumber
order by EmployeeID, Sort

-- Local Tax values.
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, PayrollCode,
	[Local] = SUM((case when PayrollRecordType = 5 then TRXAmount else 0.00 end)), Section = 2, Sort = 2
from EmployeeTransactionHistory eth 
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and ce.ClientID = e.ClientID
	left outer join InvoicePayrolls ip on ip.CompanyID = eth.CompanyID and ip.AuditControlCode = eth.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where eth.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal) and PayrollRecordType = 5
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, PayrollCode, ip.InvoiceNumber
order by EmployeeID, Sort

-- FICA SS Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, ech.Department, [Description] = 'FICA SS', 
	[TaxAmount] = SUM(ech.FICASSWithholdingPayRun + ech.FICASSTaxOnTips), Section = 2,  Sort = 3
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	left outer join ClientEmployees ce on ech.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and ce.ClientID = e.ClientID
	left outer join InvoicePayrolls ip on ip.CompanyID = ech.CompanyID and ip.AuditControlCode = ech.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where ech.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal)
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, ech.Department, ip.InvoiceNumber
order by EmployeeID, Sort

insert into #FICASSAllocations(EmployeeID, HomeDepartment, Department, GrossWageAmount, GrossWageTotal, GrossWagePct, FICASSTotal, FICASSAllocated, FICASSAllocatedTotal, FICASSAllocatedDifference, FICASSAdjusted)
select *,
      FICASSAllocatedTotal = sum(FICASSAllocated) over (partition by EmployeeID),
      FICASSAllocatedDifference = sum(FICASSAllocated) over (partition by EmployeeID) - FICASSTotal,
      FICASSAdjusted = 0 
from (select en.EmployeeID, en.HomeDepartment, en.Department, en.TRXAmount, en.TRXTotal, GrossWagePct, 
            FICASSTotal = (select sum(FICASSWithholdingPayRun + FICASSTaxOnTips) from EmployeeCheckHistory ech where ech.EmployeeID = en.EmployeeID AND ech.AuditControlCode = en.AuditControlCode),
            FICASSAllocated = ISNULL((select sum(FICASSWithholdingPayRun + FICASSTaxOnTips) * (CASE TRXTotal WHEN 0 THEN 0 ELSE en.TRXAmount / en.TRXTotal END)
									  from EmployeeCheckHistory ech
									  where ech.EmployeeID = en.EmployeeID and ech.Department = en.Department AND ech.AuditControlCode = en.AuditControlCode),0)
      from (select e.EmployeeID, HomeDepartment = e.Department, eth.Department, eth.AuditControlCode, TRXAmount = SUM(eth.TRXAmount), TRXTotal = sum(sum(eth.TRXAmount)) over (partition by e.EmployeeID, eth.Department),
				   GrossWagePct = (CASE sum(eth.TRXAmount) WHEN 0 THEN 0 ELSE sum(eth.TRXAmount) / sum(sum(eth.TRXAmount)) over (partition by e.EmployeeID, eth.Department) END)
            from EmployeeTransactionHistory eth
				INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
				inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
				right outer join InvoicePayrolls ip on ip.AuditControlCode = eth.AuditControlCode and ip.CompanyID = eth.CompanyID
				right outer join Invoices i on i.InvoiceNumber = ip.InvoiceNumber and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal)
            where eth.CompanyID = @companyInternal and PayrollRecordType = 1
            group by eth.CompanyID, e.EmployeeID, eth.Department, e.Department, eth.AuditControlCode
            ) as en
) as allocations
update #FICASSAllocations SET FICASSAdjusted = CASE WHEN HomeDepartment = Department THEN FICASSAllocated + FICASSAllocatedDifference ELSE FICASSAllocated END

-- FICA Med Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, ech.Department, 'FICA Med', SUM(ech.FICAMWithholdingPayRun + ech.FICAMTaxOnTips) as [TaxAmount], Section = 2, Sort = 4
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	left outer join ClientEmployees ce on ech.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID
	left outer join InvoicePayrolls ip on ip.CompanyID = ech.CompanyID and ip.AuditControlCode = ech.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where ech.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal)
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, ech.Department, ip.InvoiceNumber
order by EmployeeID, Sort

insert into #FICAMedAllocations(FICAMedEmployeeID, FICAMedHomeDepartment, FICAMedDepartment, FICAMedGrossWageAmount, FICAMedGrossWageTotal, FICAMedGrossWagePct, FICAMedTotal, FICAMedAllocated, FICAMedAllocatedTotal, FICAMedAllocatedDifference, FICAMedAdjusted)
select *,
      FICAMedAllocatedTotal = sum(FICAMedAllocated) over (partition by FICAMedEmployeeID),
      FICAMedAllocatedDifference = sum(FICAMedAllocated) over (partition by FICAMedEmployeeID) - FICAMedTotal,
      FICAMedAdjusted = 0 
from (select FICAMedEmployeeID, FICAMedHomeDepartment, FICAMedDepartment, FICAMedGrossWageAmount, FICAMedGrossWageTotal, FICAMedGrossWagePct,
            FICAMedTotal = (select sum(ech.FICAMWithholdingPayRun + ech.FICAMTaxOnTips) from EmployeeCheckHistory ech where ech.EmployeeID = en.FICAMedEmployeeID AND ech.AuditControlCode = en.AuditControlCode),
            FICAMedAllocated = (select sum(ech.FICAMWithholdingPayRun + ech.FICAMTaxOnTips) * (CASE FICAMedGrossWageTotal WHEN 0 THEN 0 ELSE en.FICAMedGrossWageAmount / en.FICAMedGrossWageTotal END) from EmployeeCheckHistory ech where ech.EmployeeID = en.FICAMedEmployeeID AND ech.AuditControlCode = en.AuditControlCode)
      from (select FICAMedEmployeeID = e.EmployeeID, FICAMedHomeDepartment = e.Department, FICAMedDepartment = eth.Department, eth.AuditControlCode, FICAMedGrossWageAmount = SUM(eth.TRXAmount),
				FICAMedGrossWageTotal = sum(sum(eth.TRXAmount)) over (partition by e.EmployeeID), FICAMedGrossWagePct = (CASE sum(TRXAmount) WHEN 0 THEN 0 ELSE sum(TRXAmount) / sum(sum(eth.TRXAmount)) over (partition by e.EmployeeID) END)
            from EmployeeTransactionHistory eth
				INNER JOIN #Departments d ON d.Department = eth.Department
				INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
				inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
				right outer join InvoicePayrolls ip on ip.AuditControlCode = eth.AuditControlCode and ip.CompanyID = eth.CompanyID
				right outer join Invoices i on i.InvoiceNumber = ip.InvoiceNumber and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal)
            where eth.CompanyID = @companyInternal and PayrollRecordType = 1
            group by eth.CompanyID, e.EmployeeID, eth.Department, e.Department, eth.AuditControlCode
            ) as en
) as allocations

update #FICAMedAllocations SET FICAMedAdjusted = CASE WHEN FICAMedHomeDepartment = FICAMedDepartment THEN FICAMedAllocated + FICAMedAllocatedDifference ELSE FICAMedAllocated END

-- Federal Tax Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, [Description], TaxAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, ech.Department, 'Federal', SUM(ech.FederalWithholdingPayRun) as [TaxAmount], Section = 2, Sort = 5
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	left outer join ClientEmployees ce on ech.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID
	left outer join InvoicePayrolls ip on ip.CompanyID = ech.CompanyID and ip.AuditControlCode = ech.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where ech.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal)
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, ech.Department, ip.InvoiceNumber
order by EmployeeID, Sort

insert into #FedAllocations(FedEmployeeID, FedHomeDepartment, FedDepartment, FedGrossWageAmount, FedGrossWageTotal, FedGrossWagePct, FedTotal, FedAllocated, FedAllocatedTotal, FedAllocatedDifference, FedAdjusted)
select *,
      FedAllocatedTotal = sum(FedAllocated) over (partition by FedEmployeeID),
      FedAllocatedDifference = sum(FedAllocated) over (partition by FedEmployeeID) - FedTotal,
      FedAdjusted = 0 
from (select FedEmployeeID, FedHomeDepartment, FedDepartment, FedGrossWageAmount, FedGrossWageTotal, FedGrossWagePct,
            FedTotal = (select sum(ech.FederalWithholdingPayRun) from EmployeeCheckHistory ech where ech.EmployeeID = en.FedEmployeeID AND ech.AuditControlCode = en.AuditControlCode),
            FedAllocated = (select sum(ech.FederalWithholdingPayRun) * Convert(decimal(8,2), CASE FedGrossWageTotal WHEN 0 THEN 0 ELSE en.FedGrossWageAmount / en.FedGrossWageTotal END) from EmployeeCheckHistory ech where ech.EmployeeID = en.FedEmployeeID AND ech.AuditControlCode = en.AuditControlCode)
      from (select 
				FedEmployeeID = e.EmployeeID,
				FedHomeDepartment = e.Department,
				FedDepartment = eth.Department,
				eth.AuditControlCode,
				FedGrossWageAmount = SUM(eth.TRXAmount),
				FedGrossWageTotal = sum(sum(eth.TRXAmount)) over (partition by e.EmployeeID),
				FedGrossWagePct = Convert(decimal(19,2), CASE sum(TRXAmount) WHEN 0 THEN 0 ELSE sum(TRXAmount) / sum(sum(eth.TRXAmount)) over (partition by e.EmployeeID) END)
            from EmployeeTransactionHistory eth
				INNER JOIN #Departments d ON d.Department = eth.Department
				INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
				inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
				right outer join InvoicePayrolls ip on ip.AuditControlCode = eth.AuditControlCode and ip.CompanyID = eth.CompanyID
				right outer join Invoices i on i.InvoiceNumber = ip.InvoiceNumber and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal)
            where eth.CompanyID = @companyInternal and PayrollRecordType = 1
            group by eth.CompanyID, e.EmployeeID, eth.Department, e.Department, eth.AuditControlCode
            ) as en
) as allocations
update #FedAllocations SET FedAdjusted = CASE WHEN FedHomeDepartment = FedDepartment THEN FedAllocated + FedAllocatedDifference ELSE FedAllocated END

-- * * * * *      S E C T I O N  3 :  Deductions     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, [Description], DeductAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, eth.PayrollCode, SUM(eth.TRXAmount), Section = 3, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID 
	left outer join InvoicePayrolls ip on ip.CompanyID = eth.CompanyID and ip.AuditControlCode = eth.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where eth.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal) and eth.PayrollRecordType = 2
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, eth.Department, PayrollCode, ip.InvoiceNumber
order by EmployeeID, Sort

-- * * * * *      S E C T I O N  4 :  Benefits     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, Department, [Description], BenefitAmount, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.Department, eth.PayrollCode, SUM(eth.TRXAmount), Section = 4, Sort = 1
from EmployeeTransactionHistory eth
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join ClientEmployees ce on ce.CompanyID = eth.CompanyID and ce.EmployeeID = e.EmployeeID and ce.ClientID = e.ClientID
	left outer join InvoicePayrolls ip on ip.CompanyID = eth.CompanyID and ip.AuditControlCode = eth.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where eth.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal) and eth.PayrollRecordType = 3
group by e.EmployeeID, (e.LastName + ', ' + e.FirstName) , e.SSN, eth.Department, PayrollCode, ip.InvoiceNumber
order by EmployeeID, Sort

---- * * * * *     N E T    P A Y    * * * * * * * 
insert into #EmployeePayExtras(CompanyID, EmployeeID, NetPay)
select ech.CompanyID, ech.EmployeeID, SUM(ech.NetWagesPayRun)
from EmployeeCheckHistory ech 
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	left outer join ClientEmployees ce on ech.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID and ce.ClientID = e.ClientID
	left outer join InvoicePayrolls ip on ip.CompanyID = ech.CompanyID and ip.AuditControlCode = ech.AuditControlCode
	left outer join Invoices i on i.CompanyID = ip.CompanyID and i.ClientID = ip.ClientID and i.InvoiceNumber = ip.InvoiceNumber
where ech.CompanyID = @companyInternal and ce.ClientID = @clientInternal and (i.DarwinInvoiceNumber = @invoiceInternal or i.MergedInvoiceNumber = @invoiceInternal)
group by ech.CompanyID, ech.EmployeeID, ip.InvoiceNumber

-- * * * * *     O U T P U T     * * * * * * * 
select pd.Id, pd.EmployeeID, EmployeeName, '***-**-' + SUBSTRING(EmployeeSSN, 6, 4) [EmployeeSSN],
	   rtrim(ltrim(REPLACE(pd.Department, NCHAR(0x00A0), ''))) as Department, pd.DepartmentName, [Description], Units, Rate, EarnAmount, GrossWagesFlag,
	   TaxAmount = CASE pd.Description WHEN 'FICA SS' THEN fss.FICASSAdjusted WHEN 'FICA Med' THEN fmd.FICAMedAdjusted WHEN 'Federal' THEN Fed.FedAdjusted ELSE pd.TaxAmount END,
	   DeductAmount, BenefitAmount, ex.NetPay, Section, Sort
from #EmployeePayDetail pd
	left outer join #EmployeePayExtras ex on ex.EmployeeID = pd.EmployeeID and ex.CompanyID = @companyInternal
	left join #FICASSAllocations fss on pd.EmployeeID = fss.EmployeeID and pd.Department = fss.Department and pd.[Description] = 'FICA SS'
	left join #FICAMedAllocations fmd on pd.EmployeeID = fmd.FICAMedEmployeeID and pd.Department = fmd.FICAMedDepartment and pd.[Description] = 'FICA Med'
	left join #FedAllocations fed on pd.EmployeeID = fed.FedEmployeeID and pd.Department = fed.FedDepartment and pd.[Description] = 'Federal'
order by pd.EmployeeID, pd.Department, Section, Sort

OPTION (RECOMPILE)

DROP TABLE #Departments
DROP TABLE #EmployeeIDs
DROP TABLE #EmployeePayDetail
DROP TABLE #EmployeePayExtras
DROP TABLE #FedAllocations
DROP TABLE #FICAMedAllocations
DROP TABLE #FICASSAllocations
