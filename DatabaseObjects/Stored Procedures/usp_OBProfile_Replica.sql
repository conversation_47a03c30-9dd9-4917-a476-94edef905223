-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Creates on-boarding profile, based on default data from system database 
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBProfile_Replica] 
	-- Add the parameters for the stored procedure here
	@CompanyID as int,
	@BaseProfile as int,
	@ProfileName as nvarchar(50) = '',
	@NewProfile as int OUTPUT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

	DECLARE @ErrNum as int = 0
	DECLARE @ProfileID as nvarchar(10)
	
	IF @ProfileName = ''
		INSERT INTO OBProfiles (CompanyID, ProfileName, EEFinalize, [Signature], MaskSSNInSign, BankPresentation, SortOption, CodeSetup, DayToDueDate, NotifyBeforeDueDate, AllowCCAddCodes, Completed,AllowCodesOutsideOfPeriod, VerificationAgreement) SELECT 
		                        @CompanyID as CompanyID, ProfileName + '_Copy', EEFinalize, [Signature], MaskSSNInSign, BankPresentation, SortOption, CodeSetup, DayToDueDate, NotifyBeforeDueDate, AllowCCAddCodes, 0,AllowCodesOutsideOfPeriod, VerificationAgreement FROM OBProfiles as OBProfiles_1 WHERE ProfileID = @BaseProfile
    ELSE
		INSERT INTO OBProfiles (CompanyID, ProfileName, EEFinalize, [Signature], MaskSSNInSign, BankPresentation, SortOption, CodeSetup,  DayToDueDate, NotifyBeforeDueDate, AllowCCAddCodes, Completed,AllowCodesOutsideOfPeriod) SELECT 
		                        @CompanyID as CompanyID, @ProfileName as ProfileName, EEFinalize, [Signature], MaskSSNInSign, BankPresentation, SortOption, CodeSetup, DayToDueDate, NotifyBeforeDueDate, AllowCCAddCodes, 0,AllowCodesOutsideOfPeriod FROM OBProfiles as OBProfiles_1 WHERE ProfileID = @BaseProfile
    IF @@ERROR = 0
	BEGIN
		SELECT @NewProfile = @@IDENTITY
		SELECT @ProfileID = CAST(@NewProfile as nvarchar(10))
		INSERT INTO OBProfileTasks(CompanyID, ProfileID, TaskID, TaskName, TaskType, TaskOrder, TaskAssignment, TaskRequired, TaskLocked, CCInstruction, EEInstruction, InstructionDoc, TipText, TipImage, FormCols, DocumentsUpdated, InstructionsUpdated, FieldsUpdated, TipsUpdated)
				SELECT @CompanyID as CompanyID, @ProfileID as ProfileID, TaskID, TaskName, TaskType, TaskOrder, TaskAssignment, TaskRequired, TaskLocked, CCInstruction, EEInstruction, InstructionDoc, TipText, TipImage, 1, 0, 0, 0, 0 FROM OBProfileTasks as OBProfileTasks_1 WHERE ProfileID = @BaseProfile 
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 2
				
		INSERT INTO OBProfileTaskFields(CompanyID, ProfileID, TaskID, FName, SeqNbr, FLabel, FType, FSize, DNType, FValueOptions, PermanentTBL, PermanentFLD, RelatedTask, RelatedFLD, FRequired, FLocked, CCAccess, EEAccess, FTip)
				SELECT @CompanyID as CompanyID, @ProfileID as  ProfileID, TaskID, FName, SeqNbr, FLabel, FType, FSize, DNType, FValueOptions, PermanentTBL, PermanentFLD, RelatedTask, RelatedFLD, FRequired, FLocked, CCAccess, EEAccess, FTip FROM OBProfileTaskFields as OBProfileTaskFields_1 WHERE ProfileID = @BaseProfile 
		
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 4	
	    
		INSERT INTO OBProfileTaskDocuments    (CompanyID, ProfileID, TaskID, DocumentID, DocumentName, Verification, ManualUpdate, Locked, VerificationAgreement, UseAssignments)
			SELECT  @CompanyID as CompanyID, @ProfileID as  ProfileID, TaskID, DocumentID, DocumentName, Verification, ManualUpdate, Locked, VerificationAgreement, UseAssignments FROM OBProfileTaskDocuments AS OBProfileTaskDocuments_1 WHERE ProfileID = @BaseProfile  
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 8	
		
		INSERT INTO OBProfileNotifications (CompanyID, ProfileID, [Type], Assigned, [Enabled], EmailSubject, EmailBody)
			SELECT @CompanyID as CompanyID, @ProfileID as  ProfileID, [Type], Assigned, [Enabled], EmailSubject, EmailBody FROM OBProfileNotifications as OBProfileNotifications_1 WHERE ProfileID = @BaseProfile  
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 16	
	    
		INSERT INTO OBProfileNotificationAttachments  (CompanyID, ProfileID, [Type], Assigned, DocumentName, ContentType, DocumentFolder, DocumentFile, DocumentBody, DBStatus)
			SELECT @CompanyID as CompanyID, @ProfileID as ProfileID, [Type], Assigned, DocumentName, ContentType, DocumentFolder, DocumentFile, DocumentBody, DBStatus FROM  OBProfileNotificationAttachments AS OBProfileNotificationAttachments_1 WHERE ProfileID = @BaseProfile  		
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 32	
		
		INSERT INTO OBProfileImportTranslation   (CompanyID, ProfileID, CodeType, DNetCode, ImportCode)
			SELECT @CompanyID as CompanyID, @ProfileID as ProfileID, CodeType, DNetCode, ImportCode FROM OBProfileImportTranslation AS OBProfileImportTranslation_1 WHERE ProfileID = @BaseProfile 		
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 64	
		
		INSERT INTO OBProfileFinalize (CompanyID, ProfileID, Section, Assigned, [Enabled], [Notification])
			SELECT @CompanyID as CompanyID, @ProfileID  as  ProfileID, Section, Assigned, [Enabled], [Notification] FROM OBProfileFinalize as OBProfileFinalize_1 WHERE ProfileID = @BaseProfile 
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 128	
		
		INSERT INTO OBProfileTaskDocumentMapping  (CompanyID, ProfileID, DocumentID, FormName, FormFieldType, DB_Table, DB_Field, DB_RecNum, DB_Value, FieldPresentation, FieldStatus, UseInWF, WF_SeqNbr, WF_FieldLabel, WF_FieldType)
			SELECT  @CompanyID as CompanyID, @ProfileID  as  ProfileID, DocumentID, FormName, FormFieldType, DB_Table, DB_Field, DB_RecNum, DB_Value, FieldPresentation, FieldStatus, UseInWF, WF_SeqNbr, WF_FieldLabel, WF_FieldType FROM OBProfileTaskDocumentMapping AS OBProfileTaskDocumentMapping_1 WHERE ProfileID = @BaseProfile
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 256
			
		INSERT INTO OBProfileTaskDocumentAssignments  (CompanyID, ProfileID, DocumentID, CodeType, CodeID)
			SELECT  @CompanyID as CompanyID, @ProfileID  as  ProfileID, DocumentID, CodeType, CodeID FROM OBProfileTaskDocumentAssignments AS OBProfileTaskDocumentAssignments_1 WHERE ProfileID = @BaseProfile
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 512
			
		INSERT INTO OBProfileCodeDetails (CompanyID, ProfileID, CodeType, CodeID) 
			SELECT  @CompanyID as CompanyID, @ProfileID  as  ProfileID, CodeType, CodeID	FROM OBProfileCodeDetails AS OBProfileCodeDetails_1 WHERE ProfileID = @BaseProfile	
		if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 1024
    END
    ELSE
		SELECT @ErrNum = 1

	UPDATE OBProfileTaskFields SET CCAccess = 5 WHERE ProfileID = @NewProfile  AND CompanyID = @CompanyID AND TaskID = 10 AND FName = 'EmployeeClass' AND CCAccess > 5

	UPDATE OBProfileTaskFields SET EEAccess = 5	WHERE ProfileID = @NewProfile  AND CompanyID = @CompanyID AND TaskID = 10 AND FName = 'EmployeeClass' AND EEAccess > 5

	RETURN 	
END
