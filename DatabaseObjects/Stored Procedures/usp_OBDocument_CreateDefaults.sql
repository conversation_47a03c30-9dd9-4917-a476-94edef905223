-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Refresh default on-boarding documents from system database to company database
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBDocument_CreateDefaults]
 
	-- Add the parameters for the stored procedure here
	@CompanyID as int,
	@SystemDB as nvarchar(50)
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	DECLARE @SQL_Query as nvarchar(1000)
	DECLARE @ErrNum as int = 0

	SELECT @SQL_Query = 'INSERT INTO OBDocuments (CompanyID,DocumentCode, [Document], BasedOn, AssignedToState, AutoExempt, DPath, DFile, DBody, ContentType, Status, AutoFill, Mapped, Assigned, ManualUpdate, Locked, Owner, VerificationAgreement, UseAssignments, EEStampPage, EEStampAlignment, CCStampPage, CCStampAlignment )
						SELECT ' + CAST(@CompanyID as nvarchar(3)) + ' as CompanyID, DocumentCode, [Document], BasedOn, AssignedToState, AutoExempt, DPath, DFile, DBody, ContentType, Status, 1 AS AutoFill, 1 AS Mapped, ''ALL'' AS Assigned, 1 AS ManualUpdate, 1 AS Locked, ''G'' AS Owner, VerificationAgreement, 0, EEStampPage, EEStampAlignment, CCStampPage, CCStampAlignment
						FROM [' + @SystemDB + '].dbo.OnBoardingDocuments AS OBDocuments_1
						WHERE (DocumentCode NOT IN (SELECT DocumentCode FROM OBDocuments AS OBDocuments_2))'
	EXEC(@SQL_Query)
	if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 1


	SELECT @SQL_Query = 'INSERT INTO OBDocumentMapping (CompanyID, DocumentID, FormName, FormFieldType, DB_Table, DB_Field, DB_RecNum, DB_Value, FieldPresentation, FieldStatus, UseInWF, WF_SeqNbr, WF_FieldLabel, WF_FieldType, WF_FieldOptions)
						SELECT a.CompanyID, a.DocumentID, b.FormName, b.FormFieldType, b.DB_Table, b.DB_Field, b.DB_RecNum, b.DB_Value, b.FieldPresentation, b.FieldStatus, UseInWF, WF_SeqNbr, WF_FieldLabel, WF_FieldType, WF_FieldOptions
						FROM [' + @SystemDB + '].dbo.OnBoardingDocumentMapping AS b INNER JOIN OBDocuments AS a ON b.DocumentCode = a.DocumentCode AND a.CompanyID = ' + CAST(@CompanyID as nvarchar(3)) + ' 
						WHERE (a.DocumentID NOT IN (SELECT DocumentID FROM OBDocumentMapping AS c))'
	EXEC(@SQL_Query)
	if @@ERROR <> 0
			SELECT @ErrNum = @ErrNum + 2


END
