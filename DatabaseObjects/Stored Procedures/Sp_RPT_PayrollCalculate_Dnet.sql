-- =============================================
-- Author:		<Dhara Gohel>
-- Create date: <02/01/2024>
-- Description:	<returns the reslt for the Sp_RPT_PayrollCalculate_Dnet  report>
-- =============================================
CREATE     PROCEDURE [dbo].[Sp_RPT_PayrollCalculate_Dnet]
	
	@company INT=0,
	@client VARCHAR(15) = NULL,
	@userid NVARCHAR(20)=NULL,
	@invoice int=0
	

AS
BEGIN
CREATE TABLE #EmployeePayDetail(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[EmployeeName] [nvarchar](50) NULL,
	[EmployeeSSN] [nvarchar](50) NULL,
	[CheckNumber] [varchar](50) NULL,
	[CheckDate] [date] NULL,
	[Dept] [nvarchar](50) NULL,
	[DivisionID] [nvarchar](50) NULL,
	[Position] [nvarchar](50) NULL,
	[Hours]  [decimal](19,5) NULL,
	[PayRate]  [decimal](19,5) NULL,
	[EarningAmount] [decimal](19,5) NULL,
    [PayType] [tinyint] NULL,
	--Tax
	[Description] [nvarchar](30) NULL,
	[TaxAmount] [decimal](19,5) NULL,
	--Deduction
	[DeductAmount] [decimal](19,5) NULL,
	--Benefit
	[BenefitAmount] [decimal](19,5) NULL,
	[Section] [int] NULL,
	[Sort] [int] NULL	)

CREATE TABLE #EmployeePayExtras(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmployeeID] [nvarchar](30) NULL,
	[CheckNumber] [varchar](50) NULL,
	[GrossPay] [decimal](19,5) NULL,
	[NetPay] [decimal](19,5) NULL,
	[DirectDeposits] [decimal](19,5) NULL,
	[OtherDeposits] [decimal](19,5) NULL
	)
-- UserSecurity start -- 

declare @ProfileID nvarchar(max)= ''
select @ProfileID = (select pwm.ProfileID  from InvoicePayrolls b
inner join Invoices a  on  a.CompanyID =b.CompanyID and a.ClientID =b.ClientID 
and a.InvoiceNumber=b.InvoiceNumber
inner join  PayrollWorkMasters pwm on pwm.CompanyID = b.CompanyID 
and pwm.AuditControlCode = b.AuditControlCode and pwm.PayrollNumber = b.PayrollNumber
where a.CompanyID=@company and a.ClientID=@client and a.DarwinInvoiceNumber=@invoice)


CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)
INSERT INTO #Departments(DivisionID, Department) VALUES ('','')

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

CREATE TABLE #Payrolls(PayrollNumber NVARCHAR(MAX), AuditControlCode NVARCHAR(15))
INSERT INTO #Payrolls SELECT ip.PayrollNumber, ip.AuditControlCode from InvoicePayrolls ip
INNER JOIN Invoices i ON i.CompanyID = ip.CompanyID AND i.ClientID = ip.ClientID AND i.InvoiceNumber = ip.InvoiceNumber
WHERE ip.CompanyID = @company and ip.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
GROUP BY  PayrollNumber, AuditControlCode

declare @Sortoption nvarchar(max)= ''
if  exists(select companyid,ProfileID from CalcCheckSortOption
              where CompanyID=@company and  ProfileID =@ProfileID) 
	begin
		select @Sortoption = (select DISTINCT  (CASE WHEN c.SortChoice = 1 THEN 'pd.EmployeeID,pd.Section,pd.Sort' 
			WHEN c.SortChoice = 2 THEN 'LEFT(pd.EmployeeName, CHARINDEX('','',EmployeeName)-1),pd.Section,pd.Sort' 
			WHEN c.SortChoice = 3 THEN 'pd.Dept,pd.EmployeeID,pd.Section,pd.Sort'
			WHEN c.SortChoice = 4 THEN 'pd.Position,pd.EmployeeID,pd.Section,pd.Sort'
			WHEN c.SortChoice = 5 THEN 'pd.Dept,LEFT(pd.EmployeeName, CHARINDEX('','',EmployeeName)-1),pd.Section,pd.Sort'
			WHEN c.SortChoice = 6 THEN 'pd.Position,LEFT(pd.EmployeeName, CHARINDEX('','',EmployeeName)-1),pd.Section,pd.Sort'
			WHEN c.SortChoice = 7 THEN 'pd.EmployeeID,pd.Section,pd.Sort'
			ELSE '' END) as dispStatus from  CalcCheckSortOption c where c.CompanyID=@company and c.ProfileID =@ProfileID)
	end
else
	begin
		select @Sortoption = 'pd.EmployeeID,pd.Section,pd.Sort'
	end


-- * * * * *      S E C T I O N  1 :  Earnings     * * * * * * * 
-- Earnings by Payroll Type
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept,DivisionID, Position, [Hours], PayRate, EarningAmount, [Description], Section, Sort, PayType)
select e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), e.SSN, eth.CheckNumber, eth.CheckDate, eth.Department, d.DivisionID, eth.Position,  SUM(IsNUll(eth.UnitsToPay,0)), IsNull(eth.PayRate,0),
	SUM(IsNull(eth.TRXAmount,0)), eth.PayrollCode,	1,1, ep.PayType 
from EmployeeTransactionHistory eth
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	INNER JOIN #Departments d ON d.Department = eth.Department
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join EmployeePaycodes ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode
	inner join Departments dp on dp.CompanyID = @company and dp.Department = eth.Department
	inner join #Payrolls p on p.AuditControlCode = eth.AuditControlCode
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID 
where eth.CompanyID = @company and e.ClientID = @client and eth.PayrollRecordType = 1 and eth.PayRate <> 0 
group by e.EmployeeID,  e.LastName + ', ' + e.FirstName, e.SSN, eth.CheckNumber, eth.PayrollCode, eth.Department, eth.Position, eth.CheckDate, eth.PayrollNumber, d.DivisionID, 
	eth.PayRate, eth.PayrollCode,ep.PayType
--order by EmployeeID

insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, Dept,DivisionID, Position, [Hours], PayRate, EarningAmount, [Description], Section, Sort, PayType)
select e.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), e.SSN, eth.CheckNumber, eth.CheckDate, eth.Department, d.DivisionID, eth.Position,  SUM(IsNUll(eth.UnitsToPay,0)), 
	CASE SUM(eth.UnitsToPay) WHEN 0 THEN 0 ELSE SUM(eth.TRXAmount)/SUM(eth.UnitsToPay) END, SUM(IsNull(eth.TRXAmount,0)), eth.PayrollCode,	1, 1, ep.PayType 
from EmployeeTransactionHistory eth
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	INNER JOIN #Departments d ON d.Department = eth.Department
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	left outer join EmployeePaycodes ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode
	inner join #Payrolls p on p.AuditControlCode = eth.AuditControlCode
	left outer join EmployeeCheckHistory ech ON ech.CompanyID = eth.CompanyID AND ech.AuditControlCode = eth.AuditControlCode AND ech.CheckNumber = eth.CheckNumber AND ech.EmployeeID = eth.EmployeeID 
where eth.CompanyID = @company and e.ClientID = @client and eth.PayrollRecordType = 1 and eth.PayRate = 0 
group by e.EmployeeID,  e.LastName + ', ' + e.FirstName, e.SSN, eth.CheckNumber, eth.PayrollCode, eth.Department, eth.Position, eth.CheckDate, eth.PayrollNumber, d.DivisionID, 
	eth.PayRate, eth.PayrollCode,ep.PayType
--order by EmployeeID

-- * * * * *      S E C T I O N  2 :  Taxes     * * * * * * * 

-- Federal Tax
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, ech.CheckNumber, ech.CheckDate, 'Federal'  [Description], SUM(ech.FederalWithholdingPayRun + ech.FederalTaxOnTips) as [TaxAmount],d.DivisionID,
e.Department, e.Position , 2, 1
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeCheckHistory ech on ech.CompanyID = b.CompanyID and ech.AuditControlCode = p.AuditControlCode
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	INNER JOIN #Departments d ON d.Department = e.Department
where ech.CompanyID = @company and e.ClientID = @client
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, ech.CheckNumber, ech.CheckDate, d.DivisionID, e.Department, e.Position


-- State Tax Version
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN,eth.CheckNumber,eth.CheckDate, eth.PayrollCode,  SUM(TRXAmount),
d.DivisionID, eth.Department, eth.Position, Section = 2, Sort = 2 
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeTransactionHistory eth  on eth.CompanyID = b.CompanyID and eth.AuditControlCode = p.AuditControlCode
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	INNER JOIN #Departments d ON d.Department = eth.Department
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
where eth.CompanyID = @company and e.ClientID = @client and PayrollRecordType = 4  
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, d.DivisionID, eth.Department, eth.Position
--order by EmployeeID, Sort


--Local
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN,  eth.CheckNumber,eth.CheckDate,eth.PayrollCode, SUM(TRXAmount), d.DivisionID,
eth.Department,eth.Position, 2, 3
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeTransactionHistory eth  on eth.CompanyID = b.CompanyID and eth.AuditControlCode = p.AuditControlCode
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	INNER JOIN #Departments d ON d.Department = eth.Department
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
where eth.CompanyID = @company and e.ClientID = @client and PayrollRecordType = 5 
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN,  eth.CheckNumber, eth.CheckDate, eth.PayrollCode, d.DivisionID, eth.Department, eth.Position
--order by EmployeeID


-- FICA SS Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, ech.CheckNumber, ech.CheckDate,'FICA SS', sum(ech.FICASSWithholdingPayRun + ech.FICASSTaxOnTips), d.DivisionID,
ech.Department, e.Position, Section = 2, Sort = 4
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeCheckHistory ech  on ech.CompanyID = b.CompanyID and ech.AuditControlCode = p.AuditControlCode
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	INNER JOIN #Departments d ON d.Department = ech.Department
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
where ech.CompanyID = @company and e.ClientID = @client 
group by e.EmployeeID,e.LastName + ', ' + e.FirstName, e.SSN, ech.CheckNumber, ech.CheckDate, d.DivisionID,
ech.Department, e.Position
--order by EmployeeID, Sort

-- FICA Med Witholding
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], TaxAmount, DivisionID, Dept, Position, Section, Sort)
select e.EmployeeID, EmployeeName = e.LastName + ', ' + e.FirstName, e.SSN, ech.CheckNumber, ech.CheckDate, 'FICA Med', SUM(ech.FICAMWithholdingPayRun + ech.FICAMTaxOnTips), 
d.DivisionID, ech.Department, e.Position, 2, 5
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeCheckHistory ech  on ech.CompanyID = b.CompanyID and ech.AuditControlCode = p.AuditControlCode
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	INNER JOIN #Departments d ON d.Department = ech.Department
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
where ech.CompanyID = @company and e.ClientID = @client 
group by  e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, ech.CheckNumber, ech.CheckDate, 
d.DivisionID, ech.Department, e.Position
--order by EmployeeID


-- * * * * *      S E C T I O N  3 :  Deductions     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], DeductAmount, DivisionID, Dept, Position, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, SUM(eth.TRXAmount), d.DivisionID, eth.Department, eth.Position, 3, 1
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeTransactionHistory eth  on eth.CompanyID = b.CompanyID and eth.AuditControlCode = p.AuditControlCode
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
where eth.CompanyID = @company and e.ClientID = @client and eth.PayrollRecordType = 2 
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, d.DivisionID, eth.Department, eth.Position
--order by EmployeeID

-- * * * * *      S E C T I O N  4 :  Benefits     * * * * * * * 
insert into #EmployeePayDetail(EmployeeID, EmployeeName, EmployeeSSN, CheckNumber, CheckDate, [Description], BenefitAmount, DivisionID, Dept, Position, Section, Sort)
select e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, SUM(ISNULL(eth.TRXAmount,0)), d.DivisionID, eth.Department, eth.Position, 4, 1
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeTransactionHistory eth  on eth.CompanyID = b.CompanyID and eth.AuditControlCode = p.AuditControlCode
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	INNER JOIN #Departments d ON d.Department = eth.Department
	inner join Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
where eth.CompanyID = @company and e.ClientID = @client and eth.PayrollRecordType = 3 
group by e.EmployeeID, e.LastName + ', ' + e.FirstName, e.SSN, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, d.DivisionID, eth.Department, eth.Position
--order by EmployeeID


-- * * * * *      S E C T I O N  5 :  Direct Deposit     * * * * * * * 
insert into #EmployeePayExtras(EmployeeID, CheckNumber, NetPay, GrossPay, DirectDeposits, OtherDeposits)
select  ech.EmployeeID, ech.CheckNumber, IsNull(ech.NetWagesPayRun, 0), IsNull(ech.GrossWagesPayRun, 0), IsNull(pwb.DirectDeposits,0) DirectDeposit1,
	case when IsNull(ech.NetWagesPayRun,0) > IsNull(pwb.DirectDeposits,0) then IsNull(ech.NetWagesPayRun,0) - IsNull(pwb.DirectDeposits,0) else cast(0 as decimal(19,5)) end as CheckAmount
from InvoicePayrolls b  
	inner join #Payrolls p on p.AuditControlCode = b.AuditControlCode and p.PayrollNumber = b.PayrollNumber
	inner join EmployeeCheckHistory ech  on ech.CompanyID = b.CompanyID and ech.AuditControlCode = p.AuditControlCode
	inner join Employees e on e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	left outer join (select CompanyID, EmployeeID, PayrollNumber, PaymentAdjustmentNumber, sum(IsNull(DirectDepositAmount,0)) DirectDeposits
			from EmployeeDirectDepositHistory 
			group by CompanyID, EmployeeID, PayrollNumber, PaymentAdjustmentNumber) pwb on pwb.CompanyID = ech.CompanyID  and pwb.PayrollNumber = ech.PayrollNumber and
										 pwb.EmployeeID = ech.EmployeeID and pwb.PaymentAdjustmentNumber = ech.PaymentAdjustmentNumber
where ech.CompanyID = @company and e.ClientID = @client 

-- * * * * *     O U T P U T     * * * * * * * 
declare @sql nvarchar(max)=''
 set @sql = 'select pd.Id, pd.EmployeeID, pd.EmployeeName, ''***-**-'' + SUBSTRING(pd.EmployeeSSN, 6, 4) [EmployeeSSN],
rtrim(ltrim(REPLACE(pd.CheckNumber, NCHAR(0x00A0), ''''))) AS CheckNumber,pd.CheckDate
,pd.Dept,pd.Position,pd.Hours,pd.PayRate,pd.EarningAmount,
 [Description],TaxAmount,DeductAmount,BenefitAmount,
pd.Section, pd.Sort,pd.PayType,
ex.GrossPay,ex.NetPay,ex.DirectDeposits,ex.[OtherDeposits] as CashDeposit
from #EmployeePayDetail pd
INNER JOIN #EmployeeIDs eid ON eid.EmployeeID = pd.EmployeeID 
INNER JOIN #Divisions d ON pd.DivisionID = d.DivisionID
left outer join #EmployeePayExtras ex on ex.EmployeeID=pd.EmployeeID and ex.CheckNumber=pd.CheckNumber
ORDER BY ' +  @Sortoption
exec (@sql)


DROP TABLE #EmployeePayExtras
DROP TABLE #EmployeePayDetail
DROP TABLE #Departments
DROP TABLE #EmployeeIDs
DROP TABLE #Divisions

END

