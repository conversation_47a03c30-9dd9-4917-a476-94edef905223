-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- =============================================
-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Permanent removepending on-boarding user from process
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBDocument_Remove]
	@CompanyID as int,
	@DocumentID as int
AS
BEGIN
	DECLARE @ErrNum as int = 0
	
	SET NOCOUNT ON;
	
	IF @DocumentID > 0 
	BEGIN
		DELETE FROM OBDocumentAssignments WHERE (CompanyID = @CompanyID) AND DocumentID = @DocumentID
		IF @@ERROR <> 0
			SET @ErrNum = @ErrNum + 4
			
		DELETE FROM OBDocumentMapping WHERE (CompanyID = @CompanyID) AND DocumentID = @DocumentID
		IF @@ERROR <> 0
			SET @ErrNum = @ErrNum + 2
			
		DELETE FROM OBDocuments WHERE (CompanyID = @CompanyID) AND DocumentID = @DocumentID
		IF @@ERROR <> 0
			SET @ErrNum = @ErrNum + 1
	END	
END
