/*
********************************************************************
**** Created by <PERSON>.
**** Date: 03/11/2015.
**** Purpose: Generates data for the SSRS Report Invoice
****
********************************************************************
*/

CREATE     PROCEDURE [dbo].[sp_RPT_InvoiceCustom1]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@invoice INT,
	@userid NVARCHAR(20)
)
AS

-- Get Client "Main" address in case the divisional address IS NULL.
declare @AddressCode varchar(50)
select @AddressCode = (Select Addresscode from Clients where CompanyID = @company and clientid = @client)

--This is the table that we're going to build to run the report on.
-- We will order by Section so that the report will organize the report areas properly.
-- My first attempt at this will have me applying a filter on report tablix based on the Section column.

CREATE TABLE #CustomInvoice1_Section(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[ClientID] [nvarchar](15) NULL,
	[DarwinInvoiceNumber] [int] NULL,
	[ChargeType] [nvarchar](30) NULL,
	[SequenceNumber] [int] NULL,
	[ChargeBase] [decimal](19,5) NULL,
	[Rate] [varchar](19) NULL,
	[Total] [decimal](19,5) NULL,
	[DetailChargeType] [nvarchar](30) NULL,
	[ComponentCode] [nvarchar](30) NULL,
	[DetailSequenceNumber] [int] NULL,
	[DetailTotal] [decimal](19,5) NULL,
	[SelectForPrint] [tinyint] NULL,
	[Section] [int] NULL)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

declare @FICASRate [decimal] (19,5) 
select @FICASRate = (select TaxBracketRate from PayrollTaxTableSetup where CompanyID = @company AND TAXCODE = 'FICAS')

declare @FICAMRate [decimal] (19,5) 
select @FICAMRate = (select TaxBracketRate from PayrollTaxTableSetup where CompanyID = @company AND TAXCODE = 'EFICM')

declare @FUTARate [decimal] (19,5) 
select @FUTARate = (select FUTASUTATaxRate from UnemploymentSetup where CompanyID = @company AND FUTASUTA = 'FED')

declare @SUTACount int 
select @SUTACount = (select count(*) from (
					select distinct StateTaxArray1 from EmployeeInvoiceCheckHistory where DarwinInvoiceNumber = @invoice UNION
					select distinct StateTaxArray2 from EmployeeInvoiceCheckHistory where DarwinInvoiceNumber = @invoice UNION
					select distinct StateTaxArray3 from EmployeeInvoiceCheckHistory where DarwinInvoiceNumber = @invoice UNION
					select distinct StateTaxArray4 from EmployeeInvoiceCheckHistory where DarwinInvoiceNumber = @invoice UNION
					select distinct StateTaxArray5 from EmployeeInvoiceCheckHistory where DarwinInvoiceNumber = @invoice UNION
					select distinct StateTaxArray6 from EmployeeInvoiceCheckHistory where DarwinInvoiceNumber = @invoice ) x)

declare @SUTARate [decimal] (19,5) 
declare @SUTAState [Varchar] (3) 
select @SUTAState = (select top 1 StateTaxArray1 from EmployeeInvoiceCheckHistory where DarwinInvoiceNumber = @invoice)

--Only do this if @SUTACount=1.
select @SUTARate = (select FUTASUTATaxRate from UnemploymentSetup where CompanyID = @company AND FUTASUTA = @SUTAState)

-- Build Section 2.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
SELECT	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, ic.ChargeBase [ChargeBase],
		CASE WHEN cd.ComponentCode = 'FICA-SOCIAL SECURITY' THEN CONVERT(varchar(25),@FICASRate )
			 WHEN cd.ComponentCode = 'FICA-MEDICARE' THEN CONVERT(varchar(25),@FICAMRate )
			 WHEN cd.ComponentCode = 'FUTA' THEN CONVERT(varchar(25),@FUTARate )
			 WHEN cd.ComponentCode = 'SUTA' THEN 
				CASE 
					WHEN @SUTACount>1 THEN 'Multiple'
					WHEN @SUTACount=1 THEN CONVERT(varchar(25),@SUTARate )
					ELSE 'N/A'
				END
			ELSE '0.00'
		END [Rate],
		0 [Total], cd.ChargeType [DetailChargeType], cd.ComponentCode [DetailComponentCode], cd.SequenceNumber [DetailSequenceNumber],
		cd.Total [DetailTotal], 2 [Section]
from InvoiceCharges ic
	INNER JOIN Invoices i ON i.CompanyID = ic.CompanyID AND i.ClientID = ic.ClientID AND i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join InvoiceChargeDetails cd on cd.CompanyID = ic.CompanyID and cd.ClientID = ic.ClientID and cd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
				and REPLACE(cd.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
where ic.DarwinInvoiceNumber = @invoice and ic.CompanyID = @company and ic.ClientID = @client and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') = 'PAYROLL'
  and cd.SelectForPrint = 1 and REPLACE(cd.ComponentCode, NCHAR(0x00A0),'') in ('FICA-MEDICARE','FICA-SOCIAL SECURITY','FUTA','SUTA') and cd.total <> 0

-- Build Section 4.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, cd.ComponentCode, ic.SequenceNumber, ic.ChargeBase,
		1 [Rate],	-- Rate to be determined by the psudocode
		cd.total [Total], cd.ChargeType [DetailChargeType], cd.ComponentCode [DetailComponentCode], cd.SequenceNumber [DetailSequenceNumber],
		cd.total [DetailTotal], 4 [Section]
from InvoiceCharges ic
	INNER JOIN Invoices i ON i.CompanyID = ic.CompanyID AND i.ClientID = ic.ClientID AND i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join InvoiceChargeDetails cd on cd.CompanyID = ic.CompanyID and cd.ClientID = ic.ClientID and cd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
				and cd.ChargeType = ic.ChargeType
where ic.DarwinInvoiceNumber = @invoice and ic.CompanyID = @company and ic.ClientID = @client and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') = 'PAYROLL'
  and cd.SelectForPrint = 1 and REPLACE(cd.ComponentCode, NCHAR(0x00A0),'') in ('BENEFITS','NON-Gross Bus EXP') and cd.total <> 0

-- Build Section 3 - Agency Credits.		(Made Deductions all caps to be consistent with all others--BRP).
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, 'DEDUCTIONS' [ChargeType], ic.SequenceNumber, 0 [ChargeBase],
		0 [Rate],	-- Rate to be determined by the psudocode = 0.
		0 [Total], '' [DetailChargeType], '' [DetailComponentCode], '' [DetailSequenceNumber] , ic.Total, 3 [Section]
from InvoiceCharges ic
	INNER JOIN Invoices i ON i.CompanyID = ic.CompanyID AND i.ClientID = ic.ClientID AND i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join InvoiceChargeDetails cd on cd.CompanyID = ic.CompanyID and cd.ClientID = ic.ClientID and cd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
				and cd.ChargeType = ic.ChargeType
where ic.DarwinInvoiceNumber = @invoice and ic.CompanyID = @company and REPLACE(ic.ClientID, NCHAR(0x00A0), '') = REPLACE(@client, NCHAR(0x00A0), '')
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') = 'Agency Credits'

-- Build Section 3 - The Rest.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, ic.ChargeBase,
		0 [Rate],	-- Rate to be determined by the psudocode = 0.
		cd.Total [Total], cd.ChargeType [DetailChargeType], cd.ComponentCode [DetailComponentCode], cd.SequenceNumber [DetailSequenceNumber],
		cd.Total [DetailTotal], 3 [Section]
from InvoiceCharges ic
	INNER JOIN Invoices i ON i.CompanyID = ic.CompanyID AND i.ClientID = ic.ClientID AND i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join InvoiceChargeDetails cd on cd.CompanyID = ic.CompanyID and cd.ClientID = ic.ClientID and cd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
				and cd.ChargeType = ic.ChargeType
where ic.DarwinInvoiceNumber = @invoice and ic.CompanyID = @company and ic.ClientID = @client
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') in ('Hire Act Tax Credit', 'PTO Credit')

-- Build Section 4 - The Rest.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, 0 [ChargeBase],
		0 [Rate],	-- Rate to be determined by the psudocode = 0.
		0 [Total], '' [DetailChargeType], '' [DetailComponentCode], 0 [DetailSequenceNumber], ic.Total [DetailTotal], 4 [Section]
from Invoices i
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join InvoiceCharges ic on i.CompanyID = ic.CompanyID and i.Clientid = REPLACE(ic.ClientID, NCHAR(0x00A0), '')
				and i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
	left outer join ClientCharges cc on cc.CompanyID = i.CompanyID and cc.ClientID = i.ClientID
				and REPLACE(cc.DivisionID, NCHAR(0x00A0), '') = REPLACE(i.DivisionID, NCHAR(0x00A0), '')
				and REPLACE(cc.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client and ic.ChargeType not in ('OUTSTANDING BALANCE')
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') not in ('Payroll', 'Agency Credits', 'Hire Act Tax Credit', 'PTO Credit')
  and ic.Total <> 0 and coalesce(EmpChargeBreakdown,0) = 0

-- Build Section 1 - The Rest pt 1.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, ic.ChargeBase,
		ic.ChargeMultiplier [Rate],	-- Rate to be determined by the psudocode = 0.
		ic.total [Total], ic.ChargeType [DetailChargeType], '' [DetailComponentCode], ic.SequenceNumber [DetailSequenceNumber],
		ic.ChargeBase + ic.Total [DetailTotal], 1 [Section]
from Invoices i
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	left outer join InvoiceCharges ic on ic.CompanyID = i.CompanyID and REPLACE(ic.ClientID, NCHAR(0x00A0), '') = REPLACE(i.ClientID, NCHAR(0x00A0), '')
				and ic.DarwinInvoiceNumber = i.DarwinInvoiceNumber
	left outer join ClientCharges cc on cc.CompanyID = i.CompanyID and cc.ClientID = i.ClientID
				and REPLACE(cc.DivisionID, NCHAR(0x00A0), '') = REPLACE(i.DivisionID, NCHAR(0x00A0), '')
				and REPLACE(cc.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') not in ('Payroll', 'Agency Credits', 'Hire Act Tax Credit', 'PTO Credit')
  and ic.Criteria  > 3 and ic.Criteria <> 13 and ic.Criteria not in (21, 22, 23, 24)
  and ic.Total <> 0 and coalesce(EmpChargeBreakdown,0) = 1

-- Build Section 1 - The Rest pt 2.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType,
									ComponentCode, DetailSequenceNumber, DetailTotal, Section)
select	ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, ic.ChargeBase, 
		ic.ChargeMultiplier [Rate],	-- Rate to be determined by the psudocode = 0.
		ic.Total [Total], ic.ChargeType [DetailChargeType], '' [DetailComponentCode], ic.SequenceNumber [DetailSequenceNumber],
		ic.ChargeBase + ic.Total [DetailTotal], 1 [Section]
from Invoices i
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
left outer join InvoiceCharges ic on ic.CompanyID = i.CompanyID and REPLACE(ic.ClientID, NCHAR(0x00A0), '') = REPLACE(i.ClientID, NCHAR(0x00A0), '')
			and ic.DarwinInvoiceNumber = i.DarwinInvoiceNumber
left outer join ClientCharges cc on cc.CompanyID = i.CompanyID and cc.ClientID = i.ClientID
			and REPLACE(cc.DivisionID, NCHAR(0x00A0), '') = REPLACE(i.DivisionID, NCHAR(0x00A0), '')
			and REPLACE(cc.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
where i.DarwinInvoiceNumber = @invoice and i.CompanyID = @company and i.ClientID = @client
  and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') not in ('Payroll', 'Agency Credits', 'Hire Act Tax Credit', 'PTO Credit')
  and (ic.Criteria  < 4 or ic.Criteria in (13, 21, 22, 23, 24)) and ic.Total <> 0 and coalesce(EmpChargeBreakdown,0) = 1

-- Now add Totals to report.
INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 101 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 1
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 102 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 2
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 103 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 3
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 104 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section = 4
group by Section

INSERT INTO #CustomInvoice1_Section(CompanyID, ClientID, DarwinInvoiceNumber, Section, ChargeBase, Total, DetailTotal)
select @company, @client, @invoice, 1000 [Section], sum(t.ChargeBase) [ChargeBase], sum(t.Total) [Total], sum(t.DetailTotal) [DetailTotal]
from #CustomInvoice1_Section t
where section in (1,2,3,4)
group by CompanyID

-- Get the number of checks on the invoice.
declare @Checks int = 0
select @Checks = (
			select count(ech.EmployeeID) [Checks]
			from EmployeeCheckHistory ech
				INNER JOIN #Departments d ON d.Department = ech.Department
				inner join Employees e on e.CompanyID = ech.CompanyID and e.EmployeeID = ech.EmployeeID
				inner join InvoicePayrolls cip on cip.CompanyID = ech.CompanyID and  cip.ClientID = e.clientid
					   and cip.AuditControlCode = ech.AuditControlCode
				inner join Invoices i on i.CompanyID = cip.CompanyID and  i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
				INNER JOIN ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID
					   and cdd.Department = ech.Department
			where ech.CompanyID = @company and e.ClientID = @client and (i.DarwinInvoiceNumber = @invoice or i.MergedInvoiceNumber = @invoice)
		   )	

select distinct cl.CompanyID [CL_CompanyID], cl.clientid [CL_ClientID], cl.ClientName [CL_ClientName], ad.address1 [CL_Add1], ad.Address2 [CL_Add2],
	ad.City + ',' + ad.[State] + '  ' + ad.Zip [CL_CSV], ad.Phone1 [CL_Phone], ad.Fax [CL_Fax], cl.PaymentTermsID [PaymentTerms], cl.Comment1 [Comment],
	co.CompanyName [Co_CompanyName], co.Address1 [Co_Add1], co.Address2 [Co_Add2], co.City + ', ' + co.[State] + '  ' + co.Zip [Co_CSV],
	co.Phone1 [Co_Phone1], co.Fax [Co_Fax], i.StartDate, i.EndDate, i.TotalDeductions [AgencyCredits], i.[Date] [PostedDate], i.Total [SubTotal],
	i.Tax [Tax], i.GrandTotal [GrandTotal], [OutstandingBalance] = i.ClientBalance, [TotalDue] = i.GrandTotal + i.ClientBalance, @Checks [TotalChecks],
	i.Employees [TotalChecksPaid], REPLACE(cd.[Description], NCHAR(0x00A0), '') [ClientDivision], t.Id, t.CompanyID, t.ClientID, t.DarwinInvoiceNumber,
	t.ChargeType, t.SequenceNumber, t.ChargeBase,
	(case t.rate when 'Multiple' then 'Multiple' else RTRIM(CAST(CAST(t.rate/100000.0 as decimal(12,5)) AS Varchar(15))) + '%' end) as Rate, 
	t.Total, t.DetailChargeType, t.ComponentCode, t.DetailSequenceNumber, t.DetailTotal, t.SelectForPrint, t.Section
from clients cl
left outer join Companies co on co.companyid = cl.CompanyID
left outer join invoices i on i.CompanyID = cl.CompanyID and i.ClientID = cl.ClientID
inner join #CustomInvoice1_Section t on t.CompanyID = i.CompanyID and t.ClientID = t.ClientID and t.DarwinInvoiceNumber = i.DarwinInvoiceNumber
left outer join ClientDivisions cd on cd.ClientID = i.ClientID and cd.CompanyID = i.CompanyID and cd.DivisionID = i.DivisionID
left outer join ClientAddresses ad on ad.CompanyID = cl.CompanyID and ad.ClientID = cl.ClientID
			and REPLACE(ad.AddressCode, NCHAR(0x00A0), '') = coalesce(REPLACE(@addresscode, NCHAR(0x00A0), ''), 'CORPORATE')
where co.companyid = @company and cl.ClientID = @client and i.DarwinInvoiceNumber = @invoice
order by Section

If(OBJECT_ID('tempdb..#CustomInvoice1_Section') Is Not Null)
Begin
    Drop Table #CustomInvoice1_Section
End
