
CREATE PROCEDURE [dbo].[Sp_ExpSol_LD_Proration]
	-- Add the parameters for the stored procedure here
	@company INT=0,
	@client VARCHAR(15) = NULL,
	@Invoice VARCHAR(1000)= null
AS
BEGIN
	SELECT     eth.CompanyID, e.ClientID, eth.EmployeeID, i.DivisionID, eth.Department, eth.Position, ISNULL(ath.LaborDistributionLabel1, N'') AS LaborDistributionLabel1, ISNULL(ath.LaborDistributionValue1, N'') AS LaborDistributionValue1, ISNULL(ath.LaborDistributionLabel2, N'') 
                  AS LaborDistributionLabel2, ISNULL(ath.LaborDistributionValue2, N'') AS LaborDistributionValue2, ISNULL(ath.LaborDistributionLabel3, N'') AS LaborDistributionLabel3, ISNULL(ath.LaborDistributionValue3, N'') AS LaborDistributionValue3, ISNULL(ath.LaborDistributionLabel4, N'') 
                  AS LaborDistributionLabel4, ISNULL(ath.LaborDistributionValue4, N'') AS LaborDistributionValue4, ISNULL(ath.LaborDistributionLabel5, N'') AS LaborDistributionLabel5, ISNULL(ath.LaborDistributionValue5, N'') AS LaborDistributionValue5, ISNULL(ath.LaborDistributionLabel6, N'') 
                  AS LaborDistributionLabel6, ISNULL(ath.LaborDistributionValue6, N'') AS LaborDistributionValue6, ISNULL(ath.LaborDistributionLabel7, N'') AS LaborDistributionLabel7, ISNULL(ath.LaborDistributionValue7, N'') AS LaborDistributionValue7, ISNULL(ath.LaborDistributionLabel8, N'') 
                  AS LaborDistributionLabel8, ISNULL(ath.LaborDistributionValue8, N'') AS LaborDistributionValue8, eich.DarwinInvoiceNumber AS Invoice, SUM(eth.TRXAmount) AS GrossWages
FROM        dbo.EmployeeTransactionHistory AS eth LEFT OUTER JOIN
                  dbo.EmployeeAdditionalTransactionHistory AS ath ON ath.CompanyID = eth.CompanyID AND ath.RecordNumber = eth.RecordNumber INNER JOIN
                  dbo.Employees AS e ON e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID INNER JOIN
                  dbo.EmployeePaycodes AS ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode AND ep.PayType <> 5 INNER JOIN
                  dbo.EmployeeCheckHistory AS ech ON ech.EmployeeID = eth.EmployeeID AND ech.AuditControlCode = eth.AuditControlCode and ech.CompanyID = eth.CompanyID INNER JOIN
                  dbo.EmployeeInvoiceCheckHistory AS eich ON eich.CompanyID = eth.CompanyID AND eich.ClientID = e.ClientID AND eich.PaymentAdjustmentNumber = ech.PaymentAdjustmentNumber INNER JOIN
                  dbo.Invoices AS i ON i.CompanyID = eth.CompanyID AND i.ClientID = e.ClientID AND i.DarwinInvoiceNumber = eich.DarwinInvoiceNumber
WHERE     (eth.Department IN
                      (SELECT     Department
                       FROM        dbo.ClientDivisionDetails
                       WHERE     (CompanyID = eth.CompanyID) AND (ClientID = e.ClientID) AND (DivisionID = i.DivisionID))) and
					   eth.CompanyID=@company and  e.ClientID=@client and 
				  (eich.DarwinInvoiceNumber in (SELECT CAST(Item AS INTEGER)
        FROM [dbo].[SplitString](@Invoice, ',')))
GROUP BY eth.CompanyID, e.ClientID, eth.EmployeeID, i.DivisionID, eth.Department, eth.Position, ath.LaborDistributionLabel1, ath.LaborDistributionValue1, ath.LaborDistributionLabel2, ath.LaborDistributionValue2, ath.LaborDistributionLabel3, ath.LaborDistributionValue3, 
                  ath.LaborDistributionLabel4, ath.LaborDistributionValue4, ath.LaborDistributionLabel5, ath.LaborDistributionValue5, ath.LaborDistributionLabel6, ath.LaborDistributionValue6, ath.LaborDistributionLabel7, ath.LaborDistributionValue7, ath.LaborDistributionLabel8, 
                  ath.LaborDistributionValue8, eich.DarwinInvoiceNumber
	SET NOCOUNT ON;

   
END
