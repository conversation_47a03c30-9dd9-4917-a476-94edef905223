
/*
********************************************************************
**** Created by <PERSON>.
**** Date: 04/27/2015.
**** Purpose: PTO Accrual History Detail by Check Number.
****
********************************************************************
*/

-- sp_RPT_PTOAccrualDetail 1, '016', '2017-01-01', '2017-01-26', 'BrianN'

CREATE PROCEDURE [dbo].[sp_RPT_PTOAccrualDetail]  
(
	@company INT,
	@client VARCHAR(15) = NULL,
	@startdate date = NULL,
	@enddate date = NULL,
	@userid NVARCHAR(20)
)
AS 
--DECLARE @company INT = 1, @client VARCHAR(15) = '016', @startdate date = '2017-01-01', @enddate date = '2017-01-26', @userid NVARCHAR(20) = 'BrianN'

if @startdate is null
    set @startdate = getdate()
if @enddate is null
    set @enddate = getdate()

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

CREATE TABLE #Merge(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] int NOT NULL,
	[ClientId] [nvarchar](15) NULL,
	[EmployeeID] [nvarchar](15) NULL,
	[CheckDate] [date] NULL,
	[CheckNumber] [nvarchar](20),
	[AuditCode] [nvarchar](13),
	[PTOType] [int] NULL,
	[Forfeited] [decimal](19,5) NULL,
	[Accrued] [decimal](19,5) NULL,
	[Taken] [decimal](19,5) NULL,
	[Available] [decimal](19,5) NULL)

-- Handle Vacation.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 1, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray01)) <  ABS(MIN(eath.ForfeitedHoursArray01)) THEN MIN(eath.ForfeitedHoursArray01) ELSE MAX(eath.ForfeitedHoursArray01) END Forfeited, 
		CASE WHEN ABS(MAX(eath.VacationTimeAccrued)) <  ABS(MIN(eath.VacationTimeAccrued)) THEN MIN(eath.VacationTimeAccrued) ELSE MAX(eath.VacationTimeAccrued) END Accrued,
		CASE WHEN ABS(MAX(eath.VacationHours)) <  ABS(MIN(eath.VacationHours)) THEN MIN(eath.VacationHours) ELSE MAX(eath.VacationHours) END Taken,
		CASE WHEN ABS(MAX(eath.VacationAvailable)) <  ABS(MIN(eath.VacationAvailable)) THEN MIN(eath.VacationAvailable) ELSE MAX(eath.VacationAvailable) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle Sick.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 2, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray02)) <  ABS(MIN(eath.ForfeitedHoursArray02)) THEN MIN(eath.ForfeitedHoursArray02) ELSE MAX(eath.ForfeitedHoursArray02) END Forfeited, 
		CASE WHEN ABS(MAX(eath.SickTimeAccrued)) <  ABS(MIN(eath.SickTimeAccrued)) THEN MIN(eath.SickTimeAccrued) ELSE MAX(eath.SickTimeAccrued) END Accrued,
		CASE WHEN ABS(MAX(eath.SickTimeHours)) <  ABS(MIN(eath.SickTimeHours)) THEN MIN(eath.SickTimeHours) ELSE MAX(eath.SickTimeHours) END Taken,
		CASE WHEN ABS(MAX(eath.SickTimeAvailable)) <  ABS(MIN(eath.SickTimeAvailable)) THEN MIN(eath.SickTimeAvailable) ELSE MAX(eath.SickTimeAvailable) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO1.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select	eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 3, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray03)) <  ABS(MIN(eath.ForfeitedHoursArray03)) THEN MIN(eath.ForfeitedHoursArray03) ELSE MAX(eath.ForfeitedHoursArray03) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued01)) <  ABS(MIN(eath.PTOTimeAccrued01)) THEN MIN(eath.PTOTimeAccrued01) ELSE MAX(eath.PTOTimeAccrued01) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours01)) <  ABS(MIN(eath.PTOHours01)) THEN MIN(eath.PTOHours01) ELSE MAX(eath.PTOHours01) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable01)) <  ABS(MIN(eath.PTOAvailable01)) THEN MIN(eath.PTOAvailable01) ELSE MAX(eath.PTOAvailable01) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO2.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 4, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray04)) <  ABS(MIN(eath.ForfeitedHoursArray04)) THEN MIN(eath.ForfeitedHoursArray04) ELSE MAX(eath.ForfeitedHoursArray04) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued02)) <  ABS(MIN(eath.PTOTimeAccrued02)) THEN MIN(eath.PTOTimeAccrued02) ELSE MAX(eath.PTOTimeAccrued02) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours02)) <  ABS(MIN(eath.PTOHours02)) THEN MIN(eath.PTOHours02) ELSE MAX(eath.PTOHours02) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable02)) <  ABS(MIN(eath.PTOAvailable02)) THEN MIN(eath.PTOAvailable02) ELSE MAX(eath.PTOAvailable02) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO3.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 5, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray05)) <  ABS(MIN(eath.ForfeitedHoursArray05)) THEN MIN(eath.ForfeitedHoursArray05) ELSE MAX(eath.ForfeitedHoursArray05) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued03)) <  ABS(MIN(eath.PTOTimeAccrued03)) THEN MIN(eath.PTOTimeAccrued03) ELSE MAX(eath.PTOTimeAccrued03) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours03)) <  ABS(MIN(eath.PTOHours03)) THEN MIN(eath.PTOHours03) ELSE MAX(eath.PTOHours03) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable03)) <  ABS(MIN(eath.PTOAvailable03)) THEN MIN(eath.PTOAvailable03) ELSE MAX(eath.PTOAvailable03) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO4.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 6, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray06)) <  ABS(MIN(eath.ForfeitedHoursArray06)) THEN MIN(eath.ForfeitedHoursArray06) ELSE MAX(eath.ForfeitedHoursArray06) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued04)) <  ABS(MIN(eath.PTOTimeAccrued04)) THEN MIN(eath.PTOTimeAccrued04) ELSE MAX(eath.PTOTimeAccrued04) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours04)) <  ABS(MIN(eath.PTOHours04)) THEN MIN(eath.PTOHours04) ELSE MAX(eath.PTOHours04) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable04)) <  ABS(MIN(eath.PTOAvailable04)) THEN MIN(eath.PTOAvailable04) ELSE MAX(eath.PTOAvailable04) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO5.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 7, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray07)) <  ABS(MIN(eath.ForfeitedHoursArray07)) THEN MIN(eath.ForfeitedHoursArray07) ELSE MAX(eath.ForfeitedHoursArray07) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued05)) <  ABS(MIN(eath.PTOTimeAccrued05)) THEN MIN(eath.PTOTimeAccrued05) ELSE MAX(eath.PTOTimeAccrued05) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours05)) <  ABS(MIN(eath.PTOHours05)) THEN MIN(eath.PTOHours05) ELSE MAX(eath.PTOHours05) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable05)) <  ABS(MIN(eath.PTOAvailable05)) THEN MIN(eath.PTOAvailable05) ELSE MAX(eath.PTOAvailable05) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO6.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 8, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray08)) <  ABS(MIN(eath.ForfeitedHoursArray08)) THEN MIN(eath.ForfeitedHoursArray08) ELSE MAX(eath.ForfeitedHoursArray08) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued06)) <  ABS(MIN(eath.PTOTimeAccrued06)) THEN MIN(eath.PTOTimeAccrued06) ELSE MAX(eath.PTOTimeAccrued06) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours06)) <  ABS(MIN(eath.PTOHours06)) THEN MIN(eath.PTOHours06) ELSE MAX(eath.PTOHours06) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable06)) <  ABS(MIN(eath.PTOAvailable06)) THEN MIN(eath.PTOAvailable06) ELSE MAX(eath.PTOAvailable06) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO7.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 9, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray09)) <  ABS(MIN(eath.ForfeitedHoursArray09)) THEN MIN(eath.ForfeitedHoursArray09) ELSE MAX(eath.ForfeitedHoursArray09) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued07)) <  ABS(MIN(eath.PTOTimeAccrued07)) THEN MIN(eath.PTOTimeAccrued07) ELSE MAX(eath.PTOTimeAccrued07) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours07)) <  ABS(MIN(eath.PTOHours07)) THEN MIN(eath.PTOHours07) ELSE MAX(eath.PTOHours07) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable07)) <  ABS(MIN(eath.PTOAvailable07)) THEN MIN(eath.PTOAvailable07) ELSE MAX(eath.PTOAvailable07) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO8.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 10, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray10)) <  ABS(MIN(eath.ForfeitedHoursArray10)) THEN MIN(eath.ForfeitedHoursArray10) ELSE MAX(eath.ForfeitedHoursArray10) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued08)) <  ABS(MIN(eath.PTOTimeAccrued08)) THEN MIN(eath.PTOTimeAccrued08) ELSE MAX(eath.PTOTimeAccrued08) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours08)) <  ABS(MIN(eath.PTOHours08)) THEN MIN(eath.PTOHours08) ELSE MAX(eath.PTOHours08) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable08)) <  ABS(MIN(eath.PTOAvailable08)) THEN MIN(eath.PTOAvailable08) ELSE MAX(eath.PTOAvailable08) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO9.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 11, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray11)) <  ABS(MIN(eath.ForfeitedHoursArray11)) THEN MIN(eath.ForfeitedHoursArray11) ELSE MAX(eath.ForfeitedHoursArray11) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued09)) <  ABS(MIN(eath.PTOTimeAccrued09)) THEN MIN(eath.PTOTimeAccrued09) ELSE MAX(eath.PTOTimeAccrued09) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours09)) <  ABS(MIN(eath.PTOHours09)) THEN MIN(eath.PTOHours09) ELSE MAX(eath.PTOHours09) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable09)) <  ABS(MIN(eath.PTOAvailable09)) THEN MIN(eath.PTOAvailable09) ELSE MAX(eath.PTOAvailable09) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Handle PTO10.
insert into #Merge(CompanyId, ClientId, EmployeeID, AuditCode, CheckDate, CheckNumber, PTOType, Forfeited, Accrued, Taken, Available)
select eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber, 12, 
		CASE WHEN ABS(MAX(eath.ForfeitedHoursArray12)) <  ABS(MIN(eath.ForfeitedHoursArray12)) THEN MIN(eath.ForfeitedHoursArray12) ELSE MAX(eath.ForfeitedHoursArray12) END Forfeited, 
		CASE WHEN ABS(MAX(eath.PTOTimeAccrued10)) <  ABS(MIN(eath.PTOTimeAccrued10)) THEN MIN(eath.PTOTimeAccrued10) ELSE MAX(eath.PTOTimeAccrued10) END Accrued,
		CASE WHEN ABS(MAX(eath.PTOHours10)) <  ABS(MIN(eath.PTOHours10)) THEN MIN(eath.PTOHours01) ELSE MAX(eath.PTOHours10) END Taken,
		CASE WHEN ABS(MAX(eath.PTOAvailable10)) <  ABS(MIN(eath.PTOAvailable10)) THEN MIN(eath.PTOAvailable10) ELSE MAX(eath.PTOAvailable10) END Available
from EmployeeAdditionalTransactionHistory eath
	INNER JOIN #Departments d ON d.Department = eath.Department
	INNER JOIN #EmployeeIDs e ON e.EmployeeID = eath.EmployeeID
	inner join ClientEmployees ce on ce.CompanyID = eath.CompanyID and ce.EmployeeID = eath.EmployeeID
where ce.CompanyID = @company and ce.ClientID = @client and DATEADD(dd, 0, DATEDIFF(dd, 0, eath.CheckDate)) between @startdate and @enddate
GROUP BY eath.CompanyID, ce.ClientID, eath.EmployeeID, eath.AuditControlCode, eath.CheckDate, eath.CheckNumber

-- Output
select m.companyid, m.ClientId, m.EmployeeID, e.LastName + ', ' + e.FirstName AS EmployeeName, DATEADD(dd, 0, DATEDIFF(dd, 0, CheckDate)) as CheckDate, CheckNumber, AuditCode, m.PTOType,
	   ept.[Description] [PTOName], COALESCE(pto.[Description],'') [PTOTypeDescription], Available/100.0 - Accrued + Taken/100.0 [BeginningBalance], 
	   Forfeited/100.0 [Forfeited], Accrued [Accrued], Taken/100.0 [Taken], Available/100.0 [Available],
	   ept.LastCarryoverAmount/100.0 [EC Beginning Balance], ept.AvailableHours/100.0 [EC Available], ept.PTOAccrualAmount/100.0 [EC Accrued],
	   ept.ForfeitedHours/100.0 [EC Forfeited], ept.PTOAmountTaken/100.0 [EC Taken]
from #Merge m
	left outer join ClientPTOTypes pto on pto.CompanyID = m.CompanyId and pto.ClientID = m.ClientId AND pto.PTOType = m.PTOType
	inner join EmployeePTOTypes ept on ept.CompanyID = m.CompanyId and ept.EmployeeID = m.EmployeeID AND ept.PTOType = m.PTOType
	inner join Employees e ON e.EmployeeID = m.EmployeeID AND e.CompanyID = m.CompanyId
order by m.EmployeeID, CheckDate, CheckNumber, AuditCode, m.PTOType

DROP TABLE #Merge
DROP TABLE #Departments
DROP TABLE #Divisions
DROP TABLE #EmployeeIDs
