

/*
********************************************************************
**** Created by <PERSON>.
**** Date: 03/10/2015.
**** Purpose: Returns all Client records with minimal data.
****
********************************************************************
*/

-- sp_GetClients 1, '001'
CREATE PROCEDURE [dbo].[sp_GetClients]  
(
	@company INT,
	@client VARCHAR(15) = NULL
)
AS
SELECT c.CompanyID, c.ClientID, ClientName, ClientClass, CorporateClientID, c.EmployerStateIDNumber [FederalId], c.<PERSON>, StatementName, ShortName, 
	   c.AddressCode, PaymentTermsID, Comment1, Comment2, c.UserDefined1, c.UserDefined2, TaxExempt1, TaxExempt2, TaxRegistrationNumber, Inactive, Hold, 
	   c.<PERSON>ate, c.ModifiedDate, c.CountryCode, RegionID, BusinessType, ClientStatus, ClientType, County, a.Address1, a.Address2, 
	   a.City + ' ' + a.[State] + '  ' + a.Zip [CSZ], left(a.Phone1,10) [Phone1],
	   CASE WHEN coalesce(a.Phone1,'') = '' THEN '' ELSE substring(a.phone1,11,len(a.phone1)-1) END  [PhoneExt],
	   c.EmployerStateIDNumber, ci.Image [Image], ci.ImageEncoding
FROM Clients c
left outer join ClientAddresses a on a.CompanyID = c.CompanyID and a.ClientID = c.ClientID and a.AddressCode = c.AddressCode
left outer join ClientImages ci on ci.ID = c.Logo
WHERE (c.CompanyID = @company or @company is null) AND (c.ClientID = @client or @client is null)
