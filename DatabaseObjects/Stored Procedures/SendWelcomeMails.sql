-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	11/23/2022
-- =============================================
CREATE PROCEDURE [dbo].[SendWelcomeMails] 
AS
BEGIN
	Declare
	@Object int
	,@status int
	,@msg varchar(255)

	Declare @ResponseText as Varchar(8000);
	Declare @URL as varchar(255) = (Select DnetAddress from ProjectSetup) + '/NotificationAPI/SendWelcomeEmailNotifications'
	exec SendWakeUp;
	Exec sp_OACreate 'MSXML2.ServerXMLHTTP', @Object OUT;
	EXEC  sp_OAMethod @Object, 'open', NULL, 'post',@URL, 'false'

	Exec sp_OAMethod @Object, 'setRequestHeader', null, 'Content-Type', 'application/json'
	Exec sp_OAMethod @Object, 'send', null, ''
	exec sp_OAGetProperty @Object, 'status', @status OUT
	Exec sp_OAMethod @Object, 'responseText', @ResponseText OUTPUT
	Select @ResponseText, @status
	Insert Into SQLNotificationLog([Type],[CompanyID],[ClientID],[EmployeeID],[InvoiceNumber],[Status],[DateStamp],[Response])
	Values
	('SendWelcomeEmailNotifications',0,0,'',0,@status,GETDATE(),@ResponseText)
END
