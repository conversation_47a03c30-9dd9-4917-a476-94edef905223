-- =============================================
-- Author:		<PERSON>
-- Create date: 2017-05-01
-- Description:	
-- =============================================
CREATE PROCEDURE [dbo].[sp_ExpSol_GL_Wages]
	@company int,
	@client varchar(50),
	@divisionID varchar(50),
	@userid NVARCHAR(20)
	
AS
BEGIN
	SET NOCOUNT ON;
	
    CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
	INSERT INTO #Departments (DivisionID, Department) VALUES(@divisionID, '')

	IF @divisionID != '_ALL_'
		INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid) WHERE DivisionID = @divisionID
	ELSE
		INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

	SELECT eth.CompanyID, e.ClientID, eth.EmployeeID, eth.PayrollCode, ISNULL(eth.PayrollRecordType,0) AS PayrollRecordType, eth.Department, eth.Position, 
                         ISNULL(ath.LaborDistributionLabel1, N'') AS LaborDistributionLabel1, ISNULL(ath.LaborDistributionValue1, N'') AS LaborDistributionValue1, 
                         ISNULL(ath.LaborDistributionLabel2, N'') AS LaborDistributionLabel2, ISNULL(ath.LaborDistributionValue2, N'') AS LaborDistributionValue2, 
                         ISNULL(ath.LaborDistributionLabel3, N'') AS LaborDistributionLabel3, ISNULL(ath.LaborDistributionValue3, N'') AS LaborDistributionValue3, 
                         ISNULL(ath.LaborDistributionLabel4, N'') AS LaborDistributionLabel4, ISNULL(ath.LaborDistributionValue4, N'') AS LaborDistributionValue4, 
                         ISNULL(ath.LaborDistributionLabel5, N'') AS LaborDistributionLabel5, ISNULL(ath.LaborDistributionValue5, N'') AS LaborDistributionValue5, 
                         ISNULL(ath.LaborDistributionLabel6, N'') AS LaborDistributionLabel6, ISNULL(ath.LaborDistributionValue6, N'') AS LaborDistributionValue6, 
                         ISNULL(ath.LaborDistributionLabel7, N'') AS LaborDistributionLabel7, ISNULL(ath.LaborDistributionValue7, N'') AS LaborDistributionValue7, 
                         ISNULL(ath.LaborDistributionLabel8, N'') AS LaborDistributionLabel8, ISNULL(ath.LaborDistributionValue8, N'') AS LaborDistributionValue8,
						 ISNULL(ep.PayType,0) AS PayType, ISNULL(ep.ReportAsWages,0) AS ReportAsWages, eich.DarwinInvoiceNumber Invoice, eich.CheckDate, ISNULL(eth.TRXAmount,0) AS TRXAmount, eth.AuditControlCode
	FROM dbo.EmployeeTransactionHistory eth
        INNER JOIN dbo.EmployeeAdditionalTransactionHistory AS ath ON ath.CompanyID = eth.CompanyID AND ath.RecordNumber = eth.RecordNumber 
		INNER JOIN dbo.Employees e ON e.EmployeeID = eth.EmployeeID
		INNER JOIN dbo.EmployeeInvoiceCheckHistory eich ON eich.CompanyID = eth.CompanyID AND eich.ClientID = e.ClientID AND eich.CheckNumber = eth.CheckNumber
		INNER JOIN dbo.EmployeePaycodes ep ON ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode AND ep.CompanyID = eth.CompanyID
		INNER JOIN dbo.Invoices i ON i.CompanyID = eth.CompanyID AND i.ClientID = e.ClientID AND i.DarwinInvoiceNumber = eich.DarwinInvoiceNumber
		INNER JOIN dbo.ClientDivisionDetails cdd ON cdd.CompanyID = eth.CompanyID AND cdd.ClientID = e.ClientID AND cdd.DivisionID = i.DivisionID AND cdd.Department = eth.Department
		INNER JOIN #Departments d ON d.Department = eth.Department
	WHERE eth.CompanyID = @company AND e.ClientID = @client

	DROP TABLE #Departments
	
	END


