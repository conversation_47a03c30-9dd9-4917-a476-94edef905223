/*
********************************************************************
**** Created by <PERSON><PERSON> gohel.
**** Date: 03/11/2015.
**** Purpose: Generates data for the SSRS Report Invoice
**** 7/2/2024 Updated for CH-886
********************************************************************
*/

CREATE   PROCEDURE [dbo].[sp_RPT_DescriptioInvoice_D2]  
(
	@company INT,
	@client VARCHAR(15),
	@invoice INT,
	@userid NVARCHAR(20)
)
AS	

-- Get Client "Main" address in case the divisional address IS NULL.
declare @AddressCode varchar(50)
select @AddressCode = (Select Addresscode from Clients where CompanyID = @company and clientid = @client)

CREATE TABLE #StandardInvoice_Section(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NULL,
	[ClientID] [nvarchar](15) NULL,
	[DarwinInvoiceNumber] [int] NULL,
	[ChargeType] [nvarchar](30) NULL,
	[SequenceNumber] [int] NULL,
	[ChargeBase] [decimal](19,5) NULL,
	[Rate] varchar(50) NULL,
	[Total] [decimal](19,5) NULL,
	[DetailChargeType] [nvarchar](30) NULL,
	[ComponentCode] [nvarchar](30) NULL,
	[DetailSequenceNumber] [int] NULL,
	[DetailTotal] [decimal](19,5) NULL,
	[SelectForPrint] [tinyint] NULL,
	[PayrollNumber] [nvarchar] (70)NULL,
	[Section] [int] NULL)

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@company, @client, @userid)

CREATE TABLE #Divisions(DivisionID NVARCHAR(15))
Insert into #Divisions Select DISTINCT DivisionID from #Departments

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployeesWithInactive(@company, @client, @userid)

-- Payroll Stuff.
INSERT INTO #StandardInvoice_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType, ComponentCode, DetailSequenceNumber, DetailTotal, PayrollNumber,Section)
SELECT ic.CompanyID, ic.ClientID, ic.DarwinInvoiceNumber, cd.ComponentCode, ic.SequenceNumber, ic.ChargeBase [ChargeBase],
		case WHEN ic.PrintDetails = 0 then '' else (case when ic.Criteria in (4,5,6,7,8,9,10,11,12,14) then FORMAT((ic.ChargeMultiplier * 100), 'N', 'en-us') + '%' else cast(ic.ChargeMultiplier as varchar(20)) end) end as [Rate],
		ic.total [Total], cd.ChargeType [DetailChargeType], cd.ComponentCode [DetailComponentCode], cd.SequenceNumber [DetailSequenceNumber],
		cd.Total [DetailTotal], ip.AuditControlCode,2 [Section]
from InvoiceCharges ic
	INNER JOIN Invoices i ON i.CompanyID = ic.CompanyID AND i.ClientID = ic.ClientID AND i.DarwinInvoiceNumber = ic.DarwinInvoiceNumber
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
		
				LEFT OUTER JOIN Clients c on c.CompanyID = ic.CompanyID and c.ClientID=ic.ClientID
	left outer join InvoiceChargeDetails cd on cd.CompanyID = ic.CompanyID and cd.ClientID = ic.ClientID
				and cd.DarwinInvoiceNumber = ic.DarwinInvoiceNumber and REPLACE(cd.ChargeType, NCHAR(0x00A0), '') = REPLACE(ic.ChargeType, NCHAR(0x00A0), '')
				left outer join InvoicePayrolls ip on ip.CompanyID = ic.CompanyID and ip.DarwinInvoiceNumber = i.DarwinInvoiceNumber and ip.ClientID = ic.ClientID
where ic.DarwinInvoiceNumber = @invoice and ic.CompanyID = @company and ic.ClientID = @client and REPLACE(ic.ChargeType, NCHAR(0x00A0), '') = 'PAYROLL'
  and cd.SelectForPrint = 1 and cd.total <> 0

-- Non-Payroll Stuff.
INSERT INTO #StandardInvoice_Section(CompanyID, ClientID, DarwinInvoiceNumber, ChargeType, SequenceNumber, ChargeBase, Rate, Total, DetailChargeType, ComponentCode, DetailSequenceNumber, DetailTotal, PayrollNumber,Section)
SELECT	i.CompanyID, i.ClientID, i.DarwinInvoiceNumber, ic.ChargeType, ic.SequenceNumber, ic.ChargeBase [ChargeBase],
		case WHEN ic.PrintDetails = 0 then '' else (case when ic.Criteria in (4,5,6,7,8,9,10,11,12,14) then FORMAT((ic.ChargeMultiplier * 100), 'N', 'en-us') + '%' else cast(ic.ChargeMultiplier as varchar(20)) end) end as [Rate],
		ic.Total [Total], 'ChargeType' [DetailChargeType], 'ComponentCode' [DetailComponentCode], 0 [DetailSequenceNumber], ic.Total [DetailTotal],  ip.AuditControlCode,2 [Section]
from clients cl
	LEFT OUTER JOIN Companies co on co.companyid = cl.CompanyID
	LEFT OUTER JOIN Invoices i on  i.CompanyID = cl.CompanyID and  i.ClientID = cl.ClientID
	INNER JOIN #Divisions d ON d.DivisionID = i.DivisionID
	LEFT OUTER JOIN InvoiceCharges ic on ic.CompanyID = i.CompanyID and REPLACE(ic.clientid, NCHAR(0x00A0), '')  = REPLACE(i.clientid, NCHAR(0x00A0), '')
				and ic.DarwinInvoiceNumber = i.DarwinInvoiceNumber
	LEFT OUTER JOIN ClientDivisions cd on cd.ClientID = i.ClientID and cd.CompanyID = i.CompanyID and cd.DivisionID = i.DivisionID
	LEFT OUTER JOIN ClientAddresses ad on ad.CompanyID = cl.CompanyID and ad.ClientID = cl.ClientID
				and REPLACE(ad.AddressCode, NCHAR(0x00A0), '') = coalesce(REPLACE(cd.AddressCode, NCHAR(0x00A0), ''), REPLACE(@addresscode, NCHAR(0x00A0), ''), 'CORPORATE')
					left outer join InvoicePayrolls ip on ip.CompanyID = ic.CompanyID and ip.DarwinInvoiceNumber = i.DarwinInvoiceNumber and ip.ClientID = ic.ClientID
WHERE (cl.CompanyID = @company or @company IS NULL) AND (cl.ClientID = @client or @client IS NULL) and ChargeType not in ('OUTSTANDING BALANCE')
  AND (i.DarwinInvoiceNumber = @invoice or @invoice IS NULL) AND (ic.ChargeBase + ic.ChargeMultiplier + ic.Total <> 0) AND ic.ChargeType <> 'PAYROLL'

-- Get the number of checks on the invoice.
declare @Checks int = 0
select @Checks = (select count(ech.EmployeeID) [Checks]
					from PayrollWorkHeaders ech
					INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
						inner join Employees e on e.CompanyID = ech.CompanyID and e.EmployeeID = ech.EmployeeID
						inner join InvoicePayrolls cip on cip.CompanyID = ech.CompanyID and  cip.ClientID = e.ClientID and cip.PayrollNumber = ech.PayrollNumber
						inner join Invoices i on i.CompanyID = cip.CompanyID and  i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
						INNER JOIN ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID and cdd.Department = e.Department
where i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber=@invoice or i.MergedInvoiceNumber=@invoice)
				 )	

declare @uprcc nvarchar(255)
select @uprcc = (SELECT STUFF((SELECT ', ' + rtrim(convert(nvarchar(20),AuditControlCode))
								FROM   InvoicePayrolls
								WHERE CompanyID = @company and ClientID = @client and (DarwinInvoiceNumber = @invoice or MergedInvoiceNumber = @invoice)
								FOR XML PATH('')),1,1,'') UPRCC
				FROM   InvoicePayrolls
				WHERE CompanyID = @company and ClientID = @client and DarwinInvoiceNumber = @invoice)

declare @StartingCheck VarChar(20),
	@EndingCheck VarChar(20)

Select @StartingCheck = StartingCheck,
	@EndingCheck = EndingCheck
From (
	select i.CompanyID,
		i.ClientID,
		--i.DarwinInvoiceNumber,
		Min(CheckNumber) as 'StartingCheck',
		Max(CheckNumber) as 'EndingCheck'
						from EmployeeCheckHistory ech
						INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
							inner join Employees e on e.CompanyID = ech.CompanyID and e.EmployeeID = ech.EmployeeID
							inner join InvoicePayrolls cip on cip.CompanyID = ech.CompanyID and  cip.ClientID = e.ClientID and cip.PayrollNumber = ech.PayrollNumber
							inner join Invoices i on i.CompanyID = cip.CompanyID and  i.ClientID = cip.ClientID and i.InvoiceNumber = cip.InvoiceNumber
							INNER JOIN ClientDivisionDetails cdd on cdd.CompanyID = i.CompanyID and cdd.ClientID = i.ClientID and cdd.DivisionID = i.DivisionID and cdd.Department = e.Department
	where i.CompanyID = @company and i.ClientID = @client and (i.DarwinInvoiceNumber=@invoice or i.MergedInvoiceNumber=@invoice)
	Group By i.CompanyID,
		i.ClientID
--		i.DarwinInvoiceNumber
	) s


select i.StartDate, 
	i.EndDate, 
	i.DarwinInvoiceNumber, 
	i.TotalDeductions [AgencyCredits], 
	i.[Date] [PostedDate], 
	i.Total,
	i.Tax, 
	i.GrandTotal, 
	i.ClientBalance [OutstandingBalance] ,
	i.CheckDate, 
	@Checks [TotalChecks], 
	@StartingCheck [StartingCheck],
	@EndingCheck [EndingCheck],
	i.Employees [TotalChecksPaid], 
	REPLACE(i.DivisionID, NCHAR(0x00A0), '') [ClientDivision],
	t.Id, 
	t.CompanyID, 
	t.ClientID, 
	t.DarwinInvoiceNumber [DIN], 
	CASE WHEN t.ChargeType = 'AGENCY CREDITS' THEN 'EMP DEDUCTION DEBITS/CREDITS' ELSE t.ChargeType END 'ChargeType',
	t.SequenceNumber, 
	t.ChargeBase, 
	t.Rate, 
	--t.Total,
	t.DetailChargeType,
	t.DetailTotal,
	CASE WHEN NULLIF(i.CommentArray1,'') IS NULL THEN i.CommentArray1 ELSE i.CommentArray1 + CHAR(13)+CHAR(10) END
		+ CASE WHEN NULLIF(i.CommentArray2,'') IS NULL THEN i.CommentArray2 ELSE i.CommentArray2 + CHAR(13)+CHAR(10) END
		+ CASE WHEN NULLIF(i.CommentArray3,'') IS NULL THEN i.CommentArray3 ELSE i.CommentArray3 + CHAR(13)+CHAR(10) END
		+ CASE WHEN NULLIF(i.CommentArray4,'') IS NULL THEN i.CommentArray4 ELSE i.CommentArray4 + CHAR(13)+CHAR(10) END as 'Comments1',
	CASE WHEN NULLIF(i.Comment1,'') IS NULL THEN i.Comment1 ELSE i.Comment1 + CHAR(13)+CHAR(10) END +
	i.Comment2 as 'Comments2',@uprcc as [PayrollNumber],ad.ShippingMethod as [Delivery],c.PaymentTermsID as [Method],i.DebitDate as [ACHDraftDate]
	
from #StandardInvoice_Section t
	left outer join invoices i on i.CompanyID = t.CompanyID 
		and i.ClientID = t.ClientID
	left outer join clients c on c.ClientID=t.ClientID 
		and c.CompanyID = t.CompanyID
		LEFT OUTER JOIN ClientDivisions cd on cd.ClientID = i.ClientID and cd.CompanyID = i.CompanyID and cd.DivisionID = i.DivisionID
		LEFT OUTER JOIN ClientAddresses ad on ad.CompanyID = t.CompanyID and ad.ClientID = t.ClientID
				and REPLACE(ad.AddressCode, NCHAR(0x00A0), '') = coalesce(REPLACE(cd.AddressCode, NCHAR(0x00A0), ''), REPLACE(@addresscode, NCHAR(0x00A0), ''), 'CORPORATE')
				
where t.companyid = @company 
	and t.ClientID = @client 
	and i.DarwinInvoiceNumber = @invoice
order by Section

DROP TABLE #Departments
DROP TABLE #Divisions
DROP TABLE #EmployeeIDs
DROP TABLE #StandardInvoice_Section

