
-- =============================================
-- Author:		<Dhara Gohel>
-- Create date: <04/07/2022>
-- Description:	<returns the reslt for the PayrollCheckRegisterbypaycode report>
-- =============================================
CREATE PROCEDURE [dbo].[Sp_RPT_D2_PayrollCheckRegister]
	
	@PayrollNo nvarchar(max) = NULL,
	@userid NVARCHAR(20)='',
	@company int=0,
	@client nvarchar(10)=''

AS
BEGIN

select pwh.EmployeeID, EmployeeName = (e.LastName + ', ' + e.FirstName), sum(isnull(pwh.GrossWagesPayRun,0) + isnull(pwh.ReportedTips,0) + isnull(pwh.ChargedTips,0)) as 'GrossWages', sum(isnull(pwh.FederalWithholdingPayRun,0)) as 'Federal',
sum(isnull(pwh.FICASocialSecurityWithholdingPayRun,0)) as 'FICA SS', sum(isnull(pwh.FICAMedicareWithholdingPayRun,0)) as 'FICA MED',isnull(sum(pwh.NetWagesPayRun),0) as 'Netwages',
pwh.CheckNumber as 'CheckNumber', sum(isnull(pwd.TotalDeduction,0)) as 'Deductions', sum(isnull(pwb.TotalBenefit,0)) as 'Benefits',
sum(isnull(pwst.TotalStateTax,0)) as 'State', sum(isnull(pwl.TotalLocalTax,0)) as 'Local', sum(isnull(pwpc.TotalNonGross,0)) as 'NonGrossWages',
c.CheckDate, c.PayrollNumber, c.ClientID, c.ProfileID, c.PayPeriod_BeginDate, c.PayPeriod_EndDate
from PayrollWorkHeaders pwh

left outer join Employees e on e.EmployeeID = pwh.EmployeeID and e.CompanyID = pwh.CompanyID
left outer join ClientPayrollSchedules c on c.PayrollNumber = pwh.PayrollNumber and c.CompanyID = pwh.CompanyID
left outer join (select CompanyID, EmployeeID, PayrollNumber, SUM(TotalDeduction) as TotalDeduction 
				 FROM  PayrollWorkDeductions pwd 
				 GROUP BY CompanyID, EmployeeID, PayrollNumber) pwd on pwd.CompanyID = pwh.CompanyID and pwd.EmployeeID = pwh.EmployeeID and pwd.PayrollNumber = pwh.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, SUM(TotalBenefit) as TotalBenefit
				 FROM  PayrollWorkBenefits pwb 
				 GROUP BY CompanyID, EmployeeID, PayrollNumber) pwb on pwb.CompanyID = pwh.CompanyID and pwb.EmployeeID = pwh.EmployeeID and pwb.PayrollNumber = pwh.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, SUM(TotalStateTax + StateTaxOnTips) as TotalStateTax
				 FROM  PayrollWorkStateTaxes pwst 
				 GROUP BY CompanyID, EmployeeID, PayrollNumber) pwst on pwst.CompanyID = pwh.CompanyID and pwst.EmployeeID = pwh.EmployeeID and pwst.PayrollNumber = pwh.PayrollNumber
left outer join (select CompanyID, EmployeeID, PayrollNumber, SUM(TotalLocalTax + LocalTaxOnTips) as TotalLocalTax 
				 FROM  PayrollWorkLocalTaxes pwl 
				 GROUP BY CompanyID, EmployeeID, PayrollNumber) pwl on pwl.CompanyID = pwh.CompanyID and pwl.EmployeeID = pwh.EmployeeID and pwl.PayrollNumber = pwh.PayrollNumber
left outer join (select pwpc1.CompanyID, pwpc1.EmployeeID, PayrollNumber, SUM(TotalPay) as TotalNonGross 
				 FROM  PayrollWorkPayCodes pwpc1
				 INNER JOIN EmployeePaycodes epc ON epc.CompanyID = pwpc1.CompanyID and epc.EmployeeID = pwpc1.EmployeeID and epc.PayRecord = pwpc1.PayRecord and epc.PayType = pwpc1.PayType
				 WHERE pwpc1.PayType = 5 and epc.ReportAsWages = 0
				 GROUP BY pwpc1.CompanyID, pwpc1.EmployeeID, PayrollNumber) pwpc on pwpc.CompanyID = pwh.CompanyID and pwpc.EmployeeID = pwh.EmployeeID and pwpc.PayrollNumber = pwh.PayrollNumber
where pwh.PayrollNumber = @PayrollNo and pwh.CompanyID=@company and e.ClientID=@client  and pwh.IsRemoved = 0
group by pwh.EmployeeID, e.LastName + ', ' + e.FirstName, pwh.CheckNumber, c.CheckDate, c.PayrollNumber, c.ClientID, c.ProfileID, c.PayPeriod_BeginDate, c.PayPeriod_EndDate

END



