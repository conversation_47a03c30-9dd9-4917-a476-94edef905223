-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	copy all document's mapping to on-boarding profile task document
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBProfile_RemoveDocument]
	-- Add the parameters for the stored procedure here
	@CompanyID int,
	@DocumentID int,
	@ProfileID int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	DELETE FROM OBProfileTaskDocumentAssignments WHERE CompanyID = @CompanyID AND ProfileID = @ProfileID AND DocumentID = @DocumentID
	DELETE FROM OBProfileTaskDocumentMapping WHERE CompanyID = @CompanyID AND ProfileID = @ProfileID AND DocumentID = @DocumentID
	DELETE FROM OBProfileTaskDocuments WHERE CompanyID = @CompanyID AND ProfileID = @ProfileID AND DocumentID = @DocumentID
END
