-- =============================================
-- Author:		<PERSON><PERSON><PERSON>
-- Create date: <Create Date,,>
-- Description:	Create record id for new OBProcessRecord
-- =============================================
CREATE PROCEDURE [dbo].[usp_OBProcess_AddRecord]
	@CompanyID int, 
	@EmployeeID nvarchar(15),
	@TaskID int,
	@RecordID int OUTPUT
AS
BEGIN
	SET NOCOUNT ON;
	INSERT INTO OBProcessRecords(CompanyID, EmployeeID, TaskID, ImportStatus, EECompleted, CCCompleted)
	VALUES  (@CompanyID,@EmployeeID,@TaskID,0,0,0)
	SELECT @RecordID = @@IDENTITY
END

