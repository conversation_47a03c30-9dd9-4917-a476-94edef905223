-- =============================================
-- Author:		BNeumann
-- Create date: 
-- Description:	
-- =============================================

CREATE PROCEDURE [dbo].[sp_Report_PayrollRegister]
	@company INT,
	@client VARCHAR(15),
	@invoice int = NULL,
	@startdate DATETIME = NULL,
	@enddate DATETIME = NULL,
	@userid NVARCHAR(20)
AS

BEGIN
-- exec sp_Report_PayrollRegister 1,'001',100,NULL,NULL,'BrianN'
--DECLARE @company INT = 1, @client VARCHAR(15) = '001', @userid NVARCHAR(20) = 'BrianN',
--		@invoice INT = NULL,
--		@startdate DATETIME = '2016-12-28',
--		@enddate DATETIME = '2016-12-30'

DECLARE @companyInternal INT = @company,
		@clientInternal VARCHAR(15) = @client,
		@invoiceInternal int = @invoice,
		@startdateInternal DATETIME = @startdate,
		@enddateInternal DATETIME = @enddate,
		@useridInternal NVARCHAR(20) = @userid

CREATE TABLE #Departments(DivisionID NVARCHAR(15), Department NVARCHAR(6))
INSERT INTO #Departments SELECT DivisionID, Department FROM GetAllowedDepartments(@companyInternal, @clientInternal, @useridInternal)

CREATE TABLE #EmployeeIDs(EmployeeID NVARCHAR(15))
INSERT INTO #EmployeeIDs SELECT * FROM GetAllowedEmployees(@companyInternal, @clientInternal, @useridInternal)

CREATE TABLE #AuditControlCodes(AuditControlCode NVARCHAR(50), DarwinInvoiceNumber INT)
INSERT INTO #AuditControlCodes
SELECT ip.AuditControlCode, i.DarwinInvoiceNumber
FROM Invoices i
	INNER JOIN InvoicePayrolls ip ON ip.CompanyID = i.CompanyID AND ip.ClientID = i.ClientID AND ip.InvoiceNumber = i.InvoiceNumber
WHERE i.CompanyID = @companyInternal AND i.ClientID = @clientInternal AND i.Posted = 1 AND
	((i.DarwinInvoiceNumber = @invoiceInternal OR @invoiceInternal IS NULL) AND (@startdateInternal IS NULL OR @enddateInternal IS NULL OR i.CheckDate between @startdateInternal and @enddateInternal))

INSERT INTO #AuditControlCodes
SELECT ip.AuditControlCode, i.DarwinInvoiceNumber
FROM Invoices i
	INNER JOIN InvoicePayrolls ip ON ip.CompanyID = i.CompanyID AND ip.ClientID = i.ClientID AND ip.InvoiceNumber = i.InvoiceNumber
    INNER JOIN #AuditControlCodes cc ON cc.DarwinInvoiceNumber = i.MergedInvoiceNumber
WHERE i.CompanyID = @companyInternal AND i.ClientID = @clientInternal AND i.MergedInvoiceNumber = @invoiceInternal

CREATE TABLE #EmployeeCheckHistory(EmployeeID nvarchar(30) NULL, Department nvarchar(50) NULL, CheckNumber nvarchar(50), CheckDate datetime NULL, Net decimal(19,5) NULL, FICASS decimal(19,5) NULL, FICAM decimal(19,5) NULL, Fed decimal(19,5) NULL)
INSERT INTO #EmployeeCheckHistory
SELECT ech.EmployeeID, ech.Department, ech.CheckNumber, ech.CheckDate, ech.NetWagesPayRun, ech.FICASSWithholdingPayRun + ech.FICASSTaxOnTips, ech.FICAMWithholdingPayRun + ech.FICAMTaxOnTips, ech.FederalWithholdingPayRun
FROM EmployeeCheckHistory ech
	INNER JOIN #AuditControlCodes a ON a.AuditControlCode = ech.AuditControlCode
	INNER JOIN #Departments d ON d.Department = ech.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = ech.EmployeeID
	INNER JOIN Employees e ON e.EmployeeID = ech.EmployeeID and e.CompanyID = ech.CompanyID
	INNER JOIN EmployeeInvoiceCheckHistory eich ON eich.CompanyID = ech.CompanyID AND eich.ClientID = e.ClientID AND eich.CheckNumber = ech.CheckNumber and eich.EmployeeID = ech.EmployeeID and eich.Department = ech.Department
WHERE ech.CompanyID = @companyInternal AND e.ClientID = @clientInternal

-- * * * * *     NET PAY    * * * * * * * 
CREATE TABLE #NetPay(EmployeeID nvarchar(30) NULL, Department nvarchar(50) NULL, CheckNumber INT, CheckDate datetime NULL, NetPay decimal(19,5) NULL)
INSERT INTO #NetPay(EmployeeID, Department, CheckNumber, CheckDate, NetPay)
SELECT ech.EmployeeID, ech.Department, CAST(ech.CheckNumber AS INT), ech.CheckDate, ISNULL(SUM(ech.Net),0) NetPay
FROM #EmployeeCheckHistory ech
GROUP BY ech.EmployeeID, ech.Department, ech.CheckNumber, ech.CheckDate

CREATE TABLE #Employees(EmployeeID NVARCHAR(15), EmployeeName NVARCHAR(50), EmployeeSSN NVARCHAR(15))
INSERT INTO #Employees(EmployeeID, EmployeeName, EmployeeSSN)
SELECT DISTINCT np.EmployeeID, e.LastName + ', ' + e.FirstName EmployeeName, '***-**-' + SUBSTRING(e.SSN, 6, 4) EmployeeSSN
FROM #NetPay np
	INNER JOIN #Departments d ON d.Department = np.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = np.EmployeeID
	INNER JOIN Employees e ON e.EmployeeID = np.EmployeeID and e.CompanyID = @companyInternal


CREATE TABLE #Output
(
	EmployeeID NVARCHAR(15),
	Department NVARCHAR(6),
	Position NVARCHAR(50) NULL,
	CheckNumber INT NULL,
	CheckDate DATETIME NULL,
	PayrollCode NVARCHAR(10),
	Units DECIMAL(19,5) NULL,
	PayRate DECIMAL(19,5) NULL,
	Amount DECIMAL(19,5),
	PayCatagory NVARCHAR(15),
	GrossWagesFlag BIT NULL
)

-- * * * * *      Earnings     * * * * * * * 
INSERT INTO #Output(EmployeeID, Department, Position, CheckNumber, CheckDate, PayrollCode, Units, PayRate, Amount, PayCatagory, GrossWagesFlag)
SELECT e.EmployeeID, eth.Department, eth.Position, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, SUM(eth.UnitsToPay) Units, eth.PayRate, SUM(eth.TRXAmount) Wages, 'Earnings' PayCatagory, GrossWagesFlag = CAST(CASE WHEN ep.PayType = 5 THEN ep.ReportAsWages ELSE 1 END AS BIT)
FROM EmployeeTransactionHistory eth
	 INNER JOIN #AuditControlCodes a ON eth.AuditControlCode = a.AuditControlCode
	 INNER JOIN #Departments d ON d.Department = eth.Department
	 INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	 INNER JOIN Employees e ON e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	 LEFT OUTER JOIN ClientEmployees ce ON ce.CompanyID = eth.CompanyID AND ce.EmployeeID = e.EmployeeID
	 LEFT OUTER JOIN EmployeePaycodes ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode
WHERE eth.CompanyID = @companyInternal AND ce.ClientID = @clientInternal AND eth.PayrollRecordType = 1 and eth.PayRate <> 0
GROUP BY e.EmployeeID, eth.Department, eth.Position, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, eth.PayRate, ep.PayType, ep.ReportAsWages

INSERT INTO #Output(EmployeeID, Department, Position, CheckNumber, CheckDate, PayrollCode, Units, PayRate, Amount, PayCatagory, GrossWagesFlag)
SELECT e.EmployeeID, eth.Department, eth.Position, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, SUM(eth.UnitsToPay) Units, CASE SUM(eth.UnitsToPay) WHEN 0 THEN 0 ELSE SUM(eth.TRXAmount)/SUM(eth.UnitsToPay) END as Rate, SUM(eth.TRXAmount) Wages, 'Earnings' PayCatagory, GrossWagesFlag = CAST(CASE WHEN ep.PayType = 5 THEN ep.ReportAsWages ELSE 1 END AS BIT)
FROM EmployeeTransactionHistory eth
	 INNER JOIN #AuditControlCodes a ON eth.AuditControlCode = a.AuditControlCode
	 INNER JOIN #Departments d ON d.Department = eth.Department
	 INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	 INNER JOIN Employees e ON e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	 LEFT OUTER JOIN ClientEmployees ce ON ce.CompanyID = eth.CompanyID AND ce.EmployeeID = e.EmployeeID
	 LEFT OUTER JOIN EmployeePaycodes ep ON ep.CompanyID = eth.CompanyID AND ep.EmployeeID = eth.EmployeeID AND ep.PayRecord = eth.PayrollCode
WHERE eth.CompanyID = @companyInternal AND ce.ClientID = @clientInternal AND eth.PayrollRecordType = 1 and eth.PayRate = 0
GROUP BY e.EmployeeID, eth.Department, eth.Position, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, ep.PayType, ep.ReportAsWages


INSERT INTO #Output(EmployeeID, Department, Position, CheckNumber, CheckDate, PayrollCode, Amount, PayCatagory)
SELECT e.EmployeeID, eth.Department, eth.Position, eth.CheckNumber, eth.CheckDate, PayrollCode, SUM(eth.TRXAmount) Wages,
	CASE WHEN PayrollRecordType = 2 THEN 'Deductions'
		 WHEN PayrollRecordType = 3 THEN 'Benefits'
		 WHEN PayrollRecordType = 4 THEN 'Taxes'
		 WHEN PayrollRecordType = 5 THEN 'Taxes'
	END PayCatagory
FROM EmployeeTransactionHistory eth 
	INNER JOIN #AuditControlCodes a ON eth.AuditControlCode = a.AuditControlCode
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	INNER JOIN Employees e on e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
	LEFT OUTER JOIN ClientEmployees ce on eth.CompanyID = ce.CompanyID and e.EmployeeID = ce.EmployeeID
WHERE eth.CompanyID = @companyInternal and ce.ClientID = @clientInternal AND eth.PayrollRecordType <> 1
GROUP BY e.EmployeeID, eth.Department, eth.Position, eth.CheckNumber, eth.CheckDate, eth.PayrollCode, eth.PayrollRecordType


CREATE TABLE #TransactionTotals(EmployeeID NVARCHAR(15), Department NVARCHAR(6), HomeDepartment NVARCHAR(6), Position NVARCHAR(50), HomePosition NVARCHAR(50),
	CheckNumber NVARCHAR(50), CheckDate DATETIME, PaymentAdjustmentNumber INT, TRXAmount DECIMAL(19,5), TRXTotal DECIMAL(19,5), GrossWagePct DECIMAL(19,5))
INSERT INTO #TransactionTotals
SELECT e.EmployeeID, eth.Department, e.Department HomeDepartment, eth.Position, e.Position HomePosition, eth.CheckNumber, eth.CheckDate, eth.PaymentAdjustmentNumber,
	TRXAmount = SUM(eth.TRXAmount),
	TRXTotal = SUM(SUM(eth.TRXAmount)) OVER (PARTITION BY eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID),
	GrossWagePct = (CASE SUM(TRXAmount) WHEN 0 THEN 0 ELSE SUM(TRXAmount) / SUM(SUM(eth.TRXAmount)) OVER (PARTITION BY eth.CheckNumber, eth.PaymentAdjustmentNumber, e.EmployeeID) END)
FROM EmployeeTransactionHistory eth
	INNER JOIN #AuditControlCodes a ON a.AuditControlCode = eth.AuditControlCode
	INNER JOIN #Departments d ON d.Department = eth.Department
	INNER JOIN #EmployeeIDs e1 ON e1.EmployeeID = eth.EmployeeID
	INNER JOIN Employees e ON e.EmployeeID = eth.EmployeeID and e.CompanyID = eth.CompanyID
WHERE  eth.CompanyID = @companyInternal AND PayrollRecordType = 1
GROUP BY eth.CompanyID, e.EmployeeID, eth.CheckNumber, eth.CheckDate, eth.PaymentAdjustmentNumber, eth.Department, e.Department, eth.Position, e.Position


-- FICA SS Witholding
INSERT INTO #Output(EmployeeID, Department, Position, CheckNumber, CheckDate, PayrollCode, PayCatagory, Amount)
SELECT EmployeeID, Department, Position, CheckNumber, CheckDate, AllocationType, 'Taxes' PayCatagory,
	CASE WHEN (HomeDepartment = Department OR HomePosition = Position) THEN Allocated + AllocatedDifference ELSE Allocated END Wages
FROM (
	SELECT a.EmployeeID, a.Department, a.HomeDepartment, a.Position, a.HomePosition, a.CheckNumber, a.CheckDate, Allocated,
		   (SUM(Allocated) OVER (PARTITION BY CheckNumber, EmployeeID) - Total) AllocatedDifference, 0 Adjusted, 'FICA SS' AllocationType
	FROM (
		SELECT en.EmployeeID, en.Department, en.HomeDepartment, en.Position, en.HomePosition, en.CheckNumber, en.CheckDate,
				(SELECT SUM(ech.FICASS) FROM #EmployeeCheckHistory ech WHERE ech.EmployeeID = en.EmployeeID AND ech.CheckNumber = en.CheckNumber) Total,
				(SELECT SUM(ech.FICASS) * (CASE en.TRXTotal WHEN 0 THEN 0 ELSE en.TRXAmount / en.TRXTotal END) FROM #EmployeeCheckHistory ech WHERE ech.EmployeeID = en.EmployeeID AND ech.CheckNumber = en.CheckNumber) Allocated
		  FROM #TransactionTotals AS en
	) AS a
) b

------ FICA Med Witholding
INSERT INTO #Output(EmployeeID, Department, Position, CheckNumber, CheckDate, PayrollCode, PayCatagory, Amount)
SELECT EmployeeID, Department, Position, CheckNumber, CheckDate, AllocationType, 'Taxes' PayCatagory,
	CASE WHEN (HomeDepartment = Department OR HomePosition = Position) THEN Allocated + AllocatedDifference ELSE Allocated END Wages
FROM (
	SELECT EmployeeID, Department, HomeDepartment, Position, HomePosition, CheckNumber, CheckDate, Allocated,
		   (SUM(Allocated) OVER (PARTITION BY CheckNumber, EmployeeID) - Total) AllocatedDifference, 0 Adjusted, 'FICA Med' AllocationType
	FROM (SELECT en.EmployeeID, en.Department, en.HomeDepartment, en.Position, en.HomePosition, en.CheckNumber, en.CheckDate,
				(SELECT SUM(ech.FICAM) FROM #EmployeeCheckHistory ech WHERE ech.EmployeeID = en.EmployeeID AND ech.CheckNumber = en.CheckNumber) Total,
				(SELECT SUM(ech.FICAM) * (CASE en.TRXTotal WHEN 0 THEN 0 ELSE en.TRXAmount / en.TRXTotal END) FROM #EmployeeCheckHistory ech WHERE ech.EmployeeID = en.EmployeeID AND ech.CheckNumber = en.CheckNumber) Allocated
		  FROM #TransactionTotals AS en
	) AS a
) b


------ Federal Tax Witholding
INSERT INTO #Output(EmployeeID, Department, Position, CheckNumber, CheckDate, PayrollCode, PayCatagory, Amount)
SELECT EmployeeID, Department, Position, CheckNumber, CheckDate, AllocationType, 'Taxes' PayCatagory,
	CASE WHEN (HomeDepartment = Department OR HomePosition = Position) THEN Allocated + AllocatedDifference ELSE Allocated END Wages
FROM (
	SELECT EmployeeID, Department, HomeDepartment, Position, HomePosition, CheckNumber, CheckDate, Allocated,
		   (SUM(Allocated) OVER (PARTITION BY CheckNumber, EmployeeID) - Total) AllocatedDifference, 0 Adjusted, 'Federal' AllocationType
	FROM (SELECT en.EmployeeID, en.Department, en.HomeDepartment, en.Position, en.HomePosition, en.CheckNumber, en.CheckDate,
				(SELECT SUM(ech.Fed) FROM #EmployeeCheckHistory ech WHERE ech.EmployeeID = en.EmployeeID AND ech.CheckNumber = en.CheckNumber) Total,
				(SELECT SUM(ech.Fed) * (CASE en.TRXTotal WHEN 0 THEN 0 ELSE en.TRXAmount / en.TRXTotal END) FROM #EmployeeCheckHistory ech WHERE ech.EmployeeID = en.EmployeeID AND ech.CheckNumber = en.CheckNumber) Allocated
		  FROM #TransactionTotals AS en
	) AS a
) b

SELECT e.EmployeeID, e.EmployeeName, e.EmployeeSSN
	 , np.NetPay
	 , o.Department, o.Position, o.CheckNumber, o.CheckDate, o.PayCatagory, o.PayrollCode, o.Units, o.PayRate, o.GrossWagesFlag, o.Amount
	 , d.[Description] DepartmentName
	 , p.[Description] PositionName
FROM #Employees e
	 INNER JOIN #NetPay np ON np.EmployeeID = e.EmployeeID
	 INNER JOIN #Output o ON o.EmployeeID = e.EmployeeID AND o.CheckDate = np.CheckDate AND o.CheckNumber = np.CheckNumber
	 LEFT OUTER JOIN Departments d ON d.CompanyID = @companyInternal AND d.Department = o.Department
	 LEFT OUTER JOIN Positions p ON p.CompanyID = @companyInternal AND p.Position = o.Position


DROP TABLE #EmployeeCheckHistory
DROP TABLE #TransactionTotals
DROP TABLE #Departments
DROP TABLE #EmployeeIDs
DROP TABLE #AuditControlCodes
DROP TABLE #NetPay
DROP TABLE #Employees
DROP TABLE #Output

END
