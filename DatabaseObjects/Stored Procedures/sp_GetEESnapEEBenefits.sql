/*
********************************************************************
**** Created by <PERSON><PERSON> gohel.
**** Date: 10/05/2021.
**** Purpose: Returns all ee search snapshot benefits records for one ee and this also fetch employees->eelist when you click on ee - benefits grid data.
****
********************************************************************
*/


CREATE PROCEDURE [dbo].[sp_GetEESnapEEBenefits]  
(
	@company INT,
	@eeid VARCHAR(15) = NULL
)
AS                                             

select e.EmployeeID,ISNULL(e.Benefit,'') as Code, ISNULL(e.BenefitPercent1,0)/100.00 as [Percent],
ISNULL(e.BenefitAmount1,0) as Amount,e.Inactive,
(CASE  WHEN e.BenefitFrequency = 1 THEN 'Weekly'
WHEN e.BenefitFrequency = 2 THEN 'Biweekly'
WHEN e.BenefitFrequency = 3 THEN 'Semimonthly'
WHEN e.BenefitFrequency = 4 THEN 'Monthly'
WHEN e.BenefitFrequency = 5 THEN 'Quarterly'
WHEN e.BenefitFrequency = 6 THEN 'Semiannually'
WHEN e.BenefitFrequency = 7 THEN 'Annually'
WHEN e.BenefitFrequency = 8 THEN 'Daily/Miscellaneous'
ELSE ''
END) as FrequencyName,ISNULL(p.Description,'') as [Description],
cast(IsNull(h.MonthToDateWages1+h.MonthToDateWages2+h.MonthToDateWages3+h.MonthToDateWages4+h.MonthToDateWages5+h.MonthToDateWages6
+h.MonthToDateWages7+h.MonthToDateWages8+h.MonthToDateWages9+h.MonthToDateWages10+h.MonthToDateWages11+
h.MonthToDateWages12,0) as float) as YTD
from EmployeeBenefits e
left outer join Benefits p on p.CompanyID=e.CompanyID and p.Benefit = e.Benefit
left outer join EmployeeTransactionHistoryHDR h on h.CompanyID = e.CompanyID and h.EmployeeID = e.EmployeeID and h.PayrollRecordType = 3 and h.PayrollCode = e.Benefit and h.YEAR = YEAR(GETDATE())
where e.CompanyID=@company and e.EmployeeID = @eeid




                      