<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>DatabaseObjects</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{950162a2-2bc5-4ec9-9b54-6da83dd51e04}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql130DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath />
    <RootNamespace>DatabaseObjects</RootNamespace>
    <AssemblyName>DatabaseObjects</AssemblyName>
    <ModelCollation>1033,CI</ModelCollation>
    <DefaultFileStructure>BySchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
    <DefaultCollation>SQL_Latin1_General_CP1_CI_AS</DefaultCollation>
    <DefaultFilegroup>PRIMARY</DefaultFilegroup>
    <Trustworthy>False</Trustworthy>
    <ServiceBrokerOption>DisableBroker</ServiceBrokerOption>
    <GenerateCreateScript>True</GenerateCreateScript>
    <DacVersion>1.0.0</DacVersion>
    <DacApplicationName>CohesionDatabase</DacApplicationName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <SqlTargetName>CohesionDatabase</SqlTargetName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="Tables\" />
    <Folder Include="Views\" />
    <Folder Include="Functions\" />
    <Folder Include="Stored Procedures\" />
    <Folder Include="Data" />
    <Folder Include="Data\Default" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="Tables\InvoicePositionDepartmentEmployeeDetails.sql" />
    <Build Include="Tables\UserRoleClientAccess.sql" />
    <Build Include="Tables\SQLNotificationLog.sql" />
    <Build Include="Tables\PayrollProfileVariances.sql" />
    <Build Include="Tables\PayrollProfileDivisions.sql" />
    <Build Include="Tables\RecaptureHistory.sql" />
    <Build Include="Tables\InvoicePayrollDetailsOriginals.sql" />
    <Build Include="Tables\EmployeeStateTaxes.sql" />
    <Build Include="Tables\OBEmployeePTOTypes.sql" />
    <Build Include="Tables\CertifiedJobs.sql" />
    <Build Include="Tables\EmployeeW2Forms.sql" />
    <Build Include="Tables\TimeSheetImport.sql" />
    <Build Include="Tables\Invoices.sql" />
    <Build Include="Tables\InvoicePreviewDetails.sql" />
    <Build Include="Tables\ProjectSetup.sql" />
    <Build Include="Tables\ClientTaxSetup.sql" />
    <Build Include="Tables\EmailLog.sql" />
    <Build Include="Tables\CertifiedJobExceptions.sql" />
    <Build Include="Tables\ClientVendors.sql" />
    <Build Include="Tables\EmployeePTOTiersAnnualReset.sql" />
    <Build Include="Tables\EmployeeEnrollmentPlanDocuments.sql" />
    <Build Include="Tables\InvoicePreviewStatuses.sql" />
    <Build Include="Tables\InvoiceCertifiedPayrollHeaders.sql" />
    <Build Include="Tables\InvoiceASOClientPostHistory.sql" />
    <Build Include="Tables\ClientImages.sql" />
    <Build Include="Tables\PayrollApprovalSetupSequences.sql" />
    <Build Include="Tables\AttachmentDocTypes.sql" />
    <Build Include="Tables\OBClientSetupTaskFields.sql" />
    <Build Include="Tables\CheckPrinterData.sql" />
    <Build Include="Tables\InvoiceReports.sql" />
    <Build Include="Tables\ActivityLog.sql" />
    <Build Include="Tables\InvoiceTaxAccounts.sql" />
    <Build Include="Tables\InvoiceASOAccountSetupHistory.sql" />
    <Build Include="Tables\TimeSheetColumns.sql" />
    <Build Include="Tables\InvoiceStatuses.sql" />
    <Build Include="Tables\ClientACAPlanLevels.sql" />
    <Build Include="Tables\ClientTimeSheetProfileSelectedValues.sql" />
    <Build Include="Tables\InvoiceTypeSetup.sql" />
    <Build Include="Tables\InvoiceEmployeeBillingDetailsOriginals.sql" />
    <Build Include="Tables\AdditionalFutaAmounts.sql" />
    <Build Include="Tables\GeneralLedgerAccounts.sql" />
    <Build Include="Tables\EmployeeBenefits.sql" />
    <Build Include="Tables\EmployeeDirectDepositHistory.sql" />
    <Build Include="Tables\ClientACAShareLevels.sql" />
    <Build Include="Tables\PayrollApprovals.sql" />
    <Build Include="Tables\ClientTimeSheetProfileSelectedJobs.sql" />
    <Build Include="Tables\InvoiceWCEmployeeDetails.sql" />
    <Build Include="Tables\TallyCalendar.sql" />
    <Build Include="Tables\AdminUsers.sql" />
    <Build Include="Tables\EmployeeW2FormCodes.sql" />
    <Build Include="Tables\OBProcessTasks.sql" />
    <Build Include="Tables\InvoiceAgencyChecksOriginals.sql" />
    <Build Include="Tables\ClientAssignments.sql" />
    <Build Include="Tables\OnBoardingFinalizeSections.sql" />
    <Build Include="Tables\ClientTimePunchesSetupCodes.sql" />
    <Build Include="Tables\OBProfileTasks.sql" />
    <Build Include="Tables\IzendaAdHocReports.sql" />
    <Build Include="Tables\CertifiedJobCostingExceptions.sql" />
    <Build Include="Tables\OBEmployeePaycodes.sql" />
    <Build Include="Tables\LocalTaxDetails.sql" />
    <Build Include="Tables\ClientChargeWCCodes.sql" />
    <Build Include="Tables\OnBoardingDocuments.sql" />
    <Build Include="Tables\ClientTimePunchesSetup.sql" />
    <Build Include="Tables\OBProcessTaskFields.sql" />
    <Build Include="Tables\CertifiedJobCostingTaskClassifications.sql" />
    <Build Include="Tables\ClientASOAccountSetup.sql" />
    <Build Include="Tables\PayrollApprovalEmailTokens.sql" />
    <Build Include="Tables\OBProcessRecordAttachments.sql" />
    <Build Include="Tables\PayrollWorkMissedCodes.sql" />
    <Build Include="Tables\TimeSheetSelectedValues.sql" />
    <Build Include="Tables\LocalTaxTableSetups.sql" />
    <Build Include="Tables\ClientACACodeSelection.sql" />
    <Build Include="Tables\EmployeeAssignedPlans.sql" />
    <Build Include="Tables\CertifiedJobCosting.sql" />
    <Build Include="Tables\TimeSheetSelectedJobs.sql" />
    <Build Include="Tables\LogInAsHistory.sql" />
    <Build Include="Tables\OBRequests.sql" />
    <Build Include="Tables\InvoiceEmployeeChargeDetailsOriginals.sql" />
    <Build Include="Tables\ClientContactTypes.sql" />
    <Build Include="Tables\OBDocuments.sql" />
    <Build Include="Tables\OBProfileTaskFields.sql" />
    <Build Include="Tables\EmployeePayrateHistory.sql" />
    <Build Include="Tables\ChecksChargeSetup.sql" />
    <Build Include="Tables\MinimumWageSetups.sql" />
    <Build Include="Tables\OBProcessDocumentUDF.sql" />
    <Build Include="Tables\EmployeeTimePunchRequests.sql" />
    <Build Include="Tables\ClientInsurancePlanDetails.sql" />
    <Build Include="Tables\EmployeeAddresses.sql" />
    <Build Include="Tables\OBProfiles.sql" />
    <Build Include="Tables\ClientBillingGroups.sql" />
    <Build Include="Tables\PayrollProcessOptions.sql" />
    <Build Include="Tables\MobileCodes.sql" />
    <Build Include="Tables\EmployeeCustomInfo.sql" />
    <Build Include="Tables\TimeSheets.sql" />
    <Build Include="Tables\InvoiceEmployeeDetailsOriginals.sql" />
    <Build Include="Tables\EmployeeTimePunchLog.sql" />
    <Build Include="Tables\WorkersCompCodes.sql" />
    <Build Include="Tables\ClientInvoiceCertifiedPayrollDetails.sql" />
    <Build Include="Tables\PayrollWorkHeadersDivisional.sql" />
    <Build Include="Tables\DarwiNetCodes.sql" />
    <Build Include="Tables\InvoiceChargeDetails.sql" />
    <Build Include="Tables\ClientContributionPlanLevels.sql" />
    <Build Include="Tables\PayrollBenefitsDeductions.sql" />
    <Build Include="Tables\EmployeeMissedCodes.sql" />
    <Build Include="Tables\EmployeeFMLAMaster.sql" />
    <Build Include="Tables\EmployeeTimePunchImport.sql" />
    <Build Include="Tables\SUTAStates.sql" />
    <Build Include="Tables\ClientManagers.sql" />
    <Build Include="Tables\ClientPlanHDR.sql" />
    <Build Include="Tables\InvoiceCharges.sql" />
    <Build Include="Tables\LookupPayrollChargeCodes.sql" />
    <Build Include="Tables\ClientContributionSelect.sql" />
    <Build Include="Tables\PayrollAutoPayEmployees.sql" />
    <Build Include="Tables\EmployeeBeneficiaries.sql" />
    <Build Include="Tables\PayrollProfileSettings.sql" />
    <Build Include="Tables\OBFinalizeLog.sql" />
    <Build Include="Tables\GetHired_Import.sql" />
    <Build Include="Tables\ClientPosting.sql" />
    <Build Include="Tables\EditCalcLocks.sql" />
    <Build Include="Tables\ClientCompositeBillingSetup.sql" />
    <Build Include="Tables\ClientExcludedDeductions.sql" />
    <Build Include="Tables\EmployeeTimePunchesSetup.sql" />
    <Build Include="Tables\InvoiceAgencyCreditHistory.sql" />
    <Build Include="Tables\OBEmployeeTrainingComments.sql" />
    <Build Include="Tables\ClientTimeImportTranslation.sql" />
    <Build Include="Tables\ClientTimeSheetProfileCodes.sql" />
    <Build Include="Tables\OBUserChallengeSecurInfo.sql" />
    <Build Include="Tables\ClientReviewActions.sql" />
    <Build Include="Tables\CompanyImages.sql" />
    <Build Include="Tables\PayrollResetHistory.sql" />
    <Build Include="Tables\OBProfileNotifications.sql" />
    <Build Include="Tables\ClientFutaSutaEffectiveRates.sql" />
    <Build Include="Tables\EmployeeEnrollmentPlanTypes.sql" />
    <Build Include="Tables\OBEmployeeTraining.sql" />
    <Build Include="Tables\ClientWorkersCompPlans.sql" />
    <Build Include="Tables\PayrollWorkBlend.sql" />
    <Build Include="Tables\ClientTimeImportSetup.sql" />
    <Build Include="Tables\OldEEIDTable.sql" />
    <Build Include="Tables\Client401kPlanCodes.sql" />
    <Build Include="Tables\InvoiceEmployeeMinimumBillingHistoryOriginals.sql" />
    <Build Include="Tables\ClientReviewRatings.sql" />
    <Build Include="Tables\OBClientSetupTasks.sql" />
    <Build Include="Tables\TimeImportModels.sql" />
    <Build Include="Tables\OnBoardingDocumentDefaultFields.sql" />
    <Build Include="Tables\ClientFutaSutaTSASettings.sql" />
    <Build Include="Tables\OBEmployeeStateTaxes.sql" />
    <Build Include="Tables\OBProfileNotificationAttachments.sql" />
    <Build Include="Tables\Users.sql" />
    <Build Include="Tables\EmployeeOBDocuments.sql" />
    <Build Include="Tables\ClientReviewTypes.sql" />
    <None Include="Tables\RapidPaySetup.sql" />
    <Build Include="Tables\PayrollWorkMissedCodesOriginal.sql" />
    <Build Include="Tables\ClientReports.sql" />
    <Build Include="Tables\OnBoardingFields.sql" />
    <Build Include="Tables\InvoicePayrollsOriginals.sql" />
    <Build Include="Tables\ClientLaborDistributionValues.sql" />
    <Build Include="Tables\OBProfileImportTranslation.sql" />
    <Build Include="Tables\EmployeeTimePunches.sql" />
    <Build Include="Tables\ClientSicktimeTiers.sql" />
    <None Include="Tables\RapidPayExport.sql" />
    <Build Include="Tables\EmployeeMultiStates.sql" />
    <Build Include="Tables\ClientReportAssignment.sql" />
    <Build Include="Tables\PayrollWorkStateTaxes.sql" />
    <Build Include="Tables\OnBoardingStateW4Rules.sql" />
    <Build Include="Tables\ClientLookup.sql" />
    <Build Include="Tables\CompanyDocumentAssignedUsers.sql" />
    <Build Include="Tables\OBDocumentMapping.sql" />
    <Build Include="Tables\OBProfileFinalize.sql" />
    <Build Include="Tables\CovidReliefEmployeeCredits.sql" />
    <Build Include="Tables\TimeImportModelDetails.sql" />
    <Build Include="Tables\UserReportSecurity.sql" />
    <Build Include="Tables\ClientTrainingRatings.sql" />
    <Build Include="Tables\TaxScheduleMaster.sql" />
    <Build Include="Tables\PayrollWorkPTO.sql" />
    <Build Include="Tables\OpenEnrollmentDataFields.sql" />
    <Build Include="Tables\ClientMultipleChargesDeductions.sql" />
    <Build Include="Tables\BenefitWaiverReasons.sql" />
    <Build Include="Tables\OBClientSetupNotifications.sql" />
    <Build Include="Tables\ClientPunchIPSecurity.sql" />
    <Build Include="Tables\OpenEnrollmentSetup.sql" />
    <Build Include="Tables\OBEmployeeOBDocuments.sql" />
    <Build Include="Tables\CompanyDocumentAssignments.sql" />
    <Build Include="Tables\ClientTrainingStatuses.sql" />
    <Build Include="Tables\TaxDetailMaster.sql" />
    <Build Include="Tables\ReportOptions.sql" />
    <Build Include="Tables\OBProfileCodeDetails.sql" />
    <Build Include="Tables\OpenEnrollmentDocumentDefaultFields.sql" />
    <Build Include="Tables\SSRSReports.sql" />
    <Build Include="Tables\ClientMultipleChargesDepartments.sql" />
    <Build Include="Tables\OBDocumentAssignments.sql" />
    <Build Include="Tables\ClientPayrollSchedule_TimesheetProfiles.sql" />
    <Build Include="Tables\PayrollWorkLocalTaxes.sql" />
    <Build Include="Tables\OpenEnrollmentNotifications.sql" />
    <Build Include="Tables\InvoiceEmployeeDetails.sql" />
    <Build Include="Tables\UserDeptDivSecurity.sql" />
    <Build Include="Tables\ClientVacationTiers.sql" />
    <Build Include="Tables\PayrollWorkPosts.sql" />
    <Build Include="Tables\PayrollAccountCaches.sql" />
    <Build Include="Tables\ClientMultipleChargesPaycodes.sql" />
    <Build Include="Tables\OBClientSetupNotificationAttachments.sql" />
    <Build Include="Tables\ClientOpenEnrollmentSetup.sql" />
    <Build Include="Tables\DarwinPayrollCheckHistory.sql" />
    <Build Include="Tables\InvoiceClientMinimumBillingHistory.sql" />
    <Build Include="Tables\EmployeeDependents.sql" />
    <Build Include="Tables\TimeSheetProfiles.sql" />
    <Build Include="Tables\OpenEnrollmentInstructions.sql" />
    <Build Include="Tables\PayrollAccountSettings.sql" />
    <Build Include="Tables\ClientTimeSheetProfileColumns.sql" />
    <Build Include="Tables\OBImportEmployees.sql" />
    <Build Include="Tables\ClientMultipleChargesPositions.sql" />
    <Build Include="Tables\EmployeeTimeEntryDetails.sql" />
    <Build Include="Tables\EmployeeReviews.sql" />
    <Build Include="Tables\OBEmployeeLocalTaxes.sql" />
    <Build Include="Tables\OBClientSetupImportTranslation.sql" />
    <Build Include="Tables\PayrollWorkBenefitsOriginal.sql" />
    <Build Include="Tables\ClientDivisions.sql" />
    <Build Include="Tables\OBEmployees.sql" />
    <Build Include="Tables\ClientNotifications.sql" />
    <Build Include="Tables\PayrollBatchOfferedHours.sql" />
    <Build Include="Tables\ClientMultipleCharges.sql" />
    <Build Include="Tables\EmployeeTimeEntryCodeHours.sql" />
    <Build Include="Tables\OBClientSetupFinalize.sql" />
    <Build Include="Tables\PayrollWorkBlendOriginal.sql" />
    <Build Include="Tables\PayrollWorkFutaSutaWC.sql" />
    <Build Include="Tables\OpenEnrollmentDocumentSetup.sql" />
    <Build Include="Tables\ClientTimeClockImportSetup.sql" />
    <Build Include="Tables\InvoiceAgencyCreditMissedCodesHistory.sql" />
    <Build Include="Tables\ProcessGroupMembers.sql" />
    <Build Include="Tables\PayrollNotificationGroupMembers.sql" />
    <Build Include="Tables\ClientPlanDisabilityCodes.sql" />
    <Build Include="Tables\CompanyDocumentAssignmentsData.sql" />
    <Build Include="Tables\EmployeeTimeEntry.sql" />
    <Build Include="Tables\EditCalcDetail.sql" />
    <Build Include="Tables\PayrollWorkDeductionsOriginal.sql" />
    <Build Include="Tables\ClientImportCodeMappings.sql" />
    <Build Include="Tables\ProcessGroups.sql" />
    <Build Include="Tables\PayrollWorkDeductions.sql" />
    <Build Include="Tables\OpenEnrollmentDocuments.sql" />
    <Build Include="Tables\PayrollSetupGTL.sql" />
    <Build Include="Tables\InsurancePlans.sql" />
    <Build Include="Tables\ClientPlanEligibilityCodes.sql" />
    <Build Include="Tables\Departments.sql" />
    <Build Include="Tables\PayrollSnapshots.sql" />
    <Build Include="Tables\OBClientSetupDocuments.sql" />
    <Build Include="Tables\PayrollWorkErrors.sql" />
    <Build Include="Tables\ClientHolidays.sql" />
    <Build Include="Tables\OBUsers.sql" />
    <Build Include="Tables\InvoiceAgencyChecksFedTaxOriginals.sql" />
    <Build Include="Tables\CompanyDocumentDefaultFields.sql" />
    <Build Include="Tables\TimeSheetApproval.sql" />
    <Build Include="Tables\EmployeePTOTypes.sql" />
    <Build Include="Tables\OBProfileTaskDocumentMapping.sql" />
    <Build Include="Tables\Checkbooks.sql" />
    <Build Include="Tables\ClientPlanRateGrid.sql" />
    <Build Include="Tables\OBEmployeeLicensesAndCertifications.sql" />
    <Build Include="Tables\PayrollWorkFutaSutaWCOriginal.sql" />
    <Build Include="Tables\ClientBusinessExpenses.sql" />
    <Build Include="Tables\PayrollTeamMembers.sql" />
    <Build Include="Tables\CompanyDocumentLog.sql" />
    <Build Include="Tables\PayrollBatchDetails.sql" />
    <Build Include="Tables\DarwinetSetup.sql" />
    <Build Include="Tables\ClientPlanSpecs.sql" />
    <Build Include="Tables\Employee1095Info.sql" />
    <Build Include="Tables\OBProfileTaskDocumentAssignments.sql" />
    <Build Include="Tables\EmployeeDependentAssignedPlans.sql" />
    <Build Include="Tables\PayrollWorkHeadersDivisionalOriginal.sql" />
    <Build Include="Tables\CompanyDocumentMapping.sql" />
    <Build Include="Tables\EmployeeDailyTime.sql" />
    <Build Include="Tables\OBEmployeeEligibilityVerification.sql" />
    <Build Include="Tables\EmployeeTransactionSplitDetails.sql" />
    <Build Include="Tables\CheckPrinterProfiles.sql" />
    <Build Include="Tables\OpenEnrollmentDocumentMapping.sql" />
    <Build Include="Tables\OBProcessAuditLog.sql" />
    <Build Include="Tables\ClientPlanStateModifier.sql" />
    <Build Include="Tables\PayrollProfileControls.sql" />
    <Build Include="Tables\Employee125Balances.sql" />
    <Build Include="Tables\OBClientSetupDocumentMapping.sql" />
    <Build Include="Tables\PayrollWorkHeadersOriginal.sql" />
    <Build Include="Tables\EmployeeTimeEntryColumnSetup.sql" />
    <Build Include="Tables\InvoiceEmployeeChargeDetails.sql" />
    <Build Include="Tables\CheckPrinterPreferences.sql" />
    <Build Include="Tables\InvoiceApprovalSetups.sql" />
    <Build Include="Tables\ACA_Setup.sql" />
    <Build Include="Tables\PayrollWorkBenefits.sql" />
    <Build Include="Tables\InvoiceAgencyCreditHistoryOriginals.sql" />
    <Build Include="Tables\ClientRecipientGroups.sql" />
    <Build Include="Tables\OBEmployeeDirectDeposits.sql" />
    <Build Include="Tables\Employee125Payments.sql" />
    <Build Include="Tables\PayrollWorkLocalTaxesOriginal.sql" />
    <Build Include="Tables\Companies.sql" />
    <Build Include="Tables\EmployeePayrateChanges.sql" />
    <Build Include="Tables\OBClientSetupCustomCodes.sql" />
    <Build Include="Tables\ClientTimeSheetCodes1.sql" />
    <Build Include="Tables\EmployeeBenefitBasedOnCodes.sql" />
    <Build Include="Tables\PayrollWorkMasterDetails.sql" />
    <Build Include="Tables\TimeSheetNotes.sql" />
    <Build Include="Tables\CompanyDocuments.sql" />
    <Build Include="Tables\SmartTags.sql" />
    <Build Include="Tables\PayrollFlatTaxSettings.sql" />
    <Build Include="Tables\OBClientSetup.sql" />
    <Build Include="Tables\UnemploymentSetup.sql" />
    <Build Include="Tables\ClientTransactionSplitDetails.sql" />
    <Build Include="Tables\OBImportLog.sql" />
    <Build Include="Tables\OBEmployeeDependents.sql" />
    <Build Include="Tables\EmployeeCertifiedTaskClassifications.sql" />
    <Build Include="Tables\InvoiceChargeExpansionDetail.sql" />
    <Build Include="Tables\PayrollWorkPTOOriginal.sql" />
    <Build Include="Tables\CalendarCustomEvents.sql" />
    <Build Include="Tables\OpenEnrollmentClientNotifications.sql" />
    <Build Include="Tables\TimeSheetAttachments.sql" />
    <Build Include="Tables\InvoicesOriginals.sql" />
    <Build Include="Tables\InvoiceDirectDeposits.sql" />
    <Build Include="Tables\PlanContEligVerification.sql" />
    <Build Include="Tables\EmployeeJobCostAssignments.sql" />
    <Build Include="Tables\ShippingMethods.sql" />
    <Build Include="Tables\ClientTransactionSplits.sql" />
    <Build Include="Tables\Paycodes.sql" />
    <Build Include="Tables\EmployeeClassDetails.sql" />
    <Build Include="Tables\OBProfileTaskDocuments.sql" />
    <Build Include="Tables\Clients.sql" />
    <Build Include="Tables\EmployeeSuccessorTaxes.sql" />
    <Build Include="Tables\PayrollWorkPayCodesOriginal.sql" />
    <Build Include="Tables\BusinessExpenseVendors.sql" />
    <Build Include="Tables\ClientTimeSheetProfiles.sql" />
    <Build Include="Tables\TimeSheetApprovalSetup.sql" />
    <Build Include="Tables\EmployeeTrainingComments.sql" />
    <Build Include="Tables\InvoiceNotificationGroups.sql" />
    <Build Include="Tables\QuickStats.sql" />
    <Build Include="Tables\ClientUDFDefinitions.sql" />
    <Build Include="Tables\EmployeeDeductionBasedOnCodes.sql" />
    <Build Include="Tables\PayrollWorkPostsOriginal.sql" />
    <Build Include="Tables\OpenEnrollmentClientInstructions.sql" />
    <Build Include="Tables\EmployeeTraining.sql" />
    <Build Include="Tables\TableSnapshots.sql" />
    <Build Include="Tables\PayrollNotificationGroups.sql" />
    <Build Include="Tables\BusinessExpenses.sql" />
    <Build Include="Tables\PayrollWorkHeaders.sql" />
    <Build Include="Tables\ReportTables.sql" />
    <Build Include="Tables\ClientWorkersCompPlanDetails.sql" />
    <Build Include="Tables\MenuItems.sql" />
    <Build Include="Tables\EmployeeHIREActCredits.sql" />
    <Build Include="Tables\PayrollWorkStateTaxesOriginal.sql" />
    <Build Include="Tables\InvoiceNotificationGroupMembers.sql" />
    <Build Include="Tables\PostingSettings.sql" />
    <Build Include="Tables\ClientCharges.sql" />
    <Build Include="Tables\ClientJBSCodes.sql" />
    <Build Include="Tables\ChargeSetup.sql" />
    <Build Include="Tables\ClientTrainingTypes.sql" />
    <Build Include="Tables\SSNDisplayTypes.sql" />
    <Build Include="Tables\ClientWorkersCompPlanExperienceModifiers.sql" />
    <Build Include="Tables\EmployeeSicktimeTiers.sql" />
    <Build Include="Tables\CertifiedJobCostingTasks.sql" />
    <Build Include="Tables\PayrollProfileSettingDetails.sql" />
    <Build Include="Tables\PlanTypes.sql" />
    <Build Include="Tables\EmployeeTransactionHistoryHDR.sql" />
    <Build Include="Tables\NewsItems.sql" />
    <Build Include="Tables\SalesTaxSetups.sql" />
    <Build Include="Tables\OnBoardingNotifications.sql" />
    <Build Include="Tables\CompositeChargeFlatFees.sql" />
    <Build Include="Tables\EmployeeDocumentUDF.sql" />
    <Build Include="Tables\OBProcessRecords.sql" />
    <Build Include="Tables\EmployeeSuccessorSUTATaxes.sql" />
    <Build Include="Tables\ServiceLogs.sql" />
    <Build Include="Tables\ClientCalendar.sql" />
    <Build Include="Tables\ClientPlanRateGridCategories.sql" />
    <Build Include="Tables\InvoiceApprovalRecipients.sql" />
    <Build Include="Tables\PayrollCallbackUrls.sql" />
    <Build Include="Tables\UserRoleMenuAccess.sql" />
    <Build Include="Tables\OBEmployeeDeductions.sql" />
    <Build Include="Tables\InvoiceAgencyChecksFedTax.sql" />
    <Build Include="Tables\CovidReliefLimits.sql" />
    <Build Include="Tables\UserDocumentSecurity.sql" />
    <Build Include="Tables\InvoiceNotifications.sql" />
    <Build Include="Tables\PayrollTaxTableSetup.sql" />
    <Build Include="Tables\EmployeeSummary.sql" />
    <Build Include="Tables\BenefitTypes.sql" />
    <Build Include="Tables\Emails.sql" />
    <Build Include="Tables\PayrollProfileSecondCheckOptions.sql" />
    <Build Include="Tables\PayrollTeamTypes.sql" />
    <Build Include="Tables\Supervisors.sql" />
    <Build Include="Tables\DarwinTableMap.sql" />
    <Build Include="Tables\OBProcessRecordDetails.sql" />
    <Build Include="Tables\CompanyCalendars.sql" />
    <Build Include="Tables\InvoiceChargeComponentDetailsOriginals.sql" />
    <Build Include="Tables\EmployeeVacationTiers.sql" />
    <Build Include="Tables\ClientJobCostAssignments.sql" />
    <Build Include="Tables\PayrollWorkPayCodes.sql" />
    <Build Include="Tables\InvoiceASOAccountSetupHistoryOriginals.sql" />
    <Build Include="Tables\PayrollProfileReports.sql" />
    <Build Include="Tables\GroupTermLimits.sql" />
    <Build Include="Tables\UserPrinterSetup.sql" />
    <Build Include="Tables\UserRoleMenuAccessOverride.sql" />
    <Build Include="Tables\PayrollTeams.sql" />
    <Build Include="Tables\UserRoles.sql" />
    <Build Include="Tables\TestUsage.sql" />
    <Build Include="Tables\ClientACARptDetails.sql" />
    <Build Include="Tables\DeductionMaximums.sql" />
    <Build Include="Tables\OBImportDetails.sql" />
    <Build Include="Tables\InvoiceChargeDetailsOriginals.sql" />
    <Build Include="Tables\EmployeeDirectDeposits.sql" />
    <Build Include="Tables\InvoiceApprovalSetupSequences.sql" />
    <Build Include="Tables\401kPlanDetails.sql" />
    <Build Include="Tables\NewsAttachments.sql" />
    <Build Include="Tables\ThirdPartyServiceProvider.sql" />
    <Build Include="Tables\ClientUnemploymentSetup.sql" />
    <Build Include="Tables\DeleteTables.sql" />
    <Build Include="Tables\ClientCommissions.sql" />
    <Build Include="Tables\DarwinColumnMap.sql" />
    <Build Include="Tables\InvoicePayrolls.sql" />
    <Build Include="Tables\InvoiceTaxAccountsOriginals.sql" />
    <Build Include="Tables\EmployeePayrollNotes.sql" />
    <Build Include="Tables\LibraryDocument.sql" />
    <Build Include="Tables\TimeSheetColumnDefaults.sql" />
    <Build Include="Tables\OnBoardingTasks.sql" />
    <Build Include="Tables\EarningsCodeSetupDeductions.sql" />
    <Build Include="Tables\ClientCommissionTiers.sql" />
    <Build Include="Tables\OBAwaitingFinalize.sql" />
    <Build Include="Tables\OBProcessWarningNotifications.sql" />
    <Build Include="Tables\Banks.sql" />
    <Build Include="Tables\Library.sql" />
    <Build Include="Tables\TimeSheetConsolidatedEETime.sql" />
    <Build Include="Tables\NotificationGroups.sql" />
    <Build Include="Tables\EarningsCodeSetupPaycodes.sql" />
    <Build Include="Tables\InactiveReasons.sql" />
    <Build Include="Tables\CommissionScales.sql" />
    <Build Include="Tables\EmployeeCertifiedJobTasks.sql" />
    <Build Include="Tables\InvoiceApprovals.sql" />
    <Build Include="Tables\ClientContributionGroup.sql" />
    <Build Include="Tables\ClientACARptHdr.sql" />
    <Build Include="Tables\EmployeeAdditionalTransactionHistory.sql" />
    <Build Include="Tables\InvoiceChargeRateHistoryOriginals.sql" />
    <Build Include="Tables\EmployeeInsurancePlans.sql" />
    <Build Include="Tables\TimeSheetProfileDetails.sql" />
    <Build Include="Tables\PayrollProfileNotificationUsers.sql" />
    <Build Include="Tables\EarningsCodeSetup.sql" />
    <Build Include="Tables\InsurancePlanDetails.sql" />
    <Build Include="Tables\OBProcessMonitor.sql" />
    <Build Include="Tables\CommissionScaleTiers.sql" />
    <Build Include="Tables\TimeSheetProfileApproval.sql" />
    <Build Include="Tables\ExportSolutionsExports.sql" />
    <Build Include="Tables\UserRoleClientEmployeeAssignment.sql" />
    <Build Include="Tables\EditCalcHeader.sql" />
    <Build Include="Tables\ClientPTOTypes.sql" />
    <Build Include="Tables\Usage.sql" />
    <Build Include="Tables\PayrollProfileNotifications.sql" />
    <Build Include="Tables\EmployeeTransactionHistory.sql" />
    <Build Include="Tables\EmployeeBenefitChanges.sql" />
    <Build Include="Tables\CompanyDocumentDataFields.sql" />
    <Build Include="Tables\ELMAH_Error.sql" />
    <Build Include="Tables\ClientInsurancePlans.sql" />
    <Build Include="Tables\InsurancePlanTypes.sql" />
    <Build Include="Tables\Salespersons.sql" />
    <Build Include="Tables\401kPlans.sql" />
    <Build Include="Tables\InvoiceASOClientPostHistoryOriginals.sql" />
    <Build Include="Tables\ExportSolutionRpts.sql" />
    <Build Include="Tables\UserCustomCalendar.sql" />
    <Build Include="Tables\PayrollProfileNotes.sql" />
    <Build Include="Tables\EmployeeDeductionChanges.sql" />
    <Build Include="Tables\LicenseCertificationNotifications.sql" />
    <Build Include="Tables\InvoiceCertifiedPayrolls.sql" />
    <Build Include="Tables\ClientJobCostAssignmentDetails.sql" />
    <Build Include="Tables\UserToDoItems.sql" />
    <Build Include="Tables\InvoiceApprovalEmailTokens.sql" />
    <Build Include="Tables\EmployeePaycodes.sql" />
    <Build Include="Tables\ControlsAndVariances.sql" />
    <Build Include="Tables\PlanHDR.sql" />
    <Build Include="Tables\UserCustomLinks.sql" />
    <Build Include="Tables\InvoiceCertifiedPayrollsOriginals.sql" />
    <Build Include="Tables\EmployeeInsurancePlanBeneficiaries.sql" />
    <Build Include="Tables\InvoiceCompFlatFees.sql" />
    <Build Include="Tables\EmployeeEnrollmentStatus.sql" />
    <Build Include="Tables\PayrollProfileNoteAttachments.sql" />
    <Build Include="Tables\ControlRecords.sql" />
    <Build Include="Tables\SalespersonTiers.sql" />
    <Build Include="Tables\SalesSetup.sql" />
    <Build Include="Tables\UserRoleDashboard.sql" />
    <Build Include="Tables\WelcomePageSetup.sql" />
    <Build Include="Tables\EmployeeInsurancePlanDependents.sql" />
    <Build Include="Tables\InvoiceDepartmentDetails.sql" />
    <Build Include="Tables\InvoiceApprovalAttachments.sql" />
    <Build Include="Tables\DarwiNetClientNotificationFilterUsers.sql" />
    <Build Include="Tables\PayrollProfileEmployees.sql" />
    <Build Include="Tables\InvoiceEmployeePayrollDetails.sql" />
    <Build Include="Tables\InvoiceAgencyChecks.sql" />
    <Build Include="Tables\CommissionAmounts.sql" />
    <Build Include="Tables\UserRoleClientEmployeeAssignmentHistory.sql" />
    <Build Include="Tables\PayrollMissedCodes.sql" />
    <Build Include="Tables\PayrollWorkMasters.sql" />
    <Build Include="Tables\WorkForceReady.sql" />
    <Build Include="Tables\EmployeeMultipleChargesDeductions.sql" />
    <Build Include="Tables\VarianceRecords.sql" />
    <Build Include="Tables\InvoiceChargesOriginals.sql" />
    <Build Include="Tables\InvoiceEmployeeBillingDetails.sql" />
    <Build Include="Tables\EmployeeEnrollmentPlanRateGrids.sql" />
    <Build Include="Tables\NotificationTypes.sql" />
    <Build Include="Tables\PayrollProfileEmployeeCriterias.sql" />
    <Build Include="Tables\OBEmployeeCustomInfo.sql" />
    <Build Include="Tables\EmployeeInvoiceCheckHistory.sql" />
    <Build Include="Tables\WorkersCompPlanDetails.sql" />
    <Build Include="Tables\ClientCodeDescriptions.sql" />
    <Build Include="Tables\EmployeeMultipleChargesDepartments.sql" />
    <Build Include="Tables\InvoiceEmployeeMinimumBillingHistory.sql" />
    <Build Include="Tables\SalesTerritories.sql" />
    <Build Include="Tables\PayrollProcessPermissions.sql" />
    <Build Include="Tables\NotificationClientUsers.sql" />
    <Build Include="Tables\PayrollProfileEmployeeAutoPaySettings.sql" />
    <Build Include="Tables\ControlRecordHistory.sql" />
    <Build Include="Tables\OBEmployeeBenefits.sql" />
    <Build Include="Tables\ClientInvoiceDefaults.sql" />
    <Build Include="Tables\LocalTaxes.sql" />
    <Build Include="Tables\PayrollApprovalAttachments.sql" />
    <Build Include="Tables\WorkersCompPlanExperienceModifiers.sql" />
    <Build Include="Tables\EmployeeMultipleChargesPaycodes.sql" />
    <Build Include="Tables\TimeImports.sql" />
    <Build Include="Tables\LicenseTypes.sql" />
    <Build Include="Tables\ClientContributionPlan.sql" />
    <Build Include="Tables\NotificationQueue.sql" />
    <Build Include="Tables\VarianceRecordHistory.sql" />
    <Build Include="Tables\CheckPrinterCrystalFiles.sql" />
    <Build Include="Tables\PayrollSetup.sql" />
    <Build Include="Tables\voa_keygen.sql" />
    <Build Include="Tables\EmployeeMultipleChargesPositions.sql" />
    <Build Include="Tables\PayrollAudit.sql" />
    <Build Include="Tables\EmployeeEnrollmentPlanDependents.sql" />
    <Build Include="Tables\NotificationQueueDetails.sql" />
    <Build Include="Tables\TimeImportLog.sql" />
    <Build Include="Tables\EmployeeEnrollmentEligiblePlans.sql" />
    <Build Include="Tables\EmployeeMultipleChargesWorkersComps.sql" />
    <Build Include="Tables\SecurityQuestions.sql" />
    <Build Include="Tables\Client401kPlans.sql" />
    <Build Include="Tables\ClientTimeSheetProfileApproval.sql" />
    <Build Include="Tables\Notifications.sql" />
    <Build Include="Tables\PayrollProfileCommunicationSettings.sql" />
    <Build Include="Tables\UserNotificationSettings.sql" />
    <Build Include="Tables\EmployeeLocalTaxes.sql" />
    <Build Include="Tables\EmployeeMultipleCharges.sql" />
    <Build Include="Tables\SecurityAnswers.sql" />
    <Build Include="Tables\Positions.sql" />
    <Build Include="Tables\PayrollProfileAutoPayEmployees.sql" />
    <Build Include="Tables\OBImports.sql" />
    <Build Include="Tables\NotificationQueueRecipients.sql" />
    <Build Include="Tables\EmployeePTOTiers.sql" />
    <Build Include="Tables\Regions.sql" />
    <Build Include="Tables\InvoiceClientMinimumBillingHistoryOriginals.sql" />
    <Build Include="Tables\Keys.sql" />
    <Build Include="Tables\EmployeeEnrollmentPlanBeneficiaries.sql" />
    <Build Include="Tables\ClientEmployeeCompositeCharges.sql" />
    <Build Include="Tables\PayrollBatches.sql" />
    <Build Include="Tables\EmployeePlanEligibility.sql" />
    <Build Include="Tables\SalesPeriods.sql" />
    <Build Include="Tables\NotificationSmartTags.sql" />
    <Build Include="Tables\Deductions.sql" />
    <Build Include="Tables\EmployeeEligibilityVerification.sql" />
    <Build Include="Tables\EmployeeTransactionSplitDepartments.sql" />
    <Build Include="Tables\NotificationClients.sql" />
    <Build Include="Tables\Languages.sql" />
    <Build Include="Tables\ClientDivisionPayrollCodes.sql" />
    <Build Include="Tables\ShiftCodes.sql" />
    <Build Include="Tables\EmployeeEnrollmentDocumentUDF.sql" />
    <Build Include="Tables\SalesOpenDocuments.sql" />
    <Build Include="Tables\DarwiNetNotificationFilterUsers.sql" />
    <Build Include="Tables\EmployeeTransactionSplitPositions.sql" />
    <Build Include="Tables\Translations.sql" />
    <Build Include="Tables\CheckPrinterSetup.sql" />
    <Build Include="Tables\EmployeeEligibilityActivity.sql" />
    <Build Include="Tables\Employee401kPlans.sql" />
    <Build Include="Tables\OnBoardingDocumentMapping.sql" />
    <Build Include="Tables\EmployeeClasses.sql" />
    <Build Include="Tables\MergedInvoices.sql" />
    <Build Include="Tables\EmployeeTransactionSplitWCs.sql" />
    <Build Include="Tables\Announcement.sql" />
    <Build Include="Tables\OBClientSetupAssignmentCodes.sql" />
    <Build Include="Tables\DarwiNetNotifications.sql" />
    <Build Include="Tables\NewsAcknowledgement.sql" />
    <Build Include="Tables\ErrorMessages.sql" />
    <Build Include="Tables\OBEmployeeAddresses.sql" />
    <Build Include="Tables\UserAnnouncement.sql" />
    <Build Include="Tables\OBProcessDocuments.sql" />
    <Build Include="Tables\PayrollEditHeader.sql" />
    <Build Include="Tables\DarwinetNotes.sql" />
    <Build Include="Tables\NotificationGroupMembers.sql" />
    <Build Include="Tables\FutaSutaTSASettings.sql" />
    <Build Include="Tables\EmployeeBeneficiariesAssignedPlans.sql" />
    <Build Include="Tables\InvoiceCompFlatFeesOriginals.sql" />
    <Build Include="Tables\OBEmployee401kPlans.sql" />
    <Build Include="Tables\UserImport.sql" />
    <Build Include="Tables\ClientContacts.sql" />
    <Build Include="Tables\EmployeeLicensesAndCertifications.sql" />
    <Build Include="Tables\PayrollEditDetail.sql" />
    <Build Include="Tables\Employee401kPlanDetails.sql" />
    <Build Include="Tables\EmployeeFutaSutaWorkersCompHistory.sql" />
    <Build Include="Tables\GeneralLedgerAccountIndices.sql" />
    <Build Include="Tables\ClientDivisionDetails.sql" />
    <Build Include="Tables\UserImportUser.sql" />
    <Build Include="Tables\SalesHistoryDocuments.sql" />
    <Build Include="Tables\DarwiNetClientNotifications.sql" />
    <Build Include="Tables\Client401kPlanCatchUpCodes.sql" />
    <Build Include="Tables\InvoiceDepartmentDetailsOriginals.sql" />
    <Build Include="Tables\PayrollTaxTableSetups.sql" />
    <Build Include="Tables\EmployeeLOAMaster.sql" />
    <Build Include="Tables\ClientPayrollSchedules.sql" />
    <Build Include="Tables\GridState.sql" />
    <Build Include="Tables\NewsAssignments.sql" />
    <Build Include="Tables\OBClientSetupCodeDetails.sql" />
    <Build Include="Tables\ClientCheckNotesHistory.sql" />
    <Build Include="Tables\ChangeRequestsHDR.sql" />
    <Build Include="Tables\TimeSheetDetails.sql" />
    <Build Include="Tables\IPAddresses.sql" />
    <Build Include="Tables\PayrollTaxSetups.sql" />
    <Build Include="Tables\InvoiceChargeComponentDetails.sql" />
    <Build Include="Tables\OBProcessDocumentAssignments.sql" />
    <Build Include="Tables\TaxFilingAdditionals.sql" />
    <Build Include="Tables\PTORequests.sql" />
    <Build Include="Tables\ClientWorkSchedule.sql" />
    <Build Include="Tables\EmployeeCheckNotesHistory.sql" />
    <Build Include="Tables\EmployeeDeductions.sql" />
    <Build Include="Tables\OBEmployee401kPlanDetails.sql" />
    <Build Include="Tables\401kPlanTypes.sql" />
    <Build Include="Tables\PayrollTaxFilingStatuses.sql" />
    <Build Include="Tables\HRPerformanceProSetup.sql" />
    <Build Include="Tables\ClientTimePunchSecurity.sql" />
    <Build Include="Tables\InvoiceChargeRateHistory.sql" />
    <Build Include="Tables\EmployeePTOTakenEdits.sql" />
    <Build Include="Tables\UserTableFilters.sql" />
    <Build Include="Tables\EmployeeCheckHistory.sql" />
    <Build Include="Tables\ClientPlanDocuments.sql" />
    <Build Include="Tables\UserSupervisorSecurity.sql" />
    <Build Include="Tables\ACA_Report_Codes.sql" />
    <Build Include="Tables\PayrollApprovalSetups.sql" />
    <Build Include="Tables\HRPerformanceProAssignment.sql" />
    <Build Include="Tables\PostedInvoices.sql" />
    <Build Include="Tables\ChangeRequestDetails.sql" />
    <Build Include="Tables\InvoiceDepartmentEmployeeDetails.sql" />
    <Build Include="Tables\AgencyTaxes.sql" />
    <Build Include="Tables\ACHSetup.sql" />
    <Build Include="Tables\Employees.sql" />
    <Build Include="Tables\Activity.sql" />
    <Build Include="Tables\SalesDocuments.sql" />
    <Build Include="Tables\OBClientSetupDocumentAssignments.sql" />
    <Build Include="Tables\InvoiceDirectDepositsOriginals.sql" />
    <Build Include="Tables\ClientContributions.sql" />
    <Build Include="Tables\Attachments.sql" />
    <Build Include="Tables\EmployeeTimeEntryCodes.sql" />
    <Build Include="Tables\ClientPayrollChecks.sql" />
    <Build Include="Tables\Areas.sql" />
    <Build Include="Tables\ClientAddresses.sql" />
    <Build Include="Tables\PayrollApprovalRecipients.sql" />
    <Build Include="Tables\ClientEmployees.sql" />
    <Build Include="Tables\InvoicePositionDepartmentDetails.sql" />
    <Build Include="Tables\TaxFilingStatuses.sql" />
    <Build Include="Tables\InvoicePayrollDetails.sql" />
    <Build Include="Tables\EmployeeDependentPlanPriceAudit.sql" />
    <Build Include="Tables\Benefits.sql" />
    <Build Include="Tables\EmployeeBusinessExpenses.sql" />
    <Build Include="Tables\EmployeeChecks.sql" />
    <Build Include="Tables\OBEmployeeReviews.sql" />
    <Build Include="Tables\AreaDetails.sql" />
    <Build Include="Tables\CalcCheckSortOption.sql" />
    <Build Include="Tables\ClientSubClients.sql" />
    <Build Include="Views\ww_ExpSol_LD_Taxes_FutaSutaWC.sql" />
    <Build Include="Views\vw_Invoices.sql" />
    <Build Include="Views\vw_OBMonitorEmployees.sql" />
    <Build Include="Views\vw_EmployeeDependents.sql" />
    <Build Include="Views\vw_EmployeeFederalTaxesByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_CheckHistoryByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeePayRateChanges.sql" />
    <Build Include="Views\vw_EmployeeBusinessExpensesByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_ExpSol_GL_FICA.sql" />
    <Build Include="Views\vw_EmployeeDirectDeposit.sql" />
    <Build Include="Views\vw_InvoicesBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeBenefitsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_ExpSol_GL_Employees.sql" />
    <Build Include="Views\vw_TransactionHistoryBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeDeductionsBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeLicensesAndCertifications.sql" />
    <Build Include="Views\vw_EmployeeDeductionHistoryByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_Clients.sql" />
    <Build Include="Views\vw_TimeSheetImport.sql" />
    <Build Include="Views\vw_EmploymentTypeBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_ExpSol_GL_EE_Fed_FICA.sql" />
    <Build Include="Views\vw_EmployeeTrainingByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeFederalTaxes.sql" />
    <Build Include="Views\vw_Employees.sql" />
    <Build Include="Views\vw_InvoicesByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeLocalTaxes.sql" />
    <Build Include="Views\vw_ExpSol_GL_Wages.sql" />
    <Build Include="Views\vw_EmployeeBenefitsBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeTaxHistoryBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_Payroll_Payrolls.sql" />
    <Build Include="Views\vw_EmployeePTO.sql" />
    <Build Include="Views\vw_EmployeeLicensesAndCertificationsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeBusinessExpenses.sql" />
    <Build Include="Views\vw_OBProcessMonitor.sql" />
    <Build Include="Views\vw_RPT_Benefit_PlanAssignment.sql" />
    <Build Include="Views\vw_EmployeePaycodesByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_Billing_Profitablilty.sql" />
    <Build Include="Views\vw_EmployeeTraining.sql" />
    <Build Include="Views\vw_EmployeePayRateHistory.sql" />
    <Build Include="Views\vw_PayrollDetails_EmployeeTransactionHistory.sql" />
    <Build Include="Views\vw_EmployeeLocalTaxesBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeUserDefinedFields.sql" />
    <Build Include="Views\vw_Payroll_Hours.sql" />
    <Build Include="Views\vw_EmployeeDependentsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeAdditionalInformation.sql" />
    <Build Include="Views\vw_EmployeesBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeDependentsBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_CheckHistoryBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeUserDefinedFieldsBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeStateTaxes.sql" />
    <Build Include="Views\vw_ClientUserDefinedFieldsBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeTaxHistory.sql" />
    <Build Include="Views\vw_ExpSol_LD_Taxes_CodeSetup.sql" />
    <Build Include="Views\vw_EmployeeAdditionalInformationByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeUserDefinedFieldsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_Payroll_Checks.sql" />
    <Build Include="Views\vw_EmployeeDeductionHistory.sql" />
    <Build Include="Views\vw_ExpSol_LD_Deductions.sql" />
    <Build Include="Views\vw_EmployeePTOByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_RPT_Billing_Fees.sql" />
    <Build Include="Views\vw_EmployeeAdditionalInformationBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeBenefits.sql" />
    <Build Include="Views\vw_ClientPTOByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_PayrollDetails_EmployeeFutaSutaWorkersCompHistory.sql" />
    <Build Include="Views\vw_ExpSol_LD_Proration.sql" />
    <Build Include="Views\vw_ExpSol_LD_Taxes_BasicSetup.sql" />
    <Build Include="Views\vw_EmployeeDeductions.sql" />
    <Build Include="Views\vw_UserReportSecurityReports.sql" />
    <Build Include="Views\vw_ClientsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_TransactionHistory.sql" />
    <Build Include="Views\vw_ExpSol_LD_PayWage_NetWages.sql" />
    <Build Include="Views\vw_ExpSol_LD_Benefits_Old.sql" />
    <Build Include="Views\vw_EmployeeFederalTaxesBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_ClientPTO.sql" />
    <Build Include="Views\vw_ExpSol_GL_InvoiceEmployeeCharges.sql" />
    <Build Include="Views\vw_EmployeeLocalTaxesByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_ExpSol_GL_PayrollCodes_Wages.sql" />
    <Build Include="Views\vw_PayHistory.sql" />
    <Build Include="Views\vw_EmployeePayRateHistoryByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_ClientsBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_ExpSol_LD_PayWage_CodeSetup.sql" />
    <Build Include="Views\vw_EmployeeReviewsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_ClientPTOBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmploymentTypeByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_ClientUserDefinedFieldsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeReviews.sql" />
    <Build Include="Views\vw_ExpSol_GL_Fees.sql" />
    <Build Include="Views\vw_ExpSol_LD_PayWage_BasicSetup.sql" />
    <Build Include="Views\vw_EmployeeBenefitHistoryByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeBenefitHistory.sql" />
    <Build Include="Views\vw_EmployeeTrainingBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeReviewsBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeDirectDepositByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_CheckHistory.sql" />
    <Build Include="Views\vw_ExpSol_GL_FutaSutaWC.sql" />
    <Build Include="Views\vw_ExpSol_GL_TrxHistory.sql" />
    <Build Include="Views\vw_ExpSol_LD_DisplayOptions.sql" />
    <Build Include="Views\vw_EmployeeTaxHistoryByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeePaycodesBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeBusinessExpensesBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeDirectDepositBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeDeductionHistoryBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_ClientUserDefinedFields.sql" />
    <Build Include="Views\vw___GroupingTest.sql" />
    <Build Include="Views\vw_ExpSol_GL_AllocationCodes.sql" />
    <Build Include="Views\vw_EmployeeStateTaxesBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_ExpSol_LD_Benefits.sql" />
    <Build Include="Views\vw_ExpSol_LD_Fees.sql" />
    <Build Include="Views\vw_EmployeePayRateHistoryBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_ExpSol_GL_Charges.sql" />
    <Build Include="Views\vw_ExpSol_GL_Credits.sql" />
    <Build Include="Views\vw_EmployeeBenefitHistoryBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeDeductionsByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_Payroll_VoidManual.sql" />
    <Build Include="Views\vw_EmploymentType.sql" />
    <Build Include="Views\vw_PayrollDetails_EmployeeCheckHistory.sql" />
    <Build Include="Views\vw_EmployeesByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeePTOBySupervisorSecuritySettings.sql" />
    <Build Include="Views\vw_TransactionHistoryByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeeStateTaxesByDepartmentSecuritySettings.sql" />
    <Build Include="Views\vw_EmployeePaycodes.sql" />
    <Build Include="Views\vw_EmployeeLicensesAndCertificationsBySupervisorSecuritySettings.sql" />
    <Build Include="Functions\TuBishvat.sql" />
    <Build Include="Functions\YomHaAtzmaut.sql" />
    <Build Include="Functions\Passover.sql" />
    <Build Include="Functions\FindChanukah.sql" />
    <Build Include="Functions\fcn_FindEasterSunday.sql" />
    <Build Include="Functions\fnGetNthWeekdayOfMonth.sql" />
    <Build Include="Functions\PayTypeCode.sql" />
    <Build Include="Functions\TishaBAv.sql" />
    <Build Include="Functions\GetAllowedEmployeesWithInactive.sql" />
    <Build Include="Functions\GetAllowedDocumentTypes.sql" />
    <Build Include="Functions\GetAllowedEmployees.sql" />
    <Build Include="Functions\FederalTaxAllocations.sql" />
    <Build Include="Functions\GetAllowedDepartments.sql" />
    <Build Include="Functions\SplitString.sql" />
    <Build Include="Functions\Get_LocalDateTimeITVF.sql" />
    <Build Include="Functions\sp_LunarPhaseITVF.sql" />
    <Build Include="Functions\ExplodeDates.sql" />
    <Build Include="Stored Procedures\sp_AvailableEmployees.sql" />
    <Build Include="Stored Procedures\sp_AvailableEmployeesFilteredCount.sql" />
    <Build Include="Stored Procedures\sp_AvailableEmployeesNonFilteredCount.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollDetailRegisterByEEDepartment.sql" />
    <Build Include="Stored Procedures\sp_RPT_COVID-19_PPPLoanVerification_DetailVersion.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegistersPayCode.sql" />
    <Build Include="Stored Procedures\Sp_RPT_BenefitsCodeRegister.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterDetail.sql" />
    <Build Include="Stored Procedures\sp_GesnaptEECheckHistory.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetupDocuments_SetVerification.sql" />
    <Build Include="Stored Procedures\Sp_RPT_DeductionCodeRegister.sql" />
    <Build Include="Stored Procedures\sp_GetUsersByDateSummary.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_RestoreFinalize.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegistersPosition.sql" />
    <Build Include="Stored Procedures\Sp_RPT_LocalTaxRegister.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayRollDetails_GetSutaStateCodes.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom2_Izenda.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitPlanAssignment_SubReport.sql" />
    <Build Include="Stored Procedures\Sp_RPT_StateTaxRegister.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetupTask_Remove.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_CleanUp.sql" />
    <Build Include="Stored Procedures\Sp_GetFolderContents.sql" />
    <Build Include="Stored Procedures\usp_OBFinalize_CleanOBTempTBL.sql" />
    <Build Include="Stored Procedures\Sp_RPT_D2_PayrollCheckRegister.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollDetailRegisterByEEDepartment_byInvoice.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegistersStateTax.sql" />
    <Build Include="Stored Procedures\Sp_RPT_D2_TaxLiability_Wages.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetup_CreateDetails.sql" />
    <Build Include="Stored Procedures\sp_ExpSol_GL_Wages.sql" />
    <Build Include="Stored Procedures\SendWakeUp.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitPlanAssignment.sql" />
    <Build Include="Stored Procedures\sp_CHP_CheckDetails.sql" />
    <Build Include="Stored Procedures\sp_RPT_DetailedPayRecap.sql" />
    <Build Include="Stored Procedures\sp_GetClients.sql" />
    <Build Include="Stored Procedures\sp_AddNewUser.sql" />
    <Build Include="Stored Procedures\ProcessEmailQueue.sql" />
    <Build Include="Stored Procedures\sp_CHP_CheckPayrolls.sql" />
    <Build Include="Stored Procedures\SendWelcomeMails.sql" />
    <Build Include="Stored Procedures\sp_TS_RemoveOldColumns.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice.sql" />
    <Build Include="Stored Procedures\sp_CHP_WorkChecks.sql" />
    <Build Include="Stored Procedures\sp_GetEmployees_Count.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollDetailRegisterByEEPosition.sql" />
    <Build Include="Stored Procedures\sp_RPT_TaxLiability.sql" />
    <Build Include="Stored Procedures\sp_CP_GetCheckTakenAndAccruedHours.sql" />
    <Build Include="Stored Procedures\Sp_D2_RPT_FUTASUTAWC.sql" />
    <Build Include="Stored Procedures\sp_RPT_Fees_GetAdminFeeTypes.sql" />
    <Build Include="Stored Procedures\usp_CleanEnrollmentDocuments.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollDetailRegisterByEEPosition_byInvoice.sql" />
    <Build Include="Stored Procedures\Sp_D2_RPT_PayrollCalculate.sql" />
    <Build Include="Stored Procedures\sp_DeleteEmployeeFromSupervisor.sql" />
    <Build Include="Stored Procedures\sp_RPT_TaxLiability_backup.sql" />
    <Build Include="Stored Procedures\sp_GetEEList.sql" />
    <Build Include="Stored Procedures\usp_W2_BULKINSERT.sql" />
    <Build Include="Stored Procedures\sp_GetEEProcessMonitorClient.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_Remove.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayRollDetails_GetPayrollCodes.sql" />
    <Build Include="Stored Procedures\sp_RPT_TaxLiability_Wages.sql" />
    <Build Include="Stored Procedures\sp_GetEEProcessMonitorSystem.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterDetailByDepartment.sql" />
    <Build Include="Stored Procedures\CreatePayrollSnapshot.sql" />
    <Build Include="Stored Procedures\sp_AddRequestHeader.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_RemoveDocument.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayRollDetails_GetPayTypes.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_CreateTasks.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitPlanReconciliation.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_PayWage_BasicSetup.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterDetailByEEDepartment.sql" />
    <Build Include="Stored Procedures\CheckOE.sql" />
    <Build Include="Stored Procedures\sp_RPT_ASOCashRequirements.sql" />
    <Build Include="Stored Procedures\sp_RPT_PostingRegisters.sql" />
    <Build Include="Stored Procedures\sp_ExpSol_vw_ExpSol_GL_TrxHistory.sql" />
    <Build Include="Stored Procedures\sp_TE_ClearDetails.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_Taxes_CodeSetup1.sql" />
    <Build Include="Stored Procedures\Sp_RPT_FUTASUTAWC.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_Replica.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollLaborDistribution.sql" />
    <Build Include="Stored Procedures\sp_ExpSol_vw_ExpSol_GL_Credits.sql" />
    <Build Include="Stored Procedures\Sp_RPT_PayrollCalculate.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterDetailByEEPosition.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollLaborDistributionByDepartment.sql" />
    <Build Include="Stored Procedures\sp_TE_DELETE.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_Proration.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_ClearColumns.sql" />
    <Build Include="Stored Procedures\sp_Report_PayrollRegister.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_PayWage_NetWages.sql" />
    <Build Include="Stored Procedures\usp_OBProfileTask_Remove.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollLaborDistributionByPosition.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_PayWage_CodeSetup.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterDetailByPosition.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetail.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_Finalize.sql" />
    <Build Include="Stored Procedures\UpdateGUIToUsers.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetail_Izenda.sql" />
    <Build Include="Stored Procedures\sp_AssignDocTypesToUser.sql" />
    <Build Include="Stored Procedures\sp_AssignEmployeesToSupervisor.sql" />
    <Build Include="Stored Procedures\Sp_ChangeRequest.sql" />
    <Build Include="Stored Procedures\sp_TS_ChangeColumnStatus.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_Taxes_BasicSetup.sql" />
    <Build Include="Stored Procedures\usp_OBCreateDefaultDocuments.sql" />
    <Build Include="Stored Procedures\sp_TS_CleanPunches.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_Taxes_CodeSetup.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_ComponentCharge.sql" />
    <Build Include="Stored Procedures\sp_TS_ClearColumns.sql" />
    <Build Include="Stored Procedures\Sp_ExpSol_LD_Taxes_FutaSutaWC.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_VerifyCodes.sql" />
    <Build Include="Stored Procedures\sp_TS_ClearDetails.sql" />
    <Build Include="Stored Procedures\sp_RPT_ASOCashRequirements_MultiInvoices.sql" />
    <Build Include="Stored Procedures\sp_GetCompanies.sql" />
    <Build Include="Stored Procedures\sp_TS_ClearRequestDetails.sql" />
    <Build Include="Stored Procedures\usp_CopyUserRoleMenu.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_ApprovalLevel.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_CleanApproval.sql" />
    <Build Include="Stored Procedures\usp_CreateDefaultUserRoleMenu.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_ClearCodes.sql" />
    <Build Include="Stored Procedures\sp_RPT_Fees_GetSutaStates.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_MoveDocument.sql" />
    <Build Include="Stored Procedures\sp_Report_GetAuditControlCodes.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_Izenda.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_ClearJobs.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitClientPlanSetup.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitWaivedCoverage.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitSelectedCoverage.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_ClearRange.sql" />
    <Build Include="Stored Procedures\sp_TS_ShiftColumns.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_Assign.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_MoveDocuments.sql" />
    <Build Include="Stored Procedures\usp_OBDocument_ClientMapping.sql" />
    <Build Include="Stored Procedures\sp_RPT_COVID-19_PPPLoanVerification_SummaryVersion.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom3.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_Section1.sql" />
    <Build Include="Stored Procedures\sp_TS_Profile_RollDown.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceBillingRegister.sql" />
    <Build Include="Stored Procedures\sp_GetEmployees.sql" />
    <Build Include="Stored Procedures\sp_DeleteUserAssignment.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_Delete.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_ReorderColumns.sql" />
    <Build Include="Stored Procedures\sp_RPT_Fees_GetWorkersCompCodes.sql" />
    <Build Include="Stored Procedures\sp_GetEmployees_Demo.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_RecordRemove.sql" />
    <Build Include="Stored Procedures\sp_TS_CreateSetup.sql" />
    <Build Include="Stored Procedures\sp_RPT_PostingRegisterDepartment.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_SectionSubTotal.sql" />
    <Build Include="Stored Procedures\sp_RPT_PostingRegisterPosition.sql" />
    <Build Include="Stored Procedures\sp_TS_UpdateCell.sql" />
    <Build Include="Stored Procedures\sp_RPT_Fees.sql" />
    <Build Include="Stored Procedures\usp_MoveOBUser_ByID.sql" />
    <Build Include="Stored Procedures\sp_TS_SetProfileColumnsFromTS.sql" />
    <None Include="Stored Procedures\sp_RapidPay_Tips.sql" />
    <Build Include="Stored Procedures\sp_RPT_PTOAccrualHistory.sql" />
    <None Include="Stored Procedures\sp_RapidPay_TimeCard.sql" />
    <Build Include="Stored Procedures\usp_OBDocument_ProfileMapping.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitSelectedCoverage_Dependents.sql" />
    <Build Include="Stored Procedures\sp_GetGraphs.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_Remove.sql" />
    <Build Include="Stored Procedures\usp_OBFinalize_CleanDNetTBL.sql" />
    <Build Include="Stored Procedures\sp_TS_AddCell.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_SSRS.sql" />
    <Build Include="Stored Procedures\sp_RPT_DetailedComprehensiveDepartment.sql" />
    <Build Include="Stored Procedures\sp_TS_Delete.sql" />
    <None Include="Stored Procedures\sp_RapidPay_Employee.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_RemoveDocument.sql" />
    <None Include="Stored Procedures\sp_RapidPay_Payroll.sql" />
    <Build Include="Stored Procedures\sp_TS_Profile_Assign.sql" />
    <Build Include="Stored Procedures\usp_OBImport_AddError.sql" />
    <Build Include="Stored Procedures\sp_TS_Profile_Delete.sql" />
    <Build Include="Stored Procedures\sp_TS_Replica.sql" />
    <Build Include="Stored Procedures\sp_TS_RemoveChangesRequests.sql" />
    <Build Include="Stored Procedures\usp_OBDocument_Remap.sql" />
    <Build Include="Stored Procedures\sp_GetInvoiceCharges.sql" />
    <Build Include="Stored Procedures\sp_DNETData_Migration.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetup_Remove.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetail_MP.sql" />
    <Build Include="Stored Procedures\usp_OBDocument_Remove.sql" />
    <Build Include="Stored Procedures\sp_GetInvoices.sql" />
    <Build Include="Stored Procedures\usp_OBDocuments_CleanConditions.sql" />
    <Build Include="Stored Procedures\usp_CreateUserRoleMenu.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom1.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_RemoveInfo.sql" />
    <Build Include="Stored Procedures\sp_Calendar_CleanUp.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_RemoveStateW4Doc.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetailByPosition.sql" />
    <Build Include="Stored Procedures\sp_RPT_BenefitsExport.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetail_YTD.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterSummaryByDepartment.sql" />
    <Build Include="Stored Procedures\sp_TS_CopyImportModel.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom1_Izenda.sql" />
    <Build Include="Stored Procedures\sp_DL_CreateDocumentDataRecords.sql" />
    <Build Include="Stored Procedures\usp_OBImport_CleanUpInvalid.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterSummaryByPosition.sql" />
    <Build Include="Stored Procedures\sp_DL_ReplicateDocumentDataRecords.sql" />
    <Build Include="Stored Procedures\ClientPayrollSchedule_Create.sql" />
    <Build Include="Stored Procedures\SendInvoice.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetailByDepartment.sql" />
    <Build Include="Stored Procedures\Sp_GetLibraryContents.sql" />
    <Build Include="Stored Procedures\usp_OEDocument_Remove.sql" />
    <Build Include="Stored Procedures\usp_DeleteUserRole.sql" />
    <Build Include="Stored Procedures\sp_GetNotesContent.sql" />
    <Build Include="Stored Procedures\sp_RPT_DetailedComprehensiveEmployee.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterYTD.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetailByEEDepartment.sql" />
    <Build Include="Stored Procedures\SendChecks.sql" />
    <Build Include="Stored Procedures\sp_TS_Profile_CleanApproval.sql" />
    <Build Include="Stored Procedures\sp_TimeImport_Remove.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetup_RemoveDocument.sql" />
    <Build Include="Stored Procedures\usp_OEDocument_Remap.sql" />
    <Build Include="Stored Procedures\SendInvoicePreview.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollRegisterDetailByEEPosition.sql" />
    <Build Include="Stored Procedures\ELMAH_GetErrorXml.sql" />
    <Build Include="Stored Procedures\sp_JobCosting_Delete.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegister.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegister_byInvoice.sql" />
    <Build Include="Stored Procedures\usp_OBImport_FinalCleanUp.sql" />
    <Build Include="Stored Procedures\sp_RPT_PostingRegisterPayCode.sql" />
    <Build Include="Stored Procedures\usp_OBDocuments_ClearAssignments.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegisterByDepartmentEE.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_RenewID.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegisterByEEDepartment.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom2.sql" />
    <Build Include="Stored Procedures\sp_RPT_DetailedComprehensiveEmployeeHeader.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegisterByEEDepartment_byInvoice.sql" />
    <Build Include="Stored Procedures\ELMAH_GetErrorsXml.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegisterByEEPosition.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_Copy.sql" />
    <Build Include="Stored Procedures\sp_InvoiceReport_PayrollSummaryRegister.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegisterByEEPosition_byInvoice.sql" />
    <Build Include="Stored Procedures\sp_RemoveDocAssignment.sql" />
    <Build Include="Stored Procedures\sp_DeletePayrollTeam.sql" />
    <Build Include="Stored Procedures\CreateInvoiceSnapshot.sql" />
    <Build Include="Stored Procedures\sp_TS_ClearImportDetails.sql" />
    <Build Include="Stored Procedures\FindTablesByColumns.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollSummaryRegisterByPositionEE.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_MassUpdate.sql" />
    <Build Include="Stored Procedures\SearchAllTables.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_CleanCodes.sql" />
    <Build Include="Stored Procedures\SearchString.sql" />
    <Build Include="Stored Procedures\sp_RPT_PTOAccrualDetail.sql" />
    <Build Include="Stored Procedures\sp_InvoiceReport_PayrollDetailRegister.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_ClearDocumentAssignments.sql" />
    <Build Include="Stored Procedures\sp_FieldInfo.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegisters.sql" />
    <Build Include="Stored Procedures\Sp_Dhara_SSISDemo.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceHeader.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom4.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegistersBenefit.sql" />
    <Build Include="Stored Procedures\sp_TS_Profile_ClearCodes.sql" />
    <Build Include="Stored Procedures\usp_OBImport_Remove.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterSummary.sql" />
    <Build Include="Stored Procedures\sp_WhoIsActive.sql" />
    <Build Include="Stored Procedures\sp_RPT_EmployeeCheckHistory.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_Create.sql" />
    <Build Include="Stored Procedures\usp_ClientClassesDelete.sql" />
    <Build Include="Stored Procedures\ELMAH_LogError.sql" />
    <Build Include="Stored Procedures\usp_ClientClassesInsert.sql" />
    <Build Include="Stored Procedures\sp_TS_RemoveTempDetails.sql" />
    <Build Include="Stored Procedures\usp_DeleteUserRoleMenu.sql" />
    <Build Include="Stored Procedures\usp_ClientClassesSelect.sql" />
    <Build Include="Stored Procedures\sp_DeletePayrollProfile.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_Credits.sql" />
    <Build Include="Stored Procedures\usp_ClientClassesUpdate.sql" />
    <Build Include="Stored Procedures\sp_InsertEEWorkStatus.sql" />
    <Build Include="Stored Procedures\usp_OBImport_SetID.sql" />
    <Build Include="Stored Procedures\usp_OBDocument_CreateDefaults.sql" />
    <Build Include="Stored Procedures\sp_RPT_EmployeePayrollSummary.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceSummaryRegisterByPosition.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_SetColumnsCodeType.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetup_CleanAssignments.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegistersDeduction.sql" />
    <Build Include="Stored Procedures\sp_TS_SetColumnsCodeType.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapEEDeductions.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollCheckRegister.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegistersDepartment.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapFederalWitholdingTax.sql" />
    <Build Include="Stored Procedures\sp_TS_ClientProfile_RefreshSysApproval.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapLocalTax.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapFederalSocialSecurityTax.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollCheckRegisterByDepartment.sql" />
    <Build Include="Stored Procedures\sp_RPT_ReprintRegistersLocalTax.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapFederalMedWitholdingTax.sql" />
    <Build Include="Stored Procedures\sp_DeleteUser.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetup_CleanCodes.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapEEDirectDeposit.sql" />
    <Build Include="Stored Procedures\sp_InsertUserReportSecurity.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_AddClientSetupDoc.sql" />
    <Build Include="Stored Procedures\sp_TS_RemoveOldDetails.sql" />
    <Build Include="Stored Procedures\sp_RPT_Client_GetTaxes.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapEEBenefits.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollCheckRegisterByPosition.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetup_Replica.sql" />
    <Build Include="Stored Procedures\sp_Add_IP_Secirity.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapStateTax.sql" />
    <Build Include="Stored Procedures\sp_RPT_CompanyClientInfo.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapEEPayCodes.sql" />
    <Build Include="Stored Procedures\sp_RPT_ClientEmployeeMetrics.sql" />
    <Build Include="Stored Procedures\usp_OBTaskRecordsSelect.sql" />
    <Build Include="Stored Procedures\sp_TS_RestorePTOPunches.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayRollDetails_GetWorkerCompCodes.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapInfo.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollDetailRegister.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_AddRecord.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetupTask_Assign.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapSearchInfo_clientwithactiveEEInSearch.sql" />
    <Build Include="Stored Procedures\sp_RPT_EmployeePayrollSummaryByDepartment.sql" />
    <Build Include="Stored Procedures\sp_RPT_AgencyCheck.sql" />
    <Build Include="Stored Procedures\usp_OBClientSetup_ClearDocumentAssignments.sql" />
    <Build Include="Stored Procedures\Sp_RPT_PaycodeRegisterByPayCode.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceHistory.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapSearchInfo_clientwithInactiveEEInSearch.sql" />
    <Build Include="Stored Procedures\usp_OBProcess_AddW4Doc.sql" />
    <Build Include="Stored Procedures\usp_OBDocuments_DropStateDefault.sql" />
    <Build Include="Stored Procedures\sp_TE_ErigoDelete.sql" />
    <Build Include="Stored Procedures\Sp_RPT_WorkersCompCodeRegister.sql" />
    <Build Include="Stored Procedures\sp_GetClientLocations.sql" />
    <Build Include="Stored Procedures\usp_OBProfile_CreateDetails.sql" />
    <Build Include="Stored Procedures\sp_GetEESnapSearchInfo_system.sql" />
    <Build Include="Stored Procedures\usp_OBDocument_Copy.sql" />
    <Build Include="Stored Procedures\sp_DeleteClientTeam.sql" />
    <Build Include="Tables\SalesTaxScheduleAssignments.sql" />
    <Build Include="Tables\EmployeeCheckStatusHistory.sql" />
    <Build Include="Tables\SalesTaxSchedules.sql" />
    <Build Include="Tables\Vendors.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom1_D2.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom2_D2.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceCustom3_D2.sql" />
    <Build Include="Stored Procedures\sp_GetWorkCheck.sql" />
    <Build Include="Stored Procedures\sp_CHP_CheckPayrolls_NonFiltered.sql" />
    <Build Include="Stored Procedures\sp_CHP_CheckPayrolls_Filtered.sql" />
    <Build Include="Stored Procedures\sp_CHP_CheckPayrolls_Filtered_Count.sql" />
    <Build Include="Stored Procedures\Sp_RPT_InvoiceDetailRegister-bundled_D2.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterSummary_D2.sql" />
    <Build Include="Stored Procedures\sp_RPT_ComponentNames.sql" />
    <Build Include="Stored Procedures\sp_RPT_ComponentBillingReport.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_ComponentBilling.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_ComponentBilling_Cohesion.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_D2.sql" />
    <Build Include="Tables\PostStatus.sql" />
    <Build Include="Stored Procedures\Sp_RPT_PayrollCalculate_Dnet.sql" />
    <Build Include="Stored Procedures\sp_RPT_ASOCashRequirements_D2.sql" />
    <Build Include="Stored Procedures\sp_RPT_Invoice_Credits_D2.sql" />
    <Build Include="Stored Procedures\sp_RPT_InvoiceRegisterDetail_D2.sql" />
    <Build Include="Tables\EmployeeInvoiceCheckHistoryOriginals.sql" />
    <Build Include="Stored Procedures\sp_RPT_DescriptionInvoice_D2.sql" />
    <Build Include="Tables\ClientTeamMembers.sql" />
    <Build Include="Tables\ClientTeams.sql" />
    <Build Include="Tables\ClientTeamTypes.sql" />
    <Build Include="Stored Procedures\sp_RPT_AgencyCheck_D2.sql" />
    <Build Include="Stored Procedures\sp_GetTotalBurden.sql" />
    <Build Include="Stored Procedures\sp_GetInvoicesWithVoidedStatus.sql" />
    <None Include="Data\Default\CompanyDocumentDefaultFields.sql" />
    <None Include="Data\Default\ControlsAndVariances.sql" />
    <None Include="Data\Default\DarwinColumnMap.sql" />
    <None Include="Data\Default\DarwiNetCodes.sql" />
    <None Include="Data\Default\DarwiNetNotifications.sql" />
    <None Include="Data\Default\DarwinTableMap.sql" />
    <None Include="Data\Default\Languages.sql" />
    <None Include="Data\Default\Library.sql" />
    <None Include="Data\Default\Menuitems.sql" />
    <None Include="Data\Default\OnBoardingDocumentDefaultFields.sql" />
    <None Include="Data\Default\OnBoardingDocumentMapping.sql" />
    <None Include="Data\Default\OnBoardingDocuments.sql" />
    <None Include="Data\Default\OnBoardingFields.sql" />
    <None Include="Data\Default\OnBoardingFinalizeSections.sql" />
    <None Include="Data\Default\OnBoardingNotifications.sql" />
    <None Include="Data\Default\OnBoardingStateW4Rules.sql" />
    <None Include="Data\Default\OnBoardingTasks.sql" />
    <None Include="Data\Default\OpenEnrollmentDataFields.sql" />
    <None Include="Data\Default\ProjectSetup.sql" />
    <None Include="Data\Default\ReportTables.sql" />
    <None Include="Data\Default\SecurityQuestions.sql" />
    <None Include="Data\Default\SmartTags.sql" />
    <None Include="Data\Default\SSNDisplayTypes.sql" />
    <None Include="Data\Default\SSRSReports.sql" />
    <None Include="Data\Default\TallyCalendar.sql" />
    <None Include="Data\Default\TaxFilingAdditionals.sql" />
    <None Include="Data\Default\TimeSheetColumnDefaults.sql" />
    <None Include="Data\Default\Translations.sql" />
    <None Include="Data\Default\UserRoleMenuAccess.sql" />
    <None Include="Data\Default\Users.sql" />
    <None Include="Data\UpdateData.sql" />
    <Build Include="Tables\CustomReportDetails.sql" />
    <Build Include="Tables\CustomReports.sql" />
    <Build Include="Tables\InvoiceChargeExpansionDetailOriginals.sql" />
    <Build Include="Stored Procedures\sp_RW_AssignToClient.sql" />
    <Build Include="Stored Procedures\sp_RW_Copy.sql" />
    <Build Include="Stored Procedures\Sp_RPT_MissedPay.sql" />
    <Build Include="Tables\ACHProfiles.sql" />
    <Build Include="Tables\ACHHeaderFooterSetup.sql" />
    <Build Include="Tables\ClientACHHeaders.sql" />
    <Build Include="Tables\CustomReportAssignments.sql" />
    <Build Include="Tables\CustomReportFilters.sql" />
    <Build Include="Tables\CustomReportSorts.sql" />
    <Build Include="Tables\Table1.sql" />
    <None Include="Data\Default\ReportDataFields.sql" />
    <Build Include="Stored Procedures\sp_RW_RollDown.sql" />
    <Build Include="Tables\ReportDataFields.sql" />
    <Build Include="Tables\OutboxMessages.sql" />
    <Build Include="Tables\EmployeePayrollChanges.sql" />
    <Build Include="Tables\ClientPTOTiers.sql" />
    <Build Include="Tables\PayrollWorkDirectDeposits.sql" />
    <Build Include="Tables\PayrollWorkDirectDepositsOriginals.sql" />
    <None Include="Data\Default\Job_CheckOE.sql" />
    <None Include="Data\Default\Job_SendPayrollNotifications.sql" />
    <Build Include="Tables\InvoiceAgencyCreditMissedCodesHistoryOriginals.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollACHTransactions.sql" />
    <Build Include="Tables\Deals.sql" />
    <Build Include="Tables\DealDeliverables.sql" />
    <Build Include="Tables\DealAthletePayments.sql" />
    <Build Include="Tables\DealTypes.sql" />
    <Build Include="Tables\DealActivityTypes.sql" />
    <Build Include="Tables\InterfaceTranslations.sql" />
    <Build Include="Tables\DealAthleteTaxPayments.sql" />
    <Build Include="Tables\Donors.sql" />
    <Build Include="Tables\DonorDonations.sql" />
    <Build Include="Tables\DealAthleteDeliverables.sql" />
    <None Include="Data\Default\InterfaceTranslations.sql" />
    <Build Include="Tables\ReportDataObjects.sql" />
    <None Include="Data\Default\ReportDataObjects.sql" />
    <Build Include="Stored Procedures\Sp_RPT_PayrollCalculate_Preview.sql" />
    <Build Include="Stored Procedures\sp_RPT_PayrollCalculateReport_Preview.sql" />
    <Build Include="Tables\IpRestrictionPolicy.sql" />
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="DatabaseObjects.refactorlog" />
  </ItemGroup>
  <ItemGroup>
    <PostDeploy Include="Script.PostDeployment.sql" />
  </ItemGroup>
  <ItemGroup>
    <None Include="DatabaseObjects.publish.xml" />
    <None Include="ExistingDatabaseUpdatePublish.xml" />
    <None Include="ExistingDnetDatabaseUpdate.publish.xml" />
  </ItemGroup>
</Project>