

CREATE VIEW [dbo].[vw_EmployeePayRateHistoryByDepartmentSecuritySettings]
AS
SELECT 
	eph.CompanyID, 
	ISNULL(e.ClientID, SUBSTRING(eph.EmployeeID, 3 ,3)) ClientID, 
	eph.EmployeeID, 
	eph.PayRecord [Payment Code], 
	eph.EffectiveDate,
	eph.PayRateAmountOld [Old Amount], 
	eph.PayRateAmount [New Amount], 
	eph.UserID [Changed By], 
	eph.[Date] [Date Changed], 
	eph.ChangeReason,
	CASE WHEN (eph.PayRateAmountOld IS NULL OR eph.PayRateAmountOld = 0) THEN 100 ELSE (eph.PayRateAmount - eph.PayRateAmountOld) / eph.PayRateAmountOld * 100 END [Change Percentage],
	UDDS.UserID
FROM    
	EmployeePayrateHistory eph
	
		LEFT OUTER JOIN Employees e 
		ON e.CompanyID = eph.CompanyID 
		AND e.EmployeeID = eph.EmployeeID

		-- If department security is turned on for the client, then get the specifics from UserDeptDivSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseDepartmentSecurity = 1

		INNER JOIN dbo.UserDeptDivSecurity AS UDDS 
		ON UDDS.CompanyID = e.CompanyID 
		AND UDDS.ClientID = e.ClientID 
		AND (UDDS.AllAccess = 1 OR UDDS.EmployeeAccess = 1) 
		AND UDDS.[Enable] = 1
		AND UDDS.Department = e.Department
