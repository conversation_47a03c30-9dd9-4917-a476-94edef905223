
CREATE VIEW [dbo].[vw_EmployeeBenefitsBySupervisorSecuritySettings]
AS
SELECT        
	eb.CompanyID, 
	e.ClientID, 
	eb.EmployeeID, 
	eb.Benefit, 
	b.Des<PERSON>, 
	CASE WHEN eb.Inactive = 0 THEN 'Inactive' WHEN eb.Inactive = 1 THEN 'Active' END AS InActive, 
	eb.BenefitBeginDate AS StartDate, 
	eb.EffectiveDate, 
	eb.BenefitEndDate, 
	CASE WHEN eb.BasedOnRecords = 0 THEN 'ALL' WHEN eb.BasedOnRecords = 1 THEN 'Selected' END AS BasedOnPayCode, 
	bmeth.CodeText AS BenefitMethod,
	eb.BenefitAmount1, 
	eb.BenefitAmount2, 
	eb.BenefitAmount3, 
	eb.BenefitAmount4, 
	eb.BenefitAmount5, 
	eb.PercentArray1, 
	eb.PercentArray2, 
	eb.PercentArray3, 
	eb.PercentArray4, 
	eb.PercentArray5, 
	eb.BenefitPayPeriodMax, 
	eb.BenefitMonthMax, 
	eb.BenefitQtrMax, 
	eb.BenefitFiscalMax, 
	eb.BenefitLifetimeMax
FROM            
	dbo.EmployeeBenefits AS eb 
	
		INNER JOIN dbo.Employees AS e 
		ON eb.EmployeeID = e.EmployeeID 
		
		INNER JOIN dbo.Benefits AS b 
		ON b.CompanyID = eb.CompanyID 
		AND b.Benefit = eb.Benefit 

		LEFT OUTER JOIN dbo.DarwiNetCodes AS bmeth 
		ON bmeth.Value = eb.BenefitMethod 
		AND bmeth.CodeType = 'BenefitMethod'

		-- If supervisor security is turned on for the client, then get the specifics from UserSupervisorSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseSupervisorSecurity = 1

		INNER JOIN dbo.UserSupervisorSecurity AS USS 
		ON USS.CompanyID = e.CompanyID 
		AND USS.ClientID = e.ClientID 
		AND USS.EmployeeID = e.EmployeeID

