

CREATE VIEW [dbo].[vw_EmployeeBusinessExpensesBySupervisorSecuritySettings]
AS

SELECT 
	ebe.CompanyID, 
	ebe.ClientID, 
	ebe.EmployeeID, 
	ebe.[Date], 
	ebe.ExpenseType, 
	ebe.Vendor, 
	ebe.PayCode, 
	ebe.Amount, 
	ebe.Note
FROM    
	EmployeeBusinessExpenses ebe

		-- If department security is turned on for the client, then get the specifics from UserSupervisorSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = ebe.CompanyID
		AND DS.ClientID = ebe.ClientID
		AND DS.UseSupervisorSecurity = 1

		INNER JOIN dbo.UserSupervisorSecurity AS USS 
		ON USS.CompanyID = ebe.CompanyID 
		AND USS.ClientID = ebe.ClientID 
		AND USS.EmployeeID = ebe.EmployeeID


