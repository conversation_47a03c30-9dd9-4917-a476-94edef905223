




CREATE VIEW [dbo].[vw_RPT_Benefit_PlanAssignment]
AS
SELECT eap.CompanyID, e.ClientID, eap.YEAR1, eap.PlanName, e.LastName, e.FirstName, '***-**-' + RIGHT(e.SSN,4) SSN, eap.[Description] Selection,
	EmployeeDeductionAmount [EEMonthly], ed.DeductionAmount1 [EEPayPeriod], eap.BenefitAmount [ERMonthly], eb.BenefitAmount1 [ERPayPeriod]
FROM EmployeeAssignedPlans eap
	LEFT OUTER JOIN Employees e ON e.CompanyID = eap.CompanyID AND e.EmployeeID = eap.EmployeeID
	INNER JOIN EmployeeDeductions ed ON ed.CompanyID = eap.CompanyID AND ed.EmployeeID = eap.EmployeeID AND ed.Deduction = eap.EmployeeDeductionCode
	INNER JOIN EmployeeBenefits eb ON eb.CompanyID = eap.CompanyID AND eb.EmployeeID = eap.EmployeeID AND eb.Benefit = eap.EmployeeBenefitCode


