

CREATE VIEW [dbo].[vw_EmployeeLicensesAndCertifications]
AS
SELECT elc.CompanyID, ISNULL(e.ClientID, SUBSTRING(elc.EmployeeID, 3 ,3)) ClientID, elc.EmployeeID, elc.LicenseCertificationID, elc.[Description], elc.LicenseCertificationNumber, elc.EffectiveDate, elc.ExpirationDate, elc.Comments, 
		CASE WHEN elc.Inactive = 0 THEN 'N' 
			 WHEN elc.Inactive = 1 THEN 'Y' END [Inactive]
		
FROM    EmployeeLicensesAndCertifications elc
	LEFT OUTER JOIN Employees e ON e.CompanyID = elc.CompanyID AND e.EmployeeID = elc.EmployeeID


