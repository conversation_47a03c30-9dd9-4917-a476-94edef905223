

CREATE VIEW [dbo].[vw_EmployeeBenefitHistoryByDepartmentSecuritySettings]
AS
SELECT 
	eth.CompanyID, 
	ISNULL(e.ClientID, SUBSTRING(eth.EmployeeID, 3 ,3)) ClientID,
	eth.EmployeeID, 
	eth.PaymentAdjustmentNumber [Payment ID], 
	eth.CheckNumber, 
	eth.CheckDate, 
	eth.PayrollCode [Benefit Code], 
	d.[Description] [Benefit Code Description], 
	eth.TRXAmount, 
	eth.Department, 
	eth.Position,
	UDDS.UserID
FROM    
	EmployeeTransactionHistory eth
	
		INNER JOIN Deductions d 
		on d.CompanyID = eth.CompanyID 
		AND d.Deduction = eth.PayrollCode

		LEFT OUTER JOIN Employees e 
		ON e.CompanyID = eth.CompanyID 
		AND e.EmployeeID = eth.EmployeeID

		-- If department security is turned on for the client, then get the specifics from UserDeptDivSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseDepartmentSecurity = 1

		INNER JOIN dbo.UserDeptDivSecurity AS UDDS 
		ON UDDS.CompanyID = e.CompanyID 
		AND UDDS.ClientID = e.ClientID 
		AND (UDDS.AllAccess = 1 OR UDDS.EmployeeAccess = 1) 
		AND UDDS.[Enable] = 1
		AND UDDS.Department = e.Department

WHERE 
	eth.PayrollRecordType = 3


