

CREATE VIEW [dbo].[vw_CheckHistoryByDepartmentSecuritySettings]
AS
SELECT
	EICH.CompanyID, 
	EICH.EmployeeID, 
	EICH.PaymentAdjustmentNumber AS [PaymentID], 
	EICH.ClientID, 
	C.<PERSON>, 
	EICH.CheckDate, 
	EICH.CheckNumber, 
	EICH.GrossWagesPayRun, 
	EICH.FederalWithholdingPayRun AS [Fed Tax], 
	EICH.FICASSWithholdingPayRun AS [FICA Soc Tax], 
	EICH.FICAMWithholdingPayRun AS [FICA Med Tax],
	EICH.TotalDeductions, 
	EICH.TotalBenefits, 
	EICH.NetWagesPayRun AS [Net Pay], 
	EICH.StartPayPeriod, 
	EICH.EndPayPeriod, 
	StateTaxWithholdingArray1 + StateTaxWithholdingArray2 + StateTaxWithholdingArray3 + StateTaxWithholdingArray4 + StateTaxWithholdingArray5 + 
		StateTaxWithholdingArray6 + StateTaxWithholdingArray7 + StateTaxWithholdingArray8 + StateTaxWithholdingArray9 + StateTaxWithholdingArray10 + 
		StateTaxWithholdingArray11 + StateTaxWithholdingArray12 + StateTaxWithholdingArray13 + StateTaxWithholdingArray14 + StateTaxWithholdingArray15 + 
		StateTaxWithholdingArray16 + StateTaxWithholdingArray17 + StateTaxWithholdingArray18 + StateTaxWithholdingArray19 + StateTaxWithholdingArray20 + 
		StateTaxWithholdingArray21 + StateTaxWithholdingArray22 + StateTaxWithholdingArray23 + StateTaxWithholdingArray24 + StateTaxWithholdingArray25 AS [State Tax],
	LocalTaxWithholdingArray1 + LocalTaxWithholdingArray2 + LocalTaxWithholdingArray3 + LocalTaxWithholdingArray4 + LocalTaxWithholdingArray5 + 
		LocalTaxWithholdingArray6 + LocalTaxWithholdingArray7 + LocalTaxWithholdingArray8 + LocalTaxWithholdingArray9 + LocalTaxWithholdingArray10 + 
		LocalTaxWithholdingArray11 + LocalTaxWithholdingArray12 + LocalTaxWithholdingArray13 + LocalTaxWithholdingArray14 + LocalTaxWithholdingArray15 + 
		LocalTaxWithholdingArray16 + LocalTaxWithholdingArray17 + LocalTaxWithholdingArray18 + LocalTaxWithholdingArray19 + LocalTaxWithholdingArray20 + 
		LocalTaxWithholdingArray21 + LocalTaxWithholdingArray22 + LocalTaxWithholdingArray23 + LocalTaxWithholdingArray24 + LocalTaxWithholdingArray25 AS [Local Tax],
	UDDS.UserID

FROM 
	EmployeeInvoiceCheckHistory AS EICH

		INNER JOIN ClientEmployees AS CE 
		ON CE.CompanyID = EICH.CompanyID 
		AND CE.EmployeeID = EICH.EmployeeID 

		INNER JOIN Clients AS C 
		ON C.CompanyID = EICH.CompanyID 
		AND C.ClientID = EICH.ClientID

		-- If department security is turned on for the client, then get the specifics from UserDeptDivSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = EICH.CompanyID
		AND DS.ClientID = EICH.ClientID
		AND DS.UseDepartmentSecurity = 1

		INNER JOIN dbo.UserDeptDivSecurity AS UDDS 
		ON UDDS.CompanyID = EICH.CompanyID 
		AND UDDS.ClientID = EICH.ClientID 
		AND (UDDS.AllAccess = 1 OR UDDS.EmployeeAccess = 1) 
		AND UDDS.[Enable] = 1
		AND UDDS.Department = EICH.Department


