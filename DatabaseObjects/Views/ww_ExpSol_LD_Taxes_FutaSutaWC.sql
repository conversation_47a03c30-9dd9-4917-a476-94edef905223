
CREATE VIEW [dbo].[ww_ExpSol_LD_Taxes_FutaSutaWC]
AS
SELECT        ech.CompanyID, eich.ClientID, ech.EmployeeID, cdd.DivisionID, ech.Department, ech.Position, ech.CheckDate, eich.DarwinInvoiceNumber AS Invoice, eich.Voided, 
                         CASE WHEN PayrollRecordType = 1 THEN 'SUTA' WHEN PayrollRecordType = 2 THEN 'FUTA' ELSE 'WC' END AS [Tax Type], 
						 --BUG 5837 FIX START
						 SUM(ISNULL(ech.FUTABilling, 0)) AS Amount, 
						 --BUG 5837 FIX END
						 ISNULL(ech.PayrollCode, N'') AS [Tax Code]
FROM            dbo.EmployeeFutaSutaWorkersCompHistory AS ech INNER JOIN
                         dbo.EmployeeCheckHistory AS ech1 ON ech1.CompanyID = ech.CompanyID AND ech1.EmployeeID = ech.EmployeeID AND ech1.AuditControlCode = ech.AuditControlCode INNER JOIN
                         dbo.Employees AS e ON e.CompanyID = ech.CompanyID AND e.EmployeeID = ech.EmployeeID INNER JOIN
                         dbo.EmployeeInvoiceCheckHistory AS eich ON eich.CompanyID = ech.CompanyID AND eich.EmployeeID = ech.EmployeeID AND eich.CheckNumber = ech1.CheckNumber INNER JOIN
                         dbo.Invoices AS i ON i.CompanyID = ech.CompanyID AND i.ClientID = eich.ClientID AND i.DarwinInvoiceNumber = eich.DarwinInvoiceNumber INNER JOIN
                         dbo.ClientDivisionDetails AS cdd ON cdd.CompanyID = ech.CompanyID AND cdd.ClientID = eich.ClientID AND cdd.DivisionID = i.DivisionID AND cdd.Department = ech.Department
WHERE        (ech.TotalPay <> 0)
--BUG 5837 FIX START
GROUP BY ech.CompanyID, eich.ClientID, ech.EmployeeID, cdd.DivisionID, ech.Department, ech.Position, ech.CheckDate, eich.DarwinInvoiceNumber, eich.Voided, ech.PayrollRecordType, ech.PayrollCode
--BUG 5837 FIX END

GO
EXECUTE sp_addextendedproperty @name = N'MS_DiagramPane1', @value = N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "ech"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 135
               Right = 272
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "ech1"
            Begin Extent = 
               Top = 6
               Left = 310
               Bottom = 135
               Right = 557
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "e"
            Begin Extent = 
               Top = 6
               Left = 595
               Bottom = 135
               Right = 909
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "eich"
            Begin Extent = 
               Top = 6
               Left = 947
               Bottom = 135
               Right = 1189
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "i"
            Begin Extent = 
               Top = 6
               Left = 1227
               Bottom = 135
               Right = 1449
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "cdd"
            Begin Extent = 
               Top = 138
               Left = 38
               Bottom = 267
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 9
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Wi', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'VIEW', @level1name = N'ww_ExpSol_LD_Taxes_FutaSutaWC';


GO
EXECUTE sp_addextendedproperty @name = N'MS_DiagramPane2', @value = N'dth = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'VIEW', @level1name = N'ww_ExpSol_LD_Taxes_FutaSutaWC';


GO
EXECUTE sp_addextendedproperty @name = N'MS_DiagramPaneCount', @value = 2, @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'VIEW', @level1name = N'ww_ExpSol_LD_Taxes_FutaSutaWC';

