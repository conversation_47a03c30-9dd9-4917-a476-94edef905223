


CREATE VIEW [dbo].[vw_PayrollDetails_EmployeeCheckHistory]
AS
	SELECT ech.CompanyID, c.Company<PERSON>ame, cl.ClientID, cl.ClientName, cl.RegionID, cl.RegionDescription, cdd.DivisionID, cd.[Description], ech.CheckDate AS [Date], ech.<PERSON><PERSON>,
		   ech.FederalWithholdingPayRun, ech.EmployerFICASSWithholding, ech.FICASSWithholdingPayRun, ech.EmployerFICAMWithholding, ech.FICAMWithholdingPayRun
	FROM EmployeeCheckHistory ech
		LEFT OUTER JOIN Companies c ON c.CompanyID = ech.CompanyID
		LEFT OUTER JOIN ClientEmployees ce on ce.CompanyID = ech.CompanyID and ce.EmployeeID = ech.EmployeeID
        LEFT OUTER JOIN Clients cl on cl.CompanyID = ce.CompanyID and cl.ClientID = ce.ClientID
        LEFT OUTER JOIN ClientDivisionDetails cdd on cdd.CompanyID = ce.CompanyID and cdd.ClientID = ce.ClientID and cdd.Department = ech.Department
		LEFT OUTER JOIN ClientDivisions cd ON cdd.DivisionID = cd.DivisionID AND cd.CompanyID = ech.CompanyID AND cd.ClientID = ce.ClientID



