


CREATE VIEW [dbo].[vw_EmployeeDeductionsBySupervisorSecuritySettings]
AS
SELECT 
	ed.CompanyID, 
	ed.Client<PERSON>, 
	ed.EmployeeID, 
	ed.Deduction, 
	d.[Description], 
	CASE WHEN ed.Inactive = 0 THEN 'Inactive' 
			WHEN ed.Inactive = 1 THEN 'Active' END [InActive], 
	CASE WHEN ed.BasedOnRecords = 0 THEN 'ALL' 
			WHEN ed.BasedOnRecords = 1 THEN 'Selected' END [BasedOnPayCode], 
	ed.DeductionBegDate [StartDate], 
	ed.DeductionEndDate [EndDate], 
	ed.EffectiveDate, 
	dmeth.CodeText DeductionMethod, 
	ed.DeductionAmount1, 
	ed.DeductionAmount2, 
	ed.DeductionAmount3, 
	ed.DeductionAmount4, 
	ed.DeductionAmount5, 
	ed.DeductionPercent1, 
	ed.DeductionPercent2, 
	ed.DeductionPercent3, 
	ed.DeductionPercent4, 
	ed.DeductionPercent5,
	d.PayPeriodMax, 
	d.De<PERSON>ax, 
	d.DeductionQuarterMax, 
	d.<PERSON><PERSON><PERSON><PERSON>ear<PERSON>ax,
	d.DeductionType, 
	d.<PERSON>, 
	d.LifetimeMax
FROM    
	EmployeeDeductions ed

		INNER JOIN Deductions d 
		ON d.Deduction = ed.Deduction 
		AND d.CompanyID = ed.CompanyID

		LEFT OUTER JOIN DarwiNetCodes dmeth 
		ON dmeth.Value = ed.DeductionMethod 
		AND dmeth.CodeType = 'DeductionMethod'

		-- If supervisor security is turned on for the client, then get the specifics from UserSupervisorSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = ed.CompanyID
		AND DS.ClientID = ed.ClientID
		AND DS.UseSupervisorSecurity = 1

		INNER JOIN dbo.UserSupervisorSecurity AS USS 
		ON USS.CompanyID = ed.CompanyID 
		AND USS.ClientID = ed.ClientID 
		AND USS.EmployeeID = ed.EmployeeID



