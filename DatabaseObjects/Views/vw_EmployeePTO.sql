


CREATE VIEW [dbo].[vw_EmployeePTO]
AS
SELECT pto.CompanyID, ISNULL(e.ClientID, SUBSTRING(pto.EmployeeID, 3 ,3)) ClientID,
	   pto.EmployeeID,
	   ptype.CodeText PTOType, 
	   pto.[Description] AS PTOName, 
	   CAST(pto.YTDHoursAccrued AS decimal(19, 5)) / 100.0 AS YTDAccrued,
	   CAST(pto.AvailableHours AS decimal(19, 5)) / 100.0 AS Available,
	   CAST(pto.PTOAmountTaken AS decimal(19, 5)) / 100 AS Taken, 
	   CASE WHEN pto.Inactive = 0 THEN 'Inactive' WHEN pto.Inactive = 1 THEN 'Active' END AS InActive,
	   CASE WHEN pto.PTOAnniversaryMethod = 0 THEN ''
	        WHEN pto.PTOAnniversaryMethod = 1 THEN 'Year-End'
			WHEN pto.PTOAnniversaryMethod = 2 THEN 'Orig. Date of Hire'
			WHEN pto.PTOAnniversaryMethod = 3 THEN 'Date'
	   END AS AnniversaryBasedOn, 
       pto.PTOLastAnniversaryDate AS LastAnniversaryDate,
	   pam.CodeText AccrualMethod,
	   CAST(pto.PTOHoursPerYear AS decimal(19, 5)) / 10000.0 AS PTOHoursPerYear, 
       pto.MaxAccrualBasedOnRG AS MaxAccrualBasedOn,
	   CAST(pto.MaxAccrualYTD AS decimal(19, 5)) / 100.0 AS MaxAccrualHrsYTD,
	   CAST(pto.MaxAccrualLTD AS decimal(19, 5)) / 100.0 AS MaxAccrualHrsLTD,
	   pto.WaitPeriodAccrualBeginLimit AS WaitingPeriodToBeginAccrual,
	   pto.WaitPeriodAccrualBeginDate AS DateToBeginAccrual,
	   pto.WaitPeriodUsageBeginLimit AS WaitingPeriodToBeginUse,
	   pto.WaitPeriodUsageBeginDate AS DateToBeginUse, 
	   pto.CarryOver,
	   CAST(pto.MaxCarryOver AS decimal(19, 5)) / 100.0 AS MaxHoursToCarryOver,
	   CAST(pto.LastCarryoverAmount AS decimal(19, 5)) / 100.0 AS LastAnniversaryCarryOverAmount
FROM dbo.EmployeePTOTypes AS pto
	LEFT OUTER JOIN Employees e ON e.CompanyID = pto.CompanyID AND e.EmployeeID = pto.EmployeeID
	LEFT OUTER JOIN DarwinetCodes AS ptype ON ptype.CodeType = 'PTOTypes' AND ptype.Value = pto.PTOType
	LEFT OUTER JOIN DarwinetCodes AS pam ON pam.CodeType = 'PTOAccrualMethods' AND pam.Value = pto.AccrualMethod





GO
EXECUTE sp_addextendedproperty @name = N'MS_DiagramPane1', @value = N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "pto"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 125
               Right = 263
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 9
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 1440
         Alias = 900
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'VIEW', @level1name = N'vw_EmployeePTO';


GO
EXECUTE sp_addextendedproperty @name = N'MS_DiagramPaneCount', @value = 1, @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'VIEW', @level1name = N'vw_EmployeePTO';

