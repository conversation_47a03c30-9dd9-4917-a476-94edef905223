

CREATE VIEW [dbo].[vw_EmployeeTaxHistoryBySupervisorSecuritySettings]
AS
	SELECT 
		eth.CompanyID, 
		ISNULL(e.ClientID, SUBSTRING(eth.EmployeeID, 3 ,3)) ClientID, 
		eth.EmployeeID, 
		eth.PaymentAdjustmentNumber [Payment ID], 
		eth.CheckNumber, 
		eth.CheckDate, 
		eth.PayrollCode [Tax Code],
		'UNDONE YET' [Tax Code Description], 
		eth.TRXAmount [Amount]
	FROM 
		EmployeeTransactionHistory eth
	
		LEFT OUTER JOIN Employees e 
		ON e.CompanyID = eth.CompanyID 
		AND e.EmployeeID = eth.EmployeeID

		-- If supervisor security is turned on for the client, then get the specifics from UserSupervisorSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseSupervisorSecurity = 1

		INNER JOIN dbo.UserSupervisorSecurity AS USS 
		ON USS.CompanyID = e.CompanyID 
		AND USS.ClientID = e.ClientID 
		AND USS.EmployeeID = e.EmployeeID
	WHERE 
		eth.PayrollRecordType in (4,5)

UNION

	-- Get Section 2 - FICA SS
	SELECT 
		h.CompanyID, 
		ISNULL(e.ClientID, SUBSTRING(h.EmployeeID, 3 ,3)) ClientID, 
		h.EmployeeID, 
		h.PaymentAdjustmentNumber, 
		h.CheckNumber, 
		h.CheckDate, 
		'FICA SS' [Tax Code], 
		'FICA SS' [Tax Code Description], 
		h.EmployerFICASSWithholding
	FROM 
		EmployeeCheckHistory h
	
		LEFT OUTER JOIN InvoicePayrolls cip 
		ON cip.CompanyID = h.CompanyID 
		and cip.AuditControlCode = h.AuditControlCode
	
		LEFT OUTER JOIN Invoices i 
		ON i.CompanyID = cip.CompanyID 
		and i.ClientID = cip.ClientID 
		and i.InvoiceNumber = cip.InvoiceNumber
	
		LEFT OUTER JOIN Employees e 
		ON e.CompanyID = h.CompanyID 
		AND e.EmployeeID = h.EmployeeID

		-- If supervisor security is turned on for the client, then get the specifics from UserSupervisorSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseSupervisorSecurity = 1

		INNER JOIN dbo.UserSupervisorSecurity AS USS 
		ON USS.CompanyID = e.CompanyID 
		AND USS.ClientID = e.ClientID 
		AND USS.EmployeeID = e.EmployeeID

UNION

	-- Get Section 2 - FICA Med
	SELECT 
		h.CompanyID, 
		ISNULL(e.ClientID, SUBSTRING(h.EmployeeID, 3 ,3)) ClientID, 
		h.EmployeeID, 
		h.PaymentAdjustmentNumber, 
		h.CheckNumber, 
		h.CheckDate, 
		'FICA Med' [Description], 
		'FICA Med' [Tax Code Description], 
		h.EmployerFICAMWithholding
	FROM 
		EmployeeCheckHistory h
	
		LEFT OUTER JOIN InvoicePayrolls cip 
		ON cip.CompanyID = h.CompanyID 
		and cip.AuditControlCode = h.AuditControlCode
	
		LEFT OUTER JOIN Invoices i 
		ON i.CompanyID = cip.CompanyID 
		and i.ClientID = cip.ClientID 
		and i.InvoiceNumber = cip.InvoiceNumber
	
		LEFT OUTER JOIN Employees e 
		ON e.CompanyID = h.CompanyID 
		AND e.EmployeeID = h.EmployeeID

		-- If supervisor security is turned on for the client, then get the specifics from UserSupervisorSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseSupervisorSecurity = 1

		INNER JOIN dbo.UserSupervisorSecurity AS USS 
		ON USS.CompanyID = e.CompanyID 
		AND USS.ClientID = e.ClientID 
		AND USS.EmployeeID = e.EmployeeID

UNION

	-- Get Section 2 - Federal Tax
	select 
		h.CompanyID, 
		ISNULL(e.ClientID, SUBSTRING(h.EmployeeID, 3 ,3)) ClientID, 
		h.EmployeeID, 
		h.PaymentAdjustmentNumber, 
		h.CheckNumber, 
		h.CheckDate, 
		'Federal' [Tax Code], 
		'Federal' [Tax Code Description], 
		h.FederalWithholdingPayRun
	from 
		EmployeeCheckHistory h
	
		LEFT OUTER JOIN InvoicePayrolls cip 
		ON cip.CompanyID = h.CompanyID 
		and cip.AuditControlCode = h.AuditControlCode
	
		LEFT OUTER JOIN Invoices i 
		ON i.CompanyID = cip.CompanyID 
		and i.ClientID = cip.ClientID 
		and i.InvoiceNumber = cip.InvoiceNumber
	
		LEFT OUTER JOIN Employees e 
		ON e.CompanyID = h.CompanyID 
		AND e.EmployeeID = h.EmployeeID

		-- If supervisor security is turned on for the client, then get the specifics from UserSupervisorSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseSupervisorSecurity = 1

		INNER JOIN dbo.UserSupervisorSecurity AS USS 
		ON USS.CompanyID = e.CompanyID 
		AND USS.ClientID = e.ClientID 
		AND USS.EmployeeID = e.EmployeeID


