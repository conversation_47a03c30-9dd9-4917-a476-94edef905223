

CREATE VIEW [dbo].[vw_EmployeeStateTaxesByDepartmentSecuritySettings]
AS
SELECT 
	est.CompanyID, 
	ISNULL(e.ClientID, SUBSTRING(est.EmployeeID, 3 ,3)) ClientID,
	est.EmployeeID, 
	est.StateCode, 
	est.TaxFilingStatus, 
	est.Dependents, 
	est.AdditionalAllowances,
	est.AdditionalStateWithholding, 
	est.EstimatedStateWithholding,
	UDDS.UserID
FROM 
	EmployeeStateTaxes est
	
		LEFT OUTER JOIN Employees e 
		ON e.CompanyID = est.CompanyID 
		AND e.EmployeeID = est.EmployeeID

		-- If department security is turned on for the client, then get the specifics from UserDeptDivSecurity
		INNER JOIN DarwinetSetup AS DS
		ON DS.CompanyID = e.CompanyID
		AND DS.ClientID = e.ClientID
		AND DS.UseDepartmentSecurity = 1

		INNER JOIN dbo.UserDeptDivSecurity AS UDDS 
		ON UDDS.CompanyID = e.CompanyID 
		AND UDDS.ClientID = e.ClientID 
		AND (UDDS.AllAccess = 1 OR UDDS.EmployeeAccess = 1) 
		AND UDDS.[Enable] = 1
		AND UDDS.Department = e.Department


