using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Extensions;
using System;
using System.Linq;
using System.Web.Mvc;
using System.Web.Routing;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Filters
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class RequireBackOfficeAccessAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            var dbContext = DependencyResolver.Current.GetService<DnetEntities>();
            var companyID = GlobalVariables.CompanyID;
            var clientID = GlobalVariables.Client;

            if (Int32.TryParse(filterContext.ActionParameters.FirstOrDefault(x => x.Key.ToLower() == "companyid").Value?.ToString(), out int companyId)) companyID = companyId;

            var clientIDParameter = filterContext.ActionParameters.FirstOrDefault(x => x.Key.ToLower() == "clientid");
            if (clientIDParameter.Key != null && clientIDParameter.Value != null) clientID = clientIDParameter.Value.ToString();

            var payrollNumberParameter = filterContext.ActionParameters.FirstOrDefault(x => x.Key.ToLower() == "payrollnumber");
            if (payrollNumberParameter.Key != null && payrollNumberParameter.Value != null && payrollNumberParameter.Value.ToString() != "")
            {
                PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumberParameter.Value.ToString());
                companyID = oPayrollNumber.CompanyId;
                clientID = oPayrollNumber.ClientId;
            }

            if (!GlobalVariables.HasPayrollAccess)
            {
                filterContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { controller = "NoAccess", action = "Index" }));
                return;
            }
            base.OnActionExecuting(filterContext);
        }
    }
}