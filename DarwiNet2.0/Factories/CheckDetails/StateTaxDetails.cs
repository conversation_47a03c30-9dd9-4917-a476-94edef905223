using DarwiNet2._0.Factories.CheckDetails.Interfaces;
using DarwiNet2._0.Models.API;
using DarwiNet2._0.Models.API.Interfaces;

using DarwiNet2._0.Providers.API;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Factories.CheckDetails
{
    public class StateTaxDetails : AbstractCheckDetails, ICheckDetails
    {
        public StateTaxDetails(EmployeeCheckHistory check, ICodeDescriptionsProvider codeDescriptionsProvider)
        {
            Check = check;
            CodeDescriptionsProvider = codeDescriptionsProvider;
            TransactionType = CheckDetailRecordTypes.StateTax;
            CodeDescriptionType = CodeDescriptionTypes.StateTax;
        }

        /// <summary>
        /// Gets all the State Tax Details from the check transaction
        /// </summary>
        /// <returns></returns>
        protected override List<IDetailItemDTO> DetailsFromCheck()
        {
            return CheckTransactions.
                    GroupBy(t => t.PayrollCode).
                    Select(group =>
                        new DetailItemDTO
                        {
                            Code = group.Key,
                            Description = CodeDescription(group.Key),
                            Amount = group.Sum(t => t.TRXAmount ?? 0),
                            YTD = YtdCodeTotal(group.First().PayrollCode, "", ""),
                            Rate = (group.First().PayRate + group.First().ShiftPremium) ?? 0,
                            Hours = group.Sum(t => t.UnitsToPay ?? 0)
                        }
                    ).ToList<IDetailItemDTO>();
        }
        
        /// <summary>
        /// Gets all the state tax codes that have a YTD Summary but are not on the current check
        /// </summary>
        /// <returns></returns>
        protected override List<IDetailItemDTO> ExtraDetailsWithYtdSummary()
        {
            if (!ClientIncludesYtd())
            {
                return new List<IDetailItemDTO>();
            }

            return EmployeeStateTaxes.
                    Where(t => NewCodeToAddToYTDSummary(t)).
                    Select(t =>
                        new DetailItemDTO
                        {
                            Code = t.StateCode,
                            Description = CodeDescription(t.StateCode),
                            Amount = 0,
                            YTD = YtdCodeTotal(t.StateCode, "", ""),
                            Rate = 0,
                            Hours = 0
                        }
                    ).ToList<IDetailItemDTO>();
        }

        /// <summary>
        /// Returns true if this code does not exist on this check's transaction history but does have a YTD
        /// Summary
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        private bool NewCodeToAddToYTDSummary(EmployeeStateTax code)
        {
            return !CheckTransactions.Any(t => t.PayrollCode == code.StateCode) &&
                    YtdTransactions.Any(tyt => tyt.PayrollCode == code.StateCode);
        }

        /// <summary>
        /// Returns true if this client shows ytd info
        /// </summary>
        /// <returns></returns>
        protected override bool ClientIncludesYtd()
        {
            return ClientSetup != null && ClientSetup.CheckYTDStateTaxes;
        }

        protected override bool ClientIncludesDepartment()
        {
            return ClientSetup != null && ClientSetup.CheckShowEarningDepartment;
        }
        protected override bool ClientIncludesPosition()
        {
            return ClientSetup != null && ClientSetup.CheckShowEarningPosition;
        }

        #region Class Properties
        private List<EmployeeStateTax> _employeeStateTaxes;
        private List<EmployeeStateTax> EmployeeStateTaxes
        {
            get
            {
                if (_employeeStateTaxes == null)
                {
                    _employeeStateTaxes = RetrieveEmployeeStateTaxes();
                }

                return _employeeStateTaxes;
            }
        }

        private List<EmployeeStateTax> RetrieveEmployeeStateTaxes()
        {
            var employee = Check.Employee;
            return employee.EmployeeStateTaxes.ToList();
        }
        #endregion
    }
}