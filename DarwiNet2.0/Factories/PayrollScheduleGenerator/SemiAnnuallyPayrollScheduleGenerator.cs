using DarwiNet2._0.DTOs;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Factories.PayrollScheduleGenerator
{
    public class SemiAnnuallyPayrollScheduleGenerator : AbstractPayrollScheduleGenerator
    {
        public SemiAnnuallyPayrollScheduleGenerator(int numberOfSchedules,
            PayrollScheduleDetails templateScheduleDetails, IEnumerable<DayOfWeek> workdays, IEnumerable<DateTime> holidays,
            PayrollScheduleRule holidayRule, PayrollScheduleRule weekendRule) :
            base(numberOfSchedules, templateScheduleDetails, workdays, holidays, holidayRule, weekendRule)
        { }

        /// <summary>
        /// Used to initialize any fields that need to be calculated
        /// </summary>
        /// <history>
        ///     [dkocher]   07/15/20    Task-6347: Add logic to create new payroll schedule and future schedules
        /// </history>
        /// <returns></returns>
        public override PayrollScheduleDetails InitializeInitialScheduleDetails()
        {
            var scheduleDetails = new PayrollScheduleDetails
            {
                InitialCheckDate = TemplateScheduleDetails.InitialCheckDate,
                InitialPayPeriodBeginDate = TemplateScheduleDetails.InitialPayPeriodBeginDate,
                InitialPayPeriodEndDate = TemplateScheduleDetails.InitialPayPeriodBeginDate.AddMonths(6).AddDays(-1),
                InitialReceiveTimesheetDate = TemplateScheduleDetails.InitialReceiveTimesheetDate,
                InitialSendTimesheetDate = TemplateScheduleDetails.InitialSendTimesheetDate,
                InitialProcessDate = TemplateScheduleDetails.InitialProcessDate,
                InitialShipDate = TemplateScheduleDetails.InitialShipDate,
                Inactive = TemplateScheduleDetails.Inactive
            };

            return scheduleDetails;
        }

        /// <summary>
        /// Create initial new schedule dates before any holiday or weekend rules applied.
        /// </summary>
        /// <history>
        ///     [dkocher]   07/15/20    Task-6347: Add logic to create new payroll schedule and future schedules
        /// </history>
        /// <returns></returns>
        public override PayrollScheduleDetails CreateNewScheduleDetails() =>
            new PayrollScheduleDetails
            {
                InitialCheckDate = PreviousScheduleDetails.InitialCheckDate.AddMonths(6),
                InitialPayPeriodBeginDate = PreviousScheduleDetails.InitialPayPeriodBeginDate.AddMonths(6),
                InitialPayPeriodEndDate = PreviousScheduleDetails.InitialPayPeriodBeginDate.AddYears(1).AddDays(-1),
                InitialReceiveTimesheetDate = AddMonths(PreviousScheduleDetails.InitialReceiveTimesheetDate, 6),
                InitialSendTimesheetDate = AddMonths(PreviousScheduleDetails.InitialSendTimesheetDate, 6),
                InitialProcessDate = PreviousScheduleDetails.InitialProcessDate.AddMonths(6),
                InitialShipDate = PreviousScheduleDetails.InitialShipDate.AddMonths(6),
                Inactive = PreviousScheduleDetails.Inactive
            };
    }
}