using DarwiNet2._0.Factories.PayrollBuildValidators;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Data;
using DarwiNet2._0.Providers.D2;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using static DarwiNet2._0.Factories.PayrollBuildValidator;

namespace DarwiNet2._0.Factories
{
    public static class PayrollBuildValidator
    {
        public enum VALIDATOR_TYPE
        {
            CATASTROPHIC = 0,
            PAYROLL_ACCOUNT_SETTINGS = 1,
            EMPLOYEES = 2,
            EMPLOYEE_PAYCODES = 3,
            EMPLOYEE_DEDUCTIONS = 4,
            EMPLOYEE_BENEFITS = 5,
            EMPLOYEE_TAXES = 6,
            EMPLOYEE_PTO = 7,
            EMPLOYEE_DEPARTMENT = 8,
            EMPLOYEE_WORKERS_COMP = 9,
            EMPLOYEE_FMLA_LOA = 10,
            CLIENT = 11
        }
    }

    public class PayrollBuildValidatorFactory
    {
        public static void Validate(VALIDATOR_TYPE type, IPayrollValidationProvider payrollValidationProvider, string userId, int companyId, string clientId, string profileId, string payrollNumber)
        {
            switch (type) 
            {
                case VALIDATOR_TYPE.CATASTROPHIC:
                    new CatastrophicValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.PAYROLL_ACCOUNT_SETTINGS:
                    new PayrollAccountSettingsValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEES:
                    new EmployeesValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_PAYCODES:
                    new EmployeesPayrollCodesValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_DEDUCTIONS:
                    new EmployeesDeductionsValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_BENEFITS:
                    new EmployeesBenefitsValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_TAXES:
                    new EmployeeTaxValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_PTO:
                    new EmployeesPTOValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_DEPARTMENT:
                    new EmployeesDepartmentValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_WORKERS_COMP:
                    new EmployeesWorkersCompValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.EMPLOYEE_FMLA_LOA:
                    new EmployeesFMLALOAValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
                case VALIDATOR_TYPE.CLIENT:
                    new ClientValidator(payrollValidationProvider, userId, companyId, clientId, profileId, payrollNumber).Validate();
                    break;
            }
        }
    }
}