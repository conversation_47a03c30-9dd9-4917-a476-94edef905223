using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RestSharp.Serializers;
using System;

namespace Thinkware.RestSharp.JSON.JsonNet
{
    public class JsonNetSerializer : ISerializer
    {
        private readonly JsonSerializerSettings _settings;

        public string RootElement { get; set; }
        public string Namespace { get; set; }
        public string DateFormat { get; set; }
        public string ContentType { get; set; }

        public JsonNetSerializer(
            Action<JsonNetSerializerSettings> configure)
        {
            var settings = new JsonNetSerializerSettings();
            configure?.Invoke(settings);
            _settings = new JsonSerializerSettings();
            if (settings.UseCamelCasePropertyNames)
                _settings.ContractResolver = new CamelCasePropertyNamesContractResolver();
        }

        public string Serialize(object obj)
        {
            if (obj is null)
                return null;
            return JsonConvert.SerializeObject(obj, _settings);
        }
    }
}
