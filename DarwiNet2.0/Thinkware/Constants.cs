using System.Collections.Generic;

namespace Thinkware.Pay360
{
    /// <summary>
    /// Contains constant Pay360 variables.
    /// </summary>
    public static class Constants
    {
        public static class AutoPayOptions
        {
            public const string AUTO_PROCESS_FINALIZE = "AutoProcessFinalize";
            public const string AUTO_PROCESS_INVOICE = "AutoProcessInvoice";
            public const string AUTO_PROCESS_PAYROLL = "AutoProcessPayroll";
            public const string BYPASS_INVOICE = "BypassInvoice";
            //public const string FINALIZE_APPROVAL_REQUIRED = "FinalizeApprovalRequired";
            public const string INVOICE_APPROVAL_REQUIRED = "InvoiceApprovalRequired";
            public const string PAYROLL_APPROVAL_REQUIRED = "PayrollApprovalRequired";
            public const string STOP_ON_WARNINGS = "StopOnWarnings";
            public const string STOP_ON_BUILD = "StopOnBuild";
        }

        public const string CONTROLLER_ACTION_CREATE = "Create";
        public const string CONTROLLER_ACTION_DELETE = "Delete";
        public const string CONTROLLER_ACTION_EDIT = "Edit";
        public const string CONTROLLER_ACTION_INDEX = "Index";

        public const string DEFAULT_USER_SYSTEM = "System";

        public const string FORMAT_TIME_FULL = "hh:mm tt";
        public const string FORMAT_TIME_HOURS_MINS = "hh:mm";
        public const string FORMAT_TIME_AM_PM = "tt";

        public static class PayrollProfileEmployeeCriteriaTypes
        {
            public const string BENEFITS = "Benefits";
            public const string DEDUCTIONS = "Deductions";
            public const string DEPARTMENT = "Department";
            public const string EECLASS = "EEClass";
            public const string POSITION = "Position";
            public const string WORKSITE = "WorkSite";
            public const string PAYGROUP = "PayGroup";
            public const string LOCATION = "Location";
        }

        public static class PayrollProfileNoteTypes
        {
            public const string PAYROLL = "Payroll";
            public const string INVOICE = "Invoice";
        }

        public static readonly HashSet<string> NO_TAX_STATES = new HashSet<string>(new string[] { "AK", "FL", "NH", "NV", "SD", "TN", "TX", "WA", "WY" });

        public static class EmployeeEnrollmentStatusEnrollmentTypes
        {
            public const string LIFE_EVENT = "Life Event";
            public const string NEW_HIRE = "New Hire";
            public const string OPEN = "Open";
        }

        public static class EmployeeEnrollmentStatusEnrollmentStatuses
        {
            public const string PENDING = "Pending";
            public const string COMPLETED = "Completed";
            public const string REOPENED = "Re-Opened";
            public const string STARTED = "Started";
        }

        public static class PayrollProfileApprovalTypes
        {
            public const string PAYROLL = "Payroll";
            public const string INVOICE = "Invoice";


            public static class PayrollProfilePayrollApprovalTypes
            {
                public const string APPROVE_PAYROLL = "ApprovePayroll";
                public const string APPROVE_INVOICE = "ApproveInvoice";
            }

            public static class PayrollProfileApprovalNotificationTypes
            {
                public const string PAYROLL_CALC_COMPLETED = "PayrollCalcCompleted";
                public const string PAYROLL_APPROVED = "PayrollApproved";
                public const string PAYROLL_FINALIZE_COMPLETED = "PayrollFinalizeCompleted";
                public const string INVOICE_CREATED = "InvoiceCreated";
                public const string INVOICE_APPROVED = "InvoiceApproved";
                public const string INVOICE_FINALIZE_COMPLETED = "InvoiceFinalizeCompleted";
            }
        }

        public class PayrollWorkFutaSutaWCPayrollRecordTypes
        {
            public const byte FUTA = 2;
            public const byte SUTA = 1;
            public const byte WC = 3;
        }
    }
}
