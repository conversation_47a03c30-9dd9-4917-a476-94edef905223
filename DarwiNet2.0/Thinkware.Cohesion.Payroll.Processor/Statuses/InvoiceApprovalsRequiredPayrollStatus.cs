using System.Collections.Generic;

namespace Thinkware.Cohesion.Payroll.Processor
{
    /// <summary>
    /// If approvals are required for an invoice, based on profile setting.
    /// </summary>
    public class InvoiceApprovalsRequiredPayrollStatus : PayrollStatus, IPayrollStatus
    {
        public override string Name => "Invoice Approvals Required";
        public override PayrollStatusCode Code => PayrollStatusCode.InvoiceApprovalsRequired;
        public override string Message => "Invoice approvals required!";
        public override string Description => "If approvals are required for an invoice, based on profile setting.";
        public override PayrollStatusTag Tags => PayrollStatusTag.Payroll | PayrollStatusTag.Invoice;
        public override IEnumerable<PayrollStatusTransition> Transitions => PayrollStatusTransition.None;
    }
}