using System.Collections.Generic;

namespace Thinkware.Cohesion.Payroll.Processor
{
    /// <summary>
    /// The invoice is set to this status if invoice approval is necessary. This status indicates the invoice is waiting approval/review.
    /// </summary>
    public class InvoiceWaitingApprovalPayrollStatus : PayrollStatus, IPayrollStatus
    {
        public override string Name => "Invoice Waiting Approval";
        public override PayrollStatusCode Code => PayrollStatusCode.InvoiceWaitingApproval;
        public override string Message => "Invoice waiting approval...";
        public override string Description => "The invoice is set to this status if invoice approval is necessary. This status indicates the invoice is waiting approval/review.";
        public override PayrollStatusTag Tags => PayrollStatusTag.Payroll | PayrollStatusTag.Invoice;
        public override IEnumerable<PayrollStatusTransition> Transitions => PayrollStatusTransition.None;
    }
}