using System.Collections.Generic;

namespace Thinkware.Cohesion.Payroll.Processor
{
    /// <summary>
    /// The invoice is set to this status when it has been marked as completed by a user.
    /// </summary>
    public class InvoiceCompletedPayrollStatus : PayrollStatus, IPayrollStatus
    {
        public override string Name => "Invoice Completed";
        public override PayrollStatusCode Code => PayrollStatusCode.InvoiceCompleted;
        public override string Message => "Invoice completed!";
        public override string Description => "The invoice is set to this status when it has been marked as completed by a user.";
        public override PayrollStatusTag Tags => PayrollStatusTag.Payroll | PayrollStatusTag.Invoice;
        public override IEnumerable<PayrollStatusTransition> Transitions => PayrollStatusTransition.None;
    }
}