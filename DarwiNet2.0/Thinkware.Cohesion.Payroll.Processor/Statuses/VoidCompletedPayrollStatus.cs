using System.Collections.Generic;

namespace Thinkware.Cohesion.Payroll.Processor
{
    /// <summary>
    /// 
    /// </summary>
    public class VoidCompletedPayrollStatus : PayrollStatus, IPayrollStatus
    {
        public override string Name => "Void Completed";
        public override PayrollStatusCode Code => PayrollStatusCode.VoidCompleted;
        public override string Message => "Void completed!";
        public override string Description => "";
        public override PayrollStatusTag Tags => PayrollStatusTag.Payroll;
        public override IEnumerable<PayrollStatusTransition> Transitions => PayrollStatusTransition.None;
    }
}