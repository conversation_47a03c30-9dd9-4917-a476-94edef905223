namespace Thinkware.Cohesion.Payroll.Processor
{
    /// <summary>
    /// When the schedule is created, the first record is set to Available.
    /// Then, when the payroll entry ahead of it completes, the next one becomes Available.
    /// Available payrolls are the payrolls that can be processed.
    /// </summary>
    [PayrollStatus(
        Name = "Available",
        Code = PayrollStatusCode.Available,
        Description = "When the schedule is created, the first record is set to Available. Then, when the payroll entry ahead of it completes, the next one becomes Available. Available payrolls are the payrolls that can be processed.")]
    public class AvailablePayrollStatus : PayrollStatus
    {
        //Transition.To<Validating>().OnEvent<Validating>().Always();
    }
}
