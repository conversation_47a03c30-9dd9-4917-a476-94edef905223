using System.Collections.Generic;

namespace Thinkware.Cohesion.Payroll.Processor
{
    /// <summary>
    /// The payroll is set to this status if the build process creates warnings in the processing.
    /// </summary>
    public class BuildWarningsExistPayrollStatus : PayrollStatus, IPayrollStatus
    {
        public override string Name => "Build Warnings Exist";
        public override PayrollStatusCode Code => PayrollStatusCode.BuildWarningsExist;
        public override string Message => "The payroll is set to this status if the build process creates warnings in the processing.";
        public override string Description => "The payroll is set to this status if the build process creates warnings in the processing.";
        public override PayrollStatusTag Tags => PayrollStatusTag.Payroll;
        public override IEnumerable<PayrollStatusTransition> Transitions => PayrollStatusTransition.None;
    }
}