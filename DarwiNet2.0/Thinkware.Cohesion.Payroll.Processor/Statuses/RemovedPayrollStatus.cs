using System.Collections.Generic;

namespace Thinkware.Cohesion.Payroll.Processor
{
    /// <summary>
    /// This will be used if the user removes a payroll and wants to “remove” the entry from the schedule. We won’t remove it, just mark it with this status. This will be done in the Finalize step or on the Payroll Cockpit.
    /// </summary>
    public class RemovedPayrollStatus : PayrollStatus, IPayrollStatus
    {
        public override string Name => "Removed";
        public override PayrollStatusCode Code => PayrollStatusCode.Removed;
        public override string Message => "Removed payroll!";
        public override string Description => "This will be used if the user removes a payroll and wants to “remove” the entry from the schedule. We won’t remove it, just mark it with this status. This will be done in the Finalize step or on the Payroll Cockpit.";
        public override PayrollStatusTag Tags => PayrollStatusTag.Payroll;
        public override IEnumerable<PayrollStatusTransition> Transitions => PayrollStatusTransition.None;
    }
}