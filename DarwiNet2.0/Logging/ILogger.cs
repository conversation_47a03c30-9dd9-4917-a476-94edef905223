using System;

namespace DarwiNet2._0.Logging
{
    /// <summary>
    ///     Interface for a logger.
    /// </summary>
    /// <history>
    ///     [mframe]    03/16/20    Task-5812: Created history.
    /// </history>
    public interface ILogger
    {
        void LogError(Exception ex, params object[] args);
        void LogCritical(Exception ex, params object[] args);
        void LogInformation(params object[] args);
        void LogWarning(params object[] args);
    }
}
