tinymce.PluginManager.add("image",function(a){function b(a,b){function c(a,c){d.parentNode&&d.parentNode.removeChild(d),b({width:a,height:c})}var d=document.createElement("img");d.onload=function(){c(Math.max(d.width,d.clientWidth),Math.max(d.height,d.clientHeight))},d.onerror=function(){c()};var e=d.style;e.visibility="hidden",e.position="fixed",e.bottom=e.left=0,e.width=e.height="auto",document.body.appendChild(d),d.src=a}function c(a,b,c){function d(a,c){return c=c||[],tinymce.each(a,function(a){var e={text:a.text||a.title};a.menu?e.menu=d(a.menu):(e.value=a.value,b(e)),c.push(e)}),c}return d(a,c||[])}function d(b){return function(){var c=a.settings.image_list;"string"==typeof c?tinymce.util.XHR.send({url:c,success:function(a){b(tinymce.util.JSON.parse(a))}}):"function"==typeof c?c(b):b(c)}}function e(d){function e(){var a,b,c,d;a=l.find("#width")[0],b=l.find("#height")[0],a&&b&&(c=a.value(),d=b.value(),l.find("#constrain")[0].checked()&&m&&n&&c&&d&&(m!=c?(d=Math.round(c/m*d),isNaN(d)||b.value(d)):(c=Math.round(d/n*c),isNaN(c)||a.value(c))),m=c,n=d)}function f(){function b(b){function c(){b.onload=b.onerror=null,a.selection&&(a.selection.select(b),a.nodeChanged())}b.onload=function(){q.width||q.height||!t||r.setAttribs(b,{width:b.clientWidth,height:b.clientHeight}),c()},b.onerror=c}j(),e(),q=tinymce.extend(q,l.toJSON()),q.alt||(q.alt=""),q.title||(q.title=""),""===q.width&&(q.width=null),""===q.height&&(q.height=null),q.style||(q.style=null),q={src:q.src,alt:q.alt,title:q.title,width:q.width,height:q.height,style:q.style,"class":q["class"]},a.undoManager.transact(function(){return q.src?(""===q.title&&(q.title=null),s?r.setAttribs(s,q):(q.id="__mcenew",a.focus(),a.selection.setContent(r.createHTML("img",q)),s=r.get("__mcenew"),r.setAttrib(s,"id",null)),void b(s)):void(s&&(r.remove(s),a.focus(),a.nodeChanged()))})}function g(a){return a&&(a=a.replace(/px$/,"")),a}function h(c){var d,e,f,g=c.meta||{};o&&o.value(a.convertURL(this.value(),"src")),tinymce.each(g,function(a,b){l.find("#"+b).value(a)}),g.width||g.height||(d=a.convertURL(this.value(),"src"),e=a.settings.image_prepend_url,f=new RegExp("^(?:[a-z]+:)?//","i"),e&&!f.test(d)&&d.substring(0,e.length)!==e&&(d=e+d),this.value(d),b(a.documentBaseURI.toAbsolute(this.value()),function(a){a.width&&a.height&&t&&(m=a.width,n=a.height,l.find("#width").value(m),l.find("#height").value(n))}))}function i(a){if(a.margin){var b=a.margin.split(" ");switch(b.length){case 1:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[0],a["margin-bottom"]=a["margin-bottom"]||b[0],a["margin-left"]=a["margin-left"]||b[0];break;case 2:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[0],a["margin-left"]=a["margin-left"]||b[1];break;case 3:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[2],a["margin-left"]=a["margin-left"]||b[1];break;case 4:a["margin-top"]=a["margin-top"]||b[0],a["margin-right"]=a["margin-right"]||b[1],a["margin-bottom"]=a["margin-bottom"]||b[2],a["margin-left"]=a["margin-left"]||b[3]}delete a.margin}return a}function j(){function b(a){return a.length>0&&/^[0-9]+$/.test(a)&&(a+="px"),a}if(a.settings.image_advtab){var c=l.toJSON(),d=r.parseStyle(c.style);d=i(d),c.vspace&&(d["margin-top"]=d["margin-bottom"]=b(c.vspace)),c.hspace&&(d["margin-left"]=d["margin-right"]=b(c.hspace)),c.border&&(d["border-width"]=b(c.border)),l.find("#style").value(r.serializeStyle(r.parseStyle(r.serializeStyle(d))))}}function k(){if(a.settings.image_advtab){var b=l.toJSON(),c=r.parseStyle(b.style);l.find("#vspace").value(""),l.find("#hspace").value(""),c=i(c),(c["margin-top"]&&c["margin-bottom"]||c["margin-right"]&&c["margin-left"])&&(l.find("#vspace").value(c["margin-top"]===c["margin-bottom"]?g(c["margin-top"]):""),l.find("#hspace").value(c["margin-right"]===c["margin-left"]?g(c["margin-right"]):"")),c["border-width"]&&l.find("#border").value(g(c["border-width"])),l.find("#style").value(r.serializeStyle(r.parseStyle(r.serializeStyle(c))))}}var l,m,n,o,p,q={},r=a.dom,s=a.selection.getNode(),t=a.settings.image_dimensions!==!1;m=r.getAttrib(s,"width"),n=r.getAttrib(s,"height"),"IMG"!=s.nodeName||s.getAttribute("data-mce-object")||s.getAttribute("data-mce-placeholder")?s=null:q={src:r.getAttrib(s,"src"),alt:r.getAttrib(s,"alt"),title:r.getAttrib(s,"title"),"class":r.getAttrib(s,"class"),width:m,height:n},d&&(o={type:"listbox",label:"Image list",values:c(d,function(b){b.value=a.convertURL(b.value||b.url,"src")},[{text:"None",value:""}]),value:q.src&&a.convertURL(q.src,"src"),onselect:function(a){var b=l.find("#alt");(!b.value()||a.lastControl&&b.value()==a.lastControl.text())&&b.value(a.control.text()),l.find("#src").value(a.control.value()).fire("change")},onPostRender:function(){o=this}}),a.settings.image_class_list&&(p={name:"class",type:"listbox",label:"Class",values:c(a.settings.image_class_list,function(b){b.value&&(b.textStyle=function(){return a.formatter.getCssText({inline:"img",classes:[b.value]})})})});var u=[{name:"src",type:"filepicker",filetype:"image",label:"Source",autofocus:!0,onchange:h},o];a.settings.image_description!==!1&&u.push({name:"alt",type:"textbox",label:"Image description"}),a.settings.image_title&&u.push({name:"title",type:"textbox",label:"Image Title"}),t&&u.push({type:"container",label:"Dimensions",layout:"flex",direction:"row",align:"center",spacing:5,items:[{name:"width",type:"textbox",maxLength:5,size:3,onchange:e,ariaLabel:"Width"},{type:"label",text:"x"},{name:"height",type:"textbox",maxLength:5,size:3,onchange:e,ariaLabel:"Height"},{name:"constrain",type:"checkbox",checked:!0,text:"Constrain proportions"}]}),u.push(p),a.settings.image_advtab?(s&&(s.style.marginLeft&&s.style.marginRight&&s.style.marginLeft===s.style.marginRight&&(q.hspace=g(s.style.marginLeft)),s.style.marginTop&&s.style.marginBottom&&s.style.marginTop===s.style.marginBottom&&(q.vspace=g(s.style.marginTop)),s.style.borderWidth&&(q.border=g(s.style.borderWidth)),q.style=a.dom.serializeStyle(a.dom.parseStyle(a.dom.getAttrib(s,"style")))),l=a.windowManager.open({title:"Insert/edit image",data:q,bodyType:"tabpanel",body:[{title:"General",type:"form",items:u},{title:"Advanced",type:"form",pack:"start",items:[{label:"Style",name:"style",type:"textbox",onchange:k},{type:"form",layout:"grid",packV:"start",columns:2,padding:0,alignH:["left","right"],defaults:{type:"textbox",maxWidth:50,onchange:j},items:[{label:"Vertical space",name:"vspace"},{label:"Horizontal space",name:"hspace"},{label:"Border",name:"border"}]}]}],onSubmit:f})):l=a.windowManager.open({title:"Insert/edit image",data:q,body:u,onSubmit:f})}a.addButton("image",{icon:"image",tooltip:"Insert/edit image",onclick:d(e),stateSelector:"img:not([data-mce-object],[data-mce-placeholder])"}),a.addMenuItem("image",{icon:"image",text:"Insert/edit image",onclick:d(e),context:"insert",prependToContext:!0}),a.addCommand("mceImage",d(e))});