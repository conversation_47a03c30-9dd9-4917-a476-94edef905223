/**
 * @preserve jQuery DateTimePicker plugin v2.4.5
 * @homepage http://xdsoft.net/jqplugins/datetimepicker/
 * (c) 2014, Chupurnov Valeriy.
 */
function HighlightedDate(n,t,i){"use strict";this.date=n;this.desc=t;this.style=i}(function(n){"use strict";var t={i18n:{ar:{months:["كانون الثاني","شباط","آذار","نيسان","مايو","حزيران","تموز","آب","أيلول","تشرين الأول","تشرين الثاني","كانون الأول"],dayOfWeek:["ن","ث","ع","خ","ج","س","ح"]},ro:{months:["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie"],dayOfWeek:["l","ma","mi","j","v","s","d"]},id:{months:["<PERSON>uari","Februari","<PERSON>t","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"],dayOfWeek:["Min","Sen","Sel","Rab","Kam","Jum","Sab"]},is:{months:["Janúar","Febrúar","Mars","Apríl","Maí","Júní","Júlí","Ágúst","September","Október","Nóvember","Desember"],dayOfWeek:["Sun","Mán","Þrið","Mið","Fim","Fös","Lau"]},bg:{months:["Януари","Февруари","Март","Април","Май","Юни","Юли","Август","Септември","Октомври","Ноември","Декември"],dayOfWeek:["Нд","Пн","Вт","Ср","Чт","Пт","Сб"]},fa:{months:["فروردین","اردیبهشت","خرداد","تیر","مرداد","شهریور","مهر","آبان","آذر","دی","بهمن","اسفند"],dayOfWeek:["یکشنبه","دوشنبه","سه شنبه","چهارشنبه","پنجشنبه","جمعه","شنبه"]},ru:{months:["Январь","Февраль","Март","Апрель","Май","Июнь","Июль","Август","Сентябрь","Октябрь","Ноябрь","Декабрь"],dayOfWeek:["Вск","Пн","Вт","Ср","Чт","Пт","Сб"]},uk:{months:["Січень","Лютий","Березень","Квітень","Травень","Червень","Липень","Серпень","Вересень","Жовтень","Листопад","Грудень"],dayOfWeek:["Ндл","Пнд","Втр","Срд","Чтв","Птн","Сбт"]},en:{months:["January","February","March","April","May","June","July","August","September","October","November","December"],dayOfWeek:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},el:{months:["Ιανουάριος","Φεβρουάριος","Μάρτιος","Απρίλιος","Μάιος","Ιούνιος","Ιούλιος","Αύγουστος","Σεπτέμβριος","Οκτώβριος","Νοέμβριος","Δεκέμβριος"],dayOfWeek:["Κυρ","Δευ","Τρι","Τετ","Πεμ","Παρ","Σαβ"]},de:{months:["Januar","Februar","März","April","Mai","Juni","Juli","August","September","Oktober","November","Dezember"],dayOfWeek:["So","Mo","Di","Mi","Do","Fr","Sa"]},nl:{months:["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"],dayOfWeek:["zo","ma","di","wo","do","vr","za"]},tr:{months:["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık"],dayOfWeek:["Paz","Pts","Sal","Çar","Per","Cum","Cts"]},fr:{months:["Janvier","Février","Mars","Avril","Mai","Juin","Juillet","Août","Septembre","Octobre","Novembre","Décembre"],dayOfWeek:["Dim","Lun","Mar","Mer","Jeu","Ven","Sam"]},es:{months:["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"],dayOfWeek:["Dom","Lun","Mar","Mié","Jue","Vie","Sáb"]},th:{months:["มกราคม","กุมภาพันธ์","มีนาคม","เมษายน","พฤษภาคม","มิถุนายน","กรกฎาคม","สิงหาคม","กันยายน","ตุลาคม","พฤศจิกายน","ธันวาคม"],dayOfWeek:["อา.","จ.","อ.","พ.","พฤ.","ศ.","ส."]},pl:{months:["styczeń","luty","marzec","kwiecień","maj","czerwiec","lipiec","sierpień","wrzesień","październik","listopad","grudzień"],dayOfWeek:["nd","pn","wt","śr","cz","pt","sb"]},pt:{months:["Janeiro","Fevereiro","Março","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],dayOfWeek:["Dom","Seg","Ter","Qua","Qui","Sex","Sab"]},ch:{months:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],dayOfWeek:["日","一","二","三","四","五","六"]},se:{months:["Januari","Februari","Mars","April","Maj","Juni","Juli","Augusti","September","Oktober","November","December"],dayOfWeek:["Sön","Mån","Tis","Ons","Tor","Fre","Lör"]},kr:{months:["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월"],dayOfWeek:["일","월","화","수","목","금","토"]},it:{months:["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],dayOfWeek:["Dom","Lun","Mar","Mer","Gio","Ven","Sab"]},da:{months:["January","Februar","Marts","April","Maj","Juni","July","August","September","Oktober","November","December"],dayOfWeek:["Søn","Man","Tir","Ons","Tor","Fre","Lør"]},no:{months:["Januar","Februar","Mars","April","Mai","Juni","Juli","August","September","Oktober","November","Desember"],dayOfWeek:["Søn","Man","Tir","Ons","Tor","Fre","Lør"]},ja:{months:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["日","月","火","水","木","金","土"]},vi:{months:["Tháng 1","Tháng 2","Tháng 3","Tháng 4","Tháng 5","Tháng 6","Tháng 7","Tháng 8","Tháng 9","Tháng 10","Tháng 11","Tháng 12"],dayOfWeek:["CN","T2","T3","T4","T5","T6","T7"]},sl:{months:["Januar","Februar","Marec","April","Maj","Junij","Julij","Avgust","September","Oktober","November","December"],dayOfWeek:["Ned","Pon","Tor","Sre","Čet","Pet","Sob"]},cs:{months:["Leden","Únor","Březen","Duben","Květen","Červen","Červenec","Srpen","Září","Říjen","Listopad","Prosinec"],dayOfWeek:["Ne","Po","Út","St","Čt","Pá","So"]},hu:{months:["Január","Február","Március","Április","Május","Június","Július","Augusztus","Szeptember","Október","November","December"],dayOfWeek:["Va","Hé","Ke","Sze","Cs","Pé","Szo"]},az:{months:["Yanvar","Fevral","Mart","Aprel","May","Iyun","Iyul","Avqust","Sentyabr","Oktyabr","Noyabr","Dekabr"],dayOfWeek:["B","Be","Ça","Ç","Ca","C","Ş"]},bs:{months:["Januar","Februar","Mart","April","Maj","Jun","Jul","Avgust","Septembar","Oktobar","Novembar","Decembar"],dayOfWeek:["Ned","Pon","Uto","Sri","Čet","Pet","Sub"]},ca:{months:["Gener","Febrer","Març","Abril","Maig","Juny","Juliol","Agost","Setembre","Octubre","Novembre","Desembre"],dayOfWeek:["Dg","Dl","Dt","Dc","Dj","Dv","Ds"]},"en-GB":{months:["January","February","March","April","May","June","July","August","September","October","November","December"],dayOfWeek:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},et:{months:["Jaanuar","Veebruar","Märts","Aprill","Mai","Juuni","Juuli","August","September","Oktoober","November","Detsember"],dayOfWeek:["P","E","T","K","N","R","L"]},eu:{months:["Urtarrila","Otsaila","Martxoa","Apirila","Maiatza","Ekaina","Uztaila","Abuztua","Iraila","Urria","Azaroa","Abendua"],dayOfWeek:["Ig.","Al.","Ar.","Az.","Og.","Or.","La."]},fi:{months:["Tammikuu","Helmikuu","Maaliskuu","Huhtikuu","Toukokuu","Kesäkuu","Heinäkuu","Elokuu","Syyskuu","Lokakuu","Marraskuu","Joulukuu"],dayOfWeek:["Su","Ma","Ti","Ke","To","Pe","La"]},gl:{months:["Xan","Feb","Maz","Abr","Mai","Xun","Xul","Ago","Set","Out","Nov","Dec"],dayOfWeek:["Dom","Lun","Mar","Mer","Xov","Ven","Sab"]},hr:{months:["Siječanj","Veljača","Ožujak","Travanj","Svibanj","Lipanj","Srpanj","Kolovoz","Rujan","Listopad","Studeni","Prosinac"],dayOfWeek:["Ned","Pon","Uto","Sri","Čet","Pet","Sub"]},ko:{months:["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월"],dayOfWeek:["일","월","화","수","목","금","토"]},lt:{months:["Sausio","Vasario","Kovo","Balandžio","Gegužės","Birželio","Liepos","Rugpjūčio","Rugsėjo","Spalio","Lapkričio","Gruodžio"],dayOfWeek:["Sek","Pir","Ant","Tre","Ket","Pen","Šeš"]},lv:{months:["Janvāris","Februāris","Marts","Aprīlis ","Maijs","Jūnijs","Jūlijs","Augusts","Septembris","Oktobris","Novembris","Decembris"],dayOfWeek:["Sv","Pr","Ot","Tr","Ct","Pk","St"]},mk:{months:["јануари","февруари","март","април","мај","јуни","јули","август","септември","октомври","ноември","декември"],dayOfWeek:["нед","пон","вто","сре","чет","пет","саб"]},mn:{months:["1-р сар","2-р сар","3-р сар","4-р сар","5-р сар","6-р сар","7-р сар","8-р сар","9-р сар","10-р сар","11-р сар","12-р сар"],dayOfWeek:["Дав","Мяг","Лха","Пүр","Бсн","Бям","Ням"]},"pt-BR":{months:["Janeiro","Fevereiro","Março","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro"],dayOfWeek:["Dom","Seg","Ter","Qua","Qui","Sex","Sáb"]},sk:{months:["Január","Február","Marec","Apríl","Máj","Jún","Júl","August","September","Október","November","December"],dayOfWeek:["Ne","Po","Ut","St","Št","Pi","So"]},sq:{months:["January","February","March","April","May","June","July","August","September","October","November","December"],dayOfWeek:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},"sr-YU":{months:["Januar","Februar","Mart","April","Maj","Jun","Jul","Avgust","Septembar","Oktobar","Novembar","Decembar"],dayOfWeek:["Ned","Pon","Uto","Sre","čet","Pet","Sub"]},sr:{months:["јануар","фебруар","март","април","мај","јун","јул","август","септембар","октобар","новембар","децембар"],dayOfWeek:["нед","пон","уто","сре","чет","пет","суб"]},sv:{months:["Januari","Februari","Mars","April","Maj","Juni","Juli","Augusti","September","Oktober","November","December"],dayOfWeek:["Sön","Mån","Tis","Ons","Tor","Fre","Lör"]},"zh-TW":{months:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],dayOfWeek:["日","一","二","三","四","五","六"]},zh:{months:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],dayOfWeek:["日","一","二","三","四","五","六"]},he:{months:["ינואר","פברואר","מרץ","אפריל","מאי","יוני","יולי","אוגוסט","ספטמבר","אוקטובר","נובמבר","דצמבר"],dayOfWeek:["א'","ב'","ג'","ד'","ה'","ו'","שבת"]},hy:{months:["Հունվար","Փետրվար","Մարտ","Ապրիլ","Մայիս","Հունիս","Հուլիս","Օգոստոս","Սեպտեմբեր","Հոկտեմբեր","Նոյեմբեր","Դեկտեմբեր"],dayOfWeek:["Կի","Երկ","Երք","Չոր","Հնգ","Ուրբ","Շբթ"]},kg:{months:["Үчтүн айы","Бирдин айы","Жалган Куран","Чын Куран","Бугу","Кулжа","Теке","Баш Оона","Аяк Оона","Тогуздун айы","Жетинин айы","Бештин айы"],dayOfWeek:["Жек","Дүй","Шей","Шар","Бей","Жум","Ише"]}},value:"",lang:"en",format:"Y/m/d H:i",formatTime:"H:i",formatDate:"Y/m/d",startDate:!1,step:60,monthChangeSpinner:!0,closeOnDateSelect:!1,closeOnTimeSelect:!0,closeOnWithoutClick:!0,closeOnInputClick:!0,timepicker:!0,datepicker:!0,weeks:!1,defaultTime:!1,defaultDate:!1,minDate:!1,maxDate:!1,minTime:!1,maxTime:!1,disabledMinTime:!1,disabledMaxTime:!1,allowTimes:[],opened:!1,initTime:!0,inline:!1,theme:"",onSelectDate:function(){},onSelectTime:function(){},onChangeMonth:function(){},onChangeYear:function(){},onChangeDateTime:function(){},onShow:function(){},onClose:function(){},onGenerate:function(){},withoutCopyright:!0,inverseButton:!1,hours12:!1,next:"xdsoft_next",prev:"xdsoft_prev",dayOfWeekStart:0,parentID:"body",timeHeightInTimePicker:25,timepickerScrollbar:!0,todayButton:!0,prevButton:!0,nextButton:!0,defaultSelect:!0,scrollMonth:!0,scrollTime:!0,scrollInput:!0,lazyInit:!1,mask:!1,validateOnBlur:!0,allowBlank:!0,yearStart:1950,yearEnd:2050,monthStart:0,monthEnd:11,style:"",id:"",fixed:!1,roundTime:"round",className:"",weekends:[],highlightedDates:[],highlightedPeriods:[],disabledDates:[],disabledWeekDays:[],yearOffset:0,beforeShowDay:null,enterLikeTab:!0,showApplyButton:!1};window.getComputedStyle||(window.getComputedStyle=function(n){return this.el=n,this.getPropertyValue=function(t){var i=/(\-([a-z]){1})/g;return t==="float"&&(t="styleFloat"),i.test(t)&&(t=t.replace(i,function(n,t,i){return i.toUpperCase()})),n.currentStyle[t]||null},this});Array.prototype.indexOf||(Array.prototype.indexOf=function(n,t){for(var i=t||0,r=this.length;i<r;i+=1)if(this[i]===n)return i;return-1});Date.prototype.countDaysInMonth=function(){return new Date(this.getFullYear(),this.getMonth()+1,0).getDate()};n.fn.xdsoftScroller=function(t){return this.each(function(){var i=n(this),s=function(n){var t={x:0,y:0},i;return n.type==="touchstart"||n.type==="touchmove"||n.type==="touchend"||n.type==="touchcancel"?(i=n.originalEvent.touches[0]||n.originalEvent.changedTouches[0],t.x=i.clientX,t.y=i.clientY):(n.type==="mousedown"||n.type==="mouseup"||n.type==="mousemove"||n.type==="mouseover"||n.type==="mouseout"||n.type==="mouseenter"||n.type==="mouseleave")&&(t.x=n.clientX,t.y=n.clientY),t},f,u,e,o,r,h=100,c=!1,w=0,a=0,v=0,y=!1,p=0,l=function(){};if(t==="hide"){i.find(".xdsoft_scrollbar").hide();return}if(!n(this).hasClass("xdsoft_scroller_box")){f=i.children().eq(0);u=i[0].clientHeight;e=f[0].offsetHeight;o=n('<div class="xdsoft_scrollbar"><\/div>');r=n('<div class="xdsoft_scroller"><\/div>');o.append(r);i.addClass("xdsoft_scroller_box").append(o);l=function(n){var t=s(n).y-w+p;t<0&&(t=0);t+r[0].offsetHeight>v&&(t=v-r[0].offsetHeight);i.trigger("scroll_element.xdsoft_scroller",[h?t/h:0])};r.on("touchstart.xdsoft_scroller mousedown.xdsoft_scroller",function(f){if(u||i.trigger("resize_scroll.xdsoft_scroller",[t]),w=s(f).y,p=parseInt(r.css("margin-top"),10),v=o[0].offsetHeight,f.type==="mousedown"){document&&n(document.body).addClass("xdsoft_noselect");n([document.body,window]).on("mouseup.xdsoft_scroller",function e(){n([document.body,window]).off("mouseup.xdsoft_scroller",e).off("mousemove.xdsoft_scroller",l).removeClass("xdsoft_noselect")});n(document.body).on("mousemove.xdsoft_scroller",l)}else y=!0,f.stopPropagation(),f.preventDefault()}).on("touchmove",function(n){y&&(n.preventDefault(),l(n))}).on("touchend touchcancel",function(){y=!1;p=0});i.on("scroll_element.xdsoft_scroller",function(n,t){u||i.trigger("resize_scroll.xdsoft_scroller",[t,!0]);t=t>1?1:t<0||isNaN(t)?0:t;r.css("margin-top",h*t);setTimeout(function(){f.css("marginTop",-parseInt((f[0].offsetHeight-u)*t,10))},10)}).on("resize_scroll.xdsoft_scroller",function(n,t,s){var c,l;u=i[0].clientHeight;e=f[0].offsetHeight;c=u/e;l=c*o[0].offsetHeight;c>1?r.hide():(r.show(),r.css("height",parseInt(l>10?l:10,10)),h=o[0].offsetHeight-r[0].offsetHeight,s!==!0&&i.trigger("scroll_element.xdsoft_scroller",[t||Math.abs(parseInt(f.css("marginTop"),10))/(e-u)]))});i.on("mousewheel",function(n){var t=Math.abs(parseInt(f.css("marginTop"),10));return t=t-n.deltaY*20,t<0&&(t=0),i.trigger("scroll_element.xdsoft_scroller",[t/(e-u)]),n.stopPropagation(),!1});i.on("touchstart",function(n){c=s(n);a=Math.abs(parseInt(f.css("marginTop"),10))});i.on("touchmove",function(n){if(c){n.preventDefault();var t=s(n);i.trigger("scroll_element.xdsoft_scroller",[(a-(t.y-c.y))/(e-u)])}});i.on("touchend touchcancel",function(){c=!1;a=0})}i.trigger("resize_scroll.xdsoft_scroller",[t])})};n.fn.datetimepicker=function(i){var h=48,w=57,c=96,l=105,e=17,u=46,a=13,b=27,f=8,k=37,d=38,g=39,nt=40,v=9,tt=116,it=65,rt=67,ut=86,ft=90,et=89,o=!1,r=n.isPlainObject(i)||!i?n.extend(!0,{},t,i):n.extend(!0,{},t),y=0,s,p,ot=function(n){n.on("open.xdsoft focusin.xdsoft mousedown.xdsoft",function t(){n.is(":disabled")||n.data("xdsoft_datetimepicker")||(clearTimeout(y),y=setTimeout(function(){n.data("xdsoft_datetimepicker")||s(n);n.off("open.xdsoft focusin.xdsoft mousedown.xdsoft",t).trigger("open.xdsoft")},100))})};s=function(t){function ni(){var n=!1,i;return r.startDate?n=y.strToDate(r.startDate):(n=r.value||(t&&t.val&&t.val()?t.val():""),n?n=y.strToDateTime(n):r.defaultDate&&(n=y.strToDateTime(r.defaultDate),r.defaultTime&&(i=y.strtotime(r.defaultTime),n.setHours(i.getHours()),n.setMinutes(i.getMinutes())))),n&&y.isValidDate(n)?s.data("changed",!0):n="",n||0}var s=n('<div class="xdsoft_datetimepicker xdsoft_noselect"><\/div>'),ii=n('<div class="xdsoft_copyright"><a target="_blank" href="http://xdsoft.net/jqplugins/datetimepicker/">xdsoft.net<\/a><\/div>'),ct=n('<div class="xdsoft_datepicker active"><\/div>'),ot=n('<div class="xdsoft_mounthpicker"><button type="button" class="xdsoft_prev"><\/button><button type="button" class="xdsoft_today_button"><\/button><div class="xdsoft_label xdsoft_month"><span><\/span><i><\/i><\/div><div class="xdsoft_label xdsoft_year"><span><\/span><i><\/i><\/div><button type="button" class="xdsoft_next"><\/button><\/div>'),pt=n('<div class="xdsoft_calendar"><\/div>'),lt=n('<div class="xdsoft_timepicker active"><button type="button" class="xdsoft_prev"><\/button><div class="xdsoft_time_box"><\/div><button type="button" class="xdsoft_next"><\/button><\/div>'),st=lt.find(".xdsoft_time_box").eq(0),p=n('<div class="xdsoft_time_variant"><\/div>'),wt=n('<button type="button" class="xdsoft_save_selected blue-gradient-button">Save Selected<\/button>'),bt=n('<div class="xdsoft_select xdsoft_monthselect"><div><\/div><\/div>'),kt=n('<div class="xdsoft_select xdsoft_yearselect"><div><\/div><\/div>'),at=!1,ti,dt,vt,ht,yt,gt=0,ri=0,y;r.id&&s.attr("id",r.id);r.style&&s.attr("style",r.style);r.weeks&&s.addClass("xdsoft_showweeks");s.addClass("xdsoft_"+r.theme);s.addClass(r.className);ot.find(".xdsoft_month span").after(bt);ot.find(".xdsoft_year span").after(kt);ot.find(".xdsoft_month,.xdsoft_year").on("mousedown.xdsoft",function(t){var i=n(this).find(".xdsoft_select").eq(0),f=0,e=0,o=i.is(":visible"),r,u;for(ot.find(".xdsoft_select").hide(),y.currentTime&&(f=y.currentTime[n(this).hasClass("xdsoft_month")?"getMonth":"getFullYear"]()),i[o?"hide":"show"](),r=i.find("div.xdsoft_option"),u=0;u<r.length;u+=1)if(r.eq(u).data("value")===f)break;else e+=r[0].offsetHeight;return i.xdsoftScroller(e/(i.children()[0].offsetHeight-i[0].clientHeight)),t.stopPropagation(),!1});ot.find(".xdsoft_select").xdsoftScroller().on("mousedown.xdsoft",function(n){n.stopPropagation();n.preventDefault()}).on("mousedown.xdsoft",".xdsoft_option",function(){(y.currentTime===undefined||y.currentTime===null)&&(y.currentTime=y.now());var t=y.currentTime.getFullYear();y&&y.currentTime&&y.currentTime[n(this).parent().parent().hasClass("xdsoft_monthselect")?"setMonth":"setFullYear"](n(this).data("value"));n(this).parent().parent().hide();s.trigger("xchange.xdsoft");r.onChangeMonth&&n.isFunction(r.onChangeMonth)&&r.onChangeMonth.call(s,y.currentTime,s.data("input"));t!==y.currentTime.getFullYear()&&n.isFunction(r.onChangeYear)&&r.onChangeYear.call(s,y.currentTime,s.data("input"))});s.setOptions=function(i){var p={},vt=function(n){try{if(document.selection&&document.selection.createRange){var t=document.selection.createRange();return t.getBookmark().charCodeAt(2)-2}if(n.setSelectionRange)return n.selectionStart}catch(i){return 0}},yt=function(n,t){if(n=typeof n=="string"||n instanceof String?document.getElementById(n):n,!n)return!1;if(n.createTextRange){var i=n.createTextRange();return i.collapse(!0),i.moveEnd("character",t),i.moveStart("character",t),i.select(),!0}return n.setSelectionRange?(n.setSelectionRange(t,t),!0):!1},ht=function(n,t){var i=n.replace(/([\[\]\/\{\}\(\)\-\.\+]{1})/g,"\\$1").replace(/_/g,"{digit+}").replace(/([0-9]{1})/g,"{digit$1}").replace(/\{digit([0-9]{1})\}/g,"[0-$1_]{1}").replace(/\{digit[\+]\}/g,"[0-9_]{1}");return new RegExp(i).test(t)};if(r=n.extend(!0,{},r,i),i.allowTimes&&n.isArray(i.allowTimes)&&i.allowTimes.length&&(r.allowTimes=n.extend(!0,[],i.allowTimes)),i.weekends&&n.isArray(i.weekends)&&i.weekends.length&&(r.weekends=n.extend(!0,[],i.weekends)),i.highlightedDates&&n.isArray(i.highlightedDates)&&i.highlightedDates.length&&(n.each(i.highlightedDates,function(t,i){var o=n.map(i.split(","),n.trim),f,u=new HighlightedDate(Date.parseDate(o[0],r.formatDate),o[1],o[2]),e=u.date.dateFormat(r.formatDate);p[e]!==undefined?(f=p[e].desc,f&&f.length&&u.desc&&u.desc.length&&(p[e].desc=f+"\n"+u.desc)):p[e]=u}),r.highlightedDates=n.extend(!0,[],p)),i.highlightedPeriods&&n.isArray(i.highlightedPeriods)&&i.highlightedPeriods.length&&(p=n.extend(!0,[],r.highlightedDates),n.each(i.highlightedPeriods,function(t,i){for(var o=n.map(i.split(","),n.trim),u=Date.parseDate(o[0],r.formatDate),h=Date.parseDate(o[1],r.formatDate),c=o[2],f,e,s,l=o[3];u<=h;)f=new HighlightedDate(u,c,l),e=u.dateFormat(r.formatDate),u.setDate(u.getDate()+1),p[e]!==undefined?(s=p[e].desc,s&&s.length&&f.desc&&f.desc.length&&(p[e].desc=s+"\n"+f.desc)):p[e]=f}),r.highlightedDates=n.extend(!0,[],p)),i.disabledDates&&n.isArray(i.disabledDates)&&i.disabledDates.length&&(r.disabledDates=n.extend(!0,[],i.disabledDates)),i.disabledWeekDays&&n.isArray(i.disabledWeekDays)&&i.disabledWeekDays.length&&(r.disabledWeekDays=n.extend(!0,[],i.disabledWeekDays)),(r.open||r.opened)&&!r.inline&&t.trigger("open.xdsoft"),r.inline&&(at=!0,s.addClass("xdsoft_inline"),t.after(s).hide()),r.inverseButton&&(r.next="xdsoft_prev",r.prev="xdsoft_next"),r.datepicker?ct.addClass("active"):ct.removeClass("active"),r.timepicker?lt.addClass("active"):lt.removeClass("active"),r.value&&(y.setCurrentTime(r.value),t&&t.val&&t.val(y.str)),r.dayOfWeekStart=isNaN(r.dayOfWeekStart)?0:parseInt(r.dayOfWeekStart,10)%7,r.timepickerScrollbar||st.xdsoftScroller("hide"),r.minDate&&/^-(.*)$/.test(r.minDate)&&(r.minDate=y.strToDateTime(r.minDate).dateFormat(r.formatDate)),r.maxDate&&/^\+(.*)$/.test(r.maxDate)&&(r.maxDate=y.strToDateTime(r.maxDate).dateFormat(r.formatDate)),wt.toggle(r.showApplyButton),ot.find(".xdsoft_today_button").css("visibility",r.todayButton?"visible":"hidden"),ot.find("."+r.prev).css("visibility",r.prevButton?"visible":"hidden"),ot.find("."+r.next).css("visibility",r.nextButton?"visible":"hidden"),r.mask&&(t.off("keydown.xdsoft"),r.mask===!0&&(r.mask=r.format.replace(/Y/g,"9999").replace(/F/g,"9999").replace(/m/g,"19").replace(/d/g,"39").replace(/H/g,"29").replace(/i/g,"59").replace(/s/g,"59")),n.type(r.mask)==="string")){ht(r.mask,t.val())||t.val(r.mask.replace(/[0-9]/g,"_"));t.on("keydown.xdsoft",function(i){var p=this.value,s=i.which,y,ot;if(s>=h&&s<=w||s>=c&&s<=l||s===f||s===u){for(y=vt(this),ot=s!==f&&s!==u?String.fromCharCode(c<=s&&s<=l?s-h:s):"_",(s===f||s===u)&&y&&(y-=1,ot="_");/[^0-9_]/.test(r.mask.substr(y,1))&&y<r.mask.length&&y>0;)y+=s===f||s===u?-1:1;if(p=p.substr(0,y)+ot+p.substr(y+1),n.trim(p)==="")p=r.mask.replace(/[0-9]/g,"_");else if(y===r.mask.length)return i.preventDefault(),!1;for(y+=s===f||s===u?0:1;/[^0-9_]/.test(r.mask.substr(y,1))&&y<r.mask.length&&y>0;)y+=s===f||s===u?-1:1;ht(r.mask,p)?(this.value=p,yt(this,y)):n.trim(p)===""?this.value=r.mask.replace(/[0-9]/g,"_"):t.trigger("error_input.xdsoft")}else if([it,rt,ut,ft,et].indexOf(s)!==-1&&o||[b,d,nt,k,g,tt,e,v,a].indexOf(s)!==-1)return!0;return i.preventDefault(),!1})}if(r.validateOnBlur)t.off("blur.xdsoft").on("blur.xdsoft",function(){if(r.allowBlank&&!n.trim(n(this).val()).length)n(this).val(null),s.data("xdsoft_datetime").empty();else if(Date.parseDate(n(this).val(),r.format))s.data("xdsoft_datetime").setCurrentTime(n(this).val());else{var t=+[n(this).val()[0],n(this).val()[1]].join(""),i=+[n(this).val()[2],n(this).val()[3]].join("");!r.datepicker&&r.timepicker&&t>=0&&t<24&&i>=0&&i<60?n(this).val([t,i].map(function(n){return n>9?n:"0"+n}).join(":")):n(this).val(y.now().dateFormat(r.format));s.data("xdsoft_datetime").setCurrentTime(n(this).val())}s.trigger("changedatetime.xdsoft")});r.dayOfWeekStartPrev=r.dayOfWeekStart===0?6:r.dayOfWeekStart-1;s.trigger("xchange.xdsoft").trigger("afterOpen.xdsoft")};s.data("options",r).on("mousedown.xdsoft",function(n){return n.stopPropagation(),n.preventDefault(),kt.hide(),bt.hide(),!1});st.append(p);st.xdsoftScroller();s.on("afterOpen.xdsoft",function(){st.xdsoftScroller()});s.append(ct).append(lt);r.withoutCopyright!==!0&&s.append(ii);ct.append(ot).append(pt).append(wt);n(r.parentID).append(s);ti=function(){var t=this;t.now=function(n){var i=new Date,u,f;return!n&&r.defaultDate&&(u=t.strToDateTime(r.defaultDate),i.setFullYear(u.getFullYear()),i.setMonth(u.getMonth()),i.setDate(u.getDate())),r.yearOffset&&i.setFullYear(i.getFullYear()+r.yearOffset),!n&&r.defaultTime&&(f=t.strtotime(r.defaultTime),i.setHours(f.getHours()),i.setMinutes(f.getMinutes())),i};t.isValidDate=function(n){return Object.prototype.toString.call(n)!=="[object Date]"?!1:!isNaN(n.getTime())};t.setCurrentTime=function(n){t.currentTime=typeof n=="string"?t.strToDateTime(n):t.isValidDate(n)?n:t.now();s.trigger("xchange.xdsoft")};t.empty=function(){t.currentTime=null};t.getCurrentTime=function(){return t.currentTime};t.nextMonth=function(){(t.currentTime===undefined||t.currentTime===null)&&(t.currentTime=t.now());var i=t.currentTime.getMonth()+1,u;return i===12&&(t.currentTime.setFullYear(t.currentTime.getFullYear()+1),i=0),u=t.currentTime.getFullYear(),t.currentTime.setDate(Math.min(new Date(t.currentTime.getFullYear(),i+1,0).getDate(),t.currentTime.getDate())),t.currentTime.setMonth(i),r.onChangeMonth&&n.isFunction(r.onChangeMonth)&&r.onChangeMonth.call(s,y.currentTime,s.data("input")),u!==t.currentTime.getFullYear()&&n.isFunction(r.onChangeYear)&&r.onChangeYear.call(s,y.currentTime,s.data("input")),s.trigger("xchange.xdsoft"),i};t.prevMonth=function(){(t.currentTime===undefined||t.currentTime===null)&&(t.currentTime=t.now());var i=t.currentTime.getMonth()-1;return i===-1&&(t.currentTime.setFullYear(t.currentTime.getFullYear()-1),i=11),t.currentTime.setDate(Math.min(new Date(t.currentTime.getFullYear(),i+1,0).getDate(),t.currentTime.getDate())),t.currentTime.setMonth(i),r.onChangeMonth&&n.isFunction(r.onChangeMonth)&&r.onChangeMonth.call(s,y.currentTime,s.data("input")),s.trigger("xchange.xdsoft"),i};t.getWeekOfYear=function(n){var t=new Date(n.getFullYear(),0,1);return Math.ceil(((n-t)/864e5+t.getDay()+1)/7)};t.strToDateTime=function(n){var i=[],f,u;return n&&n instanceof Date&&t.isValidDate(n)?n:(i=/^(\+|\-)(.*)$/.exec(n),i&&(i[2]=Date.parseDate(i[2],r.formatDate)),i&&i[2]?(f=i[2].getTime()-i[2].getTimezoneOffset()*6e4,u=new Date(t.now(!0).getTime()+parseInt(i[1]+"1",10)*f)):u=n?Date.parseDate(n,r.format):t.now(),t.isValidDate(u)||(u=t.now()),u)};t.strToDate=function(n){if(n&&n instanceof Date&&t.isValidDate(n))return n;var i=n?Date.parseDate(n,r.formatDate):t.now(!0);return t.isValidDate(i)||(i=t.now(!0)),i};t.strtotime=function(n){if(n&&n instanceof Date&&t.isValidDate(n))return n;var i=n?Date.parseDate(n,r.formatTime):t.now(!0);return t.isValidDate(i)||(i=t.now(!0)),i};t.str=function(){return t.currentTime.dateFormat(r.format)};t.currentTime=this.now()};y=new ti;wt.on("click",function(n){n.preventDefault();s.data("changed",!0);y.setCurrentTime(ni());t.val(y.str());s.trigger("close.xdsoft")});ot.find(".xdsoft_today_button").on("mousedown.xdsoft",function(){s.data("changed",!0);y.setCurrentTime(0);s.trigger("afterOpen.xdsoft")}).on("dblclick.xdsoft",function(){var n=y.getCurrentTime(),i,u;(n=new Date(n.getFullYear(),n.getMonth(),n.getDate()),i=y.strToDate(r.minDate),i=new Date(i.getFullYear(),i.getMonth(),i.getDate()),n<i)||(u=y.strToDate(r.maxDate),u=new Date(u.getFullYear(),u.getMonth(),u.getDate()),n>u)||(t.val(y.str()),s.trigger("close.xdsoft"))});ot.find(".xdsoft_prev,.xdsoft_next").on("mousedown.xdsoft",function(){var t=n(this),i=0,u=!1;(function f(n){t.hasClass(r.next)?y.nextMonth():t.hasClass(r.prev)&&y.prevMonth();r.monthChangeSpinner&&(u||(i=setTimeout(f,n||100)))})(500);n([document.body,window]).on("mouseup.xdsoft",function e(){clearTimeout(i);u=!0;n([document.body,window]).off("mouseup.xdsoft",e)})});lt.find(".xdsoft_prev,.xdsoft_next").on("mousedown.xdsoft",function(){var i=n(this),u=0,f=!1,t=110;(function e(n){var s=st[0].clientHeight,h=p[0].offsetHeight,o=Math.abs(parseInt(p.css("marginTop"),10));i.hasClass(r.next)&&h-s-r.timeHeightInTimePicker>=o?p.css("marginTop","-"+(o+r.timeHeightInTimePicker)+"px"):i.hasClass(r.prev)&&o-r.timeHeightInTimePicker>=0&&p.css("marginTop","-"+(o-r.timeHeightInTimePicker)+"px");st.trigger("scroll_element.xdsoft_scroller",[Math.abs(parseInt(p.css("marginTop"),10)/(h-s))]);t=t>10?10:t-10;f||(u=setTimeout(e,n||t))})(500);n([document.body,window]).on("mouseup.xdsoft",function o(){clearTimeout(u);f=!0;n([document.body,window]).off("mouseup.xdsoft",o)})});dt=0;s.on("xchange.xdsoft",function(t){clearTimeout(dt);dt=setTimeout(function(){(y.currentTime===undefined||y.currentTime===null)&&(y.currentTime=y.now());for(var e="",t=new Date(y.currentTime.getFullYear(),y.currentTime.getMonth(),1,12,0,0),u=0,o,k=y.now(),c=!1,l=!1,v,it,b,rt,h,ut,f=[],a,d=!0,g="",w="",nt,tt;t.getDay()!==r.dayOfWeekStart;)t.setDate(t.getDate()-1);for(e+="<table><thead><tr>",r.weeks&&(e+="<th><\/th>"),o=0;o<7;o+=1)e+="<th>"+r.i18n[r.lang].dayOfWeek[(o+r.dayOfWeekStart)%7]+"<\/th>";for(e+="<\/tr><\/thead>",e+="<tbody>",r.maxDate!==!1&&(c=y.strToDate(r.maxDate),c=new Date(c.getFullYear(),c.getMonth(),c.getDate(),23,59,59,999)),r.minDate!==!1&&(l=y.strToDate(r.minDate),l=new Date(l.getFullYear(),l.getMonth(),l.getDate()));u<y.currentTime.countDaysInMonth()||t.getDay()!==r.dayOfWeekStart||y.currentTime.getMonth()===t.getMonth();)f=[],u+=1,it=t.getDay(),b=t.getDate(),rt=t.getFullYear(),h=t.getMonth(),ut=y.getWeekOfYear(t),tt="",f.push("xdsoft_date"),a=r.beforeShowDay&&n.isFunction(r.beforeShowDay.call)?r.beforeShowDay.call(s,t):null,c!==!1&&t>c||l!==!1&&t<l||a&&a[0]===!1?f.push("xdsoft_disabled"):r.disabledDates.indexOf(t.dateFormat(r.formatDate))!==-1?f.push("xdsoft_disabled"):r.disabledWeekDays.indexOf(it)!==-1&&f.push("xdsoft_disabled"),a&&a[1]!==""&&f.push(a[1]),y.currentTime.getMonth()!==h&&f.push("xdsoft_other_month"),(r.defaultSelect||s.data("changed"))&&y.currentTime.dateFormat(r.formatDate)===t.dateFormat(r.formatDate)&&f.push("xdsoft_current"),k.dateFormat(r.formatDate)===t.dateFormat(r.formatDate)&&f.push("xdsoft_today"),(t.getDay()===0||t.getDay()===6||r.weekends.indexOf(t.dateFormat(r.formatDate))!==-1)&&f.push("xdsoft_weekend"),r.highlightedDates[t.dateFormat(r.formatDate)]!==undefined&&(v=r.highlightedDates[t.dateFormat(r.formatDate)],f.push(v.style===undefined?"xdsoft_highlighted_default":v.style),tt=v.desc===undefined?"":v.desc),r.beforeShowDay&&n.isFunction(r.beforeShowDay)&&f.push(r.beforeShowDay(t)),d&&(e+="<tr>",d=!1,r.weeks&&(e+="<th>"+ut+"<\/th>")),e+='<td data-date="'+b+'" data-month="'+h+'" data-year="'+rt+'" class="xdsoft_date xdsoft_day_of_week'+t.getDay()+" "+f.join(" ")+'" title="'+tt+'"><div>'+b+"<\/div><\/td>",t.getDay()===r.dayOfWeekStartPrev&&(e+="<\/tr>",d=!0),t.setDate(b+1);if(e+="<\/tbody><\/table>",pt.html(e),ot.find(".xdsoft_label span").eq(0).text(r.i18n[r.lang].months[y.currentTime.getMonth()]),ot.find(".xdsoft_label span").eq(1).text(y.currentTime.getFullYear()),g="",w="",h="",nt=function(n,t){var i=y.now(),u,e;i.setHours(n);n=parseInt(i.getHours(),10);i.setMinutes(t);t=parseInt(i.getMinutes(),10);u=new Date(y.currentTime);u.setHours(n);u.setMinutes(t);f=[];(r.minDateTime!==!1&&r.minDateTime>u||r.maxTime!==!1&&y.strtotime(r.maxTime).getTime()<i.getTime()||r.minTime!==!1&&y.strtotime(r.minTime).getTime()>i.getTime())&&f.push("xdsoft_disabled");(r.minDateTime!==!1&&r.minDateTime>u||r.disabledMinTime!==!1&&i.getTime()>y.strtotime(r.disabledMinTime).getTime()&&r.disabledMaxTime!==!1&&i.getTime()<y.strtotime(r.disabledMaxTime).getTime())&&f.push("xdsoft_disabled");e=new Date(y.currentTime);e.setHours(parseInt(y.currentTime.getHours(),10));e.setMinutes(Math[r.roundTime](y.currentTime.getMinutes()/r.step)*r.step);(r.initTime||r.defaultSelect||s.data("changed"))&&e.getHours()===parseInt(n,10)&&(r.step>59||e.getMinutes()===parseInt(t,10))&&(r.defaultSelect||s.data("changed")?f.push("xdsoft_current"):r.initTime&&f.push("xdsoft_init_time"));parseInt(k.getHours(),10)===parseInt(n,10)&&parseInt(k.getMinutes(),10)===parseInt(t,10)&&f.push("xdsoft_today");g+='<div class="xdsoft_time '+f.join(" ")+'" data-hour="'+n+'" data-minute="'+t+'">'+i.dateFormat(r.formatTime)+"<\/div>"},r.allowTimes&&n.isArray(r.allowTimes)&&r.allowTimes.length)for(u=0;u<r.allowTimes.length;u+=1)w=y.strtotime(r.allowTimes[u]).getHours(),h=y.strtotime(r.allowTimes[u]).getMinutes(),nt(w,h);else for(u=0,o=0;u<(r.hours12?12:24);u+=1)for(o=0;o<60;o+=r.step)w=(u<10?"0":"")+u,h=(o<10?"0":"")+o,nt(w,h);for(p.html(g),i="",u=0,u=parseInt(r.yearStart,10)+r.yearOffset;u<=parseInt(r.yearEnd,10)+r.yearOffset;u+=1)i+='<div class="xdsoft_option '+(y.currentTime.getFullYear()===u?"xdsoft_current":"")+'" data-value="'+u+'">'+u+"<\/div>";for(kt.children().eq(0).html(i),u=parseInt(r.monthStart,10),i="";u<=parseInt(r.monthEnd,10);u+=1)i+='<div class="xdsoft_option '+(y.currentTime.getMonth()===u?"xdsoft_current":"")+'" data-value="'+u+'">'+r.i18n[r.lang].months[u]+"<\/div>";bt.children().eq(0).html(i);n(s).trigger("generate.xdsoft")},10);t.stopPropagation()}).on("afterOpen.xdsoft",function(){if(r.timepicker){var n,t,i,u;p.find(".xdsoft_current").length?n=".xdsoft_current":p.find(".xdsoft_init_time").length&&(n=".xdsoft_init_time");n?(t=st[0].clientHeight,i=p[0].offsetHeight,u=p.find(n).index()*r.timeHeightInTimePicker+1,i-t<u&&(u=i-t),st.trigger("scroll_element.xdsoft_scroller",[parseInt(u,10)/(i-t)])):st.trigger("scroll_element.xdsoft_scroller",[0])}});vt=0;pt.on("click.xdsoft","td",function(i){i.stopPropagation();vt+=1;var f=n(this),u=y.currentTime;if((u===undefined||u===null)&&(y.currentTime=y.now(),u=y.currentTime),f.hasClass("xdsoft_disabled"))return!1;u.setDate(1);u.setFullYear(f.data("year"));u.setMonth(f.data("month"));u.setDate(f.data("date"));s.trigger("select.xdsoft",[u]);t.val(y.str());(vt>1||r.closeOnDateSelect===!0||r.closeOnDateSelect===!1&&!r.timepicker)&&!r.inline&&s.trigger("close.xdsoft");r.onSelectDate&&n.isFunction(r.onSelectDate)&&r.onSelectDate.call(s,y.currentTime,s.data("input"),i);s.data("changed",!0);s.trigger("xchange.xdsoft");s.trigger("changedatetime.xdsoft");setTimeout(function(){vt=0},200)});p.on("click.xdsoft","div",function(t){t.stopPropagation();var u=n(this),i=y.currentTime;if((i===undefined||i===null)&&(y.currentTime=y.now(),i=y.currentTime),u.hasClass("xdsoft_disabled"))return!1;i.setHours(u.data("hour"));i.setMinutes(u.data("minute"));s.trigger("select.xdsoft",[i]);s.data("input").val(y.str());r.inline!==!0&&r.closeOnTimeSelect===!0&&s.trigger("close.xdsoft");r.onSelectTime&&n.isFunction(r.onSelectTime)&&r.onSelectTime.call(s,y.currentTime,s.data("input"),t);s.data("changed",!0);s.trigger("xchange.xdsoft");s.trigger("changedatetime.xdsoft")});ct.on("mousewheel.xdsoft",function(n){return r.scrollMonth?(n.deltaY<0?y.nextMonth():y.prevMonth(),!1):!0});t.on("mousewheel.xdsoft",function(n){return r.scrollInput?!r.datepicker&&r.timepicker?(ht=p.find(".xdsoft_current").length?p.find(".xdsoft_current").eq(0).index():0,ht+n.deltaY>=0&&ht+n.deltaY<p.children().length&&(ht+=n.deltaY),p.children().eq(ht).length&&p.children().eq(ht).trigger("mousedown"),!1):r.datepicker&&!r.timepicker?(ct.trigger(n,[n.deltaY,n.deltaX,n.deltaY]),t.val&&t.val(y.str()),s.trigger("changedatetime.xdsoft"),!1):void 0:!0});s.on("changedatetime.xdsoft",function(t){if(r.onChangeDateTime&&n.isFunction(r.onChangeDateTime)){var i=s.data("input");r.onChangeDateTime.call(s,y.currentTime,i,t);delete r.value;i.trigger("change")}}).on("generate.xdsoft",function(){r.onGenerate&&n.isFunction(r.onGenerate)&&r.onGenerate.call(s,y.currentTime,s.data("input"));at&&(s.trigger("afterOpen.xdsoft"),at=!1)}).on("click.xdsoft",function(n){n.stopPropagation()});ht=0;yt=function(){var f=s.data("input").offset(),i=f.top+s.data("input")[0].offsetHeight-1,u=f.left,e="absolute",t;r.fixed?(i-=n(window).scrollTop(),u-=n(window).scrollLeft(),e="fixed"):(i+s[0].offsetHeight>n(window).height()+n(window).scrollTop()&&(i=f.top-s[0].offsetHeight+1),i<0&&(i=0),u+s[0].offsetWidth>n(window).width()&&(u=n(window).width()-s[0].offsetWidth));t=s[0];do if(t=t.parentNode,window.getComputedStyle(t).getPropertyValue("position")==="relative"&&n(window).width()>=t.offsetWidth){u=u-(n(window).width()-t.offsetWidth)/2;break}while(t.nodeName!=="HTML");s.css({left:u,top:i,position:e})};s.on("open.xdsoft",function(t){var i=!0;if(r.onShow&&n.isFunction(r.onShow)&&(i=r.onShow.call(s,y.currentTime,s.data("input"),t)),i!==!1){s.show();yt();n(window).off("resize.xdsoft",yt).on("resize.xdsoft",yt);if(r.closeOnWithoutClick)n([document.body,window]).on("mousedown.xdsoft",function u(){s.trigger("close.xdsoft");n([document.body,window]).off("mousedown.xdsoft",u)})}}).on("close.xdsoft",function(t){var i=!0;ot.find(".xdsoft_month,.xdsoft_year").find(".xdsoft_select").hide();r.onClose&&n.isFunction(r.onClose)&&(i=r.onClose.call(s,y.currentTime,s.data("input"),t));i===!1||r.opened||r.inline||s.hide();t.stopPropagation()}).on("toggle.xdsoft",function(){s.is(":visible")?s.trigger("close.xdsoft"):s.trigger("open.xdsoft")}).data("input",t);gt=0;ri=0;s.data("xdsoft_datetime",y);s.setOptions(r);y.setCurrentTime(ni());t.data("xdsoft_datetimepicker",s).on("open.xdsoft focusin.xdsoft mousedown.xdsoft",function(){t.is(":disabled")||t.data("xdsoft_datetimepicker").is(":visible")&&r.closeOnInputClick||(clearTimeout(gt),gt=setTimeout(function(){t.is(":disabled")||(at=!0,y.setCurrentTime(ni()),s.trigger("open.xdsoft"))},100))}).on("keydown.xdsoft",function(t){var f=this.value,i,u=t.which;return[a].indexOf(u)!==-1&&r.enterLikeTab?(i=n("input:visible,textarea:visible"),s.trigger("close.xdsoft"),i.eq(i.index(this)+1).focus(),!1):[v].indexOf(u)!==-1?(s.trigger("close.xdsoft"),!0):void 0})};p=function(t){var i=t.data("xdsoft_datetimepicker");i&&(i.data("xdsoft_datetime",null),i.remove(),t.data("xdsoft_datetimepicker",null).off(".xdsoft"),n(window).off("resize.xdsoft"),n([window,document.body]).off("mousedown.xdsoft"),t.unmousewheel&&t.unmousewheel())};n(document).off("keydown.xdsoftctrl keyup.xdsoftctrl").on("keydown.xdsoftctrl",function(n){n.keyCode===e&&(o=!0)}).on("keyup.xdsoftctrl",function(n){n.keyCode===e&&(o=!1)});return this.each(function(){var t=n(this).data("xdsoft_datetimepicker"),u;if(t){if(n.type(i)==="string")switch(i){case"show":n(this).select().focus();t.trigger("open.xdsoft");break;case"hide":t.trigger("close.xdsoft");break;case"toggle":t.trigger("toggle.xdsoft");break;case"destroy":p(n(this));break;case"reset":this.value=this.defaultValue;this.value&&t.data("xdsoft_datetime").isValidDate(Date.parseDate(this.value,r.format))||t.data("changed",!1);t.data("xdsoft_datetime").setCurrentTime(this.value);break;case"validate":u=t.data("input");u.trigger("blur.xdsoft")}else t.setOptions(i);return 0}n.type(i)!=="string"&&(!r.lazyInit||r.open||r.inline?s(n(this)):ot(n(this)))})};n.fn.datetimepicker.defaults=t})(jQuery),function(){
/*! Copyright (c) 2013 Brandon Aaron (http://brandon.aaron.sh)
 * Licensed under the MIT License (LICENSE.txt).
 *
 * Version: 3.1.12
 *
 * Requires: jQuery 1.2.2+
 */
!function(n){"function"==typeof define&&define.amd?define(["jquery"],n):"object"==typeof exports?module.exports=n:n(jQuery)}(function(n){function u(r){var u=r||window.event,w=c.call(arguments,1),l=0,s=0,e=0,a=0,b=0,k=0,v,y,p;if(r=n.event.fix(u),r.type="mousewheel","detail"in u&&(e=-1*u.detail),"wheelDelta"in u&&(e=u.wheelDelta),"wheelDeltaY"in u&&(e=u.wheelDeltaY),"wheelDeltaX"in u&&(s=-1*u.wheelDeltaX),"axis"in u&&u.axis===u.HORIZONTAL_AXIS&&(s=-1*e,e=0),l=0===e?s:e,"deltaY"in u&&(e=-1*u.deltaY,l=e),"deltaX"in u&&(s=u.deltaX,0===e&&(l=-1*s)),0!==e||0!==s)return 1===u.deltaMode?(v=n.data(this,"mousewheel-line-height"),l*=v,e*=v,s*=v):2===u.deltaMode&&(y=n.data(this,"mousewheel-page-height"),l*=y,e*=y,s*=y),(a=Math.max(Math.abs(e),Math.abs(s)),(!t||t>a)&&(t=a,o(u,a)&&(t/=40)),o(u,a)&&(l/=40,s/=40,e/=40),l=Math[l>=1?"floor":"ceil"](l/t),s=Math[s>=1?"floor":"ceil"](s/t),e=Math[e>=1?"floor":"ceil"](e/t),i.settings.normalizeOffset&&this.getBoundingClientRect)&&(p=this.getBoundingClientRect(),b=r.clientX-p.left,k=r.clientY-p.top),r.deltaX=s,r.deltaY=e,r.deltaFactor=t,r.offsetX=b,r.offsetY=k,r.deltaMode=0,w.unshift(r,l,s,e),f&&clearTimeout(f),f=setTimeout(h,200),(n.event.dispatch||n.event.handle).apply(this,w)}function h(){t=null}function o(n,t){return i.settings.adjustOldDeltas&&"mousewheel"===n.type&&t%120==0}var f,t,s=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],r="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],c=Array.prototype.slice,e,i;if(n.event.fixHooks)for(e=s.length;e;)n.event.fixHooks[s[--e]]=n.event.mouseHooks;i=n.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var t=r.length;t;)this.addEventListener(r[--t],u,!1);else this.onmousewheel=u;n.data(this,"mousewheel-line-height",i.getLineHeight(this));n.data(this,"mousewheel-page-height",i.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var t=r.length;t;)this.removeEventListener(r[--t],u,!1);else this.onmousewheel=null;n.removeData(this,"mousewheel-line-height");n.removeData(this,"mousewheel-page-height")},getLineHeight:function(t){var r=n(t),i=r["offsetParent"in n.fn?"offsetParent":"parent"]();return i.length||(i=n("body")),parseInt(i.css("fontSize"),10)||parseInt(r.css("fontSize"),10)||16},getPageHeight:function(t){return n(t).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};n.fn.extend({mousewheel:function(n){return n?this.bind("mousewheel",n):this.trigger("mousewheel")},unmousewheel:function(n){return this.unbind("mousewheel",n)}})});Date.parseFunctions={count:0};Date.parseRegexes=[];Date.formatFunctions={count:0};Date.prototype.dateFormat=function(n){if(n=="unixtime")return parseInt(this.getTime()/1e3);Date.formatFunctions[n]==null&&Date.createNewFormat(n);var t=Date.formatFunctions[n];return this[t]()};Date.createNewFormat=function(format){var funcName="format"+Date.formatFunctions.count++,i;Date.formatFunctions[format]=funcName;var codePrefix="Date.prototype."+funcName+" = function() {return ",code="",special=!1,ch="";for(i=0;i<format.length;++i)ch=format.charAt(i),special||ch!="\\"?special?(special=!1,code+="'"+String.escape(ch)+"' + "):code+=Date.getFormatCode(ch):special=!0;code=code.length==0?'""':code.substring(0,code.length-3);eval(codePrefix+code+";}")};Date.getFormatCode=function(n){switch(n){case"d":return"String.leftPad(this.getDate(), 2, '0') + ";case"D":return"Date.dayNames[this.getDay()].substring(0, 3) + ";case"j":return"this.getDate() + ";case"l":return"Date.dayNames[this.getDay()] + ";case"S":return"this.getSuffix() + ";case"w":return"this.getDay() + ";case"z":return"this.getDayOfYear() + ";case"W":return"this.getWeekOfYear() + ";case"F":return"Date.monthNames[this.getMonth()] + ";case"m":return"String.leftPad(this.getMonth() + 1, 2, '0') + ";case"M":return"Date.monthNames[this.getMonth()].substring(0, 3) + ";case"n":return"(this.getMonth() + 1) + ";case"t":return"this.getDaysInMonth() + ";case"L":return"(this.isLeapYear() ? 1 : 0) + ";case"Y":return"this.getFullYear() + ";case"y":return"('' + this.getFullYear()).substring(2, 4) + ";case"a":return"(this.getHours() < 12 ? 'am' : 'pm') + ";case"A":return"(this.getHours() < 12 ? 'AM' : 'PM') + ";case"g":return"((this.getHours() %12) ? this.getHours() % 12 : 12) + ";case"G":return"this.getHours() + ";case"h":return"String.leftPad((this.getHours() %12) ? this.getHours() % 12 : 12, 2, '0') + ";case"H":return"String.leftPad(this.getHours(), 2, '0') + ";case"i":return"String.leftPad(this.getMinutes(), 2, '0') + ";case"s":return"String.leftPad(this.getSeconds(), 2, '0') + ";case"O":return"this.getGMTOffset() + ";case"T":return"this.getTimezone() + ";case"Z":return"(this.getTimezoneOffset() * -60) + ";default:return"'"+String.escape(n)+"' + "}};Date.parseDate=function(n,t){if(t=="unixtime")return new Date(isNaN(parseInt(n))?0:parseInt(n)*1e3);Date.parseFunctions[t]==null&&Date.createParser(t);var i=Date.parseFunctions[t];return Date[i](n)};Date.createParser=function(format){var funcName="parse"+Date.parseFunctions.count++,regexNum=Date.parseRegexes.length,currentGroup=1,i;Date.parseFunctions[format]=funcName;var code="Date."+funcName+" = function(input) {\nvar y = -1, m = -1, d = -1, h = -1, i = -1, s = -1, z = -1;\nvar d = new Date();\ny = d.getFullYear();\nm = d.getMonth();\nd = d.getDate();\nvar results = input.match(Date.parseRegexes["+regexNum+"]);\nif (results && results.length > 0) {",regex="",special=!1,ch="";for(i=0;i<format.length;++i)ch=format.charAt(i),special||ch!="\\"?special?(special=!1,regex+=String.escape(ch)):(obj=Date.formatCodeToRegex(ch,currentGroup),currentGroup+=obj.g,regex+=obj.s,obj.g&&obj.c&&(code+=obj.c)):special=!0;code+="if (y > 0 && z > 0){\nvar doyDate = new Date(y,0);\ndoyDate.setDate(z);\nm = doyDate.getMonth();\nd = doyDate.getDate();\n}";code+="if (y > 0 && m >= 0 && d > 0 && h >= 0 && i >= 0 && s >= 0)\n{return new Date(y, m, d, h, i, s);}\nelse if (y > 0 && m >= 0 && d > 0 && h >= 0 && i >= 0)\n{return new Date(y, m, d, h, i);}\nelse if (y > 0 && m >= 0 && d > 0 && h >= 0)\n{return new Date(y, m, d, h);}\nelse if (y > 0 && m >= 0 && d > 0)\n{return new Date(y, m, d);}\nelse if (y > 0 && m >= 0)\n{return new Date(y, m);}\nelse if (y > 0)\n{return new Date(y);}\n}return null;}";Date.parseRegexes[regexNum]=new RegExp("^"+regex+"$","i");eval(code)};Date.formatCodeToRegex=function(n,t){switch(n){case"D":return{g:0,c:null,s:"(?:Sun|Mon|Tue|Wed|Thu|Fri|Sat)"};case"j":case"d":return{g:1,c:"d = parseInt(results["+t+"], 10);\n",s:"(\\d{1,2})"};case"l":return{g:0,c:null,s:"(?:"+Date.dayNames.join("|")+")"};case"S":return{g:0,c:null,s:"(?:st|nd|rd|th)"};case"w":return{g:0,c:null,s:"\\d"};case"z":return{g:1,c:"z = parseInt(results["+t+"], 10);\n",s:"(\\d{1,3})"};case"W":return{g:0,c:null,s:"(?:\\d{2})"};case"F":return{g:1,c:"m = parseInt(Date.monthNumbers[results["+t+"].substring(0, 3)], 10);\n",s:"("+Date.monthNames.join("|")+")"};case"M":return{g:1,c:"m = parseInt(Date.monthNumbers[results["+t+"]], 10);\n",s:"(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)"};case"n":case"m":return{g:1,c:"m = parseInt(results["+t+"], 10) - 1;\n",s:"(\\d{1,2})"};case"t":return{g:0,c:null,s:"\\d{1,2}"};case"L":return{g:0,c:null,s:"(?:1|0)"};case"Y":return{g:1,c:"y = parseInt(results["+t+"], 10);\n",s:"(\\d{4})"};case"y":return{g:1,c:"var ty = parseInt(results["+t+"], 10);\ny = ty > Date.y2kYear ? 1900 + ty : 2000 + ty;\n",s:"(\\d{1,2})"};case"a":return{g:1,c:"if (results["+t+"] == 'am') {\nif (h == 12) { h = 0; }\n} else { if (h < 12) { h += 12; }}",s:"(am|pm)"};case"A":return{g:1,c:"if (results["+t+"] == 'AM') {\nif (h == 12) { h = 0; }\n} else { if (h < 12) { h += 12; }}",s:"(AM|PM)"};case"g":case"G":case"h":case"H":return{g:1,c:"h = parseInt(results["+t+"], 10);\n",s:"(\\d{1,2})"};case"i":return{g:1,c:"i = parseInt(results["+t+"], 10);\n",s:"(\\d{2})"};case"s":return{g:1,c:"s = parseInt(results["+t+"], 10);\n",s:"(\\d{2})"};case"O":return{g:0,c:null,s:"[+-]\\d{4}"};case"T":return{g:0,c:null,s:"[A-Z]{3}"};case"Z":return{g:0,c:null,s:"[+-]\\d{1,5}"};default:return{g:0,c:null,s:String.escape(n)}}};Date.prototype.getTimezone=function(){return this.toString().replace(/^.*? ([A-Z]{3}) [0-9]{4}.*$/,"$1").replace(/^.*?\(([A-Z])[a-z]+ ([A-Z])[a-z]+ ([A-Z])[a-z]+\)$/,"$1$2$3")};Date.prototype.getGMTOffset=function(){return(this.getTimezoneOffset()>0?"-":"+")+String.leftPad(Math.floor(Math.abs(this.getTimezoneOffset())/60),2,"0")+String.leftPad(Math.abs(this.getTimezoneOffset())%60,2,"0")};Date.prototype.getDayOfYear=function(){var t=0,n;for(Date.daysInMonth[1]=this.isLeapYear()?29:28,n=0;n<this.getMonth();++n)t+=Date.daysInMonth[n];return t+this.getDate()};Date.prototype.getWeekOfYear=function(){var n=this.getDayOfYear()+(4-this.getDay()),t=new Date(this.getFullYear(),0,1),i=11-t.getDay();return String.leftPad(Math.ceil((n-i)/7)+1,2,"0")};Date.prototype.isLeapYear=function(){var n=this.getFullYear();return(n&3)==0&&(n%100||n%400==0&&n)};Date.prototype.getFirstDayOfMonth=function(){var n=(this.getDay()-(this.getDate()-1))%7;return n<0?n+7:n};Date.prototype.getLastDayOfMonth=function(){var n=(this.getDay()+(Date.daysInMonth[this.getMonth()]-this.getDate()))%7;return n<0?n+7:n};Date.prototype.getDaysInMonth=function(){return Date.daysInMonth[1]=this.isLeapYear()?29:28,Date.daysInMonth[this.getMonth()]};Date.prototype.getSuffix=function(){switch(this.getDate()){case 1:case 21:case 31:return"st";case 2:case 22:return"nd";case 3:case 23:return"rd";default:return"th"}};String.escape=function(n){return n.replace(/('|\\)/g,"\\$1")};String.leftPad=function(n,t,i){var r=new String(n);for(i==null&&(i=" ");r.length<t;)r=i+r;return r};Date.daysInMonth=[31,28,31,30,31,30,31,31,30,31,30,31];Date.monthNames=["January","February","March","April","May","June","July","August","September","October","November","December"];Date.dayNames=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];Date.y2kYear=50;Date.monthNumbers={Jan:0,Feb:1,Mar:2,Apr:3,May:4,Jun:5,Jul:6,Aug:7,Sep:8,Oct:9,Nov:10,Dec:11};Date.patterns={ISO8601LongPattern:"Y-m-d H:i:s",ISO8601ShortPattern:"Y-m-d",ShortDatePattern:"n/j/Y",LongDatePattern:"l, F d, Y",FullDateTimePattern:"l, F d, Y g:i:s A",MonthDayPattern:"F d",ShortTimePattern:"g:i A",LongTimePattern:"g:i:s A",SortableDateTimePattern:"Y-m-d\\TH:i:s",UniversalSortableDateTimePattern:"Y-m-d H:i:sO",YearMonthPattern:"F, Y"}}();
//# sourceMappingURL=jquery.datetimepicker.min.js.map
