function passwordCheck(value) {
    var uppercase = new RegExp('[A-Z]');
    var lowercase = new RegExp('[a-z]');
    var numbers = new RegExp('[0-9]');
    if (value.length >= 8) {
        $('#passwordLength').removeClass('fa-square-o').addClass('fa-check icon-green');
    } else {
        $('#passwordLength').removeClass('fa-check icon-green').addClass('fa-square-o');
    }
    // Maximum length check (<= 15)
    if (value.length <= 15) {
        $('#passwordMaxLength').removeClass('fa-square-o').addClass('fa-check icon-green');
    } else {
        $('#passwordMaxLength').removeClass('fa-check icon-green').addClass('fa-square-o');
    }
    if (value.match(uppercase)) {
        $('#passwordUpper').removeClass('fa-square-o').addClass('fa-check icon-green');
    } else {
        $('#passwordUpper').removeClass('fa-check icon-green').addClass('fa-square-o');
    }
    if (value.match(lowercase)) {
        $('#passwordLower').removeClass('fa-square-o').addClass('fa-check icon-green');
    } else {
        $('#passwordLower').removeClass('fa-check icon-green').addClass('fa-square-o');
    }
    if (value.match(numbers)) {
        $('#passwordNumber').removeClass('fa-square-o').addClass('fa-check icon-green');
    } else {
        $('#passwordNumber').removeClass('fa-check icon-green').addClass('fa-square-o');
    }
}