// --------------------------------------------------------------------------------
// JavaScript functions used for dealing with general data.
// --------------------------------------------------------------------------------



// ------------------------------------------------------------
// Formats a phone number as a 10-digit number, removing all 
// special characters.
// ------------------------------------------------------------
function FormatPhoneNumber(phoneNumber) {
    // Format
    phoneNumber = phoneNumber.replace("(", "");
    phoneNumber = phoneNumber.replace(")", "");
    phoneNumber = phoneNumber.replace(" ", "");
    phoneNumber = phoneNumber.replace("-", "");
    phoneNumber = phoneNumber.replace(".", "");

    return phoneNumber;
}

function toCamelCase(object) {
    if (object instanceof Array) {
        return object.map(value => {
            if (typeof value === "object") {
                value = toCamelCase(value);
            }
            return value;
        });
    } else {
        let returnObject = {};
        for (let origKey in object) {
            if (object.hasOwnProperty(origKey)) {
                let newKey = (origKey.charAt(0).toLowerCase() + origKey.slice(1) || origKey).toString()
                let value = object[origKey];
                if (value instanceof Array || (value !== null && value.constructor === Object)) {
                    value = toCamelCase(value);
                }
                returnObject[newKey] = value;
            }
        }
        return returnObject;
    }
}
