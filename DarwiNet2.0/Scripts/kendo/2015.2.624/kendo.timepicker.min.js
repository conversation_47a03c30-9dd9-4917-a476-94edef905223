/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.popup.min"],e)}(function(){return function(e,t){function n(e,t,n){var i,o=e.getTimezoneOffset();e.setTime(e.getTime()+t),n||(i=e.getTimezoneOffset()-o,e.setTime(e.getTime()+i*z))}function i(){var e=new $,t=new $(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0),n=new $(e.getFullYear(),e.getMonth(),e.getDate(),12,0,0);return-1*(t.getTimezoneOffset()-n.getTimezoneOffset())}function o(e){return 60*e.getHours()*z+e.getMinutes()*z+1e3*e.getSeconds()+e.getMilliseconds()}function r(e,t,n){var i,r=o(t),a=o(n);return e&&r!=a?(i=o(e),r>i&&(i+=B),r>a&&(a+=B),i>=r&&a>=i):!0}function a(e){var t=e.parseFormats;e.format=p(e.format||d.getCulture(e.culture).calendars.standard.patterns.t),t=j(t)?t:[t],t.splice(0,0,e.format),e.parseFormats=t}function s(e){e.preventDefault()}var l,c,d=window.kendo,u=d.keys,h=d.parseDate,f=d._activeElement,p=d._extractFormat,g=d.support,m=g.browser,v=d.ui,_=v.Widget,w="open",y="close",b="change",x=".kendoTimePicker",k="click"+x,C="k-state-default",S="disabled",T="readonly",D="li",A="<span/>",E="k-state-focused",P="k-state-hover",M="mouseenter"+x+" mouseleave"+x,I="mousedown"+x,z=6e4,B=864e5,L="k-state-selected",R="k-state-disabled",F="aria-selected",O="aria-expanded",H="aria-hidden",N="aria-disabled",V="aria-readonly",U="aria-activedescendant",W="id",j=e.isArray,G=e.extend,q=e.proxy,$=Date,Y=new $;Y=new $(Y.getFullYear(),Y.getMonth(),Y.getDate(),0,0,0),l=function(t){var n=this,i=t.id;n.options=t,n.ul=e('<ul tabindex="-1" role="listbox" aria-hidden="true" unselectable="on" class="k-list k-reset"/>').css({overflow:g.kineticScrollNeeded?"":"auto"}).on(k,D,q(n._click,n)).on("mouseenter"+x,D,function(){e(this).addClass(P)}).on("mouseleave"+x,D,function(){e(this).removeClass(P)}),n.list=e("<div class='k-list-container'/>").append(n.ul).on(I,s),i&&(n._timeViewID=i+"_timeview",n._optionID=i+"_option_selected",n.ul.attr(W,n._timeViewID)),n._popup(),n._heightHandler=q(n._height,n),n.template=d.template('<li tabindex="-1" role="option" class="k-item" unselectable="on">#=data#</li>',{useWithBlock:!1})},l.prototype={current:function(n){var i=this,o=i.options.active;return n===t?i._current:(i._current&&i._current.removeClass(L).removeAttr(F).removeAttr(W),n&&(n=e(n).addClass(L).attr(W,i._optionID).attr(F,!0),i.scroll(n[0])),i._current=n,o&&o(n),t)},close:function(){this.popup.close()},destroy:function(){var e=this;e.ul.off(x),e.list.off(x),e._touchScroller&&e._touchScroller.destroy(),e.popup.destroy()},open:function(){var e=this;e.ul[0].firstChild||e.bind(),e.popup.open(),e._current&&e.scroll(e._current[0])},dataBind:function(e){for(var t,n=this,i=n.options,o=i.format,a=d.toString,s=n.template,l=e.length,c=0,u="";l>c;c++)t=e[c],r(t,i.min,i.max)&&(u+=s(a(t,o,i.culture)));n._html(u)},refresh:function(){var e,t,r,a=this,s=a.options,l=s.format,c=i(),u=0>c,h=s.min,f=s.max,p=o(h),g=o(f),m=s.interval*z,v=d.toString,_=a.template,w=new $(+h),y=w.getDate(),b=0,x="";for(r=u?(B+c*z)/m:B/m,p!=g&&(p>g&&(g+=B),r=(g-p)/m+1),t=parseInt(r,10);r>b;b++)b&&n(w,m,u),g&&t==b&&(e=o(w),y<w.getDate()&&(e+=B),e>g&&(w=new $(+f))),x+=_(v(w,l,s.culture));a._html(x)},bind:function(){var e=this,t=e.options.dates;t&&t[0]?e.dataBind(t):e.refresh()},_html:function(e){var t=this;t.ul[0].innerHTML=e,t.popup.unbind(w,t._heightHandler),t.popup.one(w,t._heightHandler),t.current(null),t.select(t._value)},scroll:function(e){if(e){var t,n=this.ul[0],i=e.offsetTop,o=e.offsetHeight,r=n.scrollTop,a=n.clientHeight,s=i+o,l=this._touchScroller;l?(t=this.list.height(),i>t&&(i=i-t+o),l.scrollTo(0,-i)):n.scrollTop=r>i?i:s>r+a?s-a:r}},select:function(t){var n=this,i=n.options,o=n._current;t instanceof Date&&(t=d.toString(t,i.format,i.culture)),"string"==typeof t&&(o&&o.text()===t?t=o:(t=e.grep(n.ul[0].childNodes,function(e){return(e.textContent||e.innerText)==t}),t=t[0]?t:null)),n.current(t)},setOptions:function(e){var t=this.options;e.min=h(e.min),e.max=h(e.max),this.options=G(t,e,{active:t.active,change:t.change,close:t.close,open:t.open}),this.bind()},toggle:function(){var e=this;e.popup.visible()?e.close():e.open()},value:function(e){var t=this;t._value=e,t.ul[0].firstChild&&t.select(e)},_click:function(t){var n=this,i=e(t.currentTarget),o=i.text(),r=n.options.dates;r&&r.length>0&&(o=r[i.index()]),t.isDefaultPrevented()||(n.select(i),n.options.change(o,!0),n.close())},_height:function(){var e=this,t=e.list,n=t.parent(".k-animation-container"),i=e.options.height;e.ul[0].children.length&&t.add(n).show().height(e.ul[0].scrollHeight>i?i:"auto").hide()},_parse:function(e){var t=this,n=t.options,i=t._value||Y;return e instanceof $?e:(e=h(e,n.parseFormats,n.culture),e&&(e=new $(i.getFullYear(),i.getMonth(),i.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds())),e)},_adjustListWidth:function(){var e,t,n=this.list,i=n[0].style.width,o=this.options.anchor;(n.data("width")||!i)&&(e=window.getComputedStyle?window.getComputedStyle(o[0],null):0,t=e?parseFloat(e.width):o.outerWidth(),e&&(m.mozilla||m.msie)&&(t+=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight)+parseFloat(e.borderLeftWidth)+parseFloat(e.borderRightWidth)),i=t-(n.outerWidth()-n.width()),n.css({fontFamily:o.css("font-family"),width:i}).data("width",i))},_popup:function(){var e=this,t=e.list,n=e.options,i=n.anchor;e.popup=new v.Popup(t,G(n.popup,{anchor:i,open:n.open,close:n.close,animation:n.animation,isRtl:g.isRtl(n.anchor)})),e._touchScroller=d.touchScroller(e.popup.element)},move:function(e){var n=this,i=e.keyCode,o=n.ul[0],r=n._current,a=i===u.DOWN;if(i===u.UP||a){if(e.altKey)return n.toggle(a),t;r=a?r?r[0].nextSibling:o.firstChild:r?r[0].previousSibling:o.lastChild,r&&n.select(r),n.options.change(n._current.text()),e.preventDefault()}else(i===u.ENTER||i===u.TAB||i===u.ESC)&&(e.preventDefault(),r&&n.options.change(r.text(),!0),n.close())}},l.getMilliseconds=o,d.TimeView=l,c=_.extend({init:function(t,n){var i,o,r,s=this;_.fn.init.call(s,t,n),t=s.element,n=s.options,n.min=h(t.attr("min"))||h(n.min),n.max=h(t.attr("max"))||h(n.max),a(n),s._initialOptions=G({},n),s._wrapper(),s.timeView=o=new l(G({},n,{id:t.attr(W),anchor:s.wrapper,format:n.format,change:function(e,n){n?s._change(e):t.val(e)},open:function(e){s.timeView._adjustListWidth(),s.trigger(w)?e.preventDefault():(t.attr(O,!0),i.attr(H,!1))},close:function(e){s.trigger(y)?e.preventDefault():(t.attr(O,!1),i.attr(H,!0))},active:function(e){t.removeAttr(U),e&&t.attr(U,o._optionID)}})),i=o.ul,s._icon(),s._reset();try{t[0].setAttribute("type","text")}catch(c){t[0].type="text"}t.addClass("k-input").attr({role:"combobox","aria-expanded":!1,"aria-owns":o._timeViewID}),r=t.is("[disabled]")||e(s.element).parents("fieldset").is(":disabled"),r?s.enable(!1):s.readonly(t.is("[readonly]")),s._old=s._update(n.value||s.element.val()),s._oldText=t.val(),d.notify(s)},options:{name:"TimePicker",min:Y,max:Y,format:"",dates:[],parseFormats:[],value:null,interval:30,height:200,animation:{}},events:[w,y,b],setOptions:function(e){var t=this,n=t._value;_.fn.setOptions.call(t,e),e=t.options,a(e),t.timeView.setOptions(e),n&&t.element.val(d.toString(n,e.format,e.culture))},dataBind:function(e){j(e)&&this.timeView.dataBind(e)},_editable:function(e){var t=this,n=e.disable,i=e.readonly,o=t._arrow.off(x),r=t.element.off(x),a=t._inputWrapper.off(x);i||n?(a.addClass(n?R:C).removeClass(n?C:R),r.attr(S,n).attr(T,i).attr(N,n).attr(V,i)):(a.addClass(C).removeClass(R).on(M,t._toggleHover),r.removeAttr(S).removeAttr(T).attr(N,!1).attr(V,!1).on("keydown"+x,q(t._keydown,t)).on("focusout"+x,q(t._blur,t)).on("focus"+x,function(){t._inputWrapper.addClass(E)}),o.on(k,q(t._click,t)).on(I,s))},readonly:function(e){this._editable({readonly:e===t?!0:e,disable:!1})},enable:function(e){this._editable({readonly:!1,disable:!(e=e===t?!0:e)})},destroy:function(){var e=this;_.fn.destroy.call(e),e.timeView.destroy(),e.element.off(x),e._arrow.off(x),e._inputWrapper.off(x),e._form&&e._form.off("reset",e._resetHandler)},close:function(){this.timeView.close()},open:function(){this.timeView.open()},min:function(e){return this._option("min",e)},max:function(e){return this._option("max",e)},value:function(e){var n=this;return e===t?n._value:(n._old=n._update(e),null===n._old&&n.element.val(""),n._oldText=n.element.val(),t)},_blur:function(){var e=this,t=e.element.val();e.close(),t!==e._oldText&&e._change(t),e._inputWrapper.removeClass(E)},_click:function(){var e=this,t=e.element;e.timeView.toggle(),g.touch||t[0]===f()||t.focus()},_change:function(e){var t=this;e=t._update(e),+t._old!=+e&&(t._old=e,t._oldText=t.element.val(),t._typing||t.element.trigger(b),t.trigger(b)),t._typing=!1},_icon:function(){var t,n=this,i=n.element;t=i.next("span.k-select"),t[0]||(t=e('<span unselectable="on" class="k-select"><span unselectable="on" class="k-icon k-i-clock">select</span></span>').insertAfter(i)),n._arrow=t.attr({role:"button","aria-controls":n.timeView._timeViewID})},_keydown:function(e){var t=this,n=e.keyCode,i=t.timeView,o=t.element.val();i.popup.visible()||e.altKey?i.move(e):n===u.ENTER&&o!==t._oldText?t._change(o):t._typing=!0},_option:function(e,n){var i=this,o=i.options;return n===t?o[e]:(n=i.timeView._parse(n),n&&(n=new $(+n),o[e]=n,i.timeView.options[e]=n,i.timeView.bind()),t)},_toggleHover:function(t){e(t.currentTarget).toggleClass(P,"mouseenter"===t.type)},_update:function(e){var t=this,n=t.options,i=t.timeView,o=i._parse(e);return r(o,n.min,n.max)||(o=null),t._value=o,t.element.val(o?d.toString(o,n.format,n.culture):e),i.value(o),o},_wrapper:function(){var t,n=this,i=n.element;t=i.parents(".k-timepicker"),t[0]||(t=i.wrap(A).parent().addClass("k-picker-wrap k-state-default"),t=t.wrap(A).parent()),t[0].style.cssText=i[0].style.cssText,n.wrapper=t.addClass("k-widget k-timepicker k-header").addClass(i[0].className),i.css({width:"100%",height:i[0].style.height}),n._inputWrapper=e(t[0].firstChild)},_reset:function(){var t=this,n=t.element,i=n.attr("form"),o=i?e("#"+i):n.closest("form");o[0]&&(t._resetHandler=function(){t.value(n[0].defaultValue),t.max(t._initialOptions.max),t.min(t._initialOptions.min)},t._form=o.on("reset",t._resetHandler))}}),v.plugin(c)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});