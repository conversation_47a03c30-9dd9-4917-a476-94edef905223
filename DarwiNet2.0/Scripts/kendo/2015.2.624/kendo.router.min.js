/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.core.min"],e)}(function(){return function(e,t){function n(e,t){if(!t)return e;e+"/"===t&&(e=t);var n=RegExp("^"+t,"i");return n.test(e)||(e=t+"/"+e),f.protocol+"//"+(f.host+"/"+e).replace(/\/\/+/g,"/")}function i(e){return e?"#!":"#"}function o(e){var t=f.href;return"#!"===e&&t.indexOf("#")>-1&&t.indexOf("#!")<0?null:t.split(e)[1]||""}function r(e,t){return 0===t.indexOf(e)?t.substr(e.length).replace(/\/\//g,"/"):t}function s(e){return e.replace(/^(#)?/,"#")}function a(e){return e.replace(/^(#(!)?)?/,"#!")}var l=window.kendo,c="change",d="back",u="same",h=l.support,f=window.location,p=window.history,g=50,m=l.support.browser.msie,v=/^#*/,_=window.document,w=l.Class.extend({back:function(){m?setTimeout(function(){p.back()}):p.back()},forward:function(){m?setTimeout(function(){p.forward()}):p.forward()},length:function(){return p.length},replaceLocation:function(e){f.replace(e)}}),y=w.extend({init:function(e){this.root=e},navigate:function(e){p.pushState({},_.title,n(e,this.root))},replace:function(e){p.replaceState({},_.title,n(e,this.root))},normalize:function(e){return r(this.root,e)},current:function(){var e=f.pathname;return f.search&&(e+=f.search),r(this.root,e)},change:function(t){e(window).bind("popstate.kendo",t)},stop:function(){e(window).unbind("popstate.kendo")},normalizeCurrent:function(e){var t,r=e.root,s=f.pathname,a=o(i(e.hashBang));r===s+"/"&&(t=r),r===s&&a&&(t=n(a.replace(v,""),r)),t&&p.pushState({},_.title,t)}}),b=w.extend({init:function(e){this._id=l.guid(),this.prefix=i(e),this.fix=e?a:s},navigate:function(e){f.hash=this.fix(e)},replace:function(e){this.replaceLocation(this.fix(e))},normalize:function(e){return e.indexOf(this.prefix)<0?e:e.split(this.prefix)[1]},change:function(t){h.hashChange?e(window).on("hashchange."+this._id,t):this._interval=setInterval(t,g)},stop:function(){e(window).off("hashchange."+this._id),clearInterval(this._interval)},current:function(){return o(this.prefix)},normalizeCurrent:function(e){var t=f.pathname,n=e.root;return e.pushState&&n!==t?(this.replaceLocation(n+this.prefix+r(n,t)),!0):!1}}),x=l.Observable.extend({start:function(t){if(t=t||{},this.bind([c,d,u],t),!this._started){this._started=!0,t.root=t.root||"/";var n,i=this.createAdapter(t);i.normalizeCurrent(t)||(n=i.current(),e.extend(this,{adapter:i,root:t.root,historyLength:i.length(),current:n,locations:[n]}),i.change(e.proxy(this,"_checkUrl")))}},createAdapter:function(e){return h.pushState&&e.pushState?new y(e.root):new b(e.hashBang)},stop:function(){this._started&&(this.adapter.stop(),this.unbind(c),this._started=!1)},change:function(e){this.bind(c,e)},replace:function(e,t){this._navigate(e,t,function(t){t.replace(e),this.locations[this.locations.length-1]=this.current})},navigate:function(e,n){return"#:back"===e?(this.backCalled=!0,this.adapter.back(),t):(this._navigate(e,n,function(t){t.navigate(e),this.locations.push(this.current)}),t)},_navigate:function(e,n,i){var o=this.adapter;return e=o.normalize(e),this.current===e||this.current===decodeURIComponent(e)?(this.trigger(u),t):((n||!this.trigger(c,{url:e}))&&(this.current=e,i.call(this,o),this.historyLength=o.length()),t)},_checkUrl:function(){var e=this.adapter,n=e.current(),i=e.length(),o=this.historyLength===i,r=n===this.locations[this.locations.length-2]&&o,s=this.backCalled,a=this.current;return null===n||this.current===n||this.current===decodeURIComponent(n)?!0:(this.historyLength=i,this.backCalled=!1,this.current=n,r&&this.trigger("back",{url:a,to:n})?(e.forward(),this.current=a,t):this.trigger(c,{url:n,backButtonPressed:!s})?(r?e.forward():(e.back(),this.historyLength--),this.current=a,t):(r?this.locations.pop():this.locations.push(n),t))}});l.History=x,l.History.HistoryAdapter=w,l.History.HashAdapter=b,l.History.PushStateAdapter=y,l.absoluteURL=n,l.history=new x}(window.kendo.jQuery),function(){function e(e,t){return t?e:"([^/]+)"}function t(t,n){return RegExp("^"+t.replace(p,"\\$&").replace(u,"(?:$1)?").replace(h,e).replace(f,"(.*?)")+"$",n?"i":"")}function n(e){return e.replace(/(\?.*)|(#.*)/g,"")}var i=window.kendo,o=i.history,r=i.Observable,s="init",a="routeMissing",l="change",c="back",d="same",u=/\((.*?)\)/g,h=/(\(\?)?:\w+/g,f=/\*\w+/g,p=/[\-{}\[\]+?.,\\\^$|#\s]/g,g=i.Class.extend({init:function(e,n,i){e instanceof RegExp||(e=t(e,i)),this.route=e,this._callback=n},callback:function(e){var t,o,r=0,s=i.parseQueryStringParams(e);for(e=n(e),t=this.route.exec(e).slice(1),o=t.length;o>r;r++)void 0!==t[r]&&(t[r]=decodeURIComponent(t[r]));t.push(s),this._callback.apply(null,t)},worksWith:function(e){return this.route.test(n(e))?(this.callback(e),!0):!1}}),m=r.extend({init:function(e){e||(e={}),r.fn.init.call(this),this.routes=[],this.pushState=e.pushState,this.hashBang=e.hashBang,this.root=e.root,this.ignoreCase=e.ignoreCase!==!1,this.bind([s,a,l,d],e)},destroy:function(){o.unbind(l,this._urlChangedProxy),o.unbind(d,this._sameProxy),o.unbind(c,this._backProxy),this.unbind()},start:function(){var e,t=this,n=function(){t._same()},i=function(e){t._back(e)},r=function(e){t._urlChanged(e)};o.start({same:n,change:r,back:i,pushState:t.pushState,hashBang:t.hashBang,root:t.root}),e={url:o.current||"/",preventDefault:$.noop},t.trigger(s,e)||t._urlChanged(e),this._urlChangedProxy=r,this._backProxy=i},route:function(e,t){this.routes.push(new g(e,t,this.ignoreCase))},navigate:function(e,t){i.history.navigate(e,t)},replace:function(e,t){i.history.replace(e,t)},_back:function(e){this.trigger(c,{url:e.url,to:e.to})&&e.preventDefault()},_same:function(){this.trigger(d)},_urlChanged:function(e){var t,n,o,r,s=e.url;if(s||(s="/"),this.trigger(l,{url:e.url,params:i.parseQueryStringParams(e.url),backButtonPressed:e.backButtonPressed}))return void e.preventDefault();for(t=0,n=this.routes,r=n.length;r>t;t++)if(o=n[t],o.worksWith(s))return;this.trigger(a,{url:s,params:i.parseQueryStringParams(s),backButtonPressed:e.backButtonPressed})&&e.preventDefault()}});i.Router=m}(),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});