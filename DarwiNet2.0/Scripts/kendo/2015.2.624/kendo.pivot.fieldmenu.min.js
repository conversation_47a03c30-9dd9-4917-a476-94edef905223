/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.pivotgrid.min","./kendo.menu.min","./kendo.window.min","./kendo.treeview.min","./kendo.dropdownlist.min"],e)}(function(){return function(e,t){function n(e,t){var n,i,o=[];for(n=0,i=e.length;i>n;n++)e[n].field!==t&&o.push(e[n]);return o}function i(e,t,n){var i,o,r,a;if(!e)return[];for(e=e.filters,i=0,o=[],r=e.length;r>i;i++)a=e[i].operator,(n||"in"===a)&&a!==n||e[i].field!==t||o.push(e[i]);return o}function o(t,n,o){var r,a=0,s=o.length;if(t=i(t,n,"in")[0])for(r=t.value.split(",");s>a;a++)o[a].checked=e.inArray(o[a].uniqueName,r)>=0;else for(;s>a;a++)o[a].checked=!0}function r(e,t){var n,i=e.length;for(n=0;i>n;n++)e[n].checked&&0!==e[n].level()&&t.push(e[n].uniqueName),e[n].hasChildren&&r(e[n].children.view(),t)}var a=window.kendo,s=a.ui,l="kendoContextMenu",c=e.proxy,d=".kendoPivotFieldMenu",h=s.Widget,u=h.extend({init:function(e,t){h.fn.init.call(this,e,t),this._dataSource(),this._layout(),a.notify(this)},events:[],options:{name:"PivotFieldMenu",filter:null,filterable:!0,sortable:!0,messages:{info:"Show items with value that:",sortAscending:"Sort Ascending",sortDescending:"Sort Descending",filterFields:"Fields Filter",filter:"Filter",include:"Include Fields...",title:"Fields to include",clear:"Clear",ok:"OK",cancel:"Cancel",operators:{contains:"Contains",doesnotcontain:"Does not contain",startswith:"Starts with",endswith:"Ends with",eq:"Is equal to",neq:"Is not equal to"}}},_layout:function(){var t=this.options;this.wrapper=e(a.template(p)({ns:a.ns,filterable:t.filterable,sortable:t.sortable,messages:t.messages})),this.menu=this.wrapper[l]({filter:t.filter,target:this.element,orientation:"vertical",showOn:"click",closeOnClick:!1,open:c(this._menuOpen,this),select:c(this._select,this),copyAnchorStyles:!1}).data(l),this._createWindow(),t.filterable&&this._initFilterForm()},_initFilterForm:function(){var e=this.menu.element.find(".k-filter-item"),t=c(this._filter,this);this._filterOperator=new a.ui.DropDownList(e.find("select")),this._filterValue=e.find(".k-textbox"),e.on("submit"+d,t).on("click"+d,".k-button-filter",t).on("click"+d,".k-button-clear",c(this._reset,this))},_setFilterForm:function(e){var t=this._filterOperator,n="",i="";e&&(n=e.operator,i=e.value),t.value(n),t.value()||t.select(0),this._filterValue.val(i)},_clearFilters:function(e){var t,n,o=this.dataSource.filter()||{},r=0;for(o.filters=o.filters||[],t=i(o,e),n=t.length;n>r;r++)o.filters.splice(o.filters.indexOf(t[r]),1);return o},_filter:function(e){var n,i,o=this,r=o._filterValue.val();return e.preventDefault(),r?(n={field:o.currentMember,operator:o._filterOperator.value(),value:r},i=o._clearFilters(o.currentMember),i.filters.push(n),o.dataSource.filter(i),o.menu.close(),t):(o.menu.close(),t)},_reset:function(e){var t=this,n=t._clearFilters(t.currentMember);e.preventDefault(),n.filters[0]||(n={}),t.dataSource.filter(n),t._setFilterForm(null),t.menu.close()},_sort:function(e){var t=this.currentMember,i=this.dataSource.sort()||[];i=n(i,t),i.push({field:t,dir:e}),this.dataSource.sort(i),this.menu.close()},setDataSource:function(e){this.options.dataSource=e,this._dataSource()},_dataSource:function(){this.dataSource=a.data.PivotDataSource.create(this.options.dataSource)},_createWindow:function(){var t=this.options.messages;this.includeWindow=e(a.template(g)({messages:t})).on("click"+d,".k-button-ok",c(this._applyIncludes,this)).on("click"+d,".k-button-cancel",c(this._closeWindow,this)),this.includeWindow=new s.Window(this.includeWindow,{title:t.title,visible:!1,resizable:!1,open:c(this._windowOpen,this)})},_applyIncludes:function(e){var t,n=[],o=this.treeView.dataSource.view(),a=o[0].checked,s=this.dataSource.filter(),l=i(s,this.currentMember,"in")[0];r(o,n),l&&(a?(s.filters.splice(s.filters.indexOf(l),1),s.filters.length||(s={})):l.value=n.join(","),t=s),n.length&&(t||a||(t={field:this.currentMember,operator:"in",value:n.join(",")},s&&(s.filters.push(t),t=s))),t&&this.dataSource.filter(t),this._closeWindow(e)},_closeWindow:function(e){e.preventDefault(),this.includeWindow.close()},_treeViewDataSource:function(){var e=this;return a.data.HierarchicalDataSource.create({schema:{model:{id:"uniqueName",hasChildren:function(e){return parseInt(e.childrenCardinality,10)>0}}},transport:{read:function(t){var n={},i=e.treeView.dataSource.get(t.data.uniqueName),r=t.data.uniqueName;r?(n.memberUniqueName=i.uniqueName.replace(/\&/g,"&amp;"),n.treeOp=1):n.levelUniqueName=e.currentMember+".[(ALL)]",e.dataSource.schemaMembers(n).done(function(n){o(e.dataSource.filter(),e.currentMember,n),t.success(n)}).fail(t.error)}}})},_createTreeView:function(e){var t=this;t.treeView=new s.TreeView(e,{autoBind:!1,dataSource:t._treeViewDataSource(),dataTextField:"caption",template:"#: data.item.caption || data.item.name #",checkboxes:{checkChildren:!0},dataBound:function(){s.progress(t.includeWindow.element,!1)}})},_menuOpen:function(t){var n;t.event&&(n=a.attr("name"),this.currentMember=e(t.event.target).closest("["+n+"]").attr(n),this.options.filterable&&this._setFilterForm(i(this.dataSource.filter(),this.currentMember)[0]))},_select:function(t){var n=e(t.item);e(".k-pivot-filter-window").not(this.includeWindow.element).kendoWindow("close"),n.hasClass("k-include-item")?this.includeWindow.center().open():n.hasClass("k-sort-asc")?this._sort("asc"):n.hasClass("k-sort-desc")&&this._sort("desc")},_windowOpen:function(){this.treeView||this._createTreeView(this.includeWindow.element.find(".k-treeview")),s.progress(this.includeWindow.element,!0),this.treeView.dataSource.read()},destroy:function(){h.fn.destroy.call(this),this.menu&&(this.menu.destroy(),this.menu=null),this.treeView&&(this.treeView.destroy(),this.treeView=null),this.includeWindow&&(this.includeWindow.destroy(),this.includeWindow=null),this.wrapper=null,this.element=null}}),f='<div class="k-filterable k-content" tabindex="-1" data-role="fieldmenu"><form class="k-filter-menu"><div><div class="k-filter-help-text">#=messages.info#</div><select>#for(var op in messages.operators){#<option value="#=op#">#=messages.operators[op]#</option>#}#</select><input class="k-textbox" type="text" /><div><a class="k-button k-primary k-button-filter" href="\\#">#=messages.filter#</a><a class="k-button k-button-clear" href="\\#">#=messages.clear#</a></div></div></form></div>',p='<ul class="k-pivot-fieldmenu"># if (sortable) {#<li class="k-item k-sort-asc"><span class="k-link"><span class="k-icon k-i-sort-asc"></span>${messages.sortAscending}</span></li><li class="k-item k-sort-desc"><span class="k-link"><span class="k-icon k-i-sort-desc"></span>${messages.sortDescending}</span></li># if (filterable) {#<li class="k-separator"></li># } ## } ## if (filterable) {#<li class="k-item k-include-item"><span class="k-link"><span class="k-icon k-filter"></span>${messages.include}</span></li><li class="k-separator"></li><li class="k-item k-filter-item"><span class="k-link"><span class="k-icon k-filter"></span>${messages.filterFields}</span><ul><li>'+f+"</li></ul></li># } #</ul>",g='<div class="k-popup-edit-form k-pivot-filter-window"><div class="k-edit-form-container"><div class="k-treeview"></div><div class="k-edit-buttons k-state-default"><a class="k-button k-primary k-button-ok" href="\\#">${messages.ok}</a><a class="k-button k-button-cancel" href="\\#">${messages.cancel}</a></div></div>';s.plugin(u)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});