/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.scheduler.view.min"],e)}(function(){return function(e){function t(e){var t,n,i=0;for(t=0,n=e.length;n>t;t++)i+=e[t].items.length;return i}function n(e,t){return e.valuePrimitive&&(t=r.getter(e.dataValueField)(t)),t}function i(e){for(var t,n=0,i=e.length,r=[];i>n;n++)t=e[n],t.groups?(t=o(t.groups),r=r.concat(t)):r=r.concat(o(t.items));return r}function o(e){for(var t=[].concat(e),n=t.shift(),i=[],o=[].push;n;)n.groups?o.apply(t,n.groups):n.items?o.apply(t,n.items):o.call(i,n),n=t.shift();return i}var r=window.kendo,s=r.ui,a=".kendoAgendaView",l='<div class="k-task" title="#:title.replace(/"/g,"\'")#" data-#=kendo.ns#uid="#=uid#"># if (resources[0]) {#<span class="k-scheduler-mark" style="background-color:#=resources[0].color#"></span># } ## if (data.isException()) { #<span class="k-icon k-i-exception"></span># } else if (data.isRecurring()) {#<span class="k-icon k-i-refresh"></span># } #{0}#if (showDelete) {#<a href="\\#" class="k-link k-event-delete"><span class="k-icon k-si-close"></span></a>#}#</div>';s.AgendaView=s.SchedulerView.extend({init:function(t,n){s.SchedulerView.fn.init.call(this,t,n),n=this.options,n.editable&&(n.editable=e.extend({"delete":!0},n.editable,{create:!1,update:!1})),this.title=n.title,this.name="agenda",this._eventTemplate=this._eventTmpl(n.eventTemplate,l),this._dateTemplate=r.template(n.eventDateTemplate),this._groupTemplate=r.template(n.eventGroupTemplate),this._timeTemplate=r.template(n.eventTimeTemplate),this.element.on("mouseenter"+a,".k-scheduler-agenda .k-scheduler-content tr","_mouseenter").on("mouseleave"+a,".k-scheduler-agenda .k-scheduler-content tr","_mouseleave").on("click"+a,".k-scheduler-agenda .k-scheduler-content .k-link:has(.k-si-close)","_remove"),this._renderLayout(n.date)},_mouseenter:function(t){e(t.currentTarget).addClass("k-state-hover")},_mouseleave:function(t){e(t.currentTarget).removeClass("k-state-hover")},_remove:function(t){t.preventDefault(),this.trigger("remove",{uid:e(t.currentTarget).closest(".k-task").attr(r.attr("uid"))})},nextDate:function(){return r.date.nextDay(this.startDate())},startDate:function(){return this._startDate},endDate:function(){return this._endDate},previousDate:function(){return r.date.previousDay(this.startDate())},_renderLayout:function(e){this._startDate=e,this._endDate=r.date.addDays(e,7),this.createLayout(this._layout()),this.table.addClass("k-scheduler-agenda")},_layout:function(){var e,t,n,i=[{text:this.options.messages.time,className:"k-scheduler-timecolumn"},{text:this.options.messages.event}];if(this._isMobilePhoneView()||i.splice(0,0,{text:this.options.messages.date,className:"k-scheduler-datecolumn"}),e=this.groupedResources,e.length){for(t=[],n=0;e.length>n;n++)t.push({text:"",className:"k-scheduler-groupcolumn"});i=t.concat(i)}return{columns:i}},_tasks:function(e){var t,n,i,o,s,a,l,c=[];for(t=0;e.length>t;t++)if(n=e[t],i=n.start,o=n.end,s=(r.date.getDate(o)-r.date.getDate(i))/r.date.MS_PER_DAY+1,a=n.clone(),a.startDate=r.date.getDate(i),a.startDate>=this.startDate()&&c.push(a),s>1)for(a.end=r.date.nextDay(i),a.head=!0,l=1;s>l;l++)i=a.end,a=n.clone(),a.start=i,a.startDate=r.date.getDate(i),a.end=r.date.nextDay(i),l==s-1?(a.end=new Date(a.start.getFullYear(),a.start.getMonth(),a.start.getDate(),o.getHours(),o.getMinutes(),o.getSeconds(),o.getMilliseconds()),a.tail=!0):(a.isAllDay=!0,a.middle=!0),a.end<=this.endDate()&&a.start>=this.startDate()&&c.push(a);return new r.data.Query(c).sort([{field:"start",dir:"asc"},{field:"end",dir:"asc"}]).groupBy({field:"startDate"}).toArray()},_renderTaskGroups:function(e,t){var n,i,o,s,a,l,c,d,u,h=[],f=this.options.editable,p=f&&f.destroy!==!1&&!this._isMobile(),g=this._isMobilePhoneView();for(n=0;e.length>n;n++)for(i=e[n].value,o=e[n].items,s=r.date.isToday(i),a=0;o.length>a;a++){if(l=o[a],c=[],d=g?[]:c,0===n&&0===a&&t.length)for(u=0;t.length>u;u++)d.push(r.format('<td class="k-scheduler-groupcolumn{2}" rowspan="{0}">{1}</td>',t[u].rowSpan,this._groupTemplate({value:t[u].text}),t[u].className));0===a&&(g?(d.push(r.format('<td class="k-scheduler-datecolumn" colspan="2">{0}</td>',this._dateTemplate({date:i}))),h.push('<tr role="row" aria-selected="false"'+(s?' class="k-today">':">")+d.join("")+"</tr>")):c.push(r.format('<td class="k-scheduler-datecolumn{3}{2}" rowspan="{0}">{1}</td>',o.length,this._dateTemplate({date:i}),n!=e.length-1||t.length?"":" k-last",t.length?"":" k-first"))),l.format=l.head?"{0:t}":l.tail?"{1:t}":"{0:t}-{1:t}",l.resources=this.eventResources(l),c.push(r.format('<td class="k-scheduler-timecolumn"><div>{0}{1}{2}</div></td><td>{3}</td>',l.tail||l.middle?'<span class="k-icon k-i-arrow-w"></span>':"",this._timeTemplate(l.clone({start:l._startTime||l.start,end:l.endTime||l.end})),l.head||l.middle?'<span class="k-icon k-i-arrow-e"></span>':"",this._eventTemplate(l.clone({showDelete:p})))),h.push('<tr role="row" aria-selected="false"'+(s?' class="k-today">':">")+c.join("")+"</tr>")}return h.join("")},render:function(e){var t,n,o=this.content.find("table").empty(),r=[];e.length>0&&(t=this.groupedResources,t.length?(r=this._createGroupConfiguration(e,t,null),this._renderGroups(r,o,[])):(r=this._tasks(e),o.append(this._renderTaskGroups(r,[])))),n=this._eventsList=i(r),this._angularItems(o,n),this.refreshLayout(),this.trigger("activate")},_angularItems:function(e,t){this.angular("compile",function(){var n=[],i=t.map(function(t){return n.push({dataItem:t}),e.find(".k-task["+r.attr("uid")+"="+t.uid+"]")});return{elements:i,data:n}})},_renderGroups:function(e,t,n){var i,o,r;for(i=0,o=e.length;o>i;i++)r=n.splice(0),r.push(e[i]),e[i].groups?this._renderGroups(e[i].groups,t,r):t.append(this._renderTaskGroups(e[i].items,r))},_createGroupConfiguration:function(e,i,o){var a,l,c,d,u,h,f,p=i[0],g=[],m=p.dataSource.view(),v=this._isMobilePhoneView();for(a=0;m.length>a;a++)l=n(p,m[a]),c=new r.data.Query(e).filter({field:p.field,operator:s.SchedulerView.groupEqFilter(l)}).toArray(),c.length&&(d=this._tasks(c),u=o?"":" k-first",a===m.length-1&&(!o||o.className.indexOf("k-last")>-1)&&(u+=" k-last"),h={text:r.getter(p.dataTextField)(m[a]),value:l,rowSpan:0,className:u},i.length>1?(h.groups=this._createGroupConfiguration(c,i.slice(1),h),o&&(o.rowSpan+=h.rowSpan)):(h.items=d,f=t(h.items),v&&(f+=h.items.length),h.rowSpan=f,o&&(o.rowSpan+=f)),g.push(h));return g},selectionByElement:function(t){var n,i,o;return t=e(t),!t.hasClass("k-scheduler-datecolumn")&&this._eventsList.length?(t.is(".k-task")&&(t=t.closest("td")),this._isMobile()?(o=t.parent(),n=o.parent().children().filter(function(){return e(this).children(":not(.k-scheduler-datecolumn)").length}).index(o)):n=t.parent().index(),i=this._eventsList[n],{index:n,start:i.start,end:i.end,isAllDay:i.isAllDay,uid:i.uid}):void 0},select:function(e){this.clearSelection();var t=this.table.find(".k-task").eq(e.index).closest("tr").addClass("k-state-selected").attr("aria-selected",!0)[0];this.current(t)},move:function(e,t){var n,i=!1,o=e.index;return t==r.keys.UP?(o--,i=!0):t==r.keys.DOWN&&(o++,i=!0),i&&(n=this._eventsList[o],n&&(e.start=n.start,e.end=n.end,e.isAllDay=n.isAllDay,e.events=[n.uid],e.index=o)),i},moveToEvent:function(){return!1},constrainSelection:function(e){var t=this._eventsList[0];t&&(e.start=t.start,e.end=t.end,e.isAllDay=t.isAllDay,e.events=[t.uid],e.index=0)},isInRange:function(){return!0},destroy:function(){this.element&&this.element.off(a),s.SchedulerView.fn.destroy.call(this)},options:{title:"Agenda",name:"agenda",editable:!0,selectedDateFormat:"{0:D}-{1:D}",selectedShortDateFormat:"{0:d} - {1:d}",eventTemplate:"#:title#",eventTimeTemplate:"#if(data.isAllDay) {##=this.options.messages.allDay##} else { ##=kendo.format(format, start, end)## } #",eventDateTemplate:'<strong class="k-scheduler-agendaday">#=kendo.toString(date, "dd")#</strong><em class="k-scheduler-agendaweek">#=kendo.toString(date,"dddd")#</em><span class="k-scheduler-agendadate">#=kendo.toString(date, "y")#</span>',eventGroupTemplate:'<strong class="k-scheduler-adgendagroup">#=value#</strong>',messages:{event:"Event",date:"Date",time:"Time",allDay:"all day"}}})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});