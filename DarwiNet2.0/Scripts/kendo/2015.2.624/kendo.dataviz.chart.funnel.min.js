/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.dataviz.chart.min","./kendo.drawing.min"],e)}(function(){return function(e,t){var n,i=window.kendo,r=i.deepExtend,o=e.extend,a=i.isFunction,s=i.template,l=i.util,c=l.append,d=i.drawing,u=i.geometry,h=i.dataviz,p=i.drawing.Color,f=h.ChartElement,g=h.PieChartMixin,m=h.PlotAreaBase,v=h.PlotAreaFactory,_=h.Point2D,b=h.Box2D,w=h.SeriesBinder,y=h.TextBox,k=h.autoFormat,x=h.evalOptions,C=l.limitValue,S=h.seriesTotal,T="category",D="color",A="funnel",E="value",M="black",I="white",P=m.extend({render:function(){var e=this,t=e.series;e.createFunnelChart(t)},createFunnelChart:function(e){var t=this,n=e[0],i=new F(t,{series:e,legend:t.options.legend,neckRatio:n.neckRatio,dynamicHeight:n.dynamicHeight,dynamicSlope:n.dynamicSlope,segmentSpacing:n.segmentSpacing,highlight:n.highlight});t.appendChart(i)},appendChart:function(e,t){m.fn.appendChart.call(this,e,t),c(this.options.legend.items,e.legendItems)}}),F=f.extend({init:function(e,t){var n=this;f.fn.init.call(n,t),n.plotArea=e,n.points=[],n.labels=[],n.legendItems=[],n.render()},options:{neckRatio:.3,width:300,dynamicSlope:!1,dynamicHeight:!0,segmentSpacing:0,labels:{visible:!1,align:"center",position:"center"}},formatPointValue:function(e,t){return k(t,e.value)},render:function(){var e,n,i,o,s,l,c,d=this,u=d.options,h=d.plotArea.options.seriesColors||[],p=h.length,f=u.series[0],g=f.data;if(g)for(i=S(f),s=0;g.length>s;s++)e=w.current.bindPoint(f,s),o=e.valueFields.value,null!==o&&o!==t&&(n=e.fields,a(f.color)||(f.color=n.color||h[s%p]),n=r({index:s,owner:d,series:f,category:n.category,dataItem:g[s],percentage:Math.abs(o)/i,visibleInLegend:n.visibleInLegend,visible:n.visible},n),l=d.createSegment(o,n),c=d.createLabel(o,n),l&&c&&l.append(c))},evalSegmentOptions:function(e,t,n){var i=n.series;x(e,{value:t,series:i,dataItem:n.dataItem,index:n.index},{defaults:i._defaults,excluded:["data","toggle","visual"]})},createSegment:function(e,i){var a,s=this,l=r({},i.series);return s.evalSegmentOptions(l,e,i),s.createLegendItem(e,l,i),i.visible!==!1?(a=new n(e,l,i),o(a,i),s.append(a),s.points.push(a),a):t},createLabel:function(e,n){var i,o,a,l=this,c=n.series,d=n.dataItem,u=r({},l.options.labels,c.labels),h=e;return u.visible?(u.template?(o=s(u.template),h=o({dataItem:d,value:e,percentage:n.percentage,category:n.category,series:c})):u.format&&(h=k(u.format,h)),u.color||"center"!==u.align||(a=new p(c.color).percBrightness(),u.color=a>180?M:I),l.evalSegmentOptions(u,e,n),i=new y(h,r({vAlign:u.position},u)),l.labels.push(i),i):t},labelPadding:function(){var e,t,n,i,r=this.labels,o={left:0,right:0};for(i=0;r.length>i;i++)e=r[i],t=e.options.align,"center"!==t&&(n=r[i].box.width(),"left"===t?o.left=Math.max(o.left,n):o.right=Math.max(o.right,n));return o},reflow:function(t){var n,i,r,o,a,s,l,c,d,h,p,f,g=this,m=g.options,v=g.points,_=v.length,b=1>=m.neckRatio,w=t.clone().unpad(g.labelPadding()),y=w.width(),k=0,x=b?0:(y-y/m.neckRatio)/2,S=m.segmentSpacing,T=m.dynamicSlope,D=w.height()-S*(_-1),A=b?m.neckRatio*y:y;if(_){if(T)for(l=v[0],c=l,e.each(v,function(e,t){t.percentage>c.percentage&&(c=t)}),r=l.percentage/c.percentage*y,x=(y-r)/2,n=0;_>n;n++)a=v[n].percentage,d=v[n+1],h=d?d.percentage:a,o=v[n].points=[],i=m.dynamicHeight?D*a:D/_,s=(y-r*(h/a))/2,s=C(s,0,y),o.push(new u.Point(w.x1+x,w.y1+k)),o.push(new u.Point(w.x1+y-x,w.y1+k)),o.push(new u.Point(w.x1+y-s,w.y1+i+k)),o.push(new u.Point(w.x1+s,w.y1+i+k)),x=s,k+=i+S,r*=h/a,r=C(r,0,y);else for(p=b?y:y-2*x,f=(p-A)/2,n=0;_>n;n++)o=v[n].points=[],a=v[n].percentage,s=m.dynamicHeight?f*a:f/_,i=m.dynamicHeight?D*a:D/_,o.push(new u.Point(w.x1+x,w.y1+k)),o.push(new u.Point(w.x1+y-x,w.y1+k)),o.push(new u.Point(w.x1+y-x-s,w.y1+i+k)),o.push(new u.Point(w.x1+x+s,w.y1+i+k)),x+=s,k+=i+S;for(n=0;_>n;n++)v[n].reflow(t)}}});r(F.fn,g),n=f.extend({init:function(e,t,n){var i=this;f.fn.init.call(i,t),i.value=e,i.options.index=n.index},options:{color:I,border:{width:1}},reflow:function(e){var t=this,n=t.points,i=t.children[0];t.box=new b(n[0].x,n[0].y,n[1].x,n[2].y),i&&i.reflow(new b(e.x1,n[0].y,e.x2,n[2].y))},createVisual:function(){var e,t,n;f.fn.createVisual.call(this),e=this.options,t=e.border,n=d.Path.fromPoints(this.points,{fill:{color:e.color,opacity:e.opacity},stroke:{color:t.color,opacity:t.opacity,width:t.width}}).close(),this.visual.append(n)},createHighlight:function(e){return d.Path.fromPoints(this.points,e)},highlightVisual:function(){return this.visual.children[0]},highlightVisualArgs:function(){var e=d.Path.fromPoints(this.points).close();return{options:this.options,path:e}},highlightOverlay:function(e,t){var n,i,r,a=this.options,s=a.highlight||{};if(s.visible!==!1)return n=s.border||{},i=o({},t,{fill:s.color,stroke:n.color,strokeOpacity:n.opacity,strokeWidth:n.width,fillOpacity:s.opacity}),r=e.createPolyline(this.points,!0,i)},tooltipAnchor:function(e){var t=this.box;return new _(t.center().x-e/2,t.y1)},formatValue:function(e){var t=this;return t.owner.formatPointValue(t,e)}}),r(n.fn,h.PointEventsMixin),v.current.register(P,[A]),w.current.register([A],[E],[T,D,"visibleInLegend","visible"]),r(h,{FunnelChart:F,FunnelSegment:n})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});