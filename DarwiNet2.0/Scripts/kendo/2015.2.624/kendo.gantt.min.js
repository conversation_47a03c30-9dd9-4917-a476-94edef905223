/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.data.min","./kendo.popup.min","./kendo.window.min","./kendo.resizable.min","./kendo.gantt.list.min","./kendo.gantt.timeline.min","./kendo.grid.min","./kendo.pdf.min"],e)}(function(){return function(e,t){function n(e){return"["+p.attr("uid")+(e?"='"+e+"']":"]")}function i(e){return delete e.name,delete e.prefix,delete e.remove,delete e.edit,delete e.add,delete e.navigate,e}function o(e){var t,n,i,o,r,s;if(e.filter("[name=end], [name=start]").length){for(t=e.attr("name"),n=p.widgetInstance(e,p.ui),i={},o=e;o!==window&&!r;)o=o.parent(),r=o.data("kendoEditable");return(s=r?r.options.model:null)?(i.start=s.start,i.end=s.end,i[t]=n?n.value():p.parseDate(e.value()),i.end>=i.start):!0}return!0}function r(t,n){var i=t.parents("["+p.attr("role")+'="gantt"]'),o=[],r=s(i);t.attr(I,0),n&&r.each(function(t,n){o[t]=e(n).scrollTop()});try{t[0].setActive()}catch(a){t[0].focus()}n&&r.each(function(t,n){e(n).scrollTop(o[t])})}function s(t){return e(t).parentsUntil("body").filter(function(e,t){var n=p.getComputedStyles(t,["overflow"]);return"visible"!=n.overflow}).add(window)}var a,l,c,h,d,u,f,p=window.kendo,g=p.support.browser,m=p.Observable,v=p.ui.Widget,_=p.data.DataSource,y=p.data.ObservableObject,w=p.data.ObservableArray,b=p.data.Query,x=e.isArray,k=e.inArray,C=p.isFunction,S=e.proxy,T=e.extend,D=e.isPlainObject,A=e.map,P=p.keys,M=".kendoGantt",E="p0",I="tabIndex",B="click",L="width",z="string",R={down:{origin:"bottom center",position:"top center"},up:{origin:"top center",position:"bottom center"}},F="aria-activedescendant",O="gantt_active_cell",N="action-option-focused",V=".",H="Are you sure you want to delete this task?",U="Are you sure you want to delete this dependency?",W='<button class="#=styles.button# #=className#" #if (action) {#data-action="#=action#"#}#><span class="#=iconClass#"></span>#=text#</button>',j='<a class="#=className#" #=attr# href="\\#">#=text#</a>',G=p.template('<ul class="#=styles.viewsWrapper#">#for(var view in views){#<li class="#=styles.viewButtonDefault# #=styles.viewButton#-#= view.toLowerCase() #" data-#=ns#name="#=view#"><a href="\\#" class="#=styles.link#">#=views[view].title#</a></li>#}#</ul>'),q=p.template('<div class="#=styles.popupWrapper#"><ul class="#=styles.popupList#" role="listbox">#for(var i = 0, l = actions.length; i < l; i++){#<li class="#=styles.item#" data-action="#=actions[i].data#" role="option">#=actions[i].text#</span>#}#</ul></div>'),$=function(t,n){var i={name:n.field},o=n.model.fields[n.field].validation;o&&D(o)&&o.message&&(i[p.attr("dateCompare-msg")]=o.message),e('<input type="text" required '+p.attr("type")+'="date" '+p.attr("role")+'="datetimepicker" '+p.attr("bind")+'="value:'+n.field+'" '+p.attr("validate")+"='true' />").attr(i).appendTo(t),e("<span "+p.attr("for")+'="'+n.field+'" class="k-invalid-msg"/>').hide().appendTo(t)},Y=function(t,n){e('<a href="#" class="'+n.styles.button+'">'+n.messages.assignButton+"</a>").click(n.click).appendTo(t)},Q={wrapper:"k-widget k-gantt",listWrapper:"k-gantt-layout k-gantt-treelist",list:"k-gantt-treelist",timelineWrapper:"k-gantt-layout k-gantt-timeline",timeline:"k-gantt-timeline",splitBarWrapper:"k-splitbar k-state-default k-splitbar-horizontal k-splitbar-draggable-horizontal k-gantt-layout",splitBar:"k-splitbar",splitBarHover:"k-splitbar-horizontal-hover",popupWrapper:"k-list-container",popupList:"k-list k-reset",resizeHandle:"k-resize-handle",icon:"k-icon",item:"k-item",line:"k-line",buttonDelete:"k-gantt-delete",buttonCancel:"k-gantt-cancel",buttonSave:"k-gantt-update",primary:"k-primary",hovered:"k-state-hover",selected:"k-state-selected",focused:"k-state-focused",gridHeader:"k-grid-header",gridHeaderWrap:"k-grid-header-wrap",gridContent:"k-grid-content",popup:{form:"k-popup-edit-form",editForm:"k-gantt-edit-form",formContainer:"k-edit-form-container",resourcesFormContainer:"k-resources-form-container",message:"k-popup-message",buttonsContainer:"k-edit-buttons k-state-default",button:"k-button",editField:"k-edit-field",editLabel:"k-edit-label",resourcesField:"k-gantt-resources"},toolbar:{headerWrapper:"k-floatwrap k-header k-gantt-toolbar",footerWrapper:"k-floatwrap k-header k-gantt-toolbar",toolbar:"k-gantt-toolbar",views:"k-gantt-views",viewsWrapper:"k-reset k-header k-gantt-views",actions:"k-gantt-actions",button:"k-button k-button-icontext",iconPlus:"k-icon k-i-plus",iconPdf:"k-icon k-i-pdf",viewButtonDefault:"k-state-default",viewButton:"k-view",link:"k-link",pdfButton:"k-gantt-pdf",appendButton:"k-gantt-create"}},X={append:{text:"Add Task",action:"add",className:Q.toolbar.appendButton,iconClass:Q.toolbar.iconPlus},pdf:{text:"Export to PDF",className:Q.toolbar.pdfButton,iconClass:Q.toolbar.iconPdf}},K=m.extend({init:function(e,t){m.fn.init.call(this),this.element=e,this.options=T(!0,{},this.options,t),this._popup()},options:{direction:"down",navigatable:!1},_current:function(e){var t=f.styles,n=this.list.find(V+t.focused),i=n[e]();i.length&&(n.removeClass(t.focused).removeAttr("id"),i.addClass(t.focused).attr("id",N),this.list.find("ul").removeAttr(F).attr(F,N))},_popup:function(){var t=this,n=f.styles,i="li"+V+n.item,o=this.options.messages.actions,r=this.options.navigatable;this.list=e(q({styles:n,actions:[{data:"add",text:o.addChild},{data:"insert-before",text:o.insertBefore},{data:"insert-after",text:o.insertAfter}]})),this.element.append(this.list),this.popup=new p.ui.Popup(this.list,T({anchor:this.element,open:function(){t._adjustListWidth()},animation:this.options.animation},R[this.options.direction])),this.element.on(B+M,V+n.toolbar.appendButton,function(i){var o=e(this),s=o.attr(p.attr("action"));i.preventDefault(),s?t.trigger("command",{type:s}):(t.popup.open(),r&&t.list.find("li:first").addClass(n.focused).attr("id",N).end().find("ul").attr({TABINDEX:0,"aria-activedescendant":N}).focus())}),this.list.find(i).hover(function(){e(this).addClass(n.hovered)},function(){e(this).removeClass(n.hovered)}).end().on(B+M,i,function(){t.trigger("command",{type:e(this).attr(p.attr("action"))}),t.popup.close()}),r&&(this.popup.bind("close",function(){t.list.find(i).removeClass(n.focused).end().find("ul").attr(I,0),t.element.parents("["+p.attr("role")+'="gantt"]').find(V+n.gridContent+" > table:first").focus()}),this.list.find("ul").on("keydown"+M,function(e){var i=e.keyCode;switch(i){case P.UP:e.preventDefault(),t._current("prev");break;case P.DOWN:e.preventDefault(),t._current("next");break;case P.ENTER:t.list.find(V+n.focused).click();break;case P.ESC:e.preventDefault(),t.popup.close()}}))},_adjustListWidth:function(){var e,t,n=this.list,i=n[0].style.width,o=this.element;(n.data(L)||!i)&&(e=window.getComputedStyle?window.getComputedStyle(o[0],null):0,t=e?parseFloat(e.width):o.outerWidth(),e&&(g.mozilla||g.msie)&&(t+=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight)+parseFloat(e.borderLeftWidth)+parseFloat(e.borderRightWidth)),i="border-box"!==n.css("box-sizing")?t-(n.outerWidth()-n.width()):t,n.css({fontFamily:o.css("font-family"),width:i}).data(L,i))},destroy:function(){clearTimeout(this._focusTimeout),this.popup.destroy(),this.element.off(M),this.list.off(M),this.unbind()}}),Z=function(e,t){return function(n){var i,o;if(n=x(i)?{data:n}:n,i=n||{},o=i.data,i.data=o,!(i instanceof e)&&i instanceof _)throw Error("Incorrect DataSource type. Only "+t+" instances are supported");return i instanceof e?i:new e(i)}},J=p.data.Model.define({id:"id",fields:{id:{type:"number"},predecessorId:{type:"number"},successorId:{type:"number"},type:{type:"number"}}}),et=_.extend({init:function(e){_.fn.init.call(this,T(!0,{},{schema:{modelBase:J,model:J}},e))},successors:function(e){return this._dependencies("predecessorId",e)},predecessors:function(e){return this._dependencies("successorId",e)},dependencies:function(e){var t=this.predecessors(e),n=this.successors(e);return t.push.apply(t,n),t},_dependencies:function(e,t){var n=this.view(),i={field:e,operator:"eq",value:t};return n=new b(n).filter(i).toArray()}});et.create=Z(et,"GanttDependencyDataSource"),a=p.data.Model.define({duration:function(){var e=this.end,t=this.start;return e-t},isMilestone:function(){return 0===this.duration()},_offset:function(e){var t,n,i=["start","end"];for(n=0;i.length>n;n++)t=new Date(this.get(i[n]).getTime()+e),this.set(i[n],t)},id:"id",fields:{id:{type:"number"},parentId:{type:"number",defaultValue:null,validation:{required:!0}},orderId:{type:"number",validation:{required:!0}},title:{type:"string",defaultValue:""},start:{type:"date",validation:{required:!0,dateCompare:o,message:"Start date should be before or equal to the end date"}},end:{type:"date",validation:{required:!0,dateCompare:o,message:"End date should be after or equal to the start date"}},percentComplete:{type:"number",validation:{required:!0,min:0,max:1,step:.01}},summary:{type:"boolean"},expanded:{type:"boolean",defaultValue:!0}}}),l=_.extend({init:function(e){_.fn.init.call(this,T(!0,{},{schema:{modelBase:a,model:a}},e))},remove:function(e){var t=e.get("parentId"),n=this.taskAllChildren(e);return this._removeItems(n),e=_.fn.remove.call(this,e),this._childRemoved(t,e.get("orderId")),e},add:function(e){return e?(e=this._toGanttTask(e),this.insert(this.taskSiblings(e).length,e)):t},insert:function(e,n){return n?(n=this._toGanttTask(n),n.set("orderId",e),n=_.fn.insert.call(this,e,n),this._reorderSiblings(n,this.taskSiblings(n).length-1),this._resolveSummaryFields(this.taskParent(n)),n):t},taskChildren:function(e){var n,i=this.view(),o={field:"parentId",operator:"eq",value:null},r=this._sort||{field:"orderId",dir:"asc"};if(e){if(n=e.get("id"),n===t||null===n||""===n)return[];o.value=n}return i=new b(i).filter(o).sort(r).toArray()},taskAllChildren:function(e){var t=[],n=this,i=function(e){var o=n.taskChildren(e);t.push.apply(t,o),A(o,i)};return e?i(e):t=this.view(),t},taskSiblings:function(e){if(!e)return null;var t=this.taskParent(e);return this.taskChildren(t)},taskParent:function(e){return e&&null!==e.get("parentId")?this.get(e.parentId):null},taskLevel:function(e){for(var t=0,n=this.taskParent(e);null!==n;)t+=1,n=this.taskParent(n);return t},taskTree:function(e){var t,n,i,o,r=[],s=this.taskChildren(e);for(n=0,i=s.length;i>n;n++)t=s[n],r.push(t),t.get("expanded")&&(o=this.taskTree(t),r.push.apply(r,o));return r},update:function(e,n){var i,o,r=this,s=function(e,t){var n,i,o=r.taskAllChildren(e);for(n=0,i=o.length;i>n;n++)o[n]._offset(t)},a=function(e){var t=e.field,n=e.sender;switch(t){case"start":r._resolveSummaryStart(r.taskParent(n)),s(n,n.get(t).getTime()-i.getTime());break;case"end":r._resolveSummaryEnd(r.taskParent(n));break;case"percentComplete":r._resolveSummaryPercentComplete(r.taskParent(n));break;case"orderId":r._reorderSiblings(n,i)}};n.parentId!==t&&(i=e.get("parentId"),i!==n.parentId&&(e.set("parentId",n.parentId),r._childRemoved(i,e.get("orderId")),e.set("orderId",r.taskSiblings(e).length-1),r._resolveSummaryFields(r.taskParent(e))),delete n.parentId),e.bind("change",a);for(o in n)i=e.get(o),e.set(o,n[o]);e.unbind("change",a)},_resolveSummaryFields:function(e){e&&(this._updateSummary(e),this.taskChildren(e).length&&(this._resolveSummaryStart(e),this._resolveSummaryEnd(e),this._resolveSummaryPercentComplete(e)))},_resolveSummaryStart:function(e){var t=this,n=function(e){var n,i,o,r=t.taskChildren(e),s=r[0].start.getTime();for(i=1,o=r.length;o>i;i++)n=r[i].start.getTime(),s>n&&(s=n);return new Date(s)};this._updateSummaryRecursive(e,"start",n)},_resolveSummaryEnd:function(e){var t=this,n=function(e){var n,i,o,r=t.taskChildren(e),s=r[0].end.getTime();for(i=1,o=r.length;o>i;i++)n=r[i].end.getTime(),n>s&&(s=n);return new Date(s)};this._updateSummaryRecursive(e,"end",n)},_resolveSummaryPercentComplete:function(e){var t=this,n=function(e){var n=t.taskChildren(e),i=new b(n).aggregate([{field:"percentComplete",aggregate:"average"}]);return i.percentComplete.average};this._updateSummaryRecursive(e,"percentComplete",n)},_updateSummaryRecursive:function(e,t,n){var i,o;e&&(i=n(e),e.set(t,i),o=this.taskParent(e),o&&this._updateSummaryRecursive(o,t,n))},_childRemoved:function(e,t){var n,i,o=null===e?null:this.get(e),r=this.taskChildren(o);for(n=t,i=r.length;i>n;n++)r[n].set("orderId",n);this._resolveSummaryFields(o)},_reorderSiblings:function(e,t){var n,i=e.get("orderId"),o=i>t,r=o?t:i,s=o?i:t,a=o?r:r+1,l=this.taskSiblings(e);for(s=Math.min(s,l.length-1),n=r;s>=n;n++)l[n]!==e&&(l[n].set("orderId",a),a+=1)},_updateSummary:function(e){if(null!==e){var t=this.taskChildren(e).length;e.set("summary",t>0)}},_toGanttTask:function(e){if(!(e instanceof a)){var t=e;e=this._createNewModel(),e.accept(t)}return e}}),l.create=Z(l,"GanttDataSource"),T(!0,p.data,{GanttDataSource:l,GanttTask:a,GanttDependencyDataSource:et,GanttDependency:J}),c={desktop:{dateRange:$,resources:Y}},h=p.Observable.extend({init:function(e,t){p.Observable.fn.init.call(this),this.element=e,this.options=T(!0,{},this.options,t),this.createButton=this.options.createButton},fields:function(t,n){var i,o=this,r=this.options,s=r.messages.editor,a=r.resources,l=function(e){e.preventDefault(),a.editor(o.container.find(V+f.styles.popup.resourcesField),n)};return r.editable.template?i=e.map(n.fields,function(e,t){return{field:t}}):(i=[{field:"title",title:s.title},{field:"start",title:s.start,editor:t.dateRange},{field:"end",title:s.end,editor:t.dateRange},{field:"percentComplete",title:s.percentComplete,format:E}],n.get(a.field)&&i.push({field:a.field,title:s.resources,messages:s,editor:t.resources,click:l,styles:f.styles.popup})),i},_buildEditTemplate:function(e,t,n){var i,o,r,s,a=this.options.resources,l=this.options.editable.template,c=T({},p.Template,this.options.templateSettings),h=c.paramName,d=f.styles.popup,u="";if(l)typeof l===z&&(l=window.unescape(l)),u+=p.template(l,c)(e);else for(i=0,o=t.length;o>i;i++)r=t[i],u+='<div class="'+d.editLabel+'"><label for="'+r.field+'">'+(r.title||r.field||"")+"</label></div>",r.field===a.field&&(u+='<div class="'+d.resourcesField+'" style="display:none"></div>'),!e.editable||e.editable(r.field)?(n.push(r),u+="<div "+p.attr("container-for")+'="'+r.field+'" class="'+d.editField+'"></div>'):(s="#:",r.field?(r=p.expr(r.field,h),s+=r+"==null?'':"+r):s+="''",s+="#",s=p.template(s,c),u+='<div class="'+d.editField+'">'+s(e)+"</div>");return u}}),d=h.extend({destroy:function(){this.close(),this.unbind()},editTask:function(e){this.editable=this._createPopupEditor(e)},close:function(){var e=this,t=function(){e.editable&&(e.editable.destroy(),e.editable=null,e.container=null),e.popup&&(e.popup.destroy(),e.popup=null)};this.editable&&this.container.is(":visible")?this.container.data("kendoWindow").bind("deactivate",t).close():t()},showDialog:function(t){var n,i,o,r,s=t.buttons,a=f.styles.popup,l=p.format('<div class="{0}"><div class="{1}"><p class="{2}">{3}</p><div class="{4}">',a.form,a.formContainer,a.message,t.text,a.buttonsContainer);for(n=0,i=s.length;i>n;n++)l+=this.createButton(s[n]);l+="</div></div></div>",o=this.element,this.popup&&this.popup.destroy(),r=this.popup=e(l).appendTo(o).eq(0).on("click",V+a.button,function(t){t.preventDefault(),r.close();var n=e(t.currentTarget).index();s[n].click()}).kendoWindow({modal:!0,resizable:!1,draggable:!1,title:t.title,visible:!1,close:function(){this.destroy(),o.focus()}}).getKendoWindow(),r.center().open()},_createPopupEditor:function(t){var n,i,o=this,r={},s=this.options.messages,a=f.styles,l=a.popup,h=p.format('<div {0}="{1}" class="{2} {3}"><div class="{4}">',p.attr("uid"),t.uid,l.form,l.editForm,l.formContainer),d=this.fields(c.desktop,t),u=[];return h+=this._buildEditTemplate(t,d,u),h+='<div class="'+l.buttonsContainer+'">',h+=this.createButton({name:"update",text:s.save,className:f.styles.primary}),h+=this.createButton({name:"cancel",text:s.cancel}),h+=this.createButton({name:"delete",text:s.destroy}),h+="</div></div></div>",n=this.container=e(h).appendTo(this.element).eq(0).kendoWindow(T({modal:!0,resizable:!1,draggable:!0,title:s.editor.editorTitle,visible:!1,close:function(e){e.userTriggered&&o.trigger("cancel",{container:n,model:t})&&e.preventDefault()}},r)),i=n.kendoEditable({fields:u,model:t,clearContainer:!1,validateOnBlur:!0,target:o.options.target}).data("kendoEditable"),p.cycleForm(n),this.trigger("edit",{container:n,model:t})?o.trigger("cancel",{container:n,model:t}):(n.data("kendoWindow").center().open(),n.on(B+M,V+a.buttonCancel,function(e){e.preventDefault(),e.stopPropagation(),o.trigger("cancel",{container:n,model:t})}),n.on(B+M,V+a.buttonSave,function(e){var i,r,s,a,l;for(e.preventDefault(),e.stopPropagation(),i=o.fields(c.desktop,t),r={},a=0,l=i.length;l>a;a++)s=i[a].field,r[s]=t.get(s);o.trigger("save",{container:n,model:t,updateInfo:r})}),n.on(B+M,V+a.buttonDelete,function(e){e.preventDefault(),e.stopPropagation(),o.trigger("remove",{container:n,model:t})})),i}}),u=v.extend({init:function(e,t){v.fn.init.call(this,e,t),this.wrapper=this.element,this.model=this.options.model,this.resourcesField=this.options.resourcesField,this.createButton=this.options.createButton,this._initContainer(),this._attachHandlers()},events:["save"],open:function(){this.window.center().open(),this.grid.resize(!0)},close:function(){this.window.bind("deactivate",S(this.destroy,this)).close()},destroy:function(){this._dettachHandlers(),this.grid.destroy(),this.grid=null,this.window.destroy(),this.window=null,v.fn.destroy.call(this),p.destroy(this.wrapper),this.element=this.wrapper=null},_attachHandlers:function(){var t=f.styles,n=this.grid,i=this._cancelProxy=S(this._cancel,this);this.container.on(B+M,V+t.buttonCancel,this._cancelProxy),this._saveProxy=S(this._save,this),this.container.on(B+M,V+t.buttonSave,this._saveProxy),this.window.bind("close",function(e){e.userTriggered&&i(e)}),n.wrapper.on(B+M,"input[type='checkbox']",function(){var t=e(this),i=e(t).closest("tr"),o=n.dataSource.getByUid(i.attr(p.attr("uid"))),r=e(t).is(":checked")?1:"";o.set("value",r)})},_dettachHandlers:function(){this._cancelProxy=null,this._saveProxy=null,this.container.off(M),this.grid.wrapper.off()},_cancel:function(e){e.preventDefault(),this.close()},_save:function(e){e.preventDefault(),this._updateModel(),this.wrapper.is(V+f.styles.popup.resourcesField)||this.trigger("save",{container:this.wrapper,model:this.model}),this.close()},_initContainer:function(){var t=f.styles.popup,n=p.format('<div class="{0} {1}"><div class="{2} {3}"/></div>"',t.form,t.editForm,t.formContainer,t.resourcesFormContainer);n=e(n),this.container=n.find(V+t.resourcesFormContainer),this.window=n.kendoWindow({modal:!0,resizable:!1,draggable:!0,visible:!1,title:this.options.messages.resourcesEditorTitle}).data("kendoWindow"),this._resourceGrid(),this._createButtons()},_resourceGrid:function(){var t=this,n=this.options.messages,i=e('<div id="resources-grid"/>').appendTo(this.container);this.grid=new p.ui.Grid(i,{columns:[{field:"name",title:n.resourcesHeader,template:"<label><input type='checkbox' value='#=name#'# if (value > 0 && value !== null) {#checked='checked'# } #/>#=name#</labe>"},{field:"value",title:n.unitsHeader,template:function(e){var t=e.format,n=null!==e.value?e.value:"";return t?p.toString(n,t):n}}],height:280,sortable:!0,editable:!0,filterable:!0,dataSource:{data:t.options.data,schema:{model:{id:"id",fields:{id:{from:"id"},name:{from:"name",type:"string",editable:!1},value:{from:"value",type:"number",defaultValue:""},format:{from:"format",type:"string"}}}}},save:function(e){var t=!!e.values.value;e.container.parent().find("input[type='checkbox']").prop("checked",t)}})},_createButtons:function(){var e,t,n=this.options.buttons,i='<div class="'+f.styles.popup.buttonsContainer+'">';for(e=0,t=n.length;t>e;e++)i+=this.createButton(n[e]);i+="</div>",this.container.append(i)},_updateModel:function(){var e,t,n,i=[],o=this.grid.dataSource.data();for(t=0,n=o.length;n>t;t++)e=o[t].get("value"),null!==e&&e>0&&i.push(o[t]);this.model[this.resourcesField]=i}}),f=v.extend({init:function(e,t){x(t)&&(t={dataSource:t}),v.fn.init.call(this,e,t),this._wrapper(),this._resources(),this._timeline(),this._toolbar(),this._footer(),this._adjustDimensions(),this._preventRefresh=!0,this.view(this.timeline._selectedViewName),this._preventRefresh=!1,this._dataSource(),this._assignments(),this._dropDowns(),this._list(),this._dependencies(),this._resizable(),this._scrollable(),this._dataBind(),this._attachEvents(),this._createEditor(),p.notify(this)},events:["dataBinding","dataBound","add","edit","remove","cancel","save","change","navigate","moveStart","move","moveEnd","resizeStart","resize","resizeEnd"],options:{name:"Gantt",autoBind:!0,navigatable:!1,selectable:!0,editable:!0,columns:[],views:[],dataSource:{},dependencies:{},resources:{},assignments:{},messages:{save:"Save",cancel:"Cancel",destroy:"Delete",deleteTaskConfirmation:H,deleteDependencyConfirmation:U,deleteTaskWindowTitle:"Delete task",deleteDependencyWindowTitle:"Delete dependency",views:{day:"Day",week:"Week",month:"Month",year:"Year",start:"Start",end:"End"},actions:{append:"Add Task",addChild:"Add Child",insertBefore:"Add Above",insertAfter:"Add Below",pdf:"Export to PDF"},editor:{editorTitle:"Task",resourcesEditorTitle:"Resources",title:"Title",start:"Start",end:"End",percentComplete:"Complete",resources:"Resources",assignButton:"Assign",resourcesHeader:"Resources",unitsHeader:"Units"}},showWorkHours:!0,showWorkDays:!0,toolbar:null,workDayStart:new Date(1980,1,1,8,0,0),workDayEnd:new Date(1980,1,1,17,0,0),workWeekStart:1,workWeekEnd:5,hourSpan:1,snap:!0,height:600,listWidth:"30%"},select:function(e){var n=this.list;return e?(n.select(e),t):n.select()},clearSelection:function(){this.list.clearSelection()},destroy:function(){v.fn.destroy.call(this),this.dataSource&&(this.dataSource.unbind("change",this._refreshHandler),this.dataSource.unbind("progress",this._progressHandler),this.dataSource.unbind("error",this._errorHandler)),this.dependencies&&(this.dependencies.unbind("change",this._dependencyRefreshHandler),this.dependencies.unbind("error",this._dependencyErrorHandler)),this.timeline&&(this.timeline.unbind(),this.timeline.destroy()),this.list&&(this.list.unbind(),this.list.destroy()),this.footerDropDown&&this.footerDropDown.destroy(),this.headerDropDown&&this.headerDropDown.destroy(),this._editor&&this._editor.destroy(),this._resizeDraggable&&this._resizeDraggable.destroy(),this.toolbar.off(M),e(window).off("resize"+M,this._resizeHandler),e(this.wrapper).off(M),this.toolbar=null,this.footer=null},_attachEvents:function(){this._resizeHandler=S(this.resize,this,!1),e(window).on("resize"+M,this._resizeHandler)},_wrapper:function(){var e=f.styles,t=[e.icon,e.resizeHandle].join(" "),n=this.options,i=n.height,o=n.width;this.wrapper=this.element.addClass(e.wrapper).append("<div class='"+e.listWrapper+"'><div></div></div>").append("<div class='"+e.splitBarWrapper+"'><div class='"+t+"'></div></div>").append("<div class='"+e.timelineWrapper+"'><div></div></div>"),this.wrapper.find(V+e.list).width(n.listWidth),i&&this.wrapper.height(i),o&&this.wrapper.width(o)},_toolbar:function(){var t,n,i=this,o=f.styles,r=V+o.toolbar.views+" > li",s=V+o.toolbar.pdfButton,a=o.hovered,l=this.options.toolbar,c=e("<div class='"+o.toolbar.actions+"'>");C(l)||(l=typeof l===z?l:this._actions(l),l=S(p.template(l),this)),n=e(G({ns:p.ns,views:this.timeline.views,styles:o.toolbar})),c.append(l({})),t=e("<div class='"+o.toolbar.headerWrapper+"'>").append(c).append(n),this.wrapper.prepend(t),this.toolbar=t,t.on(B+M,r,function(t){t.preventDefault();var n=e(this).attr(p.attr("name"));i.trigger("navigate",{view:n})||i.view(n)}).on(B+M,s,function(){i.saveAsPDF()}),this.wrapper.find(V+o.toolbar.toolbar+" li").hover(function(){e(this).addClass(a)},function(){e(this).removeClass(a)})},_actions:function(){var e,t,n=this.options,i=n.toolbar,o="";if(!x(i)){if(!n.editable)return o;i=["append"]}for(e=0,t=i.length;t>e;e++)o+=this._createButton(i[e]);return o},_footer:function(){var t,n,i,o,r;this.options.editable&&(t=f.styles.toolbar,n=this.options.messages.actions,i=e(p.template(W)(T(!0,{styles:t},X.append,{text:n.append}))),o=e("<div class='"+t.actions+"'>").append(i),r=e("<div class='"+t.footerWrapper+"'>").append(o),this.wrapper.append(r),this.footer=r)},_createButton:function(e){var t=e.template||W,n=this.options.messages.actions,i=typeof e===z?e:e.name||e.text,o=X[i]?X[i].className:"k-gantt-"+(i||"").replace(/\s/g,""),r={iconClass:"",action:"",text:i,className:o,styles:f.styles.toolbar};if(!(i||D(e)&&e.template))throw Error("Custom commands should have name specified");return r=T(!0,r,X[i],{text:n[i]}),D(e)&&(e.className&&k(r.className,e.className.split(" "))<0&&(e.className+=" "+r.className),r=T(!0,r,e)),p.template(t)(r)},_adjustDimensions:function(){var e=this.element,t=f.styles,n=V+t.list,i=V+t.timeline,o=V+t.splitBar,r=this.toolbar.outerHeight(),s=this.footer?this.footer.outerHeight():0,a=e.height(),l=e.width(),c=e.find(o).outerWidth(),h=e.find(n).outerWidth();e.children([n,i,o].join(",")).height(a-(r+s)).end().children(i).width(l-(c+h))},_scrollTo:function(e){var t=this.timeline.view(),i=p.attr("uid"),o="string"==typeof e?e:e.closest("tr"+n()).attr(i),r=t.content.find(n(o));0!==r.length&&t._scrollTo(r)},_dropDowns:function(){var e=this,t=V+f.styles.toolbar.actions,n=this.options.messages.actions,i=this.dataSource,o=this.timeline,r=function(t){var n,r=t.type,s=i._createNewModel(),a=e.dataItem(e.select()),l=i.taskParent(a),c=o.view()._timeSlots()[0],h="add"===r?a:l;s.set("title","New task"),h?(s.set("parentId",h.get("id")),s.set("start",h.get("start")),s.set("end",h.get("end"))):(s.set("start",c.start),s.set("end",c.end)),"add"!==r&&(n=a.get("orderId"),n="insert-before"===r?n:n+1),e._createTask(s,n)};this.options.editable&&(this.footerDropDown=new K(this.footer.children(t).eq(0),{messages:{actions:n},direction:"up",animation:{open:{effects:"slideIn:up"}},navigatable:e.options.navigatable}),this.headerDropDown=new K(this.toolbar.children(t).eq(0),{messages:{actions:n},navigatable:e.options.navigatable}),this.footerDropDown.bind("command",r),this.headerDropDown.bind("command",r))},_list:function(){var e,t,n=this,i=n.options.navigatable,o=f.styles,s=this.wrapper.find(V+o.list),a=s.find("> div"),l=this.wrapper.find(V+o.toolbar.actions+" > button"),c={columns:this.options.columns||[],dataSource:this.dataSource,selectable:this.options.selectable,editable:this.options.editable,listWidth:s.outerWidth(),resourcesField:this.resources.field},h=c.columns,d=function(){i&&(n._current(n._cachedCurrent),r(n.list.content.find("table"),!0)),delete n._cachedCurrent};for(t=0;h.length>t;t++)e=h[t],e.field===this.resources.field&&"function"!=typeof e.editor&&(e.editor=S(this._createResourceEditor,this));this.list=new p.ui.GanttList(a,c),this.list.bind("render",function(){n._navigatable()},!0).bind("edit",function(e){n._cachedCurrent=e.cell,n.trigger("edit",{task:e.model,container:e.cell})&&e.preventDefault()}).bind("cancel",function(e){n.trigger("cancel",{task:e.model,container:e.cell})&&e.preventDefault(),d()}).bind("update",function(e){n._updateTask(e.task,e.updateInfo),d()}).bind("change",function(){n.trigger("change");var e=n.list.select();e.length?(l.removeAttr("data-action","add"),n.timeline.select("[data-uid='"+e.attr("data-uid")+"']")):(l.attr("data-action","add"),n.timeline.clearSelection())})},_timeline:function(){var e=this,t=f.styles,n=i(T(!0,{resourcesField:this.resources.field},this.options)),o=this.wrapper.find(V+t.timeline+" > div");this.timeline=new p.ui.GanttTimeline(o,n),this.timeline.bind("navigate",function(n){e.toolbar.find(V+t.toolbar.views+" > li").removeClass(t.selected).end().find(V+t.toolbar.viewButton+"-"+n.view.replace(/\./g,"\\.").toLowerCase()).addClass(t.selected),e.refresh()}).bind("moveStart",function(t){e.trigger("moveStart",{task:t.task})&&t.preventDefault()}).bind("move",function(t){var n=t.task,i=t.start,o=new Date(i.getTime()+n.duration());e.trigger("move",{task:n,start:i,end:o})&&t.preventDefault()}).bind("moveEnd",function(t){var n=t.task,i=t.start,o=new Date(i.getTime()+n.duration());e.trigger("moveEnd",{task:n,start:i,end:o})||e._updateTask(e.dataSource.getByUid(n.uid),{start:i,end:o})}).bind("resizeStart",function(t){e.trigger("resizeStart",{task:t.task})&&t.preventDefault()}).bind("resize",function(t){e.trigger("resize",{task:t.task,start:t.start,end:t.end})&&t.preventDefault()}).bind("resizeEnd",function(t){var n=t.task,i={};t.resizeStart?i.start=t.start:i.end=t.end,e.trigger("resizeEnd",{task:n,start:t.start,end:t.end})||e._updateTask(e.dataSource.getByUid(n.uid),i)}).bind("percentResizeEnd",function(t){e._updateTask(e.dataSource.getByUid(t.task.uid),{percentComplete:t.percentComplete})}).bind("dependencyDragEnd",function(t){var n=e.dependencies._createNewModel({type:t.type,predecessorId:t.predecessor.id,successorId:t.successor.id});e._createDependency(n)}).bind("select",function(t){e.select("[data-uid='"+t.uid+"']")}).bind("editTask",function(t){e.editTask(t.uid)}).bind("clear",function(){e.clearSelection()}).bind("removeTask",function(t){e.removeTask(e.dataSource.getByUid(t.uid))}).bind("removeDependency",function(t){e.removeDependency(e.dependencies.getByUid(t.uid))})},_dataSource:function(){var e=this.options,t=e.dataSource;t=x(t)?{data:t}:t,this.dataSource&&this._refreshHandler?this.dataSource.unbind("change",this._refreshHandler).unbind("progress",this._progressHandler).unbind("error",this._errorHandler):(this._refreshHandler=S(this.refresh,this),this._progressHandler=S(this._requestStart,this),this._errorHandler=S(this._error,this)),this.dataSource=p.data.GanttDataSource.create(t).bind("change",this._refreshHandler).bind("progress",this._progressHandler).bind("error",this._errorHandler)},_dependencies:function(){var e=this.options.dependencies||{},t=x(e)?{data:e}:e;this.dependencies&&this._dependencyRefreshHandler?this.dependencies.unbind("change",this._dependencyRefreshHandler).unbind("error",this._dependencyErrorHandler):(this._dependencyRefreshHandler=S(this.refreshDependencies,this),this._dependencyErrorHandler=S(this._error,this)),this.dependencies=p.data.GanttDependencyDataSource.create(t).bind("change",this._dependencyRefreshHandler).bind("error",this._dependencyErrorHandler)},_resources:function(){var e=this.options.resources,t=e.dataSource||{};this.resources={field:"resources",dataTextField:"name",dataColorField:"color",dataFormatField:"format"},T(this.resources,e),this.resources.dataSource=p.data.DataSource.create(t)},_assignments:function(){var e=this.options.assignments,t=e.dataSource||{};this.assignments?this.assignments.dataSource.unbind("change",this._assignmentsRefreshHandler):this._assignmentsRefreshHandler=S(this.refresh,this),this.assignments={dataTaskIdField:"taskId",dataResourceIdField:"resourceId",dataValueField:"value"},T(this.assignments,e),this.assignments.dataSource=p.data.DataSource.create(t),this.assignments.dataSource.bind("change",this._assignmentsRefreshHandler)},_createEditor:function(){var e=this,n=this._editor=new d(this.wrapper,T({},this.options,{target:this,resources:{field:this.resources.field,editor:S(this._createResourceEditor,this)},createButton:S(this._createPopupButton,this)}));n.bind("cancel",function(n){var i=e.dataSource.getByUid(n.model.uid);return e.trigger("cancel",{container:n.container,task:i})?(n.preventDefault(),t):(e.cancelTask(),t)}).bind("edit",function(t){var n=e.dataSource.getByUid(t.model.uid);e.trigger("edit",{container:t.container,task:n})&&t.preventDefault()}).bind("save",function(t){var n=e.dataSource.getByUid(t.model.uid);e.saveTask(n,t.updateInfo)}).bind("remove",function(t){e.removeTask(t.model.uid)})},_createResourceEditor:function(e,t){var n=this,i=t instanceof y?t:t.model,o=i.get("id"),r=this.options.messages,s=n.resources.field,a=this._resourceEditor=new u(e,{resourcesField:s,data:this._wrapResourceData(o),model:i,messages:T({},r.editor),buttons:[{name:"update",text:r.save,className:f.styles.primary},{name:"cancel",text:r.cancel}],createButton:S(this._createPopupButton,this),save:function(e){n._updateAssignments(e.model.get("id"),e.model.get(s))
}});a.open()},_createPopupButton:function(e){var t=e.name||e.text,n={className:f.styles.popup.button+" k-gantt-"+(t||"").replace(/\s/g,""),text:t,attr:""};if(!(t||D(e)&&e.template))throw Error("Custom commands should have name specified");return D(e)&&(e.className&&(e.className+=" "+n.className),n=T(!0,n,e)),p.template(j)(n)},view:function(e){return this.timeline.view(e)},dataItem:function(e){var t,n;return e?(t=this.list,n=t.content.find(e),t._modelFromElement(n)):null},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.list._setDataSource(this.dataSource),this.options.autoBind&&e.fetch()},setDependenciesDataSource:function(e){this.options.dependencies=e,this._dependencies(),this.options.autoBind&&e.fetch()},items:function(){return this.wrapper.children(".k-task")},_updateAssignments:function(e,t){for(var n,i,o,r,s,a,l,c=this.assignments.dataSource,h=this.assignments.dataTaskIdField,d=this.assignments.dataResourceIdField,u=!1,f=new b(c.view()).filter({field:h,operator:"eq",value:e}).toArray();f.length;){for(n=f[0],r=0,s=t.length;s>r;r++)if(i=t[r],n.get(d)===i.get("id")){o=t[r].get("value"),this._updateAssignment(n,o),t.splice(r,1),u=!0;break}u||this._removeAssignment(n),u=!1,f.shift()}for(a=0,l=t.length;l>a;a++)i=t[a],this._createAssignment(i,e);c.sync()},cancelTask:function(){var e=this._editor,t=e.container;t&&e.close()},editTask:function(e){var t,n="string"==typeof e?this.dataSource.getByUid(e):e;n&&(t=this.dataSource._createNewModel(n.toJSON()),t.uid=n.uid,this.cancelTask(),this._editTask(t))},_editTask:function(e){this._editor.editTask(e)},saveTask:function(e,t){var n=this._editor,i=n.container,o=n.editable;i&&o&&o.end()&&this._updateTask(e,t)},_updateTask:function(e,t){var n=this.resources.field;this.trigger("save",{task:e,values:t})||(this._preventRefresh=!0,this.dataSource.update(e,t),t[n]&&this._updateAssignments(e.get("id"),t[n]),this._syncDataSource())},_updateAssignment:function(e,t){var n=this.assignments.dataValueField;e.set(n,t)},removeTask:function(e){var t=this,n="string"==typeof e?this.dataSource.getByUid(e):e;n&&this._taskConfirm(function(e){e||t._removeTask(n)},n)},_createTask:function(e,n){if(!this.trigger("add",{task:e,dependency:null})){var i=this.dataSource;this._preventRefresh=!0,n===t?i.add(e):i.insert(n,e),this._scrollToUid=e.uid,this._syncDataSource()}},_createDependency:function(e){this.trigger("add",{task:null,dependency:e})||(this._preventDependencyRefresh=!0,this.dependencies.add(e),this._preventDependencyRefresh=!1,this.dependencies.sync())},_createAssignment:function(e,t){var n=this.assignments,i=n.dataSource,o=n.dataTaskIdField,r=n.dataResourceIdField,s=n.dataValueField,a=i._createNewModel();a[o]=t,a[r]=e.get("id"),a[s]=e.get("value"),i.add(a)},removeDependency:function(e){var t=this,n="string"==typeof e?this.dependencies.getByUid(e):e;n&&this._dependencyConfirm(function(e){e||t._removeDependency(n)},n)},_removeTaskDependencies:function(e,t){this._preventDependencyRefresh=!0;for(var n=0,i=t.length;i>n;n++)this.dependencies.remove(t[n]);this._preventDependencyRefresh=!1,this.dependencies.sync()},_removeTaskAssignments:function(e){var t,n,i=this.assignments.dataSource,o=i.view(),r={field:this.assignments.dataTaskIdField,operator:"eq",value:e.get("id")};for(o=new b(o).filter(r).toArray(),this._preventRefresh=!0,t=0,n=o.length;n>t;t++)i.remove(o[t]);this._preventRefresh=!1,i.sync()},_removeTask:function(e){var t=this.dependencies.dependencies(e.id);this.trigger("remove",{task:e,dependencies:t})||(this._removeTaskDependencies(e,t),this._removeTaskAssignments(e),this._preventRefresh=!0,this.dataSource.remove(e)&&this._syncDataSource(),this._preventRefresh=!1)},_removeDependency:function(e){this.trigger("remove",{task:null,dependencies:[e]})||this.dependencies.remove(e)&&this.dependencies.sync()},_removeAssignment:function(e){this.assignments.dataSource.remove(e)},_taskConfirm:function(e,t){var n=this.options.messages;this._confirm(e,{model:t,text:n.deleteTaskConfirmation,title:n.deleteTaskWindowTitle})},_dependencyConfirm:function(e,t){var n=this.options.messages;this._confirm(e,{model:t,text:n.deleteDependencyConfirmation,title:n.deleteDependencyWindowTitle})},_confirm:function(e,t){var n,i,o=this.options.editable;o===!0||o.confirmation!==!1?(n=this.options.messages,i=[{name:"delete",text:n.destroy,className:f.styles.primary,click:function(){e()}},{name:"cancel",text:n.cancel,click:function(){e(!0)}}],this.showDialog(T(!0,{},t,{buttons:i}))):e()},showDialog:function(e){this._editor.showDialog(e)},refresh:function(){var e,t,i,o,r,s;this._preventRefresh||this.list.editable||(this._progress(!1),e=this.dataSource,t=e.taskTree(),i=this._scrollToUid,s=-1,this.current&&(r=this.current.closest("tr").attr(p.attr("uid")),s=this.current.index()),this.trigger("dataBinding")||(0!==this.resources.dataSource.data().length&&this._assignResources(t),this._editor&&this._editor.close(),this.clearSelection(),this.list._render(t),this.timeline._render(t),this.timeline._renderDependencies(this.dependencies.view()),i&&(this._scrollTo(i),this.select(n(i))),(i||r)&&s>=0&&(o=this.list.content.find("tr"+n(i||r)+" > td:eq("+s+")"),this._current(o)),this._scrollToUid=null,this.trigger("dataBound")))},refreshDependencies:function(){this._preventDependencyRefresh||this.trigger("dataBinding")||(this.timeline._renderDependencies(this.dependencies.view()),this.trigger("dataBound"))},_assignResources:function(e){var t,n,i=this.resources,o=this.assignments,r=function(){var e=o.dataSource.view(),t={field:o.dataTaskIdField};return e=new b(e).group(t).toArray()},s=r(),a=function(e,t){var n,o,r=e.get("id");for(p.setter(i.field)(e,new w([])),n=0,o=s.length;o>n;n++)s[n].value===r&&t(e,s[n].items)},l=function(e,t){var n,r,s,a,l,c,h,d;for(n=0,r=t.length;r>n;n++)s=t[n],a=i.dataSource.get(s.get(o.dataResourceIdField)),l=s.get(o.dataValueField),c=s.get(o.dataResourceIdField),h=a.get(i.dataFormatField)||E,d=p.toString(l,h),e[i.field].push(new y({id:c,name:a.get(i.dataTextField),color:a.get(i.dataColorField),value:l,formatedValue:d}))};for(t=0,n=e.length;n>t;t++)a(e[t],l)},_wrapResourceData:function(e){var t,n,i,o=this,r=[],s=this.resources.dataSource.view(),a=this.assignments.dataSource.view(),l=new b(a).filter({field:o.assignments.dataTaskIdField,operator:"eq",value:e}).toArray(),c=function(e){var t=null;return new b(l).filter({field:o.assignments.dataResourceIdField,operator:"eq",value:e}).select(function(e){t+=e.get(o.assignments.dataValueField)}),t};for(n=0,i=s.length;i>n;n++)t=s[n],r.push({id:t.get("id"),name:t.get(o.resources.dataTextField),format:t.get(o.resources.dataFormatField)||E,value:c(t.id)});return r},_syncDataSource:function(){this._preventRefresh=!1,this._requestStart(),this.dataSource.sync()},_requestStart:function(){this._progress(!0)},_error:function(){this._progress(!1)},_progress:function(e){p.ui.progress(this.element,e)},_resizable:function(){var t,n,i,o=this,r=this.wrapper,s=f.styles,a=V+s.gridContent,l=r.find(V+s.list),c=r.find(V+s.timeline);this._resizeDraggable=r.find(V+s.splitBar).height(l.height()).hover(function(){e(this).addClass(s.splitBarHover)},function(){e(this).removeClass(s.splitBarHover)}).end().kendoResizable({orientation:"horizontal",handle:V+s.splitBar,start:function(){t=l.width(),n=c.width(),i=c.find(a).scrollLeft()},resize:function(e){var s=e.x.initialDelta;p.support.isRtl(r)&&(s*=-1),0>t+s||0>n-s||(l.width(t+s),c.width(n-s),c.find(a).scrollLeft(i+s),o.timeline.view()._renderCurrentTime())}}).data("kendoResizable")},_scrollable:function(){var t=f.styles,n=V+t.gridContent,i=V+t.gridHeaderWrap,o=this.timeline.element,r=this.list.element;o.find(n).on("scroll",function(){o.find(i).scrollLeft(this.scrollLeft),r.find(n).scrollTop(this.scrollTop)}),r.find(n).on("scroll",function(){r.find(i).scrollLeft(this.scrollLeft)}).on("DOMMouseScroll"+M+" mousewheel"+M,function(t){var i=o.find(n),r=i.scrollTop(),s=p.wheelDeltaY(t);s&&(t.preventDefault(),e(t.currentTarget).one("wheel"+M,!1),i.scrollTop(r+-s))})},_navigatable:function(){var i,o=this,s=this.options.navigatable,a=this.options.editable,l=this.list.header.find("table"),c=this.list.content.find("table"),h=f.styles,d=p.support.isRtl(this.wrapper),u=this.timeline.element.find(V+h.gridContent),g=l.add(c),m=n(),v={collapse:!1,expand:!0},_=function(e){var t=o.timeline.view()._timeSlots()[0].offsetWidth;u.scrollLeft(u.scrollLeft()+(e?-t:t))},y=function(e){var t=o.current.parent("tr"+n()),i=o.current.index(),s=t[e]();0!==o.select().length&&o.clearSelection(),0!==s.length?(o._current(s.children("td:eq("+i+")")),o._scrollTo(o.current)):o.current.is("td")&&"prev"==e?r(l):o.current.is("th")&&"next"==e&&r(c)},w=function(e){var t=o.current[e]();0!==t.length&&(o._current(t),i=o.current.index())},b=function(e){var t=o.dataItem(o.current);t.summary&&t.expanded!==e&&t.set("expanded",e)},x=function(){var e,t;o.options.editable&&!o.list.editable&&(e=o.select(),t=p.attr("uid"),e.length&&o.removeTask(e.attr(t)))};return e(this.wrapper).on("mousedown"+M,"tr"+m+", div"+m+":not("+V+h.line+")",function(t){var i,l=e(t.currentTarget),c=e(t.target).is(":button,a,:input,a>.k-icon,textarea,span.k-icon,span.k-link,.k-input,.k-multiselect-wrap");t.ctrlKey||(s&&(i=l.is("tr")?e(t.target).closest("td"):o.list.content.find("tr"+n(l.attr(p.attr("uid")))+" > td:first"),o._current(i)),!s&&!a||c||(o._focusTimeout=setTimeout(function(){r(o.list.content.find("table"),!0)},2)))}),s!==!0?(c.on("keydown"+M,function(e){e.keyCode==P.DELETE&&x()}),t):(g.on("focus"+M,function(){var t=this===c.get(0)?"td":"th",n=(e(this),o.select()),r=o.current||e(n.length?n:this).find(t+":eq("+(i||0)+")");o._current(r)}).on("blur"+M,function(){o._current(),this==l&&e(this).attr(I,-1)}).on("keydown"+M,function(e){var t,n=e.keyCode;if(o.current)switch(t=o.current.is("td"),n){case P.RIGHT:e.preventDefault(),e.altKey?_():e.ctrlKey?b(d?v.collapse:v.expand):w(d?"prev":"next");break;case P.LEFT:e.preventDefault(),e.altKey?_(!0):e.ctrlKey?b(d?v.expand:v.collapse):w(d?"next":"prev");break;case P.UP:e.preventDefault(),y("prev");break;case P.DOWN:e.preventDefault(),y("next");break;case P.SPACEBAR:e.preventDefault(),t&&o.select(o.current.closest("tr"));break;case P.ENTER:e.preventDefault(),t?o.options.editable&&(o._cachedCurrent=o.current,o.list._startEditHandler(o.current),e.stopPropagation()):o.current.children("a.k-link").click();break;case P.ESC:e.stopPropagation();break;case P.DELETE:t&&x();break;default:n>=49&&57>=n&&o.view(o.timeline._viewByIndex(n-49))}}),t)},_current:function(t){var n,i=f.styles;this.current&&this.current.length&&this.current.removeClass(i.focused).removeAttr("id"),t&&t.length?(this.current=t.addClass(i.focused).attr("id",O),n=e(p._activeElement()),n.is("table")&&this.wrapper.find(n).length>0&&n.removeAttr(F).attr(F,O)):this.current=null},_dataBind:function(){var t,n=this;n.options.autoBind&&(this._preventRefresh=!0,this._preventDependencyRefresh=!0,t=e.map([this.dataSource,this.dependencies,this.resources.dataSource,this.assignments.dataSource],function(e){return e.fetch()}),e.when.apply(null,t).done(function(){n._preventRefresh=!1,n._preventDependencyRefresh=!1,n.refresh()}))},_resize:function(){this._adjustDimensions(),this.timeline.view()._adjustHeight(),this.timeline.view()._renderCurrentTime(),this.list._adjustHeight()}}),p.PDFMixin&&(p.PDFMixin.extend(f.fn),f.fn._drawPDF=function(){var e="."+Q.list,t=this.wrapper.find(e).width(),n=this.wrapper.clone();return n.find(e).css("width",t),this._drawPDFShadow({content:n})}),p.ui.plugin(f),T(!0,f,{styles:Q})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});