/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.list.min","./kendo.mobile.scroller.min"],e)}(function(){return function(e,t){function n(e,t,n){return n?t.substring(0,e).split(n).length-1:0}function i(e,t,i){return t.split(i)[n(e,t,i)]}function r(e,t,i,r){var o=t.split(r);return o.splice(n(e,t,r),1,i),r&&""!==o[o.length-1]&&o.push(""),o.join(r)}var o=window.kendo,a=o.support,s=o.caret,l=o._activeElement,c=a.placeholder,d=o.ui,u=d.List,h=o.keys,p=o.data.DataSource,f="aria-disabled",g="aria-readonly",m="k-state-default",v="disabled",_="readonly",w="k-state-focused",b="k-state-selected",y="k-state-disabled",k="k-state-hover",x=".kendoAutoComplete",C="mouseenter"+x+" mouseleave"+x,S=e.proxy,T=u.extend({init:function(t,n){var i,r,a=this;a.ns=x,n=e.isArray(n)?{dataSource:n}:n,u.fn.init.call(a,t,n),t=a.element,n=a.options,n.placeholder=n.placeholder||t.attr("placeholder"),c&&t.attr("placeholder",n.placeholder),a._wrapper(),a._loader(),a._dataSource(),a._ignoreCase(),t[0].type="text",i=a.wrapper,a._popup(),t.addClass("k-input").on("keydown"+x,S(a._keydown,a)).on("paste"+x,S(a._search,a)).on("focus"+x,function(){a._prev=a._accessor(),a._placeholder(!1),i.addClass(w)}).on("focusout"+x,function(){a._change(),a._placeholder(),i.removeClass(w)}).attr({autocomplete:"off",role:"textbox","aria-haspopup":!0}),a._enable(),a._old=a._accessor(),t[0].id&&t.attr("aria-owns",a.ul[0].id),a._aria(),a._placeholder(),a._initList(),r=e(a.element).parents("fieldset").is(":disabled"),r&&a.enable(!1),o.notify(a)},options:{name:"AutoComplete",enabled:!0,suggest:!1,template:"",groupTemplate:"#:data#",fixedGroupTemplate:"#:data#",dataTextField:"",minLength:1,delay:200,height:200,filter:"startswith",ignoreCase:!0,highlightFirst:!1,separator:null,placeholder:"",animation:{},value:null},_dataSource:function(){var e=this;e.dataSource&&e._refreshHandler?e._unbindDataSource():e._progressHandler=S(e._showBusy,e),e.dataSource=p.create(e.options.dataSource).bind("progress",e._progressHandler)},setDataSource:function(e){this.options.dataSource=e,this._dataSource(),this.listView.setDataSource(this.dataSource)},events:["open","close","change","select","filtering","dataBinding","dataBound"],setOptions:function(e){var t=this._listOptions(e);u.fn.setOptions.call(this,e),t.dataValueField=t.dataTextField,this.listView.setOptions(t),this._accessors(),this._aria()},_editable:function(e){var t=this,n=t.element,i=t.wrapper.off(x),r=e.readonly,o=e.disable;r||o?(i.addClass(o?y:m).removeClass(o?m:y),n.attr(v,o).attr(_,r).attr(f,o).attr(g,r)):(i.addClass(m).removeClass(y).on(C,t._toggleHover),n.removeAttr(v).removeAttr(_).attr(f,!1).attr(g,!1))},close:function(){var e=this,t=e.listView.focus();t&&t.removeClass(b),e.popup.close()},destroy:function(){var e=this;e.element.off(x),e.wrapper.off(x),u.fn.destroy.call(e)},refresh:function(){this.listView.refresh()},select:function(e){this._select(e)},search:function(e){var t,n=this,r=n.options,o=r.ignoreCase,a=r.separator;e=e||n._accessor(),clearTimeout(n._typingTimeout),a&&(e=i(s(n.element)[0],e,a)),t=e.length,(!t||t>=r.minLength)&&(n._open=!0,n.listView.filter(!0),n._filterSource({value:o?e.toLowerCase():e,operator:r.filter,field:r.dataTextField,ignoreCase:o}))},suggest:function(e){var i,r=this,o=r._last,a=r._accessor(),c=r.element[0],d=s(c)[0],p=r.options.separator,f=a.split(p),g=n(d,a,p),m=d;return o==h.BACKSPACE||o==h.DELETE?(r._last=t,t):(e=e||"","string"!=typeof e&&(e[0]&&(e=r.dataSource.view()[u.inArray(e[0],r.ul[0])]),e=e?r._text(e):""),0>=d&&(d=a.toLowerCase().indexOf(e.toLowerCase())+1),i=a.substring(0,d).lastIndexOf(p),i=i>-1?d-(i+p.length):d,a=f[g].substring(0,i),e&&(e=""+e,i=e.toLowerCase().indexOf(a.toLowerCase()),i>-1&&(e=e.substring(i+a.length),m=d+e.length,a+=e),p&&""!==f[f.length-1]&&f.push("")),f[g]=a,r._accessor(f.join(p||"")),c===l()&&s(c,d,m),t)},value:function(e){return e===t?this._accessor():(this.listView.value(e),this._accessor(e),this._old=this._accessor(),t)},_click:function(e){var n=e.item,i=this.element;return this.trigger("select",{item:n})?(this.close(),t):(this._select(n),this._blur(),s(i,i.val().length),t)},_initList:function(){var t=this,n=t.options.virtual,i=!!n,r=S(t._listBound,t),a={autoBind:!1,selectable:!0,dataSource:t.dataSource,click:e.proxy(t._click,this),change:e.proxy(t._listChange,this),activate:S(t._activateItem,t),deactivate:S(t._deactivateItem,t),dataBinding:function(){t.trigger("dataBinding"),t._angularItems("cleanup")},dataBound:r,listBound:r};a=e.extend(t._listOptions(),a,"object"==typeof n?n:{}),a.dataValueField=a.dataTextField,t.listView=i?new o.ui.VirtualList(t.ul,a):new o.ui.StaticList(t.ul,a),t.listView.value(t.options.value)},_listBound:function(){var e,n,i=this,r=i.popup,o=i.options,a=i.dataSource.flatView(),s=a.length,c=i.element[0]===l();i._angularItems("compile"),i.listView.value([]),i.listView.focus(-1),i.listView.filter(!1),i._calculateGroupPadding(i._height(s)),r.position(),s&&(n=this.listView.focus(),o.highlightFirst&&!n&&i.listView.first(),o.suggest&&c&&i.suggest(a[0])),i._open&&(i._open=!1,e=s?"open":"close",i._typingTimeout&&!c&&(e="close"),r[e](),i._typingTimeout=t),i._touchScroller&&i._touchScroller.reset(),i._hideBusy(),i._makeUnselectable(),i.trigger("dataBound")},_listChange:function(){this.listView.filter()||this._selectValue(this.listView.selectedDataItems()[0])},_selectValue:function(e){var t=this.options.separator,n="";e&&(n=this._text(e)),null===n&&(n=""),t&&(n=r(s(this.element)[0],this._accessor(),n,t)),this._prev=n,this._accessor(n),this._placeholder()},_accessor:function(e){var n=this,i=n.element[0];return e===t?(e=i.value,i.className.indexOf("k-readonly")>-1&&e===n.options.placeholder?"":e):(i.value=null===e?"":e,n._placeholder(),t)},_keydown:function(e){var t=this,n=e.keyCode,i=t.popup.visible(),r=this.listView.focus();if(t._last=n,n===h.DOWN)i&&this._move(r?"next":"first"),e.preventDefault();else if(n===h.UP)i&&this._move(r?"prev":"last"),e.preventDefault();else if(n===h.ENTER||n===h.TAB){if(n===h.ENTER&&i&&e.preventDefault(),i&&r){if(t.trigger("select",{item:r}))return;this._select(r)}this._blur()}else n===h.ESC?(i&&e.preventDefault(),t.close()):(t._search(),t._typing=!0)},_move:function(e){this.listView[e](),this.options.suggest&&this.suggest(this.listView.focus())},_hideBusy:function(){var e=this;clearTimeout(e._busy),e._loading.hide(),e.element.attr("aria-busy",!1),e._busy=null},_showBusy:function(){var e=this;e._busy||(e._busy=setTimeout(function(){e.element.attr("aria-busy",!0),e._loading.show()},100))},_placeholder:function(e){if(!c){var n,i=this,r=i.element,o=i.options.placeholder;if(o){if(n=r.val(),e===t&&(e=!n),e||(o=n!==o?n:""),n===i._old&&!e)return;r.toggleClass("k-readonly",e).val(o),o||r[0]!==document.activeElement||s(r[0],0,0)}}},_search:function(){var e=this;clearTimeout(e._typingTimeout),e._typingTimeout=setTimeout(function(){e._prev!==e._accessor()&&(e._prev=e._accessor(),e.search())},e.options.delay)},_select:function(e){this.listView.select(e)},_loader:function(){this._loading=e('<span class="k-icon k-loading" style="display:none"></span>').insertAfter(this.element)},_toggleHover:function(t){e(t.currentTarget).toggleClass(k,"mouseenter"===t.type)},_wrapper:function(){var e,t=this,n=t.element,i=n[0];e=n.parent(),e.is("span.k-widget")||(e=n.wrap("<span />").parent()),e.attr("tabindex",-1),e.attr("role","presentation"),e[0].style.cssText=i.style.cssText,n.css({width:"100%",height:i.style.height}),t._focused=t.element,t.wrapper=e.addClass("k-widget k-autocomplete k-header").addClass(i.className)}});d.plugin(T)}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});