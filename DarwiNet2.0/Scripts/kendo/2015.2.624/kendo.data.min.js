/*
* Kendo UI v2015.2.624 (http://www.telerik.com/kendo-ui)
* Copyright 2015 Telerik AD. All rights reserved.
*
* Kendo UI commercial licenses may be obtained at
* http://www.telerik.com/purchase/license-agreement/kendo-ui-complete
* If you do not own a commercial license, this file shall be governed by the trial license terms.
*/
!function(e,define){define(["./kendo.core.min","./kendo.data.odata.min","./kendo.data.xml.min"],e)}(function(){return function(e,t){function n(e,t,n,i){return function(r){var o,a={};for(o in r)a[o]=r[o];a.field=i?n+"."+r.field:n,t==yt&&e._notifyChange&&e._notifyChange(a),e.trigger(t,a)}}function i(t,n){if(t===n)return!0;var r,o=e.type(t),a=e.type(n);if(o!==a)return!1;if("date"===o)return t.getTime()===n.getTime();if("object"!==o&&"array"!==o)return!1;for(r in t)if(!i(t[r],n[r]))return!1;return!0}function r(e,t){var n,i;for(i in e){if(n=e[i],nt(n)&&n.field&&n.field===t)return n;if(n===t)return n}return null}function o(e){this.data=e||[]}function a(e,n){if(e){var i=typeof e===pt?{field:e,dir:n}:e,r=rt(i)?i:i!==t?[i]:[];return ot(r,function(e){return!!e.dir})}}function s(e){var t,n,i,r,o=e.filters;if(o)for(t=0,n=o.length;n>t;t++)i=o[t],r=i.operator,r&&typeof r===pt&&(i.operator=U[r.toLowerCase()]||r),s(i)}function l(e){return e&&!it(e)?((rt(e)||!e.filters)&&(e={logic:"and",filters:rt(e)?e:[e]}),s(e),e):t}function c(e){return rt(e)?e:[e]}function d(e,n){var i=typeof e===pt?{field:e,dir:n}:e,r=rt(i)?i:i!==t?[i]:[];return L(r,function(e){return{field:e.field,dir:e.dir||"asc",aggregates:e.aggregates}})}function u(e,t){return e&&e.getTime&&t&&t.getTime?e.getTime()===t.getTime():e===t}function h(e,t,n,i,r,o){var a,s,l,c,d;for(t=t||[],c=t.length,a=0;c>a;a++)s=t[a],l=s.aggregate,d=s.field,e[d]=e[d]||{},o[d]=o[d]||{},o[d][l]=o[d][l]||{},e[d][l]=W[l.toLowerCase()](e[d][l],n,ct.accessor(d),i,r,o[d][l])}function p(e){return"number"==typeof e&&!isNaN(e)}function f(e){return e&&e.getTime}function g(e){var t,n=e.length,i=Array(n);for(t=0;n>t;t++)i[t]=e[t].toJSON();return i}function m(e,t,n,i,r){var o,a,s,l,c;for(l=0,c=e.length;c>l;l++){o=e[l];for(a in t)s=r[a],s&&s!==a&&(o[s]=t[a](o),delete o[a])}}function v(e,t,n,i,r){var o,a,s,l,c;for(l=0,c=e.length;c>l;l++){o=e[l];for(a in t)o[a]=n._parse(a,t[a](o)),s=r[a],s&&s!==a&&delete o[s]}}function _(e,t,n,i,r){var o,a,s,l;for(a=0,l=e.length;l>a;a++)o=e[a],s=i[o.field],s&&s!=o.field&&(o.field=s),o.value=n._parse(o.field,o.value),o.hasSubgroups?_(o.items,t,n,i,r):v(o.items,t,n,i,r)}function y(e,t,n,i,r,o){return function(a){return a=e(a),a&&!it(i)&&("[object Array]"===Lt.call(a)||a instanceof Wt||(a=[a]),n(a,i,new t,r,o)),a||[]}}function b(e,t,n,i){for(var r,o,a,s=0;t.length&&i&&(r=t[s],o=r.items,a=o.length,e&&e.field===r.field&&e.value===r.value?(e.hasSubgroups&&e.items.length?b(e.items[e.items.length-1],r.items,n,i):(o=o.slice(n,n+i),e.items=e.items.concat(o)),t.splice(s--,1)):r.hasSubgroups&&o.length?(b(r,o,n,i),r.items.length||t.splice(s--,1)):(o=o.slice(n,n+i),r.items=o,r.items.length||t.splice(s--,1)),0===o.length?n-=a:(n=0,i-=o.length),!(++s>=t.length)););t.length>s&&t.splice(s,t.length-s)}function w(e){var t,n,i,r,o,a=[];for(t=0,n=e.length;n>t;t++)if(o=e.at(t),o.hasSubgroups)a=a.concat(w(o.items));else for(i=o.items,r=0;i.length>r;r++)a.push(i.at(r));return a}function k(e,t){var n,i,r;if(t)for(n=0,i=e.length;i>n;n++)r=e.at(n),r.hasSubgroups?k(r.items,t):r.items=new jt(r.items,t)}function x(e,t){for(var n=0,i=e.length;i>n;n++)if(e[n].hasSubgroups){if(x(e[n].items,t))return!0}else if(t(e[n].items,e[n]))return!0}function C(e,t,n,i){for(var r=0;e.length>r&&e[r].data!==t&&!S(e[r].data,n,i);r++);}function S(e,t,n){for(var i=0,r=e.length;r>i;i++){if(e[i]&&e[i].hasSubgroups)return S(e[i].items,t,n);if(e[i]===t||e[i]===n)return e[i]=n,!0}}function T(e,n,i,r,o){var a,s,l,c;for(a=0,s=e.length;s>a;a++)if(l=e[a],l&&!(l instanceof r))if(l.hasSubgroups===t||o){for(c=0;n.length>c;c++)if(n[c]===l){e[a]=n.at(c),C(i,n,l,e[a]);break}}else T(l.items,n,i,r,o)}function D(e,t){var n,i,r;for(n=0,i=e.length;i>n;n++)if(r=e.at(n),r.uid==t.uid)return e.splice(n,1),r}function A(e,t){return t?I(e,function(e){return e.uid?e.uid==t.uid:e[t.idField]===t.id}):-1}function E(e,t){return t?I(e,function(e){return e.uid==t.uid}):-1}function I(e,t){var n,i;for(n=0,i=e.length;i>n;n++)if(t(e[n]))return n;return-1}function M(e,t){var n,i;return e&&!it(e)?(n=e[t],i=nt(n)?n.from||n.field||t:e[t]||t,dt(i)?t:i):t}function P(e,t){var n,i,r,o={};for(r in e)"filters"!==r&&(o[r]=e[r]);if(e.filters)for(o.filters=[],n=0,i=e.filters.length;i>n;n++)o.filters[n]=P(e.filters[n],t);else o.field=M(t.fields,o.field);return o}function F(e,t){var n,i,r,o,a,s=[];for(n=0,i=e.length;i>n;n++){r={},o=e[n];for(a in o)r[a]=o[a];r.field=M(t.fields,r.field),r.aggregates&&rt(r.aggregates)&&(r.aggregates=F(r.aggregates,t)),s.push(r)}return s}function z(t,n){var i,r,o,a,s,l,c,d,u,h;for(t=e(t)[0],i=t.options,r=n[0],o=n[1],a=[],s=0,l=i.length;l>s;s++)u={},d=i[s],c=d.parentNode,c===t&&(c=null),d.disabled||c&&c.disabled||(c&&(u.optgroup=c.label),u[r.field]=d.text,h=d.attributes.value,h=h&&h.specified?d.value:d.text,u[o.field]=h,a.push(u));return a}function R(t,n){var i,r,o,a,s,l,c,d=e(t)[0].tBodies[0],u=d?d.rows:[],h=n.length,p=[];for(i=0,r=u.length;r>i;i++){for(s={},c=!0,a=u[i].cells,o=0;h>o;o++)l=a[o],"th"!==l.nodeName.toLowerCase()&&(c=!1,s[n[o].field]=l.innerHTML);c||p.push(s)}return p}function H(e){return function(){var t=this._data,n=Y.fn[e].apply(this,Ht.call(arguments));return this._data!=t&&this._attachBubbleHandlers(),n}}function B(t,n){function i(e,t){return e.filter(t).add(e.find(t))}var r,o,a,s,l,c,d,u,h=e(t).children(),p=[],f=n[0].field,g=n[1]&&n[1].field,m=n[2]&&n[2].field,v=n[3]&&n[3].field;for(r=0,o=h.length;o>r;r++)a={_loaded:!0},s=h.eq(r),c=s[0].firstChild,u=s.children(),t=u.filter("ul"),u=u.filter(":not(ul)"),l=s.attr("data-id"),l&&(a.id=l),c&&(a[f]=3==c.nodeType?c.nodeValue:u.text()),g&&(a[g]=i(u,"a").attr("href")),v&&(a[v]=i(u,"img").attr("src")),m&&(d=i(u,".k-sprite").prop("className"),a[m]=d&&e.trim(d.replace("k-sprite",""))),t.length&&(a.items=B(t.eq(0),n)),"true"==s.attr("data-hasChildren")&&(a.hasChildren=!0),p.push(a);return p}var L,O,N,V,U,W,j,q,G,$,Y,Q,K,X,Z,J,et=e.extend,tt=e.proxy,nt=e.isPlainObject,it=e.isEmptyObject,rt=e.isArray,ot=e.grep,at=e.ajax,st=e.each,lt=e.noop,ct=window.kendo,dt=ct.isFunction,ut=ct.Observable,ht=ct.Class,pt="string",ft="function",gt="create",mt="read",vt="update",_t="destroy",yt="change",bt="sync",wt="get",kt="error",xt="requestStart",Ct="progress",St="requestEnd",Tt=[gt,mt,vt,_t],Dt=function(e){return e},At=ct.getter,Et=ct.stringify,It=Math,Mt=[].push,Pt=[].join,Ft=[].pop,zt=[].splice,Rt=[].shift,Ht=[].slice,Bt=[].unshift,Lt={}.toString,Ot=ct.support.stableSort,Nt=/^\/Date\((.*?)\)\/$/,Vt=/(\r+|\n+)/g,Ut=/(?=['\\])/g,Wt=ut.extend({init:function(e,t){var n=this;n.type=t||qt,ut.fn.init.call(n),n.length=e.length,n.wrapAll(e,n)},at:function(e){return this[e]},toJSON:function(){var e,t,n=this.length,i=Array(n);for(e=0;n>e;e++)t=this[e],t instanceof qt&&(t=t.toJSON()),i[e]=t;return i},parent:lt,wrapAll:function(e,t){var n,i,r=this,o=function(){return r};for(t=t||[],n=0,i=e.length;i>n;n++)t[n]=r.wrap(e[n],o);return t},wrap:function(e,t){var n,i=this;return null!==e&&"[object Object]"===Lt.call(e)&&(n=e instanceof i.type||e instanceof Yt,n||(e=e instanceof qt?e.toJSON():e,e=new i.type(e)),e.parent=t,e.bind(yt,function(e){i.trigger(yt,{field:e.field,node:e.node,index:e.index,items:e.items||[this],action:e.node?e.action||"itemloaded":"itemchange"})})),e},push:function(){var e,t=this.length,n=this.wrapAll(arguments);return e=Mt.apply(this,n),this.trigger(yt,{action:"add",index:t,items:n}),e},slice:Ht,sort:[].sort,join:Pt,pop:function(){var e=this.length,t=Ft.apply(this);return e&&this.trigger(yt,{action:"remove",index:e-1,items:[t]}),t},splice:function(e,t,n){var i,r,o,a=this.wrapAll(Ht.call(arguments,2));if(i=zt.apply(this,[e,t].concat(a)),i.length)for(this.trigger(yt,{action:"remove",index:e,items:i}),r=0,o=i.length;o>r;r++)i[r]&&i[r].children&&i[r].unbind(yt);return n&&this.trigger(yt,{action:"add",index:e,items:a}),i},shift:function(){var e=this.length,t=Rt.apply(this);return e&&this.trigger(yt,{action:"remove",index:0,items:[t]}),t},unshift:function(){var e,t=this.wrapAll(arguments);return e=Bt.apply(this,t),this.trigger(yt,{action:"add",index:0,items:t}),e},indexOf:function(e){var t,n,i=this;for(t=0,n=i.length;n>t;t++)if(i[t]===e)return t;return-1},forEach:function(e){for(var t=0,n=this.length;n>t;t++)e(this[t],t,this)},map:function(e){for(var t=0,n=[],i=this.length;i>t;t++)n[t]=e(this[t],t,this);return n},reduce:function(e){var t,n=0,i=this.length;for(2==arguments.length?t=arguments[1]:i>n&&(t=this[n++]);i>n;n++)t=e(t,this[n],n,this);return t},reduceRight:function(e){var t,n=this.length-1;for(2==arguments.length?t=arguments[1]:n>0&&(t=this[n--]);n>=0;n--)t=e(t,this[n],n,this);return t},filter:function(e){for(var t,n=0,i=[],r=this.length;r>n;n++)t=this[n],e(t,n,this)&&(i[i.length]=t);return i},find:function(e){for(var t,n=0,i=this.length;i>n;n++)if(t=this[n],e(t,n,this))return t},every:function(e){for(var t,n=0,i=this.length;i>n;n++)if(t=this[n],!e(t,n,this))return!1;return!0},some:function(e){for(var t,n=0,i=this.length;i>n;n++)if(t=this[n],e(t,n,this))return!0;return!1},remove:function(e){var t=this.indexOf(e);-1!==t&&this.splice(t,1)},empty:function(){this.splice(0,this.length)}}),jt=Wt.extend({init:function(e,t){ut.fn.init.call(this),this.type=t||qt;for(var n=0;e.length>n;n++)this[n]=e[n];this.length=n,this._parent=tt(function(){return this},this)},at:function(e){var t=this[e];return t instanceof this.type?t.parent=this._parent:t=this[e]=this.wrap(t,this._parent),t}}),qt=ut.extend({init:function(e){var t,n,i=this,r=function(){return i};ut.fn.init.call(this);for(n in e)t=e[n],"object"==typeof t&&t&&!t.getTime&&"_"!=n.charAt(0)&&(t=i.wrap(t,n,r)),i[n]=t;i.uid=ct.guid()},shouldSerialize:function(e){return this.hasOwnProperty(e)&&"_events"!==e&&typeof this[e]!==ft&&"uid"!==e},forEach:function(e){for(var t in this)this.shouldSerialize(t)&&e(this[t],t)},toJSON:function(){var e,t,n={};for(t in this)this.shouldSerialize(t)&&(e=this[t],(e instanceof qt||e instanceof Wt)&&(e=e.toJSON()),n[t]=e);return n},get:function(e){var t,n=this;return n.trigger(wt,{field:e}),t="this"===e?n:ct.getter(e,!0)(n)},_set:function(e,t){var n,i,r,o=this,a=e.indexOf(".")>=0;if(a)for(n=e.split("."),i="";n.length>1;){if(i+=n.shift(),r=ct.getter(i,!0)(o),r instanceof qt)return r.set(n.join("."),t),a;i+="."}return ct.setter(e)(o,t),a},set:function(e,t){var n=this,i=e.indexOf(".")>=0,r=ct.getter(e,!0)(n);r!==t&&(n.trigger("set",{field:e,value:t})||(i||(t=n.wrap(t,e,function(){return n})),(!n._set(e,t)||e.indexOf("(")>=0||e.indexOf("[")>=0)&&n.trigger(yt,{field:e})))},parent:lt,wrap:function(e,t,i){var r,o,a=this,s=Lt.call(e);return null==e||"[object Object]"!==s&&"[object Array]"!==s||(r=e instanceof Wt,o=e instanceof Y,"[object Object]"!==s||o||r?("[object Array]"===s||r||o)&&(r||o||(e=new Wt(e)),e.parent()!=i()&&e.bind(yt,n(a,yt,t,!1))):(e instanceof qt||(e=new qt(e)),e.parent()!=i()&&(e.bind(wt,n(a,wt,t,!0)),e.bind(yt,n(a,yt,t,!0)))),e.parent=i),e}}),Gt={number:function(e){return ct.parseFloat(e)},date:function(e){return ct.parseDate(e)},"boolean":function(e){return typeof e===pt?"true"===e.toLowerCase():null!=e?!!e:e},string:function(e){return null!=e?e+"":e},"default":function(e){return e}},$t={string:"",number:0,date:new Date,"boolean":!1,"default":""},Yt=qt.extend({init:function(n){var i,r,o=this;if((!n||e.isEmptyObject(n))&&(n=e.extend({},o.defaults,n),o._initializers))for(i=0;o._initializers.length>i;i++)r=o._initializers[i],n[r]=o.defaults[r]();qt.fn.init.call(o,n),o.dirty=!1,o.idField&&(o.id=o.get(o.idField),o.id===t&&(o.id=o._defaultId))},shouldSerialize:function(e){return qt.fn.shouldSerialize.call(this,e)&&"uid"!==e&&!("id"!==this.idField&&"id"===e)&&"dirty"!==e&&"_accessors"!==e},_parse:function(e,t){var n,i=this,o=e,a=i.fields||{};return e=a[e],e||(e=r(a,o)),e&&(n=e.parse,!n&&e.type&&(n=Gt[e.type.toLowerCase()])),n?n(t):t},_notifyChange:function(e){var t=e.action;("add"==t||"remove"==t)&&(this.dirty=!0)},editable:function(e){return e=(this.fields||{})[e],e?e.editable!==!1:!0},set:function(e,t,n){var r=this;r.editable(e)&&(t=r._parse(e,t),i(t,r.get(e))||(r.dirty=!0,qt.fn.set.call(r,e,t,n)))},accept:function(e){var t,n,i=this,r=function(){return i};for(t in e)n=e[t],"_"!=t.charAt(0)&&(n=i.wrap(e[t],t,r)),i._set(t,n);i.idField&&(i.id=i.get(i.idField)),i.dirty=!1},isNew:function(){return this.id===this._defaultId}});Yt.define=function(e,n){n===t&&(n=e,e=Yt);var i,r,o,a,s,l,c,d,u=et({defaults:{}},n),h={},p=u.id,f=[];if(p&&(u.idField=p),u.id&&delete u.id,p&&(u.defaults[p]=u._defaultId=""),"[object Array]"===Lt.call(u.fields)){for(l=0,c=u.fields.length;c>l;l++)o=u.fields[l],typeof o===pt?h[o]={}:o.field&&(h[o.field]=o);u.fields=h}for(r in u.fields)o=u.fields[r],a=o.type||"default",s=null,d=r,r=typeof o.field===pt?o.field:r,o.nullable||(s=u.defaults[d!==r?d:r]=o.defaultValue!==t?o.defaultValue:$t[a.toLowerCase()],"function"==typeof s&&f.push(r)),n.id===r&&(u._defaultId=s),u.defaults[d!==r?d:r]=s,o.parse=o.parse||Gt[a];return f.length>0&&(u._initializers=f),i=e.extend(u),i.define=function(e){return Yt.define(i,e)},u.fields&&(i.fields=u.fields,i.idField=u.idField),i},O={selector:function(e){return dt(e)?e:At(e)},compare:function(e){var t=this.selector(e);return function(e,n){return e=t(e),n=t(n),null==e&&null==n?0:null==e?-1:null==n?1:e.localeCompare?e.localeCompare(n):e>n?1:n>e?-1:0}},create:function(e){var t=e.compare||this.compare(e.field);return"desc"==e.dir?function(e,n){return t(n,e,!0)}:t},combine:function(e){return function(t,n){var i,r,o=e[0](t,n);for(i=1,r=e.length;r>i;i++)o=o||e[i](t,n);return o}}},N=et({},O,{asc:function(e){var t=this.selector(e);return function(e,n){var i=t(e),r=t(n);return i&&i.getTime&&r&&r.getTime&&(i=i.getTime(),r=r.getTime()),i===r?e.__position-n.__position:null==i?-1:null==r?1:i.localeCompare?i.localeCompare(r):i>r?1:-1}},desc:function(e){var t=this.selector(e);return function(e,n){var i=t(e),r=t(n);return i&&i.getTime&&r&&r.getTime&&(i=i.getTime(),r=r.getTime()),i===r?e.__position-n.__position:null==i?1:null==r?-1:r.localeCompare?r.localeCompare(i):r>i?1:-1}},create:function(e){return this[e.dir](e.field)}}),L=function(e,t){var n,i=e.length,r=Array(i);for(n=0;i>n;n++)r[n]=t(e[n],n,e);return r},V=function(){function e(e){return e.replace(Ut,"\\").replace(Vt,"")}function t(t,n,i,r){var o;return null!=i&&(typeof i===pt&&(i=e(i),o=Nt.exec(i),o?i=new Date(+o[1]):r?(i="'"+i.toLowerCase()+"'",n="("+n+" || '').toLowerCase()"):i="'"+i+"'"),i.getTime&&(n="("+n+"?"+n+".getTime():"+n+")",i=i.getTime())),n+" "+t+" "+i}return{quote:function(t){return t&&t.getTime?"new Date("+t.getTime()+")":"string"==typeof t?"'"+e(t)+"'":""+t},eq:function(e,n,i){return t("==",e,n,i)},neq:function(e,n,i){return t("!=",e,n,i)},gt:function(e,n,i){return t(">",e,n,i)},gte:function(e,n,i){return t(">=",e,n,i)},lt:function(e,n,i){return t("<",e,n,i)},lte:function(e,n,i){return t("<=",e,n,i)},startswith:function(t,n,i){return i&&(t="("+t+" || '').toLowerCase()",n&&(n=n.toLowerCase())),n&&(n=e(n)),t+".lastIndexOf('"+n+"', 0) == 0"},endswith:function(t,n,i){return i&&(t="("+t+" || '').toLowerCase()",n&&(n=n.toLowerCase())),n&&(n=e(n)),t+".indexOf('"+n+"', "+t+".length - "+(n||"").length+") >= 0"},contains:function(t,n,i){return i&&(t="("+t+" || '').toLowerCase()",n&&(n=n.toLowerCase())),n&&(n=e(n)),t+".indexOf('"+n+"') >= 0"},doesnotcontain:function(t,n,i){return i&&(t="("+t+" || '').toLowerCase()",n&&(n=n.toLowerCase())),n&&(n=e(n)),t+".indexOf('"+n+"') == -1"}}}(),o.filterExpr=function(e){var n,i,r,a,s,l,c=[],d={and:" && ",or:" || "},u=[],h=[],p=e.filters;for(n=0,i=p.length;i>n;n++)r=p[n],s=r.field,l=r.operator,r.filters?(a=o.filterExpr(r),r=a.expression.replace(/__o\[(\d+)\]/g,function(e,t){return t=+t,"__o["+(h.length+t)+"]"}).replace(/__f\[(\d+)\]/g,function(e,t){return t=+t,"__f["+(u.length+t)+"]"}),h.push.apply(h,a.operators),u.push.apply(u,a.fields)):(typeof s===ft?(a="__f["+u.length+"](d)",u.push(s)):a=ct.expr(s),typeof l===ft?(r="__o["+h.length+"]("+a+", "+V.quote(r.value)+")",h.push(l)):r=V[(l||"eq").toLowerCase()](a,r.value,r.ignoreCase!==t?r.ignoreCase:!0)),c.push(r);return{expression:"("+c.join(d[e.logic])+")",fields:u,operators:h}},U={"==":"eq",equals:"eq",isequalto:"eq",equalto:"eq",equal:"eq","!=":"neq",ne:"neq",notequals:"neq",isnotequalto:"neq",notequalto:"neq",notequal:"neq","<":"lt",islessthan:"lt",lessthan:"lt",less:"lt","<=":"lte",le:"lte",islessthanorequalto:"lte",lessthanequal:"lte",">":"gt",isgreaterthan:"gt",greaterthan:"gt",greater:"gt",">=":"gte",isgreaterthanorequalto:"gte",greaterthanequal:"gte",ge:"gte",notsubstringof:"doesnotcontain"},o.normalizeFilter=l,o.prototype={toArray:function(){return this.data},range:function(e,t){return new o(this.data.slice(e,e+t))},skip:function(e){return new o(this.data.slice(e))},take:function(e){return new o(this.data.slice(0,e))},select:function(e){return new o(L(this.data,e))},order:function(e,t){var n={dir:t};return e&&(e.compare?n.compare=e.compare:n.field=e),new o(this.data.slice(0).sort(O.create(n)))},orderBy:function(e){return this.order(e,"asc")},orderByDescending:function(e){return this.order(e,"desc")},sort:function(e,t,n){var i,r,o=a(e,t),s=[];if(n=n||O,o.length){for(i=0,r=o.length;r>i;i++)s.push(n.create(o[i]));return this.orderBy({compare:n.combine(s)})}return this},filter:function(e){var t,n,i,r,a,s,c,d,u=this.data,h=[];if(e=l(e),!e||0===e.filters.length)return this;for(r=o.filterExpr(e),s=r.fields,c=r.operators,a=d=Function("d, __f, __o","return "+r.expression),(s.length||c.length)&&(d=function(e){return a(e,s,c)}),t=0,i=u.length;i>t;t++)n=u[t],d(n)&&h.push(n);return new o(h)},group:function(e,t){e=d(e||[]),t=t||this.data;var n,i=this,r=new o(i.data);return e.length>0&&(n=e[0],r=r.groupBy(n).select(function(i){var r=new o(t).filter([{field:i.field,operator:"eq",value:i.value,ignoreCase:!1}]);return{field:i.field,value:i.value,items:e.length>1?new o(i.items).group(e.slice(1),r.toArray()).toArray():i.items,hasSubgroups:e.length>1,aggregates:r.aggregate(n.aggregates)}})),r},groupBy:function(e){if(it(e)||!this.data.length)return new o([]);var t,n,i,r,a=e.field,s=this._sortForGrouping(a,e.dir||"asc"),l=ct.accessor(a),c=l.get(s[0],a),d={field:a,value:c,items:[]},h=[d];for(i=0,r=s.length;r>i;i++)t=s[i],n=l.get(t,a),u(c,n)||(c=n,d={field:a,value:c,items:[]},h.push(d)),d.items.push(t);return new o(h)},_sortForGrouping:function(e,t){var n,i,r=this.data;if(!Ot){for(n=0,i=r.length;i>n;n++)r[n].__position=n;for(r=new o(r).sort(e,t,N).toArray(),n=0,i=r.length;i>n;n++)delete r[n].__position;return r}return this.sort(e,t).toArray()},aggregate:function(e){var t,n,i={},r={};if(e&&e.length)for(t=0,n=this.data.length;n>t;t++)h(i,e,this.data[t],t,n,r);return i}},W={sum:function(e,t,n){var i=n.get(t);return p(e)?p(i)&&(e+=i):e=i,e},count:function(e){return(e||0)+1},average:function(e,n,i,r,o,a){var s=i.get(n);return a.count===t&&(a.count=0),p(e)?p(s)&&(e+=s):e=s,p(s)&&a.count++,r==o-1&&p(e)&&(e/=a.count),e},max:function(e,t,n){var i=n.get(t);return p(e)||f(e)||(e=i),i>e&&(p(i)||f(i))&&(e=i),e},min:function(e,t,n){var i=n.get(t);return p(e)||f(e)||(e=i),e>i&&(p(i)||f(i))&&(e=i),e}},o.process=function(e,n){n=n||{};var i,r=new o(e),s=n.group,l=d(s||[]).concat(a(n.sort||[])),c=n.filterCallback,u=n.filter,h=n.skip,p=n.take;return u&&(r=r.filter(u),c&&(r=c(r)),i=r.toArray().length),l&&(r=r.sort(l),s&&(e=r.toArray())),h!==t&&p!==t&&(r=r.range(h,p)),s&&(r=r.group(s,e)),{total:i,data:r.toArray()}},j=ht.extend({init:function(e){this.data=e.data},read:function(e){e.success(this.data)},update:function(e){e.success(e.data)},create:function(e){e.success(e.data)},destroy:function(e){e.success(e.data)}}),q=ht.extend({init:function(e){var t,n=this;e=n.options=et({},n.options,e),st(Tt,function(t,n){typeof e[n]===pt&&(e[n]={url:e[n]})}),n.cache=e.cache?G.create(e.cache):{find:lt,add:lt},t=e.parameterMap,dt(e.push)&&(n.push=e.push),n.push||(n.push=Dt),n.parameterMap=dt(t)?t:function(e){var n={};return st(e,function(e,i){e in t&&(e=t[e],nt(e)&&(i=e.value(i),e=e.key)),n[e]=i}),n}},options:{parameterMap:Dt},create:function(e){return at(this.setup(e,gt))},read:function(n){var i,r,o,a=this,s=a.cache;n=a.setup(n,mt),i=n.success||lt,r=n.error||lt,o=s.find(n.data),o!==t?i(o):(n.success=function(e){s.add(n.data,e),i(e)},e.ajax(n))},update:function(e){return at(this.setup(e,vt))},destroy:function(e){return at(this.setup(e,_t))},setup:function(e,t){e=e||{};var n,i=this,r=i.options[t],o=dt(r.data)?r.data(e.data):r.data;return e=et(!0,{},r,e),n=et(!0,{},o,e.data),e.data=i.parameterMap(n,t),dt(e.url)&&(e.url=e.url(n)),e}}),G=ht.extend({init:function(){this._store={}},add:function(e,n){e!==t&&(this._store[Et(e)]=n)},find:function(e){return this._store[Et(e)]},clear:function(){this._store={}},remove:function(e){delete this._store[Et(e)]}}),G.create=function(e){var t={inmemory:function(){return new G}};return nt(e)&&dt(e.find)?e:e===!0?new G:t[e]()},$=ht.extend({init:function(e){var t,n,i,r,o,a,s,l,c,d,u,h,p,f=this;e=e||{};for(t in e)n=e[t],f[t]=typeof n===pt?At(n):n;r=e.modelBase||Yt,nt(f.model)&&(f.model=i=r.define(f.model)),o=tt(f.data,f),f._dataAccessFunction=o,f.model&&(a=tt(f.groups,f),s=tt(f.serialize,f),l={},c={},d={},u={},h=!1,i=f.model,i.fields&&(st(i.fields,function(e,t){var n;p=e,nt(t)&&t.field?p=t.field:typeof t===pt&&(p=t),nt(t)&&t.from&&(n=t.from),h=h||n&&n!==e||p!==e,c[e]=At(n||p),d[e]=At(e),l[n||p]=e,u[e]=n||p}),!e.serialize&&h&&(f.serialize=y(s,i,m,d,l,u))),f._dataAccessFunction=o,f.data=y(o,i,v,c,l,u),f.groups=y(a,i,_,c,l,u))},errors:function(e){return e?e.errors:null},parse:Dt,data:Dt,total:function(e){return e.length},groups:Dt,aggregates:function(){return{}},serialize:function(e){return e}}),Y=ut.extend({init:function(e){var n,i,r,o=this;e&&(i=e.data),e=o.options=et({},o.options,e),o._map={},o._prefetch={},o._data=[],o._pristineData=[],o._ranges=[],o._view=[],o._pristineTotal=0,o._destroyed=[],o._pageSize=e.pageSize,o._page=e.page||(e.pageSize?1:t),o._sort=a(e.sort),o._filter=l(e.filter),o._group=d(e.group),o._aggregate=e.aggregate,o._total=e.total,o._shouldDetachObservableParents=!0,ut.fn.init.call(o),o.transport=Q.create(e,i,o),dt(o.transport.push)&&o.transport.push({pushCreate:tt(o._pushCreate,o),pushUpdate:tt(o._pushUpdate,o),pushDestroy:tt(o._pushDestroy,o)}),null!=e.offlineStorage&&("string"==typeof e.offlineStorage?(r=e.offlineStorage,o._storage={getItem:function(){return JSON.parse(localStorage.getItem(r))},setItem:function(e){localStorage.setItem(r,Et(o.reader.serialize(e)))}}):o._storage=e.offlineStorage),o.reader=new ct.data.readers[e.schema.type||"json"](e.schema),n=o.reader.model||{},o._detachObservableParents(),o._data=o._observe(o._data),o._online=!0,o.bind(["push",kt,yt,xt,bt,St,Ct],e)},options:{data:null,schema:{modelBase:Yt},offlineStorage:null,serverSorting:!1,serverPaging:!1,serverFiltering:!1,serverGrouping:!1,serverAggregates:!1,batch:!1},online:function(n){return n!==t?this._online!=n&&(this._online=n,n)?this.sync():e.Deferred().resolve().promise():this._online},offlineData:function(e){return null==this.options.offlineStorage?null:e!==t?this._storage.setItem(e):this._storage.getItem()||{}},_isServerGrouped:function(){var e=this.group()||[];return this.options.serverGrouping&&e.length},_pushCreate:function(e){this._push(e,"pushCreate")},_pushUpdate:function(e){this._push(e,"pushUpdate")},_pushDestroy:function(e){this._push(e,"pushDestroy")},_push:function(e,t){var n=this._readData(e);n||(n=e),this[t](n)},_flatData:function(e,t){if(e){if(this._isServerGrouped())return w(e);if(!t)for(var n=0;e.length>n;n++)e.at(n)}return e},parent:lt,get:function(e){var t,n,i=this._flatData(this._data);for(t=0,n=i.length;n>t;t++)if(i[t].id==e)return i[t]},getByUid:function(e){var t,n,i=this._flatData(this._data);if(i)for(t=0,n=i.length;n>t;t++)if(i[t].uid==e)return i[t]},indexOf:function(e){return E(this._data,e)},at:function(e){return this._data.at(e)},data:function(e){var n,i=this;if(e===t){if(i._data)for(n=0;i._data.length>n;n++)i._data.at(n);return i._data}i._detachObservableParents(),i._data=this._observe(e),i._pristineData=e.slice(0),i._storeData(),i._ranges=[],i.trigger("reset"),i._addRange(i._data),i._total=i._data.length,i._pristineTotal=i._total,i._process(i._data)},view:function(e){return e===t?this._view:(this._view=this._observeView(e),t)},_observeView:function(e){var t,n=this;return T(e,n._data,n._ranges,n.reader.model||qt,n._isServerGrouped()),t=new jt(e,n.reader.model),t.parent=function(){return n.parent()},t},flatView:function(){var e=this.group()||[];return e.length?w(this._view):this._view},add:function(e){return this.insert(this._data.length,e)},_createNewModel:function(e){return this.reader.model?new this.reader.model(e):e instanceof qt?e:new qt(e)},insert:function(e,t){return t||(t=e,e=0),t instanceof Yt||(t=this._createNewModel(t)),this._isServerGrouped()?this._data.splice(e,0,this._wrapInEmptyGroup(t)):this._data.splice(e,0,t),t},pushCreate:function(e){var t,n,i,r,o,a;rt(e)||(e=[e]),t=[],n=this.options.autoSync,this.options.autoSync=!1;try{for(i=0;e.length>i;i++)r=e[i],o=this.add(r),t.push(o),a=o.toJSON(),this._isServerGrouped()&&(a=this._wrapInEmptyGroup(a)),this._pristineData.push(a)}finally{this.options.autoSync=n}t.length&&this.trigger("push",{type:"create",items:t})},pushUpdate:function(e){var t,n,i,r,o;for(rt(e)||(e=[e]),t=[],n=0;e.length>n;n++)i=e[n],r=this._createNewModel(i),o=this.get(r.id),o?(t.push(o),o.accept(i),o.trigger(yt),this._updatePristineForModel(o,i)):this.pushCreate(i);t.length&&this.trigger("push",{type:"update",items:t})},pushDestroy:function(e){var t=this._removeItems(e);t.length&&this.trigger("push",{type:"destroy",items:t})},_removeItems:function(e){var t,n,i,r,o,a;rt(e)||(e=[e]),t=[],n=this.options.autoSync,this.options.autoSync=!1;try{for(i=0;e.length>i;i++)r=e[i],o=this._createNewModel(r),a=!1,this._eachItem(this._data,function(e){var n,i;for(n=0;e.length>n;n++)if(i=e.at(n),i.id===o.id){t.push(i),e.splice(n,1),a=!0;break}}),a&&(this._removePristineForModel(o),this._destroyed.pop())}finally{this.options.autoSync=n}return t},remove:function(e){var n,i=this,r=i._isServerGrouped();return this._eachItem(i._data,function(o){return n=D(o,e),n&&r?(n.isNew&&n.isNew()||i._destroyed.push(n),!0):t}),this._removeModelFromRanges(e),this._updateRangesLength(),e},destroyed:function(){return this._destroyed},created:function(){var e,t,n=[],i=this._flatData(this._data);for(e=0,t=i.length;t>e;e++)i[e].isNew&&i[e].isNew()&&n.push(i[e]);return n},updated:function(){var e,t,n=[],i=this._flatData(this._data);for(e=0,t=i.length;t>e;e++)i[e].isNew&&!i[e].isNew()&&i[e].dirty&&n.push(i[e]);return n},sync:function(){var t,n=this,i=[],r=[],o=n._destroyed,a=(n._flatData(n._data),e.Deferred().resolve().promise());if(n.online()){if(!n.reader.model)return a;i=n.created(),r=n.updated(),t=[],n.options.batch&&n.transport.submit?t=n._sendSubmit(i,r,o):(t.push.apply(t,n._send("create",i)),t.push.apply(t,n._send("update",r)),t.push.apply(t,n._send("destroy",o))),a=e.when.apply(null,t).then(function(){var e,t;for(e=0,t=arguments.length;t>e;e++)n._accept(arguments[e]);n._storeData(!0),n._change({action:"sync"}),n.trigger(bt)})}else n._storeData(!0),n._change({action:"sync"});return a},cancelChanges:function(e){var t=this;e instanceof ct.data.Model?t._cancelModel(e):(t._destroyed=[],t._detachObservableParents(),t._data=t._observe(t._pristineData),t.options.serverPaging&&(t._total=t._pristineTotal),t._ranges=[],t._addRange(t._data),t._change())},hasChanges:function(){var e,t,n=this._flatData(this._data);if(this._destroyed.length)return!0;for(e=0,t=n.length;t>e;e++)if(n[e].isNew&&n[e].isNew()||n[e].dirty)return!0;return!1},_accept:function(t){var n,i=this,r=t.models,o=t.response,a=0,s=i._isServerGrouped(),l=i._pristineData,c=t.type;if(i.trigger(St,{response:o,type:c}),o&&!it(o)){if(o=i.reader.parse(o),i._handleCustomErrors(o))return;o=i.reader.data(o),rt(o)||(o=[o])}else o=e.map(r,function(e){return e.toJSON()});for("destroy"===c&&(i._destroyed=[]),a=0,n=r.length;n>a;a++)"destroy"!==c?(r[a].accept(o[a]),"create"===c?l.push(s?i._wrapInEmptyGroup(r[a]):o[a]):"update"===c&&i._updatePristineForModel(r[a],o[a])):i._removePristineForModel(r[a])},_updatePristineForModel:function(e,t){this._executeOnPristineForModel(e,function(e,n){ct.deepExtend(n[e],t)})},_executeOnPristineForModel:function(e,n){this._eachPristineItem(function(i){var r=A(i,e);return r>-1?(n(r,i),!0):t})},_removePristineForModel:function(e){this._executeOnPristineForModel(e,function(e,t){t.splice(e,1)})},_readData:function(e){var t=this._isServerGrouped()?this.reader.groups:this.reader.data;return t.call(this.reader,e)},_eachPristineItem:function(e){this._eachItem(this._pristineData,e)},_eachItem:function(e,t){e&&e.length&&(this._isServerGrouped()?x(e,t):t(e))},_pristineForModel:function(e){var n,i,r=function(r){return i=A(r,e),i>-1?(n=r[i],!0):t};return this._eachPristineItem(r),n},_cancelModel:function(e){var t=this._pristineForModel(e);this._eachItem(this._data,function(n){var i=E(n,e);i>=0&&(!t||e.isNew()&&!t.__state__?n.splice(i,1):n[i].accept(t))})},_submit:function(t,n){var i=this;i.trigger(xt,{type:"submit"}),i.transport.submit(et({success:function(n,i){var r=e.grep(t,function(e){return e.type==i})[0];r&&r.resolve({response:n,models:r.models,type:i})},error:function(e,n,r){for(var o=0;t.length>o;o++)t[o].reject(e);i.error(e,n,r)}},n))},_sendSubmit:function(t,n,i){var r=this,o=[];return r.options.batch&&(t.length&&o.push(e.Deferred(function(e){e.type="create",e.models=t})),n.length&&o.push(e.Deferred(function(e){e.type="update",e.models=n})),i.length&&o.push(e.Deferred(function(e){e.type="destroy",e.models=i})),r._submit(o,{data:{created:r.reader.serialize(g(t)),updated:r.reader.serialize(g(n)),destroyed:r.reader.serialize(g(i))}})),o},_promise:function(t,n,i){var r=this;return e.Deferred(function(e){r.trigger(xt,{type:i}),r.transport[i].call(r.transport,et({success:function(t){e.resolve({response:t,models:n,type:i})},error:function(t,n,i){e.reject(t),r.error(t,n,i)}},t))}).promise()},_send:function(e,t){var n,i,r=this,o=[],a=r.reader.serialize(g(t));if(r.options.batch)t.length&&o.push(r._promise({data:{models:a}},t,e));else for(n=0,i=t.length;i>n;n++)o.push(r._promise({data:a[n]},[t[n]],e));return o},read:function(t){var n=this,i=n._params(t),r=e.Deferred();return n._queueRequest(i,function(){var e=n.trigger(xt,{type:"read"});e?(n._dequeueRequest(),r.resolve(e)):(n.trigger(Ct),n._ranges=[],n.trigger("reset"),n.online()?n.transport.read({data:i,success:function(e){n.success(e,i),r.resolve()},error:function(){var e=Ht.call(arguments);n.error.apply(n,e),r.reject.apply(r,e)}}):null!=n.options.offlineStorage&&(n.success(n.offlineData(),i),r.resolve()))}),r.promise()},_readAggregates:function(e){return this.reader.aggregates(e)},success:function(e){var n,i,r,o,a,s,l,c,d=this,u=d.options;if(d.trigger(St,{response:e,type:"read"}),d.online()){if(e=d.reader.parse(e),d._handleCustomErrors(e))return d._dequeueRequest(),t;d._total=d.reader.total(e),d._aggregate&&u.serverAggregates&&(d._aggregateResult=d._readAggregates(e)),e=d._readData(e)}else{for(e=d._readData(e),n=[],i={},r=d.reader.model,o=r?r.idField:"id",a=0;this._destroyed.length>a;a++)s=this._destroyed[a][o],i[s]=s;for(a=0;e.length>a;a++)l=e[a],c=l.__state__,"destroy"==c?i[l[o]]||this._destroyed.push(this._createNewModel(l)):n.push(l);e=n,d._total=e.length}d._pristineTotal=d._total,d._pristineData=e.slice(0),d._detachObservableParents(),d._data=d._observe(e),null!=d.options.offlineStorage&&d._eachItem(d._data,function(e){var t,n;for(t=0;e.length>t;t++)n=e.at(t),"update"==n.__state__&&(n.dirty=!0)}),d._storeData(),d._addRange(d._data),d._process(d._data),d._dequeueRequest()},_detachObservableParents:function(){if(this._data&&this._shouldDetachObservableParents)for(var e=0;this._data.length>e;e++)this._data[e].parent&&(this._data[e].parent=lt)},_storeData:function(e){function t(e){var n,i,r,s=[];for(n=0;e.length>n;n++)i=e.at(n),r=i.toJSON(),o&&i.items?r.items=t(i.items):(r.uid=i.uid,a&&(i.isNew()?r.__state__="create":i.dirty&&(r.__state__="update"))),s.push(r);return s}var n,i,r,o=this._isServerGrouped(),a=this.reader.model;if(null!=this.options.offlineStorage){for(n=t(this._data),i=0;this._destroyed.length>i;i++)r=this._destroyed[i].toJSON(),r.__state__="destroy",n.push(r);
this.offlineData(n),e&&(this._pristineData=n)}},_addRange:function(e){var t=this,n=t._skip||0,i=n+t._flatData(e,!0).length;t._ranges.push({start:n,end:i,data:e,timestamp:(new Date).getTime()}),t._ranges.sort(function(e,t){return e.start-t.start})},error:function(e,t,n){this._dequeueRequest(),this.trigger(St,{}),this.trigger(kt,{xhr:e,status:t,errorThrown:n})},_params:function(e){var t=this,n=et({take:t.take(),skip:t.skip(),page:t.page(),pageSize:t.pageSize(),sort:t._sort,filter:t._filter,group:t._group,aggregate:t._aggregate},e);return t.options.serverPaging||(delete n.take,delete n.skip,delete n.page,delete n.pageSize),t.options.serverGrouping?t.reader.model&&n.group&&(n.group=F(n.group,t.reader.model)):delete n.group,t.options.serverFiltering?t.reader.model&&n.filter&&(n.filter=P(n.filter,t.reader.model)):delete n.filter,t.options.serverSorting?t.reader.model&&n.sort&&(n.sort=F(n.sort,t.reader.model)):delete n.sort,t.options.serverAggregates?t.reader.model&&n.aggregate&&(n.aggregate=F(n.aggregate,t.reader.model)):delete n.aggregate,n},_queueRequest:function(e,n){var i=this;i._requestInProgress?i._pending={callback:tt(n,i),options:e}:(i._requestInProgress=!0,i._pending=t,n())},_dequeueRequest:function(){var e=this;e._requestInProgress=!1,e._pending&&e._queueRequest(e._pending.options,e._pending.callback)},_handleCustomErrors:function(e){if(this.reader.errors){var t=this.reader.errors(e);if(t)return this.trigger(kt,{xhr:null,status:"customerror",errorThrown:"custom error",errors:t}),!0}return!1},_shouldWrap:function(e){var t=this.reader.model;return t&&e.length?!(e[0]instanceof t):!1},_observe:function(e){var t,n=this,i=n.reader.model;return n._shouldDetachObservableParents=!0,e instanceof Wt?(n._shouldDetachObservableParents=!1,n._shouldWrap(e)&&(e.type=n.reader.model,e.wrapAll(e,e))):(t=n.pageSize()&&!n.options.serverPaging?jt:Wt,e=new t(e,n.reader.model),e.parent=function(){return n.parent()}),n._isServerGrouped()&&k(e,i),n._changeHandler&&n._data&&n._data instanceof Wt?n._data.unbind(yt,n._changeHandler):n._changeHandler=tt(n._change,n),e.bind(yt,n._changeHandler)},_change:function(e){var t,n,i,r=this,o=e?e.action:"";if("remove"===o)for(t=0,n=e.items.length;n>t;t++)e.items[t].isNew&&e.items[t].isNew()||r._destroyed.push(e.items[t]);!r.options.autoSync||"add"!==o&&"remove"!==o&&"itemchange"!==o?(i=parseInt(r._total,10),p(r._total)||(i=parseInt(r._pristineTotal,10)),"add"===o?i+=e.items.length:"remove"===o?i-=e.items.length:"itemchange"===o||"sync"===o||r.options.serverPaging?"sync"===o&&(i=r._pristineTotal=parseInt(r._total,10)):i=r._pristineTotal,r._total=i,r._process(r._data,e)):r.sync()},_calculateAggregates:function(e,t){t=t||{};var n=new o(e),i=t.aggregate,r=t.filter;return r&&(n=n.filter(r)),n.aggregate(i)},_process:function(e,n){var i,r=this,o={};r.options.serverPaging!==!0&&(o.skip=r._skip,o.take=r._take||r._pageSize,o.skip===t&&r._page!==t&&r._pageSize!==t&&(o.skip=(r._page-1)*r._pageSize)),r.options.serverSorting!==!0&&(o.sort=r._sort),r.options.serverFiltering!==!0&&(o.filter=r._filter),r.options.serverGrouping!==!0&&(o.group=r._group),r.options.serverAggregates!==!0&&(o.aggregate=r._aggregate,r._aggregateResult=r._calculateAggregates(e,o)),i=r._queryProcess(e,o),r.view(i.data),i.total===t||r.options.serverFiltering||(r._total=i.total),n=n||{},n.items=n.items||r._view,r.trigger(yt,n)},_queryProcess:function(e,t){return o.process(e,t)},_mergeState:function(e){var n=this;return e!==t&&(n._pageSize=e.pageSize,n._page=e.page,n._sort=e.sort,n._filter=e.filter,n._group=e.group,n._aggregate=e.aggregate,n._skip=e.skip,n._take=e.take,n._skip===t&&(n._skip=n.skip(),e.skip=n.skip()),n._take===t&&n._pageSize!==t&&(n._take=n._pageSize,e.take=n._take),e.sort&&(n._sort=e.sort=a(e.sort)),e.filter&&(n._filter=e.filter=l(e.filter)),e.group&&(n._group=e.group=d(e.group)),e.aggregate&&(n._aggregate=e.aggregate=c(e.aggregate))),e},query:function(n){var i,r,o=this.options.serverSorting||this.options.serverPaging||this.options.serverFiltering||this.options.serverGrouping||this.options.serverAggregates;return o||(this._data===t||0===this._data.length)&&!this._destroyed.length?this.read(this._mergeState(n)):(r=this.trigger(xt,{type:"read"}),r||(this.trigger(Ct),i=this._queryProcess(this._data,this._mergeState(n)),this.options.serverFiltering||(this._total=i.total!==t?i.total:this._data.length),this._aggregateResult=this._calculateAggregates(this._data,n),this.view(i.data),this.trigger(St,{type:"read"}),this.trigger(yt,{items:i.data})),e.Deferred().resolve(r).promise())},fetch:function(e){var t=this,n=function(n){n!==!0&&dt(e)&&e.call(t)};return this._query().then(n)},_query:function(e){var t=this;return t.query(et({},{page:t.page(),pageSize:t.pageSize(),sort:t.sort(),filter:t.filter(),group:t.group(),aggregate:t.aggregate()},e))},next:function(e){var n=this,i=n.page(),r=n.total();return e=e||{},!i||r&&i+1>n.totalPages()?t:(n._skip=i*n.take(),i+=1,e.page=i,n._query(e),i)},prev:function(e){var n=this,i=n.page();return e=e||{},i&&1!==i?(n._skip=n._skip-n.take(),i-=1,e.page=i,n._query(e),i):t},page:function(e){var n,i=this;return e!==t?(e=It.max(It.min(It.max(e,1),i.totalPages()),1),i._query({page:e}),t):(n=i.skip(),n!==t?It.round((n||0)/(i.take()||1))+1:t)},pageSize:function(e){var n=this;return e!==t?(n._query({pageSize:e,page:1}),t):n.take()},sort:function(e){var n=this;return e!==t?(n._query({sort:e}),t):n._sort},filter:function(e){var n=this;return e===t?n._filter:(n._query({filter:e,page:1}),n.trigger("reset"),t)},group:function(e){var n=this;return e!==t?(n._query({group:e}),t):n._group},total:function(){return parseInt(this._total||0,10)},aggregate:function(e){var n=this;return e!==t?(n._query({aggregate:e}),t):n._aggregate},aggregates:function(){var e=this._aggregateResult;return it(e)&&(e=this._emptyAggregates(this.aggregate())),e},_emptyAggregates:function(e){var t,n,i={};if(!it(e))for(t={},rt(e)||(e=[e]),n=0;e.length>n;n++)t[e[n].aggregate]=0,i[e[n].field]=t;return i},_wrapInEmptyGroup:function(e){var t,n,i,r,o=this.group();for(i=o.length-1,r=0;i>=r;i--)n=o[i],t={value:e.get(n.field),field:n.field,items:t?[t]:[e],hasSubgroups:!!t,aggregates:this._emptyAggregates(n.aggregates)};return t},totalPages:function(){var e=this,t=e.pageSize()||e.total();return It.ceil((e.total()||0)/t)},inRange:function(e,t){var n=this,i=It.min(e+t,n.total());return!n.options.serverPaging&&n._data.length>0?!0:n._findRange(e,i).length>0},lastRange:function(){var e=this._ranges;return e[e.length-1]||{start:0,end:0,data:[]}},firstItemUid:function(){var e=this._ranges;return e.length&&e[0].data.length&&e[0].data[0].uid},enableRequestsInProgress:function(){this._skipRequestsInProgress=!1},_timeStamp:function(){return(new Date).getTime()},range:function(e,n){var i,r,o,a,s,l,c,d;if(this._currentRequestTimeStamp=this._timeStamp(),this._skipRequestsInProgress=!0,e=It.min(e||0,this.total()),i=this,r=It.max(It.floor(e/n),0)*n,o=It.min(r+n,i.total()),a=i._findRange(e,It.min(e+n,i.total())),a.length){i._pending=t,i._skip=e>i.skip()?It.min(o,(i.totalPages()-1)*i.take()):r,i._take=n,s=i.options.serverPaging,l=i.options.serverSorting,c=i.options.serverFiltering,d=i.options.serverAggregates;try{i.options.serverPaging=!0,i._isServerGrouped()||i.group()&&i.group().length||(i.options.serverSorting=!0),i.options.serverFiltering=!0,i.options.serverPaging=!0,i.options.serverAggregates=!0,s&&(i._detachObservableParents(),i._data=a=i._observe(a)),i._process(a)}finally{i.options.serverPaging=s,i.options.serverSorting=l,i.options.serverFiltering=c,i.options.serverAggregates=d}}else n!==t&&(i._rangeExists(r,o)?e>r&&i.prefetch(o,n,function(){i.range(e,n)}):i.prefetch(r,n,function(){e>r&&o<i.total()&&!i._rangeExists(o,It.min(o+n,i.total()))?i.prefetch(o,n,function(){i.range(e,n)}):i.range(e,n)}))},_findRange:function(e,n){var i,r,o,s,l,c,u,h,p,f,g,m,v=this,_=v._ranges,y=[],b=v.options,w=b.serverSorting||b.serverPaging||b.serverFiltering||b.serverGrouping||b.serverAggregates;for(r=0,g=_.length;g>r;r++)if(i=_[r],e>=i.start&&i.end>=e){for(f=0,o=r;g>o;o++)if(i=_[o],p=v._flatData(i.data,!0),p.length&&e+f>=i.start&&(c=i.data,u=i.end,w||(m=d(v.group()||[]).concat(a(v.sort()||[])),h=v._queryProcess(i.data,{sort:m,filter:v.filter()}),p=c=h.data,h.total!==t&&(u=h.total)),s=0,e+f>i.start&&(s=e+f-i.start),l=p.length,u>n&&(l-=u-n),f+=l-s,y=v._mergeGroups(y,c,s,l),i.end>=n&&f==n-e))return y;break}return[]},_mergeGroups:function(e,t,n,i){if(this._isServerGrouped()){var r,o=t.toJSON();return e.length&&(r=e[e.length-1]),b(r,o,n,i),e.concat(o)}return e.concat(t.slice(n,i))},skip:function(){var e=this;return e._skip===t?e._page!==t?(e._page-1)*(e.take()||1):t:e._skip},take:function(){return this._take||this._pageSize},_prefetchSuccessHandler:function(e,t,n,i){var r=this,o=r._timeStamp();return function(a){var s,l,c,d=!1,u={start:e,end:t,data:[],timestamp:r._timeStamp()};if(r._dequeueRequest(),r.trigger(St,{response:a,type:"read"}),a=r.reader.parse(a),c=r._readData(a),c.length){for(s=0,l=r._ranges.length;l>s;s++)if(r._ranges[s].start===e){d=!0,u=r._ranges[s];break}d||r._ranges.push(u)}u.data=r._observe(c),u.end=u.start+r._flatData(u.data,!0).length,r._ranges.sort(function(e,t){return e.start-t.start}),r._total=r.reader.total(a),(i||o>=r._currentRequestTimeStamp||!r._skipRequestsInProgress)&&(n&&c.length?n():r.trigger(yt,{}))}},prefetch:function(e,t,n){var i=this,r=It.min(e+t,i.total()),o={take:t,skip:e,page:e/t+1,pageSize:t,sort:i._sort,filter:i._filter,group:i._group,aggregate:i._aggregate};i._rangeExists(e,r)?n&&n():(clearTimeout(i._timeout),i._timeout=setTimeout(function(){i._queueRequest(o,function(){i.trigger(xt,{type:"read"})?i._dequeueRequest():i.transport.read({data:i._params(o),success:i._prefetchSuccessHandler(e,r,n),error:function(){var e=Ht.call(arguments);i.error.apply(i,e)}})})},100))},_multiplePrefetch:function(e,t,n){var i=this,r=It.min(e+t,i.total()),o={take:t,skip:e,page:e/t+1,pageSize:t,sort:i._sort,filter:i._filter,group:i._group,aggregate:i._aggregate};i._rangeExists(e,r)?n&&n():i.trigger(xt,{type:"read"})||i.transport.read({data:i._params(o),success:i._prefetchSuccessHandler(e,r,n,!0)})},_rangeExists:function(e,t){var n,i,r=this,o=r._ranges;for(n=0,i=o.length;i>n;n++)if(e>=o[n].start&&o[n].end>=t)return!0;return!1},_removeModelFromRanges:function(e){var t,n,i,r,o;for(r=0,o=this._ranges.length;o>r&&(i=this._ranges[r],this._eachItem(i.data,function(i){t=D(i,e),t&&(n=!0)}),!n);r++);},_updateRangesLength:function(){var e,t,n,i,r=0;for(n=0,i=this._ranges.length;i>n;n++)e=this._ranges[n],e.start=e.start-r,t=this._flatData(e.data,!0).length,r=e.end-t,e.end=e.start+t}}),Q={},Q.create=function(e,t,n){var i,r=e.transport;return r?(r.read=typeof r.read===pt?{url:r.read}:r.read,n&&(r.dataSource=n),e.type&&(ct.data.transports=ct.data.transports||{},ct.data.schemas=ct.data.schemas||{},ct.data.transports[e.type]&&!nt(ct.data.transports[e.type])?i=new ct.data.transports[e.type](et(r,{data:t})):r=et(!0,{},ct.data.transports[e.type],r),e.schema=et(!0,{},ct.data.schemas[e.type],e.schema)),i||(i=dt(r.read)?r:new q(r))):i=new j({data:e.data||[]}),i},Y.create=function(e){(rt(e)||e instanceof Wt)&&(e={data:e});var n,i,r,o=e||{},a=o.data,s=o.fields,l=o.table,c=o.select,d={};if(a||!s||o.transport||(l?a=R(l,s):c&&(a=z(c,s),o.group===t&&a[0]&&a[0].optgroup!==t&&(o.group="optgroup"))),ct.data.Model&&s&&(!o.schema||!o.schema.model)){for(n=0,i=s.length;i>n;n++)r=s[n],r.type&&(d[r.field]=r);it(d)||(o.schema=et(!0,o.schema,{model:{fields:d}}))}return o.data=a,c=null,o.select=null,l=null,o.table=null,o instanceof Y?o:new Y(o)},K=Yt.define({idField:"id",init:function(e){var t=this,n=t.hasChildren||e&&e.hasChildren,i="items",r={};ct.data.Model.fn.init.call(t,e),typeof t.children===pt&&(i=t.children),r={schema:{data:i,model:{hasChildren:n,id:t.idField,fields:t.fields}}},typeof t.children!==pt&&et(r,t.children),r.data=e,n||(n=r.schema.data),typeof n===pt&&(n=ct.getter(n)),dt(n)&&(t.hasChildren=!!n.call(t,t)),t._childrenOptions=r,t.hasChildren&&t._initChildren(),t._loaded=!(!e||!e[i]&&!e._loaded)},_initChildren:function(){var e,t,n,i=this;i.children instanceof X||(e=i.children=new X(i._childrenOptions),t=e.transport,n=t.parameterMap,t.parameterMap=function(e,t){return e[i.idField||"id"]=i.id,n&&(e=n(e,t)),e},e.parent=function(){return i},e.bind(yt,function(e){e.node=e.node||i,i.trigger(yt,e)}),e.bind(kt,function(e){var t=i.parent();t&&(e.node=e.node||i,t.trigger(kt,e))}),i._updateChildrenField())},append:function(e){this._initChildren(),this.loaded(!0),this.children.add(e)},hasChildren:!1,level:function(){for(var e=this.parentNode(),t=0;e&&e.parentNode;)t++,e=e.parentNode?e.parentNode():null;return t},_updateChildrenField:function(){var e=this._childrenOptions.schema.data;this[e||"items"]=this.children.data()},_childrenLoaded:function(){this._loaded=!0,this._updateChildrenField()},load:function(){var n,i,r={},o="_query";return this.hasChildren?(this._initChildren(),n=this.children,r[this.idField||"id"]=this.id,this._loaded||(n._data=t,o="read"),n.one(yt,tt(this._childrenLoaded,this)),i=n[o](r)):this.loaded(!0),i||e.Deferred().resolve().promise()},parentNode:function(){var e=this.parent();return e.parent()},loaded:function(e){return e===t?this._loaded:(this._loaded=e,t)},shouldSerialize:function(e){return Yt.fn.shouldSerialize.call(this,e)&&"children"!==e&&"_loaded"!==e&&"hasChildren"!==e&&"_childrenOptions"!==e}}),X=Y.extend({init:function(e){var t=K.define({children:e});Y.fn.init.call(this,et(!0,{},{schema:{modelBase:t,model:t}},e)),this._attachBubbleHandlers()},_attachBubbleHandlers:function(){var e=this;e._data.bind(kt,function(t){e.trigger(kt,t)})},remove:function(e){var t,n=e.parentNode(),i=this;return n&&n._initChildren&&(i=n.children),t=Y.fn.remove.call(i,e),n&&!i.data().length&&(n.hasChildren=!1),t},success:H("success"),data:H("data"),insert:function(e,t){var n=this.parent();return n&&n._initChildren&&(n.hasChildren=!0,n._initChildren()),Y.fn.insert.call(this,e,t)},_find:function(e,t){var n,i,r,o,a;if(r=Y.fn[e].call(this,t))return r;if(o=this._flatData(this._data))for(n=0,i=o.length;i>n;n++)if(a=o[n].children,a instanceof X&&(r=a[e](t)))return r},get:function(e){return this._find("get",e)},getByUid:function(e){return this._find("getByUid",e)}}),X.create=function(e){e=e&&e.push?{data:e}:e;var t=e||{},n=t.data,i=t.fields,r=t.list;return n&&n._dataSource?n._dataSource:(n||!i||t.transport||r&&(n=B(r,i)),t.data=n,t instanceof X?t:new X(t))},Z=ct.Observable.extend({init:function(e,t,n){ct.Observable.fn.init.call(this),this._prefetching=!1,this.dataSource=e,this.prefetch=!n;var i=this;e.bind("change",function(){i._change()}),e.bind("reset",function(){i._reset()}),this._syncWithDataSource(),this.setViewSize(t)},setViewSize:function(e){this.viewSize=e,this._recalculate()},at:function(e){var n=this.pageSize,i=!0;return e>=this.total()?(this.trigger("endreached",{index:e}),null):this.useRanges?this.useRanges?((this.dataOffset>e||e>=this.skip+n)&&(i=this.range(Math.floor(e/n)*n)),e===this.prefetchThreshold&&this._prefetch(),e===this.midPageThreshold?this.range(this.nextMidRange,!0):e===this.nextPageThreshold?this.range(this.nextFullRange):e===this.pullBackThreshold&&this.range(this.offset===this.skip?this.previousMidRange:this.previousFullRange),i?this.dataSource.at(e-this.dataOffset):(this.trigger("endreached",{index:e}),null)):t:this.dataSource.view()[e]},indexOf:function(e){return this.dataSource.data().indexOf(e)+this.dataOffset},total:function(){return parseInt(this.dataSource.total(),10)},next:function(){var e=this,t=e.pageSize,n=e.skip-e.viewSize+t,i=It.max(It.floor(n/t),0)*t;this.offset=n,this.dataSource.prefetch(i,t,function(){e._goToRange(n,!0)})},range:function(e,t){if(this.offset===e)return!0;var n=this,i=this.pageSize,r=It.max(It.floor(e/i),0)*i,o=this.dataSource;return t&&(r+=i),o.inRange(e,i)?(this.offset=e,this._recalculate(),this._goToRange(e),!0):this.prefetch?(o.prefetch(r,i,function(){n.offset=e,n._recalculate(),n._goToRange(e,!0)}),!1):!0},syncDataSource:function(){var e=this.offset;this.offset=null,this.range(e)},destroy:function(){this.unbind()},_prefetch:function(){var e=this,t=this.pageSize,n=this.skip+t,i=this.dataSource;i.inRange(n,t)||this._prefetching||!this.prefetch||(this._prefetching=!0,this.trigger("prefetching",{skip:n,take:t}),i.prefetch(n,t,function(){e._prefetching=!1,e.trigger("prefetched",{skip:n,take:t})}))},_goToRange:function(e,t){this.offset===e&&(this.dataOffset=e,this._expanding=t,this.dataSource.range(e,this.pageSize),this.dataSource.enableRequestsInProgress())},_reset:function(){this._syncPending=!0},_change:function(){var e=this.dataSource;this.length=this.useRanges?e.lastRange().end:e.view().length,this._syncPending&&(this._syncWithDataSource(),this._recalculate(),this._syncPending=!1,this.trigger("reset",{offset:this.offset})),this.trigger("resize"),this._expanding&&this.trigger("expand"),delete this._expanding},_syncWithDataSource:function(){var e=this.dataSource;this._firstItemUid=e.firstItemUid(),this.dataOffset=this.offset=e.skip()||0,this.pageSize=e.pageSize(),this.useRanges=e.options.serverPaging},_recalculate:function(){var e=this.pageSize,t=this.offset,n=this.viewSize,i=Math.ceil(t/e)*e;this.skip=i,this.midPageThreshold=i+e-1,this.nextPageThreshold=i+n-1,this.prefetchThreshold=i+Math.floor(e/3*2),this.pullBackThreshold=this.offset-1,this.nextMidRange=i+e-n,this.nextFullRange=i,this.previousMidRange=t-n,this.previousFullRange=i-e}}),J=ct.Observable.extend({init:function(e,t){var n=this;ct.Observable.fn.init.call(n),this.dataSource=e,this.batchSize=t,this._total=0,this.buffer=new Z(e,3*t),this.buffer.bind({endreached:function(e){n.trigger("endreached",{index:e.index})},prefetching:function(e){n.trigger("prefetching",{skip:e.skip,take:e.take})},prefetched:function(e){n.trigger("prefetched",{skip:e.skip,take:e.take})},reset:function(){n._total=0,n.trigger("reset")},resize:function(){n._total=Math.ceil(this.length/n.batchSize),n.trigger("resize",{total:n.total(),offset:this.offset})}})},syncDataSource:function(){this.buffer.syncDataSource()},at:function(e){var t,n,i=this.buffer,r=e*this.batchSize,o=this.batchSize,a=[];for(i.offset>r&&i.at(i.offset-1),n=0;o>n&&(t=i.at(r+n),null!==t);n++)a.push(t);return a},total:function(){return this._total},destroy:function(){this.buffer.destroy(),this.unbind()}}),et(!0,ct.data,{readers:{json:$},Query:o,DataSource:Y,HierarchicalDataSource:X,Node:K,ObservableObject:qt,ObservableArray:Wt,LazyObservableArray:jt,LocalTransport:j,RemoteTransport:q,Cache:G,DataReader:$,Model:Yt,Buffer:Z,BatchBuffer:J})}(window.kendo.jQuery),window.kendo},"function"==typeof define&&define.amd?define:function(e,t){t()});