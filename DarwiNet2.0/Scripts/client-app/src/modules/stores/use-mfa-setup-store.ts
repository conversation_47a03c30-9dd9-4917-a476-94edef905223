import {computed, ref, inject} from "vue";
import {useSendVerificationCode} from "@modules/mfa-setup/composables/use-send-verification-code.ts";
import type {
    MfaSetupContext,
    SendVerificationCodeRequest,
    SendVerificationCodeResponse
} from "@modules/mfa-setup/types/mfa-setup.types.ts";
import {useRouter} from "vue-router";
import {toast} from "vue-sonner";
import {useSetupMfaSms} from "@modules/mfa-setup/composables/use-set-up-mfa-sms.ts";
import {defineStore} from "pinia";
import {MFA_SETUP_CONTEXT_KEY} from "@modules/mfa-setup/main.ts";
export const useMfaSetupContext = () => {
    const mfaSetupContext = inject<MfaSetupContext>(MFA_SETUP_CONTEXT_KEY);
    
    if (!mfaSetupContext) {
        throw new Error("MFA_SETUP_CONTEXT_KEY should be defined");
    }
    
    return mfaSetupContext;
}
export const useMfaSetupStore = defineStore("mfa-setup", () => {
    // State
    const phoneNumber = ref<string>("")
    const verificationSid = ref<string>("")
    const isRedirecting = ref(false)
    const resendCooldown = ref<number>(0)
    const cooldownTimer = ref<ReturnType<typeof setInterval> | null>(null)
    
    // Computed state
    const canResend = computed(() => resendCooldown.value === 0 && !sendVerificationCodeFn.isPending.value)
    const isLoading = computed(() => sendVerificationCodeFn.isPending.value || verifyCodeFn.isPending.value)

    // Store/Composable
    const router = useRouter()
    const sendVerificationCodeFn = useSendVerificationCode();
    const verifyCodeFn = useSetupMfaSms()
    const { CompanyId, UserGuid } = useMfaSetupContext();

    // Methods
    const sendVerificationCode = (phone: string) => {
        sendVerificationCodeFn.mutate({
            PhoneNumber: phone,
            CompanyId: CompanyId,
        }, {
            onSuccess(response: SendVerificationCodeResponse, request: SendVerificationCodeRequest) {
                toast.success("Verification code sent successfully", {
                    position: "top-right",
                    duration: 3000,
                });

                // Update the state
                phoneNumber.value = request.PhoneNumber
                verificationSid.value = response.VerficationSid

                // Brief success state before transitioning
                setTimeout(() => {
                    router.push('/Verify')
                }, 1000)
            },
        })
    }
    const setupMfaForSms = (verificationCode: string) => {
        if (!phoneNumber.value || !verificationSid.value) {
            goBackToSetupStep()
            return
        }

        verifyCodeFn.mutate({
            UserGuid: UserGuid,
            CompanyId: CompanyId,
            PhoneNumber: phoneNumber.value,
            VerificationCode: verificationCode,
            VerificationSid: verificationSid.value,
        }, {
            onSuccess: () => {
                isRedirecting.value = true
            },
        })
    }

    const startResendCooldown = () => {
        resendCooldown.value = 30
        cooldownTimer.value = setInterval(() => {
            resendCooldown.value--
            if (resendCooldown.value <= 0) {
                clearInterval(cooldownTimer.value!)
                cooldownTimer.value = null
            }
        }, 1000)
    }

    const resendCode = (resetForm: () => void) => {
        if (!canResend.value) return

        // If phone number is missing somehow, prompt user to start again
        if (!phoneNumber.value) {
            goBackToSetupStep()
            return
        }

        sendVerificationCodeFn.mutate({
            PhoneNumber: phoneNumber.value,
            CompanyId: 8,
        }, {
            onSuccess: () => {
                toast.success("New verification code sent!", {
                    position: "top-right",
                    duration: 3000,
                })

                startResendCooldown()
                resetForm()
            },
        })
    }
    const goBackToSetupStep = () => router.replace("/Setup")

    return {
        phoneNumber,
        verificationSid,
        resendCooldown,
        canResend,
        isRedirecting,
        isLoading,
        goBackToSetupStep,
        sendVerificationCode,
        setupMfaForSms,
        resendCode,
        startResendCooldown,
    }
})