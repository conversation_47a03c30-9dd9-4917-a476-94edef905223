<script setup lang="ts">
import { Button } from '@components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@components/ui/dialog';
import { useRestartEnrollment } from '@open-enrollment/api';
import { AlertTriangle, LoaderCircle, RotateCcw } from 'lucide-vue-next';
import useBenefitStore from '../stores/use-benefits-store';

const props = defineProps<{
    open: boolean
}>()

const emit = defineEmits<{
    (e: 'update:open', value: boolean): void
}>()

const benefitStore = useBenefitStore()
const restartEnrollmentFn = useRestartEnrollment()

const closeDialog = () => emit('update:open', false)
const confirmRestart = () => restartEnrollmentFn.mutate(benefitStore.employeeEligibility!.SelectedYear)
</script>

<template>
    <Dialog :open="props.open" @update:open="(value) => emit('update:open', value)">
        <DialogContent class="sm:max-w-md">
            <DialogHeader>
                <DialogTitle class="text-lg font-semibold flex items-center gap-2">
                    <AlertTriangle class="w-5 h-5 text-orange-500" />
                    Restart Enrollment
                </DialogTitle>
                <DialogDescription class="text-left">
                    Are you sure you want to restart your open enrollment? This will clear all your current selections.
                </DialogDescription>
            </DialogHeader>

            <div class="bg-orange-100 p-3 rounded-md text-orange-800 text-sm my-2">
                <p>Warning: This action cannot be undone. All your current benefit selections will be reset.</p>
            </div>

            <div class="flex justify-end gap-2 mt-4">
                <Button variant="outline" @click="closeDialog">
                    Cancel
                </Button>
                <Button variant="destructive" class="!text-white" :disabled="restartEnrollmentFn.isPending.value"
                    @click="confirmRestart">
                    <LoaderCircle v-if="restartEnrollmentFn.isPending.value" class="w-4 h-4 mr-2 animate-spin" />
                    <RotateCcw v-else class="w-4 h-4" />
                    Restart
                </Button>
            </div>
        </DialogContent>
    </Dialog>
</template>