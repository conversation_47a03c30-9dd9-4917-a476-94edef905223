<script setup lang="ts">
import { But<PERSON> } from '@components/ui/button'
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON>le, CardFooter } from '@components/ui/card'
import { Skeleton } from '@components/ui/skeleton'
import type { EmployeeBenefit } from '@open-enrollment/api'
import { useGetPlansList, useWaivePlan } from '@open-enrollment/api'
import BenefitIcon from '@open-enrollment/components/benefit-icon.vue'
import BenefitStatusBadge from '@open-enrollment/components/benefit-status-badge.vue'
import CoverageSelection from '@open-enrollment/components/coverage-selection.vue'
import WaiveBenefitDialog from '@open-enrollment/components/waive-benefit-dialog.vue'
import { LoaderCircle, Send, X, Grid3x3, Edit } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'
import useBenefitStore from '@open-enrollment/stores/use-benefits-store'
import BenefitCardItem from '@open-enrollment/components/benefit-card-item.vue'
import { useBenefitsSelectionStore } from '@open-enrollment/stores/use-benefits-selection-store'
import PlanRequirementDialog from '@open-enrollment/components/plan-requirement-dialog.vue'

// Define component options
defineOptions({
  inheritAttrs: false,
})

const props = defineProps<{
  benefit: EmployeeBenefit
}>()

const benefitSelectionStore = useBenefitsSelectionStore()
const benefitStore = useBenefitStore()

// Get available plans for the benefit type
const { data: plans, isLoading: isPlansLoading, isFetching: isPlansFetching, error: plansError } = useGetPlansList(
  benefitStore.employeeEligibility!.SelectedYear,
  props.benefit.BenefitType,
  !!benefitStore.employeeEligibility,
)

const waivePlanFn = useWaivePlan()

const showWaiveDialog = ref(false)
const selectedPlan = computed(() => plans.value?.find(plan => plan.PlanName === benefitSelectionStore.userSelections.get(props.benefit.BenefitType)?.selectedPlanName))


// When the component first mounts, use the server state to set the user selection. This will also fetch the details (pricing and info)
onMounted(() => {
  benefitSelectionStore.changeUserSelection(props.benefit.BenefitType, props.benefit.SelectedPlanName, props.benefit.SelectedCoverageLevel)
})

const waiveBenefit = (reasonId: number, reason?: string, description?: string, otherReason?: string) => {
  if (!benefitStore.employeeEligibility) return

  waivePlanFn.mutate({
    year: benefitStore.employeeEligibility.SelectedYear,
    planType: props.benefit.BenefitType,
    transactionId: benefitStore.employeeEligibility.TransactionId,
    benefitWaiverReason: {
      id: reasonId,
      reason,
      description,
      otherReason,
    },
  }, {
    onSuccess: () => benefitSelectionStore.changeUserSelection(props.benefit.BenefitType, null, 0),
  })
}
</script>

<template>
  <Card v-bind="$attrs" class="w-full pt-2">
    <CardHeader class="flex justify-between items-start">
      <!-- Primary Information -->
      <div class="flex-1 min-w-0">
        <!-- Benefit Title -->
        <CardTitle class="flex items-center gap-2 mb-2">
          <BenefitIcon :benefit-type="props.benefit.BenefitType" class="icon-lg flex-shrink-0" />
          <h2 class="text-title text-foreground font-semibold leading-tight">{{ props.benefit.BenefitPlan }}
          </h2>
        </CardTitle>

        <!-- Enhanced Status Information - Secondary -->
        <div class="flex items-center gap-3">
          <!-- Loading State -->
          <div v-if="isPlansFetching" class="selection-loading">
            <LoaderCircle class="icon-xs animate-spin" />
            <span class="text-xs font-medium">Loading plans...</span>
          </div>

          <div v-else class="flex items-center gap-2">
            <BenefitStatusBadge :benefit-status="props.benefit.Status" />

            <div v-if="benefitSelectionStore.getBenefitSelection(props.benefit.BenefitType)?.selectedPlanName"
                 class="selection-available">
              <div class="w-2 h-2 bg-success rounded-full"></div>
              <span class="text-xs font-medium">{{
                  benefitSelectionStore.getBenefitSelection(props.benefit.BenefitType)?.selectedPlanName
                }}</span>
            </div>

            <!-- Plan Count Info -->
            <div v-if="plans && plans.length > 0"
                 class="px-2 py-1 bg-muted border border-border rounded-full">
                            <span class="text-xs font-medium text-muted-foreground">{{ plans.length }} plan{{
                                plans.length > 1 ?
                                  's' : ''
                              }} available</span>
            </div>
          </div>
        </div>
      </div>
    </CardHeader>

    <CardContent>
      <div v-if="isPlansLoading" class="grid xl:grid-cols-4 lg:grid-cols-2 gap-3">
        <!-- Enhanced skeleton for plan loading -->
        <div v-for="i in 4" :key="i" class="animate-in slide-in-from-left-4 duration-300"
             :style="{ animationDelay: `${i * 100}ms` }">
          <Card class="relative py-1">
            <CardContent class="p-3">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2 flex-1">
                  <Skeleton class="w-4 h-4 rounded-full" />
                  <Skeleton class="h-4 w-20" />
                </div>
                <Skeleton class="h-4 w-12" />
              </div>
              <!-- Cost skeleton -->
              <div class="flex items-center justify-between">
                <Skeleton class="h-3 w-8" />
                <div class="flex items-center gap-2">
                  <Skeleton class="h-3 w-12" />
                  <Skeleton class="h-3 w-1" />
                  <Skeleton class="h-3 w-16" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div v-else-if="plansError" class="text-center py-12 animate-in fade-in-50 duration-500">
        <div class="mx-auto max-w-md">
          <div class="card-error rounded-lg p-6">
            <div class="flex items-center justify-center w-10 h-10 mx-auto mb-3 bg-error/20 rounded-full">
              <svg class="w-5 h-5 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h4 class="text-base font-semibold text-error mb-1">Error Loading Plans</h4>
            <p class="text-sm text-error/80">{{ plansError.message }}</p>
          </div>
        </div>
      </div>

      <!-- Plans List -->
      <div v-else-if="plans && plans.length > 0">
        <div class="flex items-center gap-2 mb-3 animate-in slide-in-from-left-4 duration-300">
          <div class="flex items-center justify-center w-5 h-5 bg-success/20 rounded-full mb-1">
            <Grid3x3 class="icon-xs text-success" />
          </div>
          <p class="font-semibold text-foreground">Plan Options</p>
        </div>
        <div class="grid xl:grid-cols-4 lg:grid-cols-2 gap-3">
          <Card v-for="(plan, index) in plans" :key="plan.PlanName" :class="[
                        'relative transition-all duration-300 cursor-pointer py-1 group animate-in slide-in-from-bottom-4',
                        benefitSelectionStore.isPlanSelected(props.benefit.BenefitType, plan.PlanName)
                            ? 'border-primary bg-primary/5 border-2 shadow-lg transform scale-[1.02]'
                            : 'border-gray-200 hover:border-primary hover:shadow-md hover:bg-primary/5;'
                    ]" :style="{ animationDelay: `${index * 100}ms` }"
                @click="benefitSelectionStore.changeUserSelection(props.benefit.BenefitType, plan.PlanName, plan.DefSelected > 0 ? plan.DefSelected : 1)">
            <CardContent class="p-3">
              <BenefitCardItem :benefit-type="props.benefit.BenefitType" :plan-name="plan.PlanName" />
            </CardContent>
          </Card>
        </div>
      </div>

      <div v-else class="text-center py-12 animate-in fade-in-50 duration-500">
        <div class="mx-auto max-w-md">
          <div class="card-brand rounded-lg p-6">
            <div class="flex items-center justify-center w-10 h-10 mx-auto mb-3 bg-muted rounded-full">
              <svg class="w-5 h-5 text-muted-foreground" fill="none" stroke="currentColor"
                   viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m0 0V9a2 2 0 012-2h2m-6 6h16" />
              </svg>
            </div>
            <h4 class="text-base font-semibold text-foreground mb-1">No Plans Available</h4>
            <p class="text-sm text-muted-foreground">No plans available for {{ props.benefit.BenefitType }}
            </p>
          </div>
        </div>
      </div>

      <CoverageSelection
        v-if="!!benefitSelectionStore.getBenefitSelection(props.benefit.BenefitType)?.selectedPlanName && selectedPlan"
        :benefit-type="props.benefit.BenefitType" :benefit-plan="selectedPlan" />
    </CardContent>

    <CardFooter class="flex justify-end gap-2">
      <Button variant="outline" @click="showWaiveDialog = true">
        <X class="icon-sm" />
        Waive
      </Button>
      <Button v-if="benefitSelectionStore.getBenefitElectionStatus(props.benefit.BenefitType).value === 'pending'"
              variant="outline" disabled>
        <LoaderCircle class="icon-sm animate-spin" />
        Submitting...
      </Button>
      <Button v-else :disabled="!benefitSelectionStore.isBenefitSelected(props.benefit.BenefitType)"
              class="btn-brand-primary"
              @click="benefitSelectionStore.submitBenefitElection(props.benefit.BenefitType)">
        <div v-if="props.benefit.Status !== 'Completed'" class="flex items-center gap-2">
          <Send class="icon-sm" />
          Select Plan
        </div>
        <div v-else class="flex items-center gap-2">
          <Edit class="icon-sm" />
          Edit
        </div>
      </Button>
    </CardFooter>

    <WaiveBenefitDialog :type="'waive-benefit'" :open="showWaiveDialog" :benefit-type="props.benefit.BenefitType"
                        @update:open="showWaiveDialog = $event" @waive-selected="waiveBenefit" />
    <PlanRequirementDialog :benefit-type="props.benefit.BenefitType" />
  </Card>
</template>
