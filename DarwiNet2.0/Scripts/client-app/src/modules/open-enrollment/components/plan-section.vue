<script setup lang="ts">
import { useGetEmployeeBenefits } from '@open-enrollment/api';
import BenefitCards from '@open-enrollment/components/benefit-cards.vue';
import { Skeleton } from '@components/ui/skeleton';
import { Card, CardContent, CardFooter, CardHeader } from '@components/ui/card';
import { nextTick, watch } from 'vue';
import useBenefitStore from '@open-enrollment/stores/use-benefits-store';

const props = defineProps<{
    activeBenefitType?: string | null
}>()

const benefitStore = useBenefitStore()
const { data: employeeBenefits, isLoading: isBenefitsLoading, error: benefitsError } = useGetEmployeeBenefits(benefitStore.employeeEligibility!)

// Watch for active benefit type changes and scroll to the card
watch(() => props.activeBenefitType, async (newBenefitType) => {
    if (newBenefitType) {
        await nextTick()

        // Find the benefit card element by data attribute
        const element = document.querySelector(`[data-benefit-type="${newBenefitType}"]`) as HTMLElement

        if (element) {
            // Scroll to the benefit card with smooth behavior
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
                inline: 'nearest'
            })

            // Add a temporary highlight effect
            element.classList.add('ring-2', 'ring-primary', 'ring-offset-2')

            // Remove highlight after animation
            setTimeout(() => {
                element.classList.remove('ring-2', 'ring-primary', 'ring-offset-2')
            }, 2000)
        }
    }
}, { immediate: true })
</script>

<template>
    <div class="space-y-6">
        <!-- Loading State with Skeleton -->
        <div v-if="isBenefitsLoading" class="space-y-6">
            <!-- Skeleton for multiple benefit cards -->
            <div v-for="i in 3" :key="i" class="animate-in slide-in-from-bottom-4 duration-500"
                :style="{ animationDelay: `${i * 100}ms` }">
                <Card class="w-full pt-2">
                    <CardHeader class="pb-0">
                        <div class="flex items-center gap-6">
                            <div class="flex items-center gap-3">
                                <!-- Icon skeleton -->
                                <Skeleton class="w-6 h-6 rounded-full" />
                                <!-- Title skeleton -->
                                <Skeleton class="h-6 w-32" />
                            </div>

                            <div>
                                <!-- Badge skeleton -->
                                <Skeleton class="h-5 w-20 rounded-full" />
                            </div>
                        </div>
                    </CardHeader>

                    <CardContent class="pt-4">
                        <!-- Plans grid skeleton -->
                        <div class="grid xl:grid-cols-4 lg:grid-cols-2 gap-3">
                            <div v-for="j in 4" :key="j" class="animate-in slide-in-from-left-4 duration-300"
                                :style="{ animationDelay: `${(i * 100) + (j * 50)}ms` }">
                                <Card class="relative py-2">
                                    <CardContent>
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center gap-3">
                                                <!-- Radio button skeleton -->
                                                <Skeleton class="w-4 h-4 rounded-full" />
                                                <!-- Plan name skeleton -->
                                                <Skeleton class="h-4 w-24" />
                                            </div>
                                            <!-- More info skeleton -->
                                            <Skeleton class="h-4 w-16" />
                                        </div>

                                        <!-- Cost information skeleton -->
                                        <div class="space-y-2">
                                            <Skeleton class="h-3 w-full" />
                                            <Skeleton class="h-3 w-3/4" />
                                            <Skeleton class="h-3 w-1/2" />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>

                        <!-- Coverage selection skeleton -->
                        <div class="mt-6 animate-in slide-in-from-bottom-2 duration-400"
                            :style="{ animationDelay: `${i * 100 + 200}ms` }">
                            <Skeleton class="h-16 w-full rounded-lg" />
                        </div>
                    </CardContent>

                    <CardFooter class="flex justify-end gap-2">
                        <!-- Action buttons skeleton -->
                        <Skeleton class="h-9 w-20" />
                        <Skeleton class="h-9 w-24" />
                    </CardFooter>
                </Card>
            </div>
        </div>

        <!-- Error State -->
        <div v-else-if="benefitsError" class="text-center py-12 animate-in fade-in-50 duration-500">
            <div class="mx-auto max-w-md">
                <div class="rounded-lg bg-red-50 p-6 border border-red-200">
                    <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-red-800 mb-2">Error Loading Benefits</h3>
                    <p class="text-red-700">{{ benefitsError.message }}</p>
                </div>
            </div>
        </div>

        <!-- Benefits List -->
        <div v-else-if="employeeBenefits && employeeBenefits.length > 0" class="space-y-6">
            <div v-for="(benefit, index) in employeeBenefits" :key="benefit.BenefitType"
                class="animate-in slide-in-from-bottom-4 duration-500" :style="{ animationDelay: `${index * 150}ms` }">
                <BenefitCards :data-benefit-type="benefit.BenefitType" :benefit="benefit"
                    class="transition-all duration-300 hover:shadow-lg" />
            </div>
        </div>

        <!-- No Benefits -->
        <div v-else class="text-center py-12 animate-in fade-in-50 duration-500">
            <div class="mx-auto max-w-md">
                <div class="rounded-lg bg-gray-50 p-6 border border-gray-200">
                    <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m0 0V9a2 2 0 012-2h2m-6 6h16" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">No Benefits Available</h3>
                    <p class="text-gray-600">There are currently no benefits available for your enrollment.</p>
                </div>
            </div>
        </div>
    </div>
</template>
