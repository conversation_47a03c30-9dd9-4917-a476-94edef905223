<script setup lang="ts">
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
} from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Plus, Check, X, Users, LoaderCircle, Alert<PERSON>riangle, <PERSON>r<PERSON>he<PERSON>, Shield, Heart } from 'lucide-vue-next'
import { usePlanRequirementStore } from '@open-enrollment/stores/use-plan-requirement-store.ts'
import { useGetPlanEligibleDependents } from '@open-enrollment/composables/use-get-plan-eligible-dependents'
import { computed, ref, watch } from 'vue'
import { Button } from '@components/ui/button'
import useBenefitStore from '@open-enrollment/stores/use-benefits-store.ts'
import { useUpdatePlanDependentCoverage } from '@open-enrollment/composables/use-update-plan-dependent-coverage.ts'
import type {
  BenefitType,
  CoverageLevel,
  PlanEligibleDependentResponse,
} from '@open-enrollment/types/open-enrollment.types.ts'

const props = defineProps<{
  benefitType: BenefitType
  planName: string
  planType: string
  coverageLevel: CoverageLevel | undefined
}>()

// Stores
const planRequirementStore = usePlanRequirementStore()
const benefitStore = useBenefitStore()

// State (to keep the UI responsive without waiting for API - TODO consider doing optimistic updates)
const selectedDependents = ref<PlanEligibleDependentResponse[]>([])

// API call to get eligible dependents for this selection (plan and coverage level) 
const { data: eligibleDependents, isLoading, isError } = useGetPlanEligibleDependents(
  benefitStore.employeeEligibility!.SelectedYear,
  props.planName,
  props.planType,
  props.benefitType,
  props.coverageLevel,
)

// Sync API state with UI state
watch(eligibleDependents, (newDependents) => {
  if (newDependents) {
    selectedDependents.value = newDependents.filter(d => d.Selected)
  }
})

const updatePlanDependentCoverageFn = useUpdatePlanDependentCoverage(
  benefitStore.employeeEligibility!.SelectedYear,
  props.planName,
  props.planType,
  props.benefitType,
  props.coverageLevel)

const opened = computed(() => planRequirementStore.currentRequirement === 'dependent')

// Utility functions for formatting dependent information
const maskSSN = (ssn: string): string => {
  if (!ssn || ssn.length < 4) return ssn
  const lastFour = ssn.slice(-4)
  return `XXX-XX-${lastFour}`
}

const formatBirthDate = (birthDate: string): string => {
  if (!birthDate) return ''
  try {
    const date = new Date(birthDate)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  } catch {
    return birthDate
  }
}

const getRelationshipIcon = (relationship: string) => {
  const rel = relationship?.toLowerCase()
  if (rel?.includes('spouse') || rel?.includes('partner')) return Heart
  if (rel?.includes('child') || rel?.includes('son') || rel?.includes('daughter')) return Users
  return Shield
}

/**
 * Toggle the coverage of a dependent for a specific plan
 * @param ssn - the social security number of the dependent
 * @param isCovered - whether the dependent is covered or not for this plan
 */
const onToggleDependentCoverage = (ssn: string, isCovered: boolean) => {
  if (!eligibleDependents.value) return

  // Update the local state
  const dependent = eligibleDependents.value!.find(d => d.SSN === ssn)!
  dependent.Selected = isCovered

  // Make API call to update database
  updatePlanDependentCoverageFn.mutate({
    year: benefitStore.employeeEligibility!.SelectedYear,
    planName: props.planName,
    ssn,
    isCovered,
  })
}

const closeDialog = () => planRequirementStore.closeRequirementFlow()

/**
 * If the user wishes to add a dependent, exit the flow and navigate to the dependent management page
 */
const addDependent = () => {
  planRequirementStore.closeRequirementFlow()
  benefitStore.goToScreen('dependent-management')
}
</script>

<template>
  <Dialog :open="opened" @update:open="(value) => !value && closeDialog()">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-y-auto">
      <!-- Dialog Header with Icon and Description -->
      <DialogHeader class="pb-6">
        <DialogTitle class="text-title font-semibold flex items-center gap-3">
          <div class="flex items-center justify-center w-10 h-10 bg-brand-teal/10 rounded-full">
            <Users class="icon-sm text-brand-teal" />
          </div>
          <div class="flex-1">
            <div class="text-brand-teal">Dependent Selection</div>
            <div class="text-body-small font-medium text-muted-foreground mt-1">
              Select dependents to include in your {{ planName }} coverage
            </div>
          </div>
        </DialogTitle>
      </DialogHeader>

      <!-- Content Area -->
      <div class="space-y-4">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center py-12">
          <div class="flex items-center gap-3 text-brand-teal">
            <LoaderCircle class="icon-lg animate-spin" />
            <div>
              <div class="text-body font-medium">Loading dependents...</div>
              <div class="text-body-small text-muted-foreground">Please wait while we retrieve your eligible dependents
              </div>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div v-else-if="isError" class="bg-error/10 border border-error/20 p-6 rounded-lg">
          <div class="flex items-center gap-3">
            <div class="flex items-center justify-center w-12 h-12 bg-error/20 rounded-full">
              <AlertTriangle class="icon-lg text-error" />
            </div>
            <div class="flex-1">
              <h3 class="text-body font-semibold text-error">Unable to Load Dependents</h3>
              <p class="text-body-small text-error/80 mt-1">
                There was an error loading your eligible dependents. Please try again or contact support if the problem
                persists.
              </p>
            </div>
          </div>
        </div>

        <!-- Dependents List -->
        <div v-else-if="eligibleDependents?.length" class="space-y-4">
          <div class="flex items-center gap-2 mb-4">
            <UserCheck class="icon-sm text-muted-foreground" />
            <span class="text-body-small text-muted-foreground">
              Select the dependents you want to include in this plan
            </span>
            <Button @click="addDependent">
              <Plus class="icon-xs" />
              Add Dependent
            </Button>
          </div>

          <div v-for="dependent in selectedDependents" :key="dependent.SSN"
               class="border border-border rounded-lg p-4 hover:bg-muted/30 transition-colors"
               :class="{ 'border-primary bg-primary/10': dependent.Selected }">
            <div class="flex items-start gap-4">
              <!-- Selection Checkbox -->
              <div class="flex-shrink-0 pt-1">
                <Checkbox :model-value="dependent.Selected" class="flex-shrink-0"
                          @update:model-value="onToggleDependentCoverage(dependent.SSN, $event as boolean)" />
              </div>

              <!-- Dependent Information -->
              <div class="flex-1 min-w-0">
                <!-- Name and Selection Status -->
                <div class="flex items-start justify-between gap-3 mb-3">
                  <div class="flex items-center gap-3">
                    <div class="flex items-center justify-center w-8 h-8 bg-muted/30 rounded-full">
                      <component :is="getRelationshipIcon(dependent.Relationship)"
                                 class="icon-xs text-muted-foreground" />
                    </div>

                    <div>
                      <h4 class="text-body font-semibold text-foreground">{{ dependent.Name }}</h4>
                      <p class="text-body-small text-muted-foreground capitalize">{{ dependent.Relationship }}</p>
                    </div>
                  </div>

                  <div v-if="dependent.Selected" class="flex items-center gap-2 text-success">
                    <Check class="icon-xs" />
                    <span class="text-body-small font-medium">Selected</span>
                  </div>
                </div>

                <!-- SSN | DOB | Gender -->
                <div class="flex items-center justify-between gap-2 text-body-small text-muted-foreground">
                  <span>{{ maskSSN(dependent.SSN) }}</span>
                  <span> | </span>
                  <span>{{ formatBirthDate(dependent.BirthDate) }}</span>
                  <span> | </span>
                  <span>{{ dependent.Gender }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- No Dependents -->
        <div v-else class="text-center py-12">
          <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-muted/30 rounded-full">
            <Users class="icon-lg text-muted-foreground" />
          </div>
          <h3 class="text-body font-semibold text-foreground mb-2">No Eligible Dependents</h3>
          <p class="text-body-small text-muted-foreground">
            You don't have any eligible dependents for this plan at this time.
          </p>
        </div>
      </div>

      <!-- Footer Actions -->
      <DialogFooter class="pt-6 border-t border-border">
        <div class="flex justify-between items-center w-full">
          <Button variant="outline" :disabled="updatePlanDependentCoverageFn.isPending.value" class="px-6"
                  @click="planRequirementStore.closeRequirementFlow()">
            <X class="icon-sm" />
            Cancel
          </Button>

          <Button class="btn-brand-primary px-6" :disabled="updatePlanDependentCoverageFn.isPending.value"
                  @click="planRequirementStore.finishCurrentRequirement()">
            <LoaderCircle v-if="updatePlanDependentCoverageFn.isPending.value" class="icon-sm animate-spin" />
            <Check v-else class="icon-sm" />
            Confirm Selection
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
