<script setup lang="ts">
import { Button } from '@components/ui/button'
import { Card, CardContent } from '@components/ui/card'
import { Badge } from '@components/ui/badge'
import { useCalculateLifeAmount, useSaveLifeAmount, type CalculateLifeAmountRequest, type SelectPlanResponse, type SaveLifeAmountRequest } from '@open-enrollment/api'
import { X, LoaderCircle, Shield, DollarSign, Calendar, CreditCard, History, ChevronRight } from 'lucide-vue-next'
import { computed } from 'vue'
import useBenefitStore from '@open-enrollment/stores/use-benefits-store'
import { useBenefitsSelectionStore } from '@open-enrollment/stores/use-benefits-selection-store'
import { ScrollArea } from "@components/ui/scroll-area";

const props = defineProps<{
    open: boolean
    benefitType: string
    lifePlanResponse: NonNullable<SelectPlanResponse['LifePlanResponse']>
    selectedLifeCoverageAmount: number | null
}>()

const emits = defineEmits<{
    (e: 'selected-life-coverage-amount-changed', amount: number): void
    (e: 'move-to-next-step'): void
    (e: 'close-dialog'): void
}>()

const benefitStore = useBenefitStore()
const benefitSelectionStore = useBenefitsSelectionStore()

const calculateLifeAmountFn = useCalculateLifeAmount()
const calculateLifeAmount = (amount: number) => {
    const request: CalculateLifeAmountRequest = {
        planType: props.benefitType,
        planName: benefitSelectionStore.getBenefitSelection(props.benefitType)?.selectedPlanName ?? "",
        year: benefitStore.employeeEligibility!.SelectedYear,
        // TODO: need to avoid hardcoding this
        categoryLevel: 1,
        useGuaranteeAmount: props.lifePlanResponse.UseGuaranteeAmount,
        eeGuaranteeMaxAmount: props.lifePlanResponse.GuaranteeMaxAmount,
        approvedEE: props.lifePlanResponse.ApprovedEE,
        approvedAmountEE: props.lifePlanResponse.ApprovedAmountEE,
        inforceAmount: props.lifePlanResponse.InforceAmount,
        employeeIncrement: props.lifePlanResponse.EmployeeIncrement,
        employeeLifeAmount: props.lifePlanResponse.EmployeeLifeAmount,
        employeeLifePremium: props.lifePlanResponse.EmployeeLifePremium,
        electedLifeAmount: amount,
        employeePremium: props.lifePlanResponse.EmployeePremium,
        employeePerCheckPremium: props.lifePlanResponse.EmployeePerCheckPremium
    }

    calculateLifeAmountFn.mutate(request)
    emits('selected-life-coverage-amount-changed', amount)
}

const saveLifeAmountFn = useSaveLifeAmount()
const saveLifeAmount = () => {
    if (!props.selectedLifeCoverageAmount || !calculateLifeAmountFn.data.value) return

    const request: SaveLifeAmountRequest = {
        year: benefitStore.employeeEligibility!.SelectedYear,
        planType: props.benefitType,
        planName: benefitSelectionStore.getBenefitSelection(props.benefitType)?.selectedPlanName ?? "",
        categoryLevel: benefitSelectionStore.getBenefitSelection(props.benefitType)?.selectedCoverageLevel ?? 0,
        employeeLifeAmount: calculateLifeAmountFn.data.value?.EmployeeLifeAmount ?? 0,
        employeeLifePremium: calculateLifeAmountFn.data.value?.EmployeeLifePremium ?? 0,
        employeePerCheckPremium: calculateLifeAmountFn.data.value?.EmployeePerCheckPremium ?? 0,
        electedLifeAmount: props.selectedLifeCoverageAmount
    }

    saveLifeAmountFn.mutate(request, {
        onSuccess: () => emits('move-to-next-step')
    })
}

const formatCurrency = (amount: number) => amount.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD'
})

const lifeCoverageAmountOptions = computed(() => {
    const options = props.lifePlanResponse.AmountDropDownSource.map(x => ({
        value: x.Amount,
        label: x.Amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })
    }))

    return options.sort((a, b) => a.value - b.value)
})
</script>

<template>
    <div class="space-y-4">
        <Card class="border-border shadow-sm m-4">
            <CardContent class="space-y-4">
                <!-- Coverage Selection section -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="flex items-center justify-center w-8 h-8 bg-brand-burgundy/10 rounded-full">
                            <Shield class="icon-sm text-brand-burgundy" />
                        </div>
                        <div>
                            <h3 class="text-subtitle font-semibold text-foreground">Employee Coverage</h3>
                            <p class="text-body-small text-muted-foreground">Select your preferred coverage amount</p>
                        </div>
                    </div>
                    <Badge v-if="props.selectedLifeCoverageAmount" class="bg-brand-burgundy text-white">
                        {{ formatCurrency(props.selectedLifeCoverageAmount) }}
                    </Badge>
                </div>

                <!-- Coverage Amount Grid -->
                <ScrollArea class="h-[280px] border border-border rounded-lg">
                    <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 p-4">
                        <Button v-for="amount in lifeCoverageAmountOptions" :key="amount.value"
                            :disabled="calculateLifeAmountFn.isPending.value" variant="outline" :class="[
                                'h-16 text-left justify-between relative transition-all duration-200 hover:scale-[1.02] group',
                                props.selectedLifeCoverageAmount === amount.value
                                    ? 'border-brand-burgundy bg-brand-burgundy/5 hover:bg-brand-burgundy/10'
                                    : 'hover:border-brand-burgundy/50 hover:bg-brand-burgundy/5 hover:text-brand-burgundy'
                            ]" @click="calculateLifeAmount(amount.value)">
                            <div class="flex items-center gap-3">
                                <LoaderCircle
                                    v-if="calculateLifeAmountFn.isPending.value && props.selectedLifeCoverageAmount === amount.value"
                                    class="icon-sm animate-spin text-brand-burgundy" />
                                <DollarSign v-else class="icon-sm text-success" />
                                <span class="text-body font-semibold">{{ formatCurrency(amount.value) }}</span>
                            </div>

                            <!-- Selection Indicator -->
                            <div class="flex items-center gap-2">
                                <div v-if="props.selectedLifeCoverageAmount === amount.value && !calculateLifeAmountFn.isPending.value"
                                    class="selection-indicator-selected">
                                    <div
                                        class="w-2 h-2 rounded-full bg-brand-burgundy animate-in zoom-in-75 duration-200">
                                    </div>
                                </div>
                                <div v-else class="selection-indicator-unselected">
                                    <div class="w-2 h-2 rounded-full bg-transparent"></div>
                                </div>
                            </div>
                        </Button>
                    </div>
                </ScrollArea>

                <!-- Cost Breakdown Section -->
                <div class="flex items-center gap-3 mb-4">
                    <div class="flex items-center justify-center w-8 h-8 bg-brand-teal/10 rounded-full">
                        <CreditCard class="icon-sm text-brand-teal" />
                    </div>
                    <div>
                        <h3 class="text-subtitle font-semibold text-foreground">Cost Breakdown</h3>
                        <p class="text-body-small text-muted-foreground">Your premium and coverage details</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Monthly Premium -->
                    <div class="bg-success/10 border border-success/20 p-4 rounded-lg text-center">
                        <div class="flex items-center justify-center gap-2 mb-2">
                            <Calendar class="icon-sm text-success" />
                            <span class="text-body-small font-medium text-success">Monthly Premium</span>
                        </div>
                        <div class="text-xl font-bold text-success">
                            {{ formatCurrency(calculateLifeAmountFn.data.value?.EmployeeLifePremium ?? 0) }}
                        </div>
                    </div>

                    <!-- Per Paycheck -->
                    <div class="bg-brand-orange/10 border border-brand-orange/20 p-4 rounded-lg text-center">
                        <div class="flex items-center justify-center gap-2 mb-2">
                            <CreditCard class="icon-sm text-brand-orange" />
                            <span class="text-body-small font-medium text-brand-orange">Per Paycheck</span>
                        </div>
                        <div class="text-xl font-bold text-brand-orange">
                            {{ formatCurrency(calculateLifeAmountFn.data.value?.EmployeePerCheckPremium ?? 0) }}
                        </div>
                    </div>

                    <!-- Total Coverage -->
                    <div class="bg-brand-teal/10 border border-brand-teal/20 p-4 rounded-lg text-center">
                        <div class="flex items-center justify-center gap-2 mb-2">
                            <Shield class="icon-sm text-brand-teal" />
                            <span class="text-body-small font-medium text-brand-teal">Total Coverage</span>
                        </div>
                        <div class="text-xl font-bold text-brand-teal">
                            {{ formatCurrency(calculateLifeAmountFn.data.value?.EmployeeLifeAmount ?? 0) }}
                        </div>
                    </div>

                    <!-- Previous Election -->
                    <div class="bg-muted/50 border border-border p-4 rounded-lg text-center">
                        <div class="flex items-center justify-center gap-2 mb-2">
                            <History class="icon-sm text-muted-foreground" />
                            <span class="text-body-small font-medium text-muted-foreground">Previous Election</span>
                        </div>
                        <div class="text-xl font-bold text-muted-foreground">
                            {{ formatCurrency(props.lifePlanResponse.PreviousElectedAmount ?? 0) }}
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    </div>

    <!-- Footer Navigation -->
    <div class="flex justify-between items-center pt-4 border-t border-border bg-background sticky bottom-0 m-4">
        <Button variant="outline" @click="emits('close-dialog')">
            <X class="icon-sm" />
            Cancel
        </Button>
        <Button
            :disabled="!props.selectedLifeCoverageAmount || saveLifeAmountFn.isPending.value || !calculateLifeAmountFn.data.value"
            class="btn-brand-primary" @click="saveLifeAmount">
            <LoaderCircle v-if="saveLifeAmountFn.isPending.value" class="icon-sm animate-spin" />
            <span>Save & Continue</span>
            <ChevronRight class="icon-sm" />
        </Button>
    </div>
</template>
