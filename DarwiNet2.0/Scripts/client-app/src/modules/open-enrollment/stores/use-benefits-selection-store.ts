import { defineStore } from "pinia";
import { useSelectPlan } from "@open-enrollment/api";
import { computed, ref } from "vue";
import useBenefitStore from "@open-enrollment/stores/use-benefits-store";
import { useGetPlanDetails } from '@open-enrollment/composables/use-get-plan-details.ts'
import type { BenefitType, CoverageLevel, PlanRequirementType } from '@open-enrollment/types/open-enrollment.types.ts'
import { usePlanRequirementStore } from "@open-enrollment/stores/use-plan-requirement-store.ts";

type UserSelection = {
  selectedPlanName: string | null
  selectedCoverageLevel: CoverageLevel
}

export const useBenefitsSelectionStore = defineStore('benefits-selection', () => {
  const benefitStore = useBenefitStore()
  const planRequirementStore = usePlanRequirementStore()

  const planDetailsMutations = new Map<BenefitType, ReturnType<typeof useGetPlanDetails>>()
  const benefitElectionMutations = new Map<BenefitType, ReturnType<typeof useSelectPlan>>()

  // This state reflects the current user selection of plan and coverage for each benefit type.
  const userSelections = ref<Map<BenefitType, UserSelection>>(new Map())

  /**
   * Get the plan details mutation for a specific benefit type
   * @param benefitType - The benefit type (HEALTH, DENTAL, etc...)
   */
  const getPlanDetailsFn = (benefitType: BenefitType) => {
    if (!planDetailsMutations.has(benefitType)) {
      planDetailsMutations.set(benefitType, useGetPlanDetails())
    }
    return planDetailsMutations.get(benefitType)!
  }

  /**
   * Get the benefit election mutation for a specific benefit type
   * @param benefitType - The benefit type (HEALTH, DENTAL, etc...)
   */
  const getBenefitElectionFn = (benefitType: BenefitType) => {
    if (!benefitElectionMutations.has(benefitType)) {
      benefitElectionMutations.set(benefitType, useSelectPlan())
    }
    return benefitElectionMutations.get(benefitType)!
  }

  // A benefit is considered selected when both a plan and coverage level is selected
  const isBenefitSelected = (benefitType: BenefitType) => {
    const selection = userSelections.value.get(benefitType);

    // TODO - need to fix this -> selected coverage can be 0 when plan does not require selection
    // return !!selection && !!selection.selectedPlanName && selection.selectedCoverageLevel > 0;
    return !!selection && !!selection.selectedPlanName && selection.selectedCoverageLevel;
  }

  // Check if the plan is selected for a benefit type
  const isPlanSelected = (benefitType: string, planName: string) => {
    const selection = userSelections.value.get(benefitType);
    return !!selection && selection.selectedPlanName === planName;
  }

  // Get the user selection (plan + coverage level) for a benefit type
  const getBenefitSelection = (benefitType: string) => userSelections.value.get(benefitType)

  // Change the user selection (plan + coverage level) for a benefit type
  const changeUserSelection = (benefitType: BenefitType, planName: string | null, coverageLevel: CoverageLevel) => {
    // Update the user selection
    if (!userSelections.value.has(benefitType)) {
      userSelections.value.set(benefitType, {
        selectedPlanName: planName,
        selectedCoverageLevel: coverageLevel
      })
    } else {
      userSelections.value.get(benefitType)!.selectedPlanName = planName
      userSelections.value.get(benefitType)!.selectedCoverageLevel = coverageLevel
    }

    const selection = userSelections.value.get(benefitType)

    // Fetch the details (pricing + info) for the selected plan + coverage level
    if (planName && coverageLevel && !!benefitStore.employeeEligibility) {
      const planDetailsMutation = getPlanDetailsFn(benefitType)
      planDetailsMutation.mutate({
        transactionId: benefitStore.employeeEligibility.TransactionId,
        year: benefitStore.employeeEligibility.SelectedYear,
        benefitType,
        enrollmentType: benefitStore.employeeEligibility.EnrollmentType,
        oeEffectiveDate: benefitStore.employeeEligibility.OeEffectiveDate,
        coverageLevel: selection?.selectedCoverageLevel ?? 0,
        planName: selection?.selectedPlanName ?? ""
      })
    }
  }

  /**
   * Submit the benefit election for a specific benefit type.
   * Based on user selection, update the requirements of the steps user must complete before finalizing the plan election.
   * @param benefitType - The benefit type (HEALTH, DENTAL, etc...)
   */
  const submitBenefitElection = (benefitType: BenefitType) => {
    if (!isBenefitSelected(benefitType) || !benefitStore.employeeEligibility) return

    const selection = userSelections.value.get(benefitType)
    if (!selection) return

    // Find the requirement step of the plan
    const planDetails = getPlanDetailsFn(benefitType).data.value
    const planRequirementList = [
      ["beneficiary", planDetails?.BeneficiaryRequired],
      ["pcp", planDetails?.PcpRequired],
      ["dependent", planDetails?.DependentRequired],
      ["amount-entry", planDetails?.AmountEntryRequired] 
    ] as [PlanRequirementType, boolean][]
    
    // Add the requirement to the list if it is required
    const requirements = [] as PlanRequirementType[]
    planRequirementList.forEach(([requirement, isRequired]) => {
      if (isRequired) {
        requirements.push(requirement)
      }
    })
    
    planRequirementStore.startRequirementFlow(requirements)

    // Submit the benefit election
    const benefitElectionMutation = getBenefitElectionFn(benefitType)
    benefitElectionMutation.mutate({
      transactionId: benefitStore.employeeEligibility.TransactionId,
      year: benefitStore.employeeEligibility.SelectedYear,
      benefitType,
      coverageLevel: selection.selectedCoverageLevel,
      planName: selection.selectedPlanName!
    })
  }

  // Get benefit-specific computed properties and API state
  const getBenefitRequiredBeneficiary = (benefitType: BenefitType) => {
    const planDetailsMutation = getPlanDetailsFn(benefitType)
    return computed(() => planDetailsMutation.data.value?.BeneficiaryRequired)
  }

  const getBenefitPlanDetailsData = (benefitType: BenefitType) => {
    const planDetailsMutation = getPlanDetailsFn(benefitType)
    return planDetailsMutation.data
  }

  const getBenefitElectionData = (benefitType: BenefitType) => {
    const benefitElectionMutation = getBenefitElectionFn(benefitType)
    return benefitElectionMutation.data
  }

  const getBenefitElectionStatus = (benefitType: BenefitType) => {
    const benefitElectionMutation = getBenefitElectionFn(benefitType)
    return benefitElectionMutation.status
  }

  const getBenefitPlanDetailsStatus = (benefitType: BenefitType) => {
    const planDetailsMutation = getPlanDetailsFn(benefitType)
    return planDetailsMutation.status
  }

  const getBenefitPlanDetailsIsPending = (benefitType: BenefitType) => {
    const planDetailsMutation = getPlanDetailsFn(benefitType)
    return planDetailsMutation.isPending
  }

  return {
    userSelections,
    isBenefitSelected,
    isPlanSelected,
    getBenefitSelection,
    changeUserSelection,
    submitBenefitElection,

    // Benefit-specific API state getters
    getBenefitRequiredBeneficiary,
    getBenefitPlanDetailsData,
    getBenefitElectionData,
    getBenefitElectionStatus,
    getBenefitPlanDetailsStatus,
    getBenefitPlanDetailsIsPending,
  }
})