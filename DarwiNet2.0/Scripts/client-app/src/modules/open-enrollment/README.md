# Open Enrollment Module

Module for managing employee benefit enrollment during open enrollment periods.

## 📁 Project Structure

```md
src/modules/open-enrollment/
├── api/
│   ├── open-enrollment.apis.ts      # API functions
│   ├── open-enrollment.hooks.ts     # Vue Query hooks
│   └── open-enrollment.types.ts     # TypeScript type definitions
├── components/
│   ├── BenefitPlan/
│   │   ├── benefit-card.vue         # Main benefit plan card
│   │   └── waive-benefit-dialog.vue # Waive benefit dialog
│   ├── BenefitDetails/
│   │   ├── cost-information-section.vue    # Cost display
│   │   ├── coverage-selection.vue          # Coverage level selection
│   │   └── plan-details-hover-card.vue     # Plan details popup
│   ├── benefit-icon.vue             # Benefit type icons
│   ├── benefit-status-badge.vue     # Status indicators
│   ├── benefit-coverage-badge.vue   # Coverage level badges
│   ├── benefit-summary.vue          # Individual benefit summary
│   ├── enrollment-summary.vue       # Overall enrollment summary
│   └── plan-section.vue             # Plans container
├── index.vue                        # Main application component
├── main.ts                          # Application entry point
└── README.md                        # This file
```

## 🔌 API Layer

### Core API Functions (`open-enrollment.apis.ts`)

| Function | Description | Parameters |
|----------|-------------|------------|
| `getEmployeeEligibility()` | Get employee's open enrollment eligibility | None |
| `getEmployeeBenefits()` | Get employee's available benefits | `year`, `transactionId`, `oeEffectiveDate`, `eeType` |
| `getPlansList()` | Get available plans for a benefit type | `year`, `benefitType` |
| `getPlanDetails()` | Get detailed plan information | `year`, `request` |
| `selectPlan()` | Select a benefit plan | `transactionId`, `year`, `request` |
| `waivePlan()` | Waive a benefit plan | `year`, `planType`, `benefitWaiverReason`, `employeeEffectiveDate` |
| `getWaiveReasons()` | Get available waiver reasons | None |
| `getOpenEnrollmentSummary()` | Get enrollment summary | `year`, `transactionId`, `oeEffectiveDate`, `eeType` |

### Vue Query Hooks (`open-enrollment.hooks.ts`)

| Hook | Purpose | Features |
|------|---------|----------|
| `useGetEmployeeEligibility()` | Fetch employee eligibility | 5-minute cache |
| `useGetEmployeeBenefits()` | Fetch employee benefits | Auto-enabled when eligibility available |
| `useGetPlansList()` | Fetch plans for benefit type | Conditional enabling |
| `useGetPlanDetails()` | Fetch plan details (mutation) | Cache management |
| `useSelectPlan()` | Select plan mutation | Success/error handling |
| `useWaivePlan()` | Waive plan mutation | Query invalidation |
| `useGetWaiveReasons()` | Fetch waiver reasons | 5-minute cache |
| `useGetOpenEnrollmentSummary()` | Fetch enrollment summary | Real-time updates |

## 🚀 Features

### Core Functionality

- ✅ Employee eligibility checking
- ✅ Benefit plan browsing and selection
- ✅ Coverage level selection
- ✅ Real-time cost calculations
- ✅ Benefit waiving with reasons
- ✅ Enrollment summary tracking
- ✅ Plan details with hover cards
