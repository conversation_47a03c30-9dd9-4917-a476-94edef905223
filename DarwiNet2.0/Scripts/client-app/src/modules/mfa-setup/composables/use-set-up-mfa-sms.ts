import {useMutation} from '@tanstack/vue-query'
import {setupMfaForSms} from '@modules/mfa-setup/api'
import type {SetupMfaForSmsRequest} from "@modules/mfa-setup/types/mfa-setup.types.ts";

/**
 * Composable to verify the phone number user provided and set up MFA for it
 */
export const useSetupMfaSms = () =>
    useMutation({
        mutationFn: (request: SetupMfaForSmsRequest) =>
            setupMfaForSms(request),
        onSuccess: (data) => {
            // Check if MFA setup was successful and redirect back to the login flow
            if (data.RedirectUrl) {
                window.location.href = data.RedirectUrl;
            }
        }
    })
