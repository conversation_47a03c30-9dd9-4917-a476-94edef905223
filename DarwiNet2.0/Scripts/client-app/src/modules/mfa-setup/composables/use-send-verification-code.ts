import { useMutation } from '@tanstack/vue-query'
import { sendVerificationCode } from '@modules/mfa-setup/api'
import type {SendVerificationCodeRequest} from "@modules/mfa-setup/types/mfa-setup.types.ts";

/**
 * Composable to send code to the phone number for verification
 */
export const useSendVerificationCode = () =>
    useMutation({
      mutationFn: ({PhoneNumber, CompanyId}: SendVerificationCodeRequest) =>
          sendVerificationCode({PhoneNumber, CompanyId}),
    })