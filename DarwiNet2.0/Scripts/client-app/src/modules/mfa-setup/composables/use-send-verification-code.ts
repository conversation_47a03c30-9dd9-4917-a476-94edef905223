import { useMutation } from '@tanstack/vue-query'
import { sendVerificationCode } from '@modules/mfa-setup/api'

/**
 * Composable to send code to the phone number for verification
 */
export const useSendVerificationCode = () => {
  return useMutation({
    mutationFn: ({ phoneNumber, companyId, userId }: { phoneNumber: string, companyId: number, userId: string }) =>
      sendVerificationCode({ phoneNumber, companyId, userId }),
  })
}