<script setup lang="ts">
import {Input} from '@components/ui/input'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {Button} from '@components/ui/button'
import {Send, Phone, Loader2} from 'lucide-vue-next'
import {CardDescription, Card, CardHeader, CardTitle, CardContent, CardFooter} from '@components/ui/card'
import {z} from "zod";
import {toTypedSchema} from "@vee-validate/zod";
import {useForm} from "vee-validate";
import {watch} from "vue";
import {useMfaSetupStore} from "@modules/stores/use-mfa-setup-store.ts";

// Store/Composable
const mfaSetupStore = useMfaSetupStore()

// Enhanced phone number validation
const formSchema = toTypedSchema(z.object({
  phoneNumber: z.string()
      .min(10, 'Phone number must be at least 10 digits')
      .max(15, 'Phone number must be at most 15 digits')
      .regex(/^[\d\s()-+]+$/, 'Please enter a valid phone number'),
}))

const {handleSubmit, values} = useForm({validationSchema: formSchema})

// Watch for phone number changes and update form
watch(() => mfaSetupStore.phoneNumber, (newValue) => {
  if (newValue) {
    values.phoneNumber = newValue
  }
}, {
  immediate: true
})
const onSubmit = handleSubmit((values) => mfaSetupStore.sendVerificationCode(values.phoneNumber))
</script>

<template>
  <Card class="w-[500]">
    <CardHeader class="text-center">
      <div class="flex flex-col items-center justify-center">
        <div class="flex items-center justify-center rounded-full w-12 h-12 transition-colors duration-200"
             :class="'bg-brand-green/20'">
          <Phone class="icon-lg text-brand-green"/>
        </div>
        <CardTitle class="mt-3">
          Set up SMS Multi-Factor Authentication
        </CardTitle>
      </div>
      <CardDescription>
        Add your phone number to enable two-factor authentication and security notifications
      </CardDescription>
    </CardHeader>

    <CardContent class="space-y-4">
      <form class="space-y-4" @submit="onSubmit">
        <FormField v-slot="{componentField}" name="phoneNumber">
          <FormItem class="space-y-2">
            <FormLabel for="phoneNumber">Phone Number</FormLabel>
            <FormControl>
              <Input
                  id="phoneNumber"
                  type="tel"
                  inputmode="tel"
                  placeholder="(*************"
                  v-bind="componentField"
                  :disabled="mfaSetupStore.isLoading"
                  class="text-center"
              />
            </FormControl>
            <FormMessage/>
            <p class="text-xs text-muted-foreground">
              Enter your phone number to receive a verification code
            </p>
          </FormItem>
        </FormField>

        <CardFooter class="px-0 pt-4">
          <Button
              class="w-full"
              size="lg"
              type="submit"
              :disabled="mfaSetupStore.isLoading"
          >
            <Send v-if="!mfaSetupStore.isLoading" class="icon-sm"/>
            <Loader2 v-else class="icon-sm animate-spin"/>
            {{ mfaSetupStore.isLoading ? 'Sending code...' : 'Send' }}
          </Button>
        </CardFooter>
      </form>
    </CardContent>
  </Card>
</template>
