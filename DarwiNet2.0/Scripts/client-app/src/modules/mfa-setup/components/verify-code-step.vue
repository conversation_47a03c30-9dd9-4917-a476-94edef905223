<script setup lang="ts">
import {Shield, CheckCircle2, ArrowLeft, RotateCcw} from 'lucide-vue-next'
import {Button} from '@components/ui/button'
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {CardDescription, Card, CardHeader, CardTitle, CardContent} from '@components/ui/card'
import {Input} from '@components/ui/input'
import {toTypedSchema} from "@vee-validate/zod";
import {z} from "zod";
import {useForm} from "vee-validate";
import {watch, computed, nextTick, onMounted} from "vue";
import {useMfaSetupStore} from "@modules/stores/use-mfa-setup-store.ts";

// Store/Composable
const mfaSetupStore = useMfaSetupStore()

// Check if we have the required data, redirect to set up page if not
onMounted(() => {
  if (!mfaSetupStore.verificationSid || !mfaSetupStore.phoneNumber) {
    mfaSetupStore.goBackToSetupStep()
    return
  }
})

// Enhanced validation for verification code
const formSchema = toTypedSchema(z.object({
  verificationCode: z.string()
      .length(6, 'Verification code must be exactly 6 digits')
      .regex(/^\d{6}$/, 'Verification code must contain only numbers')
}))

const {handleSubmit, resetForm, values} = useForm({
  validationSchema: formSchema,
})
const maskedPhoneNumber = computed(() => {
  const currentPhone = mfaSetupStore.phoneNumber
  if (!currentPhone) return ''

  const phone = currentPhone.replace(/\D/g, '')
  if (phone.length >= 4) {
    return phone.slice(0, -4).replace(/\d/g, '*') + phone.slice(-4)
  }
  return currentPhone
})

// Watch for state changes
watch([
  () => mfaSetupStore.phoneNumber,
  () => mfaSetupStore.verificationSid
], () => resetForm())

// Auto-format verification code input
watch(() => values.verificationCode, (newValue) => {
  if (newValue && /^\d{6}$/.test(newValue)) {
    // Auto-submit when 6 digits are entered
    nextTick(() => {
      if (!mfaSetupStore.isLoading) {
        onSubmit()
      }
    })
  }
})


const onSubmit = handleSubmit((values) => mfaSetupStore.setupMfaForSms(values.verificationCode))


mfaSetupStore.startResendCooldown()
</script>

<template>
  <Card class="w-[500]">
    <CardHeader class="text-center">
      <div class="flex flex-col items-center justify-center">
        <div class="flex items-center justify-center rounded-full w-12 h-12 transition-colors duration-200"
             :class="[ 'bg-brand-green/20'
             ]">
          <Shield class="icon-lg text-brand-green"/>
        </div>
        <CardTitle class="mt-3">
          {{ mfaSetupStore.isRedirecting ? 'Setup Complete!' : 'Verify Your Phone Number' }}
        </CardTitle>
      </div>
      <CardDescription>
        <span v-if="mfaSetupStore.isRedirecting">
          Your multi-factor authentication has been set up successfully
        </span>
        <span v-else>
          We sent a 6-digit code to <strong>{{ maskedPhoneNumber }}</strong>
        </span>
      </CardDescription>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- Redirecting Message -->
      <div v-if="mfaSetupStore.isRedirecting" class="bg-success/10 border border-success/20 p-4 rounded-lg">
        <div class="flex items-center gap-3">
          <CheckCircle2 class="icon-sm text-success"/>
          <div>
            <p class="text-xs text-success/80 mt-1">
              You will be redirected automatically...
            </p>
          </div>
        </div>
      </div>

      <!-- Form -->
      <form v-if="!mfaSetupStore.isRedirecting" class="space-y-4" @submit="onSubmit">
        <FormField v-slot="{componentField}" name="verificationCode">
          <FormItem class="space-y-2">
            <FormLabel for="verificationCode">Verification Code</FormLabel>
            <FormControl>
              <Input
                  id="verificationCode"
                  autocomplete="one-time-code"
                  inputmode="numeric"
                  maxlength="6"
                  v-bind="componentField"
                  :disabled="mfaSetupStore.isLoading"
                  class="text-center text-2xl font-mono tracking-widest"
                  @input="(e: Event) => {
                  const target = e.target as HTMLInputElement
                  target.value = target.value.replace(/\D/g, '').slice(0, 6) 
                  componentField['onInput']?.(e)
                }"
              />
            </FormControl>
            <FormMessage/>
            <p class="text-xs text-muted-foreground text-center">
              Enter the 6-digit code sent to your phone
            </p>
          </FormItem>
        </FormField>

        <!-- Action Buttons -->
        <div class="space-y-3">
          <Button
              class="w-full"
              size="lg"
              type="submit"
              :disabled="mfaSetupStore.isLoading"
          >
            <CheckCircle2 v-if="!mfaSetupStore.isLoading" class="icon-sm"/>
            <Shield v-else-if="!mfaSetupStore.isLoading" class="icon-sm"/>
            <div v-else class="flex items-center gap-2">
              <div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
            </div>
            {{ mfaSetupStore.isLoading ? 'Verifying...' : 'Verify Code' }}
          </Button>

          <!-- Secondary Actions -->
          <div class="flex items-center justify-between gap-3">
            <Button
                type="button"
                variant="ghost"
                size="sm"
                :disabled="mfaSetupStore.isLoading"
                class="flex items-center gap-2"
                @click="mfaSetupStore.goBackToSetupStep()"
            >
              <ArrowLeft class="icon-xs"/>
              Change number
            </Button>

            <Button
                type="button"
                variant="ghost"
                size="sm"
                :disabled="!mfaSetupStore.canResend"
                :aria-label="mfaSetupStore.canResend ? 'Resend Code' : `Resend available in ${mfaSetupStore.resendCooldown} seconds`"
                class="flex items-center gap-2"
                @click="mfaSetupStore.resendCode(resetForm)"
            >
              <RotateCcw v-if="!mfaSetupStore.isLoading" class="icon-xs"/>
              <div v-else class="animate-spin rounded-full h-3 w-3 border-2 border-current border-t-transparent"></div>
              <span v-if="mfaSetupStore.resendCooldown > 0">
                Resend in {{ mfaSetupStore.resendCooldown }}s
              </span>
              <span v-else-if="mfaSetupStore.isLoading">
                Sending...
              </span>
              <span v-else>
                Resend code
              </span>
            </Button>
          </div>
        </div>
      </form>

      <!-- Redirecting State -->
      <div v-else class="text-center py-8">
        <div class="flex items-center justify-center gap-3 mb-4">
          <div class="animate-spin rounded-full h-6 w-6 border-2 border-success border-t-transparent"></div>
          <span class="text-success font-medium">Preparing your account...</span>
        </div>
        <p class="text-sm text-muted-foreground">
          This will only take a moment
        </p>
      </div>
    </CardContent>
  </Card>
</template>

