import { createClient } from '@/api'
import type {
  SendVerificationCodeRequest,
  SendVerificationCodeResponse,
  SetupMfaForSmsRequest, SetupMfaForSmsResponse
} from "@modules/mfa-setup/types/mfa-setup.types.ts";

const mfaApi = createClient({ baseURL: '/TwoFactorAuthenticationApi/' })

export const routes = {
  sendVerificationCode: '/SendVerificationCodeForMfa',
  setupMfaForSms: '/SetupMfaForSms',
}

/**
 * Send verification code
 */
export const sendVerificationCode = (request: SendVerificationCodeRequest) => mfaApi.post<SendVerificationCodeResponse>(routes.sendVerificationCode, request)

/**
 * Verify the user's provided phone number   
 */
export const setupMfaForSms = (request: SetupMfaForSmsRequest) => mfaApi.post<SetupMfaForSmsResponse>(routes.setupMfaForSms, request)

