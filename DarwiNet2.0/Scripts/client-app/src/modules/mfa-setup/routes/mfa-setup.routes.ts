import { createRouter, createWebHashHistory } from 'vue-router'
import SendVerificationCodeStep from '@modules/mfa-setup/components/send-verification-code-step.vue'
import VerifyCodeStep from '@modules/mfa-setup/components/verify-code-step.vue'

const routes = [
  {
    path: '/',
    redirect: '/Setup'
  },
  {
    path: '/Setup',
    name: 'Setup',
    component: SendVerificationCodeStep
  },
  {
    path: '/Verify',
    name: 'Verify',
    component: VerifyCodeStep,
    meta: { requiresVerificationSid: true }
  }
]

export const router = createRouter({
  history: createWebHashHistory(),
  routes
})
