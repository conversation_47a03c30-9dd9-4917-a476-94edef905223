import {createApp} from 'vue'
import {createPinia} from 'pinia'
import MfaSetupApp from './index.vue'
import {VueQueryPlugin} from '@tanstack/vue-query'
import {router} from "@modules/mfa-setup/routes/mfa-setup.routes.ts"
import '@/index.css'
import type {MfaSetupContext} from "@modules/mfa-setup/types/mfa-setup.types.ts";

/**
 * Read the script tag to get the context data 
 */
export function readAppContext(): MfaSetupContext {
  // First try CSP-friendly script tag
  const el = document.getElementById('app-context') as HTMLScriptElement | null
  if (el?.textContent?.trim()) {
    return JSON.parse(el.textContent) as MfaSetupContext
  }
  throw new Error('App context not found on page')
}

const app = createApp(MfaSetupApp)
app.use(createPinia())
app.use(VueQueryPlugin)
app.use(router)

export const MFA_SETUP_CONTEXT_KEY = 'mfa-setup-context'
const context = readAppContext()! as MfaSetupContext
app.provide(MFA_SETUP_CONTEXT_KEY, context)

app.mount('#mfa-setup-app')
