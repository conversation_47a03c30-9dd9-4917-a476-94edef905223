using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Models;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using DarwiNet2._0.ViewModels.Documents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.DNetSynch
{
    // TODO: TW-MF: Entire HTTP Session logic needs reworked. This is reference WAY TOO MANY times and included in business logic way too much. This is making testing & maintaining code impossible.
    public static class GlobalVariables
    {
        public static List<EEDocView> LibraryDocuments
        {
            get
            {
                List<EEDocView> tmpList = (List<EEDocView>)HttpContext.Current.Session["LibraryDocuments" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["LibraryDocuments" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }

        public static string RedirectToOEFlag
        {
            get { return HttpContext.Current.Session["RedirectToOEFlag" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["RedirectToOEFlag" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string SystemDB
        {
            get { return HttpContext.Current.Session["SystemDB" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["SystemDB +  HttpContext.Current.Session.SessionID.ToString()" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        //Added by Naresh Jakkam on 24SEP2020
        public static string isHSA
        {
            get { return HttpContext.Current.Session["isHSA" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["isHSA" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        //End

        public static string GlobalDB
        {
            get { return HttpContext.Current.Session["GlobalDB" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["GlobalDB +  HttpContext.Current.Session.SessionID.ToString()" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string MenuLogoSmall
        {
            get { return HttpContext.Current.Session["MenuLogoS" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["MenuLogoS" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string isBenefitEdit
        {
            get { return HttpContext.Current.Session["isBenefitEdit" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["isBenefitEdit" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string isDeductionEdit
        {
            get { return HttpContext.Current.Session["isDeductionEdit" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["isDeductionEdit" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string MenuLogoLarge
        {
            get { return HttpContext.Current.Session["MenuLogoB" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["MenuLogoB" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static int CompanyID
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["CompanyID" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["CompanyID" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }

        public static string Customer
        {
            get { return HttpContext.Current.Session["Customer" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Customer" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string Company
        {
            get { return HttpContext.Current.Session["Company" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Company" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string DarwinCompany
        {
            get { return HttpContext.Current.Session["DarwinCompany" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["DarwinCompany" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string SystemConnection
        {
            get { return HttpContext.Current.Session["SystemConnection" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["SystemConnection" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string GlobalConnection
        {
            get { return HttpContext.Current.Session["GlobalConnection" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["GlobalConnection" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string DNetLocation
        {
            get { return HttpContext.Current.Session["DNetLocation" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["DNetLocation" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string DNetURL
        {
            get { return HttpContext.Current.Session["DNetURL" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["DNetURL" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string DNetAssets
        {
            get { return HttpContext.Current.Session["DNetAssets" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["DNetAssets" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string CompanyFolder
        {
            get { return HttpContext.Current.Session["CompanyFolder" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["CompanyFolder" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ClientFolder
        {
            get { return HttpContext.Current.Session["ClientFolder" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ClientFolder" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ExportFolder
        {
            get { return HttpContext.Current.Session["ExportFolder" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ExportFolder" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static int? LanguageID
        {
            get { return HttpContext.Current.Session["LanguageID" + HttpContext.Current.Session.SessionID.ToString()] as int?; }
            set { HttpContext.Current.Session["LanguageID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string Client
        {
            get { return HttpContext.Current.Session["Client" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Client" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ClientMasterClientID
        {
            get { return HttpContext.Current.Session["ClientMasterClientID" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ClientMasterClientID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ClientSubClientID
        {
            get { return HttpContext.Current.Session["ClientSubClientID" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ClientSubClientID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static List<CodeDescription> ClientSubClients
        {
            get { return HttpContext.Current.Session["ClientSubClients" + HttpContext.Current.Session.SessionID.ToString()] as List<CodeDescription>; }
            set { HttpContext.Current.Session["ClientSubClients" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string EmployeeID
        {
            get { return HttpContext.Current.Session["EmployeeID" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["EmployeeID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        /// <summary>
        /// This will help decide which saml configuration to generate in SamlConfigurationResolver
        /// </summary>
        public static string SamlNameConfiguration
        {
            get
            {
                return HttpContext.Current.Session["SAMLConfiguration" + HttpContext.Current.Session.SessionID] as string;
            }
            set
            {
                HttpContext.Current.Session["SAMLConfiguration" + HttpContext.Current.Session.SessionID] = value;
            }
        }

        public static string KronosIntegrationSettings
        {
            get
            {
                return HttpContext.Current.Session["KronosIntegrationSettings" + HttpContext.Current.Session.SessionID] as string;
            }
            set
            {
                HttpContext.Current.Session["KronosIntegrationSettings" + HttpContext.Current.Session.SessionID] = value;
            }
        }

        public static string DNETLevel
        {
            get { return HttpContext.Current.Session["DNETLevel" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["DNETLevel" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string DNETOwnerID
        {
            get { if (HttpContext.Current == null) return null; else return HttpContext.Current.Session[$"DNETOwnerID{HttpContext.Current.Session.SessionID}"] as string; }
            set { if (HttpContext.Current == null) return; else HttpContext.Current.Session["DNETOwnerID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string LoggedInAsUser
        {
            get { return HttpContext.Current.Session["LoggedInAsUser" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LoggedInAsUser" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string LoggedInUserPWD
        {
            get { return HttpContext.Current.Session["LoggedInUserPWD" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LoggedInUserPWD" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static int TransactionId
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["TransactionId" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["TransactionId" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }
        public static string DNETOwnerPWD
        {
            get { return HttpContext.Current.Session["DNETOwnerPWD" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["DNETOwnerPWD" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string OwnerIP
        {
            get { return HttpContext.Current.Session["OwnerIP" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["OwnerIP" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string EIN
        {
            get { return HttpContext.Current.Session["ClientEIN" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ClientEIN" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string PEOName
        {
            get { return HttpContext.Current.Session["PEOName" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["PEOName" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static string PEOPhone
        {
            get { return HttpContext.Current.Session["PEOPhone" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["PEOPhone" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string PEOMail
        {
            get { return HttpContext.Current.Session["PEOMail" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["PEOMail" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string MailHost
        {
            get { return HttpContext.Current.Session["MailHost" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["MailHost" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string MailPort
        {
            get { return HttpContext.Current.Session["MailPort" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["MailPort" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string MailHostPWD
        {
            get { return HttpContext.Current.Session["MailHostPWD" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["MailHostPWD" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string MailHostUser
        {
            get { return HttpContext.Current.Session["MailHostUser" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["MailHostUser" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string PEOMailFrom
        {
            get { return HttpContext.Current.Session["PEOMailFrom" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["PEOMailFrom" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string PEOMailFromAddress
        {
            get { return HttpContext.Current.Session["PEOMailFromAddress" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["PEOMailFromAddress" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool MailSSL
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["MailSSL" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["MailSSL" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool isThereMultiCharge
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["isThereMultiCharge" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["isThereMultiCharge" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool isPayrollUser
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["isPayrollUser" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["isPayrollUser" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool UseEncryption
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["UseEncryption" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["UseEncryption" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool LoggedInAs
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["LoggedInAs" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["LoggedInAs" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string WelcomeBackground
        {
            get { return HttpContext.Current.Session["WelcomeBackground" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["WelcomeBackground" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string LoginAsSelectedClient
        {
            get { return HttpContext.Current.Session["LoginAsSelectedClient" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LoginAsSelectedClient" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool UseAutoUpload
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["UseAutoUpload" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["UseAutoUpload" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string CompanyConnection
        {
            get { return HttpContext.Current.Session["CompanyConnection" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["CompanyConnection" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ColorSchema
        {
            get { return HttpContext.Current.Session["ColorSchema" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ColorSchema" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string Css1
        {
            get { return HttpContext.Current.Session["Css1" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Css1" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string Css2
        {
            get { return HttpContext.Current.Session["Css2" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Css2" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static DateTime? OBEmployeeDueDate
        {
            get { return Convert.ToDateTime(HttpContext.Current.Session["DueDate" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["DueDate" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToDateTime(value); }
        }

        public static string OBEmployeeName
        {
            get { return HttpContext.Current.Session["OBEmployeeName" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["OBEmployeeName" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string GH_ApplicantID
        {
            get { return HttpContext.Current.Session["GHApplicantID" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["GHApplicantID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string AppTest
        {
            get { return HttpContext.Current.Application["AppTest" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Application["AppTest" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ReturnSingleSignOn
        {
            get { return HttpContext.Current.Session["ReturnSignOn" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ReturnSignOn" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ProfileName
        {
            get { return HttpContext.Current.Session["ProfileName" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ProfileName" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool UseRealTimeSearch
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["UseRealTimeSearch" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["UseRealTimeSearch" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string ReturnTimedOut
        {
            get
            {
                var httpCookie = HttpContext.Current.Request.Cookies["ReturnURL" + HttpContext.Current.Session.SessionID.ToString()];
                if (httpCookie != null)
                    return httpCookie.Value ?? string.Empty;
                return string.Empty;
            }
            set
            {
                HttpCookie cookie = new HttpCookie("ReturnURL");
                cookie.Value = Convert.ToString(value);
                cookie.Expires = DateTime.Now.AddHours(8);
                HttpContext.Current.Response.Cookies.Add(cookie);
            }
        }

        public static string ClientName
        {
            get { return HttpContext.Current.Session["ClientName" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ClientName" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool MaskSSN
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["MaskSSN" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["MaskSSN" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool ClientMaskSSN
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["ClientMaskSSN" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["ClientMaskSSN" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string DnetRoleID
        {
            get { return HttpContext.Current.Session["UserRole" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["UserRole" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static List<MenuItem> DnetRoleMenu
        {
            get { return HttpContext.Current.Session["UserMenu" + HttpContext.Current.Session.SessionID.ToString()] as List<MenuItem>; }
            set { HttpContext.Current.Session["UserMenu" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static List<Employee> SelfServiceEmployees
        {
            get { return HttpContext.Current.Session["SelfServiceEmployees" + HttpContext.Current.Session.SessionID.ToString()] as List<Employee>; }
            set { HttpContext.Current.Session["SelfServiceEmployees" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static List<CodeDescription> AvailableRoles
        {
            get { return HttpContext.Current.Session["AvailableRoles" + HttpContext.Current.Session.SessionID.ToString()] as List<CodeDescription>; }
            set { HttpContext.Current.Session["AvailableRoles" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool HasMultiRole
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["MultiRole" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["MultiRole" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static int DnetRoleRecordID
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["RoleRecID" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["RoleRecID" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }

        public static string LinkedIn
        {
            get { return HttpContext.Current.Session["LinkedIn" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LinkedIn" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string FaceBook
        {
            get { return HttpContext.Current.Session["FaceBook" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["FaceBook" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string Twitter
        {
            get { return HttpContext.Current.Session["Twitter" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Twitter" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool ShowTWCopyright
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["ShowTWCopyright" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["ShowTWCopyright" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string FooterPhone1
        {
            get { return HttpContext.Current.Session["FooterPhone1" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["FooterPhone1" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string FooterPhone2
        {
            get { return HttpContext.Current.Session["FooterPhone2" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["FooterPhone2" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string FooterFax
        {
            get { return HttpContext.Current.Session["FooterFax" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["FooterFax" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string FooterEmail
        {
            get { return HttpContext.Current.Session["FooterEmail" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["FooterEmail" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static string Website
        {
            get { return HttpContext.Current.Session["Website" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Website" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string CompanyLogo
        {
            get { return HttpContext.Current.Session["CompanyLogo" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["CompanyLogo" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string Browser
        {
            get { return HttpContext.Current.Session["Browser" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Browser" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool IsIE
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["IsIE" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["IsIE" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static int? TimeZone
        {
            get { return HttpContext.Current.Session["TimeZone" + HttpContext.Current.Session.SessionID.ToString()] as int?; }
            set { HttpContext.Current.Session["TimeZone" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static int? DaylightShift
        {
            get { return HttpContext.Current.Session["DaylightShift" + HttpContext.Current.Session.SessionID.ToString()] as int?; }
            set { HttpContext.Current.Session["DaylightShift" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        // TODO: Use either CurrentTimeSheet or CurrentTimeSheetId. These are both the same.
        public static int? CurrentTimeSheet
        {
            get { return HttpContext.Current.Session["CurrentTimeSheet" + HttpContext.Current.Session.SessionID.ToString()] as int?; }
            set { HttpContext.Current.Session["CurrentTimeSheet" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static int CurrentTimeSheetId
        {
            get { return (int)HttpContext.Current.Session["CurrentTimeSheet" + HttpContext.Current.Session.SessionID.ToString()]; }
            set { HttpContext.Current.Session["CurrentTimeSheet" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string HelpURL
        {
            get { return HttpContext.Current.Session["HelpURL" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["HelpURL" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool ShowW4
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["ShowW4" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["ShowW4" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string W4EE
        {
            get { return HttpContext.Current.Session["W4EE" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["W4EE" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string OwnerName
        {
            get { return HttpContext.Current.Session["OwnerName" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["OwnerName" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static int? DefaultAccess
        {
            get { return HttpContext.Current.Session["DefaultAccess" + HttpContext.Current.Session.SessionID.ToString()] as int?; }
            set { HttpContext.Current.Session["DefaultAccess" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool IsClientAdmin
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["IsClientAdmin" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["IsClientAdmin" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool ShowAllSysTS
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["ShowAllSysTS" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["ShowAllSysTS" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string RemoteServerName
        {
            get { return HttpContext.Current.Session["RemoteServerName" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["RemoteServerName" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string RemoteCheckPrinterUrl
        {
            get { return HttpContext.Current.Session["RemoteCheckPrinterUrl" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["RemoteCheckPrinterUrl" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool IsOBAuditMode
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["IsOBAuditMode" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["IsOBAuditMode" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool UseKERS
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["UseKERS" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["UseKERS" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool IgnoreChanges
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["IgnoreChanges" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["IgnoreChanges" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool AllowOB
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["AllowOB" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["AllowOB" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool AllowPayroll
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["AllowPayroll" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["AllowPayroll" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool AllowSwipeclock
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["AllowSwipeclock" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["AllowSwipeclock" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool AllowSingleSignOn
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["AllowSingleSignOn" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["AllowSingleSignOn" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool AllowTimeco
        {
            //get { return Convert.ToBoolean(HttpContext.Current.Session["AllowTimeco" + HttpContext.Current.Session.SessionID.ToString()]); }
            get { return true; }
            set { HttpContext.Current.Session["AllowTimeco" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool UseTimeco
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["UseTimeco" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["UseTimeco" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool UseDnetPrinter
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["UseDnetPrinter" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["UseDnetPrinter" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static string CodeVersion
        {
            get { return HttpContext.Current.Session["CodeVersion" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["CodeVersion" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static string DBVersion
        {
            get { return HttpContext.Current.Session["DBVersion" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["DBVersion" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static List<MenuItem> AllowedWidgets
        {
            get { return HttpContext.Current.Session["AllowedWidgets" + HttpContext.Current.Session.SessionID.ToString()] as List<MenuItem>; }
            set { HttpContext.Current.Session["AllowedWidgets" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool PlanFirstLoad
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["PlanFirstLoad" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["PlanFirstLoad" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool DependentFirstLoad
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["DependentFirstLoad" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["DependentFirstLoad" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool SummaryFirstLoad
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["SummaryFirstLoad" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["SummaryFirstLoad" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static bool OpenEnrollmentAvailable
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["OpenEnrollmentAvailable" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["OpenEnrollmentAvailable" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool[] LifeEventType
        {
            get { return (bool[])HttpContext.Current.Session["LifeEventType" + HttpContext.Current.Session.SessionID.ToString()]; }
            set { HttpContext.Current.Session["LifeEventType" + HttpContext.Current.Session.SessionID.ToString()] = (bool[])(value); }
        }

        public static bool NewUser
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["NewUser" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["NewUser" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string OEEmployeeID
        {
            get { return HttpContext.Current.Session["OEEmployeeID" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["OEEmployeeID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string OEClientID
        {
            get { return HttpContext.Current.Session["OEClientID" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["OEClientID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static string OEType
        {
            get { return HttpContext.Current.Session["OEType" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["OEType" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static int OETransactionID
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["OETransactionID" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["OETransactionID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        /*public static int MaxPayrollUsers
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["MaxPayrollUsers" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["MaxPayrollUsers" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }*/

        public static User CurrentUser
        {
            get { return HttpContext.Current.Session["CurrentUser" + HttpContext.Current.Session.SessionID.ToString()] as User; }
            set { HttpContext.Current.Session["CurrentUser" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool OEAllowSignDoc
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["OEAllowSignDoc" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["OEAllowSignDoc" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool PendingES
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["PendingES" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["PendingES" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static List<string> SecurityEmployees
        {
            get
            {
                var tmpList = HttpContext.Current.Session["SecurityEmployees" + HttpContext.Current.Session.SessionID.ToString()].ToString();
                return tmpList.Split(',').ToList();
            }
            set
            {
                HttpContext.Current.Session["SecurityEmployees" + HttpContext.Current.Session.SessionID.ToString()] = string.Join(",", value); ;
            }
        }

        public static List<string> SecurityDocTypes
        {
            get
            {
                var tmpList = HttpContext.Current.Session["SecurityDocTypes" + HttpContext.Current.Session.SessionID.ToString()].ToString();
                return tmpList.Split(',').ToList();
            }
            set
            {
                HttpContext.Current.Session["SecurityDocTypes" + HttpContext.Current.Session.SessionID.ToString()] = string.Join(",", value); ;
            }
        }

        public static bool UseD2
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["UseD2" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["UseD2" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static string PayrollProfile
        {
            get { return HttpContext.Current.Session["PayrollProfile" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["PayrollProfile" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string PayrollNumber
        {
            get { return HttpContext.Current.Session["PayrollNumber" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["PayrollNumber" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static int QLESelectedYear
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["QLESelectedYear" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["QLESelectedYear" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }
        #region DashboardVariables

        public static List<EmployeeTransactionHistory> DashboardEETransactionHistory
        {
            get
            {
                List<EmployeeTransactionHistory> tmpList = (List<EmployeeTransactionHistory>)HttpContext.Current.Session["DashboardEETransactionHistory" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["DashboardEETransactionHistory" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Invoice> DashboardInvoices
        {
            get
            {
                List<Invoice> tmpList = (List<Invoice>)HttpContext.Current.Session["DashboardInvoices" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["DashboardInvoices" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<EmployeePaycode> DashboardEEPaycodes
        {
            get
            {
                List<EmployeePaycode> tmpList = (List<EmployeePaycode>)HttpContext.Current.Session["DashboardEEPaycodes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["DashboardEEPaycodes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<InvoicePayroll> DashboardInvoicePayrolls
        {
            get
            {
                List<InvoicePayroll> tmpList = (List<InvoicePayroll>)HttpContext.Current.Session["DashboardInvoicePayrolls" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["DashboardInvoicePayrolls" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        #endregion

        #region TimeSheetVariables
        public static List<Client> Timesheet_AllClients
        {
            get
            {
                List<Client> tmpList = (List<Client>)HttpContext.Current.Session["Timesheet_AllClients" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllClients" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<ClientDivisionPayrollCode> Timesheet_ClientDivisionPayrollCodes
        {
            get
            {
                List<ClientDivisionPayrollCode> tmpList = (List<ClientDivisionPayrollCode>)HttpContext.Current.Session["Timesheet_ClientDivisionPayrollCodes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_ClientDivisionPayrollCodes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<ClientDivisionDetail> Timesheet_ClientDivisionDetails
        {
            get
            {
                List<ClientDivisionDetail> tmpList = (List<ClientDivisionDetail>)HttpContext.Current.Session["Timesheet_ClientDivisionDetails" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_ClientDivisionDetails" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }

        public static Data.TimeSheet Timesheet_TimesheetSetup
        {
            get
            {
                Data.TimeSheet tmpList = (Data.TimeSheet)HttpContext.Current.Session["Timesheet_AllTimesheets" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllTimesheets" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static ITimeSheetInfo CurrentTimeSheetInfo
        {
            get => HttpContext.Current.Session[$"CurrentTimeSheetInfo_{HttpContext.Current.Session.SessionID}"] as TimeSheetInfo;
            set { HttpContext.Current.Session[$"CurrentTimeSheetInfo_{HttpContext.Current.Session.SessionID}"] = value; }
        }

        public static List<Employee> Timesheet_AllEmployees
        {
            get
            {
                List<Employee> tmpList = (List<Employee>)HttpContext.Current.Session["Timesheet_AllEmployees" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEmployees" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }

        public static Dictionary<string, Employee> Timesheet_ActiveEmployeesDict
        {
            get
            {
                var tmpDict = (Dictionary<string, Employee>)HttpContext.Current.Session["Timesheet_ActiveEmployeesDict" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpDict;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_ActiveEmployeesDict" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }

        public static List<EmployeePaycode> Timesheet_AllEmployeePaycodes
        {
            get
            {
                List<EmployeePaycode> tmpList = (List<EmployeePaycode>)HttpContext.Current.Session["Timesheet_AllEmployeePaycodes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEmployeePaycodes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static Dictionary<string, List<EmployeePaycode>> Timesheet_AllEEPaycodesDict
        {
            get
            {
                var tmpDict = (Dictionary<string, List<EmployeePaycode>>)HttpContext.Current.Session["Timesheet_AllEEPaycodesDict" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpDict;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEEPaycodesDict" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<EmployeeLocalTax> Timesheet_AllEmployeeLocalTaxes
        {
            get
            {
                List<EmployeeLocalTax> tmpList = (List<EmployeeLocalTax>)HttpContext.Current.Session["Timesheet_AllEmployeeLocalTaxes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEmployeeLocalTaxes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<EmployeeStateTax> Timesheet_AllEmployeeStateTaxes
        {
            get
            {
                List<EmployeeStateTax> tmpList = (List<EmployeeStateTax>)HttpContext.Current.Session["Timesheet_AllEmployeeStateTaxes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEmployeeStateTaxes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<EmployeeTimePunch> Timesheet_AllEmployeeTimePunches
        {
            get
            {
                List<EmployeeTimePunch> tmpList = (List<EmployeeTimePunch>)HttpContext.Current.Session["Timesheet_AllEmployeeTimePunches" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEmployeeTimePunches" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<EmployeeBenefit> Timesheet_AllEmployeeBenefits
        {
            get
            {
                List<EmployeeBenefit> tmpList = (List<EmployeeBenefit>)HttpContext.Current.Session["Timesheet_AllEmployeeBenefits" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEmployeeBenefits" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<EmployeeDeduction> Timesheet_AllEmployeeDeductions
        {
            get
            {
                List<EmployeeDeduction> tmpList = (List<EmployeeDeduction>)HttpContext.Current.Session["Timesheet_AllEmployeeDeductions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllEmployeeDeductions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }

        public static List<Paycode> Timesheet_AllPayCodes
        {
            get
            {
                List<Paycode> tmpList = (List<Paycode>)HttpContext.Current.Session["Timesheet_AllPayCodes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllPayCodes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Benefit> Timesheet_AllBenefits
        {
            get
            {
                List<Benefit> tmpList = (List<Benefit>)HttpContext.Current.Session["Timesheet_AllBenefits" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllBenefits" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Deduction> Timesheet_AllDeductions
        {
            get
            {
                List<Deduction> tmpList = (List<Deduction>)HttpContext.Current.Session["Timesheet_AllDeductions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllDeductions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Position> Timesheet_AllPositions
        {
            get
            {
                List<Position> tmpList = (List<Position>)HttpContext.Current.Session["Timesheet_AllPositions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllPositions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Department> Timesheet_AllDepartments
        {
            get
            {
                List<Department> tmpList = (List<Department>)HttpContext.Current.Session["Timesheet_AllDepartments" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllDepartments" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<LocalTax> Timesheet_AllLocalTaxes
        {
            get
            {
                List<LocalTax> tmpList = (List<LocalTax>)HttpContext.Current.Session["Timesheet_AllLocalTaxes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllLocalTaxes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<SUTAState> Timesheet_AllStateTaxes
        {
            get
            {
                List<SUTAState> tmpList = (List<SUTAState>)HttpContext.Current.Session["Timesheet_AllStateTaxes" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllStateTaxes" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<WorkersCompCode> Timesheet_AllWorkersComps
        {
            get
            {
                List<WorkersCompCode> tmpList = (List<WorkersCompCode>)HttpContext.Current.Session["Timesheet_AllWorkersComps" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Timesheet_AllWorkersComps" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        #endregion
        public static List<EmployeeSrch> QuickSearch_Employees
        {
            get
            {
                List<EmployeeSrch> tmpList = (List<EmployeeSrch>)HttpContext.Current.Session["QuickSearch_Employees" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["QuickSearch_Employees" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Code_Description> Department_Descriptions
        {
            get
            {
                List<Code_Description> tmpList = (List<Code_Description>)HttpContext.Current.Session["Department_Descriptions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Department_Descriptions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Code_Description> Paycode_Descriptions
        {
            get
            {
                List<Code_Description> tmpList = (List<Code_Description>)HttpContext.Current.Session["Paycode_Descriptions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Paycode_Descriptions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Code_Description> Deduction_Descriptions
        {
            get
            {
                List<Code_Description> tmpList = (List<Code_Description>)HttpContext.Current.Session["Deduction_Descriptions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Deduction_Descriptions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Code_Description> Benefit_Descriptions
        {
            get
            {
                List<Code_Description> tmpList = (List<Code_Description>)HttpContext.Current.Session["Benefit_Descriptions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Benefit_Descriptions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Code_Description> Position_Descriptions
        {
            get
            {
                List<Code_Description> tmpList = (List<Code_Description>)HttpContext.Current.Session["Position_Descriptions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["Position_Descriptions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Code_Description> WC_Descriptions
        {
            get
            {
                List<Code_Description> tmpList = (List<Code_Description>)HttpContext.Current.Session["WC_Descriptions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["WC_Descriptions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }
        public static List<Code_Description> SUTAState_Descriptions
        {
            get
            {
                List<Code_Description> tmpList = (List<Code_Description>)HttpContext.Current.Session["SUTAState_Descriptions" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["SUTAState_Descriptions" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }

        public static ClientPayrollSchedule PayrollSchedule
        {
            get => HttpContext.Current.Session[$"PayrollSchedule_{HttpContext.Current.Session.SessionID}"] as ClientPayrollSchedule;
            set => HttpContext.Current.Session[$"PayrollSchedule_{HttpContext.Current.Session.SessionID}"] = value;
        }

        public static bool IsUseJBS
        {
            get
            {
                var value = HttpContext.Current.Session[$"IsUseJBS_{HttpContext.Current.Session.SessionID}"] as bool?;
                if (value.HasValue)
                    return value.Value;
                else
                    return true; // Returns 'true' by default (ref: ClientProvider.IsUseJBS)
            }
            set => HttpContext.Current.Session[$"IsUseJBS_{HttpContext.Current.Session.SessionID}"] = value;
        }

        public static ClientTimeSheetProfile ClientTimeSheetProfile
        {
            get => HttpContext.Current.Session[$"ClientTimeSheetProfile_{HttpContext.Current.Session.SessionID}"] as ClientTimeSheetProfile;
            set => HttpContext.Current.Session[$"ClientTimeSheetProfile_{HttpContext.Current.Session.SessionID}"] = value;
        }

        public static bool IsPEOApprovalRequired
        {
            get
            {
                var value = HttpContext.Current.Session[$"IsPEOApprovalRequired_{HttpContext.Current.Session.SessionID}"] as bool?;
                if (value.HasValue)
                    return value.Value;
                else
                    return false;
            }
            set => HttpContext.Current.Session[$"IsPEOApprovalRequired_{HttpContext.Current.Session.SessionID}"] = value;
        }

        public static void Set<TValue>(string key, TValue value) =>
            HttpContext.Current.Session[key] = value;
        public static TValue Get<TValue>(string key) =>
            (TValue)HttpContext.Current.Session[key];
        public static string CurrentSessionID() =>
            HttpContext.Current.Session.SessionID;

        public static List<CompanyDocumentAssignmentsMonitorVM> MonitorDocuments
        {
            get
            {
                List<CompanyDocumentAssignmentsMonitorVM> tmpList = (List<CompanyDocumentAssignmentsMonitorVM>)HttpContext.Current.Session["MonitorDocuments" + HttpContext.Current.Session.SessionID.ToString()];
                return tmpList;
            }
            set
            {
                HttpContext.Current.Session["MonitorDocuments" + HttpContext.Current.Session.SessionID.ToString()] = value; ;
            }
        }

        public static bool SignFromDashboard
        {
            get
            {
                var value = HttpContext.Current.Session["SignFromDashboard" + HttpContext.Current.Session.SessionID.ToString()] as bool?;
                if (value.HasValue)
                    return value.Value;
                else
                    return false;
            }
            set => HttpContext.Current.Session["SignFromDashboard" + HttpContext.Current.Session.SessionID.ToString()] = value;
        }

        public static string DestinationUrl
        {
            get => HttpContext.Current.Session["DestinationUrl"]?.ToString();
            set => HttpContext.Current.Session["DestinationUrl"] = value;
        }

        public static string NavPayrollNumber
        {
            get => HttpContext.Current.Session["NavPayrollNumber"]?.ToString();
            set => HttpContext.Current.Session["NavPayrollNumber"] = value;
        }

        public static bool CanPayroll
        {
            get
            {
                var value = HttpContext.Current.Session["CanPayroll" + HttpContext.Current.Session.SessionID.ToString()] as bool?;
                if (value.HasValue)
                    return value.Value;
                else
                    return false;
            }
            set => HttpContext.Current.Session["CanPayroll" + HttpContext.Current.Session.SessionID.ToString()] = value;
        }

        public static bool HasPayrollAccess => AllowPayroll && CanPayroll;
        public static bool UserCanPayroll
        {
            get
            {
                var value = HttpContext.Current.Session["UserCanPayroll" + HttpContext.Current.Session.SessionID.ToString()] as bool?;
                if (value.HasValue)
                    return value.Value;
                else
                    return false;
            }
            set => HttpContext.Current.Session["UserCanPayroll" + HttpContext.Current.Session.SessionID.ToString()] = value;
        }
        public static bool LoggedInAsFromClient
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["LoggedInAsFromClient" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["LoggedInAsFromClient" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static string LoggedInAsClientUser
        {
            get { return HttpContext.Current.Session["LoggedInAsClientUser" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LoggedInAsClientUser" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string LoggedInAsClientUserPWD
        {
            get { return HttpContext.Current.Session["LoggedInAsClientUserPWD" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LoggedInAsClientUserPWD" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
        public static bool IsReHire
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["IsReHire" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["IsReHire" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static DateTime? ReHireDate
        {
            get { return Convert.ToDateTime(HttpContext.Current.Session["ReHireDate" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["ReHireDate" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToDateTime(value); }
        }
        public static string ReHireEmployeeID
        {
            get { return HttpContext.Current.Session["ReHireEmployeeID" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["ReHireEmployeeID" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string ApplicationVersion
        {
            get => HttpContext.Current.Session["ApplicationVersion"]?.ToString();
            set => HttpContext.Current.Session["ApplicationVersion"] = value;
        }

        public static int SelectedYear
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["SelectedYear" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["SelectedYear" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }

        public static string eType
        {
            get { return HttpContext.Current.Session["eType" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["eType" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string CurretnEnYear
        {
            get { return HttpContext.Current.Session["CurretnEnYear" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["CurretnEnYear" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static List<String> EmployeeList
        {
            get { return HttpContext.Current.Session["EmployeeList" + HttpContext.Current.Session.SessionID.ToString()] as List<String>; }
            set { HttpContext.Current.Session["EmployeeList" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool selectedYearEmpEnStatus
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["selectedYearEmpEnStatus" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["selectedYearEmpEnStatus" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static bool PEOSecurityRole
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["PEOSecurityRole" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["PEOSecurityRole" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }
        public static string AdminEvent
        {
            get { return HttpContext.Current.Session["AdminEvent" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["AdminEvent" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static short? EnrollmentYear
        {
            get { return Convert.ToInt16(HttpContext.Current.Session["EnrollmentYear" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["EnrollmentYear" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt16(value); }
        }

        public static int EventType
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["EventType" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["EventType" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }

        public static string EnrollmentCategory
        {
            get { return HttpContext.Current.Session["EnrollmentCategory" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["EnrollmentCategory" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static bool Schedule
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["Schedule" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["Schedule" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static DateTime? ReHireOEEffectiveDate
        {
            get { return Convert.ToDateTime(HttpContext.Current.Session["ReHireOEEffectiveDate" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["ReHireOEEffectiveDate" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToDateTime(value); }
        }

        public static string LifeEvent
        {
            get { return HttpContext.Current.Session["LifeEvent" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LifeEvent" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string Section
        {
            get { return HttpContext.Current.Session["Section" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["Section" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static string EmailType
        {
            get { return HttpContext.Current.Session["EmailType" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["EmailType" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static int Assigned
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["Assigned" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["Assigned" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }

        public static string LifeEventStatus
        {
            get { return HttpContext.Current.Session["LifeEventStatus" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["LifeEventStatus" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }

        public static int SelectedDepId
        {
            get { return Convert.ToInt32(HttpContext.Current.Session["SelectedDepId" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["SelectedDepId" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToInt32(value); }
        }

        //QLE 09232022
        public static bool DependentDeletedFlag
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session["DependentDeleted" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["DependentDeleted" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToBoolean(value); }
        }

        public static DateTime? OEEffectiveDate
        {
            get { return Convert.ToDateTime(HttpContext.Current.Session["OBEffectiveDate" + HttpContext.Current.Session.SessionID.ToString()]); }
            set { HttpContext.Current.Session["OBEffectiveDate" + HttpContext.Current.Session.SessionID.ToString()] = Convert.ToDateTime(value); }
        }

        public static string QLEBeneficiaryConfimFlag
        {
            get { return HttpContext.Current.Session["QLEBeneficiaryConfimFlag" + HttpContext.Current.Session.SessionID.ToString()] as string; }
            set { HttpContext.Current.Session["QLEBeneficiaryConfimFlag" + HttpContext.Current.Session.SessionID.ToString()] = value; }
        }
    }
}