
GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[LoginUsersLockDetails](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[UserID] [varchar](max) NOT NULL,
	[LoginTime] [datetime] NULL,
	[LoginAttempts] [int] NOT NULL,
	[IsAccountLocked] [int] NOT NULL,
	[AccountLockExpirationDate] [datetime] NULL,
	[WinLoginName] [varchar](50) NULL,
	[Browser] [varchar](max) NULL,
	[UsedOnMobile] [bit] NULL,
	[SystemIP] [varchar](50) NULL,
	[CreatedBy] [varchar](max) NULL,
	[CreatedDate] [varchar](max) NULL,
	[LastModifiedBy] [varchar](max) NULL,
	[LastModifiedDate] [varchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO


