

/****** Object:  StoredProcedure [dbo].[sp_GetCodeSetupValues]    Script Date: 3/4/2025 10:18:34 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_GetCodeSetupValues] --'000','','S'
(
    @ClientId nvarchar(6),
	@Location varchar(100)='',
	@Operation varchar(2)=''
)
AS
BEGIN
DECLARE @fvalue VARCHAR(MAX)=''
DECLARE @setUpId int,
		@TaskId int,
		@Company int

IF(@Operation='S')
		BEGIN
		-- declare variables used in cursor
DECLARE @curFValueOptions VARCHAR(max);
DECLARE @curSetupID int;
DECLARE @curClientid VARCHAR(128);
DECLARE @FValueOptionsNew VARCHAR(max)='';
 
-- declare cursor
DECLARE cursor_locations CURSOR FOR
 select distinct Clientid,oct.SetupID,FValueOptions  FROM OBClientSetup ocs 
  INNER JOIN OBClientSetupTaskFields oct on ocs.SetupID=oct.SetupID
  WHERE Clientid=@ClientId
			and FName='UDF1'  and FValueOptions!=''
 
-- open cursor
OPEN cursor_locations;
 
-- loop through a cursor
FETCH NEXT FROM cursor_locations INTO @curClientid, @curSetupID, @curFValueOptions;
WHILE @@FETCH_STATUS = 0
    BEGIN
	set @fvalue=@fvalue+','+@curFValueOptions
    FETCH NEXT FROM cursor_locations INTO @curClientid, @curSetupID, @curFValueOptions;
    END;
 
-- close and deallocate cursor
CLOSE cursor_locations;
DEALLOCATE cursor_locations;

			--SELECT @fvalue=FValueOptions from OBClientSetup ocs
			--INNER JOIN OBClientSetupTaskFields oct on ocs.SetupID=oct.SetupID
			--WHERE Clientid=@ClientId and [Default]=1
			--and FName='UDF1' and FValueOptions!=''

			SELECT DISTINCT udf1 LocValues FROM Employees WHERE Clientid=@ClientId
			UNION
			SELECT DISTINCT val LocValues FROM dbo.[fnSplit](@fvalue,',') OPTION (MAXRECURSION 0)

		END
		ELSE IF(@Operation='A') -- Add location
				BEGIN

				SELECT TOP 1 @fvalue=FValueOptions,@setUpId=ocs.SetupID,@TaskId=TaskID,@Company=ocs.CompanyID from OBClientSetup ocs
				INNER JOIN OBClientSetupTaskFields oct on ocs.SetupID=oct.SetupID
				WHERE Clientid=@ClientId and [Default]=1
				AND FName='UDF1' 

					IF(@fvalue!='' and @fvalue is not null)

					BEGIN
					SET @fvalue=@fvalue+','+@Location+','+@Location
					--Update OBClientSetupTaskFields set FValueOptions=@fvalue where SetupID=@setUpId and TaskID=@TaskId and FName='UDF1' and CompanyID=@Company

					END
					ELSE IF(@fvalue='' and @fvalue is null)

					BEGIN
					SET @fvalue=',,,'+@Location+','+@Location
					--Update OBClientSetupTaskFields set FValueOptions=@fvalue where SetupID=@setUpId and TaskID=@TaskId and FName='UDF1' and CompanyID=@Company

					END
					UPDATE oct
						SET oct.FValueOptions = @fvalue
						FROM OBClientSetup ocs 
						INNER JOIN OBClientSetupTaskFields oct on ocs.SetupID=oct.SetupID
						WHERE Clientid=@ClientId
						and FName='UDF1'

					Select 'Insert'
				END

		ELSE IF(@Operation='D')
		BEGIN
		--Remove location
			IF(SELECT count(1) from Employees where Clientid=@ClientId and UDF1=@Location)=0
			Begin
				SELECT TOP 1 @fvalue=FValueOptions,@setUpId=ocs.SetupID,@TaskId=TaskID,@Company=ocs.CompanyID from OBClientSetup ocs
				INNER JOIN OBClientSetupTaskFields oct on ocs.SetupID=oct.SetupID
				WHERE Clientid=@ClientId and [Default]=1
				AND FName='UDF1' and FValueOptions!=''
				SET @fvalue=REPLACE(@fvalue,','+@Location,'')
				--Update OBClientSetupTaskFields set FValueOptions=@fvalue where SetupID=@setUpId and TaskID=@TaskId and FName='UDF1' and CompanyID=@Company

				UPDATE oct
						SET oct.FValueOptions = @fvalue
						FROM OBClientSetup ocs 
						INNER JOIN OBClientSetupTaskFields oct on ocs.SetupID=oct.SetupID
						WHERE Clientid=@ClientId
						and FName='UDF1'

				Select 'Delete'
			END
			ELSE

			BEGIN
			Select 'Exist'
			END

END


END
GO


