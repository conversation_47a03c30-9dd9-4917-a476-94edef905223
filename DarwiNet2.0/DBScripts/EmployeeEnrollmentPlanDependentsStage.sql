
CREATE TABLE [dbo].[EmployeeEnrollmentPlanDependentsStage](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[CompanyID] [int] NOT NULL,
	[EmployeeID] [nvarchar](50) NOT NULL,
	[PlanName] [nvarchar](50) NOT NULL,
	[YEAR] [smallint] NOT NULL,
	[DependentSSN] [nvarchar](15) NOT NULL,
	[Covered] [bit] NOT NULL,
	[PlanAmount] [decimal](19, 5) NOT NULL,
	[PlanAgencyAmount] [decimal](19, 5) NOT NULL,
	[PlanMonthlyCost] [decimal](19, 5) NOT NULL,
	[DependentName] [nvarchar](40) NULL,
	[Age] [int] NOT NULL,
	[EventType] [int] NULL,
	[EventStatus] [varchar](20) NULL,
	[IsDeleted] [bit] NULL,
	[EffectiveStartDate] [datetime] NULL,
	[EffectiveEndDate] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[EmployeeEnrollmentPlanDependentsStage] ADD  CONSTRAINT [DF_EmployeeEnrollmentPlanDependentsStage_Covered]  DEFAULT ((1)) FOR [Covered]
GO

ALTER TABLE [dbo].[EmployeeEnrollmentPlanDependentsStage] ADD  CONSTRAINT [DF_EmployeeEnrollmentPlanDependentsStage_PlanAmount]  DEFAULT ((0.00)) FOR [PlanAmount]
GO

ALTER TABLE [dbo].[EmployeeEnrollmentPlanDependentsStage] ADD  CONSTRAINT [DF_EmployeeEnrollmentPlanDependentsStage_PlanAgencyAmount]  DEFAULT ((0.00)) FOR [PlanAgencyAmount]
GO

ALTER TABLE [dbo].[EmployeeEnrollmentPlanDependentsStage] ADD  CONSTRAINT [DF_EmployeeEnrollmentPlanDependentsStage_PlanMonthlyCost]  DEFAULT ((0.00)) FOR [PlanMonthlyCost]
GO

ALTER TABLE [dbo].[EmployeeEnrollmentPlanDependentsStage] ADD  CONSTRAINT [DF_EmployeeEnrollmentPlanDependentsStage_Age]  DEFAULT ((0)) FOR [Age]
GO