using DarwiNet2._0.Data;
using System.Collections.Generic;
using System.Linq;

namespace Thinkware.Pay360
{
    /// <summary>
    /// Provides user role related functions.
    /// </summary>
    public static class UserRoleClientEmployeeAssignmentExtensions
    {
        public static bool CanUserViewRates(this UserRoleClientEmployeeAssignment userRole) =>
            userRole != null ? userRole.AllowTimeSheetRates : false;
    }
}