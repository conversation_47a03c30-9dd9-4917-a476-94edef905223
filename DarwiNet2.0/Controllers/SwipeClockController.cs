using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.SwipeClock;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers
{
    [IsSessionActive]
    public class SwipeClockController : Controller
    {
        private readonly ISwipeClockService _swipeClockService;

        public SwipeClockController(ISwipeClockService swipeClockService)
        {
            _swipeClockService = swipeClockService;
        }

        public ActionResult Index()
        {
            var isAllowed = GlobalVariables.AllowSwipeclock;
            if (!isAllowed)
            {
                TempData["Error"] = "SwipeClock is not enabled.";
                return View();
            }

            var companyId = GlobalVariables.CompanyID;
            var clientId = GlobalVariables.Client;
            var employeeId = GlobalVariables.EmployeeID;
            var dnetRoleRecordId = GlobalVariables.DnetRoleRecordID;
            var dnetAccessLevel = GlobalVariables.DNETLevel;

            var credentials = _swipeClockService.GetUserCredentials(companyId, clientId, employeeId, dnetRoleRecordId, dnetAccessLevel, isAllowed, out string errorMessage);
            if (credentials == null)
            {
                TempData["Error"] = errorMessage;
                return View();
            }

            return View(new SwipeClockViewModel()
            {
                Login = credentials.Login,
                Password = credentials.Password,
                Url = credentials.Url
            });
        }
    }
}