using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Filters;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Logging;
using DarwiNet2._0.ViewModels.D2;
using Izenda.AdHoc.Webservices;
using Microsoft.Ajax.Utilities;
using System;
using System.Web.Mvc;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Controllers.D2
{
    [IsSessionActive]
    [SystemAndClientLevelFilter]
    public class PayrollPreviewController : PayrollControllerBase
    {
        private IPayrollWorkHeaderService _payrollWorkHeadersService;
        private IPayrollWorkBenefitService _payrollWorkBenefitService;
        private IPayrollWorkDeductionService _payrollWorkDeductionService;
        private IPayrollWorkPayCodeService _payrollWorkPayCodeService;
        private IPayrollWorkStateTaxService _payrollWorkStateTaxService;
        private IPayrollWorkLocalTaxService _payrollWorkLocalTaxService;
        private IPayrollReviewHeaderService _payrollReviewHeaderService;
        private IPayrollWorkErrorsService _errorsService;
        private IPayrollProfileSettingService _payrollProfileSettingService;

        public PayrollPreviewController(
            DnetEntities dbContext,
            IPayrollWorkHeaderService payrollWorkHeadersService,
            IPayrollWorkBenefitService payrollWorkBenefitService,
            IPayrollWorkDeductionService payrollWorkDeductionService,
            IPayrollWorkPayCodeService payrollWorkPayCodeService,
            IPayrollWorkStateTaxService payrollWorkStateTaxService,
            IPayrollWorkLocalTaxService payrollWorkLocalTaxService,
            IPayrollProfileSettingService payrollProfileSettingService,
            IPayrollReviewHeaderService payrollReviewHeaderService,
            IPayrollWorkErrorsService errorsService,
            IPayrollTeamsService payrollTeamsService,
            IPayrollProfileSettingService paymentProfileSettingService,
            ILogger logger)
            : base(dbContext, payrollProfileSettingService, payrollTeamsService, logger)
        {
            _payrollWorkHeadersService = payrollWorkHeadersService;
            _payrollWorkBenefitService = payrollWorkBenefitService;
            _payrollWorkDeductionService = payrollWorkDeductionService;
            _payrollWorkPayCodeService = payrollWorkPayCodeService;
            _payrollWorkStateTaxService = payrollWorkStateTaxService;
            _payrollWorkLocalTaxService = payrollWorkLocalTaxService;
            _payrollReviewHeaderService = payrollReviewHeaderService;
            _errorsService = errorsService;
            _payrollProfileSettingService = payrollProfileSettingService;
        }

        public ActionResult Index(string payrollNumber, bool fromPayrollDashboard = false)
        {
            try
            {
                PayrollPreviewViewModel vm = PreviewHelper(payrollNumber);
                vm.FromPayrollDashboard= fromPayrollDashboard;

                return View(vm);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult Employees(string payrollNumber, TableFilter filters)
        {
            try
            {
                var companyId = GlobalVariables.CompanyID;
                var result = _payrollWorkHeadersService.GetPayrollPreviewEmployees(companyId, payrollNumber, filters);
                return JsonSuccess(data: result);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpGet]
        public JsonResult GetEEOutsidePayrollTableData (string payrollNumber)
        {
            try
            {
                var result = _errorsService.GetListEEOutsideProfileFromErrors(payrollNumber);
                return JsonSuccess(data: new TableFilterData<EEOutsideProfileDTO>
                {
                    Data = result,
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        private PayrollPreviewViewModel PreviewHelper(string payrollNumber)
        {
            PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
            var vm = new PayrollPreviewViewModel();

            vm.Header = _payrollReviewHeaderService.SetupPayrollHeader(payrollNumber);
            vm.PayrollInfo = _payrollReviewHeaderService.SetupPayrollInfo(payrollNumber);
            vm.WagesTotals = _payrollWorkHeadersService.GetWagesTotals(payrollNumber).WagesTotals;
            vm.PayCodes = _payrollWorkPayCodeService.GetPayrollPreviewPayCodes(payrollNumber);
            vm.Benefits = _payrollWorkBenefitService.GetPayrollPreviewBenefitCodes(payrollNumber);
            vm.Deductions = _payrollWorkDeductionService.GetPayrollPreviewDeductionCodes(payrollNumber);
            vm.StateTaxes = _payrollWorkStateTaxService.GetPayrollPreviewStateTaxes(payrollNumber);
            vm.LocalTaxes = _payrollWorkLocalTaxService.GetPayrollPreviewLocalTaxes(payrollNumber);
            vm.Errors = _errorsService.ListErrors(payrollNumber);
            var processOptions = _payrollProfileSettingService.GetProcessOptions(oPayrollNumber, true);
            if (processOptions != null) vm.StopOnWarnings = processOptions.StopOnWarnings;

            return vm;
        }
    }
}