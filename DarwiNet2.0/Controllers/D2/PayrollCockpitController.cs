using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Filters;
using DarwiNet2._0.Interfaces.Models;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Logging;
using DarwiNet2._0.Models.API;
using DarwiNet2._0.Services.D2;
using DarwiNet2._0.ViewModels.D2;
using DataDrivenViewEngine.Models.Core;
using EO.Pdf.Internal;
using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Mvc;
using Thinkware.Cohesion.Payroll;
using Thinkware.Pay360.Payroll;
using Thinkware.Pay360.SignalR;

namespace DarwiNet2._0.Controllers.D2
{
    [IsSessionActive]
    //[PageAccess(MenuItemIds.SystemPayrollCockpit)]
    public class PayrollCockpitController : BackOfficeController
    {
        private readonly IAppConfig _appConfig;
        private readonly IClientService _clientService;
        private readonly ICompanyService _companyService;
        private readonly IPayrollProfileSettingService _payrollProfileSettingsService;
        private readonly IPayrollScheduleService _payrollScheduleService;
        private readonly ITimeSheetService _timeSheetService;
        private readonly IUserService _userService;
        private readonly IUserTableFilterService _userTableFilterService;
        private readonly IPayrollWorkHeaderService _payrollWorkHeaderService;
        private readonly IPayrollCockpitPayTypeVerificationService _payrollCockpitPayTypeVerificationService;
        private readonly IClientPayrollScheduleService _clientPayrollScheduleService;
        private readonly IPayrollWorkMasterProvider _payrollWorkMasterProvider;
        private readonly IPayrollProfileNoteService _payrollProfileNoteService;
        private readonly IFilterBuilder<ClientPayrollSchedule> _filterBuilder;
        private readonly ISortBuilder<ClientPayrollSchedule> _sortBuilder;
        private readonly IFilterBuilder<PayrollCockpitTableRow> _filterBuilderPayrollCockpitTR;
        private readonly ISortBuilder<PayrollCockpitTableRow> _sortBuilderPayrollCockpitTR;
        private readonly IInvoiceMergeService _invoiceMergeService;
        private readonly IInvoicePayrollService _invoicePayrollService;
        private readonly IEditPayrollCalculationService _editCalcService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPayrollService _payrollService;
        private readonly IPayrollApprovalService _payrollApprovalService;
        private readonly IInvoiceMergeReversionService _invoiceMergeReversionService;
        private readonly IEmployeeCheckHistoryService _employeeCheckHistoryService;

        public PayrollCockpitController(
            IAppConfig appConfig,
            IClientService clientService,
            ICompanyService companyService,
            IPayrollProfileSettingService payrollProfileSettingsService,
            IPayrollScheduleService payrollScheduleService,
            ITimeSheetService timeSheetService,
            IUserService userService,
            IUserTableFilterService userTableFilterService,
            IPayrollWorkHeaderService payrollWorkHeaderService,
            IPayrollCockpitPayTypeVerificationService payrollCockpitPayTypeVerificationService,
            IClientPayrollScheduleService clientPayrollScheduleService,
            IPayrollWorkMasterProvider payrollWorkMasterProvider,
            IPayrollProfileNoteService payrollProfileNoteService,
            IFilterBuilder<ClientPayrollSchedule> filterBuilder,
            ISortBuilder<ClientPayrollSchedule> sortBuilder,
            IFilterBuilder<PayrollCockpitTableRow> filterBuilderPayrollCockpitTR,
            ISortBuilder<PayrollCockpitTableRow> sortBuilderPayrollCockpitTR,
            IInvoiceMergeService invoiceMergeService,
            IInvoicePayrollService invoicePayrollService,
            IEditPayrollCalculationService editCalcService,
            IInvoiceService invoiceService,
            IPayrollService payrollService,
            IPayrollApprovalService payrollApprovalService,
            IInvoiceMergeReversionService invoiceMergeReversionService,
            IEmployeeCheckHistoryService employeeCheckHistoryService,
            DnetEntities dbContext,
            ILogger logger)
            : base(dbContext, logger)
        {
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _companyService = companyService ?? throw new ArgumentNullException(nameof(companyService));
            _payrollProfileSettingsService = payrollProfileSettingsService ?? throw new ArgumentNullException(nameof(payrollProfileSettingsService));
            _payrollScheduleService = payrollScheduleService ?? throw new ArgumentNullException(nameof(payrollScheduleService));
            _timeSheetService = timeSheetService ?? throw new ArgumentNullException(nameof(timeSheetService));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _appConfig = appConfig ?? throw new ArgumentNullException(nameof(appConfig));
            _userTableFilterService = userTableFilterService ?? throw new ArgumentNullException(nameof(userTableFilterService));
            _payrollWorkHeaderService = payrollWorkHeaderService ?? throw new ArgumentNullException(nameof(payrollWorkHeaderService));
            _payrollCockpitPayTypeVerificationService = payrollCockpitPayTypeVerificationService ?? throw new ArgumentNullException(nameof(payrollCockpitPayTypeVerificationService));
            _clientPayrollScheduleService = clientPayrollScheduleService ?? throw new ArgumentNullException(nameof(clientPayrollScheduleService));
            _payrollWorkMasterProvider = payrollWorkMasterProvider ?? throw new ArgumentNullException(nameof(payrollWorkMasterProvider));
            _payrollProfileNoteService = payrollProfileNoteService ?? throw new ArgumentNullException(nameof(payrollProfileNoteService));
            _filterBuilder = filterBuilder ?? throw new ArgumentNullException(nameof(filterBuilder));
            _sortBuilder = sortBuilder ?? throw new ArgumentNullException(nameof(filterBuilder));
            _filterBuilderPayrollCockpitTR = filterBuilderPayrollCockpitTR ?? throw new ArgumentNullException(nameof(filterBuilderPayrollCockpitTR));
            _sortBuilderPayrollCockpitTR = sortBuilderPayrollCockpitTR ?? throw new ArgumentNullException(nameof(filterBuilderPayrollCockpitTR));
            _invoiceMergeService = invoiceMergeService ?? throw new ArgumentNullException(nameof(invoiceMergeService));
            _invoicePayrollService = invoicePayrollService ?? throw new ArgumentNullException(nameof(invoicePayrollService));
            _editCalcService = editCalcService ?? throw new ArgumentNullException(nameof(editCalcService));
            _invoiceService = invoiceService ?? throw new ArgumentNullException(nameof(invoiceService));
            _payrollService = payrollService ?? throw new ArgumentNullException(nameof(payrollService));
            _payrollApprovalService = payrollApprovalService ?? throw new ArgumentNullException(nameof(payrollApprovalService));
            _invoiceMergeReversionService = invoiceMergeReversionService ?? throw new ArgumentNullException(nameof(invoiceMergeReversionService));
            _employeeCheckHistoryService = employeeCheckHistoryService ?? throw new ArgumentNullException(nameof(employeeCheckHistoryService));
        }

        public ActionResult Index()
        {
            try
            {
                string userId = GlobalVariables.DNETOwnerID;
                PayrollCockpitViewModel viewModel = new PayrollCockpitViewModel()
                {
                    EmployeeName = _userService.GetNameByUserId(userId),
                    SavedFilters = _userTableFilterService.GetAllFiltersByUserAndTable(userId, FilterTableConstants.PayrollCockpit),
                    AvailableCompanies = _companyService.GetCompaniesAvailableToUser(userId)
                };

                viewModel.IsClient = false;

                int menuAccessLevel;
                if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
                {
                    viewModel.IsClient = true;
                    menuAccessLevel = MenuAccess.PageSecurity(MenuItemIds.ClientPayrollCockpit);
                }
                else
                    menuAccessLevel = MenuAccess.PageSecurity(MenuItemIds.SystemPayrollCockpit);

                viewModel.CompanyID = GlobalVariables.CompanyID;
                viewModel.ClientID = GlobalVariables.Client;
                viewModel.UserId = GlobalVariables.DNETOwnerID;
                if (menuAccessLevel != MenuAccessLevel.NoAccess)
                    return View(viewModel);
                else
                    return RedirectToAction("Index", "NoAccess");
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult UpdatePayrollDates(int companyId, string payrollNumber, string checkDate, string debitDate, string ppBeginDate, string ppEndDate, string processDate)
        {
            try
            {
                _payrollScheduleService.UpdatePayrollScheduleDates(companyId, payrollNumber, checkDate, debitDate, ppBeginDate, ppEndDate, processDate);

                return JsonSuccess("Success");
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public JsonResult UpdateOffCyclePayroll(int companyId, string payrollNumber, bool isOffCyclePayroll)
        {
            try
            {
                _payrollScheduleService.UpdateOffCyclePayroll(companyId, payrollNumber, isOffCyclePayroll);

                string message = isOffCyclePayroll ?
                    "This payroll is now an Off Cycle Payroll" :
                    "This payroll is no longer an Off Cycle Payroll";

                return JsonSuccess(message);
            }
            catch (Exception exception)
            {
                return JsonInternalServerError(exception);
            }
        }

        public JsonResult UpdatePriorityPayroll(int companyId, string payrollNumber, bool priority)
        {
            try
            {
                _payrollScheduleService.UpdatePriorityPayroll(companyId, payrollNumber, priority);

                string message = priority ?
                    "This payroll is 'Priority Payroll' now" :
                    "This payroll is no longer Priority Payroll";

                return JsonSuccess(message);
            }
            catch (Exception exception)
            {
                return JsonInternalServerError(exception);
            }
        }

        public JsonResult GetClientsForCompany(int companyID)
        {
            try
            {
                List<AddPayrollNameAndNumber> clientList = _clientService.GetClientsByCompanyId(companyID, GlobalVariables.CurrentUser.UserID).Select(x => new AddPayrollNameAndNumber()
                {
                    RecordName = x.ClientName,
                    RecordNumber = x.ClientID
                }).ToList();

                if (GlobalVariables.CurrentUser.ClientLevelEnabled)
                {
                    clientList = clientList.Where(x => x.RecordNumber == GlobalVariables.Client).ToList();
                }

                return Json(clientList, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        private TableQueryInfo<ClientPayrollSchedule> GetFilterList(TableFilter filters, string searchText, string UserID, int companyId)
        {
            var excludeFinalized = !filters.Filters.Where(f => f.FType == DTOs.Filter.FilterType.PayrollStatus)
                                                   .FirstOrDefault().ActiveFilters
                                                   .Where(x => (Int16)x.Code == (Int16)PayrollStatus.Finalized)
                                                   .ToList().Any();

            filters.Filters.Add(new DTOs.Filter
            {
                Type = "PayrollNumber",
                Code = "PayrollNumber",
                Searchable = true
            });

            var filterClause = string.IsNullOrEmpty(searchText) ?
                _filterBuilder.BuildFilterClause(filters.Filters) :
                _filterBuilder.BuildSearchClause(filters.Filters, searchText);

            return _payrollScheduleService.GetSchedules(filterClause, excludeFinalized, UserID, companyId);
        }

        [HttpPost]
        public ActionResult GetFilteredList(TableFilter filters, string searchText = "")
        {
            try
            {
                var UserID = GlobalVariables.DNETOwnerID;
                var companyId = GlobalVariables.CompanyID;
                var tableInfo = GetFilterList(filters, searchText, UserID, companyId);
                tableInfo.Query = GetSortedList(tableInfo.Query, filters);
                tableInfo.Query = tableInfo.Query.Skip(filters.EntriesToSkip).Take(filters.PerPage);
                var data = MapTableRows(tableInfo.Query.ToList());
                return JsonSuccess(data: new TableFilterData<PayrollCockpitTableRow>
                {
                    Data = data.ToList(),
                    FilteredEntries = tableInfo.FilteredEntries,
                    TotalEntries = tableInfo.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult GetPayrollCockpitTableData(TableFilter filters)
        {
            try
            {
                var userId = GlobalVariables.DNETOwnerID;

                _userTableFilterService.SaveUserTableFilter(userId, FilterTableConstants.PayrollCockpit, false, filters, 0, "");

                var tableInfo = _clientPayrollScheduleService.GetPayrollCockpitTableFilterData(filters, userId);
                tableInfo.Query = GetSortedListPayrollCockpitTR(tableInfo.Query, filters);
                tableInfo.Query = tableInfo.Query.Skip(filters.EntriesToSkip).Take(filters.PerPage);
                var data = MapTableRowsPayrollcockpitTR(tableInfo.Query.ToList());
                SetInvoicePayrollStatus(data);
                return JsonSuccess(data: new TableFilterData<PayrollCockpitTableRow>
                {
                    Data = data.ToList(),
                    FilteredEntries = tableInfo.FilteredEntries,
                    TotalEntries = tableInfo.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        private void SetInvoicePayrollStatus(IEnumerable<PayrollCockpitTableRow> data)
        {
            var minStatus = (int)PayrollStatus.InvoiceProcessing;
            var maxStatus = (int)PayrollStatus.InvoiceCompleted;
            var payrollNumbers = data.Where(x => x.Schedule_Status_Code >= minStatus && x.Schedule_Status_Code <= maxStatus).Select(x => x.PayrollNumber);
            var invoicePayrolls = _dbContext.InvoicePayrolls.Where(x => payrollNumbers.Contains(x.PayrollNumber)).ToList();
            foreach (var row in data)
            {
                var lowestStatusValue = invoicePayrolls.Where(x => x.PayrollNumber == row.PayrollNumber).OrderBy(x => x.Status).Select(x=>x.Status).FirstOrDefault();
                if (lowestStatusValue == null)
                    continue;
                row.Schedule_Status = ((PayrollStatus)lowestStatusValue).GetDisplayName();
                row.Schedule_Status_Code = lowestStatusValue;
            }
        }

        public JsonResult GetProfilesForClient(int companyID, string clientID)
        {
            try
            {
                List<AddPayrollNameAndNumber> profileList = _payrollProfileSettingsService.GetPayrollProfileSettingsList(companyID, clientID)
                    .Where(x => !x.ManualCheckProfile)
                    .Select(x => new AddPayrollNameAndNumber()
                {
                    RecordName = x.ProfileID,
                    RecordNumber = x.ProfileID
                }).ToList();

                return Json(profileList, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public JsonResult GetTimesheetsForProfile(int companyID, string clientID, string profileID)
        {
            try
            {
                List<AddPayrollNameAndNumber> tsProfileList = _dbContext.ClientTimeSheetProfiles.Where(x => x.CompanyID == companyID && x.ClientID == clientID && x.PayrollProfileID == profileID).ToList().Select(x => new AddPayrollNameAndNumber()
                {
                    RecordName = x.ProfileName,
                    RecordNumber = x.ProfileID
                }).ToList();

                return Json(tsProfileList, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public JsonResult CreateClientPayrollSchedule(int companyID, string clientID, string profileID, string beginDate, string endDate, string checkDate, string description, bool offCyclePayroll, string comments, bool runPayrollWithoutBatch, bool assignBenefits, bool assignDeductions)
        {
            try
            {
                var userId = GlobalVariables.DNETOwnerID;
                var dtCheckDate = DateTime.Parse(checkDate);
                var dtPPBegin = DateTime.Parse(beginDate);
                var dtPPEndDate = DateTime.Parse(endDate);
                var schedule = new ClientPayrollSchedule()
                {
                    CompanyID = companyID,
                    ClientID = clientID,
                    ProfileID = profileID,
                    Schedule_ID = profileID,
                    CheckDate = dtCheckDate,
                    DebitDate = dtCheckDate,
                    Process_Date = DateTime.Now.Date,
                    Responsible = userId,
                    PayPeriod_BeginDate = dtPPBegin,
                    PayPeriod_EndDate = dtPPEndDate,
                    InvoiceProcessAssigned = userId,
                    FinalizeAssigned = userId,
                    Description = description,
                    Comment = comments,
                    OffCyclePayroll = offCyclePayroll,
                    Manual = true,
                    PayrollType = (int)PayrollTypes.RegularPayroll
                };
                var result = _payrollScheduleService.CreateSchedule(schedule);
                string message = "";
                string redirect = "";
                if (result)
                {
                    if (!runPayrollWithoutBatch)
                    {
                        message = "Successfully created payroll.";
                        redirect = Url.Action("Index", "PayrollTime") + "?payrollNumber=" + schedule.PayrollNumber;
                    }
                    else
                    {
                        _payrollScheduleService.UpdatePayrollStatus(schedule.PayrollNumber, PayrollStatus.PayrollCalculated);
                        _payrollWorkMasterProvider.CreateNewPayrollWorkMaster(schedule.PayrollNumber);
                        _payrollWorkMasterProvider.UpdateIncludesBenefitsAndDeductions(schedule.PayrollNumber, assignBenefits, assignDeductions);
                        message = "Successfully created payroll. Redirecting to Payroll Review...";
                        redirect = Url.Action("Review", "PayrollReview") + "?payrollNumber=" + schedule.PayrollNumber;
                    }
                    return JsonSuccess(message, data: new { schedule.PayrollNumber, Redirect = redirect });
                }
                else
                {
                    return JsonError("Unable to create payroll.");
                }
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult DeletePayroll(string payrollNumber)
        {
            try
            {
                var schedule = _clientPayrollScheduleService.GetSchedule(payrollNumber);

                if (schedule == null) return JsonError("Could not find payroll schedule.", data: payrollNumber);

                List<short> deleteStatuses = new List<short>
                {
                    (short)PayrollStatus.Pending,
                    (short)PayrollStatus.Available,
                    (short)PayrollStatus.VoidCalculated,
                    (short)PayrollStatus.VoidCompleted,
                };

                List<byte> manualCheckTypes = new List<byte>
                {
                    (byte)ManualCheckTypes.ManualCheck,
                    (byte)ManualCheckTypes.Adjustment,
                };

                if (!deleteStatuses.Contains((short)schedule.Schedule_Status) && !(manualCheckTypes.Contains((byte)schedule.ManualCheckType) && schedule.Schedule_Status == (short)PayrollStatus.PayrollCalculated))
                    return JsonError("Unable to delete payroll in this status.", data: payrollNumber);

                bool hasPayrollAccess = _dbContext.UserRoleClientAccesses.Where(x => x.CompanyID == schedule.CompanyID &&
                                                   x.ClientID == schedule.ClientID &&
                                                   x.UserID == GlobalVariables.DNETOwnerID)
                                             .Any();
                if (!hasPayrollAccess) return JsonError("You do not have permission to delete this payroll.", data: payrollNumber);

                var status = PayrollStatusDefinition.Parse(PayrollStatus.Deleting);

                GlobalHost.ConnectionManager.GetHubContext<PayrollCockpitHub>()
                .Clients.All.updatePayrollStatus(payrollNumber, status.StatusText, status.StatusCode, GlobalVariables.DnetRoleID);
                GlobalHost.ConnectionManager.GetHubContext<EditPayrollCalculationHub>()
                    .Clients.All.updatePayrollStatus(payrollNumber, status.StatusText, status.StatusCode, GlobalVariables.DnetRoleID);

                _employeeCheckHistoryService.ResetVoidChecksForVoidPayrollDeletion(schedule);

                new ResetPayrollHandler(_dbContext, _editCalcService, _invoiceService, _payrollService, _payrollApprovalService, _invoiceMergeReversionService)
                    .Handle(new ResetPayrollRequest(payrollNumber, true));

                _payrollScheduleService.DeleteScheduleByPayrollNumber(schedule);

                PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                var company = oPayrollNumber.CompanyId;
                var clientId = oPayrollNumber.ClientId;
                var profileId = oPayrollNumber.ProfileId;

                var scheduleId = schedule.Schedule_ID;

                _payrollScheduleService.SetAvailablePayrollIfNeeded(company, clientId, profileId, scheduleId);

                return JsonSuccess("Successfully deleted payroll.");
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpGet]
        public ActionResult TimeEntry()
        {
            try
            {
                var vm = new TimeEntryViewModel();

                //dummy values for testing
                vm.TimeSheetStatus = "This is a test value";
                vm.CheckDate = "07/03/2016";
                vm.StartDate = "06/28/2016";
                vm.EndDate = "07/01/2016";
                vm.CompanyID = "PEO1";
                vm.Company = "The PEO Company";
                vm.ClientID = "001";
                vm.Client = "Harrys Side Running Boards";
                vm.BuildID = "Harrys - Regular";
                vm.TimeSheetProfile = "Regular";
                vm.TimeSheetID = "PEO1.001.Regular.01";
                vm.PayrollNum = "PEO1.001.01239";

                vm.PayCodes.Add("WAGE1");
                vm.PayCodes.Add("WAGE2");
                vm.PayCodes.Add("WAGE3");
                vm.PayCodes.Add("WAGE4");

                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Winton, James", "*********"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Smith, Joe", "*********"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Smith, Joe", "*********"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Davis, Jen", "*********"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Baker, Todd", "*********"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Burroghs, Steve", "010010005"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Lee, Georgia", "010010006"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Bordlin, Rogus", "010010007"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Rebus, Badno", "010010008"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Stradokario, Gembinor", "010010009"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Fhen, Zimnal", "010010010"));
                vm.Employees.Add(new TimeEntryViewModel.DummyTimeSheetRows("Herbie, Hobil", "010010011"));

                foreach (TimeEntryViewModel.DummyTimeSheetRows emp in vm.Employees)
                {
                    foreach (TimeEntryViewModel.TimeCodeVal val in emp.TimeCodeVals)
                    {
                        if (vm.Totals.Exists(a => a.PayCodeName.Equals(val.PayCodeName)))
                        {
                            vm.Totals.Find(a => a.PayCodeName.Equals(val.PayCodeName)).Amount += val.Amount;
                        }
                        else
                        {
                            vm.Totals.Add(new TimeEntryViewModel.TimeCodeVal(val.PayCodeName, val.Amount));
                        }
                    }
                }

                //for each paycode that no employee applies to, add in 0 for total. Also sort totals
                List<TimeEntryViewModel.TimeCodeVal> totalNewList = new List<TimeEntryViewModel.TimeCodeVal>();
                foreach (string code in vm.PayCodes)
                {
                    if (!vm.Totals.Exists(a => a.PayCodeName == code))
                    {
                        totalNewList.Add(new TimeEntryViewModel.TimeCodeVal(code, 0));
                    }
                    else
                    {
                        totalNewList.Add(vm.Totals.Find(a => a.PayCodeName == code));
                    }
                }
                vm.Totals = totalNewList;

                //sorting paycodes so they appear correctly on table
                foreach (TimeEntryViewModel.DummyTimeSheetRows emp in vm.Employees)
                {
                    List<TimeEntryViewModel.TimeCodeVal> newList = new List<TimeEntryViewModel.TimeCodeVal>();

                    foreach (string code in vm.PayCodes)
                    {
                        if (!emp.TimeCodeVals.Exists(a => a.PayCodeName == code))
                        {
                            newList.Add(new TimeEntryViewModel.TimeCodeVal(true));
                        }
                        else
                        {
                            newList.Add(emp.TimeCodeVals.Find(a => a.PayCodeName == code));
                        }
                    }
                    emp.TimeCodeVals = newList;
                }

                vm.Employees = vm.Employees.OrderBy(s => s.EmployeeName).ToList();
                return View(vm);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        private IQueryable<ClientPayrollSchedule> GetSortedList(IQueryable<ClientPayrollSchedule> query, TableFilter filters)
        {
            return _sortBuilder.Sort(query, filters.Sort);
        }

        private IEnumerable<PayrollCockpitTableRow> MapTableRows(IEnumerable<ClientPayrollSchedule> schedules)
        {
            if (schedules == null || schedules.Count() == 0)
                return new List<PayrollCockpitTableRow>(0);

            var mappedRows = schedules.Select(x => new PayrollCockpitTableRow()
            {
                ProfileID = x.ProfileID,
                CheckDate = x.CheckDate != null ? x.CheckDate : null,
                ClientID = x.ClientID,
                ClientName = x.ClientName,
                CompanyId = x.CompanyID,
                CompanyName = x.CompanyName,
                DirectDebitDate = x.DebitDate?.ToShortDateString(),
                IsDNetPayroll = x.DNetPayroll,
                IsOffCyclePayroll = x.OffCyclePayroll,
                DoesTransactionExist = _payrollCockpitPayTypeVerificationService.DoesTransactionExist(x),
                IsSalaryOnly = _payrollCockpitPayTypeVerificationService.IsSalaryOnly(x),
                IsTransactionRequired = _payrollProfileSettingsService.GetRequireTransaction(x.CompanyID, x.ClientID, x.ProfileID),
                PayPeriodBeginDate = x.PayPeriod_BeginDate != null ? ((DateTime)x.PayPeriod_BeginDate).ToShortDateString() : "",
                PayPeriodEndDate = x.PayPeriod_EndDate != null ? ((DateTime)x.PayPeriod_EndDate).ToShortDateString() : "", // ClientPayrollScheduleProvider.GetSchedules does not set this value - will always default to null date
                PayrollNumber = x.PayrollNumber,
                Process_Date = x.Process_Date != null ?x.Process_Date : null,
                Responsible = x.Responsible,
                Schedule_Status = ((PayrollStatus)x.Schedule_Status).GetDisplayName(),
                Schedule_Status_Code = x.Schedule_Status,
                ScheduleKey = x.Schedule_Key,
                ScheduleId = x.Schedule_ID,
                DivisionId = x.Division_ID,              // ClientPayrollScheduleProvider.GetSchedules does not set this value, always null
                PriorityPayroll = x.PriorityPayroll,
                GrossWages = x.Schedule_Status >= (int)PayrollStatus.PayrollReady ? _payrollWorkHeaderService.GetGrossWages(x.PayrollNumber) : 0,
                NumOfChecks = x.Schedule_Status >= (int)PayrollStatus.PayrollReady ? _payrollWorkHeaderService.GetEmployeeCount(x.PayrollNumber) : 0, //TODO: Will need to be updated when split/multiple checks are introduced.
                NumOfEmployees = x.Schedule_Status >= (int)PayrollStatus.PayrollReady ? _payrollWorkHeaderService.GetEmployeeCount(x.PayrollNumber) : 0,
            }).ToList();

            return mappedRows;
        }

        private IQueryable<PayrollCockpitTableRow> GetSortedListPayrollCockpitTR(IQueryable<PayrollCockpitTableRow> query, TableFilter filters)
        {
            return _sortBuilderPayrollCockpitTR.Sort(query, filters.Sort);
        }

        private IEnumerable<PayrollCockpitTableRow> MapTableRowsPayrollcockpitTR(IEnumerable<PayrollCockpitTableRow> schedules)
        {
            if (schedules == null || schedules.Count() == 0)
                return new List<PayrollCockpitTableRow>(0);

            List<int> manualCheckTypes = new List<int>
            {
                (int)ManualCheckTypes.ManualCheck,
                (int)ManualCheckTypes.Adjustment,
            };

            var mappedRows = schedules.Select(x => new PayrollCockpitTableRow()
            {
                ProfileID = x.DefaultClientPayrollSchedule.ProfileID,
                CheckDate = x.DefaultClientPayrollSchedule.CheckDate != null ? x.DefaultClientPayrollSchedule.CheckDate : null,
                ClientID = x.DefaultClientPayrollSchedule.ClientID,
                ClientName = x.DefaultClientPayrollSchedule.ClientName,
                CompanyId = x.DefaultClientPayrollSchedule.CompanyID,
                CompanyName = x.DefaultClientPayrollSchedule.CompanyName,
                DirectDebitDate = x.DefaultClientPayrollSchedule.DebitDate?.ToShortDateString(),
                IsDNetPayroll = x.DefaultClientPayrollSchedule.DNetPayroll,
                IsOffCyclePayroll = x.DefaultClientPayrollSchedule.OffCyclePayroll,
                DoesTransactionExist = _payrollCockpitPayTypeVerificationService.DoesTransactionExist(x.DefaultClientPayrollSchedule),
                IsSalaryOnly = _payrollCockpitPayTypeVerificationService.IsSalaryOnly(x.DefaultClientPayrollSchedule),
                IsTransactionRequired = _payrollProfileSettingsService.GetRequireTransaction(x.DefaultClientPayrollSchedule.CompanyID, x.DefaultClientPayrollSchedule.ClientID, x.DefaultClientPayrollSchedule.ProfileID),
                PayPeriodBeginDate = x.DefaultClientPayrollSchedule.PayPeriod_BeginDate != null ? ((DateTime)x.DefaultClientPayrollSchedule.PayPeriod_BeginDate).ToShortDateString() : "",
                PayPeriodEndDate = x.DefaultClientPayrollSchedule.PayPeriod_EndDate != null ? ((DateTime)x.DefaultClientPayrollSchedule.PayPeriod_EndDate).ToShortDateString() : "", // DefaultClientPayrollScheduleProvider.GetSchedules does not set this value - will always default to null date
                PayrollNumber = x.DefaultClientPayrollSchedule.PayrollNumber,
                Process_Date = x.DefaultClientPayrollSchedule.Process_Date != null ? x.DefaultClientPayrollSchedule.Process_Date : null,
                Responsible = x.DefaultClientPayrollSchedule.Responsible,
                Schedule_Status = Enum.IsDefined(typeof(PayrollStatus), (int)x.DefaultClientPayrollSchedule.Schedule_Status) ?
                                    ((PayrollStatus)x.DefaultClientPayrollSchedule.Schedule_Status).GetDisplayName() :
                                    x.DefaultClientPayrollSchedule.Schedule_Status.ToString(),
                Schedule_Status_Code = x.DefaultClientPayrollSchedule.Schedule_Status,
                ScheduleKey = x.DefaultClientPayrollSchedule.Schedule_Key,
                ScheduleId = x.DefaultClientPayrollSchedule.Schedule_ID,
                DivisionId = x.DefaultClientPayrollSchedule.Division_ID,              
                PriorityPayroll = x.DefaultClientPayrollSchedule.PriorityPayroll,
                GrossWages = x.DefaultClientPayrollSchedule.Schedule_Status >= (int)PayrollStatus.PayrollReady ? _payrollWorkHeaderService.GetGrossWages(x.DefaultClientPayrollSchedule.PayrollNumber) : 0,
                NumOfChecks = x.DefaultClientPayrollSchedule.Schedule_Status >= (int)PayrollStatus.PayrollReady ? _payrollWorkHeaderService.GetEmployeeCount(x.DefaultClientPayrollSchedule.PayrollNumber) : 0, //TODO: Will need to be updated when split/multiple checks are introduced.
                NumOfEmployees = x.DefaultClientPayrollSchedule.Schedule_Status == (int)PayrollStatus.VoidCalculated || x.DefaultClientPayrollSchedule.Schedule_Status == (int)PayrollStatus.VoidCompleted || manualCheckTypes.Contains(x.ManualCheckType) ? _payrollWorkHeaderService.GetEmployeeCount(x.DefaultClientPayrollSchedule.PayrollNumber) : 
                                    x.DefaultClientPayrollSchedule.Schedule_Status >= (int)PayrollStatus.PayrollReady ? _payrollWorkMasterProvider.GetPayrollWorkMastersNumberOfEmployeesByPayrollNumber(x.DefaultClientPayrollSchedule.CompanyID, x.DefaultClientPayrollSchedule.ProfileID, x.DefaultClientPayrollSchedule.PayrollNumber) : 0,
                Employees = _payrollWorkHeaderService.GetEmployeesByPayrollNumber(x.DefaultClientPayrollSchedule.PayrollNumber),
                HasNotes = _payrollProfileNoteService.PayrollHasNotes(x.DefaultClientPayrollSchedule.CompanyID, x.DefaultClientPayrollSchedule.ClientID, x.DefaultClientPayrollSchedule.ProfileID),
                HasMergedInvoices = _invoiceMergeService.HasMergedInvoices(x.DefaultClientPayrollSchedule.PayrollNumber),
                MergedInvoicesInfo = new MergedInvoicesInfoViewModel()
                {
                    HasMergedInvoices = _invoiceMergeService.HasMergedInvoices(x.DefaultClientPayrollSchedule.PayrollNumber),
                    IsMergedInvoice = _invoiceMergeService.IsMergedInvoice(x.DefaultClientPayrollSchedule.PayrollNumber),
                    MergedInvoiceFinalized = _invoiceMergeService.MergedInvoiceFinalized(x.DefaultClientPayrollSchedule.PayrollNumber),
                    MergedInvoicePosted = _invoiceMergeService.MergedInvoicePosted(x.DefaultClientPayrollSchedule.PayrollNumber),
                },
                PayrollType = x.DefaultClientPayrollSchedule.PayrollType,
                PayrollTypeString = x.DefaultClientPayrollSchedule.PayrollType != null
                    ? ((PayrollTypes)x.DefaultClientPayrollSchedule.PayrollType.Value).GetDisplayName()
                    : "N/A",
                ManualCheckType = x.ManualCheckType,
                ManualCheckHasInvoice = _invoicePayrollService.PayrollHasInvoicePayroll(x.DefaultClientPayrollSchedule.PayrollNumber),
            }).ToList();

            return mappedRows;
        }
    }
}
