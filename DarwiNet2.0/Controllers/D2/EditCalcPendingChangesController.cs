using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Logging;
using DarwiNet2._0.Services.D2;
using Microsoft.AspNet.SignalR;
using Microsoft.AspNet.SignalR.Infrastructure;
using MoreLinq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using Thinkware.Cohesion.Payroll;
using Thinkware.Pay360.Messaging;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Controllers.D2
{
    public class EditCalcPendingChangesController : BackOfficeController
    {
        private readonly IPayrollWorkHeaderOriginalService _payrollWorkHeaderOriginalService;
        private readonly IEditCalcPendingChangesService _editCalcPendingChangesService;
        private readonly IPayrollEditService _payrollEditService;
        private readonly IPayrollScheduleService _payrollScheduleService;
        private readonly IMergedInvoiceProvider _mergedInvoiceProvider;
        private readonly IInvoiceMergeReversionService _invoiceMergeReversionService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPayrollApprovalService _payrollApprovalService;
        private readonly IPayrollProfileSettingService _payrollProfileSettingService;

        public EditCalcPendingChangesController(
            DnetEntities dbContext,
            IPayrollWorkHeaderOriginalService payrollWorkHeaderOriginalService,
            IEditCalcPendingChangesService editCalcPendingChangesService,
            IPayrollEditService payrollEditService,
            IPayrollScheduleService payrollScheduleService,
            IMergedInvoiceProvider mergedInvoiceProvider,
            IInvoiceMergeReversionService invoiceMergeReversionService,
            IInvoiceService invoiceService,
            IPayrollApprovalService payrollApprovalService,
            IPayrollProfileSettingService payrollProfileSettingService,
            ILogger logger)
            : base(dbContext, logger)
        {
            _payrollWorkHeaderOriginalService = payrollWorkHeaderOriginalService ?? throw new ArgumentNullException(nameof(payrollWorkHeaderOriginalService));
            _editCalcPendingChangesService = editCalcPendingChangesService ?? throw new ArgumentNullException(nameof(editCalcPendingChangesService));
            _payrollEditService = payrollEditService ?? throw new ArgumentNullException(nameof(payrollEditService));
            _payrollScheduleService = payrollScheduleService ?? throw new ArgumentNullException(nameof(payrollScheduleService));
            _mergedInvoiceProvider = mergedInvoiceProvider ?? throw new ArgumentNullException(nameof(mergedInvoiceProvider));
            _invoiceMergeReversionService = invoiceMergeReversionService ?? throw new ArgumentNullException(nameof(invoiceMergeReversionService));
            _invoiceService = invoiceService ?? throw new ArgumentNullException(nameof(invoiceService));
            _payrollApprovalService = payrollApprovalService ?? throw new ArgumentNullException(nameof(payrollApprovalService));
            _payrollProfileSettingService = payrollProfileSettingService ?? throw new ArgumentNullException(nameof(payrollProfileSettingService));
        }

        [HttpPost]
        public async Task<ActionResult> SavePendingChanges(List<PayrollWorkPendingChangeDto> pendingChanges)
        {
            try
            {
                //Creates a list of only the unique PayrollNumber/EmployeeID combinations present in pendingChanges
                List<PendingChangeCheckDto> pendingChangesChecks = new List<PendingChangeCheckDto>();
                List<string> payrollsToReset = new List<string>();
                List<string> payrollNumbers = pendingChanges.Select(x => x.PayrollNumber).Distinct().ToList();
                foreach (string payrollNumber in payrollNumbers)
                {
                    _payrollApprovalService.ResetPayrollAndInvoiceApprovalsForPayroll(payrollNumber);
                    _invoiceMergeReversionService.ResetMergedPayrolls(payrollNumber, _dbContext);
                    UpdatePayrollStatus(payrollNumber, pendingChanges);
                    ClientPayrollSchedule schedule = _payrollScheduleService.FindSchedule(payrollNumber);
                    if (schedule.Schedule_Status >= (int)PayrollStatus.InvoiceCalculated)
                    {
                        _invoiceService.DeleteInvoiceData(payrollNumber);
                    }
                    List<string> employeeIDs = pendingChanges.Where(x => x.PayrollNumber == payrollNumber).Select(x => x.EmployeeID).Distinct().ToList();
                    foreach (string employeeID in employeeIDs)
                    {
                        pendingChangesChecks.Add(new PendingChangeCheckDto
                        {
                            EmployeeID = employeeID,
                            PayrollNumber = payrollNumber,
                        });
                    }
                }

                if (payrollsToReset.Count > 0)
                {
                    foreach (string payrollNumber in payrollsToReset)
                    {
                        _payrollScheduleService.UpdatePayrollStatus(payrollNumber, PayrollStatus.PayrollCalculated);
                        _invoiceService.DeleteInvoiceData(payrollNumber);
                    }
                }

                //Checks to see if there are existing entries in the PayrollWork**Originals tables and if not, copies the required entries from the PayrollWork** tables.
                List<Tuple<string, string>> results = new List<Tuple<string, string>>();
                foreach (PendingChangeCheckDto pendingChangeCheck in pendingChangesChecks)
                {
                    bool existingOriginals = _payrollWorkHeaderOriginalService.IsExistingPresent(pendingChangeCheck.PayrollNumber, pendingChangeCheck.EmployeeID);
                    if (!existingOriginals)
                    {
                        results = _editCalcPendingChangesService.SaveOriginals(pendingChangeCheck, _dbContext);
                    }
                    else
                    {
                        results.Add(new Tuple<string, string>("Success", "Originals"));
                    }
                    int processCycleIndex = _payrollEditService.GetProcessCycle(pendingChangeCheck.PayrollNumber);
                    foreach (PayrollWorkPendingChangeDto pendingChange in pendingChanges)
                    {
                        if (pendingChange.PayrollNumber == pendingChangeCheck.PayrollNumber && pendingChange.EmployeeID == pendingChangeCheck.EmployeeID)
                        {
                            pendingChange.ProcessCycleIndex = processCycleIndex;
                        }
                    }

                }
                //If the Original entries have been successfully saved or are already present, process all of the pending changes
                var saveOriginalsResult = results.FirstOrDefault(x => x.Item1 == "Success" &&
                                                                      x.Item2 == "Originals");
                if (saveOriginalsResult != null)
                {
                    results.AddRange(ProcessPendingAdds(pendingChanges));
                    results.AddRange(ProcessPendingDeletes(pendingChanges));
                    results.AddRange(SortAndProcessPendingUpdates(pendingChanges));
                }
                if (results.Where(x => x.Item1 != "Success").Any())
                {
                    return JsonSuccess("Error", results);
                }
                return JsonSuccess("Success");
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        private List<Tuple<string, string>> SortAndProcessPendingUpdates(List<PayrollWorkPendingChangeDto> pendingChanges)
        {
            List<Tuple<string, string>> results = new List<Tuple<string, string>>();
            List<PayrollWorkPendingChangeDto> payrollWorkUpdateChanges = pendingChanges.Where(x => x.Action == "Update").ToList();
            List<PayrollWorkPendingChangeKeyValuesDto> payrollWorkPendingChangeKeyValues = payrollWorkUpdateChanges.Select(x => x.KeyValues).DistinctBy(x => new { x.PayrollNumber, x.EmployeeID, x.Code, x.Department, x.Table, x.SUTAState, x.StateTax, x.LocalTax, x.Position, x.WorkersComp, x.PayRunType, x.IndexLong, x.SnapshotID, x.TRXNumber, x.Benefit, x.Deduction, x.PaymentAdjustmentNumber, x.StateCode }).ToList();

            foreach (PayrollWorkPendingChangeKeyValuesDto payrollWorkPendingChangeKeyValue in payrollWorkPendingChangeKeyValues)
            {
                List<PayrollWorkPendingChangeDto> updatesToTableRow = payrollWorkUpdateChanges.Where(x => IsEqualKeyValues(payrollWorkPendingChangeKeyValue, x.KeyValues)).ToList();
                results.Add(_editCalcPendingChangesService.ProcessChanges(updatesToTableRow, payrollWorkPendingChangeKeyValue, _dbContext));
            }
            _dbContext.SaveChanges();
            return results;
        }

        private List<Tuple<string, string>> ProcessPendingAdds(List<PayrollWorkPendingChangeDto> pendingChanges)
        {
            List<Tuple<string, string>> results = new List<Tuple<string, string>>();
            List<PayrollWorkPendingChangeDto> payrollWorkAddChanges = pendingChanges.Where(x => x.Action == "Add").ToList();
            foreach (PayrollWorkPendingChangeDto payrollWorkPendingChange in payrollWorkAddChanges)
            {
                results.Add(_editCalcPendingChangesService.ProcessAdds(payrollWorkPendingChange, _dbContext));
            }
            _dbContext.SaveChanges();
            return results;
        }

        private List<Tuple<string, string>> ProcessPendingDeletes(List<PayrollWorkPendingChangeDto> pendingChanges)
        {
            List<Tuple<string, string>> results = new List<Tuple<string, string>>();
            List<PayrollWorkPendingChangeDto> payrollWorkDeleteChanges = pendingChanges.Where(x => x.Action == "Delete").ToList();
            foreach (PayrollWorkPendingChangeDto payrollWorkPendingChange in payrollWorkDeleteChanges)
            {
                results.Add(_editCalcPendingChangesService.ProcessDeletes(payrollWorkPendingChange, _dbContext));
            }
            _dbContext.SaveChanges();
            return results;
        }

        private bool IsEqualKeyValues(PayrollWorkPendingChangeKeyValuesDto keyValuesA, PayrollWorkPendingChangeKeyValuesDto keyValuesB)
        {
            if (keyValuesA == null || keyValuesB == null) return false;

            if (keyValuesA.PayrollNumber != keyValuesB.PayrollNumber) return false;
            if (keyValuesA.EmployeeID != keyValuesB.EmployeeID) return false;
            if (keyValuesA.Table != keyValuesB.Table) return false;
            if (keyValuesA.Code != keyValuesB.Code) return false;
            if (keyValuesA.Department != keyValuesB.Department) return false;
            if (keyValuesA.Position != keyValuesB.Position) return false;
            if (keyValuesA.SUTAState != keyValuesB.SUTAState) return false;
            if (keyValuesA.StateTax != keyValuesB.StateTax) return false;
            if (keyValuesA.LocalTax != keyValuesB.LocalTax) return false;
            if (keyValuesA.WorkersComp != keyValuesB.WorkersComp) return false;
            if (keyValuesA.PayRunType != keyValuesB.PayRunType) return false;
            if (keyValuesA.IndexLong != keyValuesB.IndexLong) return false;
            if (keyValuesA.SnapshotID != keyValuesB.SnapshotID) return false;
            if (keyValuesA.TRXNumber != keyValuesB.TRXNumber) return false;
            if (keyValuesA.Benefit != keyValuesB.Benefit) return false;
            if (keyValuesA.Deduction != keyValuesB.Deduction) return false;
            if (keyValuesA.PaymentAdjustmentNumber != keyValuesB.PaymentAdjustmentNumber) return false;
            if (keyValuesA.StateCode != keyValuesB.StateCode) return false;
            return true;
        }

        private void UpdatePayrollStatus(string payrollNumber, List<PayrollWorkPendingChangeDto> pendingChanges)
        {
            List<string> nonRecalcColumns = new List<string>{ "FederalWithholding", "FederalTaxOnTips", "TotalStateTax", "StateTaxOnTips", "TotalLocalTax", "LocalTaxOnTips" };
            var updateChanges = pendingChanges.Where(x => x.Action == "Update");
            bool noRecalcRequired = updateChanges.Any(x => nonRecalcColumns.Contains(x.Column)) && !updateChanges.Any(x => !nonRecalcColumns.Contains(x.Column));

            if (noRecalcRequired)
            {
                PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                var settings = _payrollProfileSettingService.GetProcessOptions(oPayrollNumber, true);
                if (settings != null)
                {
                    if (settings.ControlsAndVariances)
                    {
                        _payrollScheduleService.UpdatePayrollStatus(payrollNumber, PayrollStatus.PayrollControlsAndVariancesNeedsProcessed);
                    }
                    else if (settings.ApprovePayroll)
                    {
                        _payrollScheduleService.UpdatePayrollStatus(payrollNumber, PayrollStatus.PayrollApprovalsRequired);
                    }
                    else
                    {
                        _payrollScheduleService.UpdatePayrollStatus(payrollNumber, PayrollStatus.PayrollCalculated);
                    }
                }
            }
            else
            {
                _payrollScheduleService.UpdatePayrollStatus(payrollNumber, PayrollStatus.PayrollCalculated);
            }
        }
    }
}