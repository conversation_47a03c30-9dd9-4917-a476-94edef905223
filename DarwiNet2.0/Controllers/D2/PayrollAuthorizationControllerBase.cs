using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Logging;
using System.Web.Mvc;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Controllers.D2
{
    public class PayrollAuthorizationControllerBase : PayrollControllerBase
    {
        public PayrollAuthorizationControllerBase(
            DnetEntities dbContext,
            IPayrollProfileSettingService payrollProfileSettingService,
            IPayrollTeamsService payrollTeamsService,
            ILogger logger)
            : base(dbContext, payrollProfileSettingService, payrollTeamsService, logger) 
        {
            
        }

        protected override void AuthorizeBackOfficeAccess(AuthorizationContext filterContext)
        {
            var request = PayrollProcessingAuthorizationRequest.FromHttpRequest(filterContext.HttpContext.Request);
            var response = new PayrollProcessingAuthorizationHandler(_dbContext).Handle(request);
            GlobalVariables.CanPayroll = response.CanPayroll;
            var hasPayrollAccess = GlobalVariables.HasPayrollAccess;
            if (!hasPayrollAccess)
            {
                filterContext.RedirectNoAccess();
            }
        }
    }
}