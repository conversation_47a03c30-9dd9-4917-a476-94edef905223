using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Filters;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Logging;
using DarwiNet2._0.ViewModels.D2;
using DarwiNet2._0.ViewModels.D2.PayrollProfileSettings;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using System.Web.Mvc.Html;

namespace DarwiNet2._0.Controllers.D2
{
    [IsSessionActive]
    [SystemAndClientLevelFilter]
    public class PayrollProfileEmployeeSelectionsController : PayrollControllerBase
    {
        private readonly ICompanyService _companyService;
        private readonly IEmployeeService _employeeService;
        private readonly IPayrollProfileEmployeeCriteriaService _payrollProfileEmployeeCriteriaService;
        private readonly IPayrollProfileEmployeeService _payrollProfileEmployeeService;
        private readonly IPayrollTeamsService _payrollTeamsService;

        public PayrollProfileEmployeeSelectionsController(
            DnetEntities dbContext,
            ICompanyService companyService,
            IEmployeeService employeeService,
            IPayrollProfileEmployeeCriteriaService payrollProfileEmployeeCriteriaService,
            IPayrollProfileEmployeeService payrollProfileEmployeeService,
            IPayrollProfileSettingService payrollProfileSettingService,
            IPayrollTeamsService payrollTeamsService,
            ILogger logger)
            : base(dbContext, payrollProfileSettingService, payrollTeamsService, logger)
        {
            _companyService = companyService ?? throw new ArgumentNullException(nameof(companyService));
            _employeeService = employeeService ?? throw new ArgumentNullException(nameof(employeeService));
            _payrollProfileEmployeeCriteriaService = payrollProfileEmployeeCriteriaService ?? throw new ArgumentNullException(nameof(payrollProfileEmployeeCriteriaService));
            _payrollProfileEmployeeService = payrollProfileEmployeeService ?? throw new ArgumentNullException(nameof(payrollProfileEmployeeService));
            _payrollTeamsService = payrollTeamsService ?? throw new ArgumentNullException(nameof(payrollTeamsService));
        }

        [PageAccess(MenuItemIds.SystemPayrollSettingsEmployeeSelection)]
        public ActionResult EmployeeSelections(int companyId, string profileId, string clientId)
        {
            try
            {
                var payrollProfileSettings = base.PayrollProfileSettingService.GetPayrollProfileSettings(companyId, clientId, profileId);
                var check = new BuildLockController();
                check.Check(payrollProfileSettings);

                EmployeeSelectionsViewModel viewModel = base.GetViewModel<EmployeeSelectionsViewModel>(companyId, clientId, profileId);

                EmployeeSelectionsModel employeeSelectionsModel = _payrollProfileEmployeeCriteriaService.GetEmployeeSelectionsModel(companyId, clientId, profileId);
                viewModel.Departments = employeeSelectionsModel.Departments;
                viewModel.EmployeeClassCodes = employeeSelectionsModel.EEClasses;
                viewModel.Positions = employeeSelectionsModel.Positions;

                viewModel.RefreshAutoListPayRun = new PayrollProfileOption { Type = "RefreshAutoListPayRun", Value = employeeSelectionsModel.RefreshAutoListPayRun };
                viewModel.IgnoreManuallyAddedEmployeesOnRefresh = new PayrollProfileOption { Type = "IgnoreManuallyAddedEmployees", Value = employeeSelectionsModel.IgnoreManuallyAddedEmployeesOnRefresh };

                var workStatusList = EnumHelper.GetSelectList(typeof(enEmploymentStatuses))
                                               .Select(ws => new Code_Description
                                               {
                                                   Code = ws.Value,
                                                   Description = ws.Text
                                               });

                viewModel.WorkStatusList = workStatusList;
                if (!string.IsNullOrEmpty(employeeSelectionsModel.EmployeeWorkStatus))
                {
                    var selectedWorkStatusCodesList = employeeSelectionsModel.EmployeeWorkStatus.Split(',');
                    var selectedWorkStatuses = selectedWorkStatusCodesList
                        .Select(x => new Code_Description
                        {
                            Code = x,
                            Description = workStatusList.Where(y => y.Code == x).Select(y => y.Description).FirstOrDefault()
                        })
                        .ToList();
                    viewModel.SelectedWorkStatuses = selectedWorkStatuses;
                }

                viewModel.PageAccess = MenuAccess.PageSecurity(MenuItemIds.SystemPayrollSettings);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult EmployeeSelections(EmployeeSelectionFilters filters)
        {
            try
            {
                var companyId = filters.CompanyId;
                var clientId = filters.ClientId;
                var profileId = filters.ProfileId;

                var settings = base.PayrollProfileSettingService.GetPayrollProfileSettings(companyId, clientId, profileId);
                var check = new BuildLockController();
                check.Check(settings);

                _payrollProfileEmployeeCriteriaService.UpdateEmployeeSelections(companyId, clientId, profileId, filters);

                // Update profile settings RefreshAutoListPayRun, EmployeeWorkStatus, and IgnoreManuallyAddedEmployees
                EmployeeSelectionsPayrollProfileSettingsModel employeeSelectionsPayrollProfileSettings = new EmployeeSelectionsPayrollProfileSettingsModel
                {
                    EmployeeWorkStatus = filters.WorkStatus,
                    //PayrollTeamID = filters.PayrollTeamID,
                    RefreshAutoListPayRun = filters.RefreshAutoListPayRun,
                    IgnoreManuallyAddedEmployeesOnRefresh = filters.IgnoreManuallyAddedEmployeesOnRefresh
                };
                base.PayrollProfileSettingService.UpdatePayrollProfileSettingsEmployeeSelections(companyId, clientId, profileId, employeeSelectionsPayrollProfileSettings);

                if (!_companyService.CheckCompanyAccessByUser(companyId, GlobalVariables.DNETOwnerID))
                    return JsonError("Do not have privileges.");

                string employeeWorkStatus = filters.WorkStatus != null ? string.Join(",", filters.WorkStatus.Select(x => x.Code)) : "";
                if (filters.RefreshAutoListPayRun)
                {
                    _payrollProfileEmployeeService.FilterEmployeeCriteria(companyId, clientId, profileId, employeeWorkStatus, settings.IgnoreManuallyAddedEmployees);
                }

                return JsonSuccess("Employee filters applied successfully.");
            }
            catch (Exception ex)
            {
                return JsonError("There was a problem saving your selections.", ex);
            }
        }

        [HttpPost]
        public ActionResult SelectArrayUpdate(int companyId, string clientId, string profileId, string[] codes, string selectionType)
        {
            try
            {
                _payrollProfileEmployeeCriteriaService.Update(companyId, clientId, profileId, selectionType, codes);
                return JsonSuccess();
            }
            catch (Exception exception)
            {
                return JsonInternalServerError(exception);
            }
        }

        [PageAccess(MenuItemIds.SystemPayrollSettingsEmployeeList)]
        public ActionResult EmployeeList(int companyId, string clientId, string profileId)
        {
            try
            {
                EmployeeListViewModel viewModel = base.GetViewModel<EmployeeListViewModel>(companyId, clientId, profileId);

                // Setup code for the context of this action.
                viewModel.Employees = _employeeService.GetPayrollProfileEmployees(companyId, clientId, profileId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult EmployeeList(int companyId, string clientId, string profileId, IEnumerable<string> selectedIds)
        {
            try
            {
                _payrollProfileEmployeeService.Update(companyId, clientId, profileId, selectedIds, true);

                return JsonSuccess("Employees list successfully updated.");
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult UpdateOption(PayrollProfileOption option)
        {
            try
            {
                base.PayrollProfileSettingService.UpdatePayrollProfileSettingOption(option);

                return JsonSuccess("Payroll Profile options have been successfully updated.");
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult UpdateUseAllEmployeeClasses(int companyID, string clientID, string profileID, bool useAllEmployeeClasses)
        {
            try
            {
                base.PayrollProfileSettingService.UpdateUseAllEmployeeClasses(companyID, clientID, profileID, useAllEmployeeClasses);

                var newCriteria = _payrollProfileEmployeeCriteriaService.GetPayrollProfileEmployeeCriteria(companyID, clientID, profileID, out var criteria);
                var newClasses = newCriteria[Constants.PayrollProfileEmployeeCriteriaTypes.EECLASS];

                return JsonSuccess("", newClasses);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }
    }
}