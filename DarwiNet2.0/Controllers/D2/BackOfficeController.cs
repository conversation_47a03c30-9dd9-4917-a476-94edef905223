using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Logging;
using System.Web.Mvc;
using System.Web.Routing;
using Thinkware;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Controllers.D2
{
    public class BackOfficeController : D2ControllerBase
    {
        protected readonly DnetEntities _dbContext;

        public BackOfficeController(DnetEntities dbContext, ILogger logger)
            : base(logger)
        {
            _dbContext = Guard.ThrowIfNull(dbContext, nameof(dbContext));
        }

        protected override void OnAuthorization(AuthorizationContext filterContext)
        {
            AuthorizeBackOfficeAccess(filterContext);
            base.OnAuthorization(filterContext);
        }

        protected virtual void AuthorizeBackOfficeAccess(AuthorizationContext filterContext)
        {
            var request = BackOfficeAuthorizationRequest.FromHttpRequest(filterContext.HttpContext.Request);
            var response = new BackOfficeAuthorizationHandler(_dbContext).Handle(request);
            GlobalVariables.CanPayroll = response.CanPayroll;
            var hasPayrollAccess = GlobalVariables.HasPayrollAccess;
            if (!hasPayrollAccess)
            {
                if (GlobalVariables.Customer == null)
                {
                    string destinationUrl = null;
                    if (filterContext.HttpContext.Request.HttpMethod == "GET") destinationUrl = filterContext.HttpContext.Request.RawUrl;
                    filterContext.Result = new RedirectToRouteResult(new RouteValueDictionary(new { action = "SessionExpired", controller = "Home", destinationUrl = destinationUrl }));
                }
                else
                {
                    filterContext.RedirectNoAccess();
                }
            }
        }
    }
}