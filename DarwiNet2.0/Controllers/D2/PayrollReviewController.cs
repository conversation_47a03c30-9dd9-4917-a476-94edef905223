using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Filters;
using DarwiNet2._0.Interfaces.Models;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Logging;
using DarwiNet2._0.ViewModels.D2;
using DarwiNet2._0.ViewModels.D2.Payroll;
using DarwiNet2._0.ViewModels.D2.PayrollReview;
using DataDrivenViewEngine.Models.Core;
using Microsoft.Ajax.Utilities;
using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web.Mvc;
using Thinkware.Cohesion.Payroll;
using Thinkware.Pay360.Messaging;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Controllers.D2
{
    [IsSessionActive]
    [SystemAndClientLevelFilter]
    public class PayrollReviewController : BackOfficeController  //D2ControllerBase
    {
        //private readonly DnetEntities _dbContext;
        private readonly IPayrollWorkHeaderService _payrollWorkHeadersService;
        private readonly IPayrollReviewTaxService _payrollReviewTaxService;
        private readonly IPayrollWorkBenefitService _payrollWorkBenefitService;
        private readonly IPayrollWorkDeductionService _payrollWorkDeductionService;
        private readonly IPayrollChecksService _payrollChecksService;
        private readonly IPayrollProfileNoteService _payrollProfileNoteService;
        private readonly IPayrollReviewHeaderService _payrollReviewHeaderService;
        private readonly IPayrollApprovalService _payrollApprovalService;
        private readonly IPayrollReviewService _payrollReviewService;
        private readonly IControlAndVarianceService _controlAndVarianceService;
        private readonly IControlRecordService _controlRecordService;
        private readonly IVarianceRecordService _varianceRecordService;
        private readonly IPayrollProfileReportProvider _payrollProfileReportProvider;
        private readonly IPayrollApprovalSetupService _payrollApprovalSetupService;
        private readonly IClientPayrollScheduleProvider _clientPayrollScheduleProvider;
        private readonly IPayrollWorkMissedCodeService _payrollWorkMissedCodeService;
        private readonly IPayrollProfileControlService _payrollProfileControlService;
        private readonly IPayrollProfileVarianceService _payrollProfileVarianceService;
        private readonly IPayrollEditService _payrollEditService;
        private readonly IPayrollProfileSettingService _payrollProfileSettingService;
        private readonly IPayrollEmployeesService _payrollEmployeesService;
        private readonly IPayrollProcessOptionProvider _payrollProcessOptionProvider;
        private readonly IInvoiceMergeService _invoiceMergeService;
        private readonly ISortBuilder<EmployeeDTO> _sortBuilder;

        public PayrollReviewController(
            IPayrollWorkHeaderService payrollWorkHeadersService,
            IPayrollReviewTaxService payrollReviewTaxService,
            IPayrollWorkBenefitService payrollWorkBenefitService,
            IPayrollWorkDeductionService payrollWorkDeductionService,
            IPayrollChecksService payrollChecksService,
            IPayrollProfileNoteService payrollProfileNoteService,
            IPayrollProfileSettingService payrollProfileSettingService,
            IPayrollReviewHeaderService payrollReviewHeaderService,
            IPayrollApprovalService payrollApprovalService,
            IPayrollReviewService payrollReviewService,
            IControlAndVarianceService controlAndVarianceService,
            IControlRecordService controlRecordService,
            IVarianceRecordService varianceRecordService,
            IPayrollProfileReportProvider payrollProfileReportProvider,
            IPayrollApprovalSetupService payrollApprovalSetupService,
            IClientPayrollScheduleProvider clientPayrollScheduleProvider,
            IPayrollWorkMissedCodeService payrollWorkMissedCodeService,
            IPayrollProfileControlService payrollProfileControlService,
            IPayrollProfileVarianceService payrollProfileVarianceService,
            IPayrollEditService payrollEditService,
            IPayrollProcessOptionProvider payrollProcessOptionProvider,
            IInvoiceMergeService invoiceMergeService,
            ISortBuilder<EmployeeDTO> sortBuilder,
            ILogger logger,
            DnetEntities dbContext)
            : base(dbContext, logger)
        {
            //_dbContext = dbContext;
            _payrollWorkHeadersService = payrollWorkHeadersService;
            _payrollReviewTaxService = payrollReviewTaxService;
            _payrollChecksService = payrollChecksService;
            _payrollWorkBenefitService = payrollWorkBenefitService;
            _payrollWorkDeductionService = payrollWorkDeductionService;
            _payrollProfileNoteService = payrollProfileNoteService;
            _payrollReviewHeaderService = payrollReviewHeaderService;
            _payrollApprovalService = payrollApprovalService;
            _payrollReviewService = payrollReviewService;
            _controlAndVarianceService = controlAndVarianceService;
            _controlRecordService = controlRecordService;
            _varianceRecordService = varianceRecordService;
            _payrollProfileReportProvider = payrollProfileReportProvider;
            _payrollApprovalSetupService = payrollApprovalSetupService;
            _clientPayrollScheduleProvider = clientPayrollScheduleProvider;
            _payrollWorkMissedCodeService = payrollWorkMissedCodeService;
            _payrollProfileControlService = payrollProfileControlService;
            _payrollProfileVarianceService = payrollProfileVarianceService;
            _payrollEditService = payrollEditService;
            _payrollProfileSettingService = payrollProfileSettingService;
            _payrollProcessOptionProvider = payrollProcessOptionProvider;
            _invoiceMergeService = invoiceMergeService;
            _sortBuilder = sortBuilder;
            _payrollEmployeesService = new PayrollEmployeesService(dbContext, sortBuilder);
        }

        // Method to be used when linking with actual data
        public ActionResult Review(string payrollNumber, bool fromApprovals = false, bool fromCompletedPayrolls = false, bool fromPayrollDashboard = false, bool fromPayrollProfile = false, int fromPayrollProfileCompany = 0, string fromPayrollProfileProfile = "", string fromPayrollProfileClient = "")
        {
            PayrollReviewViewModel vm = ReviewHelper(payrollNumber);
            /*int menuAccessLevel = (GlobalVariables.DNETLevel == DNetAccessLevel.Client) ?
                                    MenuAccess.PageSecurity(MenuItemIds.ClientPayrollCockpitPayrollReview) :
                                    MenuAccess.PageSecurity(MenuItemIds.SystemPayrollCockpitPayrollReview);
            if (menuAccessLevel == MenuAccessLevel.NoAccess) return RedirectToAction("Index", "NoAccess");*/
            //7230 - DG - 11/24/2020
            vm.fromApprovals = fromApprovals;
            vm.fromCompletedPayrolls = fromCompletedPayrolls;
            vm.fromPayrollDashboard = fromPayrollDashboard;

            vm.fromPayrollProfile = fromPayrollProfile;
            vm.fromPayrollProfileCompany = fromPayrollProfileCompany;
            vm.fromPayrollProfileProfile = fromPayrollProfileProfile;
            vm.fromPayrollProfileClient = fromPayrollProfileClient;
            vm.IsVoid = (bool)_clientPayrollScheduleProvider.GetSchedule(payrollNumber)?.OriginalPayrollNumber.IsNotNullOrEmpty();
            vm.IsPayrollUser = FieldTranslation.IsPayrollUser();
            vm.ManualCheckType = _clientPayrollScheduleProvider.GetManualCheckType(payrollNumber);

            string[] Clientid = payrollNumber.Split('-');
            ViewData["company"] = Clientid[1];
            ViewData["Clientid"] = Clientid[2];
            ViewData["PayrollNumber"] = payrollNumber;
            return View(vm);
        }

        //This action is called when a user clicks the "Go Back to Review" button that appears on the Payroll Notes page
        public ActionResult ReviewFromNotes(string payrollNumber)
        {
            PayrollReviewViewModel vm = ReviewHelper(payrollNumber);

            return View("Review", vm);
        }

        public PayrollReviewViewModel ReviewHelper(string payrollNumber)
        {
            PayrollReviewViewModel vm = new PayrollReviewViewModel();

            PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);

            var payrollProfileSettingsHeader = _payrollReviewHeaderService.SetupPayrollHeader(payrollNumber);

            vm.PayrollNumber = payrollNumber;
            vm.PayrollProfileSettingsHeader = payrollProfileSettingsHeader;
            vm.PayrollInfo = _payrollReviewHeaderService.SetupPayrollInfo(payrollNumber);
            vm.WagesTotals = _payrollWorkHeadersService.GetWagesTotals(payrollNumber);
            vm.TaxesTotals = _payrollReviewTaxService.GetTaxesTotals(payrollNumber);
            vm.BenefitsTotals = _payrollWorkBenefitService.GetBenefitsTotals(payrollNumber);
            vm.DeductionsTotals = _payrollWorkDeductionService.GetDeductionsTotals(payrollNumber);
            vm.ProfileSettingsViewModel = _payrollReviewService.GetProfileSettingsViewModel(payrollNumber);

            vm.NumNotes = _payrollProfileNoteService.GetPayrollProfileNotesCount(GlobalVariables.CompanyID, GlobalVariables.Client, GlobalVariables.PayrollProfile, new List<string>());

            vm.PayrollApprovalViewModel = GetPayrollApprovalViewModel(payrollNumber, GlobalVariables.DNETOwnerID);

            var timeSheetDecimals = _payrollReviewService.GetLargestDecimalPrecisions(GlobalVariables.CompanyID, GlobalVariables.Client, payrollNumber);

            vm.PCDecimals = timeSheetDecimals.PCDecimals;
            vm.DedDecimals = timeSheetDecimals.DedDecimals;
            vm.BenDecimals = timeSheetDecimals.BenDecimals;

            vm.ControlsAndVariances = _controlAndVarianceService.GetAll();
            vm.ControlRecords = _controlRecordService.GetControlRecords(payrollNumber);
            vm.VarianceRecords = _varianceRecordService.GetVarianceRecords(payrollNumber);
            vm.HasProfileControlsVariances = _payrollProfileControlService.HasProfileControls(oPayrollNumber.CompanyId, oPayrollNumber.ClientId, oPayrollNumber.ProfileId, "payroll") ||
                                             _payrollProfileVarianceService.HasProfileVariances(oPayrollNumber.CompanyId, oPayrollNumber.ClientId, oPayrollNumber.ProfileId, "payroll");

            vm.UserID = GlobalVariables.DNETOwnerID;
            var user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.DNETOwnerID);
            vm.IsClient = user.ClientLevelEnabled;
            vm.CanPayroll = FieldTranslation.IsPayrollUser() && _payrollProfileSettingService.UserHasProfilePermissions(oPayrollNumber.CompanyId, oPayrollNumber.ClientId, oPayrollNumber.ProfileId, GlobalVariables.DNETOwnerID, payrollNumber, "payroll");
            vm.RequiresRecalculation = _payrollEditService.PayrollHasUnprocessedEdits(payrollNumber);
            vm.PayrollProfileReportControls = (List<PayrollProfileReport>)_payrollProfileReportProvider.GetPayrollProfileReports(payrollProfileSettingsHeader.CompanyID, payrollProfileSettingsHeader.ClientID, payrollProfileSettingsHeader.ProfileID);
            vm.SSRSReportControls = (List<SSRSReport>)_payrollProfileReportProvider.GetSSRSReports();
            vm.CanViewItemsByApproval = false;
            vm.MergedInvoicesInfo = new MergedInvoicesInfoViewModel()
            {
                HasMergedInvoices = _invoiceMergeService.HasMergedInvoices(payrollNumber),
                IsMergedInvoice = _invoiceMergeService.IsMergedInvoice(payrollNumber),
                MergedInvoiceFinalized = _invoiceMergeService.MergedInvoiceFinalized(payrollNumber),
                MergedInvoicePosted = _invoiceMergeService.MergedInvoicePosted(payrollNumber),
            };

            var payrollProcessOptions = _payrollProcessOptionProvider.GetPayrollProcessOption(payrollNumber);
            
            if (payrollProcessOptions != null)
            {
                vm.AutoInvoiceApprovals = payrollProcessOptions.AutoApprovalsInvoice;
                vm.AutoMissedCodes = payrollProcessOptions.AutoMissedCodeProcessing;
            }
            else
            {
                vm.AutoInvoiceApprovals = false;
                vm.AutoMissedCodes = false;
            }

            // Get recipients that can access profile report nav menu items by approval flag
            var approvalRecipients = _payrollApprovalSetupService.GetPayrollApprovalRecipientsName(payrollProfileSettingsHeader.CompanyID, payrollProfileSettingsHeader.ClientID, payrollProfileSettingsHeader.ProfileID, payrollNumber);

            // If current user is in the approval recipients
            if (approvalRecipients.Contains(GlobalVariables.DNETOwnerID))
            {
                vm.CanViewItemsByApproval = true;
            }

            return vm;
        }

        public JsonResult GetAvailableEmployees(int companyId, string clientId, string profileId, string payrollNumber, bool showAvailableEmployees)
        {
            try
            {
                var vm = _payrollReviewService.GetAvailableEmployeesToAdd(companyId, clientId, profileId, payrollNumber, showAvailableEmployees);
                return JsonSuccess(data: new TableFilterData<PayrollReviewAvailableEmployee>
                {
                    Data = vm,
                    FilteredEntries = vm.Count(),
                    TotalEntries = vm.Count(),
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public JsonResult GetAvailableEmployeesV2(TableFilter filters)
        {
            try
            {
                var vm = _payrollReviewService.GetAvailableEmployeesToAddV2(filters);
                return JsonSuccess(data: new TableFilterData<PayrollReviewAvailableEmployee>
                {
                    Data = vm.Query.ToList(),
                    FilteredEntries = vm.FilteredEntries,
                    TotalEntries = vm.TotalEntries,
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpGet]
        public JsonResult GetApprovalInformation(string payrollNumber)
        {
            try
            {
                var approvalInformation = _payrollApprovalService.GetApprovalInformation(payrollNumber);
                return JsonSuccess("Success", approvalInformation);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult AddEmployees(string clientId, string profileId, string payrollNumber, List<PayrollReviewAvailableEmployee> employees)
        {
            try
            {
                var companyId = GlobalVariables.CompanyID;
                var updates = _payrollReviewService.AddEmployees(companyId, clientId, profileId, payrollNumber, employees);
                return JsonSuccess("Success", updates);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult CreateEmployee(PayrollEmployeeInfoViewModel employee, string payrollNumber)
        {
            try
            {
                PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                _payrollEmployeesService.CreateEmployee(employee, out Dictionary<string, string> errors);
                _payrollReviewService.AddEmployees(employee.CompanyID, employee.ClientID, oPayrollNumber.ProfileId, payrollNumber,
                    new List<DTOs.PayrollReviewAvailableEmployee>() {
                        new DTOs.PayrollReviewAvailableEmployee() {
                            EmployeeID = employee.EmployeeID,
                            EmployeeName = employee.LastName + ", " + employee.FirstName,
                            Position = employee.Position,
                            Department = employee.Department
                        } });
                if (errors.Any())
                {
                    return JsonError(data: errors);
                }
                GlobalVariables.NavPayrollNumber = payrollNumber;
                return JsonSuccess();
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        //TODO: Remove employee that hasn't had a PayrollWorkHeader created yet. (ex: Employee added but not processed). 
        public JsonResult RemoveEmployee(string employeeId, string payrollNumber, bool deleteMissedCodes = false)
        {
            try
            {
                var companyId = GlobalVariables.CompanyID;
                _payrollReviewService.RemoveEmployeeFromPayroll(companyId, employeeId, payrollNumber, _dbContext);
                if (deleteMissedCodes == true)
                {
                    _payrollWorkMissedCodeService.Delete(payrollNumber, employeeId);
                }
                UpdatePayrollStatus(payrollNumber);
                return JsonSuccess(employeeId, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        // TODO: change javascript and action parameters to pass in companyId, clientId, profileId
        public JsonResult DeleteChecksForSSN(string ssn)
        {
            try
            {
                var empID = _payrollChecksService.DeleteEmployeeChecks("1", "2", "3", ssn);
                return JsonSuccess(empID, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        // TODO: change javascript and action parameters to pass in payroll number and snapshot id
        public JsonResult AddEmployeesToSnapshot(List<PayrollReviewCheckViewModel> employeeList)
        {
            try
            {
                var empID = employeeList[0].CheckHeader.EmployeeID;
                throw new NotImplementedException(); // TW-MF: This is here because PayrollSnapshotService.AddEmployeeToSnapshot was not implemented.
                return JsonSuccess(empID, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public JsonResult CompletePayroll(string payrollNumber)
        {
            _clientPayrollScheduleProvider.CompletePayrollByPayrollNumber(payrollNumber);
            return JsonSuccess();
        }

        [HttpPost]
        public ActionResult GetPayrollApprovalViewModel(string payrollNumber)
        {
            try
            {
                PayrollApprovalViewModel vm = GetPayrollApprovalViewModel(payrollNumber, GlobalVariables.DNETOwnerID);
                return JsonSuccess(data: vm);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }

        }

        private PayrollApprovalViewModel GetPayrollApprovalViewModel(string payrollNumber, string userID)
        {
            var payrollApproval = _payrollApprovalService.GetPayrollApproval(payrollNumber, userID);

            if (payrollApproval == null)
            {
                return new PayrollApprovalViewModel();
            }

            return new PayrollApprovalViewModel
            {
                PayrollNumber = payrollNumber,
                Approved = payrollApproval.Approved,
                Rejected = payrollApproval.Rejected,
                RejectMessage = payrollApproval.RejectedMessage,
                IsApprovalCompleted = _payrollApprovalService.IsPayrollApprovalComplete(payrollNumber, payrollApproval.PayrollApprovalSetupSequence) && (payrollApproval.Approved || payrollApproval.Rejected),
                IsPayrollApprover = _payrollApprovalService.IsPayrollApprover(payrollNumber, GlobalVariables.DNETOwnerID)
            };
        }

        [HttpPost]
        public JsonResult ControlsAndVariances(string payrollNumber)
        {
            try
            {
                var oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                _payrollApprovalService.ResetPayrollAndInvoiceApprovalsForPayroll(payrollNumber);
                var userId = GlobalVariables.DNETOwnerID;
                var payrollWorkHeaders = _dbContext.PayrollWorkHeaders.Where(x => x.PayrollNumber == payrollNumber).Select(x => new { x.PayrollNumber, x.SnapshotID, x.EmployeeID });
                var snapshotId = payrollWorkHeaders.Where(x => x.SnapshotID != "").Select(x => x.SnapshotID).FirstOrDefault();
                var processOptions = _payrollProfileSettingService.GetProcessOptions(oPayrollNumber);
                RabbitMQManager.Instance.PublishTask(new ProcessPayrollControlsAndVariancesTask(payrollNumber, snapshotId, oPayrollNumber.CustomerId, userId, processOptions)
                {
                    ConnectionString = _dbContext.Database.Connection.ConnectionString
                });
                return Json(payrollWorkHeaders.Select(x => x.EmployeeID));
            }
            catch (Exception ex)
            {
                return JsonError(ex.Message);
            }
        }

        private string FormatControlOrVarianceNumber(decimal? number, string unit)
        {
            if (number == null)
            {
                return "";
            }

            if (unit == "$")
            {
                return String.Format("{0:C}", number);
            }
            else if (unit == "hr")
            {
                return $"{String.Format("{0:0.###}", number)} hr";
            }
            else if (unit == "%")
            {
                return $"{String.Format("{0:0.###}", number)}%";
            }
            else
            {
                return String.Format("{0:0.###}", number);
            }
        }

        [HttpGet]
        public JsonResult Controls(string payrollNumber, string employeeId, string controlTypes, string field)
        {
            List<string> controlTypesSplit = new List<string>();
            if (controlTypes != null)
            {
                controlTypesSplit = controlTypes.Split(',').ToList();
            }

            if (field == "null")
            {
                field = null;
            }

            List<ControlRecord> controlRecords;

            if (employeeId != "null" && employeeId != null)
            {
                IEnumerable<string> employeeIds = new string[] { employeeId };
                controlRecords = _controlRecordService.GetControlRecords(payrollNumber, employeeIds);
            }
            else if (controlTypes != null && controlTypesSplit[0] != "null")
            {
                controlRecords = _controlRecordService.GetControlRecordsByType(payrollNumber, controlTypesSplit, field);
            }
            else
            {
                controlRecords = _controlRecordService.GetControlRecords(payrollNumber);
            }

            var controlsAndVariances = _controlAndVarianceService.GetAll();

            List<ControlRecordDisplayTableDTO> controlRecordDisplayTableDTOs = new List<ControlRecordDisplayTableDTO>();

            foreach (var controlRecord in controlRecords)
            {
                var controlOrVariance = controlsAndVariances.FirstOrDefault(x => x.Name.ToString() == controlRecord.ControlType);

                ControlRecordDisplayTableDTO controlRecordDisplayTableDTO = new ControlRecordDisplayTableDTO
                {
                    ID = controlRecord.ID,
                    PayrollNumber = controlRecord.PayrollNumber,
                    EmployeeID = controlRecord.EmployeeID,
                    ControlName = controlRecord.ControlType,
                    Description = controlOrVariance.Description,
                    Field = FormatControlOrVarianceFieldValue(controlRecord.Field, controlRecord.ControlType),
                    CalculatedValue = FormatControlOrVarianceNumber(controlRecord.CalculatedValue, controlOrVariance.Unit),
                    Min = FormatControlOrVarianceNumber(controlRecord.Min, controlOrVariance.Unit),
                    Max = FormatControlOrVarianceNumber(controlRecord.Max, controlOrVariance.Unit)
                };

                controlRecordDisplayTableDTOs.Add(controlRecordDisplayTableDTO);
            }

            controlRecordDisplayTableDTOs = controlRecordDisplayTableDTOs
                .OrderByDescending(x => x.EmployeeID != null && x.EmployeeID != "")
                .ThenBy(x => x.EmployeeID)
                .ThenBy(x => x.Description).ToList();

            return JsonSuccess(data: new ControlsDataListDTO(controlRecordDisplayTableDTOs));
        }

        [HttpPost]
        public ActionResult GetControlAuditTableData(TableFilter filters)
        {
            try
            {
                var tableData = _controlRecordService.GetPayrollControlAuditTableData(filters);
                var controlsAndVariances = _controlAndVarianceService.GetAll();
                List<ControlRecordDisplayTableDTO> controlRecordDisplayTableDTOs = new List<ControlRecordDisplayTableDTO>();
                foreach (var controlRecord in tableData.Query)
                {
                    var controlOrVariance = controlsAndVariances.FirstOrDefault(cv => cv.Name.ToString() == controlRecord.ControlName);

                    controlRecordDisplayTableDTOs.Add(new ControlRecordDisplayTableDTO()
                    {
                        ID = controlRecord.ID,
                        PayrollNumber = controlRecord.PayrollNumber,
                        EmployeeID = controlRecord.EmployeeID,
                        ControlName = controlRecord.ControlName,
                        Description = controlOrVariance.Description,
                        Field = FormatControlOrVarianceFieldValue(controlRecord.Field, controlRecord.ControlName),
                        CalculatedValue = FormatControlOrVarianceNumber(decimal.Parse(controlRecord.CalculatedValue), controlOrVariance.Unit),
                        Min = FormatControlOrVarianceNumber(decimal.Parse(controlRecord.Min), controlOrVariance.Unit),
                        Max = FormatControlOrVarianceNumber(decimal.Parse(controlRecord.Max), controlOrVariance.Unit),
                        AcknowledgeDate = controlRecord.AcknowledgeDate,
                        AcknowledgeUserId = controlRecord.AcknowledgeUserId,
                    });
                }
                controlRecordDisplayTableDTOs = controlRecordDisplayTableDTOs
                    .OrderByDescending(x => x.AcknowledgeDate)
                    .ThenBy(x => x.EmployeeID)
                    .ThenBy(x => x.Description)
                    .Skip(filters.EntriesToSkip)
                    .Take(filters.PerPage)
                    .ToList();

                return JsonSuccess(data: new TableFilterData<ControlRecordDisplayTableDTO>()
                {
                    Data = controlRecordDisplayTableDTOs,
                    FilteredEntries = tableData.FilteredEntries,
                    TotalEntries = tableData.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }

        }

        [HttpPost]
        public JsonResult AcknowledgeAllControls(string payrollNumber, string employeeID = null)
        {
            try
            {
                var oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                _controlRecordService.AcknowledgeAll(oPayrollNumber, null, employeeID);
                return JsonSuccess();
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult AcknowledgeAllVariances(string payrollNumber, string employeeID = null)
        {
            try
            {
                var oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                _varianceRecordService.AcknowledgeAll(oPayrollNumber, null, employeeID);
                return JsonSuccess();
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult AcknowledgeControl(int id)
        {
            try
            {
                _controlRecordService.AcknowledgeControlRecordById(id);
            }
            catch (Exception ex)
            {
                return JsonError();
            }

            return JsonSuccess();
        }

        [HttpGet]
        public JsonResult Variances(string payrollNumber, string employeeId, string varianceTypes, string field)
        {
            List<string> varianceTypesSplit = new List<string>();
            if (varianceTypes != null)
            {
                varianceTypesSplit = varianceTypes.Split(',').ToList();
            }

            if (field == "null")
            {
                field = null;
            }

            List<VarianceRecord> varianceRecords;

            if (employeeId != "null" && employeeId != null)
            {
                IEnumerable<string> employeeIds = new string[] { employeeId };
                varianceRecords = _varianceRecordService.GetVarianceRecords(payrollNumber, employeeIds);
            }
            else if (varianceTypes != null && varianceTypesSplit[0] != "null")
            {
                varianceRecords = _varianceRecordService.GetVarianceRecordsByType(payrollNumber, varianceTypesSplit, field);
            }
            else
            {
                varianceRecords = _varianceRecordService.GetVarianceRecords(payrollNumber);
            }

            var controlsAndVariances = _controlAndVarianceService.GetAll();

            List<VarianceRecordDisplayTableDTO> varianceRecordDisplayTableDTOs = new List<VarianceRecordDisplayTableDTO>();

            foreach (var varianceRecord in varianceRecords)
            {
                var controlOrVariance = controlsAndVariances.FirstOrDefault(x => x.Name.ToString() == varianceRecord.VarianceType);

                VarianceRecordDisplayTableDTO varianceRecordDisplayTableDTO = new VarianceRecordDisplayTableDTO
                {
                    ID = varianceRecord.ID,
                    PayrollNumber = varianceRecord.PayrollNumber,
                    EmployeeID = varianceRecord.EmployeeID,
                    VarianceName = varianceRecord.VarianceType,
                    Description = controlOrVariance.Description,
                    Field = FormatControlOrVarianceFieldValue(varianceRecord.Field, varianceRecord.VarianceType),
                    CalculatedVariance = FormatControlOrVarianceNumber(varianceRecord.CalculatedVariance, "%"),
                    VarianceAmount = FormatControlOrVarianceNumber(varianceRecord.VarianceAmount, controlOrVariance.Unit),
                    TotalVariance = FormatControlOrVarianceNumber(varianceRecord.TotalVariance, controlOrVariance.Unit)
                };

                varianceRecordDisplayTableDTOs.Add(varianceRecordDisplayTableDTO);
            }

            varianceRecordDisplayTableDTOs = varianceRecordDisplayTableDTOs
                .OrderByDescending(x => x.EmployeeID != null && x.EmployeeID != "")
                .ThenBy(x => x.EmployeeID)
                .ThenBy(x => x.Description).ToList();

            return JsonSuccess(data: new VariancesDataListDTO(varianceRecordDisplayTableDTOs));
        }

        [HttpPost]
        public ActionResult GetVarianceAuditTableData(TableFilter filters)
        {
            try
            {
                var tableData = _varianceRecordService.GetPayrollVarianceAuditTableData(filters);
                var controlsAndVariances = _controlAndVarianceService.GetAll();
                List<VarianceRecordDisplayTableDTO> varianceRecordDisplayTableDTOs = new List<VarianceRecordDisplayTableDTO>();
                foreach (var varianceRecord in tableData.Query)
                {
                    var controlOrVariance = controlsAndVariances.FirstOrDefault(cv => cv.Name.ToString() == varianceRecord.VarianceName);

                    varianceRecordDisplayTableDTOs.Add(new VarianceRecordDisplayTableDTO()
                    {
                        ID = varianceRecord.ID,
                        PayrollNumber = varianceRecord.PayrollNumber,
                        EmployeeID = varianceRecord.EmployeeID,
                        VarianceName = varianceRecord.VarianceName,
                        Description = controlOrVariance.Description,
                        Field = FormatControlOrVarianceFieldValue(varianceRecord.Field, varianceRecord.VarianceName),
                        CalculatedVariance = FormatControlOrVarianceNumber(decimal.Parse(varianceRecord.CalculatedVariance), controlOrVariance.Unit),
                        VarianceAmount = FormatControlOrVarianceNumber(decimal.Parse(varianceRecord.VarianceAmount), controlOrVariance.Unit),
                        TotalVariance = FormatControlOrVarianceNumber(decimal.Parse(varianceRecord.TotalVariance), controlOrVariance.Unit),
                        AcknowledgeDate = varianceRecord.AcknowledgeDate,
                        AcknowledgeUserId = varianceRecord.AcknowledgeUserId,
                    });
                }
                varianceRecordDisplayTableDTOs = varianceRecordDisplayTableDTOs
                    .OrderByDescending(x => x.AcknowledgeDate)
                    .ThenBy(x => x.EmployeeID)
                    .ThenBy(x => x.Description)
                    .Skip(filters.EntriesToSkip)
                    .Take(filters.PerPage)
                    .ToList();

                return JsonSuccess(data: new TableFilterData<VarianceRecordDisplayTableDTO>()
                {
                    Data = varianceRecordDisplayTableDTOs,
                    FilteredEntries = tableData.FilteredEntries,
                    TotalEntries = tableData.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }

        }

        [HttpPost]
        public JsonResult AcknowledgeVariance(int id)
        {
            try
            {
                _varianceRecordService.AcknowledgeVarianceRecordById(id);
            }
            catch (Exception ex)
            {
                return JsonError();
            }

            return JsonSuccess();
        }

        [HttpPost]
        public JsonResult PayrollRecalculationStatus(string payrollNumber)
        {
            try
            {
                bool requiresRecalculation = _payrollEditService.PayrollHasUnprocessedEdits(payrollNumber);
                return JsonSuccess("", new { RequiresRecalculation = requiresRecalculation });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public ActionResult GetPayrollEditTableData (TableFilter filters)
        {
            try
            {
                var tableInfo = _payrollEditService.GetPayrollEditHistoryTableData(filters);
                tableInfo.Query = tableInfo.Query.Skip(filters.EntriesToSkip).Take(filters.PerPage);
                var data = MapTableData(tableInfo.Query.ToList());

                return JsonSuccess(data: new TableFilterData<PayrollEditHistoryTableRow>
                {
                    Data = data,
                    FilteredEntries = tableInfo.FilteredEntries,
                    TotalEntries = tableInfo.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        #region Private Functions

        private string FormatControlOrVarianceFieldValue(string value, string cvType)
        {
            List<string> perPayTypeCVTypes = new List<string>() { "1023", "1024", "2002", "2039" };
            if (perPayTypeCVTypes.Contains(cvType))
            {
                if (Int32.TryParse(value, out int int_val))
                {
                    return Enum.GetName(typeof(enPayTypes), int_val);
                }
            }
            return value;
        }

        private List<PayrollEditHistoryTableRow> MapTableData (List<PayrollEditHistoryTableRow> tableData)
        {
            var tableRows = new List<PayrollEditHistoryTableRow>();
            foreach (var row in tableData)
            {
                tableRows.Add(new PayrollEditHistoryTableRow()
                {
                    EmployeeID = row.EmployeeID,
                    EmployeeName = row.EmployeeName,
                    CodeType = ConvertCodeType(row),
                    Code = ParseCode(row.Code, row.CodeType),
                    ActionType = row.ActionType,
                    OldValue = row.OldValue,
                    NewValue = row.NewValue,
                    UserID = row.UserID,
                    EditDate = row.EditDate,
                    FieldName = row.FieldName,
                });
            }
            return tableRows;
        }

        private string ConvertCodeType (PayrollEditHistoryTableRow row)
        {
            switch (row.CodeType)
            {
                case "PayrollWorkPayCodes":
                    return "Pay Code";
                case "PayrollWorkDeductions":
                    return "Deduction";
                case "PayrollWorkBenefits":
                    return "Benfit";
                case "PayrollWorkPTO":
                    return "PTO";
                case "PayrollWorkHeaders":
                case "PayrollWorkStateTaxes":
                case "PayrollWorkLocalTaxes":
                    if (row.FieldName == "FederalWithholding")
                        return "Federal W/H";
                    else if (row.FieldName == "TotalStateTax")
                        return "State Tax";
                    else if (row.FieldName == "TotalLocalTax")
                        return "Local Tax";
                    else
                        return "";
                default:
                    return "";
            }
        }

        private string ParseCode (string keyInfo, string codeType)
        {
            var keyInfoArray = keyInfo.Split(',');
            Dictionary<string, string> keyValuePairs = new Dictionary<string, string>();
            foreach (string item in keyInfoArray)
            {
                var keyValue = item.Split('=');
                keyValuePairs.Add(keyValue[0], keyValue[1]);
            }
            string code;
            switch (codeType)
            {
                case "PayrollWorkPayCodes":
                    keyValuePairs.TryGetValue("PayRecord", out code);
                    break;
                case "PayrollWorkDeductions":
                    keyValuePairs.TryGetValue("Deduction", out code);
                    break;
                case "PayrollWorkBenefits":
                    keyValuePairs.TryGetValue("Benefit", out code);
                    break;
                case "PayrollWorkPTO":
                    code = "PTO";
                    break;
                case "PayrollWorkHeaders":
                    code = "";
                    break;
                case "PayrollWorkStateTaxes":
                    keyValuePairs.TryGetValue("StateCode", out code);
                    break;
                case "PayrollWorkLocalTaxes":
                    keyValuePairs.TryGetValue("LocalTax", out code);
                    break;
                default:
                    code = "";
                    break;
            }
            return code;
        }

        private void UpdatePayrollStatus(string payrollNumber)
        {
            PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
            CohesionProcessOptions processOptions = _payrollProfileSettingService.GetProcessOptions(oPayrollNumber);

            if (processOptions.HasPayrollControlsVariances())
            {
                if (processOptions.AutoControlsAndVariances)
                {
                    ControlsAndVariances(payrollNumber);
                    return;
                }

                new UpdatePayrollStatusHandler(_dbContext)
                    .Handle(PayrollNumber.Parse(payrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.PayrollControlsAndVariancesNeedsProcessed));
                return;
            }

            if (processOptions.ApprovePayroll)
            {
                if (processOptions.AutoApprovalsPayroll)
                {
                    new SubmitPayrollApprovalRequestHandler(_dbContext)
                        .Handle(new SubmitPayrollApprovalRequest(payrollNumber));
                    return;
                }
                new UpdatePayrollStatusHandler(_dbContext)
                    .Handle(PayrollNumber.Parse(payrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.PayrollApprovalsRequired));
                return;
            }

            new UpdatePayrollStatusHandler(_dbContext)
                    .Handle(PayrollNumber.Parse(payrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.PayrollCalculated));
        }

        #endregion
    }
}