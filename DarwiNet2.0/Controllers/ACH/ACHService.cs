using Castle.Core.Internal;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Controllers.ACH
{
    public class ACHService
    {
        private readonly DnetEntities _dbContext;
        private readonly List<ACHMessage> _aCHMessages = new List<ACHMessage>();
        private string _aCHFile = "";

        #region entities object placeholders

        List<ACHManualDepositRecord> tWACHManualDepositRecordTEMPEntities = new List<ACHManualDepositRecord>();
        List<TW_ACH_Header_HIST> tWACHHeaderHistEntities = new List<TW_ACH_Header_HIST>();

        #endregion

        #region (L)variables

        long aCHEntryHash;
        long aCHFEntryHash;
        object ItemsMarked = new object();
        bool useClientInfo = true;
        bool debitsFirst = false;
        string controlProfileID = string.Empty;
        bool tWReverseVoidsCB = false;
        int aCHEntryCount = 0;
        int aCHRecCount = 0;
        int aCHFEntryCount = 0;
        int aCHFRecCount = 0;
        string startExportID;
        string endExportID;
        bool createSeparateDepositBatches = false;
        bool excludeInvoiceNumber = false;
        int accountType = 1;
        string transitNumber = string.Empty;
        string bankAccountNumber = string.Empty;
        string employeeID = string.Empty;
        string employeeName = string.Empty;

        const char CH_SPACE = ' ';
        const char CH_NINE = '9';
        const char CH_ZERO = '0';
        const string msg735 = "PAYROLL";
        const string msg22131 = "Please define a Company Payroll Profile ID (Microsoft Dynamics GP/Setup/Company/Company/ACH Setup) before creating ACH."; // TODO this path should change
        const string msg22132 = "Please define an ACH Profile Maintenance (Microsoft Dynamics GP/Setup/System/ACH Maintenance menu) before creating ACH."; // TODO this path should change
        const string msg22133 = "Records have not been marked for creating ACH file."; // TODO this should probably be validated on the front end
        const string msg22134 = "Items in this ACH batch include checkbooks from more than one bank. To continue, select \"Allow Multiple Banks in Batch\" before generation.";
        const int TW_ASO = 1;
        const int TW_PEO = 2;
        const string ACH = "ACH";
        const string DOT = ".";

        #endregion

        public ACHService(DnetEntities dbContext)
        {
            _dbContext = dbContext;
        }

        public bool Create_ACH_File(List<ClientACHHeader> aCHHeaders, List<ACHManualDepositRecord> TW_ACH_Manual_Deposit_Record_TEMP, string IN_profile, bool i_pay, ACHBatchDTO achBatchInfo, out List<ACHMessage> aCHMessages)
        {
            aCHMessages = _aCHMessages;

            bool l_multi_trx, l_prt_offset;
            bool l_reverseBatchFlag = achBatchInfo.ReverseACHBatch;
            excludeInvoiceNumber = achBatchInfo.ExcludeInvoiceNumberFromACHFile;
            bool l_control_set = false;
            long TempBatch, m;
            long ACHFTotalCredits = default;
            long ACHTotalCredits = default;
            long ACHFTotalDebits = default;
            long ACHTotalDebits = default;
            string nACH, l_last_ACC, l_last_cb_ID, l_next_trans_num, l_EntryHash,
                 l_firstBank, l_new_ACH, l_payroll_key, l_key;
            string l_prev_cb = null;
            string l_prev_ACH_num = null;
            string l_recv_key = null;
            string l_cb = null;
            string l_prev_client = null;
            string l_prev_acc = null;
            string l_orig_ID = null;
            string l_trans_type = null;
            string l_PEO_orig_id = null;
            string l_cb_first = null;
            int n9BlockCount, n9BatchCount, l_source, l_length;
            int l_client_type = 0;
            int l_actual = 0;
            int l_selected = 0;
            decimal n, l_manual_amt;
            decimal l_post_amt = default;
            DateTime l_check_date = default;
            long PNCBatch;
            int l_batchtype;
            string l_ServiceClassCode = "8200";

            aCHEntryHash = 0;
            aCHFEntryHash = 0;

            List<ACHFileReport> tW_ACH_File_Report_Temps = new List<ACHFileReport>();

            ACHSetup aCHSetup = _dbContext.ACHSetups.FirstOrDefault();
            if (aCHSetup == null)
            {
                _aCHMessages.Add(new ACHMessage(msg22131, true));
                return default;
            }
            else
            {
                l_PEO_orig_id = aCHSetup.OriginationID;
            }

            if (ItemsMarked.IsNull())
            {
                _aCHMessages.Add(new ACHMessage(msg22133, true));
                return default;
            }

            ACHProfile aCHProfile = _dbContext.ACHProfiles.FirstOrDefault();
            if (aCHProfile == null)
            {
                _aCHMessages.Add(new ACHMessage(msg22132, true));
                return default;
            }

            l_payroll_key = aCHProfile.PayrollKey;
            if (useClientInfo)
            {
                l_recv_key = aCHProfile.ReceivableKey;
            }

            PNCBatch = 0;
            if (aCHProfile.PNCFormat)
            {
                TempBatch = PNCBatch;
            }
            else
            {
                TempBatch = aCHProfile.NextBatchID - 1;
            }

            l_batchtype = Check_BatchType(aCHHeaders);

            IEnumerable<ClientACHHeader> clientACHHeaders = aCHHeaders.Where(x => x.SelectedToPrint);

            if (clientACHHeaders.Count() == 0)
            {
                _aCHMessages.Add(new ACHMessage("No ACH Headers marked Selected To Print", true));
                return default;
            }

            if (debitsFirst)
            {
                clientACHHeaders = clientACHHeaders.Reverse();
            }

            TW_Get_ACH_Profile(clientACHHeaders.First().TransactionNumber, out string l_payroll_id, out string l_recv_ID, clientACHHeaders.First().ClientID);

            l_source = clientACHHeaders.First().ACHType.GetValueOrDefault();

            l_source = default;
            l_payroll_id = default;
            l_recv_ID = default;

            IEnumerator<ClientACHHeader> clientACHHeaderEnumerator = clientACHHeaders.GetEnumerator();
            ClientACHHeader clientACHHeader;
            while (clientACHHeaderEnumerator.MoveNext())
            {
                clientACHHeader = clientACHHeaderEnumerator.Current;
                l_selected++;

                TW_Get_ACH_Profile(clientACHHeaders.First().TransactionNumber, out l_payroll_id, out l_recv_ID, clientACHHeaders.First().ClientID);

                // re-validate profile ids
                if (!l_control_set)
                {
                    if (clientACHHeader.ACHType == ((int)ACHTypes.Payroll))
                    {
                        controlProfileID = l_payroll_id;
                    }
                    else if (clientACHHeader.ACHType == ((int)ACHTypes.Invoice))
                    {
                        controlProfileID = l_recv_ID;
                    }

                    l_control_set = true;
                }

                if (clientACHHeader.ACHType == ((int)ACHTypes.Payroll))
                {
                    if (controlProfileID != l_payroll_id)
                    {
                        TW_Save_ACH_File_Report_Info(tW_ACH_File_Report_Temps, clientACHHeader, l_payroll_id, TempBatch, true, false);
                        clientACHHeader.SelectedToPrint = false;
                        _dbContext.SaveChanges();
                    }
                    else
                    {
                        l_actual++;
                        TW_Save_ACH_File_Report_Info(tW_ACH_File_Report_Temps, clientACHHeader, l_payroll_id, TempBatch, true, true);
                    }
                }
                else if (clientACHHeader.ACHType == ((int)ACHTypes.Invoice))
                {
                    if (controlProfileID != l_recv_ID)
                    {
                        TW_Save_ACH_File_Report_Info(tW_ACH_File_Report_Temps, clientACHHeader, l_recv_ID, TempBatch, true, false);

                        clientACHHeader.SelectedToPrint = false;
                        _dbContext.SaveChanges();
                    }
                    else
                    {
                        l_actual++;
                        TW_Save_ACH_File_Report_Info(tW_ACH_File_Report_Temps, clientACHHeader, l_recv_ID, TempBatch, true, true);
                    }
                }

                if (l_cb_first.IsNullOrEmpty() && !clientACHHeader.CheckBookID.IsNullOrEmpty())
                {
                    l_cb_first = clientACHHeader.CheckBookID;
                }

                if (!l_cb_first.IsNullOrEmpty() && !clientACHHeader.CheckBookID.IsNullOrEmpty() && clientACHHeader.CheckBookID != l_cb_first)
                {
                    Checkbook checkbook = _dbContext.Checkbooks.Where(x => x.CheckbookID == l_cb_first).First();

                    l_firstBank = checkbook.BankID;

                    checkbook = _dbContext.Checkbooks.Where(x => x.CheckbookID == clientACHHeader.CheckBookID).First();

                    if (!achBatchInfo.AllowMultipleBanksInBatch && !checkbook.BankID.IsNullOrEmpty() && checkbook.CheckbookID != l_firstBank)
                    {
                        _aCHMessages.Add(new ACHMessage(msg22134, true));
                        return default;
                    }
                }
            }

            {
                // Brackets only for scope
                clientACHHeader = clientACHHeaders.First();

                l_last_ACC = clientACHHeader.TransactionNumber;
                l_last_cb_ID = clientACHHeader.CheckBookID;
                l_length = pos(aCHProfile.ACHFileName, DOT, 1) - 1;
                l_next_trans_num = substring(aCHProfile.ACHFileName, 1, l_length);
            }

            clientACHHeaderEnumerator = clientACHHeaders.GetEnumerator();
            while (clientACHHeaderEnumerator.MoveNext())
            {
                clientACHHeader = clientACHHeaderEnumerator.Current;
                l_prt_offset = false;
                l_multi_trx = false;
                l_manual_amt = 0;

                if (TW_Client_Type(clientACHHeader.ClientID) == TW_ASO)
                {
                    l_client_type = 1; // ASO
                }
                else if (TW_Client_Type(clientACHHeader.ClientID) == TW_PEO)
                {
                    l_client_type = 2; // PEO
                }

                Client client = _dbContext.Clients.Where(x => x.ClientID == clientACHHeader.ClientID).FirstOrDefault();

                if (client == null)
                {
                    if (l_client_type == 1)
                    {
                        _aCHMessages.Add(new ACHMessage("Please define a Client Payroll Profile ID (Cards/Sales/Client/ACH Setup) for Client: " + clientACHHeader.ClientID + " before creating ACH.", true));
                        return default;
                    }
                    else if (l_client_type == 2)
                    {
                        l_orig_ID = l_PEO_orig_id;
                    }
                }
                else
                {
                    l_orig_ID = client.OriginationID;
                }

                switch (clientACHHeader.ACHType)
                {
                    case 1: // Payroll
                        l_trans_type = "P";
                        Create_ACH_File_Payroll(l_orig_ID, ref l_prev_acc, ref l_prev_client, clientACHHeader, aCHProfile, aCHSetup,
                            client, ref l_check_date, ref l_cb, ref l_client_type, ref TempBatch, ref ACHTotalDebits, ref ACHFTotalDebits,
                            ref ACHTotalCredits, ref ACHFTotalCredits, ref l_post_amt, ref l_reverseBatchFlag, l_payroll_key, l_batchtype);
                        break;
                    case 2: // Invoice
                        l_trans_type = "I";
                        Create_ACH_File_Invoice(l_orig_ID, clientACHHeader,
                            aCHProfile, aCHSetup, client, ref l_cb, ref l_client_type,
                            ref TempBatch, ref ACHTotalDebits, ref ACHFTotalDebits, ref ACHTotalCredits, ref ACHFTotalCredits,
                            ref l_post_amt, ref l_reverseBatchFlag, l_recv_key, l_batchtype);
                        break;
                }

                if (tWReverseVoidsCB)
                {
                    if (!clientACHHeader.ACHReverseNumber.IsNullOrEmpty())
                    {
                        l_multi_trx = true;
                        l_prev_ACH_num = clientACHHeader.ACHReverseNumber;
                        l_prev_cb = clientACHHeader.CheckBookID;
                    }
                    else
                    {
                        l_multi_trx = false;
                    }
                    clientACHHeader.ACHReverseNumber = l_next_trans_num;
                    l_new_ACH = l_next_trans_num;
                }
                else
                {
                    if (!clientACHHeader.ACHExportID.IsNullOrEmpty())
                    {
                        l_multi_trx = true;
                        l_prev_ACH_num = clientACHHeader.ACHExportID;
                        l_prev_cb = clientACHHeader.CheckBookID;
                    }
                    else
                    {
                        l_multi_trx = false;
                    }

                    //clientACHHeader.ACHExportID = l_next_trans_num;
                    l_new_ACH = l_next_trans_num;
                }

                if (aCHFEntryCount > 0)
                {
                    Set_Transfered_Info(clientACHHeader, false, false, "", TempBatch, l_reverseBatchFlag);
                    Update_Main_Table(clientACHHeader);
                }

                l_source = clientACHHeader.ACHType.GetValueOrDefault();
                l_prev_acc = clientACHHeader.TransactionNumber;
                l_prev_client = clientACHHeader.ClientID;

                bool enumeratorEmpty = true;
                if (clientACHHeaderEnumerator.MoveNext())
                {
                    // Out of natural sequence enumeration.
                    // enumerator already reversed if necessary.
                    clientACHHeader = clientACHHeaderEnumerator.Current;
                    enumeratorEmpty = false;
                }

                if (l_last_cb_ID != clientACHHeader.CheckBookID || enumeratorEmpty)
                {
                    if (!l_last_cb_ID.IsNullOrEmpty())
                    {
                        if (l_check_date.IsNull())
                        {
                            l_check_date = DateTime.Now.Date;
                        }
                        CM_Post_DD(l_last_cb_ID, l_next_trans_num, l_post_amt, l_check_date);
                    }
                    if (!clientACHHeader.CheckBookID.IsNullOrEmpty() && !enumeratorEmpty)
                    {
                        l_next_trans_num = Next_Transferred_ID(aCHProfile);
                    }

                    if (l_multi_trx)
                    {
                        TW_Handle_Multiple_Transactions(ACHTotalCredits, ACHTotalDebits, l_trans_type, l_prev_ACH_num, l_prev_cb, l_new_ACH);
                    }

                    l_post_amt = 0;
                    l_check_date = default;
                    l_last_cb_ID = clientACHHeader.CheckBookID;

                    startExportID = l_next_trans_num;
                    endExportID = l_next_trans_num;
                }

                ACHManualDepositRecord tW_ACH_Manual_Deposit_Record_TEMP = null;
                if (l_last_ACC != clientACHHeader.TransactionNumber || enumeratorEmpty || l_prev_client != clientACHHeader.ClientID)
                {
                    tW_ACH_Manual_Deposit_Record_TEMP = tWACHManualDepositRecordTEMPEntities.Where(x => x.TW_Source == l_source && x.TW_Transaction_Number == l_prev_acc && x.CustomerNumber == l_prev_client).FirstOrDefault();

                    if (tW_ACH_Manual_Deposit_Record_TEMP != null)
                    {
                        l_prt_offset = true;
                        l_manual_amt = tW_ACH_Manual_Deposit_Record_TEMP.DepositAmount;
                        if (i_pay && l_source != 2)
                        {
                            Create_ACH_File_Payroll_Manual(aCHSetup, l_manual_amt, ref ACHTotalCredits, ref ACHFTotalCredits, ref ACHTotalDebits, ref ACHFTotalDebits);
                        }
                    }
                }

                if (l_last_ACC != clientACHHeader.TransactionNumber || enumeratorEmpty || l_prev_client != clientACHHeader.ClientID)
                {
                    if (aCHEntryCount > 0)
                    {
                        Create_ACH_File2(l_prev_client, l_prev_acc, l_client_type, l_recv_key, i_pay, l_source, 0, l_last_ACC, l_prt_offset,
                            l_manual_amt, l_cb, ref ACHTotalCredits, ref ACHTotalDebits, ref ACHFTotalCredits, ref ACHFTotalDebits, l_payroll_key,
                            aCHSetup, clientACHHeader, tW_ACH_Manual_Deposit_Record_TEMP, aCHProfile, client);

                        l_EntryHash = format(aCHEntryHash);
                        if (l_EntryHash.Length > 10)
                        {
                            l_EntryHash = substring(l_EntryHash, length(l_EntryHash) - 9, 10);
                        }
                        else
                        {
                            l_EntryHash = pad(l_EntryHash, PadFormat.LEADING, CH_ZERO, 10);
                        }
                        if (l_source == 1)
                        {
                            l_key = l_payroll_key;
                        }
                        else
                        {
                            l_key = l_recv_key;
                        }

                        l_ServiceClassCode = "";
                        if (l_batchtype == 1) // Payroll
                        {
                            if (aCHSetup.PayrollOffsetRecord)
                            {
                                // Balanced files
                                l_ServiceClassCode = "8200";
                            }
                            else
                            {
                                // Credits only
                                l_ServiceClassCode = "8220";
                            }
                        }
                        else if (l_batchtype == 2)
                        {
                            InvoiceDirectDeposit invoiceDirectDeposit = _dbContext.InvoiceDirectDeposits.Where(x => x.ClientID == l_prev_client && x.TransactionNumber == l_prev_acc).First();

                            if (TW_Receivables_Offset_Record(invoiceDirectDeposit.ClientID, invoiceDirectDeposit.DivisionID))
                            {
                                // Balanced files
                                l_ServiceClassCode = "8200";
                            }
                            else
                            {
                                // Debits only
                                l_ServiceClassCode = "8225";
                            }
                        }
                        else if (l_batchtype == 3) // Mixed Debits and Credits
                        {
                            l_ServiceClassCode = "8200";
                        }

                        if (l_client_type == 1 /*ASO*/ && l_source == 1 /*Payroll*/ || (l_source == 2 && useClientInfo))
                        {
                            if (l_key.IsNullOrEmpty())
                            {
                                nACH = upper(l_ServiceClassCode/*"8200"*/ + pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 6)
                                    + l_EntryHash +
                                    pad(str(ACHTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(ACHTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                                    pad(client.ACHCompanyID, PadFormat.TRAILING, CH_SPACE, 35) +
                                    pad(client.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                                    pad(str(TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                            }
                            else
                            {
                                // replace first character of company ID with key from ACH profile
                                nACH = upper(l_ServiceClassCode/*"8200"*/ + pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 6)
                                    + l_EntryHash +
                                    pad(str(ACHTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(ACHTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                                    pad(l_key + substring(client.ACHCompanyID, 2, 9), PadFormat.TRAILING, CH_SPACE, 35) +
                                    pad(client.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                                    pad(str(TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                            }
                            TextFile_WriteLine(nACH);
                            aCHFRecCount++;
                        }
                        else
                        {
                            if (l_client_type == 2 /*PEO*/ && UseClientLevelSetup(clientACHHeader.ClientID) && (l_source == 1 || (l_source == 2 && useClientInfo)))
                            {
                                if (l_key.IsNullOrEmpty())
                                {
                                    nACH = upper(l_ServiceClassCode/*"8200"*/ + pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 6)
                                        + l_EntryHash +
                                        pad(str(ACHTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(ACHTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                                        pad(client.ACHCompanyID, PadFormat.TRAILING, CH_SPACE, 35) +
                                        pad(client.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                                        pad(str(TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                                }
                                else
                                {
                                    nACH = upper(l_ServiceClassCode/*"8200"*/ + pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 6)
                                        + l_EntryHash +
                                        pad(str(ACHTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(ACHTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                                        pad(l_key + substring(client.ACHCompanyID, 2, 9), PadFormat.TRAILING, CH_SPACE, 35) +
                                        pad(client.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                                        pad(str(TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                                }

                                TextFile_WriteLine(nACH);
                                aCHFRecCount++;
                            }
                            else
                            {
                                TW_ACH_File_4th_Line(aCHSetup, l_client_type, l_source, l_EntryHash, ACHTotalDebits, ACHTotalCredits, TempBatch, l_orig_ID, l_ServiceClassCode);
                            }
                        }

                        aCHEntryCount = 0;
                        aCHEntryHash = 0;
                        l_EntryHash = "";
                        ACHTotalDebits = 0;
                        ACHTotalCredits = 0;
                        l_source = 0;
                        l_cb = "";
                        l_client_type = 0;
                    }
                }
                l_last_ACC = clientACHHeader.TransactionNumber;
            }

            // All Entries File Control Record - n9.
            if (aCHFEntryCount > 0)
            {
                aCHFEntryCount++;
                m = value(format(truncate(Convert.ToDouble(aCHFRecCount) / 10)));
                n = truncate(aCHFRecCount) / 10;
                if (n - m > 0)
                {
                    n9BlockCount = Convert.ToInt32(m + 1);
                }
                else
                {
                    n9BlockCount = Convert.ToInt32(m);
                }

                if (aCHProfile.PNCFormat)
                {
                    n9BatchCount = Convert.ToInt32(TempBatch);
                }
                else
                {
                    n9BatchCount = Convert.ToInt32(TempBatch - aCHProfile.NextBatchID + 1);
                    if (n9BatchCount > 0)
                    {
                        aCHProfile.NextBatchID = Convert.ToInt32(TempBatch + 1);
                        _dbContext.SaveChanges();
                    }
                }

                l_EntryHash = format(aCHFEntryHash);
                if (l_EntryHash.Length > 10)
                {
                    l_EntryHash = substring(l_EntryHash, length(l_EntryHash) - 9, 10);
                }
                else
                {
                    l_EntryHash = pad(l_EntryHash, PadFormat.LEADING, CH_ZERO, 10);
                }

                nACH = upper("9" + pad(str(n9BatchCount), PadFormat.LEADING, CH_ZERO, 6) + pad(str(n9BlockCount), PadFormat.LEADING, CH_ZERO, 6) +
                    pad(str(aCHFEntryCount), PadFormat.LEADING, CH_ZERO, 8) +
                    l_EntryHash +
                    pad(str(ACHFTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(ACHFTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                    pad("", PadFormat.TRAILING, CH_SPACE, 39));
                TextFile_WriteLine(nACH);
                for (int i = 1; i < n9BlockCount * 10 - aCHFRecCount; i++)
                {
                    nACH = pad("9", PadFormat.TRAILING, CH_NINE, 94);
                    TextFile_WriteLine(nACH);
                }

                if (aCHProfile.GenerateWBDHdrTrailRecords)
                {
                    nACH = upper("\\\\\\" + pad(str(n9BlockCount * 10 + 2), PadFormat.LEADING, CH_ZERO, 7) + pad("", PadFormat.TRAILING, CH_SPACE, 81));
                    TextFile_WriteLine(nACH);
                }

                l_source = clientACHHeader.ACHType.GetValueOrDefault();
                l_source = 0;
                // TODO report
                // run report TW_ACH_File_Report legends Path_MakeNative(l_file_name), '(L) Control Profile ID', str(countrecords(file TW_ACH_File_Report_Temp)), str(l_actual);
            }
            else
            {
                _aCHMessages.Add(new ACHMessage("No records have been written - no ACH file was created.", true));
                return default;
            }

            clientACHHeader.FileContent = _aCHFile;
            SetExportID(clientACHHeader, aCHProfile, out string error);
            if (!error.IsNullOrEmpty())
            {
                _aCHMessages.Add(new ACHMessage(error, true));
                return default;
            }
            _dbContext.SaveChanges();

            startExportID = "";

            return true;
        }

        #region main methods

        public void TW_Get_ACH_Profile(string IN_trans, out string OUT_payroll_id, out string OUT_receivables_id, string IN_client = "", string oiHistoricalPayrId = "", string oiHistoricalRecId = "")
        {
            bool l_clientLevelUsed, l_useHistory;
            string l_recvProfileID = "";

            OUT_receivables_id = "";
            l_clientLevelUsed = false;
            l_useHistory = false;

            if (!IN_client.IsNullOrEmpty())
            {
                if (!oiHistoricalPayrId.IsNullOrEmpty())
                {
                    if (!oiHistoricalRecId.IsNullOrEmpty())
                    {
                        l_recvProfileID = oiHistoricalRecId;
                        l_useHistory = true;
                    }
                }

                if (l_useHistory)
                {
                    l_recvProfileID = oiHistoricalRecId;
                }
                else
                {
                    InvoiceDirectDeposit invoiceDirectDeposit = _dbContext.InvoiceDirectDeposits.Where(x => x.ClientID == IN_client && x.TransactionNumber == IN_trans).FirstOrDefault();
                    if (invoiceDirectDeposit != null)
                    {
                        ClientDivision clientDivision = _dbContext.ClientDivisions.Where(x => x.ClientID == IN_client && x.DivisionID == invoiceDirectDeposit.DivisionID).FirstOrDefault();

                        if (!clientDivision.ReceivablesProfileID.IsNullOrEmpty())
                        {
                            l_recvProfileID = clientDivision.ReceivablesProfileID;
                        }
                    }
                    else
                    {
                        ClientDivision clientDivision = _dbContext.ClientDivisions.Where(x => x.Default == true && x.ClientID == IN_client).FirstOrDefault();

                        if (clientDivision != null && !clientDivision.ReceivablesProfileID.IsNullOrEmpty())
                        {
                            l_recvProfileID = clientDivision.ReceivablesProfileID;
                        }
                    }
                }
            }

            l_useHistory = false;
            if (!oiHistoricalPayrId.IsNullOrEmpty())
            {
                l_useHistory = true;
            }

            ACHSetup tW_Company_ACH_Setup = null;

            if (l_useHistory)
            {
                OUT_payroll_id = oiHistoricalPayrId;
            }
            else
            {
                Client client = _dbContext.Clients.Where(x => x.ClientID == IN_client).FirstOrDefault();

                if (client != null)
                {
                    l_clientLevelUsed = true;
                }

                if (l_clientLevelUsed)
                {
                    OUT_payroll_id = client.PayrollProfileID;
                }
                else
                {
                    tW_Company_ACH_Setup = _dbContext.ACHSetups.FirstOrDefault();

                    OUT_payroll_id = tW_Company_ACH_Setup.PayrollProfileID;
                }
            }

            if (l_recvProfileID.IsNullOrEmpty())
            {
                if (!l_clientLevelUsed)
                {
                    tW_Company_ACH_Setup = _dbContext.ACHSetups.FirstOrDefault();
                }

                if (tW_Company_ACH_Setup != null)
                {
                    l_recvProfileID = tW_Company_ACH_Setup.ReceivablesProfileID;
                }
            }

            OUT_receivables_id = l_recvProfileID;
        }

        public void TW_Save_ACH_File_Report_Info(List<ACHFileReport> TW_ACH_File_Report_Temps, ClientACHHeader clientACHHeader, string IN_profile, long IN_batch_id, bool IN_selected, bool IN_printed)
        {
            string l_comment;

            ACHFileReport tW_ACH_File_Report_Temp = new ACHFileReport()
            {
                CustomerNumber = clientACHHeader.ClientID,
                TW_Source = clientACHHeader.ACHType.GetValueOrDefault(),
                TW_Transaction_Number = clientACHHeader.TransactionNumber,
                TW_ACH_Export_Batch = IN_batch_id,
                Thinkware_Invoice_Number = TwGetTwInvoice(clientACHHeader.TransactionNumber),
                SelectedToPrint = IN_selected,
                Printed = IN_printed
            };

            if (IN_selected != IN_printed)
            {
                l_comment = "The profile associated with this item (" + IN_profile + ") does not match the profile used for this ACH file.";
                tW_ACH_File_Report_Temp.STR180 = l_comment;
            }
            else
            {
                tW_ACH_File_Report_Temp.STR180 = "";
            }

            TW_ACH_File_Report_Temps.Add(tW_ACH_File_Report_Temp);
        }

        public void Create_ACH_File_Payroll(string l_originationID, ref string io_prev_acc, ref string io_prev_client, ClientACHHeader clientACHHeader,
                                            ACHProfile aCHProfile, ACHSetup aCHSetup, Client client, ref DateTime l_check_date,
                                            ref string l_checkbook, ref int l_client_type, ref long TempBatch, ref long ACHTotalDebits, ref long ACHFTotalDebits,
                                            ref long ACHTotalCredits, ref long ACHFTotalCredits, ref decimal post_amount, ref bool l_reverseBatchFlag,
                                            string i_payroll_key, int in_batchtype)
        {
            string l_employee_name, n6_Individual;

            List<EmployeeDirectDepositHistory> employeeDirectDepositHistories = _dbContext.EmployeeDirectDepositHistories.Where(x => x.AuditControlCode == clientACHHeader.TransactionNumber && x.ClientID == clientACHHeader.ClientID).ToList();

            foreach (EmployeeDirectDepositHistory employeeDirectDepositHistory in employeeDirectDepositHistories)
            {
                if (employeeDirectDepositHistory.CheckbookID.IsNullOrEmpty() || employeeDirectDepositHistory.CheckbookID == clientACHHeader.CheckBookID)
                {
                    if (UseForACH(employeeDirectDepositHistory))
                    {
                        if ((employeeDirectDepositHistory.Voided || employeeDirectDepositHistory.ManualVoided) && tWReverseVoidsCB)
                        {
                            l_reverseBatchFlag = true;
                        }

                        EmployeeCheckHistory employeeCheckHistory = _dbContext.EmployeeCheckHistories.FirstOrDefault(x => x.AuditControlCode == employeeDirectDepositHistory.AuditControlCode && x.PaymentAdjustmentNumber == employeeDirectDepositHistory.PaymentAdjustmentNumber);

                        if (employeeCheckHistory != null && employeeCheckHistory.CheckDate > l_check_date)
                        {
                            l_check_date = employeeCheckHistory.CheckDate.GetValueOrDefault();
                        }

                        // only set checkbook id if TW_Actual_Deposit greater than 0 to prevent PreNotes from setting value
                        if (aCHEntryCount < 1 || l_checkbook.IsNullOrEmpty())
                        {
                            if (employeeDirectDepositHistory.ActualDeposit > 0)
                            {
                                l_checkbook = employeeDirectDepositHistory.CheckbookID;
                            }
                        }

                        if (aCHEntryCount < 1)// write ACH header records
                        {
                            ACH_File_Header(aCHProfile);
                        }

                        if (aCHEntryCount < 1 || (createSeparateDepositBatches && (io_prev_acc != clientACHHeader.TransactionNumber || io_prev_client != clientACHHeader.ClientID)))
                        {
                            io_prev_acc = clientACHHeader.TransactionNumber;
                            io_prev_client = clientACHHeader.ClientID;

                            if (TW_Client_Type(clientACHHeader.ClientID) == TW_ASO)
                            {
                                ACH_Batch_Header_Payroll(client, aCHProfile, employeeCheckHistory.CheckDate, aCHSetup.PayrollOffsetDays,
                                    employeeDirectDepositHistory.ClientID, ref TempBatch, l_client_type, i_payroll_key, in_batchtype, aCHSetup.PayrollOffsetRecord);
                            }
                            else
                            {
                                if (UseClientLevelSetup(clientACHHeader.ClientID))
                                {
                                    ACH_Batch_Header_Payroll(client, aCHProfile, employeeCheckHistory.CheckDate, aCHSetup.PayrollOffsetDays,
                                        employeeDirectDepositHistory.ClientID, ref TempBatch, l_client_type, i_payroll_key, in_batchtype, aCHSetup.PayrollOffsetRecord);
                                }
                                else
                                {
                                    ACH_Batch_Header_Payroll(aCHSetup, aCHProfile, employeeCheckHistory.CheckDate, aCHSetup.PayrollOffsetDays,
                                        employeeDirectDepositHistory.ClientID, ref TempBatch, l_client_type, i_payroll_key, in_batchtype, aCHSetup.PayrollOffsetRecord);
                                }
                            }
                        }

                        //Entry Detail Record.
                        l_employee_name = trim(TW_Employee_Name(employeeDirectDepositHistory.EmployeeID));

                        if (l_employee_name.Length > 22)
                        {
                            l_employee_name = substring(l_employee_name, 1, 22);
                        }

                        n6_Individual = pad(employeeDirectDepositHistory.EmployeeID, PadFormat.TRAILING, CH_SPACE, 15) +
                        pad(l_employee_name, PadFormat.TRAILING, CH_SPACE, 24) + "0" +
                        pad(l_originationID, PadFormat.TRAILING, CH_SPACE, 8);

                        if (tWReverseVoidsCB)
                        {
                            ACH_Entry_Detail(employeeDirectDepositHistory, employeeDirectDepositHistory.ActualDeposit, n6_Individual, 1, ref ACHTotalDebits, ref ACHFTotalDebits);
                        }
                        else
                        {
                            ACH_Entry_Detail(employeeDirectDepositHistory, employeeDirectDepositHistory.ActualDeposit, n6_Individual, 1, ref ACHTotalCredits, ref ACHFTotalCredits);
                        }

                        if (tWReverseVoidsCB)
                        {
                            // Only reverse void amount if it had already been posted in original ACH file, but has not been posted as a reverse trx yet
                            if (employeeDirectDepositHistory.ACHReverseBatch.IsNull() && !employeeDirectDepositHistory.ACHExportBatch.IsNull())
                            {
                                post_amount -= employeeDirectDepositHistory.ActualDeposit.GetValueOrDefault();
                            }
                        }
                        else
                        {
                            if (!employeeDirectDepositHistory.ACHExportBatch.IsNull())
                            {
                                post_amount += employeeDirectDepositHistory.ActualDeposit.GetValueOrDefault();
                            }
                        }

                        if (aCHProfile.AutoRemovePreNoteFlag)
                        {
                            Remove_PreNote_Flag(employeeDirectDepositHistory);
                        }
                    }

                    Set_Transfered_Info(employeeDirectDepositHistory, employeeDirectDepositHistory.Voided, employeeDirectDepositHistory.ManualVoided, "", TempBatch, l_reverseBatchFlag);
                }
            }
        }

        public void Create_ACH_File_Invoice(string l_originationID, ClientACHHeader clientACHHeader, ACHProfile TW_ACH_Profiles, ACHSetup aCHSetup,
                                                Client client, ref string l_checkbook, ref int l_client_type, ref long TempBatch,
                                                ref long ACHTotalDebits, ref long ACHFTotalDebits, ref long ACHTotalCredits, ref long ACHFTotalCredits,
                                                ref decimal post_amount, ref bool l_reverseBatchFlag, string i_recv_key, int in_batchtype)
        {
            string l_Entry_Description = "";
            string n6_Individual;

            InvoiceDirectDeposit invoiceDirectDeposit = _dbContext.InvoiceDirectDeposits.FirstOrDefault(x => x.ClientID == clientACHHeader.ClientID && x.TransactionNumber == clientACHHeader.TransactionNumber);

            if (invoiceDirectDeposit != null)
            {
                Set_Transfered_Info(invoiceDirectDeposit, invoiceDirectDeposit.Voided, invoiceDirectDeposit.ManualVoided, "", TempBatch, l_reverseBatchFlag);

                if (UseForInvoiceACH(invoiceDirectDeposit))
                {
                    if ((invoiceDirectDeposit.Voided || invoiceDirectDeposit.ManualVoided) && tWReverseVoidsCB)
                    {
                        l_reverseBatchFlag = true;
                    }

                    if (aCHEntryCount < 1)// write ACH header records
                    {
                        ACH_File_Header(TW_ACH_Profiles);

                        if (excludeInvoiceNumber)
                        {
                            l_Entry_Description = pad(invoiceDirectDeposit.ClientID, PadFormat.TRAILING, CH_SPACE, 15);
                        }
                        else
                        {
                            if (TW_ACH_Profiles.ChaseBank)
                            {
                                l_Entry_Description = invoiceDirectDeposit.ClientID + "0" + trim(str(invoiceDirectDeposit.DarwinInvoiceNumber));
                            }
                            else
                            {
                                l_Entry_Description = invoiceDirectDeposit.ClientID + "-" + trim(str(invoiceDirectDeposit.DarwinInvoiceNumber));
                            }
                        }

                        if (l_client_type == TW_ASO && useClientInfo)
                        {
                            ACH_Batch_Header_Invoice(client, TW_ACH_Profiles, invoiceDirectDeposit.ClientID,
                            trim(str(invoiceDirectDeposit.DarwinInvoiceNumber)), invoiceDirectDeposit.DebitDate, out l_checkbook,
                                        l_client_type, ref TempBatch, i_recv_key, in_batchtype);
                        }
                        else // PEO
                        {
                            if (UseClientLevelSetup(clientACHHeader.ClientID) && useClientInfo)
                            {
                                ACH_Batch_Header_Invoice(client, TW_ACH_Profiles, invoiceDirectDeposit.ClientID,
                                trim(str(invoiceDirectDeposit.DarwinInvoiceNumber)), invoiceDirectDeposit.DebitDate, out l_checkbook,
                                        l_client_type, ref TempBatch, i_recv_key, in_batchtype);
                            }
                            else
                            {
                                ACH_Batch_Header_Invoice(aCHSetup, TW_ACH_Profiles, invoiceDirectDeposit.ClientID,
                                trim(str(invoiceDirectDeposit.DarwinInvoiceNumber)), invoiceDirectDeposit.DebitDate, out l_checkbook,
                                        l_client_type, ref TempBatch, i_recv_key, in_batchtype);
                            }
                        }
                    }

                    // Entry Detail Record
                    n6_Individual = pad(l_Entry_Description, PadFormat.TRAILING, CH_SPACE, 15) +
                    pad(substring(Customer_Name(invoiceDirectDeposit.ClientID), 1, 22), PadFormat.TRAILING, CH_SPACE, 24) + CH_ZERO +
                    pad(l_originationID, PadFormat.TRAILING, CH_SPACE, 8);
                    if (tWReverseVoidsCB || invoiceDirectDeposit.DirectDepositAmount < 0)
                    {
                        ACH_Entry_Detail(invoiceDirectDeposit, invoiceDirectDeposit.DirectDepositAmount, n6_Individual, 2, ref ACHTotalCredits, ref ACHFTotalCredits);
                    }
                    else
                    {
                        ACH_Entry_Detail(invoiceDirectDeposit, invoiceDirectDeposit.DirectDepositAmount, n6_Individual, 2, ref ACHTotalDebits, ref ACHFTotalDebits);
                    }

                    if (tWReverseVoidsCB)
                    {
                        // Only reverse void amount if it had already been posted in original ACH file, but has not been posted as a reverse trx yet
                        if (invoiceDirectDeposit.ACHReverseBatch.IsNull() && invoiceDirectDeposit.ACHExportBatch.IsNotNull())
                        {
                            post_amount -= invoiceDirectDeposit.DirectDepositAmount.GetValueOrDefault();
                        }
                    }
                    else
                    {
                        if (invoiceDirectDeposit.ACHExportBatch.IsNull())
                        {
                            post_amount += invoiceDirectDeposit.DirectDepositAmount.GetValueOrDefault();
                        }
                    }

                    if (TW_ACH_Profiles.AutoRemovePreNoteFlag)
                    {
                        Remove_PreNote_Flag(invoiceDirectDeposit);
                    }
                }
            }
        }

        public void Set_Transfered_Info(ITransferInfo source_table, bool in_voided, bool in_man_voided, string in_voided_flag, long in_batch_id, bool oiReverseBatchFlag = false)
        {
            if (!in_voided_flag.IsNullOrEmpty())
            {
                source_table.ManualVoided = in_voided;
            }
            if (source_table.ACHExportBatch.IsNull())
            {
                source_table.ACHExportBatch = (int)in_batch_id;
                source_table.ACHExportDate = DateTime.Now;
                source_table.ACHExportUser = GlobalVariables.CurrentUser.UserID;
            }

            if (tWReverseVoidsCB && source_table.ACHExportBatch.IsNotNull())
            {
                if (oiReverseBatchFlag || in_voided || in_man_voided)
                {
                    source_table.ACHReverseBatch = (int)in_batch_id;
                    source_table.ACHReverseDate = DateTime.Now;
                    source_table.ACHReverseUser = GlobalVariables.CurrentUser.UserID;
                }
            }
        }

        public void Update_Main_Table(ClientACHHeader clientACHHeaderNew)
        {
            ClientACHHeader clientACHHeader = _dbContext.ClientACHHeaders.Where(x => x.ACHType == clientACHHeaderNew.ACHType && x.CheckBookID == clientACHHeaderNew.CheckBookID
                                                                    && x.TransactionNumber == clientACHHeaderNew.TransactionNumber && x.ClientID == clientACHHeaderNew.ClientID).FirstOrDefault();

            if (clientACHHeader != null)
            {
                clientACHHeader = clientACHHeaderNew;
                _dbContext.SaveChanges();
            }
        }

        public void CM_Post_DD(object IN_Checkbook, string in_Doc_Number, decimal in_DD_Amount, DateTime in_check_date)
        {
            // TODO I don't know what any of this is
            ////in 'Checkbook ID' IN_Checkbook;
            ////in string in_Doc_Number;
            ////in currency in_DD_Amount;
            ////in date in_check_date;

            //object CMDoc; // local CMDoc L_Doc;


            //if (true)// if 'Module Registered'[CM] of globals and not empty(in_DD_Amount) then
            //{
            //    clear L_Doc;
            //    set 'L_Doc:Checkbook ID' to IN_Checkbook;
            //    set 'L_Doc:CMDocType' to 6;
            //    set 'L_Doc:CMDocDate' to in_check_date;
            //    set 'L_Doc:Currency ID' to 'Functional Currency' of globals;
            //    { currency is not stored in the UPR tables}
            //    set 'L_Doc:Currency Index' to 'Functional Currency Index' of globals;
            //    { currency is not stored in the UPR files}
            //    set 'L_Doc:Decimal Places' to 'Functional Decimal Places' of globals;
            //    clear 'L_Doc:GL Posting Date';

            //    set 'L_Doc:CMDocNumber' to in_Doc_Number;
            //    clear 'L_Doc:Description';
            //    set 'L_Doc:Paid ToRcvd From' to "Payroll Deposit";
            //    clear 'L_Doc:CMLinkID';
            //    set 'L_Doc:CMDocAmount' to in_DD_Amount;
            //    set 'L_Doc:Originating Amount' to in_DD_Amount;
            //    set 'L_Doc:Checkbook Amount' to in_DD_Amount;

            //    set 'L_Doc:Source Document' to "CMADJ";
            //    set 'L_Doc:Source Doc Type' to CHECK;
            //    set 'L_Doc:Source Doc Number' to in_Doc_Number;
            //    clear 'L_Doc:Note Index';
            //    set 'L_Doc:Audit Trail Code' to "CMADJ00001";
            //    set  'L_Doc:Voided' to false;

            //    if (true)// if AutoPost(L_Doc) of form CM_BankTrxObj <> OKAY then
            //    {
            //        debug "CM_Post_UPR: autopost status bad";
            //    }
            //}
        }

        public void TW_Handle_Multiple_Transactions(long ACHTotalCredits, long ACHTotalDebits, string l_trans_type, string l_prev_ACH_num, string l_prev_checkbook, string l_new_ACH)
        {
            long cur_post_amount;
            decimal l_prev_amt = 0;
            string l_last_CM_ACH;

            List<TW_ACH_Header_HIST> tW_ACH_Header_HISTs = tWACHHeaderHistEntities.Where(x => x.SourceDocNumber == l_prev_ACH_num && x.TW_TransactionType == l_trans_type).ToList();

            if (tW_ACH_Header_HISTs.Count == 0)
            {
                l_last_CM_ACH = l_prev_ACH_num;
            }
            else
            {
                l_last_CM_ACH = tW_ACH_Header_HISTs[0].TW_Export_ID;

                tW_ACH_Header_HISTs = tWACHHeaderHistEntities.Where(x => x.TW_Export_ID == l_last_CM_ACH && x.TW_TransactionType == l_trans_type && x.TW_CM_Updated == (l_trans_type == "P")).ToList();

                if (tW_ACH_Header_HISTs.Count > 0)
                {
                    l_last_CM_ACH = tW_ACH_Header_HISTs.Last().SourceDocNumber;
                }
            }

            tW_ACH_Header_HISTs = tWACHHeaderHistEntities.Where(x => x.SourceDocNumber == l_prev_ACH_num && x.TW_TransactionType == l_trans_type).ToList();

            TW_ACH_Header_HIST tW_ACH_Header_HIST;
            tW_ACH_Header_HIST = tW_ACH_Header_HISTs.FirstOrDefault();

            if (tW_ACH_Header_HIST == null)
            {
                tW_ACH_Header_HIST = new TW_ACH_Header_HIST
                {
                    TW_Export_ID = l_prev_ACH_num
                };
                l_prev_amt = tW_ACH_Header_HIST.TRXAmount;
            }

            tW_ACH_Header_HIST.SourceDocNumber = l_new_ACH;
            tW_ACH_Header_HIST.TW_CM_Updated = false;
            tW_ACH_Header_HIST.LastDateEdited = DateTime.Now.Date;
            tW_ACH_Header_HIST.LastTimeEdited = DateTime.Now;
            tW_ACH_Header_HIST.LastUsertoEdit = GlobalVariables.CurrentUser.UserID;

            EmployeeCheckStatusHistory employeeCheckStatusHistory = null;

            if (l_trans_type == "P")
            {
                employeeCheckStatusHistory = _dbContext.EmployeeCheckStatusHistories.Where(x => x.CheckBookID == l_prev_checkbook && x.CMTrxNumber == l_prev_ACH_num).FirstOrDefault();

                if (employeeCheckStatusHistory == null)
                {
                    employeeCheckStatusHistory = _dbContext.EmployeeCheckStatusHistories.Where(x => x.CMTrxNumber == l_last_CM_ACH).FirstOrDefault();
                }
            }

            cur_post_amount = (ACHTotalCredits + ACHTotalDebits);

            if (tWReverseVoidsCB)
            {
                cur_post_amount = -cur_post_amount;
            }

            if (l_trans_type == "P")
            {
                if (employeeCheckStatusHistory != null)
                {
                    if (employeeCheckStatusHistory.TrxAmount * 100 == cur_post_amount)
                    {
                        employeeCheckStatusHistory.CMTrxNumber = l_new_ACH;
                        //cM_Transaction.SourceDocNumber = l_new_ACH; // SourceDocNumber absent from CohesionDatabase and not read anywhere

                        tW_ACH_Header_HIST.TW_CM_Updated = true;

                        _dbContext.SaveChanges();
                    }
                    else
                    {
                        _aCHMessages.Add(new ACHMessage("This ACH batch has been previously created as " + l_prev_ACH_num +
                                " with a different dollar value.  A manual bank reconcile entry " +
                                "should be created to record the difference in the appropriate checkbook."));
                    }

                    tW_ACH_Header_HIST.TRXAmount = employeeCheckStatusHistory.TrxAmount.GetValueOrDefault();
                }
            }

            if (tW_ACH_Header_HIST.TRXAmount == 0 || tW_ACH_Header_HIST.IsNull())
            {
                tW_ACH_Header_HIST.TRXAmount = l_prev_amt;
            }

            tW_ACH_Header_HIST.TRXBalance = TW_Long_to_Cur(cur_post_amount);
            tW_ACH_Header_HIST.TW_TransactionType = l_trans_type;

            _dbContext.SaveChanges();
        }

        public void Create_ACH_File_Payroll_Manual(ACHSetup aCHSetup, decimal i_manual_amount, ref long inout_ACHCreditAmount, ref long inout_ACHFCreditAmount, ref long inout_ACHDebitAmount, ref long inout_ACHFDebitAmount)
        {
            string l_n6TransActCode, l_n6TransitNumber, l_ACHTransit, l_ACHAmount, l_nACH, l_account_desc;

            aCHEntryCount++;
            aCHFEntryCount++;
            aCHFRecCount++;

            if (accountType == 1)
            {
                l_account_desc = "CHECKING";
            }
            else
            {
                l_account_desc = "SAVINGS";
            }

            l_n6TransActCode = TransActCode(l_account_desc, false, 1 /*payroll*/, i_manual_amount);
            l_n6TransitNumber = transitNumber;
            l_ACHTransit = substring(l_n6TransitNumber, 1, 8);
            aCHEntryHash += value(l_ACHTransit);
            aCHFEntryHash += value(l_ACHTransit);
            l_ACHAmount = format(Math.Abs(i_manual_amount) * 100);

            if (tWReverseVoidsCB)
            {
                inout_ACHDebitAmount += value(l_ACHAmount);
                inout_ACHFDebitAmount += value(l_ACHAmount);
            }
            else
            {
                inout_ACHCreditAmount += value(l_ACHAmount);
                inout_ACHFCreditAmount += value(l_ACHAmount);
            }

            l_nACH = upper("6" + l_n6TransActCode + l_n6TransitNumber +
                pad(bankAccountNumber, PadFormat.TRAILING, CH_SPACE, 17) +
                pad(l_ACHAmount, PadFormat.LEADING, CH_ZERO, 10) +
                pad(employeeID, PadFormat.TRAILING, CH_SPACE, 15) +
                pad(employeeName, PadFormat.TRAILING, CH_SPACE, 24) +
                CH_ZERO + pad(aCHSetup.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 7));

            TextFile_WriteLine(l_nACH);
        }

        public void Create_ACH_File2(string in_prev_client, string in_prev_acc, int in_client_type, string in_recv_key, bool in_pay, int in_source,
                                        int in_err, string in_last_ACC, bool in_prt_offset, decimal in_manual_amt, string in_cb, ref long ACHTotalCredits,
                                        ref long ACHTotalDebits, ref long ACHFTotalCredits, ref long ACHFTotalDebits, string in_payroll_key,
                                        ACHSetup aCHSetup, ClientACHHeader clientACHHeader,
                                        ACHManualDepositRecord TW_ACH_Manual_Deposit_Record_TEMP,
                                        ACHProfile aCHProfile, Client client)
        {
            string l_cb;
            string l_ind_ID;
            string l_OffsetName;
            bool l_reverse;

            l_cb = in_cb;

            switch (in_source)
            {
                case 1: // Payroll
                    if (aCHSetup.PayrollOffsetRecord)
                    {
                        if (!aCHProfile.DepPayrollOffsetRecCheckbookID.IsNullOrEmpty())
                        {
                            // use checkbook id from ACH profile for offset record
                            l_cb = aCHProfile.DepPayrollOffsetRecCheckbookID;

                            if (in_client_type == 1 /*ASO*/ || UseClientLevelSetup(clientACHHeader.ClientID))
                            {
                                if (in_payroll_key.IsNullOrEmpty())
                                {
                                    l_ind_ID = pad(client.ACHCompanyID, PadFormat.TRAILING, CH_SPACE, 10);
                                }
                                else
                                {
                                    l_ind_ID = pad(in_payroll_key +
                                        substring(client.ACHCompanyID, 2, 9), PadFormat.TRAILING, CH_SPACE, 10);
                                }
                            }
                            else
                            {
                                l_ind_ID = aCHSetup.PayrollKey +
                                    pad(aCHSetup.FEIN, PadFormat.TRAILING, CH_SPACE, 9);
                            }
                        }
                        else
                        {
                            l_ind_ID = "OFFSET ACCOUNT";
                        }

                        l_OffsetName = GetOffsetName(clientACHHeader.ClientID);

                        if (tWReverseVoidsCB)
                        {
                            l_reverse = true;
                            ACH_Offset_Records(aCHSetup, l_reverse, l_cb, "22", l_ind_ID, ref ACHTotalCredits, ref ACHTotalDebits,
                                ref ACHFTotalCredits, ref ACHFTotalDebits, in_pay, in_source, l_OffsetName);
                        }
                        else
                        {
                            l_reverse = false;
                            ACH_Offset_Records(aCHSetup, l_reverse, l_cb, "27", l_ind_ID, ref ACHTotalCredits, ref ACHTotalDebits,
                                ref ACHFTotalCredits, ref ACHFTotalDebits, in_pay, in_source, l_OffsetName);
                        }
                    }
                    break;
                case 2: // Invoice
                    InvoiceDirectDeposit invoiceDirectDeposit = _dbContext.InvoiceDirectDeposits.FirstOrDefault(x => x.ClientID == in_prev_client && x.TransactionNumber == in_prev_acc);

                    if (TW_Receivables_Offset_Record(invoiceDirectDeposit.ClientID, invoiceDirectDeposit.DivisionID))
                    {
                        if (!aCHProfile.RecOffsetRecordCheckbookID.IsNullOrEmpty())
                        {
                            // use checkbook id from ACH profile for offset record
                            l_cb = aCHProfile.RecOffsetRecordCheckbookID;

                            if (in_client_type == 1) // ASO
                            {
                                if (in_recv_key.IsNullOrEmpty())
                                {
                                    l_ind_ID = pad(client.ACHCompanyID, PadFormat.TRAILING, CH_SPACE, 10);
                                }
                                else
                                {
                                    l_ind_ID = pad(in_recv_key + substring(client.ACHCompanyID, 2, 9), PadFormat.TRAILING, CH_SPACE, 10);
                                }
                            }
                            else
                            {
                                l_ind_ID = aCHSetup.ReceivablesKey + pad(aCHSetup.FEIN, PadFormat.TRAILING, CH_SPACE, 9);
                            }
                        }
                        else
                        {
                            l_ind_ID = "INVOICE DEBIT";
                        }

                        l_OffsetName = "";
                        if (tWReverseVoidsCB)
                        {
                            l_reverse = true;
                            ACH_Offset_Records(aCHSetup, l_reverse, l_cb, "27", l_ind_ID, ref ACHTotalCredits, ref ACHTotalDebits,
                                ref ACHFTotalCredits, ref ACHFTotalDebits, in_pay, in_source, l_OffsetName, in_prt_offset, in_manual_amt,
                                clientACHHeader, in_err, in_last_ACC, in_prev_client, TW_ACH_Manual_Deposit_Record_TEMP);
                        }
                        else
                        {
                            l_reverse = false;
                            ACH_Offset_Records(aCHSetup, l_reverse, l_cb, "22", l_ind_ID, ref ACHTotalCredits, ref ACHTotalDebits,
                                ref ACHFTotalCredits, ref ACHFTotalDebits, in_pay, in_source, l_OffsetName, in_prt_offset, in_manual_amt,
                                clientACHHeader, in_err, in_last_ACC, in_prev_client, TW_ACH_Manual_Deposit_Record_TEMP);
                        }
                    }
                    break;
            }
        }

        public void TW_ACH_File_4th_Line(ACHSetup aCHSetup, int IN_Client_type, int IN_source, string IN_EntryHash, long IN_ACHTotalDebits,
                                            long IN_ACHTotalCredits, long IN_TempBatch, string IN_originationID, string IN_ServiceClassCode)
        {
            string l_nACH = "";

            // For Payroll and PEO
            if (IN_Client_type == 2 /*PEO*/ && IN_source == 1 /*Payroll*/)
            {
                l_nACH = upper(IN_ServiceClassCode/*"8200"*/ + pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 6)
                    + IN_EntryHash +
                    pad(str(IN_ACHTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(IN_ACHTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                    aCHSetup.PayrollKey +
                    pad(aCHSetup.FEIN, PadFormat.TRAILING, CH_SPACE, 34) +
                    pad(IN_originationID, PadFormat.TRAILING, CH_SPACE, 8) +
                    pad(str(IN_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
            }
            // For Invoice and PEO
            else if (IN_Client_type == 2 /*PEO*/ && IN_source == 2 /*Invoice*/)
            {
                l_nACH = upper(IN_ServiceClassCode /*"8200"*/ + pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 6)
                    + IN_EntryHash +
                    pad(str(IN_ACHTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(IN_ACHTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                    aCHSetup.ReceivablesKey +
                    pad(aCHSetup.FEIN, PadFormat.TRAILING, CH_SPACE, 34) +
                    pad(IN_originationID, PadFormat.TRAILING, CH_SPACE, 8) +
                    pad(str(IN_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
            }
            // For Invoice and ASO - Payroll and ASO is in Create_ACH_File right above where this script is called.
            else if (IN_Client_type == 1 /*ASO*/ && IN_source == 2 /*Invoice*/)
            {
                l_nACH = upper(IN_ServiceClassCode/*"8200"*/ + pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 6)
                    + IN_EntryHash +
                    pad(str(IN_ACHTotalDebits), PadFormat.LEADING, CH_ZERO, 12) + pad(str(IN_ACHTotalCredits), PadFormat.LEADING, CH_ZERO, 12) +
                    aCHSetup.ReceivablesKey +
                    pad(aCHSetup.FEIN, PadFormat.TRAILING, CH_SPACE, 34) +
                    pad(aCHSetup.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                    pad(str(IN_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
            }
            TextFile_WriteLine(l_nACH);
            aCHFRecCount++;
        }

        public void ACH_File_Header(ACHProfile aCHProfile)
        {
            string l_nACH;

            if (aCHFEntryCount >= 1)
            {
                return;
            }

            // WBD HEADER

            if (aCHProfile.GenerateWBDHdrTrailRecords)
            {
                l_nACH = upper("KH001" + pad(aCHProfile.WBDCustomerID, PadFormat.TRAILING, CH_SPACE, 8) +
                    pad(aCHProfile.WBDAppID, PadFormat.TRAILING, CH_SPACE, 10) + "R" +
                    TW_DateToString(sysdate()) + pad(aCHProfile.WBDSequence, PadFormat.TRAILING, CH_SPACE, 64));

                TextFile_WriteLine(l_nACH);
            }

            // All Entries file header record

            l_nACH = upper("101" + pad(aCHProfile.ImmediateDestination, PadFormat.TRAILING, CH_SPACE, 10) +
                pad(aCHProfile.ImmediateOrigin, PadFormat.TRAILING, CH_SPACE, 10) +
                TW_DateToString(sysdate()) + TW_TimeToString(systime()) + "A094101" +
                pad(aCHProfile.ImmediateDestinationName, PadFormat.TRAILING, CH_SPACE, 23) +
                pad(aCHProfile.ImmediateOriginName, PadFormat.TRAILING, CH_SPACE, 31));
            TextFile_WriteLine(l_nACH);

            aCHFRecCount++;
        }

        public void ACH_Batch_Header_Payroll(IBatchHeaderInvoice source_table, ACHProfile aCHProfile, DateTime? in_check_date, short? in_Offset_Days, string in_client,
                                                ref long inout_TempBatch, int in_client_type, string i_payroll_key, int in_batchtype, bool in_PayrollOffsetRecord)
        {
            string l_n5EntryDescript, l_n5EntryDate, l_nACH, l_DateTimeOrChaseAcct;
            string l_ServiceClassCode = "";
            DateTime l_check_date, l_n5EffectiveDate;
            int l_Offset_Days;

            if (in_check_date != null)
            {
                l_check_date = in_check_date.GetValueOrDefault();
            }
            else
            {
                l_check_date = DateTime.Now.Date;
            }

            l_Offset_Days = in_Offset_Days.GetValueOrDefault();

            string clientID = trim(in_client);

            Client client = _dbContext.Clients.Where(x => x.ClientID == clientID).FirstOrDefault();

            if (client != null && client.PayrollUseClientOffsetDays)
            {
                l_Offset_Days = client.PayrollOffsetDays.GetValueOrDefault();
            }

            l_n5EntryDescript = pad(upper(msg735), PadFormat.TRAILING, CH_SPACE, 10);
            l_n5EntryDate = TW_DateToString(l_check_date);
            l_n5EffectiveDate = l_check_date.AddDays(l_Offset_Days);

            if (l_n5EffectiveDate < DateTime.Now.Date)
            {
                l_n5EffectiveDate = DateTime.Now.Date;
            }

            if (in_batchtype == 1) // Payroll
            {
                if (in_PayrollOffsetRecord)
                {
                    // Balanced files
                    l_ServiceClassCode = "5200";
                }
                else
                {
                    // Credits only
                    l_ServiceClassCode = "5220";
                }
            }
            else if (in_batchtype == 3) // Mixed Debits and Credits
            {
                l_ServiceClassCode = "5200";
            }

            if (aCHProfile.ChaseBank)
            {
                l_DateTimeOrChaseAcct = pad(trim(aCHProfile.ChaseAccount), PadFormat.LEADING, CH_ZERO, 20);
            }
            else
            {
                l_DateTimeOrChaseAcct = "DARWIN" + TW_DateToString(sysdate()) + " " + TW_TimeToString(systime()) + "   ";
            }

            // All Entries Company/Batch Header Record.

            if (in_client_type == 1) //ASO
            {
                inout_TempBatch++;

                if (i_payroll_key.IsNullOrEmpty())
                {
                    l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                        l_DateTimeOrChaseAcct +
                        pad(source_table.CompanyID.ToString(), PadFormat.TRAILING, CH_SPACE, 10) + "PPD" +
                        l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + //3space + 1
                        pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                }
                else
                {
                    // replace first character of company ID with payroll key from ACH profile
                    l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                        l_DateTimeOrChaseAcct +
                        pad(i_payroll_key + substring(source_table.CompanyID.ToString(), 2, 9), PadFormat.TRAILING, CH_SPACE, 10) + "PPD" +
                        l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + //3space + 1
                        pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                }

                TextFile_WriteLine(l_nACH);
                aCHFRecCount++;
            }
            else if (in_client_type == 2) // PEO
            {
                inout_TempBatch++;

                if (UseClientLevelSetup(in_client))
                {
                    if (i_payroll_key.IsNullOrEmpty())
                    {
                        l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                            l_DateTimeOrChaseAcct + pad(source_table.CompanyID.ToString(), PadFormat.TRAILING, CH_SPACE, 10) + "PPD" +
                            l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1
                            pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                            pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                    }
                    else
                    {
                        // replace first character of company ID with payroll key from ACH profile
                        l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                            l_DateTimeOrChaseAcct + pad(i_payroll_key + substring(source_table.CompanyID.ToString(), 2, 9), PadFormat.TRAILING, CH_SPACE, 10) + "PPD" +
                            l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1
                            pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                            pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                    }
                }
                else
                {
                    l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                        l_DateTimeOrChaseAcct + source_table.PayrollKey +
                        pad(source_table.CompanyID.ToString(), PadFormat.TRAILING, CH_SPACE, 9) + "PPD" +
                        l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1
                        pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                }

                TextFile_WriteLine(l_nACH);
                aCHFRecCount++;
            }
        }

        public void ACH_Entry_Detail(IEntryDetail source_table, decimal? in_amount, string in_n6Individual, int in_ACHtype, ref long inout_ACHAmount, ref long inout_ACHFAmount)
        {
            string l_n6TransActCode, l_n6TransitNumber, l_ACHTransit, l_ACHAmount, l_nACH;

            aCHEntryCount++;
            aCHFEntryCount++;
            aCHFRecCount++;

            l_n6TransActCode = TransActCode(source_table.AccountDescription, source_table.Precheck, in_ACHtype, in_amount);
            l_n6TransitNumber = Transit_Number(source_table.BankID);

            l_ACHTransit = substring(l_n6TransitNumber, 1, 8);
            aCHEntryHash += value(l_ACHTransit);
            aCHFEntryHash += value(l_ACHTransit);

            if (source_table.Precheck && in_amount.IsNotNull())
            {
                l_ACHAmount = CH_ZERO.ToString();
            }
            else
            {
                l_ACHAmount = format(Math.Abs(in_amount.GetValueOrDefault()) * 100);
            }

            inout_ACHAmount += value(l_ACHAmount);
            inout_ACHFAmount += value(l_ACHAmount);

            l_nACH = upper("6" + l_n6TransActCode + l_n6TransitNumber +
                          pad(source_table.AccountNumber, PadFormat.TRAILING, CH_SPACE, 17) +
                          pad(l_ACHAmount, PadFormat.LEADING, CH_ZERO, 10) + in_n6Individual +
                          pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 7));

            TextFile_WriteLine(l_nACH);
            if (!source_table.ACHExportUser.IsNullOrEmpty() && source_table.MultipleExport != 1)
            {
                source_table.MultipleExport = 1;
            }
        }

        public void Remove_PreNote_Flag(EmployeeDirectDepositHistory employeeDirectDepositHistory)
        {
            string employeeID = employeeDirectDepositHistory.EmployeeID;
            string bankID = employeeDirectDepositHistory.BankID;
            string thinkware_Account_Number = employeeDirectDepositHistory.AccountNumber;
            string payRecord = employeeDirectDepositHistory.PayRecord;
            string thinkware_Account_Description = employeeDirectDepositHistory.AccountDescription;
            byte tW_DDBased_On_Type = employeeDirectDepositHistory.DirectDepositBasedOnType;
            EmployeeDirectDeposit employeeDirectDeposit = _dbContext.EmployeeDirectDeposits.Where(x => x.EmployeeID == employeeID && x.BankID == bankID &&
                                                  x.AccountNumber == thinkware_Account_Number && x.PayRecord == payRecord &&
                                                  x.AccountDescription == thinkware_Account_Description && x.DirectDepositBasedOnType == tW_DDBased_On_Type).FirstOrDefault();

            if (employeeDirectDeposit != null)
            {
                employeeDirectDeposit.Precheck = false;
                _dbContext.SaveChanges();
            }
        }

        public void Remove_PreNote_Flag(InvoiceDirectDeposit invoiceDirectDeposit)
        {
            string sourceCustomerNumber = invoiceDirectDeposit.ClientID;
            string sourceTW_Division_ID = invoiceDirectDeposit.DivisionID;
            ClientDivision clientDivision = _dbContext.ClientDivisions.Where(x => x.ClientID == sourceCustomerNumber && x.DivisionID == sourceTW_Division_ID).FirstOrDefault();

            if (clientDivision != null && clientDivision.Precheck)
            {
                clientDivision.Precheck = false;
                _dbContext.SaveChanges();
            }
        }

        public void ACH_Batch_Header_Invoice(IBatchHeaderInvoice source_table, ACHProfile aCHProfile, string in_client, string in_Inv, DateTime? in_Debit_Date,
                                                out string out_checkbookID, int in_client_type, ref long inout_TempBatch, string i_recv_key, int in_batchType)
        {
            string l_n5EntryDescript, l_n5EntryDate, l_nACH, l_DateTimeOrChaseAcct;
            string l_ServiceClassCode = "";
            DateTime l_n5EffectiveDate;
            string clientID = trim(in_client);
            Client client = _dbContext.Clients.Where(x => x.ClientID == clientID).FirstOrDefault();

            if (excludeInvoiceNumber)
            {
                l_n5EntryDescript = pad(" ", PadFormat.TRAILING, CH_SPACE, 10);
            }
            else
            {
                if (aCHProfile.ChaseBank)
                {
                    l_n5EntryDescript = pad("IN" + trim(in_client) + "0" + trim(in_Inv), PadFormat.TRAILING, CH_SPACE, 10);
                }
                else
                {
                    l_n5EntryDescript = pad("IN" + trim(in_client) + "-" + trim(in_Inv), PadFormat.TRAILING, CH_SPACE, 10);
                }
            }

            l_n5EntryDate = TW_DateToString(DateTime.Now.Date);
            l_n5EffectiveDate = in_Debit_Date.GetValueOrDefault();

            if (l_n5EffectiveDate < DateTime.Now.Date)
            {
                l_n5EffectiveDate = DateTime.Now.Date;
            }
            long darwinInvoiceNumber = value(in_Inv);

            InvoiceDirectDeposit invoiceDirectDeposit = _dbContext.InvoiceDirectDeposits.Where(x => x.ClientID == clientID && x.DarwinInvoiceNumber >= darwinInvoiceNumber).OrderBy(x => x.DarwinInvoiceNumber).FirstOrDefault();

            if (TW_Client_Type(in_client) == TW_ASO && TW_Receivables_Offset_Record(in_client, invoiceDirectDeposit.DivisionID))
            {
                ClientDivision clientDivision = _dbContext.ClientDivisions.Where(x => x.ClientID == clientID && x.DivisionID == invoiceDirectDeposit.DivisionID).FirstOrDefault();

                out_checkbookID = clientDivision.ReceivablesCheckbookID;
            }
            else
            {
                out_checkbookID = client.CheckbookID;
            }

            switch (in_batchType) // Payroll
            {
                case 1:
                    break;
                case 2:
                    if (TW_Receivables_Offset_Record(invoiceDirectDeposit.ClientID, invoiceDirectDeposit.DivisionID))
                    {
                        // Balanced files
                        l_ServiceClassCode = "5200";
                    }
                    else
                    {
                        // Debits only
                        l_ServiceClassCode = "5225";
                    }
                    break;
                case 3:
                    l_ServiceClassCode = "5200";
                    break;
            }

            if (aCHProfile.ChaseBank)
            {
                l_DateTimeOrChaseAcct = pad(trim(aCHProfile.ChaseAccount), PadFormat.LEADING, CH_ZERO, 20);
            }
            else
            {
                l_DateTimeOrChaseAcct = "DARWIN" + TW_DateToString(sysdate()) + " " + TW_TimeToString(systime()) + "   "; // 3space
            }

            // All Entries Company/Batch Header Record.

            if (in_client_type == 1 && useClientInfo)
            {
                inout_TempBatch++;

                if (i_recv_key.IsNullOrEmpty())
                {
                    l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                        l_DateTimeOrChaseAcct +
                        pad(source_table.CompanyID.ToString(), PadFormat.TRAILING, CH_SPACE, 10) +
                        "CCD" +
                        l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1}
                        pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                }
                else
                {
                    // replace first character of company ID with recv key from ACH profile
                    l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                        l_DateTimeOrChaseAcct +
                        pad(i_recv_key + substring(source_table.CompanyID.ToString(), 2, 9), PadFormat.TRAILING, CH_SPACE, 10) +
                        "CCD" +
                        l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1
                        pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                }

                TextFile_WriteLine(l_nACH);
                aCHFRecCount++;
            }
            else
            {
                inout_TempBatch++;

                if (in_client_type == 2 && UseClientLevelSetup(in_client) && useClientInfo)
                {
                    if (i_recv_key.IsNullOrEmpty())
                    {
                        l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                            l_DateTimeOrChaseAcct +
                            pad(source_table.CompanyID.ToString(), PadFormat.TRAILING, CH_SPACE, 10) +
                            "CCD" +
                            l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1
                            pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                            pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                    }
                    else
                    {
                        // replace first character of company ID with recv key from ACH profile
                        l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                            l_DateTimeOrChaseAcct +
                            pad(i_recv_key + substring(source_table.CompanyID.ToString(), 2, 9), PadFormat.TRAILING, CH_SPACE, 10) +
                            "CCD" +
                            l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1
                            pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                            pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));
                    }
                }
                else
                {
                    l_nACH = upper(l_ServiceClassCode + pad(source_table.ACHCompanyName, PadFormat.TRAILING, CH_SPACE, 16) +
                        l_DateTimeOrChaseAcct +
                        source_table.TW_Recv_Key + pad(source_table.CompanyID.ToString(), PadFormat.TRAILING, CH_SPACE, 9) +
                        "CCD" +
                        l_n5EntryDescript + l_n5EntryDate + TW_DateToString(l_n5EffectiveDate) + "   1" + // 3space + 1
                        pad(source_table.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(inout_TempBatch), PadFormat.LEADING, CH_ZERO, 7));

                }

                TextFile_WriteLine(l_nACH);
                aCHFRecCount++;
            }
        }

        public void ACH_Offset_Records(ACHSetup aCHSetup, bool i_reverse, string in_checkbook, string in_n6TransActCode,
                                       string in_n6IndividualID, ref long ACHTotalCredits, ref long ACHTotalDebits, ref long ACHFTotalCredits,
                                       ref long ACHFTotalDebits, bool i_payroll_selected, int i_source, string in_OffsetName = "",
                                       bool i_prt_offset = false, decimal i_manual_amount = default, ClientACHHeader clientACHHeader = null,
                                       int i_err = default, string i_last_ACC = "", string i_prev_client = "",
                                       ACHManualDepositRecord TW_ACH_Manual_Deposit_Record_TEMP = null)
        {
            string l_ACHTransit, l_nACH, l_account_desc, l_ACHAmount;
            string l_n6TransActCode = "";
            decimal l_manual_amount_currency;
            long l_offset_amount = 0;
            string l_OffsetName;

            if ((i_reverse && i_source == 2 /*invoice*/) || (!i_reverse && i_source == 1 /*payroll*/))
            {
                l_offset_amount = ACHTotalCredits;
            }
            else if ((i_reverse && i_source == 1/*payroll*/) || (!i_reverse && i_source == 2 /*invoice*/))
            {
                l_offset_amount = ACHTotalDebits;
            }

            Company_Bank(in_checkbook, out string l_n6TransitNumber, out string l_n6Account);
            l_ACHTransit = substring(l_n6TransitNumber, 1, 8);
            if (l_offset_amount != 0)
            {
                aCHEntryCount++;
                aCHFEntryCount++;
                aCHFRecCount++;

                if ((i_reverse && i_source == 2 /*invoice*/) || (!i_reverse && i_source == 1 /*payroll*/))
                {
                    ACHTotalDebits += l_offset_amount;
                    ACHFTotalDebits += l_offset_amount;
                }
                else if ((i_reverse && i_source == 1 /*payroll*/) || (!i_reverse && i_source == 2 /*invoice*/))
                {
                    ACHTotalCredits += l_offset_amount;
                    ACHFTotalCredits += l_offset_amount;
                }

                aCHEntryHash += value(l_ACHTransit);
                aCHFEntryHash += value(l_ACHTransit);

                if (in_n6TransActCode == "27" && i_source == 1 /*payroll*/ && !in_OffsetName.IsNullOrEmpty())
                {
                    l_nACH = upper("6" + in_n6TransActCode + pad(l_n6TransitNumber, PadFormat.TRAILING, CH_SPACE, 9) +
                        pad(l_n6Account, PadFormat.TRAILING, CH_SPACE, 17) + pad(str(l_offset_amount), PadFormat.LEADING, CH_ZERO, 10) +
                        pad(in_n6IndividualID, PadFormat.TRAILING, CH_SPACE, 15) +
                        pad(in_OffsetName, PadFormat.TRAILING, CH_SPACE, 24) + CH_ZERO +
                        pad(aCHSetup.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 7));
                }
                else
                {
                    l_nACH = upper("6" + in_n6TransActCode + pad(l_n6TransitNumber, PadFormat.TRAILING, CH_SPACE, 9) +
                        pad(l_n6Account, PadFormat.TRAILING, CH_SPACE, 17) + pad(str(l_offset_amount), PadFormat.LEADING, CH_ZERO, 10) +
                        pad(in_n6IndividualID, PadFormat.TRAILING, CH_SPACE, 15) +
                        pad(aCHSetup.CompanyName, PadFormat.TRAILING, CH_SPACE, 24) + CH_ZERO +
                        pad(aCHSetup.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                        pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 7));
                }

                TextFile_WriteLine(l_nACH);

                if (!i_payroll_selected || (i_payroll_selected && i_last_ACC != clientACHHeader?.TransactionNumber))
                {
                    // invoice only selected
                    if (clientACHHeader == null || i_last_ACC != clientACHHeader.TransactionNumber || i_prev_client != clientACHHeader.ClientID)
                    {
                        ACHManualDepositRecord tW_ACH_Manual_Deposit_Record_TEMP = tWACHManualDepositRecordTEMPEntities.Where(x => x.TW_Source == i_source && x.TW_Transaction_Number == i_last_ACC && x.CustomerNumber == i_prev_client).FirstOrDefault();

                        if (tW_ACH_Manual_Deposit_Record_TEMP != null)
                        {
                            l_manual_amount_currency = TW_ACH_Manual_Deposit_Record_TEMP.DepositAmount;
                            Create_ACH_File_Payroll_Manual(aCHSetup, l_manual_amount_currency, ref ACHTotalCredits, ref ACHFTotalCredits, ref ACHTotalDebits, ref ACHFTotalDebits);

                            aCHEntryCount++;
                            aCHFEntryCount++;
                            aCHFRecCount++;

                            aCHEntryHash += value(l_ACHTransit);
                            aCHFEntryHash += value(l_ACHTransit);

                            l_ACHAmount = format(Math.Abs(l_manual_amount_currency) * 100);

                            if (i_reverse)
                            {
                                ACHTotalCredits += value(l_ACHAmount);
                                ACHFTotalCredits += value(l_ACHAmount);
                            }
                            else
                            {
                                ACHTotalDebits += value(l_ACHAmount);
                                ACHFTotalDebits += value(l_ACHAmount);
                            }

                            if (in_n6TransActCode == "22")
                            {
                                l_n6TransActCode = "27";
                            }
                            else if (in_n6TransActCode == "27")
                            {
                                l_n6TransActCode = "22";
                            }
                            l_nACH = upper("6" + l_n6TransActCode + pad(l_n6TransitNumber, PadFormat.TRAILING, CH_SPACE, 9) +
                            pad(l_n6Account, PadFormat.TRAILING, CH_SPACE, 17) + pad(l_ACHAmount, PadFormat.LEADING, CH_ZERO, 10) +
                                pad(in_n6IndividualID, PadFormat.TRAILING, CH_SPACE, 15) +
                                pad(aCHSetup.CompanyName, PadFormat.TRAILING, CH_SPACE, 24) + CH_ZERO +
                                pad(aCHSetup.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                                pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 7));
                            TextFile_WriteLine(l_nACH);
                        }
                    }
                }
                else
                {
                    if (i_prt_offset)
                    {
                        aCHEntryCount++;
                        aCHFEntryCount++;

                        if (accountType == 1)
                        {
                            l_account_desc = "CHECKING";
                        }
                        else
                        {
                            l_account_desc = "SAVINGS";
                        }

                        l_n6TransActCode = TransActCode(l_account_desc, false, 1 /*payroll*/, i_manual_amount);

                        if (in_n6TransActCode == "22")
                        {
                            l_n6TransActCode = "27";
                        }
                        else if (in_n6TransActCode == "27")
                        {
                            l_n6TransActCode = "22";
                        }

                        if (i_reverse)
                        {
                            ACHTotalCredits += l_offset_amount;
                            ACHFTotalCredits += l_offset_amount;
                        }
                        else
                        {
                            ACHTotalDebits += l_offset_amount;
                            ACHFTotalDebits += l_offset_amount;
                        }

                        l_n6TransitNumber = transitNumber;
                        l_ACHTransit = substring(l_n6TransitNumber, 1, 8);
                        aCHEntryHash += value(l_ACHTransit);
                        aCHFEntryHash += value(l_ACHTransit);

                        l_ACHAmount = format(Math.Abs(i_manual_amount) * 100);
                        l_nACH = upper("6" + l_n6TransActCode + l_n6TransitNumber +
                            pad(bankAccountNumber, PadFormat.TRAILING, CH_SPACE, 17) +
                            pad(l_ACHAmount, PadFormat.LEADING, CH_ZERO, 10) +
                            pad(employeeID, PadFormat.TRAILING, CH_SPACE, 15) +
                            pad(employeeName, PadFormat.TRAILING, CH_SPACE, 24) +
                            CH_ZERO + pad(aCHSetup.OriginationID, PadFormat.TRAILING, CH_SPACE, 8) +
                            pad(str(aCHEntryCount), PadFormat.LEADING, CH_ZERO, 7));

                        TextFile_WriteLine(l_nACH);
                    }
                }
            }
        }

        public void Company_Bank(string in_checkbook, out string out_transit_number, out string out_account)
        {
            out_transit_number = "";
            out_account = "";

            Checkbook checkbook = _dbContext.Checkbooks.Where(x => x.CheckbookID == in_checkbook).FirstOrDefault();

            if (checkbook != null)
            {
                out_account = checkbook.BankAccountNumber;
                out_transit_number = Transit_Number(checkbook.BankID);
            }
        }

        private bool SetExportID(ClientACHHeader clientACHHeader, ACHProfile aCHProfile, out string error)
        {
            string aCHProfileID;
            if (!clientACHHeader.ACHPayrollProfileID.IsNullOrEmpty())
            {
                aCHProfileID = clientACHHeader.ACHPayrollProfileID;
            }
            else if (!clientACHHeader.ACHReceivablesProfileID.IsNullOrEmpty())
            {
                aCHProfileID = clientACHHeader.ACHReceivablesProfileID;
            }
            else
            {
                error = "Client ACH Header for payroll number " + clientACHHeader.PayrollNumber + " has no Payroll Profile ID assigned";

                return false;
            }

            if (aCHProfile == null)
            {
                error = "Cannot find ACH Profile for Profile ID " + aCHProfileID;
                return false;
            }

            if (aCHProfile.ACHFileName.IsNullOrEmpty())
            {
                error = "ACH Profile ID " + aCHProfileID + " does not have a file name";
                return false;
            }

            string fileName = aCHProfile.ACHFileName.Trim();
            clientACHHeader.ACHExportID = fileName;

            string[] fileNameAndExtension = fileName.Split('.');
            if (fileNameAndExtension.Length > 2)
            {
                error = "Default ACH file name for Profile ID " + aCHProfileID + " has more than one dot (.)";
                return false;
            }

            string newFileName = fileNameAndExtension[0];

            string extension = "";
            if (fileNameAndExtension.Length > 1)
            {
                extension = fileNameAndExtension[1];
            }

            int numberOfDigitsAtEnd = 0;
            bool hasNumbers = false;
            for (int i = newFileName.Length - 1; i >= 0; i--)
            {
                if (!char.IsDigit(newFileName[i]))
                {
                    break;
                }
                hasNumbers = true;

                numberOfDigitsAtEnd++;
            }

            int newNumber = 1;
            if (hasNumbers)
            {
                newNumber = Convert.ToInt32(newFileName.Substring(newFileName.Length - numberOfDigitsAtEnd)) + 1;
                newFileName = newFileName.Substring(0, newFileName.Length - numberOfDigitsAtEnd);
            }

            newFileName += newNumber;

            if (extension.IsNullOrEmpty())
            {
                extension = "DAT";
            }

            newFileName += "." + extension;

            aCHProfile.ACHFileName = newFileName;

            error = string.Empty;
            return true;
        }

        #endregion

        #region helper methods

        /// <summary>
        /// Returns the batch type (1-Credits only, 2-Debits only or 3-mixed debits and credits) to be use when we setup the values in Service Class Code field in the 5 and 8 records
        /// </summary>
        /// <param name="TW_ACH_Header_TEMP"></param>
        /// <returns></returns>
        public int Check_BatchType(List<ClientACHHeader> clientACHHeaders)
        {
            int out_BatchType = 0;

            bool l_Credits = false;
            bool l_Debits = false;

            ClientACHHeader clientACHHeader = clientACHHeaders.Where(x => x.SelectedToPrint == true && x.ACHType == ((int)ACHTypes.Payroll)).FirstOrDefault();

            if (clientACHHeader != null)
            {
                l_Credits = true;
            }

            clientACHHeader = clientACHHeaders.Where(x => x.SelectedToPrint == true && x.ACHType == ((int)ACHTypes.Invoice)).FirstOrDefault();

            if (clientACHHeader != null)
            {
                l_Debits = true;
            }

            if (l_Credits && l_Debits)
            {
                out_BatchType = 3; // Mixed Debits and Credits
            }
            else if (l_Credits) // Credits only
            {
                out_BatchType = 1;
            }
            else if (l_Debits) // Debits only
            {
                out_BatchType = 2;
            }

            return out_BatchType;
        }

        public string Customer_Name(string customer_ID)
        {
            Client client = _dbContext.Clients.Where(x => x.ClientID == customer_ID).FirstOrDefault();

            return client.ClientName;
        }

        public string Next_Transferred_ID(ACHProfile aCHProfile)
        {
            aCHProfile.NextExportID++;

            return ACH + pad(trim(str(aCHProfile.NextExportID)), PadFormat.LEADING, CH_ZERO, 10);
        }

        public string TransActCode(string in_account_description, bool in_precheck, int in_source, decimal? in_amount)
        {
            string out_n6TransActCode;

            if (pos(in_account_description, "CHECK", 1) > 0)
            {
                out_n6TransActCode = "2";
            }
            else
            {
                out_n6TransActCode = "3";
            }

            switch (in_source)
            {
                case 1: // Payroll
                    if (tWReverseVoidsCB || in_amount < 0)
                    {
                        if (in_precheck)
                        {
                            out_n6TransActCode += "8";
                        }
                        else
                        {
                            out_n6TransActCode += "7";
                        }
                    }
                    else
                    {
                        if (in_precheck)
                        {
                            out_n6TransActCode += "3";
                        }
                        else
                        {
                            out_n6TransActCode += "2";
                        }
                    }
                    break;
                case 2: // Invoice
                    if (tWReverseVoidsCB || in_amount < 0)
                    {
                        if (in_precheck)
                        {
                            out_n6TransActCode += "3";
                        }
                        else
                        {
                            out_n6TransActCode += "2";
                        }
                    }
                    else
                    {
                        if (in_precheck)
                        {
                            out_n6TransActCode += "8";
                        }
                        else
                        {
                            out_n6TransActCode += "7";
                        }
                    }
                    break;
            }

            return out_n6TransActCode;
        }

        public string Transit_Number(string in_BankID)
        {
            Bank bank = _dbContext.Banks.Where(x => x.BankID == in_BankID).FirstOrDefault();

            return bank?.TransitNumber;
        }

        public decimal TW_Long_to_Cur(long INint)
        {
            return INint / 100;
        }

        public bool UseClientLevelSetup(string in_client)
        {
            Client client = _dbContext.Clients.Where(x => x.ClientID == in_client).FirstOrDefault();

            return client != null;
        }

        /// <summary>
        /// Determines if this record should be included in the ACH file. It partly depends on voids are being processed or not.
        /// </summary>
        /// <param name="employeeDirectDepositHistory"></param>
        /// <returns></returns>
        public bool UseForACH(EmployeeDirectDepositHistory employeeDirectDepositHistory)
        {
            if (tWReverseVoidsCB)
            {
                if ((employeeDirectDepositHistory.Voided || employeeDirectDepositHistory.ManualVoided) && employeeDirectDepositHistory.ACHExportBatch.IsNotNull()
                    && (employeeDirectDepositHistory.ActualDeposit != 0 || employeeDirectDepositHistory.Precheck))
                {
                    return true;
                }
            }
            else
            {
                if (!employeeDirectDepositHistory.Voided && !employeeDirectDepositHistory.ManualVoided && employeeDirectDepositHistory.ActualDeposit != 0
                    || !employeeDirectDepositHistory.Voided && !employeeDirectDepositHistory.ManualVoided && employeeDirectDepositHistory.Precheck)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Determines if this record should be included in the ACH file. It partly depends on voids are being processed or not.
        /// </summary>
        /// <param name="invoiceDirectDeposit"></param>
        /// <returns></returns>
        public bool UseForInvoiceACH(InvoiceDirectDeposit invoiceDirectDeposit)
        {
            bool R_use = false;

            if (tWReverseVoidsCB)
            {
                if ((invoiceDirectDeposit.Voided || invoiceDirectDeposit.ManualVoided) && invoiceDirectDeposit.ACHExportBatch.IsNotNull()
                    && (invoiceDirectDeposit.DirectDepositAmount != 0 || invoiceDirectDeposit.Precheck))
                {
                    R_use = true;
                }
            }
            else
            {
                if (!invoiceDirectDeposit.Voided && !invoiceDirectDeposit.ManualVoided && invoiceDirectDeposit.DirectDepositAmount != 0
                    || !invoiceDirectDeposit.Voided && !invoiceDirectDeposit.ManualVoided && invoiceDirectDeposit.Precheck)
                {
                    R_use = true;
                }
            }

            return R_use;
        }

        #endregion

        #region methods that I don't understand yet

        // Many below functions are converted from dexterity documentation that can be found here https://www.microsoft.com/en-us/download/details.aspx?id=36226
        // they are in the FUNCTLIB and SANSCRPT pdfs

        private void TextFile_WriteLine(string text)
        {
            if (!_aCHFile.IsNullOrEmpty())
            {
                _aCHFile += "\n";
            }
            _aCHFile += text;
        }

        private string upper(string text)
        {
            return text.ToUpper();
        }

        private string pad(string text, PadFormat direction, char paddingChar, int totalWidth)
        {
            if (text == null)
            {
                text = "";
            }
            if (direction == PadFormat.LEADING)
            {
                return text.PadLeft(totalWidth, paddingChar);
            }

            return text.PadRight(totalWidth, paddingChar);
        }

        private string str(long value)
        {
            return value.ToString();
        }

        private string trim(string text)
        {
            return text.Trim();
        }

        /// <summary>
        /// Retrieves a substring from this instance. The substring starts at a specified
        /// character position and has a specified length.
        /// This method is 1 based as it is a direct conversion from Dexterity
        /// </summary>
        /// <param name="text">The string being subdivided.</param>
        /// <param name="startIndex">The one-based starting character position of a substring in this instance.</param>
        /// <param name="length">The number of characters in the substring.</param>
        /// <returns></returns>
        private string substring(string text, int startIndex, int length)
        {
            if (length > text.Length - startIndex - 1)
            {
                return text.Substring(startIndex - 1);
            }
            return text.Substring(startIndex - 1, length);
        }

        /// <summary>
        /// Removes all decimals from double and returns only integer value with no rounding. truncate(1.8) will return 1.
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private long truncate(double value)
        {
            return (long)Math.Floor(value);
        }

        private string format(decimal value)
        {
            return Math.Floor(value).ToString();
        }

        private long value(string text)
        {
            if (long.TryParse(text, out long value))
            {
                return value;
            }

            return 0;
        }

        private int length(string thing)
        {
            return thing.Length;
        }

        private DateTime sysdate()
        {
            return DateTime.Now;
        }

        private DateTime systime()
        {
            return DateTime.Now;
        }

        /// <summary>
        /// Returns a numeric value indicating the one-based starting position of a string within a string
        /// </summary>
        /// <param name="l_file_name"></param>
        /// <param name="matchText"></param>
        /// <param name="startIndex">one-based</param>
        /// <returns></returns>
        private int pos(string l_file_name, string matchText, int startIndex)
        {
            // +1 because c# returns -1 if not found and dexterity returns 0 if not found. Also 0 vs 1 indexing
            return l_file_name.IndexOf(matchText, startIndex - 1) + 1;
        }

        private string TW_Employee_Name(string employeeID)
        {
            Employee employee = _dbContext.Employees.First(x => x.EmployeeID == employeeID);
            string employeeName = employee.LastName + ", " + employee.FirstName;
            if (!employee.MiddleName.IsNullOrEmpty())
            {
                employeeName += ", " + employee.MiddleName.First();
            }
            return employeeName;
        }

        private int TW_Client_Type(string customerOrVendorID)
        {
            return _dbContext.Clients.First(x => x.ClientID == customerOrVendorID).ClientType.GetValueOrDefault();
        }

        private bool TW_Receivables_Offset_Record(string iClient, string iDivision)
        {
            Client client = _dbContext.Clients.First(x => x.ClientID == iClient);

            if (client.ClientType == TW_ASO)
            {
                ClientDivision clientDivision = _dbContext.ClientDivisions.First(x => x.ClientID == client.ClientID && x.DivisionID == iDivision);

                return clientDivision.ReceivablesOffsetRecord;
            }
            else
            {
                ACHSetup aCHSetup = _dbContext.ACHSetups.First();

                return aCHSetup.ReceivablesOffsetRecord;
            }
        }

        private string TwGetTwInvoice(string INinvoiceNumber)
        {
            Invoice invoice = _dbContext.Invoices.FirstOrDefault(x => x.InvoiceNumber == INinvoiceNumber);

            return invoice?.InvoiceNumber;
        }

        private string GetOffsetName(string in_client)
        {
            Client client = _dbContext.Clients.Where(x => x.ClientID == in_client).FirstOrDefault();
            if (client != null && !client.OffsetRecordAccountHolder.IsNullOrEmpty())
            {
                return client.OffsetRecordAccountHolder;
            }

            _aCHMessages.Add(new ACHMessage("Client ACH Offset Name not found"));

            return "";
        }

        private string TW_DateToString(DateTime l_date)
        {
            return substring(str(l_date.Year), 3, 2) + pad(str(l_date.Month), PadFormat.LEADING, '0', 2) + pad(str(l_date.Day), PadFormat.LEADING, '0', 2);
        }

        private string TW_TimeToString(DateTime l_time)
        {
            string time = "";
            if (l_time.Hour < 10)
            {
                time = "0";
            }
            time += str(l_time.Hour);

            if (l_time.Minute < 10)
            {
                time += "0";
            }
            time += str(l_time.Minute);

            return time;
        }

        private string TW_Increment_String(string text)
        {
            if (int.TryParse(text, out int value))
            {
                return (value + 1).ToString();
            }

            return "0";
        }

        private enum PadFormat
        {
            LEADING,
            TRAILING
        }

        #endregion
    }

    public class ACHMessage
    {
        public ACHMessage(string message, bool isError = false)
        {
            IsError = isError;
            Message = message;
        }
        public bool IsError { get; set; }
        public string Message { get; set; }
    }
}
