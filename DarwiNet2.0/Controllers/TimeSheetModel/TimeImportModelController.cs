using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Mvc;
using DarwiNet2._0.Data;
using DarwiNet2._0.Logging;
using DarwiNet2._0.ViewModels;
using DarwiNet2._0.ViewModels.TimeSheetImport;
using DataDrivenViewEngine.Models.Core;

namespace DarwiNet2._0.Controllers.TimeSheetModel
{
    [IsSessionActive]
    public class TimeImportModelController : Controller
    {
        private readonly DnetEntities _dbContext;
        private readonly TimeSheetModelAssignment _timeSheetModelAssignment;
        private readonly DNetAuth _dnetAuth;
        private readonly ILogger _logger;

        public TimeImportModelController(
            DnetEntities dbContext,
            TimeSheetModelAssignment timeSheetModelAssignment,
            ILogger logger
        )
        {
            _dbContext = dbContext;
            _timeSheetModelAssignment = timeSheetModelAssignment;
            _logger = logger;

            // Get the client/company/employee id of current user
            _dnetAuth = Services.Auth.GetParameters();
        }

        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            base.Initialize(requestContext);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && _dbContext != null)
            {
                _dbContext.Dispose();
            }

            base.Dispose(disposing);
        }

        /// <summary>
        /// Entry page for timesheet import listing all the time import models of the current company
        /// </summary>
        public ActionResult Index()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeImportSetup);

            // Get the model and model details from the database
            List<TimeImportModel> timeImportModels = _dbContext
                .TimeImportModels.Where(m => m.CompanyID == _dnetAuth.CompanyID)
                .ToList();

            // Create a view model for the page
            List<ViewTimeImportVM> timeImportVMs = timeImportModels.ConvertAll(
                t => new ViewTimeImportVM()
                {
                    ModelId = t.ModelID,
                    Description = t.Description,
                    Delimiter = t.Delimiter,
                    RemoveQuotes = t.RemoveQuotes,
                    ImportType = t.ImportType,
                    UseSwipeClock = t.UseSwipeClock,
                    SwipeClockFormat = t.SwipeClockFormat,
                    Columns = new List<ViewTimeImportModelDetailVM>(),
                }
            );

            return View(timeImportVMs);
        }

        /// <summary>
        /// Display the time import model form with default settings to start creating
        /// </summary>
        public ActionResult CreateTimeImportModel()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeImportSetup);

            // Create default view model
            return View(
                new TimeImportVM()
                {
                    ModelID = string.Empty,
                    Description = string.Empty,
                    ImportType = enImportModelType.TimeClock,
                    Delimiter = "COMMA",
                    RemoveQuotes = false,
                    UseSwipeClock = false,
                    AssignedToClients = new List<string>(),
                    SwipeClockFormat = string.Empty,
                    Columns = new List<TimeImportModelDetailVM>(),
                }
            );
        }

        /// <summary>
        /// Get the client id and name of the current company to assign model to
        /// </summary>
        public async Task<ActionResult> GetAllClients()
        {
            var clients = await _dbContext
                .Clients.Where(c => c.CompanyID == _dnetAuth.CompanyID)
                .Select(c => new { c.ClientID, c.ClientName })
                .ToListAsync();

            return new JsonResult()
            {
                Data = clients,
                JsonRequestBehavior = JsonRequestBehavior.AllowGet,
            };
        }

        /// <summary>
        /// Display the time import model form with existing model to edit
        /// </summary>
        public ActionResult EditTimeImportModel(string modelId)
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeImportSetup);

            var clientsAssignment =
                from modelSetup in _dbContext.ClientTimeImportSetups
                join client in _dbContext.Clients on modelSetup.ClientID equals client.ClientID
                where modelSetup.ModelID == modelId && modelSetup.CompanyID == client.CompanyID
                select new { client.ClientID, client.ClientName };

            // Get the model and model details from the database
            TimeImportModel timeImportModel = _dbContext.TimeImportModels.FirstOrDefault(m =>
                m.CompanyID == _dnetAuth.CompanyID && m.ModelID == modelId
            );

            // If the model to edit does not exist, return to index page with error message
            if (timeImportModel == null)
            {
                TempData["Error"] = "The time import model does not exist.";
                return View("Index");
            }

            List<TimeImportModelDetail> timeImportModelDetails = _dbContext
                .TimeImportModelDetails.Where(m =>
                    m.ModelID == modelId && _dnetAuth.CompanyID == m.CompanyID
                )
                .OrderBy(m => m.SeqNbr)
                .ToList();

            TimeImportVM model = new TimeImportVM()
            {
                ModelID = timeImportModel.ModelID,
                Description = timeImportModel.Description,
                ImportType = (enImportModelType)timeImportModel.ImportType,
                Delimiter = timeImportModel.Delimiter,
                RemoveQuotes = timeImportModel.RemoveQuotes,
                UseSwipeClock = timeImportModel.UseSwipeClock,
                SwipeClockFormat = timeImportModel.SwipeClockFormat,
                AssignedToClients = clientsAssignment.Select(a => a.ClientID).ToList(),
                Columns = timeImportModelDetails.ConvertAll(d => new TimeImportModelDetailVM
                {
                    Position = d.SeqNbr,
                    Name = d.Name,
                    Type = d.Type,
                    Length = d.Length ?? 0,
                    StartPosition = d.StartPos ?? 0,
                    EndPosition = d.EndPos ?? 0,
                    NoOfDecimals = int.TryParse(d.Decimals, out int value) ? value : 0,
                    ImportType = d.ImportType,
                }),
            };

            return View(model);
        }

        /// <summary>
        /// POST request to submit the time import model and the model details setup for the client
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> EditTimeImportModel(
            string currentModelId,
            TimeImportVM updatedModel,
            CancellationToken ct
        )
        {
            // If the model is invalid, return to the form with a message
            if (!ModelState.IsValid)
            {
                ModelState.AddModelError(
                    "TimeImportModel",
                    "An error occurred while saving the time import model. Please try again or contact admin for support!"
                );
                return RedirectToAction("EditTimeImportModel", updatedModel);
            }

            try
            {
                // Make sure the new model id (if updated) does not already exist for the current company
                if (
                    currentModelId != updatedModel.ModelID
                    && _dbContext.TimeImportModels.Any(t =>
                        t.ModelID == updatedModel.ModelID && t.CompanyID == _dnetAuth.CompanyID
                    )
                )
                {
                    TempData["Error"] = string.Format(
                        "Another model with same name {0} already exist!",
                        updatedModel.ModelID
                    );

                    return RedirectToAction("EditTimeImportModel", updatedModel);
                }

                // Query the model that is to be updated
                TimeImportModel timeImportModelToUpdate =
                    _dbContext.TimeImportModels.FirstOrDefault(t =>
                        t.CompanyID == _dnetAuth.CompanyID && t.ModelID == currentModelId
                    );
                List<TimeImportModelDetail> modelDetailsToUpdate = _dbContext
                    .TimeImportModelDetails.Where(t =>
                        t.CompanyID == _dnetAuth.CompanyID && t.ModelID == currentModelId
                    )
                    .ToList();

                // Need to override/replace by deleting and insert the current view model
                _dbContext.TimeImportModelDetails.RemoveRange(modelDetailsToUpdate);
                _dbContext.TimeImportModels.Remove(timeImportModelToUpdate);
                _dbContext.SaveChanges();

                TimeImportModel timeImportModel = new TimeImportModel()
                {
                    CompanyID = _dnetAuth.CompanyID,
                    ModelID = updatedModel.ModelID,
                    Description = updatedModel.Description,
                    ImportType = (byte)updatedModel.ImportType,
                    Delimiter = updatedModel.Delimiter,
                    RemoveQuotes = updatedModel.RemoveQuotes,
                    UseSwipeClock = updatedModel.UseSwipeClock,
                    SwipeClockFormat = updatedModel.SwipeClockFormat,
                };
                _dbContext.TimeImportModels.Add(timeImportModel);

                // Add model details for each column
                foreach (var column in updatedModel.Columns)
                {
                    _dbContext.TimeImportModelDetails.Add(
                        new TimeImportModelDetail
                        {
                            CompanyID = _dnetAuth.CompanyID,
                            ModelID = updatedModel.ModelID,
                            SeqNbr = column.Position,
                            Name = column.Name,
                            Type = column.Type,
                            ImportType = column.ImportType,
                            Decimals = column.NoOfDecimals.ToString(),
                            // default settings
                            Length = 100,
                            StartPos = 0,
                            EndPos = 0,
                        }
                    );
                }
                _dbContext.SaveChanges();

                // Edit model assignment to clients
                List<Client> clients = await _dbContext.Clients.ToListAsync(ct);
                await _timeSheetModelAssignment.AssignModelToClients(
                    timeImportModel,
                    clients,
                    updatedModel.AssignedToClients,
                    ct
                );

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);

                ModelState.AddModelError(
                    "EditTimeImportModelForm",
                    "An error occurred while saving the time import model. Please try again or contact admin for support!"
                );
                return RedirectToAction("EditTimeImportModel", updatedModel);
            }
        }

        /// <summary>
        /// HTTP POST request to create a new time import model for the current company
        /// </summary>
        /// <param name="model">The new time import model to create</param>
        /// <returns>Route to index page (list all time import models) once done</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<ActionResult> CreateTimeImportModel(
            TimeImportVM model,
            CancellationToken ct
        )
        {
            // If the model is invalid, return to the form with a message
            if (!ModelState.IsValid)
            {
                ModelState.AddModelError(
                    "TimeImportModel",
                    "An error occurred while saving the time import model. Please try again or contact admin for support!"
                );
                return View("CreateTimeImportModel", model);
            }

            // Make sure the new model id does not already exist
            if (
                _dbContext.TimeImportModels.Any(t =>
                    t.ModelID == model.ModelID && t.CompanyID == _dnetAuth.CompanyID
                )
            )
            {
                TempData["Error"] = string.Format(
                    "Another model with same name {0} already exist!",
                    model.ModelID
                );

                return RedirectToAction("CreateTimeImportModel", model);
            }

            // Create the time sheet model
            _0.Data.TimeImportModel timeImportModel = new _0.Data.TimeImportModel()
            {
                CompanyID = _dnetAuth.CompanyID,
                ModelID = model.ModelID,
                Description = model.Description,
                ImportType = (byte)model.ImportType,
                Delimiter = model.Delimiter,
                RemoveQuotes = model.RemoveQuotes,
                UseSwipeClock = model.UseSwipeClock,
                SwipeClockFormat = model.SwipeClockFormat,
            };
            _dbContext.TimeImportModels.Add(timeImportModel);

            // Add model details for each column
            _dbContext.TimeImportModelDetails.AddRange(
                model.Columns.ConvertAll(column => new TimeImportModelDetail()
                {
                    CompanyID = _dnetAuth.CompanyID,
                    ModelID = model.ModelID,
                    SeqNbr = column.Position,
                    Name = column.Name,
                    Type = column.Type,
                    ImportType = column.ImportType,
                    Decimals = column.NoOfDecimals.ToString(),
                    // default settings
                    Length = 100,
                    StartPos = 0,
                    EndPos = 0,
                })
            );

            List<Client> clients = await _dbContext
                .Clients.Where(c => c.CompanyID == _dnetAuth.CompanyID)
                .ToListAsync(ct);

            await _dbContext.SaveChangesAsync(ct);

            // Then assign the time model created to clients
            await _timeSheetModelAssignment.AssignModelToClients(
                timeImportModel,
                clients,
                model.AssignedToClients,
                ct
            );

            return RedirectToAction("Index");
        }

        /// <summary>
        /// This controller will delete time import model and existing details for the current company
        /// </summary>
        /// <param name="modelId">The time import model id to delete</param>
        [HttpPost]
        public JsonResult DeleteTimeImportModel(string modelId)
        {
            try
            {
                // Find the time import models and its column details
                TimeImportModel timeImportModelToDelete =
                    _dbContext.TimeImportModels.FirstOrDefault(t =>
                        t.ModelID == modelId && t.CompanyID == _dnetAuth.CompanyID
                    );
                List<TimeImportModelDetail> modelDetailsToDelete = _dbContext
                    .TimeImportModelDetails.Where(t =>
                        t.ModelID == modelId && t.CompanyID == _dnetAuth.CompanyID
                    )
                    .ToList();

                if (timeImportModelToDelete == null)
                {
                    return Json(
                        new
                        {
                            success = false,
                            message = "The time import model to delete could not be found.",
                        }
                    );
                }

                // Check if model is in use
                List<string> clientsUsingModel = _dbContext
                    .ClientTimeImportSetups.Where(cts =>
                        cts.CompanyID == timeImportModelToDelete.CompanyID
                        && cts.ModelID == timeImportModelToDelete.ModelID
                    )
                    .Select(cts => cts.ClientID)
                    .ToList();
                List<string> clientNames = _dbContext
                    .Clients.Where(c => clientsUsingModel.Contains(c.ClientID))
                    .Select(c => c.ClientName)
                    .ToList();

                if (clientNames.Count > 0)
                {
                    string message = string.Format(
                        "Can't delete model {0}. It is currently being used by the following client{1}:\n{2}. Please unassign clients first before deleting",
                        modelId,
                        clientNames.Count > 1 ? "s" : string.Empty,
                        string.Join(", ", clientNames)
                    );
                    return Json(new { success = false, message });
                }

                // Perform deletion
                _dbContext.TimeImportModels.Remove(timeImportModelToDelete);
                foreach (var modelDetail in modelDetailsToDelete)
                {
                    _dbContext.TimeImportModelDetails.Remove(modelDetail);
                }
                _dbContext.SaveChanges();

                return Json(
                    new { success = true, message = "Time import model deleted successfully." }
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(new InvalidOperationException(ex.Message));

                return Json(
                    new
                    {
                        success = false,
                        message = "Failed to delete time import model. Please try again.",
                    }
                );
            }
        }
    }
}
