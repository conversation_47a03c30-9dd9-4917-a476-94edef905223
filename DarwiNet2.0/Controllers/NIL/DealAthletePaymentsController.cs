using DarwiNet2._0.Controllers.D2;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Logging;
using DarwiNet2._0.Models.Core;
using DarwiNet2._0.Services.D2;
using DarwiNet2._0.ViewModels.D2;
using DataDrivenViewEngine.Models.Core;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers.NIL
{
    [IsSessionActive]
    public class DealAthletePaymentsController : BackOfficeController
    {
        private readonly IUserTableFilterService _userTableFilterService;
        private readonly IDealsService _dealsService;
        private readonly IDealAthletePaymentsService _dealAthletePaymentsService;
        private readonly IUserService _userService;
        private readonly ICompanyService _companyService;

        public DealAthletePaymentsController (DnetEntities dbContext,
                                     IUserTableFilterService userTableFilterService,
                                     IDealsService dealsService,
                                     IDealAthletePaymentsService dealAthletePaymentsService,
                                     IUserService userService,
                                     ICompanyService companyService,
                                     ILogger logger)
            : base (dbContext, logger)
        {
            _userTableFilterService = userTableFilterService ?? throw new ArgumentNullException(nameof(userTableFilterService));
            _dealsService = dealsService ?? throw new ArgumentNullException(nameof(dealsService));
            _dealAthletePaymentsService = dealAthletePaymentsService ?? throw new ArgumentNullException(nameof(dealAthletePaymentsService));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _companyService = companyService ?? throw new ArgumentNullException(nameof(companyService));
        }

        #region Public Methods

        public ActionResult Index ()
        {
            string userId = GlobalVariables.DNETOwnerID;
            var viewModel = new DealsViewModel()
            {
                EmployeeName = _userService.GetNameByUserId(userId),
                SavedFilters = _userTableFilterService.GetAllFiltersByUserAndTable(userId, FilterTableConstants.Deals),
                AvailableCompanies = _companyService.GetCompaniesAvailableToUser(userId),
                DealStatus = GetEnumCodeAndText(typeof(DealStatus)),
                DealAthletePaymentStatus = GetEnumCodeAndText(typeof(DealAthletePaymentStatus)),
                DealTypes = _dealsService.GetDealTypes(GlobalVariables.CompanyID),
                DealActivityTypes = _dealsService.GetDealActivityTypes(GlobalVariables.CompanyID),
                Positions = _dealsService.GetPositions(GlobalVariables.CompanyID)
            };
            return View(viewModel);
        }

        [HttpPost]
        public ActionResult GetDealsTableData(TableFilter filters)
        {
            try
            {
                _userTableFilterService.SaveUserTableFilter(GlobalVariables.DNETOwnerID, FilterTableConstants.Deals, false, filters, 0, "");
                var tableInfo = _dealAthletePaymentsService.GetDealTableData(GlobalVariables.CompanyID, GlobalVariables.Client, filters);
                tableInfo.Query = tableInfo.Query.Skip(filters.EntriesToSkip).Take(filters.PerPage);
                return JsonSuccess(data: new TableFilterData<DealTableRowDTO>
                {
                    Data = tableInfo.Query.ToList(),
                    FilteredEntries = tableInfo.FilteredEntries,
                    TotalEntries = tableInfo.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public ActionResult PayDealAthletePayment(int companyID, string clientID, long dealID, string employeeID, decimal? paymentAmount, DateTime? paymentDate, bool payDealAmount)
        {
            try
            {
                var response = _dealAthletePaymentsService.PayDealAthletePayment(companyID, clientID, dealID, employeeID, paymentAmount, paymentDate, payDealAmount);
                return response.errors.Any() ? JsonError(data: response.errors) : JsonSuccess();
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult SaveDealAthletePayments(int companyID, string clientID, long dealID, List<DealAthletePaymentDTO> dealAthletePayments)
        {
            try
            {
                var response = _dealAthletePaymentsService.SaveDealAthletePayments(companyID, clientID, dealID, dealAthletePayments ?? new List<DealAthletePaymentDTO>());
                if (response.errors.Any()) return JsonError(message: response.errors.Values.First());
                return new ContentResult()
                {
                    ContentType = "application/json",
                    ContentEncoding = Encoding.UTF8,
                    Content = JsonConvert.SerializeObject(new { data = response, status = "Success" }, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() })
                };
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult UpdateDealAthletePayment(DealAthletePaymentDTO dealAthletePayment)
        {
            try
            {
                var response = _dealAthletePaymentsService.UpdateDealAthletePayment(dealAthletePayment);
                return response.errors.Any() ? JsonError(data: response.errors) : JsonSuccess(data: response.dealAthletePayment);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public ActionResult UpdateDealAthletePaymentStatus(int companyID, string clientID, long dealID, string employeeID, short status)
        {
            try
            {
                var response = _dealAthletePaymentsService.UpdateDealAthletePaymentStatus(companyID, clientID, dealID, employeeID, status);
                return JsonSuccess(data: response);
            }
            catch (Exception exception)
            {
                return JsonInternalServerError(exception);
            }
        }

        public ActionResult GetDealAthletePaymentsTableData(int companyID, string clientID, long dealID)
        {
            try
            {
                var tableInfo = _dealAthletePaymentsService.GetDealAthletePaymentTableData(companyID, clientID, dealID, new TableFilter());
                tableInfo.Query = tableInfo.Query;
                return JsonSuccess(data: new TableFilterData<DealAthletePaymentDTO>
                {
                    Data = tableInfo.Query.ToList(),
                    FilteredEntries = tableInfo.FilteredEntries,
                    TotalEntries = tableInfo.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }
        #endregion

        #region Private Methods
        private List<EnumCodeText> GetEnumCodeAndText(Type type)
        {
            var list = new List<EnumCodeText>();
            foreach (var o in Enum.GetValues(type))
            {
                var item = new EnumCodeText()
                {
                    Code = (int)o,
                    Text = FieldTranslation.GetEnumDescription(type, (int)o)
                };
                list.Add(item);
            };
            return list;
        }
        #endregion
    }
}