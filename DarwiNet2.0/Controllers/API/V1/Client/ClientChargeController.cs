using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Client;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class ClientChargeController : TWApiController
    {
        [Route("api/ClientCharge")]
        [HttpGet]
        public IHttpActionResult GetClientCharge(string token, int companyId, string clientId, string divisionId, string chargeType)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    ClientCharge dbClientCharge = db.ClientCharges.Where(x => x.CompanyID == companyId && x.ClientID == clientId && x.DivisionID == divisionId && x.ChargeType == chargeType).FirstOrDefault();

                    if (dbClientCharge == null)
                    {
                        return NotFound();
                    }

                    ClientChargeModel clientCharge = ClientChargeModel.CopyClientCharge(dbClientCharge);

                    return Json(clientCharge);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}