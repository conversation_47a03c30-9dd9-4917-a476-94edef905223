using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Client;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class ClientAddressController : TWApiController
    {
        [Route("api/ClientAddress")]
        [HttpGet]
        public IHttpActionResult GetClientAddress(string token, int companyId, string clientId, string addressCode)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    ClientAddress dbClientAddress = db.ClientAddresses.Where(x => x.CompanyID == companyId && x.ClientID == clientId && x.AddressCode == addressCode).FirstOrDefault();

                    if (dbClientAddress == null)
                    {
                        return NotFound();
                    }

                    ClientAddressModel clientAddress = ClientAddressModel.CopyClientAddress(dbClientAddress);

                    return J<PERSON>(clientAddress);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}