using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Payroll;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class PayrollBatchController : TWApiController
    {
        [Route("api/PayrollBatch")]
        [HttpGet]
        public IHttpActionResult GetPayrollBatch(string token, int companyId, string batchNumber)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    PayrollBatch dbPayrollBatch = db.PayrollBatches.Where(x => x.CompanyID == companyId && x.BatchNumber == batchNumber).FirstOrDefault();

                    if (dbPayrollBatch == null)
                    {
                        return NotFound();
                    }

                    PayrollBatchModel payrollBatch = PayrollBatchModel.CopyPayrollBatch(dbPayrollBatch);

                    return Json(payrollBatch);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}