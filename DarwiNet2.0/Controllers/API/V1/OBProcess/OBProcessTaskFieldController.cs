using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.OBProcess;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class OBProcessTaskFieldController : TWApiController
    {
        [Route("api/OBProcessTaskField")]
        [HttpGet]
        public IHttpActionResult GetOBProcessTaskField(string token, int companyId, string employeeId, int taskId, string fName)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    OBProcessTaskField dBOBProcessTaskField = db.OBProcessTaskFields.Where(x => x.CompanyID == companyId && x.EmployeeID == employeeId && x.TaskID == taskId && x.FName == fName).FirstOrDefault();

                    if (dBOBProcessTaskField == null)
                    {
                        return NotFound();
                    }

                    OBProcessTaskFieldModel oBProcessTaskFieldModel = OBProcessTaskFieldModel.CopyOBProcessTaskField(dBOBProcessTaskField);

                    return Json(oBProcessTaskFieldModel);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}