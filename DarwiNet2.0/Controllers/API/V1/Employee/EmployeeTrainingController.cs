using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Employee;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class EmployeeTrainingController : TWApiController
    {
        [Route("api/EmployeeTraining")]
        [HttpGet]
        public IHttpActionResult GetEmployeeTraining(string token, int companyId, string employeeId, string trainingType)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    EmployeeTraining dbEmployeeTraining = db.EmployeeTrainings.Where(x => x.CompanyID == companyId && x.EmployeeID == employeeId && x.TrainingType == trainingType).FirstOrDefault();

                    if (dbEmployeeTraining == null)
                    {
                        return NotFound();
                    }

                    EmployeeTrainingModel employeeTraining = EmployeeTrainingModel.CopyEmployeeTraining(dbEmployeeTraining);

                    return J<PERSON>(employeeTraining);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}