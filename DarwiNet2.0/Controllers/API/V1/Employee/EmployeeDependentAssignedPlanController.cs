using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Employee;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class EmployeeDependentAssignedPlanController : TWApiController
    {
        [Route("api/EmployeeDependentAssignedPlan")]
        [HttpGet]
        public IHttpActionResult GetEmployeeDependentAssignedPlan(string token, int companyId, string employeeId, short year1, string planName)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    EmployeeDependentAssignedPlan dbEmployeeDependentAssignedPlan = db.EmployeeDependentAssignedPlans.Where(x => x.CompanyID == companyId && x.EmployeeID == employeeId && x.YEAR1 == year1 && x.PlanName == planName).FirstOrDefault();

                    if (dbEmployeeDependentAssignedPlan == null)
                    {
                        return NotFound();
                    }

                    EmployeeDependentAssignedPlanModel employeeAssignedPlan = EmployeeDependentAssignedPlanModel.CopyEmployeeDependentAssignedPlan(dbEmployeeDependentAssignedPlan);

                    return Json(employeeAssignedPlan);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}