using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Employee;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class EmployeeBeneficiaryController : TWApiController
    {
        [Route("api/EmployeeBeneficiary/All")]
        [HttpGet]
        public IHttpActionResult GetEmployeeBeneficiaries(string token, int companyId, string employeeId)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    List<EmployeeBeneficiary> dbEmployeeBeneficiaries = db.EmployeeBeneficiaries.Where(x => x.CompanyID == companyId && x.EmployeeID == employeeId).ToList();

                    if (dbEmployeeBeneficiaries.Count == 0)
                    {
                        return NotFound();
                    }

                    List<EmployeeBeneficiaryModel> employeeBeneficiaries = new List<EmployeeBeneficiaryModel>();
                    foreach (EmployeeBeneficiary employeeBeneficiary in dbEmployeeBeneficiaries)
                    {
                        employeeBeneficiaries.Add(EmployeeBeneficiaryModel.CopyEmployeeBeneficiary(employeeBeneficiary));
                    }

                    return Json(employeeBeneficiaries);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}