using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Employee;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class EmployeeDeductionController : TWApiController
    {
        [Route("api/EmployeeDeduction")]
        [HttpGet]
        public IHttpActionResult GetEmployeeDeduction(string token, int companyId, string employeeId, string deduction)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    EmployeeDeduction dbEmployeeDeduction = db.EmployeeDeductions.Where(x => x.CompanyID == companyId && x.EmployeeID == employeeId && x.Deduction == deduction).FirstOrDefault();

                    if (dbEmployeeDeduction == null)
                    {
                        return NotFound();
                    }

                    EmployeeDeductionModel employee = EmployeeDeductionModel.CopyEmployeeDeduction(dbEmployeeDeduction);

                    return <PERSON><PERSON>(employee);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }

        [Route("api/EmployeeDeduction/All")]
        [HttpGet]
        public IHttpActionResult GetEmployeeDeductions(string token, int companyId, string employeeId)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    List<EmployeeDeduction> dbEmployeeDeductions = db.EmployeeDeductions.Where(x => x.CompanyID == companyId && x.EmployeeID == employeeId).ToList();

                    if (dbEmployeeDeductions.Count == 0)
                    {
                        return NotFound();
                    }

                    List<EmployeeDeductionModel> employees = new List<EmployeeDeductionModel>();
                    foreach (EmployeeDeduction employeeDeduction in dbEmployeeDeductions)
                    {
                        employees.Add(EmployeeDeductionModel.CopyEmployeeDeduction(employeeDeduction));
                    }

                    return Json(employees);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}