using DarwiNet2._0.Data;
using DarwiNet2._0.Models.API.V1.Client;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace DarwiNet2._0.Controllers.API
{
    public class CompanyController : TWApiController
    {
        [Route("api/Company/All")]
        [HttpGet]
        public IHttpActionResult GetCompanies(string token)
        {
            if (!CheckToken(token))
            {
                return Unauthorized();
            }

            try
            {
                using (DnetEntities db = new DnetEntities())
                {
                    List<Company> dbCompanies = db.Companies.ToList();

                    if (dbCompanies.Count == 0)
                    {
                        return NotFound();
                    }

                    List<CompanyModel> companyModels = new List<CompanyModel>();
                    foreach (Company company in dbCompanies)
                    {
                        companyModels.Add(CompanyModel.CopyCompany(company));
                    }

                    return Json(companyModels);
                }
            }
            catch
            {
                return InternalServerError();
            }
        }
    }
}