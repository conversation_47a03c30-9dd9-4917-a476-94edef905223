using DarwiNet2._0.Models.API;
using DarwiNet2._0.Providers.API;
using DarwiNet2._0.Utilities;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Web.Http.ModelBinding;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Controllers.API
{
    [RoutePrefix("api/SecurityQuestions")]
    public class SecurityQuestionsController : ApiController
    {
        private DnetEntities _dbContext;

        public SecurityQuestionsController()
        {
            _dbContext = new DnetEntities();
        }

        [HttpGet]
        public IHttpActionResult Index()
        {
            try
            {
                using (var securityQuestionsProvider = new SecurityQuestionsProvider(_dbContext))
                {
                    var securityQuestions = securityQuestionsProvider.GetQuestions();
                    return Ok(securityQuestions);
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return InternalServerError();
            }
        }

        [HttpPost]
        public IHttpActionResult Post([FromBody] SecurityAnswersDTO securityAnswers)
        {
            try
            {
                using (var securityQuestionsProvider = new SecurityQuestionsProvider(_dbContext))
                {
                    ModelStateDictionary errors;
                    var valid = securityQuestionsProvider.SaveAnswers(securityAnswers, out errors);

                    if (valid)
                        return Ok();

                    return BadRequest(errors);
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
                return InternalServerError();
            }
        }
    }
}
