using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DarwiNet2._0.Data;
using Kendo.Mvc.UI;
using Kendo.Mvc.Extensions;
using DarwiNet2._0.DNetSynch;
using Newtonsoft.Json.Linq;


namespace DarwiNet2._0.Controllers
{
    [IsSessionActive]
    public class EmployeeSetupController : Controller
    {
        private DnetEntities _dbContext;
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            _dbContext = new DnetEntities();
            //this.dbContext = new DnetEntities();
            base.Initialize(requestContext);
        }
        protected override void Dispose(bool disposing)
        {
            _dbContext.Dispose();
            base.Dispose(disposing);
        }
        public ActionResult EmployeeInfo()
        {
            return View();
        }

        public ActionResult Calendar()
        {
            return View();
        }
        //public JsonResult getCalendarEntries(bool PTO, bool WS, bool PS, bool Hol, bool RD, bool BD )
        public JsonResult getCalendarEntries()
        {
            var calendarEntries = new List<CalendarEntries>();

            var i = 1;
            var ees = _dbContext.Employees.Where(ee => !ee.Inactive && ee.ClientID == GlobalVariables.Client && ee.EmployeeID == GlobalVariables.EmployeeID).ToList();
            //restrict list by Dept/Div Security
            var securityEEs = GlobalVariables.SecurityEmployees;
            ees = ees.Where(ee => securityEEs.Any(esc => esc == ee.EmployeeID)).ToList();

            #region EmployeeBirthdays
            foreach (var thisEe in ees)
            {
                if (thisEe.BirthDate != null)
                {
                    calendarEntries.Add(
                    new CalendarEntries
                    {
                        TaskID = i,
                        Description = thisEe.FirstName + " " + thisEe.LastName + "'s - Birthday is today.",
                        Title = thisEe.FirstName + " " + thisEe.LastName + "'s - Birthday",
                        Start = new DateTime(thisEe.BirthDate.Value.Year, thisEe.BirthDate.Value.Month, thisEe.BirthDate.Value.Day),
                        End = new DateTime(thisEe.BirthDate.Value.Year, thisEe.BirthDate.Value.Month, thisEe.BirthDate.Value.Day),
                        IsAllDay = true,
                        RecurrenceRule = "FREQ=YEARLY",
                        OwnerID = 1
                    }
                    );
                }
                i++;
            }
            #endregion
            #region EmployeeReviews
            foreach (var thisEe in ees)
            {
                if (thisEe.NextReviewDate != null)
                {
                    calendarEntries.Add(
                    new CalendarEntries
                    {
                        TaskID = i,
                        Description = thisEe.FirstName + " " + thisEe.LastName + "'s - Review is today.",
                        Title = thisEe.FirstName + " " + thisEe.LastName + "'s - Review",
                        Start = new DateTime(thisEe.NextReviewDate.Value.Year, thisEe.NextReviewDate.Value.Month, thisEe.NextReviewDate.Value.Day),
                        End = new DateTime(thisEe.NextReviewDate.Value.Year, thisEe.NextReviewDate.Value.Month, thisEe.NextReviewDate.Value.Day),
                        IsAllDay = true,
                        RecurrenceRule = null,
                        OwnerID = 4
                    }
                    );
                }
                i++;
            }
            #endregion
            #region EmployeePTO

            foreach (var thisEe in ees)
            {
                var eePTO =
                    _dbContext.PTORequests.Where(
                        epto =>
                            epto.Status == TimeRequestStatus.Accepted && epto.CompanyID == GlobalVariables.CompanyID &&
                            epto.EmployeeID == thisEe.EmployeeID).ToList();
                foreach (var thisPto in eePTO)
                {
                    if (thisPto.AllDay)
                    {
                        calendarEntries.Add(
                            new CalendarEntries
                            {
                                TaskID = i,
                                Description = thisEe.FirstName + " " + thisEe.LastName + " - PTO.",
                                Title = thisEe.FirstName + " " + thisEe.LastName + " - PTO",
                                Start = thisPto.StartDate,
                                End = thisPto.EndDate,
                                IsAllDay = true,
                                RecurrenceRule = null,
                                OwnerID = 5
                            }
                            );
                    }
                    else
                    {
                        var startHour = 0;
                        var startMinute = 0;
                        var endHour = 0;
                        var endMinute = 0;

                        #region StartTime

                        if (thisPto.StartTime.Contains("AM"))
                        {
                            var tmpString = thisPto.StartTime.Replace("AM", "").Split(':').ToArray();
                            startHour = Convert.ToInt32(tmpString[0]);
                            if (startHour == 12) startHour = 0;
                            startMinute = Convert.ToInt32(tmpString[1]);
                        }
                        if (thisPto.StartTime.Contains("PM"))
                        {
                            var tmpString = thisPto.StartTime.Replace("PM", "").Split(':').ToArray();
                            startHour = Convert.ToInt32(tmpString[0]);
                            if (startHour != 12) startHour = startHour + 12;
                            startMinute = Convert.ToInt32(tmpString[1]);
                        }

                        #endregion

                        #region EndTime

                        if (thisPto.EndTime.Contains("AM"))
                        {
                            var tmpString = thisPto.EndTime.Replace("AM", "").Split(':').ToArray();
                            endHour = Convert.ToInt32(tmpString[0]);
                            if (endHour == 12) startHour = 0;
                            endMinute = Convert.ToInt32(tmpString[1]);
                        }
                        if (thisPto.EndTime.Contains("PM"))
                        {
                            var tmpString = thisPto.EndTime.Replace("PM", "").Split(':').ToArray();
                            endHour = Convert.ToInt32(tmpString[0]);
                            if (endHour != 12) endHour = endHour + 12;
                            endMinute = Convert.ToInt32(tmpString[1]);
                        }

                        #endregion

                        var startTime = new DateTime(thisPto.StartDate.Year, thisPto.StartDate.Month,
                            thisPto.StartDate.Day, startHour, startMinute, 0);
                        var endTime = new DateTime(thisPto.EndDate.Year, thisPto.EndDate.Month, thisPto.EndDate.Day,
                            endHour, endMinute, 0);
                        calendarEntries.Add(
                            new CalendarEntries
                            {
                                TaskID = i,
                                Description = thisEe.FirstName + " " + thisEe.LastName + " - PTO.",
                                Title = thisEe.FirstName + " " + thisEe.LastName + " - PTO",
                                Start = DateTime.SpecifyKind(startTime, DateTimeKind.Utc),
                                End = DateTime.SpecifyKind(endTime, DateTimeKind.Utc),
                                IsAllDay = false,
                                RecurrenceRule = null,
                                OwnerID = 5
                            }
                            );
                    }
                    i++;
                }
            }

            #endregion
            #region Holidays
            var clientHolidays =
                    _dbContext.ClientHolidays.Where(
                        ch => ch.CompanyID == GlobalVariables.CompanyID && ch.ClientID == GlobalVariables.Client)
                        .ToList();
            foreach (var thisHol in clientHolidays)
            {
                var holidayName = GetHolidayName(thisHol.HolidayID);
                var theDates = _dbContext.TallyCalendars.Where(tc => tc.HolidayName.Contains(holidayName)).ToList();
                foreach (var thisDate in theDates)
                {
                    calendarEntries.Add(
                    new CalendarEntries
                    {
                        TaskID = i,
                        Description = holidayName,
                        Title = holidayName,
                        Start = thisDate.TheDate,
                        End = thisDate.TheDate,
                        IsAllDay = true,
                        RecurrenceRule = null,
                        OwnerID = 6
                    }
                    );

                    i++;
                }
            }
            #endregion

            #region CustomEvents

            List<CalendarCustomEvent> theEvents = new List<CalendarCustomEvent>();
            try
            {
                theEvents = _dbContext.CalendarCustomEvents.Where(cps => cps.CompanyID == GlobalVariables.CompanyID && cps.ClientID == GlobalVariables.Client && (string.IsNullOrEmpty(cps.EmployeeID) || cps.EmployeeID == GlobalVariables.EmployeeID)).ToList();
                foreach (var thisEvent in theEvents)
                {
                    DateTime? d1 = thisEvent.EventStartTime;
                    DateTime? d2 = thisEvent.EventEndTime;
                    DateTime day = (d1 == null) ? (d2 == null) ? DateTime.Today : (d2 ?? DateTime.Today).Date : (d1 ?? DateTime.Today).Date;
                    bool isAllDay = thisEvent.IsAllDay ?? true;
                    byte dType = thisEvent.DayType ?? CalendarDayType.WorkDay;
                    calendarEntries.Add(
                    new CalendarEntries
                    {
                        TaskID = (-1) * thisEvent.ID,
                        Description = DefaultEventDescription(thisEvent.Description, d1 ?? day, d2 ?? day, isAllDay, dType),
                        Title = thisEvent.Title,
                        Start = d1 ?? day,
                        End = d2 ?? day,
                        IsAllDay = isAllDay,
                        RecurrenceRule = null,
                        OwnerID = MapOwnerID(dType)
                    }
                    );
                    i++;
                }
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
            }
            #endregion

            return Json(calendarEntries, JsonRequestBehavior.AllowGet);
        }

        public string CreateCustomCalendar(string models)
        {
            if (models != null)
            {
                bool workDay;
                var data = JArray.Parse(models);
                DateTime start = (DateTime)data[0]["Start"];
                DateTime end = (DateTime)data[0]["End"];
                DateTime day = start.Date;
                byte dayType = ConvertOwnerID((int)data[0]["OwnerID"], out workDay);
                var test12 = (int)data[0]["TaskID"];

                var thisItem = new CalendarCustomEvent
                {
                    CompanyID = GlobalVariables.CompanyID,
                    ClientID = GlobalVariables.Client,
                    EmployeeID = GlobalVariables.EmployeeID,
                    Day = day,
                    Description = (string)data[0]["Description"],
                    EventStartTime = (DateTime)data[0]["Start"],
                    EventEndTime = (DateTime)data[0]["End"],
                    IsAllDay = (bool)data[0]["IsAllDay"],
                    DayType = dayType,
                    WorkDay = workDay,
                    Title = (string)data[0]["Title"]
                };
                _dbContext.CalendarCustomEvents.Add(thisItem);
                _dbContext.SaveChanges();
                int newId = thisItem.ID;
                data[0]["TaskID"] = ((-1) * newId).ToString();
                models = data.ToString();
            }


            return models;
        }

        public string UpdateCustomCalendar(string models)
        {
            if (models != null)
            {
                bool workDay;
                var data = JArray.Parse(models);
                DateTime start = (DateTime)data[0]["Start"];
                DateTime end = (DateTime)data[0]["End"];
                DateTime day = start.Date;
                byte dayType = ConvertOwnerID((int)data[0]["OwnerID"], out workDay);
                int id = (-1) * ((int)data[0]["TaskID"]);

                var thisItem = _dbContext.CalendarCustomEvents.FirstOrDefault(ce => ce.ID == id && ce.EmployeeID == GlobalVariables.EmployeeID);
                if (thisItem != null)
                {
                    thisItem.Description = (string)data[0]["Description"];
                    thisItem.EventStartTime = (DateTime)data[0]["Start"];
                    thisItem.EventEndTime = (DateTime)data[0]["End"];
                    thisItem.IsAllDay = (bool)data[0]["IsAllDay"];
                    thisItem.DayType = dayType;
                    thisItem.WorkDay = workDay;
                    thisItem.Title = (string)data[0]["Title"];
                    _dbContext.SaveChanges();
                }
                // else models = string.Empty;
            }
            return models;
        }

        public string RemoveCustomCalendar(string models)
        {
            if (models != null)
            {
                var data = JArray.Parse(models);
                int id = (-1) * ((int)data[0]["TaskID"]);

                var thisItem = _dbContext.CalendarCustomEvents.FirstOrDefault(ce => ce.ID == id && ce.EmployeeID == GlobalVariables.EmployeeID);
                if (thisItem != null)
                {
                    _dbContext.CalendarCustomEvents.Remove(thisItem);
                    _dbContext.SaveChanges();
                }
                //   else models = string.Empty;
            }


            return models;
        }

        public static string GetHolidayName(byte holiday)
        {
            var description = string.Empty;

            switch (holiday)
            {
                case USHoliday.NewYearsDay:
                    description = "New Years Day";
                    break;
                case USHoliday.MartinLutherKing:
                    description = "Martin Luther King Day";
                    break;
                case USHoliday.PresidentDay:
                    description = "Presidents Day";
                    break;
                case USHoliday.MemorialDay:
                    description = "Memorial Day";
                    break;
                case USHoliday.Juneteenth:
                    description = "Juneteenth";
                    break;
                case USHoliday.IndependenceDay:
                    description = "Fourth of July";
                    break;
                case USHoliday.LaborDay:
                    description = "Labor Day";
                    break;
                case USHoliday.ColumbusDay:
                    description = "Columbus Day";
                    break;
                case USHoliday.VeteransDay:
                    description = "Veterans Day";
                    break;
                case USHoliday.ThanksgivingDay:
                    description = "Thanksgiving Day";
                    break;
                case USHoliday.ChristmasDay:
                    description = "Christmas Day";
                    break;
                case USHoliday.ChristmasEve:
                    description = "Christmas Eve";
                    break;
                case USHoliday.DayafterThanksgiving:
                    description = "Black Friday";
                    break;
                case USHoliday.NewYearsEve:
                    description = "New Years Eve";
                    break;
            }
            return description;
        }

        private int MapOwnerID(short? daytype)
        {
            switch (daytype)
            {
                case CalendarDayType.Holiday:
                    return 6;
                    break;
                case CalendarDayType.PTO:
                    return 5;
                    break;
                case CalendarDayType.WorkDay:
                    return 2;
                    break;
                default:
                    return 7;
                    break;
            }
        }

        private byte ConvertOwnerID(int id, out bool workDay)
        {
            workDay = true;
            switch (id)
            {
                case 6:
                    workDay = false;
                    return CalendarDayType.Holiday;
                    break;
                case 5:
                    return CalendarDayType.PTO;
                    break;
                case 2:
                    return CalendarDayType.WorkDay;
                    break;
                default:
                    return CalendarDayType.Special;
                    break;
            }
        }

        private string DefaultEventDescription(string descr, DateTime d1, DateTime d2, bool isAllDay, short dType)
        {
            string result = descr;
            if (!string.IsNullOrEmpty(result)) return result;
            result = (isAllDay) ? "for all day" : "from " + d1.ToString("hh:mm") + " to " + d2.ToString("hh:mm");
            switch (dType)
            {
                case CalendarDayType.Holiday:
                    result = "Holiday " + result;
                    break;
                default:
                    result = "Custom Event " + result;
                    break;
            }
            return result;
        }
    }
}