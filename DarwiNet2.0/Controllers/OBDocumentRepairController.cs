using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.ViewModels;
using DataDrivenViewEngine.Models.Core;
using iTextSharp.text.pdf;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web.Mvc;
using DarwiNet2._0.Data;
using EO.Pdf.Internal;

namespace DarwiNet2._0.Controllers
{
    [IsSessionActive]
    public class OBDocumentRepairController : Controller
    {
        private DnetEntities dbContext;



        /// <summary>
        /// 
        /// </summary>
        /// <param name="disposing"></param>
        protected override void Dispose(bool disposing)
        {
            dbContext.Dispose();
            base.Dispose(disposing);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestContext"></param>
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            dbContext = new CompanyContextFactory().GetContextPerRequest();
            base.Initialize(requestContext);
        }


        // GET: OBDocumentRepair
        public ActionResult Repair()
        {
            var documents = dbContext.OBDocuments.Where(d => d.DocumentCode.StartsWith("CD")).OrderBy(d => d.CompanyID).ThenBy(d => d.DocumentCode).ToList();
            List<OBDocumentsRepairVM> repaireds = new List<OBDocumentsRepairVM>();
            foreach(var doc in documents)
            {
                OBDocumentsRepairVM repaired = new  OBDocumentsRepairVM();
                repaired.CompanyID = doc.CompanyID;
                repaired.DocumentID = doc.DocumentID;
                repaired.DocumentName = doc.Document;
                repaired.Description = RemapDocumentFile(doc.DocumentID, doc.CompanyID, doc.DBody);
                repaireds.Add(repaired);
            }
            ViewBag.Documents = repaireds;
            return View();
        }

        public ActionResult RollDownProfiles()
        {
            var documents = dbContext.OBDocuments.Where(d => d.DocumentCode.StartsWith("CD")).OrderBy(d => d.CompanyID).ThenBy(d => d.DocumentCode).ToList();
            List<OBDocumentsRepairVM> repaireds = new List<OBDocumentsRepairVM>();
            foreach(var doc in documents)
            {
                OBDocumentsRepairVM repaired = new  OBDocumentsRepairVM();
                repaired.CompanyID = doc.CompanyID;
                repaired.DocumentID = doc.DocumentID;
                repaired.DocumentName = doc.Document;
                repaired.Description = RollDownDocumentMap(doc.DocumentID, doc.CompanyID);
                repaireds.Add(repaired);
            }
            ViewBag.Documents = repaireds;
            return View();
        }

        public ActionResult ResignEEDocuments()
        {
            List<int> i9s = I9Documents();
            short signType;
            string name, msg, client;
            List<OBEEDocumentRepairVM> repaireds = dbContext.OBProcessDocuments.Where(d => d.EEVerified && d.DPath == "OnBoarding" && d.EEVerifiedDate != null && !string.IsNullOrEmpty(d.EEVerifiedBy)).OrderBy(d => d.CompanyID).ThenBy(d => d.EmployeeID).ThenBy(d => d.DocumentID).Select(d => new OBEEDocumentRepairVM {CompanyID = d.CompanyID, EmployeeID = d.EmployeeID, EmployeeName = string.Empty, DocumentID = d.DocumentID, DocumentName = d.DocumentName, Description = string.Empty}).ToList();
            repaireds = repaireds.Where(d => !i9s.Any(i => i == d.DocumentID)).ToList();
            //List<OBEEDocumentRepairVM> repaireds = new List<OBEEDocumentRepairVM>();
            foreach(var rdoc in repaireds)
            {
                string result = string.Empty;
                var doc = dbContext.OBProcessDocuments.FirstOrDefault(x => x.CompanyID == rdoc.CompanyID && x.EmployeeID == rdoc.EmployeeID && x.DocumentID == rdoc.DocumentID);
                int profile = DocumentProfile(doc, out signType, out name, out client);
                rdoc.EmployeeName = name;
                if (profile > 0)
                {
                    msg = ReplaceSignature(client, profile, signType, name, ref doc);
                    if (string.IsNullOrEmpty(msg))
                    {
                        msg = "OB document resigned -> ";
                        result = RepairPermanentDoc(doc);
                        if (!string.IsNullOrEmpty(result))
                        {
                            msg += "Document for permanent employee " + result;
                        }
                        else
                        {
                            msg += "Document for permanent employee resigned -> ";
                            result = RepairLibraryDoc(doc);
                            if (!string.IsNullOrEmpty(result))
                            {
                                msg += "Library EE document " + result;
                            }
                            else msg += "Library EE document updated.";
                        }
                    }
                    else msg = "OB document " + msg;
                }
                else msg = "Error: Cannot find employee";
                rdoc.Description = msg;
            }
            dbContext.SaveChanges();
            ViewBag.Documents = repaireds;
            return View();
        }

        public ActionResult ResignI9()
        {
            List<int> i9s = I9Documents();
            short signType;
            string name, msg, client;
            List<OBEEDocumentRepairVM> repaireds = dbContext.OBProcessDocuments.Where(d => d.EEVerified && d.DPath == "OnBoarding" && d.EEVerifiedDate != null && !string.IsNullOrEmpty(d.EEVerifiedBy)).OrderBy(d => d.CompanyID).ThenBy(d => d.EmployeeID).ThenBy(d => d.DocumentID).Select(d => new OBEEDocumentRepairVM {CompanyID = d.CompanyID, EmployeeID = d.EmployeeID, EmployeeName = string.Empty, DocumentID = d.DocumentID, DocumentName = d.DocumentName, Description = string.Empty}).ToList();
            repaireds = repaireds.Where(d => i9s.Any(i => i == d.DocumentID)).ToList();
            //List<OBEEDocumentRepairVM> repaireds = new List<OBEEDocumentRepairVM>();
            foreach(var rdoc in repaireds)
            {
                string result = string.Empty;
                var doc = dbContext.OBProcessDocuments.FirstOrDefault(x => x.CompanyID == rdoc.CompanyID && x.EmployeeID == rdoc.EmployeeID && x.DocumentID == rdoc.DocumentID);
                int profile = DocumentProfile(doc, out signType, out name, out client);
                rdoc.EmployeeName = name;
                if (profile > 0)
                {
                    msg = ReplaceSignature(client, profile, signType, name, ref doc);
                    if (string.IsNullOrEmpty(msg))
                    {
                        msg = "OB I-9 resigned -> ";
                        result = RepairPermanentDoc(doc);
                        if (!string.IsNullOrEmpty(result))
                        {
                            msg += "Permanent employee I-9 " + result;
                        }
                        else
                        {
                            msg += "Permanent employee I-9 resigned -> ";
                            result = RepairLibraryDoc(doc);
                            if (!string.IsNullOrEmpty(result))
                            {
                                msg += "Library EE I-9 document " + result;
                            }
                            else msg += "Library EE I-9 document updated.";
                        }
                    }
                    else msg = "OB I-9 " + msg;
                }
                else msg = "Error: Cannot find employee";
                rdoc.Description = msg;
            }
            dbContext.SaveChanges();
            ViewBag.Documents = repaireds;
            return View();
        }

        private string RollDownDocumentMap(int id, int compId)
        {
            RefreshAssignedDocumentMapping(compId, id);
            return "Rolled Down";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="filedata"></param>
        
        private string RemapDocumentFile(int id, int compId, byte[] filedata)
        {
            string msg = string.Empty;
            bool result = true;
            if (result)
            {
                try
                {
                    PdfReader reader = new PdfReader(filedata);
                    AcroFields form = reader.AcroFields;

                    List<string> existMap = dbContext.OBDocumentMappings
                    .Where(x => x.CompanyID == compId && x.DocumentID == id)
                    .Select(x => x.FormName)
                    .ToList();

                    List<string> newMap = new List<string>();
                    OBDocumentMapping mapping = null;
                    int cnt = 0;
                    int nf = 0;
                    int ne = 0;
                    int uf = 0;
                    foreach (KeyValuePair<string, AcroFields.Item> kvp in form.Fields)
                    {
                        nf++;
                        int fieldType = form.GetFieldType(kvp.Key);

                        if (fieldType != AcroFields.FIELD_TYPE_PUSHBUTTON)
                        {
                            string formFldName = form.GetTranslatedFieldName(kvp.Key);

                            if (dbContext.OBDocumentMappings
                                .Where(x => x.CompanyID == compId && x.DocumentID == id && x.FormName == formFldName)
                                .Select(x => x.FormName).Count() == 0)
                            {
                                newMap.Add(formFldName);

                                if (!existMap.Contains(formFldName))
                                {
                                    string exportValue = string.Empty;

                                    if (fieldType == AcroFields.FIELD_TYPE_CHECKBOX)
                                    {
                                        exportValue = GetCheckBoxExportValue(kvp.Value);
                                    }

                                    OnBoardingDocumentDefaultField mapField = MappedDBField(formFldName);

                                    if (mapField != null)
                                    {
                                        cnt++;
                                        string dbvalues = string.Empty;
                                        string buf = GetFieldOptions(mapField.DB_Table, mapField.DB_Field, out dbvalues);

                                        if (string.IsNullOrEmpty(exportValue))
                                        {
                                            exportValue = buf;
                                        }
                                        else
                                        {
                                            if (fieldType == AcroFields.FIELD_TYPE_CHECKBOX && string.IsNullOrEmpty(dbvalues)) dbvalues = "TRUE";
                                        }

                                        mapping = new OBDocumentMapping
                                        {
                                            CompanyID = compId,
                                            DocumentID = id,
                                            FormName = formFldName,
                                            FormFieldType = (short)fieldType,
                                            DB_Table = mapField.DB_Table,
                                            DB_Field = mapField.DB_Field,
                                            DB_RecNum = 0,
                                            DB_Value = dbvalues,
                                            FieldPresentation = exportValue,
                                            FieldStatus = OBDocFieldStatus.ReadOnly,
                                            UseInWF = true,
                                            WF_SeqNbr = cnt,
                                            WF_FieldLabel = formFldName,
                                            WF_FieldType = GetWebFormType(fieldType)
                                        };
                                    }
                                    else
                                    {
                                        mapping = new OBDocumentMapping
                                        {
                                            CompanyID = compId,
                                            DocumentID = id,
                                            FormName = formFldName,
                                            FormFieldType = (short)fieldType,
                                            DB_Table = string.Empty,
                                            DB_Field = string.Empty,
                                            DB_RecNum = 0,
                                            DB_Value = string.Empty,
                                            FieldPresentation = exportValue,
                                            FieldStatus = OBDocFieldStatus.EE_Editable,
                                            UseInWF = false,
                                            WF_SeqNbr = 0,
                                            WF_FieldLabel = string.Empty,
                                            WF_FieldType = GetWebFormType(fieldType)
                                        };
                                    }

                                    try
                                    {
                                        dbContext.OBDocumentMappings.Add(mapping);
                                        dbContext.SaveChanges();
                                        uf++;
                                    }
                                    catch (Exception e)
                                    {
                                        ne++;
                                    }
                                }
                            }
                        }
                    }

                    dbContext.SaveChanges();
                    msg = (nf > 0) ? (uf > 0) ? "Repaired (" + uf.ToString() + " fields added" : "Skip: mapping exists" : "No form fields";
                    msg += (ne > 0) ? " (" + ne.ToString() + " duplicate fields)" : "";
                    reader.Close();
                    //RefreshAssignedDocumentMapping(compId, id);
                }
                catch(Exception e)
                {
                    msg = "Error: " + e.Message;
                }
                finally
                {
                    
                }
            }
            else msg = "Skip: mapping exists";
            return msg;
        }


        private int DocumentProfile(OBProcessDocument doc, out short signType, out string name, out string client)
        {
            int result = 0;
            signType = OBSignatureType.Name_IP_Date;
            name = string.Empty;
            client = string.Empty;
            var monitor = dbContext.OBProcessMonitors.FirstOrDefault(x => x.CompanyID == doc.CompanyID && x.EmployeeID == doc.EmployeeID);
            if (monitor != null)
            {
                result = monitor.SetupID ?? 0;
                signType = monitor.Signature ?? OBSignatureType.Name_IP_Date;
                name = monitor.EmployeeName;
                client = monitor.ClientID;
            }           
            return result;
        }

        private string RepairPermanentDoc(OBProcessDocument doc)
        {
            string msg = string.Empty;
            var eedoc = dbContext.EmployeeOBDocuments.FirstOrDefault(x => x.CompanyID == doc.CompanyID && x.EmployeeID == doc.EmployeeID && x.DocumentID == doc.DocumentID);
            if (eedoc != null)
            {
                eedoc.DPath = doc.DPath;
                eedoc.DFile = doc.DFile;
                eedoc.DBody = doc.DBody;
                try
                {
                    dbContext.SaveChanges();
                }
                catch (Exception e)
                {
                    msg = "Error: " + e.Message;
                }
            }
            else msg = "Skip: Cannot find the document for permanent employee";
            return msg;
        }

        private string RepairLibraryDoc(OBProcessDocument doc)
        {
            string msg = string.Empty;
            int obfolder = OBLibraryFolder(doc.CompanyID);
            var eedocs = EELibraryDocuments(doc.CompanyID, doc.EmployeeID);
            var libdocs = dbContext.LibraryDocuments.Where(l => l.DocumentName == doc.DocumentName && l.FolderID == obfolder).ToList();
            var thedoc = libdocs.FirstOrDefault(x => eedocs.Any(l => l == x.id));
            if (thedoc != null)
            {
                msg = ReplaceDocFile(doc, LibraryFolder(doc.CompanyID), thedoc.SavedDocName);
            }
            else msg = "Skip: connot find the library file";
            return msg;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="filedata"></param>
        
        private string ReplaceSignature(string clientId, int profile, short signType, string name, ref OBProcessDocument doc)
        {
            string value;
            string msg = string.Empty;
            int compId = 0;
            int docId = 0;
            string doccode = string.Empty;
            bool result = doc != null && doc.DBody != null;
            if (result)
            {
                compId = doc.CompanyID;
                docId = doc.DocumentID;
                var origindoc = dbContext.OBDocuments.FirstOrDefault(x => x.DocumentID == docId);
                if (origindoc != null)
                {
                    doccode = origindoc.DocumentCode;
                    result = !string.IsNullOrEmpty(doccode);
                }
                else
                    result = false;
            }
            if (result)
            {
                result = false;
                try
                {
                    PdfReader reader = new PdfReader(doc.DBody);
                    AcroFields form = reader.AcroFields;

                    List<Code_Description> signFields = (doccode.StartsWith("CD")) ? dbContext.OBClientSetupDocumentMappings
                    .Where(x => x.CompanyID == compId && x.SetupID == profile && x.DocumentID == docId && x.DB_Table == OBDocSource.Signature)
                    .Select(x => new Code_Description {Code = x.FormName, Description = x.DB_Field} )
                    .ToList() :
                     dbContext.OnBoardingDocumentMappings
                    .Where(x => x.DocumentCode == doccode && x.DB_Table == OBDocSource.Signature)
                    .Select(x => new Code_Description {Code = x.FormName, Description = x.DB_Field} )
                    .ToList();

                    if (signFields.Count() > 0)
                    {
                        using (MemoryStream stream = new MemoryStream())
                        {
                            PdfStamper stamper = new PdfStamper(reader, stream);

                            foreach (Code_Description fld in signFields)
                            {
                                switch (fld.Description)
                                {
                                    case OBFormSignFields.EE_Sign:
                                        value = (doc.EEVerified) ? BuildSignature(signType, name, doc.EEVerifiedIP, doc.EEVerifiedDate) : string.Empty;
                                        if (!result) result = !string.IsNullOrEmpty(value);
                                        break;
                                    case OBFormSignFields.EE_SignDate:
                                        value = (doc.EEVerifiedDate == null) ? string.Empty : (doc.EEVerifiedDate ?? DateTime.MinValue).ToString("MM/dd/yyyy");
                                        break;
                                    case OBFormSignFields.ER_Sign:
                                        value = BuildSignature(signType, GetUserName(doc.CCVerifiedBy), doc.CCVerifiedIP, doc.CCVerifiedDate);
                                        if (!result) result = !string.IsNullOrEmpty(value);
                                        break;
                                    case OBFormSignFields.ER_SignDate:
                                        value = (doc.CCVerifiedDate == null) ? string.Empty : (doc.EEVerifiedDate ?? DateTime.MinValue).ToString("MM/dd/yyyy");
                                        break;
                                    case OBFormSignFields.The_Year:
                                        value = DateTime.Now.ToString();
                                        break;
                                    default:
                                        value = string.Empty;
                                        break;
                                }
                                stamper.AcroFields.SetField(fld.Code, value);
                            }
                            stamper.Close();
                            doc.DBody = stream.ToArray();
                        }
                        if (result)
                        {
                            doc.DFile = FinalOBFormName(doc.DFile, doc.EmployeeID, doc.DocumentID);
                            doc.DPath = clientId + Path.AltDirectorySeparatorChar + Folders.OBDocuments;
                        }
                        else
                            msg = "Skip: Nobody has signed the document";
                        reader.Close();
                        dbContext.SaveChanges();
                    }
                    else msg = "Skip: the document doesn't include 'Signature' fields";
                }
                catch(Exception e)
                {
                    msg = "Error: " + e.Message;
                }
                finally
                {
                    
                }
            }
            else msg = "Skip: the document is empty";
            return msg;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>

        private string ParsePDFfiledName(string name)
        {
            string result = name;
            int position = result.LastIndexOf('.');

            if (position >= 0)
            {
                result = result.Substring(position + 1);
            }

            position = result.IndexOf('[');

            if (position >= 0)
            {
                result = result.Substring(0, position);
            }

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pdfField"></param>
        /// <returns></returns>
        private OnBoardingDocumentDefaultField MappedDBField(string pdfField)
        {
            OnBoardingDocumentDefaultField field = new OnBoardingDocumentDefaultField();

            field = dbContext.OnBoardingDocumentDefaultFields.FirstOrDefault(x => x.PdfName == ParsePDFfiledName(pdfField));

            if (field == null)
            {
                field = new OnBoardingDocumentDefaultField
                {
                    PdfName = pdfField,
                    DB_Field = string.Empty,
                    DB_Table = string.Empty,
                    SpecAction = 0
                };
            }

            return field;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private string GetCheckBoxExportValue(AcroFields.Item item)
        {
            try
            {
                PdfDictionary valueDict = item.GetValue(0);
                PdfDictionary appDict = valueDict.GetAsDict(PdfName.AP);

                if (appDict != null)
                {
                    PdfDictionary normalApp = appDict.GetAsDict(PdfName.N);

                    if (normalApp != null)
                    {
                        foreach (PdfName curKey in normalApp.Keys)
                        {
                            if (!PdfName.Off_.Equals(curKey) && !PdfName.OFF.Equals(curKey))
                            {
                                return curKey.ToString().TrimStart('/');
                            }
                        }
                    }
                }

                PdfName curVal = valueDict.GetAsName(PdfName.AS);

                if (curVal != null)
                {
                    return curVal.ToString().TrimStart('/');
                }

            }
            catch
            {
                return string.Empty;
            }

            return string.Empty;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tbl"></param>
        /// <param name="fld"></param>
        /// <param name="dbvalues"></param>
        /// <returns></returns>
        private string GetFieldOptions(string tbl, string fld, out string dbvalues)
        {
            dbvalues = string.Empty;
            string result = string.Empty;
            int idx;
            if (!string.IsNullOrEmpty(tbl) && !string.IsNullOrEmpty(fld))
            {
                OnBoardingField rec = dbContext.OnBoardingFields
                    .FirstOrDefault(x => x.PermanentTBL == tbl && x.PermanentFLD == fld && !string.IsNullOrEmpty(x.FValueOptions));

                if (rec != null)
                {
                    string[] options = rec.FValueOptions.Split(',');

                    if (int.TryParse(options[0], out idx))
                    {
                        for (var i = 0; i < options.Length; i += 2)
                        {
                            if (string.IsNullOrEmpty(dbvalues))
                            {
                                result += options[i + 1];
                            }
                            else
                            {
                                result += "|" + options[i + 1];
                            }

                            if (string.IsNullOrEmpty(dbvalues))
                            {
                                dbvalues += options[i];
                            }
                            else
                            {
                                dbvalues += "|" + options[i];
                            }
                        }
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pdfFormType"></param>
        /// <returns></returns>
        private byte GetWebFormType(int pdfFormType)
        {
            switch (pdfFormType)
            {
                case AcroFields.FIELD_TYPE_CHECKBOX:
                    return FieldTypes.CheckBox;

                case AcroFields.FIELD_TYPE_LIST:
                    return FieldTypes.DropDown;

                case AcroFields.FIELD_TYPE_NONE:
                    return 0;

                case AcroFields.FIELD_TYPE_PUSHBUTTON:
                    return 0;

                case AcroFields.FIELD_TYPE_RADIOBUTTON:
                    return FieldTypes.CheckBox;

                default:
                    return FieldTypes.TextBox;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="docId"></param>
        private void RefreshAssignedDocumentMapping(int compId, int docId)
        {
            dbContext.Database.ExecuteSqlCommand(
                "usp_OBDocument_Remap @CompanyID, @CompanyID, @DocumentID",
                new SqlParameter("CompanyID", GlobalVariables.CompanyID),
                new SqlParameter("DocumentID", docId)
            );
            //using (IDbCommand oaCommand = dbContext.Database.Connection.CreateCommand())
            //{
            //    oaCommand.CommandType = System.Data.CommandType.StoredProcedure;
            //    oaCommand.CommandText = "usp_OBDocument_Remap";

            //    IDbDataParameter oaParameterC = oaCommand.CreateParameter();
            //    oaParameterC.ParameterName = "@CompanyID";
            //    oaParameterC.DbType = DbType.Int32;
            //    oaParameterC.Value = compId;
            //    oaCommand.Parameters.Add(oaParameterC);

            //    IDbDataParameter oaParameterDoc = oaCommand.CreateParameter();
            //    oaParameterDoc.ParameterName = "@DocumentID";
            //    oaParameterDoc.DbType = DbType.Int32;
            //    oaParameterDoc.Value = docId;
            //    oaCommand.Parameters.Add(oaParameterDoc);

            //    oaCommand.ExecuteNonQuery();
            //}
            dbContext.SaveChanges();
        }

        private string BuildSignature(short signType, string name, string ip, DateTime? dt)
        {
            string result = string.Empty;
            //ip = ip;
            string signDate = (dt == null) ? string.Empty : ((DateTime)dt).ToString("d");
            switch (signType)
            {
                case OBSignatureType.Name:
                    result = name;
                    break;
                case OBSignatureType.Name_IP:
                    result = name + " " + ip;
                    break;
                case OBSignatureType.Name_Date:
                    result = name + " " + signDate;
                    break;
                case OBSignatureType.Name_IP_Date:
                    result = name + " " + ip + " " + signDate;
                    break;
            }
            return result;
        }

        string FinalOBFormName(string name, string emplId, int docId)
        {
            string result = name;
            string ee_name = dbContext.OBProcessMonitors.Where(u => u.CompanyID == GlobalVariables.CompanyID && u.EmployeeID == emplId).First().EmployeeName;
            string prompt = "_" + docId.ToString() + "_" + ee_name.Replace(' ', '_') + ".pdf";
            if (result.IndexOf(prompt) <= 0) result = result.Replace(".pdf", prompt);
            return result;
        }

        List<int> I9Documents()
        {
            return dbContext.OBDocuments.Where(x => x.DocumentCode == "I9").OrderBy(x => x.DocumentID).Select(x => x.DocumentID).Distinct().ToList();
        }

        private List<int> EELibraryDocuments(int compId, string ee)
        {
            return dbContext.DarwinetNotes.Where(x => x.CompanyID == compId && x.EmployeeID == ee).Select(x => x.AttachmentID ?? 0).ToList();
        }

        private int OBLibraryFolder(int compId)
        {
            int result = 5;
            var folder = dbContext.Libraries.FirstOrDefault(l => l.CompanyID == compId && l.FolderName == "On Boarding");
            if (folder == null) folder = dbContext.Libraries.FirstOrDefault(l => l.FolderName == "On Boarding");
            if (folder != null) result = folder.id;
            return result;
        }

        private string ReplaceDocFile(OBProcessDocument doc, string path, string name)
        {
            string result = string.Empty;
            var fname = Path.Combine(path, name);

            if (System.IO.File.Exists(fname))
            {
                System.IO.File.Delete(fname);
            }

            System.IO.File.WriteAllBytes(fname, doc.DBody);
            return result;
        }

        private string LibraryFolder(int compId)
        {
            string result = string.Empty;
            string dnetLocation = AppDomain.CurrentDomain.BaseDirectory;
            var assets = "Assets";
            var comp = dbContext.Companies.FirstOrDefault(c => c.CompanyID == compId);
            if (comp != null)
            {
                var path = Path.Combine(dnetLocation, assets, comp.DnetClientID + "." + comp.DnetCompanyID);
                result = Path.Combine(path, Folders.Library);
            }
            return result;
        }

        private string GetUserName(string id)
        {
            string result = id;
            var user = dbContext.Users.FirstOrDefault(x => x.UserID == id);
            if (user != null) result = user.Name;
            return result;
        }

    }
}