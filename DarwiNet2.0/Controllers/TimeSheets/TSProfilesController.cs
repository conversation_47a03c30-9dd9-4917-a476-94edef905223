using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;

namespace DarwiNet2._0.Controllers.TimeSheets
{
    [IsSessionActive]
    public class TSProfilesController : Controller
    {
        private DnetEntities dbContext;

        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            this.dbContext = new DnetEntities();
            base.Initialize(requestContext);
        }
        protected override void Dispose(bool disposing)
        {
            this.dbContext.Dispose();
            base.Dispose(disposing);
        }
        // GET: TSProfiles
        public ActionResult Index()
        {
            ViewBag.IsSampleCompany = false;
            if (GlobalVariables.Company == "***")
            {
                ViewBag.IsSampleCompany = true;
            }
            return View(this.dbContext.TimeSheetProfiles.Where(p => p.CompanyID == GlobalVariables.CompanyID).ToList());
        }

        public ActionResult TSProfiles_Read([DataSourceRequest] DataSourceRequest request)
        {
            return Json(GetProfiles().ToDataSourceResult(request));
        }

        public IEnumerable<TimeSheetProfile> GetProfiles()
        {
            return this.dbContext.TimeSheetProfiles.Where(p => p.CompanyID == GlobalVariables.CompanyID).Select(p => new TimeSheetProfile
            {
                CompanyID = GlobalVariables.CompanyID,
                ProfileID = p.ProfileID,
                ProfileName = p.ProfileName,
                ProfileType = p.ProfileType,
                SelectionType = p.SelectionType,
                PayPeriod = p.PayPeriod,
                DefDays = p.DefDays,
                DefWeeks = p.DefWeeks,
                DefHours = p.DefHours,
                MaxRate = p.MaxRate,
                MaxDayHours = p.MaxDayHours,
                MaxEEHours = p.MaxEEHours,
                MaxEEGrossAmount = p.MaxEEGrossAmount,
                PaycodeDecimals = p.PaycodeDecimals ?? 2,
                BenefitDecimals = p.BenefitDecimals ?? 2,
                DeductionDecimals = p.DeductionDecimals ?? 2,
                IncludeAllEE = p.IncludeAllEE,
                AllowModifiedRate = p.AllowModifiedRate,
                AllowModifiedRatePermanent = p.AllowModifiedRatePermanent,
                DepartmentStatus = p.DepartmentStatus,
                PositionStatus = p.PositionStatus,
                LocalTaxStatus = p.LocalTaxStatus,
                StateTaxStatus = p.StateTaxStatus,
                SutaStateStatus = p.SutaStateStatus,
                WCStatus = p.WCStatus,
                ShiftStatus = p.ShiftStatus,
                PayCodesSource = p.PayCodesSource,
                BenefitsSource = p.BenefitsSource,
                DeductionsSource = p.DeductionsSource,
                PrintSocSecOnBlankTS = p.PrintSocSecOnBlankTS,
                PrintSocSecOnReports = p.PrintSocSecOnReports,
                PrintRateOnBlankTS = p.PrintRateOnBlankTS,
                PrintRateOnReports = p.PrintRateOnReports,
                PrintYTDOnBlankTS = p.PrintYTDOnBlankTS,
                PrintYTDOnReports = p.PrintYTDOnReports,
                AllowTimeEntry = p.AllowTimeEntry,
                AllowEEDeptChg = p.AllowEEDeptChg,
                AllowEEPositionChg = p.AllowEEPositionChg,
                PEOApprovalRequired = p.PEOApprovalRequired,
                HideOfferedHours = p.HideOfferedHours, // 10/02/2017 DS TFS # 2795
                HideCheckNumber = p.HideCheckNumber, // 10/02/2017 DS TFS # 2795
                HideWeeks = p.HideWeeks, // 10/02/2017 DS TFS # 2795
                HideDays = p.HideDays, // 10/02/2017 DS TFS # 2795
                AutoSave = p.AutoSave // 04/30/2019 DS TFS # 4163
            });
        }

        public ActionResult Details(string id)
        {
            ViewBag.ProfileTypes = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetTypes>(), "Value", "Name", string.Empty);
            ViewBag.SelectionTypes = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetSelectionTypes>(), "Value", "Name", string.Empty);
            ViewBag.ColumnStatus = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetColumnStatus>(), "Value", "Name", string.Empty);
            ViewBag.CodeSource = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetCodeSource>(), "Value", "Name", string.Empty);
            ViewBag.PayPeriods = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enPayPeriods>(), "Value", "Name", string.Empty);
            List<Code_Description> selUsers;
            ViewBag.AvailablePEOUsers = AvailablePEOUsers("", out selUsers);
            ViewBag.SelectedPEOUsers = selUsers;
            return View(this.dbContext.TimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id));
        }

        public ActionResult Edit(string id)
        {
            TimeSheetProfile profile = this.dbContext.TimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id);
            List<Code_Description> selUsers;
            if (profile != null)
            {
                ViewBag.AvailablePEOUsers = AvailablePEOUsers(id, out selUsers);
                ViewBag.SelectedPEOUsers = selUsers;
            }
            else
            {
                ViewBag.AvailablePEOUsers = new List<Code_Description>();
                ViewBag.SelectedPEOUsers = new List<Code_Description>();
            }
            return View(profile);
        }

        [HttpPost]
        public ActionResult Edit(string id, TimeSheetProfile rec, FormCollection profileForm)
        {
            TimeSheetProfile profile = this.dbContext.TimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id);
            if (profile != null)
            {
                short defWeeks;
                short defDays = GetDefaultDays(rec.PayPeriod ?? PayPeriods.Empty, out defWeeks);
                profile.CompanyID = GlobalVariables.CompanyID;
                profile.ProfileID = rec.ProfileID; // profileForm["ProfileID"];
                profile.ProfileName = rec.ProfileName; // profileForm["ProfileName"];
                profile.ProfileType = rec.ProfileType; // Convert.ToInt16(profileForm["ProfileType"]);
                profile.SelectionType = rec.SelectionType; // Convert.ToInt16(profileForm["SelectionType"]); 
                profile.PayPeriod = rec.PayPeriod ?? PayPeriods.Empty; // Convert.ToInt16(profileForm["PayPeriod"]); 
                profile.DefDays = rec.DefDays ?? defDays; // (float)Convert.ToDouble(profileForm["DefDays"]); 
                profile.DefWeeks = rec.DefWeeks ?? defWeeks; // (float)Convert.ToDouble(profileForm["DefWeeks"]); 
                profile.DefHours = rec.DefHours ?? 0; // (float)Convert.ToDouble(profileForm["DefHours"]); 
                profile.MaxRate = rec.MaxRate; // (float)Convert.ToDouble(profileForm["MaxRate"]); 
                profile.MaxDayHours = rec.MaxDayHours ?? 0; // (float)Convert.ToDouble(profileForm["MaxDayHours"]); 
                profile.MaxEEHours = rec.MaxEEHours ?? 0; // (float)Convert.ToDouble(profileForm["MaxEEHours"]); 
                profile.MaxEEGrossAmount = rec.MaxEEGrossAmount ?? 0; // Convert.ToDecimal(profileForm["MaxEEGrossAmount"]); 
                profile.PaycodeDecimals = rec.PaycodeDecimals ?? 2; // Convert.ToInt16(profileForm["PaycodeDecimals"]); 
                profile.BenefitDecimals = rec.BenefitDecimals ?? 2; // Convert.ToInt16(profileForm["BenefitDecimals"]); 
                profile.DeductionDecimals = rec.DeductionDecimals ?? 2; // Convert.ToInt16(profileForm["DeductionDecimals"]);
                profile.IncludeAllEE = rec.IncludeAllEE; // !string.IsNullOrEmpty(profileForm["IncludeAllEE"]); 
                profile.AllowModifiedRate = rec.AllowModifiedRate; // !string.IsNullOrEmpty(profileForm["AllowModifiedRate"]); 
                profile.AllowModifiedRatePermanent = rec.AllowModifiedRatePermanent; // !string.IsNullOrEmpty(profileForm["AllowModifiedRatePermanent"]); 
                profile.DepartmentStatus = rec.DepartmentStatus ?? 0; // Convert.ToInt16(profileForm["DepartmentStatus"]); 
                profile.PositionStatus = rec.PositionStatus ?? 0; // Convert.ToInt16(profileForm["PositionStatus"]); 
                profile.LocalTaxStatus = rec.LocalTaxStatus ?? 0; // Convert.ToInt16(profileForm["LocalTaxStatus"]); 
                profile.StateTaxStatus = rec.StateTaxStatus ?? 0; // Convert.ToInt16(profileForm["StateTaxStatus"]); 
                profile.SutaStateStatus = rec.SutaStateStatus ?? 0; // Convert.ToInt16(profileForm["SutaStateStatus"]); 
                profile.WCStatus = rec.WCStatus ?? 0; // Convert.ToInt16(profileForm["WCStatus"]); 
                profile.ShiftStatus = rec.ShiftStatus ?? 0; // Convert.ToInt16(profileForm["ShiftStatus"]); 
                profile.PayCodesSource = rec.PayCodesSource ?? 0; // Convert.ToInt16(profileForm["PayCodesSource"]); 
                profile.BenefitsSource = rec.BenefitsSource ?? 0; // Convert.ToInt16(profileForm["BenefitsSource"]); 
                profile.DeductionsSource = rec.DeductionsSource ?? 0; // Convert.ToInt16(profileForm["DeductionSource"]); 
                profile.PrintSocSecOnBlankTS = rec.PrintSocSecOnBlankTS; // Convert.ToInt16(profileForm["DeductionsSource"]); 
                profile.PrintSocSecOnBlankTS = rec.PrintSocSecOnBlankTS; // !string.IsNullOrEmpty(profileForm["PrintSocSecOnBlankTS"]); 
                profile.PrintSocSecOnReports = rec.PrintSocSecOnReports; // !string.IsNullOrEmpty(profileForm["PrintSocSecOnReports"]); 
                profile.PrintRateOnBlankTS = rec.PrintRateOnBlankTS; // !string.IsNullOrEmpty(profileForm["PrintRateOnBlankTS"]); 
                profile.PrintRateOnReports = rec.PrintRateOnReports; // !string.IsNullOrEmpty(profileForm["PrintRateOnReports"]); 
                profile.PrintYTDOnBlankTS = rec.PrintYTDOnBlankTS; // !string.IsNullOrEmpty(profileForm["PrintYTDOnBlankTS"]); 
                profile.PrintYTDOnReports = rec.PrintYTDOnReports; // !string.IsNullOrEmpty(profileForm["PrintYTDOnReports"]); 
                profile.AllowTimeEntry = rec.AllowTimeEntry; // !string.IsNullOrEmpty(profileForm["AllowTimeEntry"]); 
                profile.AllowEEDeptChg = rec.AllowEEDeptChg; // !string.IsNullOrEmpty(profileForm["AllowEEDeptChg"]); 
                profile.AllowEEPositionChg = rec.AllowEEPositionChg; // !string.IsNullOrEmpty(profileForm["AllowEEPositionChg"]);
                profile.PEOApprovalRequired = rec.PEOApprovalRequired; // !string.IsNullOrEmpty(profileForm["PEOApprovalRequired"]);
                profile.EditOnlyCodes = rec.EditOnlyCodes; // !string.IsNullOrEmpty(profileForm["PrintSocSecOnBlankTS"]);
                profile.HideOfferedHours = rec.HideOfferedHours; // 01/02/2017 DS TFS # 2795
                profile.HideCheckNumber = rec.HideCheckNumber; // 01/02/2017 DS TFS # 2795
                profile.HideWeeks = rec.HideWeeks; // 01/02/2017 DS TFS # 2795
                profile.HideDays = rec.HideDays; // 01/02/2017 DS TFS # 2795
                profile.AutoSave = rec.AutoSave; // 04/30/2019 DS TFS # 4163
                this.dbContext.SaveChanges();
                UpdateApprovalUsers(rec.ProfileID, rec.PEOApprovalRequired, profileForm["selectedUserList"]);
                ExecProfileStoredProc("sp_TS_ClientProfile_MassUpdate", id);
                if (profile.PEOApprovalRequired && !IsSystemUserAssignedToProfile(profile)) // 02/19/2019 DS TFS # 4516
                {
                    TempData["Error"] = "The Profile requires to select system users to approve timesheet first.";
                    return RedirectToAction("Edit", new { id = id });
                }
                else
                {
                    TempData["Success"] = "The Time Sheet Profile has been updated.";
                    return RedirectToAction("Index");
                }
            }
            else
            {
                TempData["Error"] = "The Time Sheet Profile Name cannot be empty.";
                return RedirectToAction("Edit", new { id = id });
            }
        }

        public ActionResult Create()
        {
            //ViewBag.ProfileTypes = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetTypes>(), "Value", "Name", string.Empty);
            //ViewBag.SelectionTypes = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetSelectionTypes>(), "Value", "Name", string.Empty);
            //ViewBag.ColumnStatus = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetColumnStatus>(), "Value", "Name", string.Empty);
            //ViewBag.CodeSource = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetCodeSource>(), "Value", "Name", string.Empty);
            //ViewBag.PayPeriods = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enPayPeriods>(), "Value", "Name", string.Empty);
            List<Code_Description> selUsers;
            ViewBag.AvailablePEOUsers = AvailablePEOUsers("", out selUsers);
            ViewBag.SelectedPEOUsers = selUsers;
            return View();
        }

        [HttpPost]
        public ActionResult Create(TimeSheetProfile rec, FormCollection profileForm)
        {

            string id = string.Empty;
            TimeSheetProfile profile1 = this.dbContext.TimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == rec.ProfileID.Trim());
            if (profile1 != null)
            {
                TempData["Error"] = "Time Sheet with this name already exists. Please try another name.";
                return View();
            }
            else
            {
                id = rec.ProfileID.Trim();
                short defWeeks;
                short defDays = GetDefaultDays(rec.PayPeriod ?? PayPeriods.Empty, out defWeeks);
                TimeSheetProfile profile = new TimeSheetProfile
                {
                    CompanyID = GlobalVariables.CompanyID,
                    ProfileID = id, // profileForm["ProfileID"],
                    ProfileName = rec.ProfileName, // profileForm["ProfileName"],
                    ProfileType = rec.ProfileType, // Convert.ToInt16(profileForm["ProfileType"]),
                    SelectionType = rec.SelectionType, // Convert.ToInt16(profileForm["SelectionType"]),
                    PayPeriod = rec.PayPeriod, // Convert.ToInt16(profileForm["PayPeriod"]),
                    DefDays = rec.DefDays ?? defDays, // !string.IsNullOrEmpty(profileForm["DefDays"]) ? (float)Convert.ToDouble(profileForm["DefDays"]) : (float)0,
                    DefWeeks = rec.DefWeeks ?? defWeeks, // !string.IsNullOrEmpty(profileForm["DefWeeks"]) ? (float)Convert.ToDouble(profileForm["DefWeeks"]) : (float)0,
                    DefHours = rec.DefHours ?? 0, // !string.IsNullOrEmpty(profileForm["DefHours"]) ? (float)Convert.ToDouble(profileForm["DefHours"]) : (float)0,
                    MaxRate = rec.MaxRate, // !string.IsNullOrEmpty(profileForm["MaxRate"]) ? (float)Convert.ToDouble(profileForm["MaxRate"]) : (float)0,
                    MaxDayHours = rec.MaxDayHours ?? 0, // !string.IsNullOrEmpty(profileForm["MaxDayHours"]) ? (float)Convert.ToDouble(profileForm["MaxDayHours"]) : (float)0,
                    //MaxEEHours = rec.MaxEEHours, // (float)Convert.ToDouble(profileForm["MaxEEHours"]),
                    MaxEEGrossAmount = rec.MaxEEGrossAmount ?? 0, // !string.IsNullOrEmpty(profileForm["MaxEEGrossAmount"]) ? Convert.ToDecimal(profileForm["MaxEEGrossAmount"]): 0,
                    PaycodeDecimals = rec.PaycodeDecimals ?? 2, // !string.IsNullOrEmpty(profileForm["PaycodeDecimals"]) ? Convert.ToInt16(profileForm["PaycodeDecimals"]) : (short)2,
                    BenefitDecimals = rec.BenefitDecimals ?? 2, // !string.IsNullOrEmpty(profileForm["BenefitDecimals"]) ? Convert.ToInt16(profileForm["BenefitDecimals"]) : (short)2,
                    DeductionDecimals = rec.DeductionDecimals ?? 2, // !string.IsNullOrEmpty(profileForm["DeductionDecimals"]) ? Convert.ToInt16(profileForm["DeductionDecimals"]) : (short)2,
                    IncludeAllEE = rec.IncludeAllEE, // !string.IsNullOrEmpty(profileForm["IncludeAllEE"]),
                    AllowModifiedRate = rec.AllowModifiedRate, // !string.IsNullOrEmpty(profileForm["AllowModifiedRate"]),
                    AllowModifiedRatePermanent = rec.AllowModifiedRatePermanent, // !string.IsNullOrEmpty(profileForm["AllowModifiedRatePermanent"]),
                    DepartmentStatus = rec.DepartmentStatus ?? 0, // !string.IsNullOrEmpty(profileForm["DepartmentStatus"]) ? Convert.ToInt16(profileForm["DepartmentStatus"]) : (short)0,
                    PositionStatus = rec.PositionStatus ?? 0, // !string.IsNullOrEmpty(profileForm["PositionStatus"]) ? Convert.ToInt16(profileForm["PositionStatus"]) : (short)0,
                    LocalTaxStatus = rec.LocalTaxStatus ?? 0, // !string.IsNullOrEmpty(profileForm["LocalTaxStatus"]) ? Convert.ToInt16(profileForm["LocalTaxStatus"]) : (short)0,
                    StateTaxStatus = rec.StateTaxStatus ?? 0, // !string.IsNullOrEmpty(profileForm["StateTaxStatus"]) ? Convert.ToInt16(profileForm["StateTaxStatus"]) : (short)0,
                    SutaStateStatus = rec.SutaStateStatus ?? 0, // !string.IsNullOrEmpty(profileForm["SutaStateStatus"]) ? Convert.ToInt16(profileForm["SutaStateStatus"]) : (short)0,
                    WCStatus = rec.WCStatus ?? 0, // !string.IsNullOrEmpty(profileForm["WCStatus"]) ? Convert.ToInt16(profileForm["WCStatus"]) : (short)0,
                    ShiftStatus = rec.ShiftStatus ?? 0, // !string.IsNullOrEmpty(profileForm["ShiftStatus"]) ? Convert.ToInt16(profileForm["ShiftStatus"]) : (short)0,
                    PayCodesSource = rec.PayCodesSource ?? 0, // !string.IsNullOrEmpty(profileForm["PayCodesSource"]) ? Convert.ToInt16(profileForm["PayCodesSource"]) : (short)0,
                    BenefitsSource = rec.BenefitsSource ?? 0, // !string.IsNullOrEmpty(profileForm["BenefitsSource"]) ? Convert.ToInt16(profileForm["BenefitsSource"]) : (short)0,
                    DeductionsSource = rec.DeductionsSource ?? 0, // !string.IsNullOrEmpty(profileForm["DeductionsSource"]) ? Convert.ToInt16(profileForm["DeductionsSource"]) : (short)0,
                    PrintSocSecOnBlankTS = rec.PrintSocSecOnBlankTS, // !string.IsNullOrEmpty(profileForm["PrintSocSecOnBlankTS"]),
                    PrintSocSecOnReports = rec.PrintSocSecOnReports, // !string.IsNullOrEmpty(profileForm["PrintSocSecOnReports"]),
                    PrintRateOnBlankTS = rec.PrintRateOnBlankTS, // !string.IsNullOrEmpty(profileForm["PrintRateOnBlankTS"]),
                    PrintRateOnReports = rec.PrintRateOnReports, // !string.IsNullOrEmpty(profileForm["PrintRateOnReports"]),
                    PrintYTDOnBlankTS = rec.PrintYTDOnBlankTS, // !string.IsNullOrEmpty(profileForm["PrintYTDOnBlankTS"]),
                    PrintYTDOnReports = rec.PrintYTDOnReports, // !string.IsNullOrEmpty(profileForm["PrintYTDOnReports"]),
                    AllowTimeEntry = rec.AllowTimeEntry, // !string.IsNullOrEmpty(profileForm["AllowTimeEntry"]),
                    AllowEEDeptChg = rec.AllowEEDeptChg, // !string.IsNullOrEmpty(profileForm["AllowEEDeptChg"]),
                    AllowEEPositionChg = rec.AllowEEPositionChg, // !string.IsNullOrEmpty(profileForm["AllowEEPositionChg"]),
                    PEOApprovalRequired = rec.PEOApprovalRequired, // !string.IsNullOrEmpty(profileForm["PEOApprovalRequired"]),
                    EditOnlyCodes = rec.EditOnlyCodes, // !string.IsNullOrEmpty(profileForm["PrintSocSecOnBlankTS"])
                    HideOfferedHours = rec.HideOfferedHours, // 01/02/2017 DS TFS # 2795
                    HideCheckNumber = rec.HideCheckNumber, // 01/02/2017 DS TFS # 2795
                    HideWeeks = rec.HideWeeks, // 01/02/2017 DS TFS # 2795
                    HideDays = rec.HideDays, // 01/02/2017 DS TFS # 2795
                    AutoSave = rec.AutoSave // 04/30/2019 DS TFS # 4163
                };
                try
                {

                    UpdateApprovalUsers(id, rec.PEOApprovalRequired, profileForm["SelectedUsers"]);
                    this.dbContext.TimeSheetProfiles.Add(profile);
                    this.dbContext.SaveChanges();

                    if (profile.PEOApprovalRequired && !IsSystemUserAssignedToProfile(profile)) // 02/19/2019 DS TFS # 4516
                    {
                        TempData["Error"] = "The Profile requires to select system users to approve timesheet first.";
                        return RedirectToAction("Edit", new { id = id });
                    }
                    else
                    {
                        TempData["Success"] = "The Time Sheet Profile has been created.";
                        if (!string.IsNullOrEmpty(profileForm["selected_clients"]))
                        {
                            List<string> clients = profileForm["selected_clients"].Split(',').ToList();
                            foreach (string client in clients)
                            {
                                AssignToClient(profile.ProfileID, client.Trim());
                                //ClientTimeSheetProfiles c_profile = this.dbContext.ClientTimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ClientID == client.Trim() && p.ProfileID == profile.ProfileID);
                                //if (c_profile != null) CreateProfileColumns(profile.ProfileID, client.Trim(), c_profile)
                            }
                        }
                        return RedirectToAction("Index");
                    }
                }
                catch
                {
                    TempData["Error"] = "The Time Sheet Profile has encountered an error.";
                    return RedirectToAction("Edit", new { id = id });
                }
            }
        }

        public ActionResult Delete(string id)
        {
            try
            {
                TimeSheetProfile profile = this.dbContext.TimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id);
                if (profile != null)
                {
                    if (CanDeleteProfile(id))
                    {
                        this.dbContext.TimeSheetProfiles.Remove(profile);
                        this.dbContext.SaveChanges();
                        CleanApproval(id);
                        TempData["Success"] = "The Time Sheet Profile has been deleted.";
                        return RedirectToAction("Index");
                    }
                    else
                    {
                        TempData["Error"] = "This Time Sheet Profile cannot be deleted: some of Client Time Sheet Profiles are currently use this Profile.";
                        return RedirectToAction("Index");
                    }
                }
                else
                {
                    TempData["Error"] = "This Time Sheet Profile cannot be deleted: cannot find this Profile data.";
                    return RedirectToAction("Index");
                }
            }
            catch (Exception e)
            {
                ModelState.AddModelError("Error", "An error occurred while trying to delete profile :" + e.Message);
                return RedirectToAction("Index");
            }
        }
        public ActionResult DeleteClient(string id, string c)
        {
            if (CanDeleteClientProfile(id, c))
            {
                ExecProfileStoredProc("sp_TS_ClientProfile_Delete", id, c);
                TempData["Success"] = "The Time Sheet Profile has been deleted.";
            }
            else
            {
                TempData["Error"] = "This Time Sheet Profile cannot be deleted: some of Time Sheets are currently use this Profile.";
            }
            return RedirectToAction("Assign", new { id = id });
        }
        public ActionResult ClientsAssign(string id)
        {

            return View(this.dbContext.TimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id));
        }

        public ActionResult Assign(string id)
        {
            TimeSheetProfile sys_profile = dbContext.TimeSheetProfiles
                .FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id);

            ViewBag.ProfileName = sys_profile.ProfileName ?? string.Empty;
            ViewBag.ProfileID = id;
            ViewBag.AllClients = GetClients(id);
            ViewBag.AllowPEORequirement = IsSystemUserAssignedToProfile(sys_profile);
            // Get all of the client time sheet profiles
            List<ClientTimeSheetProfile> clientTimeSheetProfiles = dbContext.ClientTimeSheetProfiles
                .Where(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id)
                .ToList();

            List<ClientTimeSheetProfilesView> clientTimeSheetProfilesViews = new List<ClientTimeSheetProfilesView>();

            // Get the client for each profile and combine the entities in to a db-like-view to send to the web-view
            foreach (var profile in clientTimeSheetProfiles)
            {
                Client client = dbContext.Clients.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.ClientID == profile.ClientID);

                ClientTimeSheetProfilesView view = new ClientTimeSheetProfilesView();
                view.ClientTimeSheetProfile = profile;
                view.Client = client;

                clientTimeSheetProfilesViews.Add(view);
            }

            return View(clientTimeSheetProfilesViews);
        }

        [HttpPost]
        public ActionResult Assign(string id, List<string> selclients)
        {
            if (selclients != null)
            {
                foreach (string client in selclients)
                {
                    AssignToClient(id, client.Trim());
                }
            }
            return RedirectToAction("Assign", new { id = id });
        }

        private bool CanDeleteProfile(string id)
        {
            return this.dbContext.ClientTimeSheetProfiles.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id).Count() == 0;
        }

        private bool CanDeleteClientProfile(string id, string c)
        {
            return this.dbContext.TimeSheets.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.ClientID == c && p.ProfileID == id).Count() == 0;
        }

        private List<Code_Description> GetClients(string id)
        {
            List<string> assigned = this.dbContext.ClientTimeSheetProfiles.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.ProfileID == id).Select(p => p.ClientID).ToList<string>();
            List<Client> AllClients = this.dbContext.Clients.Where(c => c.CompanyID == GlobalVariables.CompanyID).OrderBy(c => c.ClientID).ToList<Client>();

            return AllClients.Where(c => !assigned.Any(ac => ac == c.ClientID)).Select(c => new Code_Description() { Code = c.ClientID, Description = c.ClientID + " (" + c.ClientName + ")" }).ToList<Code_Description>();
        }

        public ActionResult RequirePEO(string id)
        {
            var origProfile = dbContext.ClientTimeSheetProfiles.FirstOrDefault(ctp => ctp.ProfileID == id);
            if (origProfile != null)
            {
                if (IsSystemUserAssignedToProfile(origProfile)) // 02/18/2019 DS TFS # 4516
                {
                    origProfile.PEOApprovalRequired = true;
                    dbContext.SaveChanges();
                }
                else
                    TempData["Error"] = "Time Sheet Profile requires to select any system users for approval first.";
            }
            return RedirectToAction("Assign", "TSPRofiles", new { id = origProfile.ProfileID });
        }

        public ActionResult UnRequirePEO(string id)
        {
            var origProfile = dbContext.ClientTimeSheetProfiles.FirstOrDefault(ctp => ctp.ProfileID == id);
            if (origProfile != null)
            {
                origProfile.PEOApprovalRequired = false;
                dbContext.SaveChanges();
                CleanApproval(id);
            }
            return RedirectToAction("Assign", "TSPRofiles", new { id = origProfile.ProfileID });
        }

        private List<string> SelectedPEOUsers(string id)
        {
            try
            {
                return this.dbContext.TimeSheetProfileApprovals.Where(u => u.CompanyID == GlobalVariables.CompanyID && u.ProfileID == id).OrderBy(u => u.UserID).Select(u => u.UserID).Distinct().ToList();
            }
            catch
            {
                return new List<string>();
            }
        }

        private List<Code_Description> AvailablePEOUsers(string id, out List<Code_Description> selUsers)
        {
            List<string> tmpUsers = (!string.IsNullOrEmpty(id)) ? SelectedPEOUsers(id) : new List<string>();
            List<Code_Description> result = this.dbContext.Users.Where(u => u.SystemLevelEnabled).OrderBy(u => u.UserID).Select(u => new Code_Description { Code = u.UserID, Description = u.Name + " (" + u.UserID + ")" }).ToList(); ;
            selUsers = new List<Code_Description>();
            try
            {

                selUsers = result.Where(u => tmpUsers.Any(s => s == u.Code)).ToList();
                result = result.Where(u => !tmpUsers.Any(s => s == u.Code)).ToList();
            }
            catch
            {
                selUsers = new List<Code_Description>();
                result = new List<Code_Description>();
            }
            return result;
        }

        private void AddApprovalUser(string id, string user)
        {
            TimeSheetProfileApproval rec = new TimeSheetProfileApproval()
            {
                CompanyID = GlobalVariables.CompanyID,
                ProfileID = id,
                UserID = user
            };
            try
            {
                dbContext.TimeSheetProfileApprovals.Add(rec);
                dbContext.SaveChanges();
            }
            catch
            {
                // ignored
            }
        }

        private void AddApprovalLevelUsers(string id, string usersList)
        {
            if (!string.IsNullOrEmpty(usersList))
            {
                List<string> users = usersList.Split(',').ToList();
                foreach (string user in users)
                {
                    AddApprovalUser(id, user.Trim());
                }
            }
        }

        private void UpdateApprovalUsers(string id, bool peoReq, string users)
        {
            CleanApproval(id);
            AddApprovalLevelUsers(id, users);
        }

        private void CleanApproval(string id)
        {
            ExecProfileStoredProc("sp_TS_Profile_CleanApproval", id);
        }

        private void AssignToClient(string id, string client)
        {

            dbContext.Database.ExecuteSqlCommand(
                "sp_TS_Profile_Assign @CompanyID, @ClientID, @Profile",
                new SqlParameter("CompanyID", GlobalVariables.CompanyID),
                new SqlParameter("ClientID", client),
                new SqlParameter("Profile", id)
            );

            //using (IDbCommand oaCommand = this.dbContext.Database.Connection.CreateCommand())
            //{
            //    oaCommand.CommandType = System.Data.CommandType.StoredProcedure;
            //    oaCommand.CommandText = "sp_TS_Profile_Assign";
            //    IDbDataParameter oaParamCm = oaCommand.CreateParameter();
            //    oaParamCm.ParameterName = "@CompanyID";
            //    oaParamCm.DbType = DbType.String;
            //    oaParamCm.Value = GlobalVariables.CompanyID;
            //    oaCommand.Parameters.Add(oaParamCm);
            //    IDbDataParameter oaParamP = oaCommand.CreateParameter();
            //    oaParamP.ParameterName = "@Profile";
            //    oaParamP.DbType = DbType.String;
            //    oaParamP.Value = id;
            //    oaCommand.Parameters.Add(oaParamP);
            //    IDbDataParameter oaParamC = oaCommand.CreateParameter();
            //    oaParamC.ParameterName = "@ClientID";
            //    oaParamC.DbType = DbType.String;
            //    oaParamC.Value = client;
            //    oaCommand.Parameters.Add(oaParamC);
            //    oaCommand.ExecuteNonQuery();
            //}
            dbContext.SaveChanges();
        }

        private void ExecProfileStoredProc(string proc, string id, string client)
        {

            string cmd = proc + " @CompanyID, @ClientID, @Profile";
            dbContext.Database.ExecuteSqlCommand(
                cmd,
                new SqlParameter("CompanyID", GlobalVariables.CompanyID),
                new SqlParameter("ClientID", client),
                new SqlParameter("Profile", id)
            );

            //using (IDbCommand oaCommand = this.dbContext.Database.Connection.CreateCommand())
            //{
            //    oaCommand.CommandType = System.Data.CommandType.StoredProcedure;
            //    oaCommand.CommandText = proc;
            //    IDbDataParameter oaParamCm = oaCommand.CreateParameter();
            //    oaParamCm.ParameterName = "@CompanyID";
            //    oaParamCm.DbType = DbType.String;
            //    oaParamCm.Value = GlobalVariables.CompanyID;
            //    oaCommand.Parameters.Add(oaParamCm);
            //    IDbDataParameter oaParamP = oaCommand.CreateParameter();
            //    oaParamP.ParameterName = "@Profile";
            //    oaParamP.DbType = DbType.String;
            //    oaParamP.Value = id;
            //    oaCommand.Parameters.Add(oaParamP);
            //    IDbDataParameter oaParamC = oaCommand.CreateParameter();
            //    oaParamC.ParameterName = "@ClientID";
            //    oaParamC.DbType = DbType.String;
            //    oaParamC.Value = client;
            //    oaCommand.Parameters.Add(oaParamC);
            //    oaCommand.ExecuteNonQuery();
            //}
            dbContext.SaveChanges();
        }

        private void ExecProfileStoredProc(string proc, string id)
        {
            dbContext.Database.ExecuteSqlCommand(
                proc + " @CompanyID, @Profile",
                new SqlParameter("CompanyID", GlobalVariables.CompanyID),
                new SqlParameter("Profile", id)
            );

            //using (IDbCommand oaCommand = this.dbContext.Database.Connection.CreateCommand())
            //{
            //    oaCommand.CommandType = System.Data.CommandType.StoredProcedure;
            //    oaCommand.CommandText = proc;
            //    IDbDataParameter oaParamCm = oaCommand.CreateParameter();
            //    oaParamCm.ParameterName = "@CompanyID";
            //    oaParamCm.DbType = DbType.String;
            //    oaParamCm.Value = GlobalVariables.CompanyID;
            //    oaCommand.Parameters.Add(oaParamCm);
            //    IDbDataParameter oaParamP = oaCommand.CreateParameter();
            //    oaParamP.ParameterName = "@Profile";
            //    oaParamP.DbType = DbType.String;
            //    oaParamP.Value = id;
            //    oaCommand.Parameters.Add(oaParamP);
            //    oaCommand.ExecuteNonQuery();
            //}
            dbContext.SaveChanges();
        }

        private short GetDefaultDays(short pperiod, out short dweeks)
        {
            short d = 0;
            dweeks = 0;
            switch (pperiod)
            {
                case PayPeriods.Weekly:
                    d = 5;
                    dweeks = 1;
                    break;
                case PayPeriods.Biweekly:
                    d = 10;
                    dweeks = 2;
                    break;
                case PayPeriods.Semimonthly:
                    d = 11;
                    dweeks = 2;
                    break;
                case PayPeriods.Monthly:
                    d = 22;
                    dweeks = 4;
                    break;
                case PayPeriods.Quarterly:
                    d = 65;
                    dweeks = 13;
                    break;
                case PayPeriods.Semiannually:
                    d = 130;
                    dweeks = 26;
                    break;
                case PayPeriods.Annually:
                    d = 260;
                    dweeks = 52;
                    break;
                case PayPeriods.DailyMiscellaneous:
                    d = 1;
                    dweeks = 0;
                    break;
            }
            return d;
        }

        // 05/01/2019 DarwinetSetup TFS # 4940
        private bool IsSystemUserAssignedToProfile(ClientTimeSheetProfile profile)  // 02/19/2019 DS TFS # 4516
        {
            int result = 0;
            if (!profile.PEOApprovalRequired) return false;
            result = dbContext.ClientTimeSheetProfileApprovals.Where(p => p.CompanyID == profile.CompanyID && p.ClientID == profile.ClientID && p.ProfileID == profile.ProfileID).Count();
            if (result <= 0)
            {
                ExecProfileStoredProc("sp_TS_ClientProfile_RefreshSysApproval", profile.ProfileID, profile.ClientID);
                result = dbContext.ClientTimeSheetProfileApprovals.Where(p => p.CompanyID == profile.CompanyID && p.ClientID == profile.ClientID && p.ProfileID == profile.ProfileID).Count();
            }
            return result > 0;
        }

        // 02/19/
        private bool IsSystemUserAssignedToProfile(TimeSheetProfile profile)  // 02/19/2019 DS TFS # 4516
        {
            if (!profile.PEOApprovalRequired) return false;
            return dbContext.TimeSheetProfileApprovals.Where(p => p.CompanyID == profile.CompanyID && p.ProfileID == profile.ProfileID).Count() > 0;
        }
        // 08/02/2018 DS 3404 - added new functionality similar to client timesheet profile controller has
        #region ClientTimeSheetProfile columns refresh 
        private void CleanUpProfileColumns(string id, string client, short type)
        {
            try
            {
                List<ClientTimeSheetProfileColumn> columns = this.dbContext.ClientTimeSheetProfileColumns.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == client && c.ProfileID == id && c.Type == type).OrderBy(c => c.ColNbr).ToList();
                foreach (ClientTimeSheetProfileColumn col in columns)
                {
                    this.dbContext.ClientTimeSheetProfileColumns.Remove(col);
                }
                this.dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
            }
        }

        private void ReorderProfileColumns(string id, string client, string selectColumns)
        {
            ClientTimeSheetProfile profile = this.dbContext.ClientTimeSheetProfiles.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.ClientID == client && p.ProfileID == id);
            if (profile != null)
            {
                try
                {
                    List<ClientTimeSheetProfileColumn> existColumns = GetProfileColumns(id, client, profile);
                    List<ClientTimeSheetProfileColumn> newColumns = new List<ClientTimeSheetProfileColumn>();
                    List<string> newcolumnname = selectColumns.Split(',').ToList();
                    AddSelectedColumnsToNewList(newcolumnname, existColumns, ref newColumns);
                    CleanUpProfileColumns(id, client, profile.ProfileType ?? TimeSheetTypes.Regular);
                    foreach (ClientTimeSheetProfileColumn col in newColumns)
                    {
                        this.dbContext.ClientTimeSheetProfileColumns.Add(col);
                    }
                    this.dbContext.SaveChanges();
                }
                catch (Exception ex)
                {
                    Bugsnag.AspNet.Client.Current.Notify(ex);
                }
            }
        }

        private List<ClientTimeSheetProfileColumn> GetProfileColumns(string id, string client, ClientTimeSheetProfile profile)
        {
            short type = profile.ProfileType ?? TimeSheetTypes.Regular;
            bool exists = this.dbContext.ClientTimeSheetProfileColumns.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == GlobalVariables.Client && c.ProfileID == id && c.Type == type).Count() > 10;
            if (!exists) CreateProfileColumns(id, client, profile);
            try
            {
                return this.dbContext.ClientTimeSheetProfileColumns.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == GlobalVariables.Client && c.ProfileID == id && c.Type == type).OrderBy(c => c.ColNbr).Select(column => new ClientTimeSheetProfileColumn
                {
                    CompanyID = column.CompanyID,
                    ClientID = column.ClientID,
                    ProfileID = column.ProfileID,
                    Type = column.Type,
                    ColNbr = column.ColNbr,
                    Name = column.Name,
                    ColType = column.ColType,
                    Label = column.Label,
                    Status = column.Status,
                    ValidationType = column.ValidationType,
                    Code = column.Code,
                    CodeType = column.CodeType,
                    BasedOn = column.BasedOn,
                    BaseCode = column.BaseCode,
                    BaseCodeType = column.BaseCodeType
                }).ToList();
            }
            catch
            {
                return new List<ClientTimeSheetProfileColumn>();
            }
        }

        private int LastColumnNumber(string id, string client)
        {
            try
            {
                return this.dbContext.ClientTimeSheetProfileColumns.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.ClientID == client && p.ProfileID == id).OrderByDescending(p => p.ColNbr).First().ColNbr;

            }
            catch
            {
                return this.dbContext.ClientTimeSheetProfileColumns.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.ClientID == GlobalVariables.Client && p.ProfileID == id).Count();
            }
        }

        private bool IsProfileCodeExists(ClientTimeSheetProfileColumn column, List<ClientTimeSheetProfileCode> codecolumns)
        {
            if (string.IsNullOrEmpty(column.Code)) return true;
            try
            {
                return codecolumns.Where(cc => cc.Code == column.Code && cc.CodeType == column.CodeType).Count() > 0;
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return false;
            }
        }

        private bool IsProfileNewCode(List<ClientTimeSheetProfileColumn> columns, ClientTimeSheetProfileCode codecolumn)
        {
            if (string.IsNullOrEmpty(codecolumn.Code)) return false;
            try
            {
                return columns.Where(cc => cc.Code == codecolumn.Code && cc.CodeType == codecolumn.CodeType).Count() <= 0;
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return false;
            }
        }

        private void CreateProfileColumns(string id, string client, ClientTimeSheetProfile profile)
        {
            int codeIdx = 0;
            int idx = 1;
            short type = profile.ProfileType ?? TimeSheetTypes.Regular;
            bool exists = this.dbContext.ClientTimeSheetProfileColumns.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == client && c.ProfileID == id && c.Type == type).Count() > 10;
            List<ClientTimeSheetProfileCode> codecolumns = (type == TimeSheetTypes.Regular) ? this.dbContext.ClientTimeSheetProfileCodes.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == client && c.ProfileID == profile.ProfileID).OrderBy(c => c.SeqNbr).ToList<ClientTimeSheetProfileCode>() : new List<ClientTimeSheetProfileCode>();
            if (!exists)
            {
                CleanUpProfileColumns(id, client, profile.ProfileType ?? TimeSheetTypes.Regular);
                List<TimeSheetColumnDefault> defcolumns = this.dbContext.TimeSheetColumnDefaults.Where(c => c.TimeSheetType == profile.ProfileType).OrderBy(c => c.ColNbr).ToList<TimeSheetColumnDefault>();
                foreach (TimeSheetColumnDefault col in defcolumns)
                {
                    if (col.ColType != TimeSheetColumnType.CodeSection && col.ColType != TimeSheetColumnType.LDSection)
                    {
                        if (AddProfileColumn(id, client, idx, col, profile)) idx++;
                    }
                    else
                    {
                        if (col.ColType == TimeSheetColumnType.CodeSection)
                        {
                            foreach (ClientTimeSheetProfileCode code in codecolumns)
                            {
                                codeIdx++;
                                idx = AddProfileCodeColumns(id, client, idx, codeIdx, code, profile, profile.ProfileType);
                                idx++;
                            }
                        }
                        if (col.ColType == TimeSheetColumnType.LDSection) // 11/06/2018 DS TFS # 3854
                        {
                            List<Code_Description> ldLabels = new List<Code_Description>();
                            if (new LaborDistribution().GetAvailableLDLabels(GlobalVariables.CompanyID, client, out ldLabels))
                            {
                                foreach (Code_Description ld in ldLabels)
                                {
                                    if (AddProfileLDColumns(id, idx, ld.Code, ld.Description, profile.ProfileType)) idx++;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                List<ClientTimeSheetProfileColumn> prcolumns = this.dbContext.ClientTimeSheetProfileColumns.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == client && c.ProfileID == id && (c.ColType == TimeSheetColumnType.PayCode || c.ColType == TimeSheetColumnType.BenefitCode || c.ColType == TimeSheetColumnType.DeductionCode)).OrderBy(c => c.ColNbr).ToList();
                foreach (ClientTimeSheetProfileColumn column in prcolumns)
                {
                    if (IsProfileCodeExists(column, codecolumns))
                    {
                        codeIdx++;
                        UpdateProfileCodeColumns(id, client, codeIdx, column.Code, column.CodeType ?? PaycodeType.Unknown);
                    }
                    else
                        RemoveProfileCodeColumns(id, client, column.Code, column.CodeType ?? PaycodeType.Unknown);
                }
                List<ClientTimeSheetProfileCode> newcodecolumns = new List<ClientTimeSheetProfileCode>();
                foreach (ClientTimeSheetProfileCode codecolumn in codecolumns)
                {
                    if (IsProfileNewCode(prcolumns, codecolumn)) newcodecolumns.Add(codecolumn);
                }
                if (newcodecolumns.Count() > 0)
                {
                    idx = LastColumnNumber(id, client) + 1;
                    foreach (ClientTimeSheetProfileCode code in newcodecolumns)
                    {
                        codeIdx++;
                        idx = AddProfileCodeColumns(id, client, idx, codeIdx, code, profile, profile.ProfileType);
                        idx++;
                    }
                }
                ReorderProfileColumns1(id, client);
            }
        }

        private void ReorderProfileColumns1(string id, string client)
        {
            ExecProfileStoredProc("sp_TS_ClientProfile_ReorderColumns", id, client);
        }

        private bool ValidateMax(string code)
        {
            bool result = false;
            try
            {
                return this.dbContext.Paycodes.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.PayRecord == code && !c.Inactive && c.PayUnit.ToUpper().Contains("HOURLY")).Count() > 0;
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return false;
            }
        }


        private bool IsShiftedCode(short type)
        {
            return (type == PaycodeType.Hourly || type == PaycodeType.Commission || type == PaycodeType.Piecework || type == PaycodeType.BusinessExp || type == PaycodeType.Overtime || type == PaycodeType.DoubleTime || type == PaycodeType.Vacation || type == PaycodeType.Sick || type == PaycodeType.Holiday || type == PaycodeType.Other);
        }

        private bool IsBusinessExp(short type)
        {
            return (type == PaycodeType.BusinessExp);
        }

        private bool IsJustRateCode(short type)
        {
            return (type == PaycodeType.BusinessExp || type == PaycodeType.Other || type == PaycodeType.MinWageBalance || type == PaycodeType.Commission || type == PaycodeType.ChargedTips || type == PaycodeType.ReportedTips);
        }

        private bool IsPTOCode(short type)
        {
            return (type == PaycodeType.PTO1 || type == PaycodeType.PTO2 || type == PaycodeType.PTO3 || type == PaycodeType.PTO4 || type == PaycodeType.PTO5 || type == PaycodeType.PTO6 || type == PaycodeType.PTO7 || type == PaycodeType.PTO8 || type == PaycodeType.PTO9 || type == PaycodeType.PTO10 || type == PaycodeType.Vacation || type == PaycodeType.Sick || type == PaycodeType.Misc1 || type == PaycodeType.Misc2 || type == PaycodeType.Misc3 || type == PaycodeType.Misc4 || type == PaycodeType.Misc5 || type == PaycodeType.Misc6 || type == PaycodeType.Misc7 || type == PaycodeType.Misc8 || type == PaycodeType.Misc9 || type == PaycodeType.Misc10);
        }

        private int AddProfileCodeColumns(string id, string client, int index, int codeIndex, ClientTimeSheetProfileCode code, ClientTimeSheetProfile profile, short? timesheetType)
        {
            int result = index;
            string suffix = codeIndex.ToString();
            string basecol = "Code" + suffix;
            short status, statusshift, coltype, validtype, codetype;
            codetype = code.CodeType ?? 0;
            if (timesheetType == null) timesheetType = profile.ProfileType ?? TimeSheetTypes.Regular;
            AddProfileColumnCode(id, client, timesheetType ?? TimeSheetTypes.Regular, result, basecol, "Code " + suffix, code.ColumnType, GetColumnStatus(code.ColumnType ?? 0, profile), ValidationType.None, code.Code, codetype, string.Empty, code.BaseCode, code.BaseCodeType);
            result++;
            coltype = TimeSheetColumnType.CodeAmount;
            validtype = ValidationType.None;
            if (code.ColumnType == TimeSheetColumnType.PayCode)
            {
                if (IsShiftedCode(codetype) || IsPTOCode(codetype) || IsJustRateCode(codetype))
                {
                    validtype = (profile.ShiftStatus != TimeSheetColumnStatus.Hidden) ? ValidationType.None : ValidationType.Decimal;
                    if (IsBusinessExp(codetype))
                        coltype = TimeSheetColumnType.BusinessExpAmount;
                    else
                        if (IsJustRateCode(codetype))
                        coltype = TimeSheetColumnType.JustAmount;
                    else
                            if (ValidateMax(code.Code))
                    {
                        validtype = ValidationType.Hour;
                        coltype = TimeSheetColumnType.CodeHours;
                    }
                }
                else
                    validtype = ValidationType.Decimal;
            }
            AddProfileColumnCode(id, client, timesheetType ?? TimeSheetTypes.Regular, result, "Hours" + suffix, (profile.ProfileType == TimeSheetTypes.Regular) ? code.Code : "Code" + suffix + " Hours", coltype, TimeSheetColumnStatus.Editable, validtype, code.Code, codetype, basecol, string.Empty, 0);
            if (timesheetType != TimeSheetTypes.EETimeEntry)
            {
                if (code.ColumnType == TimeSheetColumnType.PayCode)
                {
                    result++;
                    status = (!IsBusinessExp(codetype)) ? (profile.PrintRateOnBlankTS) ? (profile.AllowModifiedRate) ? TimeSheetColumnStatus.HideEditable : TimeSheetColumnStatus.HideReadOnly : TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Hidden;
                    AddProfileColumnCode(id, client, timesheetType ?? TimeSheetTypes.Regular, result, "Rate" + suffix, "Rate", TimeSheetColumnType.Rate, (coltype == TimeSheetColumnType.BusinessExpAmount) ? TimeSheetColumnStatus.Hidden : status, (validtype == ValidationType.Hour) ? ValidationType.Rate : ValidationType.None, code.Code, codetype, basecol, string.Empty, 0);
                    result++;
                    statusshift = status;
                    if (statusshift != TimeSheetColumnStatus.Hidden)
                    {
                        switch (profile.ShiftStatus)
                        {
                            case TimeSheetColumnStatus.ReadOnly:
                                statusshift = TimeSheetColumnStatus.HideColumnReadOnlyFld;
                                break;
                            case TimeSheetColumnStatus.Editable:
                                statusshift = TimeSheetColumnStatus.HideColumnEditableFld;
                                break;
                        }
                    }
                    AddProfileColumnCode(id, client, timesheetType ?? TimeSheetTypes.Regular, result, "Shift" + suffix, "Shift Code", TimeSheetColumnType.ShiftCode, statusshift, ValidationType.None, code.Code, codetype, basecol, string.Empty, 0);
                    result++;
                    AddProfileColumnCode(id, client, timesheetType ?? TimeSheetTypes.Regular, result, "Premium" + suffix, "Premium", TimeSheetColumnType.Premium, status, ValidationType.None, code.Code, codetype, basecol, string.Empty, 0);
                    result++;
                    AddProfileColumnCode(id, client, timesheetType ?? TimeSheetTypes.Regular, result, "Reduction" + suffix, "Reduction", TimeSheetColumnType.Reduction, TimeSheetColumnStatus.HideColumnEditableFld, ValidationType.None, code.Code, codetype, basecol, string.Empty, 0);
                }
            }
            return result++;
        }

        private void RemoveProfileCodeColumns(string id, string client, string code, short codeType)
        {
            try
            {
                List<ClientTimeSheetProfileColumn> columns = this.dbContext.ClientTimeSheetProfileColumns.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == client && c.ProfileID == id && c.Code == code && c.CodeType == codeType).OrderBy(c => c.ColNbr).ToList();
                foreach (ClientTimeSheetProfileColumn col in columns)
                {
                    this.dbContext.ClientTimeSheetProfileColumns.Remove(col);
                }
                this.dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
            }
        }

        private void UpdateProfileCodeColumns(string id, string client, int idx, string code, short codeType)
        {
            try
            {
                string suffix = idx.ToString();
                List<ClientTimeSheetProfileColumn> columns = this.dbContext.ClientTimeSheetProfileColumns.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == client && c.ProfileID == id && c.Code == code && c.CodeType == codeType).OrderBy(c => c.ColNbr).ToList();
                foreach (ClientTimeSheetProfileColumn col in columns)
                {
                    string name = col.Name;
                    string label = col.Label;
                    if (!string.IsNullOrEmpty(name))
                    {
                        switch (name.Substring(0, 4).ToUpper())
                        {
                            case "CODE":
                                name = "Code" + suffix;
                                label = "Code " + suffix;
                                break;
                            case "HOUR":
                                name = "Hours" + suffix;
                                break;
                            case "RATE":
                                name = "Rate" + suffix;
                                break;
                            case "SHIF":
                                name = "Shift" + suffix;
                                break;
                            case "PREM":
                                name = "Premium" + suffix;
                                break;
                            case "REDU":
                                name = "Reduction" + suffix;
                                break;
                        }
                        col.Name = name;
                        col.Label = label;

                    }
                }
                this.dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
            }
        }

        private void ValidateBasedOnCode(string code, ref string bcode, ref short? type)
        {
            string tmpcode;
            Paycode prec = this.dbContext.Paycodes.FirstOrDefault(c => c.CompanyID == GlobalVariables.CompanyID && c.PayRecord == code);
            if (prec != null)
            {
                tmpcode = prec.BasePayRecord ?? string.Empty;
                if (tmpcode != bcode)
                {
                    bcode = tmpcode;
                    if (!string.IsNullOrEmpty(prec.BasePayRecord))
                    {
                        var firstOrDefault = this.dbContext.Paycodes.FirstOrDefault(c => c.CompanyID == GlobalVariables.CompanyID && c.PayRecord == prec.BasePayRecord);
                        if (firstOrDefault != null) type = firstOrDefault.PayType;
                    }
                    else type = 0;
                }
            }
        }

        private void AddProfileColumnCode(string id, string client, short tstype, int index, string name, string label, short? type, short? status, short? vType, string cd, short? cType, string basedOn, string bCode, short? bcType)
        {
            if (type == TimeSheetColumnType.PayCode) ValidateBasedOnCode(cd, ref bCode, ref bcType);
            if (string.IsNullOrEmpty(bCode)) bCode = string.Empty;
            if (bcType == null) bcType = 0;
            ClientTimeSheetProfileColumn rec = new ClientTimeSheetProfileColumn
            {
                CompanyID = GlobalVariables.CompanyID,
                ClientID = client,
                ProfileID = id,
                Type = tstype,
                Name = name,
                ColNbr = index,
                ColType = type,
                Label = label,
                Status = status,
                ValidationType = vType,
                Code = cd,
                CodeType = cType,
                BasedOn = basedOn,
                BaseCode = bCode,
                BaseCodeType = bcType
            };
            this.dbContext.ClientTimeSheetProfileColumns.Add(rec);
            this.dbContext.SaveChanges();
        }

        private bool AddProfileColumn(string id, string client, int index, TimeSheetColumnDefault column, ClientTimeSheetProfile profile)
        {
            ClientTimeSheetProfileColumn rec = new ClientTimeSheetProfileColumn
            {
                CompanyID = GlobalVariables.CompanyID,
                ClientID = client,
                ProfileID = id,
                Type = profile.ProfileType ?? TimeSheetTypes.Regular,
                Name = column.Name.ToString(),
                ColNbr = index,
                ColType = column.ColType,
                Label = column.Label.ToString(),
                Status = GetColumnStatus(column.ColType ?? 0, column.Status, profile),
                ValidationType = column.ValidationType,
                Code = string.Empty,
                CodeType = column.CodeType,
                BasedOn = column.BasedOn.ToString(),
                BaseCode = string.Empty,
                BaseCodeType = 0
            };
            try
            {
                this.dbContext.ClientTimeSheetProfileColumns.Add(rec);
                this.dbContext.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return false;
            }
        }

        private bool AddProfileLDColumns(string id, int index, string name, string label, short? profileType)  // 11/07/2018 DS TFS # 3854
        {
            ClientTimeSheetProfileColumn rec = new ClientTimeSheetProfileColumn
            {
                CompanyID = GlobalVariables.CompanyID,
                ClientID = GlobalVariables.Client,
                ProfileID = id,
                Type = profileType ?? TimeSheetTypes.Regular,
                Name = name,
                ColNbr = index,
                ColType = TimeSheetColumnType.LaborDistr,
                Label = label,
                Status = TimeSheetColumnStatus.Editable,
                ValidationType = ValidationType.None,
                Code = string.Empty,
                CodeType = TimeSheetCodeType.Any,
                BasedOn = string.Empty,
                BaseCode = string.Empty,
                BaseCodeType = 0
            };
            try
            {
                this.dbContext.ClientTimeSheetProfileColumns.Add(rec);
                this.dbContext.SaveChanges();
                return true;
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return false;
            }
        }

        private short GetColumnStatus(short type, short? status, ClientTimeSheetProfile profile)
        {
            short result = status ?? TimeSheetColumnStatus.Hidden;
            short pr_type = profile.ProfileType ?? TimeSheetTypes.Regular;
            bool isCert = pr_type == TimeSheetTypes.CertifiedByEE || pr_type == TimeSheetTypes.CertifiedByJob;
            bool isJobCost = pr_type == TimeSheetTypes.JobCostByEE || pr_type == TimeSheetTypes.JobCostByJob;

            switch (type)
            {
                case TimeSheetColumnType.BenefitCode:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.CheckNbr:
                    result = (profile.HideCheckNumber) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable; // 10/02/2017 DS TFS # 2795
                    break;
                case TimeSheetColumnType.CodeAmount:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.BusinessExpAmount:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.JustAmount:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.CodeHours:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.DateDay:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Days:
                    result = (profile.HideDays) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable; // 10/02/2017 DS TFS # 2795
                    break;
                case TimeSheetColumnType.DeductionCode:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Department:
                    result = profile.DepartmentStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.EmployeeID:
                    result = (profile.TSSort == TimeSheetSortTypes.EmployeeID || profile.TSSort == TimeSheetSortTypes.DeptPlusEEID || profile.TSSort == TimeSheetSortTypes.PositionPlusEEID) ? TimeSheetColumnStatus.ReadOnly : TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.EENotes:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.EESSN:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.LocalTax:
                    result = profile.LocalTaxStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.OfferedHours:
                    result = (profile.HideOfferedHours) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable; // 10/02/2017 DS TFS # 2795
                    break;
                case TimeSheetColumnType.OTPayCode:
                    if (isCert) result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.PayCode:
                    result = (!isJobCost) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.Position:
                    result = profile.PositionStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Premium:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Rate:
                    result = ((bool)profile.PrintRateOnBlankTS) ? TimeSheetColumnStatus.HideEditable : TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Reduction:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.RegPayCode:
                    result = (isJobCost) ? TimeSheetColumnStatus.Editable : TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.SalaryLine:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.ShiftCode:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.StateTax:
                    result = profile.StateTaxStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.SutaState:
                    result = profile.SutaStateStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.TimeSheetID:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Weeks:
                    result = (profile.HideWeeks) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable; // 10/02/2017 DS TFS # 2795
                    break;
                case TimeSheetColumnType.WorkersComp:
                    result = profile.WCStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.VarPayCode:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                default:
                    //result = TimeSheetColumnStatus.ReadOnly;
                    break;
            }
            return result;
        }

        private short GetColumnStatus(short type, ClientTimeSheetProfile profile)
        {
            short result = TimeSheetColumnStatus.ReadOnly;
            short pr_type = profile.ProfileType ?? TimeSheetTypes.Regular;
            bool isCert = pr_type == TimeSheetTypes.CertifiedByEE || pr_type == TimeSheetTypes.CertifiedByJob;
            bool isJobCost = pr_type == TimeSheetTypes.JobCostByEE || pr_type == TimeSheetTypes.JobCostByJob;
            switch (type)
            {
                case TimeSheetColumnType.BenefitCode:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.CheckNbr:
                    result = (profile.HideCheckNumber) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.CodeAmount:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.BusinessExpAmount:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.JustAmount:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.CodeHours:
                    result = TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.Days:
                    result = (profile.HideDays) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.DateDay:
                    if (isCert) result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.DeductionCode:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Department:
                    result = profile.DepartmentStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.EmployeeID:
                    result = (profile.TSSort == TimeSheetSortTypes.EmployeeID || profile.TSSort == TimeSheetSortTypes.DeptPlusEEID || profile.TSSort == TimeSheetSortTypes.PositionPlusEEID) ? TimeSheetColumnStatus.ReadOnly : TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.EENotes:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.EESSN:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.LocalTax:
                    result = profile.LocalTaxStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.OfferedHours:
                    result = (profile.HideOfferedHours) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.OTPayCode:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.PayCode:
                    result = (!isJobCost) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.Position:
                    result = profile.PositionStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Premium:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Rate:
                    result = ((bool)profile.PrintRateOnBlankTS) ? TimeSheetColumnStatus.HideEditable : TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Reduction:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.RegPayCode:
                    result = (!isJobCost) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.SalaryLine:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.ShiftCode:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.StateTax:
                    result = profile.StateTaxStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.SutaState:
                    result = profile.SutaStateStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.TimeSheetID:
                    result = TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.Weeks:
                    result = (profile.HideWeeks) ? TimeSheetColumnStatus.Hidden : TimeSheetColumnStatus.Editable;
                    break;
                case TimeSheetColumnType.WorkersComp:
                    result = profile.WCStatus ?? TimeSheetColumnStatus.Hidden;
                    break;
                case TimeSheetColumnType.VarPayCode:
                    result = TimeSheetColumnStatus.Editable;
                    break;
            }
            return result;
        }

        private void AddSelectedColumnsToNewList(List<string> names, List<ClientTimeSheetProfileColumn> columns, ref List<ClientTimeSheetProfileColumn> newlist)
        {
            string code;
            int index = 1;
            List<ClientTimeSheetProfileColumn> codecolumns;
            ClientTimeSheetProfileColumn column = GetProfileColumnByName("TimeSheetID", columns);
            AddColumnToNewList(column, ref newlist, ref index);
            column = GetProfileColumnByName("EmployeeID", columns);
            AddColumnToNewList(column, ref newlist, ref index);
            foreach (string name in names)
            {
                column = GetProfileColumnByName(name, columns);
                byte type = GetProfileColumnType(column, out code);
                switch (type)
                {
                    case 1:
                        codecolumns = GetProfileCodeColumns(code, columns);
                        foreach (ClientTimeSheetProfileColumn col in codecolumns)
                        {
                            AddColumnToNewList(col, ref newlist, ref index);
                        }
                        break;
                    case 2:
                        codecolumns = GetProfileCodeBasedColumns(code, columns);
                        foreach (ClientTimeSheetProfileColumn col in codecolumns)
                        {
                            AddColumnToNewList(col, ref newlist, ref index);
                        }
                        break;
                    default:
                        AddColumnToNewList(column, ref newlist, ref index);
                        break;
                }
            }
            codecolumns = GetProfileHiddenColumns(columns, newlist);
            foreach (ClientTimeSheetProfileColumn col in codecolumns)
            {
                AddColumnToNewList(col, ref newlist, ref index);
            }
        }

        private ClientTimeSheetProfileColumn GetProfileColumnByName(string name, List<ClientTimeSheetProfileColumn> columns)
        {
            return columns.FirstOrDefault(c => c.Name == name);
        }

        private List<ClientTimeSheetProfileColumn> GetProfileHiddenColumns(List<ClientTimeSheetProfileColumn> columns, List<ClientTimeSheetProfileColumn> existcolumns)
        {
            try
            {
                List<ClientTimeSheetProfileColumn> result = columns.Where(c => c.Status == 0 && c.ColNbr > 2).OrderBy(c => c.ColNbr).ToList();
                return result.Where(c => !existcolumns.Any(ec => ec.Name == c.Name)).ToList();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return new List<ClientTimeSheetProfileColumn>();
            }
        }

        private List<ClientTimeSheetProfileColumn> GetProfileCodeColumns(string code, List<ClientTimeSheetProfileColumn> columns)
        {
            try
            {
                return columns.Where(c => c.Code == code).OrderBy(c => c.ColNbr).ToList();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return new List<ClientTimeSheetProfileColumn>();
            }
        }

        private List<ClientTimeSheetProfileColumn> GetProfileCodeBasedColumns(string code, List<ClientTimeSheetProfileColumn> columns)
        {
            try
            {
                return columns.Where(c => c.BasedOn == code).OrderBy(c => c.ColNbr).ToList();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return new List<ClientTimeSheetProfileColumn>();
            }
        }

        private byte GetProfileColumnType(ClientTimeSheetProfileColumn column, out string code)
        {
            code = string.Empty;
            if (column == null) return 0;
            if (!string.IsNullOrEmpty(column.Code))
            {
                code = column.Code.Trim();
                return 1;
            }
            if (!string.IsNullOrEmpty(column.BasedOn))
            {
                code = column.BasedOn.Trim();
                return 2;
            }
            return 0;
        }

        private void AddColumnToNewList(ClientTimeSheetProfileColumn column, ref List<ClientTimeSheetProfileColumn> newlist, ref int index)
        {
            if (column != null)
            {
                column.ColNbr = index;
                newlist.Add(column);
                index++;
            }
        }
        #endregion
    }
}