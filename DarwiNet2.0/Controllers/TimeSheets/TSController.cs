using DarwiNet2._0.Core;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels;
using DarwiNet2._0.ViewModels.TS;
using DataDrivenViewEngine.Models.Core;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Web.Mvc;
using System.Web.UI;
using DarwiNet2._0.Services;
using Thinkware.Pay360.Payroll;
using DarwiNet2._0.Extensions;

namespace DarwiNet2._0.Controllers.TimeSheets
{
    [IsSessionActive]
    public class TSController : Controller
    {
        private DnetEntities dbContext;
        private static TimeSheet timesheet;
        private int _company;
        private string _client, _employee; // TFS # 3444



        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            DNetAuth param = Services.Auth.GetParameters(); // TFS # 3444
            _company = param.CompanyID;
            _client = param.ClientID;
            _employee = param.EmployeeID;
            this.dbContext = new DnetEntities();
            base.Initialize(requestContext);
        }



        protected override void Dispose(bool disposing)
        {
            _company = 0;
            _client = null;
            _employee = null;
            base.Dispose(disposing);
        }



        #region Actions
        public ActionResult SysIndex(bool showAll)
        {
            int id = GlobalVariables.CurrentTimeSheetId;
            if (id <= 0) id = GlobalVariables.CurrentTimeSheet ?? 0;
            if (id > 0) GlobalVariables.Set<ITimeSheetInfo>($"TheTimeSheet_{GlobalVariables.CurrentSessionID()}_{id}", null);
            GlobalVariables.CurrentTimeSheetId = 0;
            GlobalVariables.CurrentTimeSheet = 0;
            _client = string.Empty;
            GlobalVariables.ShowAllSysTS = showAll;
            GlobalVariables.PayrollProfile = string.Empty;
            GlobalVariables.PayrollNumber = string.Empty;
            ViewBag.TimeSheetStatuses = new SelectList(Enums.ListFrom<enTimeSheetStatus>(), "Value", "Name", string.Empty);
            ViewBag.TimeSheetTypes = new SelectList(Enums.ListFrom<enTimeSheetTypes>(), "Value", "Name", string.Empty);
            ViewBag.Clients = TimeSheetClients();
            ViewBag.ShowAll = showAll;
            return View(GetSysTimeSheetsList(showAll));
        }



        public ActionResult SysApproval()
        {
            return RedirectToAction("SysIndex", "TS", new { showAll = true });
        }



        public ActionResult SysTimeSheets_Read(bool showAll, [DataSourceRequest] DataSourceRequest request)
        {
            return Json(GetSysTimeSheets(showAll)
                .ToDataSourceResult(request));
        }



        public ActionResult Index()
        {
            if (GlobalVariables.DNETLevel == DNetAccessLevel.System) return RedirectToAction("SysIndex", "TS", new { showAll = GlobalVariables.ShowAllSysTS });
            // 08/01/2017 DS TFS # 2695
            var securityEEs = GlobalVariables.SecurityEmployees;
            List<Employee> availEEs = this.dbContext.Employees.Where(e => e.CompanyID == _company && e.ClientID == _client).ToList();
            availEEs = availEEs.Where(e => securityEEs.Any(ee => ee == e.EmployeeID)).ToList();
            // 08/01/2017 end
            int id = GlobalVariables.CurrentTimeSheetId;
            if (id <= 0) id = GlobalVariables.CurrentTimeSheet ?? 0;
            if (id > 0) GlobalVariables.Set<ITimeSheetInfo>($"TheTimeSheet_{GlobalVariables.CurrentSessionID()}_{id}", null);
            GlobalVariables.CurrentTimeSheetId = 0;
            GlobalVariables.CurrentTimeSheet = 0;
            GlobalVariables.ShowAllSysTS = true;
            GlobalVariables.PayrollProfile = string.Empty;
            GlobalVariables.PayrollNumber = string.Empty;
            ViewBag.TimeSheetStatuses = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetStatus>(), "Value", "Name", string.Empty);
            ViewBag.TimeSheetTypes = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetTypes>(), "Value", "Name", string.Empty);
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeEntry);
            // 08/01/2017 DS TFS # 2695 - added IsProfileOK
            var timeSheets = this.dbContext.TimeSheets.Where(ts => ts.CompanyID == _company && ts.ClientID == _client && ts.Status != TimeSheetStatus.Deleted)
                .AsEnumerable()
                .OrderByDescending(ts => ts.TimeSheetID)
                .ToList();
            var timeSheetList = timeSheets
                .Select(x => new TimeSheetList
                {
                    TimeSheetID = x.TimeSheetID,
                    ProfileID = x.ProfileID,
                    TimeSheetName = x.TimeSheetName,
                    TimeSheetType = FieldTranslation.GetEnumDescription(typeof(enTimeSheetTypes), (int)x.TimeSheetType),
                    DateFrom = (x.DateFrom != null) ? new DateTime(x.DateFrom.Value.Year, x.DateFrom.Value.Month, x.DateFrom.Value.Day, 12, 0, 0, DateTimeKind.Utc) : (DateTime)x.DateFrom,  // 08/17/2017 DS TFS # 2718
                    DateTo = (x.DateTo != null) ? new DateTime(x.DateTo.Value.Year, x.DateTo.Value.Month, x.DateTo.Value.Day, 12, 0, 0, DateTimeKind.Utc) : (DateTime)x.DateTo, // 08/17/2017 DS TFS # 2718
                    Status = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), (int)x.Status),
                    Creator = x.Creator,
                    TRXNumber = 0, //GetTRXNumber(x.CompanyID, x.ClientID, x.TimeSheetID),
                    EENumber = 0, //GetEENumber(x.CompanyID, x.ClientID, x.TimeSheetID)
                    PayPeriod = FieldTranslation.GetEnumDescription(typeof(enPayPeriods), (int)(x.PayPeriod ?? 0))
                })
                .ToList();
            List<TimeSheetList> result = new List<TimeSheetList>();
            foreach (var ts in timeSheetList)
            {
                var timeSheet = timeSheets.First(x => x.TimeSheetID == ts.TimeSheetID);
                ts.TimeSheetType = FieldTranslation.GetEnumDescription(typeof(enTimeSheetTypes), (int)timeSheet.TimeSheetType);
                ts.Status = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), (int)timeSheet.Status);
                ts.PayPeriod = FieldTranslation.GetEnumDescription(typeof(enPayPeriods), (int)(timeSheet.PayPeriod ?? 0));
                if (IsProfileOK(ts.ProfileID, availEEs)) result.Add(ts);
            }
            ViewBag.CanAddTS = true; // No need to use this logic. // CanAddTimesheet();
            if (!ViewBag.CanAddTS) TempData["Error"] = "No available profiles, assigned to this client.";
            return View(result);
            //return View(timeSheets);
        }



        public ActionResult TimeSheets_Read([DataSourceRequest] DataSourceRequest request)
        {
            return Json(GetTimeSheets(_client)
                .ToDataSourceResult(request));
        }



        private List<DarwiNet2._0.Data.TimeSheet> GetSysTimeSheetsList(bool showAll)
        {
            List<DarwiNet2._0.Data.TimeSheet> listTS = new List<DarwiNet2._0.Data.TimeSheet>();
            try
            {
                if (showAll)
                {
                    listTS = this.dbContext.TimeSheets.Where(ts => ts.CompanyID == _company && (ts.Status == TimeSheetStatus.ApprovePEO || ts.Status == TimeSheetStatus.Released || ts.Status == TimeSheetStatus.Transferred || ts.Status == TimeSheetStatus.ApproveProcess)).OrderByDescending(ts => ts.TimeSheetID).ToList();
                }
                else
                {
                    listTS = this.dbContext.TimeSheets.Where(ts => ts.CompanyID == _company && ts.Status == TimeSheetStatus.ApprovePEO).OrderByDescending(ts => ts.TimeSheetID).ToList();
                    List<Code_Description> profileList = this.dbContext.ClientTimeSheetProfileApprovals.Where(p => p.CompanyID == _company && p.ClientID == _client && p.UserID == GlobalVariables.DNETOwnerID).Select(p => new Code_Description { Code = p.ProfileID, Description = p.ProfileID }).ToList();
                    listTS = listTS.Where(l => profileList.Any(p => p.Code == l.ProfileID)).ToList();
                }
            }
            catch { listTS = new List<DarwiNet2._0.Data.TimeSheet>(); }
            return listTS;
        }



        public static IEnumerable<DarwiNet2._0.Data.TimeSheet> GetSysTimeSheets(bool showAll)
        {
            var model = new DnetEntities();
            IEnumerable<DarwiNet2._0.Data.TimeSheet> ts_List;
            List<Code_Description> pr_List = new List<Code_Description>();
            if (showAll)
            {
                ts_List = model.TimeSheets.Where(ts => ts.CompanyID == GlobalVariables.CompanyID && (ts.Status == TimeSheetStatus.ApprovePEO || ts.Status == TimeSheetStatus.Released || ts.Status == TimeSheetStatus.Transferred || ts.Status == TimeSheetStatus.ApproveProcess)).OrderByDescending(ts => ts.TimeSheetID).OrderByDescending(ts => ts.TimeSheetID);
            }
            else
            {
                ts_List = model.TimeSheets.Where(ts => ts.CompanyID == GlobalVariables.CompanyID && ts.Status == TimeSheetStatus.ApprovePEO).OrderByDescending(ts => ts.TimeSheetID).OrderByDescending(ts => ts.TimeSheetID);
                pr_List = model.ClientTimeSheetProfileApprovals.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.UserID == GlobalVariables.DNETOwnerID && p.ApprovalLevel == 100).Select(p => new Code_Description { Code = p.ProfileID, Description = p.ClientID }).ToList();
                ts_List = ts_List.Where(l => pr_List.Any(p => p.Code == l.ProfileID && p.Description == l.ClientID)).ToList();
            }
            return ts_List.Select(ts => new DarwiNet2._0.Data.TimeSheet
            {
                TimeSheetID = ts.TimeSheetID,
                CompanyID = ts.CompanyID,
                ClientID = ts.ClientID,
                TimeSheetType = ts.TimeSheetType,
                TimeSheetName = ts.TimeSheetName,
                ProfileID = ts.ProfileID,
                SelectionType = ts.SelectionType,
                DateFrom = ts.DateFrom,
                DateTo = ts.DateTo,
                Status = ts.Status,
                CheckDate = ts.CheckDate,
                PageSize = ts.PageSize,
                RowsPerEE = ts.RowsPerEE,
                SortBy = ts.SortBy,
                DefDays = ts.DefDays,
                DefWeeks = ts.DefWeeks,
                DefHours = ts.DefHours,
                MaxRate = ts.MaxRate,
                MaxDayHours = ts.MaxDayHours,
                MaxEEHours = ts.MaxEEHours,
                MaxEEGrossAmount = ts.MaxEEGrossAmount,
                PCDecimals = ts.PCDecimals,
                BenDecimals = ts.BenDecimals,
                DedDecimals = ts.DedDecimals,
                PCSource = ts.PCSource,
                BenSource = ts.BenSource,
                DedSource = ts.DedSource,
                AutoSaveTime = ts.AutoSaveTime,
                MaskSSN = ts.MaskSSN,
                PrintSocSecOnBlankTS = ts.PrintSocSecOnBlankTS,
                PrintSocSecOnReports = ts.PrintSocSecOnReports,
                PrintRateOnBlankTS = ts.PrintRateOnBlankTS,
                PrintRateOnReports = ts.PrintRateOnReports,
                PrintYTDOnBlankTS = ts.PrintYTDOnBlankTS,
                PrintYTDOnReports = ts.PrintYTDOnReports,
                IgnoreWaiting = ts.IgnoreWaiting,
                AllowModifiedRatePermanent = ts.AllowModifiedRatePermanent,
                AllowAddEE = ts.AllowAddEE,
                AllowAddCode = ts.AllowAddCode,
                AllowTimeEntry = ts.AllowTimeEntry,
                EEDeptStatus = ts.EEDeptStatus,
                EEPositionStatus = ts.EEPositionStatus,
                Creator = ts.Creator,
                User = ts.User,
                Comment = ts.Comment,
                Requests = ts.Requests,
                ApprovalType = ts.ApprovalType,
                ApprovalStatus = ts.ApprovalStatus,
                TimeSheetTypeConverted = FieldTranslation.GetEnumDescription(typeof(enTimeSheetTypes), (int)ts.TimeSheetType),
                StatusConverted = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), (int)ts.Status)
            });
        }



        public static IEnumerable<DarwiNet2._0.Data.TimeSheet> GetTimeSheets(string client)
        {
            var model = new DnetEntities();
            if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
                return model.TimeSheets.Where(ts => ts.CompanyID == GlobalVariables.CompanyID && ts.Status == TimeSheetStatus.ApprovePEO).OrderByDescending(ts => ts.TimeSheetID).Select(ts => new DarwiNet2._0.Data.TimeSheet
                {
                    TimeSheetID = ts.TimeSheetID,
                    CompanyID = ts.CompanyID,
                    ClientID = ts.ClientID,
                    TimeSheetType = ts.TimeSheetType,
                    TimeSheetName = ts.TimeSheetName,
                    ProfileID = ts.ProfileID,
                    SelectionType = ts.SelectionType,
                    DateFrom = ts.DateFrom,
                    DateTo = ts.DateTo,
                    Status = ts.Status,
                    CheckDate = ts.CheckDate,
                    PageSize = ts.PageSize,
                    RowsPerEE = ts.RowsPerEE,
                    SortBy = ts.SortBy,
                    DefDays = ts.DefDays,
                    DefWeeks = ts.DefWeeks,
                    DefHours = ts.DefHours,
                    MaxRate = ts.MaxRate,
                    MaxDayHours = ts.MaxDayHours,
                    MaxEEHours = ts.MaxEEHours,
                    MaxEEGrossAmount = ts.MaxEEGrossAmount,
                    PCDecimals = ts.PCDecimals,
                    BenDecimals = ts.BenDecimals,
                    DedDecimals = ts.DedDecimals,
                    PCSource = ts.PCSource,
                    BenSource = ts.BenSource,
                    DedSource = ts.DedSource,
                    AutoSaveTime = ts.AutoSaveTime,
                    MaskSSN = ts.MaskSSN,
                    PrintSocSecOnBlankTS = ts.PrintSocSecOnBlankTS,
                    PrintSocSecOnReports = ts.PrintSocSecOnReports,
                    PrintRateOnBlankTS = ts.PrintRateOnBlankTS,
                    PrintRateOnReports = ts.PrintRateOnReports,
                    PrintYTDOnBlankTS = ts.PrintYTDOnBlankTS,
                    PrintYTDOnReports = ts.PrintYTDOnReports,
                    IgnoreWaiting = ts.IgnoreWaiting,
                    AllowModifiedRatePermanent = ts.AllowModifiedRatePermanent,
                    AllowAddEE = ts.AllowAddEE,
                    AllowAddCode = ts.AllowAddCode,
                    AllowTimeEntry = ts.AllowTimeEntry,
                    EEDeptStatus = ts.EEDeptStatus,
                    EEPositionStatus = ts.EEPositionStatus,
                    Creator = ts.Creator,
                    User = ts.User,
                    Comment = ts.Comment,
                    Requests = ts.Requests,
                    ApprovalType = ts.ApprovalType,
                    ApprovalStatus = ts.ApprovalStatus
                });
            else
                return model.TimeSheets.Where(ts => ts.CompanyID == GlobalVariables.CompanyID && ts.ClientID == client && ts.Status != TimeSheetStatus.Deleted).OrderByDescending(ts => ts.TimeSheetID).Select(ts => new DarwiNet2._0.Data.TimeSheet
                {
                    TimeSheetID = ts.TimeSheetID,
                    CompanyID = ts.CompanyID,
                    ClientID = ts.ClientID,
                    TimeSheetType = ts.TimeSheetType,
                    TimeSheetName = ts.TimeSheetName,
                    ProfileID = ts.ProfileID,
                    SelectionType = ts.SelectionType,
                    DateFrom = ts.DateFrom,
                    DateTo = ts.DateTo,
                    Status = ts.Status,
                    CheckDate = ts.CheckDate,
                    PageSize = ts.PageSize,
                    RowsPerEE = ts.RowsPerEE,
                    SortBy = ts.SortBy,
                    DefDays = ts.DefDays,
                    DefWeeks = ts.DefWeeks,
                    DefHours = ts.DefHours,
                    MaxRate = ts.MaxRate,
                    MaxDayHours = ts.MaxDayHours,
                    MaxEEHours = ts.MaxEEHours,
                    MaxEEGrossAmount = ts.MaxEEGrossAmount,
                    PCDecimals = ts.PCDecimals,
                    BenDecimals = ts.BenDecimals,
                    DedDecimals = ts.DedDecimals,
                    PCSource = ts.PCSource,
                    BenSource = ts.BenSource,
                    DedSource = ts.DedSource,
                    AutoSaveTime = ts.AutoSaveTime,
                    MaskSSN = ts.MaskSSN,
                    PrintSocSecOnBlankTS = ts.PrintSocSecOnBlankTS,
                    PrintSocSecOnReports = ts.PrintSocSecOnReports,
                    PrintRateOnBlankTS = ts.PrintRateOnBlankTS,
                    PrintRateOnReports = ts.PrintRateOnReports,
                    PrintYTDOnBlankTS = ts.PrintYTDOnBlankTS,
                    PrintYTDOnReports = ts.PrintYTDOnReports,
                    IgnoreWaiting = ts.IgnoreWaiting,
                    AllowModifiedRatePermanent = ts.AllowModifiedRatePermanent,
                    AllowAddEE = ts.AllowAddEE,
                    AllowAddCode = ts.AllowAddCode,
                    AllowTimeEntry = ts.AllowTimeEntry,
                    EEDeptStatus = ts.EEDeptStatus,
                    EEPositionStatus = ts.EEPositionStatus,
                    Creator = ts.Creator,
                    User = ts.User,
                    Comment = ts.Comment,
                    Requests = ts.Requests,
                    ApprovalType = ts.ApprovalType,
                    ApprovalStatus = ts.ApprovalStatus
                });
        }



        public ActionResult Details(int id)
        {
            timesheet = new TimeSheet();
            bool result = timesheet.Initialize(id);
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToSession(id); //09/26/2018 DS TFS # 3707 
            timesheet.Dispose();
            GlobalVariables.CurrentTimeSheetId = id;
            GlobalVariables.CurrentTimeSheet = id;
            if (!result)
            {
                TempData["Error"] = "Cannot open the timesheet. Try again";
                var pnumber = GlobalVariables.PayrollNumber;
                if (string.IsNullOrEmpty(pnumber))
                    return RedirectToAction("Index");
                else
                    return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
            }

            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        public ActionResult Next(int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
            //timesheet.InitFromSession(id);
            short type = timesheet.TimeSheetType();
            timesheet.SetNextPage();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToSession(id); //09/26/2018 DS TFS # 3707
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        public ActionResult SetPage(int id, string key)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
            //timesheet.InitFromSession(id);
            short type = timesheet.TimeSheetType();
            timesheet.SetPage(key);
            string current = timesheet.CurrentValue(type);
            timesheet.ToSession(id); //09/26/2018 DS TFS # 3707
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        public ActionResult Previous(int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
            //timesheet.InitFromSession(id);
            short type = timesheet.TimeSheetType();
            timesheet.SetPrevPage();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToSession(id); //09/26/2018 DS TFS # 3707
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        public ActionResult Edit(int id, string payrollNumber = null, bool? fromPayrollCockpit = null, bool edit = true)
        {
            GlobalVariables.CurrentTimeSheet = id;
            GlobalVariables.CurrentTimeSheetId = id;
            timesheet = new TimeSheet();
            bool result;
            if (string.IsNullOrEmpty(payrollNumber))
            {
                result = timesheet.Initialize(id);
            }
            else
            {
                var oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                GlobalVariables.PayrollNumber = payrollNumber;
                result = timesheet.Initialize(oPayrollNumber, id);
            }
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            //timesheet.ToSession(id); //09/26/2018 DS TFS # 3707
            timesheet.Dispose();
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeEntry);
            if (!result)
            {
                TempData["Error"] = "Cannot open the timesheet. Try again";
                if (string.IsNullOrEmpty(GlobalVariables.PayrollNumber))
                    return RedirectToAction("Index");
                else
                    return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
            }
            else
            {
                string action = GetControllerAction(type);
                if (action == "StandardTimeSheet")
                {
                    return RedirectToAction(action, "Payroll", new { id = id, key = key, payrollNumber, fromPayrollCockpit, edit });
                }
                else
                {
                    return RedirectToAction(action, "Payroll", new { id = id, key = key, p = payrollNumber });
                }
            }
        }

        [HttpPost]
        public ActionResult Edit(int id, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            new TimeSheet(id, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
            //timesheet.InitFromSession(id);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToSession(id); //09/26/2018 DS TFS # 3707
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }
        public ActionResult ProfileIndex()
        {
            return View(this.dbContext.ClientTimeSheetProfiles.Where(p => p.CompanyID == _company && p.ClientID == _client).ToList());
        }



        public ActionResult Create(string profileid, string tprofile = "", string df = "", string dt = "", string dc = "", string pnumber = "")
        {
            CreateVM vm = new CreateVM();
            DateTime nullDate = Convert.ToDateTime("01/01/1900");
            if (string.IsNullOrEmpty(profileid)) profileid = GlobalVariables.PayrollProfile;
            if (string.IsNullOrEmpty(pnumber)) pnumber = GlobalVariables.PayrollNumber;
            vm.SelectedProfile = string.Empty;
            vm.dispDateFrom = df;
            vm.dispDateTo = dt;
            vm.dispCheckDate = dc;

            var clientRecord = dbContext.Clients
                .Where(x => x.CompanyID == _company && x.ClientID == _client)
                .FirstOrDefault();

            if (clientRecord.IsNotNull())
            {
                vm.UseDnetPayroll = clientRecord.UseDnetPayroll;
            } else
            {
                vm.UseDnetPayroll = false;
            }

            if (!string.IsNullOrEmpty(profileid))
            {
                vm.SelectedProfile = profileid;
                if (!string.IsNullOrEmpty(pnumber))
                {
                    var payrollSchedule = this.dbContext.ClientPayrollSchedules.FirstOrDefault(x => x.CompanyID == _company && x.ClientID == _client && x.PayrollNumber == pnumber && x.ProfileID == profileid);
                    if (payrollSchedule != null)
                    {
                        vm.PayrollProfileID = profileid;
                        vm.PayrollNumber = pnumber;
                        vm.DateFrom = payrollSchedule.PayPeriod_BeginDate;
                        vm.DateTo = payrollSchedule.PayPeriod_EndDate;
                        vm.CheckDate = payrollSchedule.CheckDate;
                        vm.dispDateFrom = FieldTranslation.ToShortDate(vm.DateFrom ?? nullDate);
                        vm.dispDateTo = FieldTranslation.ToShortDate(vm.DateTo ?? nullDate);
                        vm.dispCheckDate = FieldTranslation.ToShortDate(vm.CheckDate ?? nullDate);
                    }
                    else
                    {
                        vm.PayrollProfileID = profileid;
                        vm.PayrollNumber = pnumber;
                        vm.dispDateFrom = df;
                        vm.DateFrom = GetDate(df);
                        vm.dispDateTo = dt;
                        vm.DateTo = GetDate(dt);
                        vm.dispCheckDate = dc;
                        vm.CheckDate = GetDate(dc);
                    }
                }
            }

            vm.Profiles = UserAvailableProfiles();
            vm.SortOptions = new SelectList(Enums.ListFrom<enTimeSheetSortTypes>(), "Value", "Name", string.Empty);
            vm.CanImport = !string.IsNullOrEmpty(ImportModel());
            vm.ProfileID = (vm.CanImport) ? string.IsNullOrEmpty(tprofile) ? "" : tprofile : "";
            vm.TimeSheetName = vm.ProfileID;
            vm.IsScheduled = !string.IsNullOrEmpty(tprofile);
            vm.CanSwipeImport = CanUseSwipeClockImport(TimeSheetTypes.Regular);
            vm.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeEntry);
            vm.RowsPerEE = 1;
            vm.CreateType = 1;
            return View(vm);
        }


        public ActionResult GetProfileSettings(string p, bool import)
        {
            var clientProfile = dbContext.ClientTimeSheetProfiles.FirstOrDefault(ctps => ctps.ClientID == _client && ctps.CompanyID == _company && ctps.ProfileID == p);
            if (clientProfile != null)
            {
                var result = new ProfileSettingsVM
                {
                    DateFrom = clientProfile.PayPeriodFrom != null ? (clientProfile.PayPeriodFrom ?? DateTime.MinValue).ToString("MM/dd/yyyy") : "",
                    DateTo = clientProfile.PayPeriodEnd != null ? (clientProfile.PayPeriodEnd ?? DateTime.MinValue).ToString("MM/dd/yyyy") : "",
                    SortBy = (clientProfile.TSSort ?? TimeSheetSortTypes.Name).ToString(),
                    Rows = (clientProfile.RowsPerEE ?? 0).ToString(),
                    AutoSave = clientProfile.AutoSave,
                    CanImport = import && clientProfile.ProfileType == TimeSheetTypes.Regular, 
                    ProfileType = clientProfile.ProfileType ?? TimeSheetTypes.Regular
                };
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            return Json("", JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public ActionResult Create(CreateVM vm)
        {
            // Validate the form
            if (!ModelState.IsValid)
            {
                vm.Profiles = UserAvailableProfiles();
                return View(vm);
            }

            ClientTimeSheetProfile profile = null;
            bool useImport = false;
            bool ignoreWarning = false;
            string fileName = string.Empty;
            string modelId = ImportModel();
            string errorMessage = string.Empty;

            if (!string.IsNullOrEmpty(vm.ProfileID))
            {
                profile = dbContext.ClientTimeSheetProfiles
                    .FirstOrDefault(p => p.CompanyID == _company && p.ClientID == _client && p.ProfileID == vm.ProfileID);
            }
            // Validate user imputs
            if (vm.DateFrom == null) vm.DateFrom = (profile != null) ? profile.PayPeriodFrom ?? DateTime.Now.Date : DateTime.Now.Date;
            if (vm.DateTo == null) vm.DateTo = (profile != null) ? profile.PayPeriodEnd ?? DateTime.Now.Date : DateTime.Now.Date;
            if (vm.CheckDate == null) vm.CheckDate = DateTime.Now.Date;

            if (vm.SortBy == null) vm.SortBy = (profile != null) ? profile.TSSort ?? TimeSheetSortTypes.Name : TimeSheetSortTypes.Name;
            if (vm.PageSize == null) vm.PageSize = 100;
            if (vm.RowsPerEE == null) vm.RowsPerEE = (profile != null) ? (profile.RowsPerEE ?? 1) : (short)1;

            // Convert short? to short
            short sortBy = Convert.ToInt16(vm.SortBy);
            short pageSize = Convert.ToInt16(vm.PageSize);
            short rowsPerEE = Convert.ToInt16(vm.RowsPerEE);

            if ((vm.CreateType ?? 1) != 1)
                vm.AutoSave = false;
            // If a file was uploaded, save it so we can import it later.
            else
            {
                if (!string.IsNullOrEmpty(modelId))
                {
                    int i = 0;

                    foreach (string file in Request.Files)
                    {
                        if (i == 0)
                        {
                            var PostedFile = Request.Files[file];

                            if (PostedFile != null)
                            {
                                string filename = Path.GetFileName(PostedFile.FileName);

                                if (!string.IsNullOrEmpty(filename))
                                {
                                    fileName = Path.Combine(GlobalVariables.ClientFolder, filename);
                                    PostedFile.SaveAs(fileName);
                                    useImport = true;
                                }
                            }
                            else
                            {
                                errorMessage = "Cannot upload import file.";
                                fileName = string.Empty;
                            }
                        }

                        i++;
                    }

                }
            }

            // Validate form dates (TODO: this shouldn't be done here.  It should be done client side)
            if (string.IsNullOrEmpty(errorMessage))
            {
                if (vm.DateFrom > vm.DateTo)
                {
                    errorMessage += "\"Date To\" cannot be earlier than \"Date From\"";
                }

                if (vm.CheckDate < vm.DateFrom)
                {
                    if (!string.IsNullOrEmpty(errorMessage)) errorMessage += "; ";
                    errorMessage += "\"Check Date\" cannot be earlier than \"Date From\"";
                }

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    errorMessage = "Cannot create time sheet: " + errorMessage + ".";
                }
                if (profile == null) errorMessage = "No timesheet profiles have been selected.";
            }

            if (string.IsNullOrEmpty(errorMessage))
            {
                timesheet = new TimeSheet();

                int id = 0;


                if (useImport)
                {
                    id = timesheet.Create(
                        vm.ProfileID,
                        vm.TimeSheetName,
                        fileName,
                        vm.DateFrom,
                        vm.DateTo,
                        vm.CheckDate,
                        sortBy,
                        pageSize,
                        rowsPerEE,
                        ignoreWarning,
                        vm.AutoSave,
                        vm.DeptDescr,
                        vm.PosDescr,
                        vm.Comment,
                        vm.PayrollProfileID,
                        vm.PayrollNumber,
                        vm.RecurringBatch
                    );
                }
                else
                {
                    id = timesheet.Create(
                        vm.ProfileID,
                        vm.TimeSheetName,
                        vm.DateFrom,
                        vm.DateTo,
                        vm.CheckDate,
                        sortBy,
                        pageSize,
                        rowsPerEE,
                        ignoreWarning,
                        vm.AutoSave,
                        vm.DeptDescr,
                        vm.PosDescr,
                        vm.Comment,
                        vm.PayrollProfileID,
                        vm.PayrollNumber,
                        vm.RecurringBatch
                    );
                }

                if (id > 0)
                {
                    short type;
                    bool result = true;
                    string key = TimeSheetKeyValue(id, out type);

                    if (type == TimeSheetTypes.Regular || !string.IsNullOrEmpty(key)) // 10/08/2018 DS TFS # 3782 (added regular timesheet
                    {
                        try
                        {
                            TimeSheet timeSheet = new TimeSheet(id);
                            key = timeSheet.CurrentValueDescription(type);
                            string current = timeSheet.CurrentValue(type);
                            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
                        }
                        catch (Exception ex)
                        {
                            errorMessage = "Cannot open created timesheet. Try again.";
                        }
                    }
                }
                else
                {
                    errorMessage = "Cannot create time sheet";
                }
            }

            if (errorMessage != string.Empty)
            {
                // Display error message and don't submit the page
                TempData["Error"] = errorMessage;
                vm.Profiles = UserAvailableProfiles();
                return View(vm);
            }
            var pnumber = GlobalVariables.PayrollNumber;
            if (string.IsNullOrEmpty(pnumber))
                return RedirectToAction("Index");
            else
                return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
        }



        [HttpPost]
        public ActionResult CreateFromImport(FormCollection setup)
        {
            DateTime dateFrom, dateTo, dateCheck;
            short sort, pagesize, eeRows;
            bool autoSave = false;
            bool useDDescr = false;
            bool usePDescr = false;
            bool ignoreWarning = false;
            string model = this.dbContext.ClientTimeImportSetups.Where(s => s.CompanyID == _company && s.ClientID == _client).First().ModelID;
            string fileName, description;
            int i = 0;
            bool importDone = false;
            description = string.Empty;
            fileName = string.Empty;
            foreach (string file in Request.Files)
            {

                if (i == 0)
                {
                    var PostedFile = Request.Files[file];
                    if (PostedFile != null)
                    {
                        string filename = Path.GetFileName(PostedFile.FileName);
                        fileName = Path.Combine(GlobalVariables.ClientFolder, filename);
                        PostedFile.SaveAs(fileName);
                    }
                    else
                    {
                        description = "Cannot upload import file.";
                        fileName = string.Empty;
                    }
                }
            }
            if (fileName != string.Empty)
            {
                string profileid = setup["ProfileID"];
                string comment = setup["Comment"];
                string payrollProfileId = setup["PayrollProfileID"];
                string payrollNumber = setup["PayrollNumber"];
                if (!DateTime.TryParse(setup["DateFrom"], out dateFrom)) dateFrom = DateTime.Now.Date;
                if (!DateTime.TryParse(setup["DateTo"], out dateTo)) dateTo = DateTime.Now.Date;
                if (!DateTime.TryParse(setup["CheckDate"], out dateCheck)) dateCheck = DateTime.Now.Date;
                if (!short.TryParse(setup["SortBy"], out sort)) sort = TimeSheetSortTypes.Name;
                if (!short.TryParse(setup["PageSize"], out pagesize)) pagesize = 100;
                if (!short.TryParse(setup["RowsPerEE"], out eeRows)) eeRows = 1;
                autoSave = !string.IsNullOrEmpty(setup["AutoSave"]);
                useDDescr = !string.IsNullOrEmpty(setup["DeptDescr"]);
                usePDescr = !string.IsNullOrEmpty(setup["PosDescr"]);
                timesheet = new TimeSheet();
                int id = timesheet.Create(profileid, fileName, dateFrom, dateTo, dateCheck, sort, pagesize, eeRows, ignoreWarning, autoSave, useDDescr, usePDescr, comment, payrollProfileId, payrollNumber);
                if (id > 0)
                {
                    try
                    {
                        timesheet = new TimeSheet(id);
                        timesheet.ToXml(id);
                        short type = timesheet.TimeSheetType();
                        string key = timesheet.CurrentValueDescription(type);
                        string current = timesheet.CurrentValue(type);
                        return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p  = current});
                    }
                    catch (Exception ex)
                    {
                        TempData["Error"] = "Cannot open created time sheet. Try to open from the list.";
                    }
                }
                TempData["Error"] = "Cannot create time sheet, based on import file.";
            }
            else TempData["Error"] = "Cannot upload import file.";
            var pnumber = GlobalVariables.PayrollNumber;
            if (string.IsNullOrEmpty(pnumber))
                return RedirectToAction("Index");
            else
                return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
        }



        public ActionResult ReturnToList(int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            if (timesheet.HasChanges()) timesheet.SaveData();
            timesheet.Dispose();
            var pnumber = GlobalVariables.PayrollNumber;
            if (string.IsNullOrEmpty(pnumber))
                return RedirectToAction("Index");
            else
                return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
        }



        [HttpPost]
        public ActionResult AddEmployees(int id, List<string> ee)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.AddEmployees(ee);
                timesheet.ResortData();
                short type = timesheet.TimeSheetType();
                if (timesheet.UseAutoSave() || (type != TimeSheetTypes.Regular && type != TimeSheetTypes.EETimeEntry)) timesheet.SaveTSData();
                string key = timesheet.CurrentValueDescription(type);
                string current = timesheet.CurrentValue(type);
                return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Cannot reload the time sheet.";
            }

            var pnumber = GlobalVariables.PayrollNumber;
            if (string.IsNullOrEmpty(pnumber))
                return RedirectToAction("Index");
            else
                return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
        }



        [HttpPost]
        public ActionResult AddCodeColumns(int id, short codetype, string codes)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.AddCodeColumns(codetype, codes.Split(',').ToList());
                timesheet.ToXml(id);
                short type = timesheet.TimeSheetType();
                string key = timesheet.CurrentValueDescription(type);
                string current = timesheet.CurrentValue(type);
                return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Cannot reload the time sheet.";
            }

            var pnumber = GlobalVariables.PayrollNumber;
            if (string.IsNullOrEmpty(pnumber))
                return RedirectToAction("Index");
            else
                return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
        }

        [HttpPost]
        public ActionResult AddCertJob(int id, string job, string ee)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.AddCertifiedJob(job, ee);
                timesheet.ToXml(id);
                timesheet.SaveTSData();
            }
            catch
            {
                TempData["Error"] = "Cannot add new Certified Job Assignment to the time sheet.";
            }
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        [HttpPost]
        public ActionResult AddEEToCertJob(int id, string job, string ee)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.AddCertifiedJobAssignment(job, ee);
                timesheet.ToXml(id);
                timesheet.SaveTSData();
            }
            catch
            {
                TempData["Error"] = "Cannot add new Certified Job Assignment to the time sheet.";
            }
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        [HttpPost]
        public ActionResult AddEEToJobCosting(int id, string job, string ee)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.AddJobCostingAssignment(job, ee);
                timesheet.ToXml(id);
                timesheet.SaveTSData();
            }
            catch
            {
                TempData["Error"] = "Cannot add new Certified Job Assignment to the time sheet.";
            }
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        [HttpPost]
        public ActionResult AddJobAssignment(int id, string job, string ee)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.AddJobCosting(job, ee);
                timesheet.ToXml(id);
                timesheet.SaveTSData();
            }
            catch
            {
                TempData["Error"] = "Cannot add new Job Costing Assignment to the time sheet.";
            }
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        [HttpPost]
        public ActionResult AddJobAssignments(FormCollection form)
        {
            if (!int.TryParse(form["id"], out int id)) id = 0;
            if (id > 0)
            {
                try
                {
                    if (form["jobcodes"] != null)
                    {
                        List<string> jobs = form["jobcodes"].ToString().Split(',').ToList();
                        if (jobs.Count() > 0)
                        {
                            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                            short tstype = timesheet.TimeSheetType();
                            foreach (string jobAssignment in jobs)
                            {
                                timesheet.AddJobCostingAssignment(jobAssignment, tstype);
                            }
                            timesheet.ToSession(id);
                            timesheet.ToXml(id);
                            timesheet.SaveTSData();
                        }
                        else
                            TempData["Error"] = "No new assignments were selected.";
                    }
                    else
                        TempData["Error"] = "No new assignments were selected.";
                }
                catch (Exception ex)
                {
                    TempData["Error"] = "Cannot add new Job Costing Assignments to the time sheet.";
                }
            }
            else
                TempData["Error"] = "Cannot find the timesheet";
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        public ActionResult ProcessTimeSheet(int id, string payrollNumber = null, bool edit = true)
        {
            bool canDo, canImport;
            List<Code_Description> codes, ees;
            try
            {
                if (string.IsNullOrEmpty(payrollNumber))
                    timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                else
                {
                    var oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                    _company = oPayrollNumber.CompanyId;
                    _client = oPayrollNumber.ClientId;
                    timesheet = new TimeSheet();
                    timesheet.Initialize(oPayrollNumber, id);
                }
                short type = timesheet.TimeSheetType();
                short level, totlevels;
                var ts_status = timesheet.CurrentStatus(out level, out totlevels, out short status);
                if (GlobalVariables.DNETLevel == DNetAccessLevel.System) _client = timesheet.TimeSheetClient();
                ViewBag.TimeSheetID = id;
                ViewBag.CanApprove = timesheet.CanApprove("");
                ViewBag.IsLocked = timesheet.IsLocked();
                string mode = timesheet.Mode(); 
                ViewBag.TimeSheetMode = mode;
                ViewBag.Comment = timesheet.GetSetupProperty("Comment");
                ViewBag.ErrorLog = IsErrorsExist(id);
                ViewBag.TimeSheetStatus = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), ts_status);
                ViewBag.NeedApproveConfirmation = ApproveAfterRequest(id);
                canImport = CanUseSwipeClockImport(timesheet.TimeSheetType());
                ees = timesheet.AvailableEmployees4TimeSheet(out canDo);
                if (ees == null) ees = new List<Code_Description>();
                var securityEEs = GlobalVariables.SecurityEmployees;
                ViewBag.AvailableEmployees = ees.Where(e => securityEEs.Any(sec => sec == e.Code)).ToList();
                ViewBag.CanAddEmployees = ees.Count() > 0;
                codes = timesheet.GetAvailableCodes(TimeSheetCodeType.PayCode, out canDo);
                bool canAdd = canDo; ;
                ViewBag.AvailablePayCodes = codes;
                ViewBag.CanAddPaycodes = canDo;
                if (type == TimeSheetTypes.Regular)
                {
                    codes = timesheet.GetAvailableCodes(TimeSheetCodeType.Benefit, out canDo);
                    ViewBag.AvailableBenefits = codes;
                    ViewBag.CanAddBenefits = canDo;
                    if (!canAdd) canAdd = canDo;
                    codes = timesheet.GetAvailableCodes(TimeSheetCodeType.Deduction, out canDo);
                    ViewBag.AvailableDeductions = codes;
                    ViewBag.CanAddDeductions = canDo;
                    if (!canAdd) canAdd = canDo;
                }
                ViewBag.CanAddCodes = canAdd;
                ViewBag.CanSwipeImport = canImport;
                GlobalVariables.CurrentTimeSheet = id;
                GlobalVariables.CurrentTimeSheetId = id;
                var sb = new StringBuilder();
                var sw = new StringWriter(sb);
                HtmlTextWriter htmlWriter = new HtmlTextWriter(sw);
                timesheet.DisplayTimeSheetGrid(ref htmlWriter, mode);
                ViewBag.TimeSheetGrid = sb.ToString();
                ViewBag.NeedRefreshChanges = timesheet.NeedToRefreshChanges();
                ViewBag.CanReedit = ts_status == TimeSheetStatus.Released && ViewBag.TimeSheetMode == "VIEW";
                sb.Clear();
                return View();
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Cannot reload the time sheet.";
            }

            var pnumber = GlobalVariables.PayrollNumber;
            if (string.IsNullOrEmpty(pnumber))
                return RedirectToAction("Index");
            else
                return RedirectToAction("BatchesList", new { payroll = GlobalVariables.PayrollProfile, pnumber = GlobalVariables.PayrollNumber });
        }



        public ActionResult Approve(int id, string payrollNumber = null)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            if (timesheet.IsProfileCorrect())
            {
                if (timesheet.HasChanges()) timesheet.SaveData();
                if (timesheet.CanApprove()) // 01/04/2018 DS TFS # 4094
                    timesheet.SetApprove(payrollNumber);
                else
                    TempData["Error"] = "Cannot approve 'empty' time sheet.";
            }
            else
                TempData["Error"] = "Cannot approve the time sheet, which uses the profile, doesn't assigned to any payroll profile.";
            if (string.IsNullOrWhiteSpace(payrollNumber))
                payrollNumber = GlobalVariables.PayrollNumber;
            if (!string.IsNullOrEmpty(payrollNumber) && GlobalVariables.DNETLevel == DNetAccessLevel.System)
                return RedirectToAction("Index", "PayrollTime", new { payrollNumber });
            else
                return RedirectToAction("Index");
        }

        public ActionResult RestoreRequests (int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            timesheet.RestoreRequests();
            string payrollNumber = GlobalVariables.PayrollNumber;
            if (!string.IsNullOrEmpty(payrollNumber) && GlobalVariables.DNETLevel == DNetAccessLevel.System)
                return RedirectToAction("Index", "PayrollTime", new { payrollNumber });
            else
                return RedirectToAction("Index");
        }




        public ActionResult Reedit(int id)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.SetReedit();
                short type = timesheet.TimeSheetType();
                string key = timesheet.CurrentValueDescription(type);
                string current = timesheet.CurrentValue(type);
                TempData["Success"] = "Successfully denied timesheet";
                if (GlobalVariables.DNETLevel == DNetAccessLevel.System) return RedirectToAction("SysIndex", "TS", new { showAll = GlobalVariables.ShowAllSysTS });
                return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Cannot reload the time sheet.";
            }

            return RedirectToAction("Index");
        }



        public ActionResult Save(int id)
        {
            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                timesheet.SaveData();
                short type = timesheet.TimeSheetType();
                string key = timesheet.CurrentValueDescription(type);
                string current = timesheet.CurrentValue(type);
                return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Cannot reload the time sheet.";
            }

            return RedirectToAction("Index");
        }


        public ActionResult Export(int id)
        {
            string errMsg = string.Empty;

            try
            {
                timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
                if (timesheet.CreateSpreadSheet(out errMsg)) timesheet.DownloadAsFile();
            }
            catch (Exception ex)
            {
                errMsg = string.IsNullOrEmpty(ex.InnerException.Message) ? ex.Message : ex.InnerException.Message;
            }

            if (string.IsNullOrEmpty(errMsg) || Response.IsRequestBeingRedirected)
                return new EmptyResult();
            else
            {
                TempData["Error"] = errMsg;
                return RedirectToAction("Edit", "TS", new { id = id });
            }
        }



        public ActionResult Reports(int id, char type, int Sort)
        {
            ViewBag.TimeSheetID = id;
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            ViewBag.ReportGrid = timesheet.GetTimeSheetTotalReport(type, Sort);
            timesheet.Dispose();
            return View();
        }



        public ActionResult Remove(int id, string payrollNumber = null)
        {
            DeleteTimeSheet(id);
            DeleteTimeSheetFiles(id);
            timesheet = new TimeSheet();
            timesheet.ClearXml(id);
            if (!string.IsNullOrWhiteSpace(payrollNumber) && GlobalVariables.DNETLevel == DNetAccessLevel.System)
                return RedirectToAction("Index", "PayrollTime", new { payrollNumber });
            else
                return RedirectToAction("Index");
        }



        public ActionResult Copy(int id)
        {
            if (GlobalVariables.DNETLevel != DNetAccessLevel.System) ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeEntry);
            return View(this.dbContext.TimeSheets.FirstOrDefault(t => t.CompanyID == _company && t.TimeSheetID == id));
        }



        [HttpPost]
        public ActionResult Copy(int id, string name)
        {
            CopyTimeSheet(id, GlobalVariables.DNETOwnerID, name);
            return RedirectToAction("Index");
        }



        public ActionResult EmployeeDetails(string emplId)
        {
            return View(this.dbContext.Employees.FirstOrDefault(e => e.CompanyID == _company && e.EmployeeID == emplId));
        }



        [HttpPost]
        public ActionResult SaveComment(int id, string note)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
            //timesheet.InitFromSession(id);
            timesheet.SaveNote(note);
            timesheet.ToSession(id); //09/26/2018 DS TFS # 3707
            timesheet.Dispose();
            return new EmptyResult();
        }



        public ActionResult ReorderColumns(int id)
        {
            ViewBag.TimeSheetID = id;
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
            //timesheet.InitFromSession(id);
            ViewBag.ProfileColumns = timesheet.ColumnsToOrder();
            timesheet.ToSession(id);
            timesheet.Dispose();
            return View();
        }



        [HttpPost]
        public ActionResult ReorderColumns(int id, string SelectedRangeItems)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            timesheet.ReorderColumns(SelectedRangeItems);
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public JsonResult HasChanges(int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            bool result = timesheet.HasChanges();
            return Json(result, JsonRequestBehavior.AllowGet);
        }



        [HttpPost]
        public JsonResult SaveValue(int tid, string id, string value)
        {
            int rowId = 0;
            int colId = 0;
            List<TimeSheetToCell> result = new List<TimeSheetToCell>();
            if (!string.IsNullOrEmpty(id))
            {
                if (id.Contains("R") && id.Contains("C"))
                {
                    string[] temp = id.Replace("C", "").Split('R');
                    if (temp.Length > 1)
                    {
                        if (!int.TryParse(temp[0], out colId)) colId = 0;
                        if (!int.TryParse(temp[1], out rowId)) rowId = 0;
                    }
                }
            }
            if (rowId > 0 && colId > 0)
            {
                timesheet = new TimeSheet(tid, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
                //timesheet.InitFromSession(tid);
                result = timesheet.SetValue(rowId, colId, value);
                timesheet.ToSession(tid); //09/26/2018 DS TFS # 3707
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }



        public ActionResult ShiftEdit(int id, int rowId, int colId, bool isSalary, bool isTips)
        {
            decimal rate, premium, pr_rate, fr_rate, min_rate, cert_otrate;
            bool canUpd, canView, canRollDown, flag, tmpbuf;
            short shStatus = TimeSheetColumnStatus.Hidden;
            List<Code_Description> shiftcodes;
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            bool isRegular = type == TimeSheetTypes.Regular;
            bool isCert = (type == TimeSheetTypes.CertifiedByEE || type == TimeSheetTypes.CertifiedByJob);
            string emplId = string.Empty;
            string job = string.Empty;
            string[] data = timesheet.GetRowData(rowId, out emplId, out job);
            if (!decimal.TryParse(data[colId - 1], out rate)) rate = 0;
            if (!decimal.TryParse(data[colId + 1], out premium)) premium = 0;
            if (!bool.TryParse(data[colId + 2], out flag)) flag = false;
            string code = data[colId - 3];
            string shift = data[colId];
            pr_rate = 0;
            fr_rate = 0;
            min_rate = 0;
            cert_otrate = 0;
            if (isCert) pr_rate = GetCertifiedJobRates(emplId, job, out fr_rate, out min_rate, out cert_otrate);
            ViewBag.IsCertified = isCert;
            ViewBag.EmployeeID = emplId;
            ViewBag.RowID = rowId;
            ViewBag.ColID = colId;
            ViewBag.ID = id;
            ViewBag.Code = code;
            ViewBag.Rate = rate;
            timesheet.GetColumnUpdateProperties(colId - 2, out canUpd, out canView, out canRollDown, out shStatus);
            shiftcodes = GetShiftCodes();
            bool anyView = canView || isCert;
            ViewBag.CanViewRate = canView;
            ViewBag.CanEditRate = canUpd;
            ViewBag.ShiftCodes = shiftcodes;
            ViewBag.Shift = shift;
            tmpbuf = isRegular && !isTips && (shiftcodes.Count() > 0) && (shStatus > 0);
            anyView = anyView || tmpbuf;
            ViewBag.CanViewShift = tmpbuf;
            ViewBag.CanViewPremium = tmpbuf;
            tmpbuf = isRegular && !isTips && (shStatus == TimeSheetColumnStatus.Editable) && shiftcodes.Count() > 0;
            ViewBag.CanEditShift = tmpbuf;
            anyView = anyView || tmpbuf;
            ViewBag.CanViewRollDown = canRollDown;
            ViewBag.Premium = premium;
            ViewBag.CanViewFlag = isRegular && isSalary;
            anyView = anyView || canRollDown || (isRegular && (isSalary || isTips));
            ViewBag.ShowJustReceipts = isRegular && isTips;
            ViewBag.ReductionFlag = flag;
            ViewBag.PrevailingRate = pr_rate;
            ViewBag.FringeRate = fr_rate;
            ViewBag.MinimumRate = min_rate;
            ViewBag.CertOTRate = cert_otrate;
            ViewBag.Total = rate + premium;
            tmpbuf = (!isTips) ? (canUpd) ? (bool.TryParse(timesheet.GetSetupProperty("AllowModifiedRatePermanent"), out flag)) ? flag : false : false : false;
            ViewBag.CanEditRatePermanent = tmpbuf; // 12/4/2017 DS TFS #2879
            ViewBag.CanViewAnything = anyView;
            return View();
        }



        public ActionResult TipsEdit(int id, int rowId, int colId)
        {
            decimal rate, premium;
            bool canUpd, canView, canRollDown, flag;
            List<Code_Description> shiftcodes;
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short shStatus = TimeSheetColumnStatus.Hidden;
            short type = timesheet.TimeSheetType();
            string emplId = string.Empty;
            string job = string.Empty;
            string[] data = timesheet.GetRowData(rowId, out emplId, out job);
            if (!decimal.TryParse(data[colId - 1], out rate)) rate = 0;
            if (!decimal.TryParse(data[colId + 1], out premium)) premium = 0;
            if (!bool.TryParse(data[colId + 2], out flag)) flag = false;
            string code = data[colId - 3];
            string shift = data[colId];
            ViewBag.EmployeeID = emplId;
            ViewBag.RowID = rowId;
            ViewBag.ColID = colId;
            ViewBag.ID = id;
            ViewBag.Code = code;
            ViewBag.Rate = rate;
            timesheet.GetColumnUpdateProperties(colId - 2, out canUpd, out canView, out canRollDown, out shStatus);
            ViewBag.CanViewRate = canView;
            ViewBag.CanEditRate = canUpd;
            ViewBag.CanViewRollDown = canRollDown;
            return View();
        }

        [HttpPost]
        public JsonResult ShiftChange(string code, string rate)
        {
            string result = "0.00";
            ShiftCode rec = this.dbContext.ShiftCodes.FirstOrDefault(c => c.CompanyID == _company && c.ShiftCode1 == code);
            if (rec != null)
            {
                if (!decimal.TryParse(rate, out decimal r)) r = decimal.Zero;
                decimal dvalue = (rec.ShiftType == ShiftType.Percent) ? ((rec.ShiftPercentage ?? (decimal)0) * r / (decimal)100.00) : rec.ShiftAmount ?? 0;
                result = dvalue.ToString("F2");
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }


        [HttpPost]
        public JsonResult ShiftEdit(int id, int rowId, int colId, FormCollection form)
        {
            decimal rate, dvalue;
            string temp;
            List<TimeSheetToCell> result = new List<TimeSheetToCell>();
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            string emplId = string.Empty;
            string job = string.Empty;
            bool isTips = form["isTips"].ToUpper() == "TRUE";
            bool savePermanent = !string.IsNullOrEmpty(form["SavePermanent"]);
            bool rollDown = !string.IsNullOrEmpty(form["RollDownRate"]);
            string[] data = timesheet.GetRowData(rowId, out emplId, out job);
            temp = form["rate"];
            if (decimal.TryParse(temp, out rate))
                data[colId - 1] = temp;
            else
            {
                rate = Convert.ToDecimal(data[colId - 1]);
                rollDown = false;
            }

            if (!isTips)
            {
                dvalue = decimal.Zero;
                temp = form["ShiftCode"];
                if (temp != data[colId]) data[colId] = temp;
                if (!string.IsNullOrEmpty(temp))
                {
                    if (!decimal.TryParse(form["premium"], out dvalue)) dvalue = decimal.Zero;
                    if (dvalue == decimal.Zero)
                    {
                        ShiftCode rec = this.dbContext.ShiftCodes.FirstOrDefault(c => c.CompanyID == _company && c.ShiftCode1 == temp);
                        if (rec != null) dvalue = (rec.ShiftType == ShiftType.Percent) ? ((rec.ShiftPercentage ?? (decimal)0) * rate / (decimal)100.00) : rec.ShiftAmount ?? 0;
                    }
                }
                data[colId + 1] = dvalue.ToString("F2");
                temp = form["reduction"];
                data[colId + 2] = (temp.StartsWith("true,")) ? "True" : "False";
            }
            if (rollDown)
            {
                var columns = timesheet.GetBasedCodeColumns(data[colId - 3]);
                foreach (var col in columns)
                {
                    string c_rate = "0.00000";
                    short c_type = col.CodeType ?? 0;
                    switch (c_type)
                    {
                        case PaycodeType.Overtime:
                            c_rate = ((decimal)(1.5) * rate).ToString("F5");
                            break;
                        case PaycodeType.DoubleTime:
                            c_rate = ((decimal)(2.0) * rate).ToString("F5");
                            break;
                        default:
                            c_rate = (rate).ToString("F5");
                            break;
                    }
                    data[col.ColID + 1] = c_rate;
                }

            }
            timesheet.SetRowData(rowId, data);
            result = timesheet.UpdateTotals(rowId, colId); // 12/29/2017 DS TFS # 2924
            if (!isTips && savePermanent) SaveRateFromTimeSheet(emplId, data[colId - 3], data[colId - 1], rollDown, type, job); //11/8/2017 DS TFS # 2841
            timesheet.ToXml(id);
            timesheet.Dispose();
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        public ActionResult SaveCodeRatePermanent(int id, string emplId, string name, FormCollection data)
        {
            string rate, code;
            int rowId, colId;
            string[] colInfo = name.Replace("R", string.Empty).Split('C');
            if (int.TryParse(colInfo[0], out rowId) && int.TryParse(colInfo[0], out colId))
            {
                rate = data[name];
                name = "R" + rowId.ToString() + "C" + (colId - 2).ToString();
                code = data[name];
                SaveRateFromTimeSheet(emplId, code, rate, true, TimeSheetTypes.Regular, string.Empty);
            }
            return View();
        }



        public ActionResult ExpensesIndex(int id, string emplId)
        {
            ViewBag.ExpenseStatus = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeRequestStatus>(), "Value", "Name", string.Empty);
            return View(GetEEBusinessExpenses(id, emplId));
        }



        public ActionResult ImportLogIndex(int id)
        {
            return View(GetImportLog(id));
        }



        private List<EmployeeBusinessExpens> GetEEBusinessExpenses(int id, string emplId)
        {
            try
            {
                return this.dbContext.EmployeeBusinessExpenses.Where(be => be.CompanyID == _company && be.EmployeeID == emplId && be.TimeSheetID == id).OrderBy(be => be.Date).ToList();
            }
            catch { return new List<EmployeeBusinessExpens>(); }
        }


        private List<TimeImportLog> GetImportLog(int id)
        {
            try
            {
                return this.dbContext.TimeImportLogs.Where(l => l.CompanyID == _company && l.TimeSheetID == id && l.Line >= 0).OrderBy(l => l.ID).ToList();
            }
            catch { return new List<TimeImportLog>(); }
        }



        public ActionResult ReplicateRow(int id, int rowId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ReplicateRow(rowId);
            if (timesheet.UseAutoSave() || (type != TimeSheetTypes.Regular && type != TimeSheetTypes.EETimeEntry)) 
                timesheet.SaveTSData();
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult ReplicateRow(int id, int rowId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ReplicateRow(rowId, form);
            if (timesheet.UseAutoSave() || (type != TimeSheetTypes.Regular && type != TimeSheetTypes.EETimeEntry)) timesheet.SaveTSData();
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }



        public ActionResult ConsolidatedTime(int id, string emplId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            ViewBag.PunchesGrid = timesheet.GetPunches(emplId);
            timesheet.Dispose();
            return View();
        }



        public ActionResult ConsolidatedTimeEntry(int id, string emplId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            ViewBag.TimeEntryGrid = timesheet.GetTimeEntry(emplId);
            timesheet.Dispose();
            return View();
        }



        public ActionResult ConsolidateRow(int id, int rowId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.RestorePTOPunches();
            timesheet.ConsolidateRow(rowId);
            timesheet.ConsolidateBusinessExpensesRow(rowId);
            timesheet.ResortData();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.SaveData();
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        public ActionResult ConsolidateAll(int id)
        {
            if (CanUseSwipeClockImport(TimeSheetTypes.Regular)) return RedirectToAction("ConsolidateSwipeClock", new { id = id });
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.RestorePTOPunches();
            timesheet.ConsolidateAllEETime();
            timesheet.ConsolidateBusinessExpenses();
            timesheet.ResortData();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.SaveData();
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        public ActionResult ConsolidateSwipeClock(int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            string err = string.Empty;
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);

            if (timesheet.AddSwipeClockImport(out err))
            {
                timesheet.RestorePTOPunches();
                timesheet.ConsolidateAllEETime();
                timesheet.ConsolidateBusinessExpenses();
                timesheet.ResortData();
                timesheet.SaveData();
                err = string.Empty;
            }
            else err = "Cannot finish SwipeClock import: " + err;
            if (err.Trim() != string.Empty) TempData["Error"] = err;
            timesheet.Dispose();

            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult ConsolidateRow(int id, int rowId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.RestorePTOPunches();
            timesheet.ConsolidateRow(rowId);
            timesheet.ConsolidateBusinessExpensesRow(rowId);
            timesheet.ResortData();
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.SaveTSData();
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }



        public ActionResult RemoveRow(int id, int rowId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.RemoveRow(rowId);
            timesheet.ResortData();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            if (timesheet.UseAutoSave() || (type != TimeSheetTypes.Regular && type != TimeSheetTypes.EETimeEntry)) timesheet.SaveTSData();
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult RemoveRow(int id, int rowId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.RemoveRow(rowId);
            timesheet.ResortData();
            string key1 = timesheet.CurrentValueDescription(type);
            if (timesheet.UseAutoSave() || (type != TimeSheetTypes.Regular && type != TimeSheetTypes.EETimeEntry)) timesheet.SaveTSData();
            timesheet.ToXml(id);
            timesheet.Dispose();

            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1 });
        }



        public ActionResult MoveColumnLeft(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.MoveColumn(colId, MoveDirection.Left);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult MoveColumnLeft(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.MoveColumn(colId, MoveDirection.Left);
            string key1 = timesheet.CurrentValueDescription(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1 });
        }



        public ActionResult MoveColumnRight(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.MoveColumn(colId, MoveDirection.Right);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult MoveColumnRight(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.MoveColumn(colId, MoveDirection.Right);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }



        public ActionResult FillDefaultHours(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession); // 10/01/2018 DS TFS # 3426
            //timesheet.InitFromSession(id);
            short type = timesheet.TimeSheetType();
            timesheet.FillDefHours(colId);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            //timesheet.ToSession(id); //09/26/2018 DS TFS # 3707
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult FillDefaultHours(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.FillDefHours(colId);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }

        public ActionResult RaiseReductions(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.SetColumnReductions(colId, true);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        public ActionResult ClearColumnHours(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ClearAmountColumn(colId);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        public ActionResult DropReductions(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.SetColumnReductions(colId, false);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }

        [HttpPost]
        public ActionResult ClearColumnHours(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.ClearAmountColumn(colId);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }



        public ActionResult HideRateColumn(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.TriggerColumnStatus(colId);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult HideRateColumn(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.TriggerColumnStatus(colId);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }



        public ActionResult ShowRateColumn(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.TriggerColumnStatus(colId);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult ShowRateColumn(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.TriggerColumnStatus(colId);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }



        public ActionResult CollapseColumn(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.TriggerColumnStatus(colId - 1);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        public ActionResult CollapseAllColumns(int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.CollapseAllColumns();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        public ActionResult ExpandAllColumns(int id)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ExpandAllColumns();
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult CollapseColumn(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.TriggerColumnStatus(colId - 1);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }



        public ActionResult ExpandColumn(int id, int colId)
        {
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.TriggerColumnStatus(colId - 1);
            string key = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key, p = current });
        }



        [HttpPost]
        public ActionResult ExpandColumn(int id, int colId, FormCollection data)
        {
            Dictionary<string, string> form = new Dictionary<string, string>();
            foreach (string key in Request.Form.AllKeys) form.Add(key, Request.Form[key]);
            timesheet = new TimeSheet(id, TimeSheetInitializationSource.FromSession);
            short type = timesheet.TimeSheetType();
            timesheet.ReadData(form);
            timesheet.TriggerColumnStatus(colId - 1);
            string key1 = timesheet.CurrentValueDescription(type);
            string current = timesheet.CurrentValue(type);
            timesheet.ToXml(id);
            timesheet.Dispose();
            return RedirectToAction(GetControllerAction(type), "Payroll", new { id = id, key = key1, p = current });
        }
        #endregion

        #region D2 Actions
        public ActionResult BatchesList(string payroll, string pnumber)
        {
            GlobalVariables.PayrollProfile = payroll;
            GlobalVariables.PayrollNumber = pnumber;
            var securityEEs = GlobalVariables.SecurityEmployees;
            List<Employee> availEEs = this.dbContext.Employees.Where(e => e.CompanyID == _company && e.ClientID == _client).ToList();
            availEEs = availEEs.Where(e => securityEEs.Any(ee => ee == e.EmployeeID)).ToList();
            ViewBag.TimeSheetStatuses = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetStatus>(), "Value", "Name", string.Empty);
            ViewBag.TimeSheetTypes = new SelectList(Enums.ListFrom<DataDrivenViewEngine.Models.Core.enTimeSheetTypes>(), "Value", "Name", string.Empty);
            //ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTimeEntry); ???
            ViewBag.AvailableTimesheets = GetPayrollTimeSheetsList(payroll, string.Empty);
            List<TimeSheetList> result = GetPayrollTimeSheetsList(payroll, pnumber);
            return View(result);

        }

        public ActionResult AddToPayrollCockpit(string payroll, string pnumber, int id)
        {
            List<string> err = new List<string>();
            string msg = string.Empty;
            var ts = this.dbContext.TimeSheets.FirstOrDefault(x => x.TimeSheetID == id);
            if (ts != null)
            {
                //string profile = GetPayrollProfile(payroll);
                if (!string.IsNullOrEmpty(payroll) && payroll == ts.PayrollProfileID && ts.Status == TimeSheetStatus.Released && !string.IsNullOrEmpty(pnumber) && string.IsNullOrEmpty(ts.PayrollNumber))
                {
                    ts.PayrollNumber = pnumber;
                    this.dbContext.SaveChanges();
                    return RedirectToAction("BatchesList", new { payroll = payroll, pnumber = pnumber });
                }
                else
                {
                    msg = "Cannot add the timesheet to payroll cockpit:";
                    if (ts.Status != TimeSheetStatus.Released)
                        msg += ": the timesheet's status has to be 'Released'.";
                    else
                    {
                        if (!string.IsNullOrEmpty(ts.PayrollNumber)) msg += ": this timesheet already assigned to payroll.";
                    }
                }
            }
            else
                msg = "Cannot add the timesheet to payroll cockpit: the timesheet record doesn't exist anymore.";
            if (!string.IsNullOrEmpty(msg)) TempData["Error"] = msg;
            return RedirectToAction("Details", new { id = id });

        }

        public ActionResult RemoveFromPayrollCockpit(string payroll, string pnumber, int id)
        {
            List<string> err = new List<string>();
            string msg = string.Empty;
            var ts = this.dbContext.TimeSheets.FirstOrDefault(x => x.TimeSheetID == id);
            if (ts != null)
            {
                string errmsg = string.Empty;
                switch (ts.Status)
                {
                    case TimeSheetStatus.InPayroll:
                        msg = "Cannot remove the timesheet from payroll cockpit: the timesheet is locked ('In Payroll' status).";
                        break;
                    case TimeSheetStatus.Released:
                        ts.PayrollNumber = string.Empty;
                        ts.Status = TimeSheetStatus.Released;
                        string batchID = _client + "-" + id.ToString();
                        string deleteBatchDetails = "DELETE PayrollBatchDetails WHERE CompanyID = {0} AND BatchNumber LIKE '{1}%'";
                        string deleteBatch = "DELETE PayrollBatches WHERE CompanyID = {0} AND BatchNumber LIKE '{1}%'";
                        int erNum = 0;
                        string sql = string.Format(deleteBatchDetails, _company.ToString(), batchID);
                        DataDriver.ExecuteQuery(sql, out erNum, out msg);
                        if (erNum == 0)
                        {
                            sql = string.Format(deleteBatch, _company.ToString(), batchID);
                            DataDriver.ExecuteQuery(sql, out erNum, out msg);
                        }
                        else
                            msg = "Cannot remove the timesheet from payroll cockpit: " + msg + ".";
                        this.dbContext.SaveChanges();
                        break;
                    default:
                        msg = "Cannot remove the timesheet from payroll cockpit: the timesheet has wrong status.";
                        break;

                }
            }
            else
                msg = "Cannot remove the timesheet from payroll: the timesheet record doesn't exist anymore.";
            if (!string.IsNullOrEmpty(msg)) TempData["Error"] = msg;
            return RedirectToAction("BatchesList", new { payroll = payroll, pnumber = pnumber });
        }

        public ActionResult SelectForPayroll(int id)
        {
            var ts = this.dbContext.TimeSheets.FirstOrDefault(x => x.TimeSheetID == id);
            if (ts != null && ts.Status == TimeSheetStatus.Released)
            {
                string key = _client + "-" + id.ToString() + "-";
                List<PayrollBatch> batches = this.dbContext.PayrollBatches.Where(b => b.CompanyID == _company && b.ClientID == _client && b.BatchNumber.StartsWith(key)).ToList();
                foreach (var batch in batches)
                {
                    batch.BatchMarked = true;
                    batch.MarkedByUser = GlobalVariables.DNETOwnerID;
                }
                ts.Status = TimeSheetStatus.InPayroll;
                this.dbContext.SaveChanges();
            }
            return new EmptyResult();
        }

        public ActionResult UnSelectFromPayroll(int id)
        {
            string key = _client + "-" + id.ToString() + "-";
            var ts = this.dbContext.TimeSheets.FirstOrDefault(x => x.TimeSheetID == id);
            if (ts != null)
            {
                List<PayrollBatch> batches = this.dbContext.PayrollBatches.Where(b => b.CompanyID == _company && b.ClientID == _client && b.BatchNumber.StartsWith(key)).ToList();
                foreach (var batch in batches)
                {
                    batch.BatchMarked = false;
                    batch.MarkedByUser = string.Empty;
                }
                ts.Status = TimeSheetStatus.Released;
                this.dbContext.SaveChanges();
            }
            return new EmptyResult();
        }

        #endregion

        #region Time Sheet Notes
        public ActionResult NotesIndex(int tid, string emplId, int teid = 0)
        {
            ViewBag.TimeSheetID = tid;
            ViewBag.TimeEntryID = teid;
            if (emplId != "*") ViewBag.EmployeeID = emplId;
            ViewBag.PayrollNotes = (teid == 0) ? GetEEPayrollNotes(tid, emplId) : null;
            ViewBag.ExistingNotes = this.dbContext.TimeSheetNotes.Where(a => a.TimeSheetID == tid && a.EmployeeID == emplId).OrderBy(a => a.CreatedDateTime).ToList<TimeSheetNote>();
            return View();
        }



        public ActionResult NoteView(int tid, int id)
        {
            TimeSheetNote thisNote = this.dbContext.TimeSheetNotes.Where(a => a.TimeSheetID == tid && a.ID == id).FirstOrDefault();
            ViewBag.NoteText = string.Empty;
            if (thisNote != null)
            {
                ViewBag.NoteText = thisNote.NoteText;
            }
            return View();
        }



        public ActionResult PayrollNoteView(int tid, decimal id) //03/20/2018 DS TFS # 3035
        {
            EmployeePayrollNote thisNote = this.dbContext.EmployeePayrollNotes.FirstOrDefault(a => a.CompanyID == _company && a.RecordNumber == id);
            ViewBag.NoteText = string.Empty;
            if (thisNote != null)
            {
                ViewBag.PayrollNoteText = thisNote.NotesText;
            }
            return View();
        }



        bool CanDeleteNote(int tid, int id)
        {
            return true;
        }



        List<EmployeePayrollNote> GetEEPayrollNotes(int tid, string e) //03/20/2018 DS TFS # 3035
        {
            DarwiNet2._0.Data.TimeSheet ts = dbContext.TimeSheets.FirstOrDefault(t => t.CompanyID == _company && t.TimeSheetID == tid);
            List<EmployeePayrollNote> notes = new List<EmployeePayrollNote>();
            if (ts != null)
            {
                try
                {
                    notes = dbContext.EmployeePayrollNotes.Where(n => n.CompanyID == _company && n.EmployeeID == e && !n.Inactive && (n.ValidFrom >= ts.DateFrom || n.ValidFrom <= ts.DateTo || n.ValidTo >= ts.DateFrom || n.ValidTo <= ts.DateTo)).OrderBy(n => n.CreatedDate).ToList();
                }
                catch { notes = new List<EmployeePayrollNote>(); }
            }
            return notes;
        }



        public ActionResult NoteDelete(int tid, int id, string emplId)
        {
            if (CanDeleteNote(tid, id))
            {
                TimeSheetNote rec = this.dbContext.TimeSheetNotes.FirstOrDefault(a => a.TimeSheetID == tid && a.ID == id);
                if (rec != null)
                {
                    this.dbContext.TimeSheetNotes.Remove(rec);
                    this.dbContext.SaveChanges();
                }
            }
            return new EmptyResult();
        }



        public ActionResult NoteCreate(int tid, string emplId, int teid = 0)
        {
            ViewBag.TimeSheetID = tid;
            ViewBag.TimeEntryID = teid;
            ViewBag.EmployeeID = emplId;
            return View();
        }



        [HttpPost]
        public ActionResult NoteCreate(FormCollection noteInfo)
        {
            int tsID, teID;
            string eid = noteInfo["eeid"];
            string subject = noteInfo["Subject"];
            string note = noteInfo["NoteText"];
            if (!int.TryParse(noteInfo["tsid"], out tsID)) tsID = 0;
            if (!int.TryParse(noteInfo["teid"], out teID)) teID = 0;
            TimeSheetNote rec = new TimeSheetNote
            {
                TimeSheetID = tsID, 
                TimeEntryID = teID, //0,
                EmployeeID = noteInfo["eeid"],
                Subject = subject,
                NoteText = note,
                CreatedBy = GlobalVariables.DNETOwnerID,
                CreatedDateTime = DateTime.Now
            };
            try
            {
                this.dbContext.TimeSheetNotes.Add(rec);
                this.dbContext.SaveChanges();
            }
            catch
            {
                ModelState.AddModelError("TimeSheetNotes", "Please Enter the Note");
                ViewBag.TimeSheetID = tsID;
                ViewBag.TimeEntryID = teID;
                ViewBag.EmployeeID = eid;
                return View();
            }
            return Json(TimeEntryNotesCount(tsID, eid), JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region Time Sheet EE Email
        public ActionResult EEMailCreate(int tid, string emplId)
        {
            string name;
            ViewBag.TimeSheetID = tid;
            ViewBag.EmployeeID = emplId;
            ViewBag.EmployeeEmail = EEMailInfo(emplId, out name);
            ViewBag.EmployeeName = name;
            return View();
        }



        [HttpPost]
        public ActionResult EEMailCreate(int tid, string emplId, FormCollection mailInfo)
        {
            string name, email;
            email = EEMailInfo(emplId, out name);
            string subject = mailInfo["Subject"];
            string body = mailInfo["Body"];
            string mailResult = MailToEE(tid, emplId, name, email, subject, body);
            if (string.IsNullOrEmpty(mailResult))
                TempData["Success"] = "Message successfully sent.";
            else
                TempData["Error"] = mailResult;
            return new EmptyResult();
        }



        private string EEMailInfo(string emplId, out string name)
        {
            string result = string.Empty;
            name = string.Empty;
            try
            {
                Employee ee = this.dbContext.Employees.FirstOrDefault(e => e.EmployeeID == emplId);
                if (ee != null)
                {
                    var eeEmail = new EmployeeEmail();
                    result = eeEmail.EEEmail(ee.Email, ee.DefaultEmailAddress, ee.DarwinetEmail);
                    name = ee.FirstName + " " + ee.LastName;
                }
            }
            catch { }
            return result;
        }



        private string MailToEE(int tid, string emplId, string name, string addr, string subject, string body)
        {
            string result = string.Empty;
            if (subject == string.Empty) subject = "Time Sheet# " + tid.ToString() + " request";
            if (!Services.Email.EmailEnabled.CheckDisabled(_company, _client) && addr != string.Empty)
            {
                if (SmtpEmail.SendSmtpEmail(addr, name, subject, body, new List<System.Net.Mail.Attachment>(), false, null, null, out result)) result = string.Empty;
            }
            else result = "Must have recipient address first";
            return (result != string.Empty) ? "<li>" + result + "</li>" : string.Empty;
        }
        #endregion

        #region Time Sheet EE Attachments
        public ActionResult AttachmentsIndex(int tid, string emplId)
        {
            ViewBag.EmployeeID = emplId;
            return View(this.dbContext.TimeSheetAttachments.Where(a => a.TimeSheetID == tid && a.EmployeeID == emplId).OrderBy(a => a.CreatedDateTime).ToList<TimeSheetAttachment>());
        }



        public ActionResult AttachmentView(int tid, int id)
        {
            return View(this.dbContext.TimeSheetAttachments.FirstOrDefault(a => a.TimeSheetID == tid && a.ID == id));
        }



        bool CanDeleteAttachment(int tid, int id)
        {
            return true;
        }



        public ActionResult AttachmentDelete(int tid, int id, string emplId)
        {
            if (CanDeleteAttachment(tid, id))
            {
                TimeSheetAttachment rec = this.dbContext.TimeSheetAttachments.FirstOrDefault(a => a.TimeSheetID == tid && a.ID == id);
                if (rec != null)
                {
                    this.dbContext.TimeSheetAttachments.Remove(rec);
                    this.dbContext.SaveChanges();
                }
            }
            return RedirectToAction("AttachmentIndex", new { tid = tid, emplId = emplId });
        }



        public ActionResult AttachmentCreate(int tid, string emplId)
        {
            ViewBag.TimeSheetID = tid;
            ViewBag.EmployeeID = emplId;
            return View();
        }



        [HttpPost]
        public ActionResult AttachmentCreate(int tid, string emplId, FormCollection attInfo)
        {
            int i = 0;
            int docs = 0;
            foreach (string file in Request.Files)
            {

                if (i == 0)
                {
                    docs++;
                    var PostedFile = Request.Files[file];
                    if (PostedFile != null)
                    {
                        string filetype = PostedFile.ContentType;
                        int filelength = PostedFile.ContentLength;
                        Stream filestream = PostedFile.InputStream;
                        byte[] filedata = new byte[filelength];
                        int? eeStamppage = null;
                        int? ccStamppage = null;
                        string filename = Path.GetFileName(PostedFile.FileName);
                        string docname = (string.IsNullOrEmpty(attInfo["Description"])) ? Path.GetFileNameWithoutExtension(PostedFile.FileName) : attInfo["Description"];
                        filestream.Read(filedata, 0, filelength);
                        var data = new TimeSheetAttachment
                        {
                            TimeSheetID = tid,
                            EmployeeID = emplId,
                            AttachmentName = filename,
                            AttachmentDescription = docname,
                            ContentType = filetype,
                            AttachmentBody = filedata,
                            CreatedBy = GlobalVariables.DNETOwnerID,
                            CreatedDateTime = DateTime.Now
                        };
                        try
                        {
                            this.dbContext.TimeSheetAttachments.Add(data);
                            this.dbContext.SaveChanges();
                        }
                        catch
                        {
                            docs = 0;
                        }
                    }

                }
                i++;
            }

            if (docs == 0)
            {

                ModelState.AddModelError("Attachment", "Please upload a file");
                ViewBag.TimeSheetID = tid;
                ViewBag.EmployeeID = emplId;
                return View();
            }
            else
            {
                return RedirectToAction("AttachmentIndex", new { tid = tid, emplId = emplId });
            }
        }
        #endregion

        #region Aprove Time Sheet
        private bool ApproveAfterRequest(int id)
        {
            short status;
            if (IsErigoTimeSheet(id, out status))
            {
                if (status == TimeSheetStatus.Edit) return CheckConsolidatedTE(id);
            }
            return false;
        }



        private bool CheckConsolidatedTE(int id)
        {
            return this.dbContext.EmployeeTimeEntries.Where(t => t.TimeSheetID == id && t.IsErigoTimeEntry && t.Status != TimeEntryStatus.Consolidated && t.Status != TimeEntryStatus.Locked && t.Status != TimeEntryStatus.Deleted && t.Status != TimeEntryStatus.Empty).Count() > 0;
        }



        private bool IsErigoTimeSheet(int id, out short status)
        {
            string client = _client;
            status = TimeSheetStatus.New;
            DarwiNet2._0.Data.TimeSheet ts = this.dbContext.TimeSheets.FirstOrDefault(t => t.CompanyID == _company && t.TimeSheetID == id);
            if (ts != null)
            {
                status = ts.Status ?? TimeSheetStatus.New;
                if (ts.AllowTimeEntry)
                {
                    ClientTimePunchesSetup setup = this.dbContext.ClientTimePunchesSetups.FirstOrDefault(s => s.CompanyID == _company && s.ClientID == client && s.Enabled);
                    if (setup != null) return setup.EntryType == ConsolidateStatus.DayTime;
                }
            }
            return false;
        }
        #endregion

        #region Private Functions
        private bool CanAddTimesheet()
        {
            var profiles = dbContext.ClientTimeSheetProfiles.Where(x => x.CompanyID == _company && x.ClientID == _client).ToList();
            bool result = profiles.Any();
            if (result && GlobalVariables.UseD2)
            {
                result = dbContext.PayrollProfileSettings.Where(x => x.CompanyID == _company && x.ClientID == _client).Any();
                if (result) result = profiles.Where(x => !string.IsNullOrEmpty(x.PayrollProfileID)).Any();
            }
            return result;
        }
        private string ImportModel()
        {
            ClientTimePunchesSetup setup = dbContext.ClientTimePunchesSetups.FirstOrDefault(s => s.CompanyID == _company && s.ClientID == _client);

            if (setup != null)
            {
                if (setup.EntryType == ConsolidateStatus.DayTime)
                {
                    return string.Empty;
                }
            }

            ClientTimeImportSetup model = dbContext.ClientTimeImportSetups.FirstOrDefault(s => s.CompanyID == _company && s.ClientID == _client);

            return (model != null) ? model.ModelID : string.Empty;
        }



        private string GetControllerAction(short type)
        {
            switch (type)
            {
                case TimeSheetTypes.EETimeEntry:
                    return "TimeEntry";
                    break;
                case TimeSheetTypes.JobCostByEE:
                    return "JobCostByEmployee";
                    break;
                case TimeSheetTypes.JobCostByJob:
                    return "JobCostByJob";
                    break;
                case TimeSheetTypes.CertifiedByEE:
                    return "CertifiedByEmployee";
                    break;
                case TimeSheetTypes.CertifiedByJob:
                    return "CertifiedByJob";
                    break;
                default:
                    return "StandardTimeSheet";
                    break;
            }
        }



        private void CopyTimeSheet(int id, string user, string name)
        {

            dbContext.Database.ExecuteSqlCommand(
                "sp_TS_Replica @companyid, @id, @user, @name",
                new SqlParameter("companyid", _company),
                new SqlParameter("id", id),
                new SqlParameter("user", user),
                new SqlParameter("name", name)
            );

            dbContext.SaveChanges();
        }

        private void DeleteTimeSheetFiles(int id)
        {
            string tsPath = Path.Combine(GlobalVariables.ClientFolder, TimeSheetParam.TimeSheetFolder);
            if (Directory.Exists(tsPath))
            {
                string prefix = tsPath + "\\TimeSheet_" + id.ToString();
                try
                {
                    IEnumerable<string> files = System.IO.Directory.GetFiles(tsPath).Where(x => x.StartsWith(prefix));
                    foreach (string file in files)
                    {
                        if (System.IO.File.Exists(file)) System.IO.File.Delete(file);
                    }
                }
                catch { }
            }
        }

        private void DeleteTimeSheet(int id)
        {
            short status;
            bool isErigo = IsErigoTimeSheet(id, out status);

            dbContext.Database.ExecuteSqlCommand(
                "sp_TS_Delete @companyid, @id, @isErigo",
                new SqlParameter("companyid", _company),
                new SqlParameter("id", id),
                new SqlParameter("isErigo", isErigo)
            );

            dbContext.SaveChanges();
        }



        private List<Code_Description> GetShiftCodes()
        {
            try
            {

                List<Code_Description> result = this.dbContext.ShiftCodes.Where(c => c.CompanyID == _company).OrderBy(c => c.ShiftCode1).Select(c => new Code_Description() { Code = c.ShiftCode1, Description = c.Description }).ToList();  // add  && c.Inactive != true
                result.Insert(0, new Code_Description { Code = "", Description = "" });
                return result;
            }
            catch { return new List<Code_Description>(); }
        }



        private short GetTimeSheetStatus(int id)
        {
            try
            {
                return this.dbContext.TimeSheets.Where(t => t.CompanyID == _company && t.TimeSheetID == id).First().Status ?? TimeSheetStatus.New;
            }
            catch { return TimeSheetStatus.New; }
        }



        private string TimeSheetKeyValue(int id, out short type)
        {
            string result = string.Empty;
            type = 0;
            DarwiNet2._0.Data.TimeSheet tsrec = this.dbContext.TimeSheets.FirstOrDefault(ts => ts.TimeSheetID == id && ts.CompanyID == _company);
            if (tsrec != null)
            {
                type = tsrec.TimeSheetType ?? 0;
                if (type == TimeSheetTypes.Regular || type == TimeSheetTypes.EETimeEntry) return result;
                switch (type)
                {
                    case TimeSheetTypes.JobCostByEE:
                    case TimeSheetTypes.CertifiedByEE:
                        result = "*";
                        break;
                    case TimeSheetTypes.JobCostByJob:
                    case TimeSheetTypes.CertifiedByJob:
                        result = "**";
                        break;
                    default:
                        result = string.Empty;
                        break;
                }
            }
            return result;
        }



        private void SetTimeSheetStatus(int id, short status)
        {
            DarwiNet2._0.Data.TimeSheet tsrec = this.dbContext.TimeSheets.FirstOrDefault(ts => ts.TimeSheetID == id && ts.CompanyID == _company);
            if (tsrec != null)
            {
                tsrec.Status = (byte?)status;
                this.dbContext.SaveChanges();
                if (status != TimeSheetStatus.Deleted && status != TimeSheetStatus.Transferred)
                {
                    if (status == TimeSheetStatus.Released)
                        CreateTimeSheetChangeRequests(id);
                    else
                        RemoveTimeSheetChangeRequests(id, AssignmentType(tsrec));
                }
            }

        }



        private bool IsErrorsExist(int id)
        {
            return this.dbContext.TimeImportLogs.Where(l => l.CompanyID == _company && l.TimeSheetID == id && l.Line >= 0).Count() > 0;
        }



        private byte AssignmentType(DarwiNet2._0.Data.TimeSheet ts)
        {
            switch (ts.TimeSheetType)
            {
                case TimeSheetTypes.Regular:
                    return ChangeRequestAssignmentTypes.TimeSheets;
                    break;
                case TimeSheetTypes.CertifiedByEE:
                case TimeSheetTypes.CertifiedByJob:
                    return ChangeRequestAssignmentTypes.CertifiedTimeSheet;
                    break;
                default:
                    return ChangeRequestAssignmentTypes.JobCosting;
                    break;
            }
        }



        public void CreateTimeSheetChangeRequests(int id)
        {

        }



        public void RemoveTimeSheetChangeRequests(int id, byte type)
        {

            dbContext.Database.ExecuteSqlCommand(
                "sp_TS_RemoveChangesRequests @companyid, @clientid, @type, @timesheetid",
                new SqlParameter("companyid", _company),
                new SqlParameter("clientid", _client),
                new SqlParameter("type", type),
                new SqlParameter("timesheetid", id)
            );

            dbContext.SaveChanges();
        }



        private List<Code_Description> TimeSheetClients()
        {
            try
            {
                List<string> clients = this.dbContext.TimeSheets.Where(t => t.CompanyID == _company && t.Status != TimeSheetStatus.Deleted && t.Status != TimeSheetStatus.New && t.Status != TimeSheetStatus.Empty).OrderBy(t => t.ClientID).Select(t => t.ClientID).Distinct().ToList();
                return this.dbContext.Clients.Where(c => c.CompanyID == _company && clients.Contains(c.ClientID)).OrderBy(c => c.ClientID).Select(c => new Code_Description { Code = c.ClientID, Description = c.ClientID + " (" + c.ClientName + ")" }).ToList();
            }
            catch { return new List<Code_Description>(); }
        }



        private void SaveRateFromTimeSheet(string emplId, string code, string value, bool rollDown, short type, string job)
        {
            decimal newrate, oldrate;
            if (decimal.TryParse(value, out newrate))
            {
                EmployeePaycode rec = this.dbContext.EmployeePaycodes.FirstOrDefault(c => c.CompanyID == _company && c.EmployeeID == emplId && c.PayRecord == code && !c.Inactive);
                if (rec != null)
                {
                    if (type == TimeSheetTypes.Regular)
                    {
                        UpdateRatePermanent(ref rec, newrate);
                        if (rollDown && string.IsNullOrEmpty(rec.BasePayRecord)) RollDownPayRates(rec.EmployeeID, rec.PayRecord, newrate);
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(job))
                        {
                            string[] jobs = job.Split('|');
                            string j1 = jobs[0], j2 = jobs[1], j3 = "", j4 = "", j5 = "", j6 = ""; 
                            if (jobs.Length > 2)
                            {
                                j3 = jobs[2];
                                j4 = jobs[3];
                                j5 = jobs[4];
                                j6 = jobs[5];
                            }

                      
                            EmployeeJobCostAssignment assignment = (type == TimeSheetTypes.CertifiedByEE || type == TimeSheetTypes.CertifiedByJob) ?
                                this.dbContext.EmployeeJobCostAssignments.FirstOrDefault(a => a.CompanyID == _company && a.EmployeeID == emplId && a.JobCostingName == j1 && a.JobLevel2 == j2) :
                               this.dbContext.EmployeeJobCostAssignments.FirstOrDefault(a => a.CompanyID == _company && a.EmployeeID == emplId && a.JobCostingName == j1 && a.JobLevel2 == j2 && a.JobLevel3 == j3 && a.JobLevel4 == j4 && a.JobLevel5 == j5 && a.JobLevel6 == j6);
                            if (assignment != null) UpdateRatePermanent(ref assignment, rec, newrate, type);
                        }
                    }
                }
            }
        }



        private void RollDownPayRates(string emplId, string code, decimal newrate)
        {

            try
            {
                List<EmployeePaycode> codes = this.dbContext.EmployeePaycodes.Where(c => c.CompanyID == _company && c.EmployeeID == emplId && c.BasePayRecord == code && !c.Inactive).ToList();
                foreach (EmployeePaycode rec in codes)
                {
                    decimal rate = newrate;
                    EmployeePaycode coderec = rec;
                    switch (coderec.PayType)
                    {
                        case PayTypes.Overtime:
                            rate = newrate * Convert.ToDecimal(1.5);
                            break;
                        case PayTypes.DoubleTime:
                            rate = newrate * Convert.ToDecimal(2.0);
                            break;
                    }
                    UpdateRatePermanent(ref coderec, rate);
                }
            }
            catch { }
        }

        private decimal GetCertifiedJobRates(string emplId, string job, out decimal fr_rate, out decimal min_rate, out decimal cert_otrate)
        {
            decimal result = decimal.Zero;
            fr_rate = decimal.Zero;
            min_rate = decimal.Zero;
            cert_otrate = decimal.Zero;
            if (!string.IsNullOrEmpty(job))
            {
                string[] tmp = job.Split('|');
                string JobCostingName = tmp[0];
                string JobLevel2 = tmp[1];
                var assignment = this.dbContext.EmployeeJobCostAssignments.FirstOrDefault(a => a.CompanyID == _company && a.EmployeeID == emplId && a.JobCostingName == JobCostingName && a.JobLevel2 == JobLevel2);
                if (assignment != null)
                {
                    result = assignment.PrevailingRate ?? decimal.Zero;
                    fr_rate = assignment.FringeRate ?? decimal.Zero;
                    min_rate = assignment.MinimumRate ?? decimal.Zero;
                    cert_otrate = assignment.CertOTRate ?? decimal.Zero;
                }
            }
            return result;
        }

        private void UpdateRatePermanent(ref EmployeeJobCostAssignment assignment, EmployeePaycode coderec, decimal newrate, short type)
        {
            bool isSalary = false;
            short paytype = coderec.PayType ?? PaycodeType.Hourly;
            bool isOT = paytype == PaycodeType.Overtime;
            switch (type)
            {
                case TimeSheetTypes.JobCostByEE:
                case TimeSheetTypes.JobCostByJob:
                    if (isOT)
                    {
                        var baserec = this.dbContext.EmployeePaycodes.FirstOrDefault(p => p.CompanyID == coderec.CompanyID && p.EmployeeID == coderec.EmployeeID && p.PayRecord == coderec.BasePayRecord && !p.Inactive);
                        if (baserec != null) paytype = baserec.PayType ?? PaycodeType.Hourly;
                    }
                    isSalary = paytype == PaycodeType.Salary;
                    if (isSalary)
                    {
                        if (isOT)
                            assignment.DefaultSalaryOTRate = newrate;
                        else
                            assignment.DefaultSalaryRate = newrate;
                    }
                    else
                    {
                        if (isOT)
                            assignment.OTRate = newrate;
                        else
                            assignment.RegularRate = newrate;
                    }
                    this.dbContext.SaveChanges();
                    break;
                case TimeSheetTypes.CertifiedByEE:
                case TimeSheetTypes.CertifiedByJob:
                    if (isOT)
                        assignment.CertOTRate = newrate;
                    else
                        assignment.PrevailingRate = newrate;
                    this.dbContext.SaveChanges();
                    break;
            }
        }

        private void UpdateRatePermanent(ref EmployeePaycode coderec, decimal newrate)
        {
            decimal oldrate;
            if (coderec != null)
            {
                oldrate = coderec.PayRateAmount ?? 0;
                if (newrate != oldrate)
                {
                    coderec.PayRateAmount = newrate;
                    this.dbContext.SaveChanges();
                    EmployeePayrateChanx chrec = new EmployeePayrateChanx()
                    {
                        CompanyID = _company,
                        EmployeeID = coderec.EmployeeID,
                        PayRecord = coderec.PayRecord,
                        EffectiveDate = DateTime.Now.Date,
                        PayRateAmount = newrate,
                        ChangeReason = string.Empty,
                        PayStepStatus = 0,
                        PayStepTableID = string.Empty,
                        Step = 0,
                        PayRateChangeType = 0,
                        PayRateChangeRate = (newrate - oldrate) / oldrate,
                        UserID = GlobalVariables.DNETOwnerID,
                        Date = DateTime.Today
                    };
                    this.dbContext.EmployeePayrateChanges.Add(chrec);
                    this.dbContext.SaveChanges();
                    EmployeePayrateHistory hrec = new EmployeePayrateHistory()
                    {
                        CompanyID = _company,
                        EmployeeID = coderec.EmployeeID,
                        PayRecord = coderec.PayRecord,
                        EffectiveDate = DateTime.Today,
                        PayRateAmountOld = oldrate,
                        PayRateChangeType = 0,
                        PayRateAmount = newrate,
                        PayRateChangeRate = (newrate - oldrate) / oldrate,
                        ChangeReason = string.Empty,
                        UserID = GlobalVariables.DNETOwnerID,
                        Date = DateTime.Today
                    };
                    this.dbContext.EmployeePayrateHistories.Add(hrec);
                    this.dbContext.SaveChanges();
                }
            }

        }

        private DateTime? GetDate(string value)
        {
            DateTime result;
            if (DateTime.TryParse(value, out result))
                return result;
            else
                return null;
        }
        #endregion

        #region ChangesRequests
        public Dictionary<int, string> ColumnsDictionary(int id)
        {
            Dictionary<int, string> result = new Dictionary<int, string>();
            try
            {
                List<Index_Value> list = this.dbContext.TimeSheetColumns.Where(c => c.TimeSheetID == id).OrderBy(c => c.ColNbr).Select(c => new Index_Value { Index = c.ColNbr, Value = c.Name }).ToList<Index_Value>();
                foreach (Index_Value cell in list)
                {
                    result.Add(cell.Index, cell.Value);
                }
            }
            catch { }
            return result;
        }



        public List<int> KeyColumns(int id)
        {
            try
            {
                return this.dbContext.TimeSheetColumns.Where(c => c.TimeSheetID == id).OrderBy(c => c.ColNbr).Select(c => c.ColNbr).ToList<int>();
            }
            catch
            {
                return null;
            }
        }



        public string RowKey(int id, int codeColumn, List<int> keyColumns, Dictionary<int, string> rowValues)
        {
            string result = id.ToString();
            string temp;
            result += (rowValues.TryGetValue(codeColumn, out temp)) ? "," + temp : ",";
            foreach (int col in keyColumns)
            {
                result += (rowValues.TryGetValue(col, out temp)) ? "," + temp : ",";
            }
            return result;
        }



        public void RowDictionaries(int id, int row, Dictionary<int, string> columns, out Dictionary<string, string> dictNameValue, out Dictionary<int, string> dictValues)
        {
            dictValues = new Dictionary<int, string>();
            dictNameValue = new Dictionary<string, string>();
            string temp;
            if (columns.Count() > 0)
            {
                try
                {
                    List<Index_Value> list = this.dbContext.TimeSheetDetails.Where(d => d.TimeSheetID == id && d.RowID == row).OrderBy(d => d.ColID).Select(d => new Index_Value { Index = d.ColID, Value = d.CellValue }).ToList<Index_Value>();
                    foreach (Index_Value cell in list)
                    {
                        dictValues.Add(cell.Index, cell.Value);
                        if (columns.TryGetValue(cell.Index, out temp)) dictNameValue.Add(temp, cell.Value);
                    }
                }
                catch { }
            }
        }



        private void AddChangeDetailValue(int requestId, ref int seqNbr, string name, string value)
        {
            if (!string.IsNullOrEmpty(value) && value != TimeSheetParam.GreyCellString)
            {
                if (value.ToUpper() == "TRUE") value = "1";
                if (value.ToUpper() == "FALSE") value = "0";
                ChangeRequestDetail rec = new ChangeRequestDetail
                {
                    CompanyID = _company,
                    RequestID = requestId,
                    SeqNbr = seqNbr,
                    FieldName = name,
                    OldValue = string.Empty,
                    NewValue = value
                };
                this.dbContext.ChangeRequestDetails.Add(rec);
                this.dbContext.SaveChanges();
                seqNbr++;
            }
        }



        private int CreateChangeHeader()
        {
            int request = 0;
            return request;
        }
        #endregion

        #region SwipeClock
        private bool CanUseSwipeClockImport(short type)
        {
            if (type != TimeSheetTypes.Regular) return false;
            Company company = this.dbContext.Companies.FirstOrDefault(c => c.CompanyID == _company);
            if (company != null)
            {
                if (!string.IsNullOrEmpty(company.SwipeClockLogin) && !string.IsNullOrEmpty(company.SwipeClockPwd))
                {
                    ClientTimePunchesSetup setup = this.dbContext.ClientTimePunchesSetups.FirstOrDefault(c => c.CompanyID == _company && c.ClientID == _client && c.UseSwipeClock && !string.IsNullOrEmpty(c.SwipeClockSiteID) && c.Enabled == true);
                    if (setup != null)
                    {
                        ClientTimeImportSetup import = dbContext.ClientTimeImportSetups.FirstOrDefault(s => s.CompanyID == _company && s.ClientID == _client && !string.IsNullOrEmpty(s.ModelID));
                        if (import != null)
                        {
                            TimeImportModel model = this.dbContext.TimeImportModels.FirstOrDefault(m => m.ModelID == import.ModelID && m.UseSwipeClock && !string.IsNullOrEmpty(m.SwipeClockFormat));
                            if (model != null) return true;
                        }
                    }
                }
            }
            return false;
        }



        private string GetEESwipLogin()
        {
            string result = string.Empty;
            EmployeeTimePunchesSetup setup = this.dbContext.EmployeeTimePunchesSetups.FirstOrDefault(s => s.CompanyID == _company && s.EmployeeID == _employee && !string.IsNullOrEmpty(s.SwipeClockLogin));
            if (setup != null) result = setup.SwipeClockLogin;
            return result;
        }



        #endregion

        #region Profile Security
        private string DefaultClientWorkSite()
        {
            Client client = dbContext.Clients.FirstOrDefault(c => c.CompanyID == _company && c.ClientID == _client);
            return (client != null) ? client.AddressCode : string.Empty;
        }



        private string EEWorkSite(string worksite, string defsite)
        {
            return (string.IsNullOrEmpty(worksite)) ? defsite : worksite;
        }



        private bool IsProfileOK(string profileId, List<Employee> availEEs)
        {
            if (GlobalVariables.DNETLevel == DNetAccessLevel.System) return true;
            var profile = this.dbContext.ClientTimeSheetProfiles.FirstOrDefault(p => p.CompanyID == _company && p.ClientID == _client && p.ProfileID == profileId);
            if (profile == null) return false;
            List<string> selValues = new List<string>();
            try
            {
                selValues = this.dbContext.ClientTimeSheetProfileSelectedValues.Where(sv => sv.CompanyID == _company && sv.ClientID == _client && sv.ProfileID == profile.ProfileID).Select(sv => sv.SelectedValue).ToList();
            }
            catch { selValues = new List<string>(); }
            return (IsProfileOK(profile.SelectionType ?? TimeSheetSelectionTypes.None, selValues, availEEs));
        }



        private bool IsProfileOK(short type, List<string> selections, List<Employee> availEEs)
        {
            if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
            {
                return true;
            }

            bool result = true;
            availEEs = availEEs.Where(e => !string.IsNullOrEmpty(e.EmployeeID)).ToList();
            bool useSecurity = true;
            List<string> tempList;
            List<string> securList = new List<string>();

            switch (type)
            {
                case TimeSheetSelectionTypes.Employee:
                    securList = availEEs.Select(e => e.EmployeeID).Distinct().ToList();
                    result = (useSecurity) ? (selections.Intersect(securList).Count() > 0) : true;
                    break;

                case TimeSheetSelectionTypes.EEClass:
                    securList = availEEs.Select(e => e.EmployeeClass).Distinct().ToList();
                    result = (useSecurity) ? (selections.Intersect(securList).Count() > 0) : true;
                    break;

                case TimeSheetSelectionTypes.Department:
                    securList = availEEs.Select(e => e.Department).Distinct().ToList();
                    result = (useSecurity) ? (selections.Intersect(securList).Count() > 0) : true;
                    break;

                case TimeSheetSelectionTypes.Division:
                    tempList = availEEs.Select(e => e.Department).Distinct().ToList();

                    List<ClientDivisionDetail> cdivs = dbContext.ClientDivisionDetails
                        .Where(d =>
                            d.CompanyID == _company &&
                            d.ClientID == _client)
                        .ToList();

                    securList = cdivs.Where(cd => tempList.Any(d => d == cd.Department)).Select(cd => cd.DivisionID).Distinct().ToList();
                    result = (useSecurity) ? (selections.Intersect(securList).Count() > 0) : true;
                    break;

                case TimeSheetSelectionTypes.Client:
                    result = (selections != null && selections.Any()) ? selections.Any(v => v == _client) : true;
                    break;

                case TimeSheetSelectionTypes.WorkSite:
                    string defaddr = DefaultClientWorkSite();
                    securList = availEEs.Select(e => EEWorkSite(e.EmployeeWorkAddress, defaddr)).ToList();
                    result = (useSecurity) ? (selections.Intersect(securList).Count() > 0) : true;
                    break;
                case TimeSheetSelectionTypes.Jobs:
                    //List<EmployeeJobCostAssignment> ee_jobs = this.dbContext.EmployeeJobCostAssignments.Where(j => j.CompanyID == _company && j.ClientID == _client && !j.Inactive).ToList();
                    //result = ee_jobs.Where(j => selections.Any(s => s == j.JobCostingName)).Count() > 0;
                    break;
            }

            return result;
        }


        private List<Code_Description> UserAvailableProfiles()
        {
            List<Code_Description> result = new List<Code_Description>();
            var securityEEs = GlobalVariables.SecurityEmployees;

            List<Employee> securEEs = dbContext.Employees
                .Where(e =>
                    e.CompanyID == _company &&
                    e.ClientID == _client)
                .ToList();

            securEEs = securEEs.Where(e => securityEEs.Any(ee => ee == e.EmployeeID)).ToList();

            List<ClientTimeSheetProfile> profiles = dbContext.ClientTimeSheetProfiles
                .Where(ctsp =>
                    ctsp.CompanyID == _company &&
                    ctsp.ClientID == _client)
                .ToList();

            foreach (ClientTimeSheetProfile profile in profiles)
            {
                Code_Description rec = new Code_Description { Code = profile.ProfileID, Description = profile.ProfileName };

                if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
                {
                    List<string> selValues = new List<string>();

                    try
                    {
                        selValues = dbContext.ClientTimeSheetProfileSelectedValues
                           .Where(sv =>
                               sv.CompanyID == _company &&
                               sv.ClientID == _client &&
                               sv.ProfileID == profile.ProfileID)
                           .Select(sv => sv.SelectedValue)
                           .ToList();
                    }
                    catch (Exception ex)
                    {
                        selValues = new List<string>();
                    }

                    if (IsProfileOK(profile.SelectionType ?? TimeSheetSelectionTypes.None, selValues, securEEs))
                    {
                        result.Add(rec);
                    }
                }
            }
            return result;
        }

        private int TimeEntryNotesCount(int tid, string eid)
        {
            return dbContext.TimeSheetNotes.Where(x => x.TimeSheetID == tid && x.EmployeeID == eid).Count();
        }
        #endregion

        #region Payroll Time Sheets (for D2)
        private List<TimeSheetList> GetPayrollTimeSheetsList(string profile, string pnumber)
        {
            List<TimeSheetList> listTS = new List<TimeSheetList>();
            if (!string.IsNullOrEmpty(profile))
            {
                try
                {
                    if (!string.IsNullOrEmpty(pnumber))
                    {
                        listTS = this.dbContext.TimeSheets
                            .Where(ts => ts.CompanyID == _company && ts.ClientID == _client && ts.PayrollNumber == pnumber && ts.PayrollProfileID == profile && ts.Status != TimeSheetStatus.Historical && ts.Status != TimeSheetStatus.Deleted && ts.Status != TimeSheetStatus.New)
                            .OrderByDescending(ts => ts.TimeSheetID)
                            .Select(x => new TimeSheetList
                            {
                                TimeSheetID = x.TimeSheetID,
                                ProfileID = x.ProfileID,
                                TimeSheetName = x.TimeSheetName,
                                TimeSheetType = FieldTranslation.GetEnumDescription(typeof(enTimeSheetTypes), (int)x.TimeSheetType),
                                DateFrom = (x.DateFrom != null) ? new DateTime(x.DateFrom.Value.Year, x.DateFrom.Value.Month, x.DateFrom.Value.Day, 12, 0, 0, DateTimeKind.Utc) : (DateTime)x.DateFrom,  // 08/17/2017 DS TFS # 2718
                                DateTo = (x.DateTo != null) ? new DateTime(x.DateTo.Value.Year, x.DateTo.Value.Month, x.DateTo.Value.Day, 12, 0, 0, DateTimeKind.Utc) : (DateTime)x.DateTo, // 08/17/2017 DS TFS # 2718
                                Status = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), (int)x.Status),
                                Creator = x.Creator,
                                TRXNumber = GetTRXNumber(x.CompanyID, x.ClientID, x.TimeSheetID),
                                EENumber = x.TRXEmployees ?? 0, //GetEENumber(x.CompanyID, x.ClientID, x.TimeSheetID)//, //
                                PayPeriod = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), (int)(x.PayPeriod ?? 0))
                            }).ToList();
                    }
                    else
                    {
                        listTS = this.dbContext.TimeSheets.Where(ts => ts.CompanyID == _company && ts.ClientID == _client && string.IsNullOrEmpty(ts.PayrollNumber) && ts.PayrollProfileID == profile && ts.Status != TimeSheetStatus.Released)
                            .OrderByDescending(ts => ts.TimeSheetID)
                            .Select(x => new TimeSheetList
                            {
                                TimeSheetID = x.TimeSheetID,
                                ProfileID = x.ProfileID,
                                TimeSheetName = x.TimeSheetName,
                                TimeSheetType = FieldTranslation.GetEnumDescription(typeof(enTimeSheetTypes), (int)x.TimeSheetType),
                                DateFrom = (x.DateFrom != null) ? new DateTime(x.DateFrom.Value.Year, x.DateFrom.Value.Month, x.DateFrom.Value.Day, 12, 0, 0, DateTimeKind.Utc) : (DateTime)x.DateFrom,  // 08/17/2017 DS TFS # 2718
                                DateTo = (x.DateTo != null) ? new DateTime(x.DateTo.Value.Year, x.DateTo.Value.Month, x.DateTo.Value.Day, 12, 0, 0, DateTimeKind.Utc) : (DateTime)x.DateTo, // 08/17/2017 DS TFS # 2718
                                Status = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), (int)x.Status),
                                Creator = x.Creator,
                                TRXNumber = 0,
                                EENumber = x.TRXEmployees ?? 0,
                                PayPeriod = FieldTranslation.GetEnumDescription(typeof(enTimeSheetStatus), (int)(x.PayPeriod ?? 0))
                            }).ToList();
                    }
                }
                catch { listTS = new List<TimeSheetList>(); }
            }
            return listTS;
        }

        private int GetTRXNumber(int comp, string client, int id)
        {
            int result = 0;
            if (GlobalVariables.UseD2)
            {
                result = this.dbContext.PayrollBatches.Where(x => x.CompanyID == comp && x.ClientID == client && x.BatchNumber.StartsWith(client + "-" + id.ToString())).Select(x => x.ControlTRXCount).Sum() ?? 0;
            }
            return result;
        }

        private int GetEENumber(int comp, string client, int id)
        {
            int result = 0;
            if (GlobalVariables.UseD2)
            {
                result = this.dbContext.PayrollBatches.Where(x => x.CompanyID == comp && x.ClientID == client && x.BatchNumber.StartsWith(client + "-" + id.ToString())).Select(x => x.ControlEmployeeCount).Sum() ?? 0;
            }
            return result;
        }

        private string GetPayrollProfile(string payroll)
        {
            string result = string.Empty;
            var schedule = this.dbContext.ClientPayrollSchedules.FirstOrDefault(x => x.CompanyID == _company && x.ClientID == _client && x.PayrollNumber == payroll);
            if (schedule != null) result = schedule.ProfileID;
            return result;
        }
        #endregion
    }
}
