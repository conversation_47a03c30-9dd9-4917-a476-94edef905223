using System;
using System.Threading.Tasks;
using System.Web.Mvc;
using DarwiNet2._0.DNetSynch;
using EmployerFlexible.Dnet.DTO.Employee;
using EmployerFlexible.Dnet.Responses;
using Newtonsoft.Json;

namespace DarwiNet2._0.Controllers.OpenEnrollment
{
    public partial class OpenEnrollmentController
    {
        /// <summary>
        /// Get the beneficiaries selection for a given year and plan name
        /// </summary>
        /// <param name="year">The open enrollment year</param>
        /// <param name="planName">The name of the plan to select beneficiaries for</param>
        /// <returns>The beneficiaries option</returns>
        [HttpGet]
        public async Task<ActionResult> GetBeneficiaryOptions(int year, string planName)
        {
            var response = new ServiceResponse<object>();
            try
            {
                var beneficiaries = await _mobileClient.GetBeneficiaryOptionsAsync(
                    employeeId: GlobalVariables.EmployeeID,
                    companyId: GlobalVariables.CompanyID,
                    planName: planName,
                    year: year,
                    userId: GlobalVariables.CurrentUser.UserID
                );

                response.StatusCode = System.Net.HttpStatusCode.OK;
                response.Value = beneficiaries.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to get beneficiary options");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Get the current selected beneficiary for the benefit plan
        /// </summary>
        /// <param name="year">The year of the open enrollment plan</param>
        /// <param name="planName">The benefit plan name</param>
        /// <returns>The list of currently selected beneficiaries</returns>
        [HttpGet]
        public async Task<ActionResult> GetPlanBeneficiaries(int year, string planName)
        {
            var response = new ServiceResponse<object>();
            try
            {
                var beneficiaries = await _mobileClient.GetPlanBeneficariesAsync(
                    employeeId: GlobalVariables.EmployeeID,
                    companyId: GlobalVariables.CompanyID,
                    planName: planName,
                    year: year,
                    userId: GlobalVariables.CurrentUser.UserID
                );

                response.StatusCode = System.Net.HttpStatusCode.OK;
                response.Value = beneficiaries.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to get beneficiaries for the selected plan");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Add a beneficiary to the selected plan
        /// </summary>
        /// <param name="year">The year of the open enrollment</param>
        /// <param name="planName">The plan to add beneficiary to</param>
        /// <param name="ssn">The SSN of the beneficiary</param>
        [HttpPost]
        public async Task<ActionResult> AddBeneficiaryToPlan(int year, string planName, string ssn)
        {
            var response = new ServiceResponse<object>();
            try
            {
                var beneficiaries = await _mobileClient.AddBeneficiaryAsync(
                    employeeId: GlobalVariables.EmployeeID,
                    companyId: GlobalVariables.CompanyID,
                    year: (short)year,
                    planName: planName,
                    sociailSecurityNumber: ssn,
                    userId: GlobalVariables.CurrentUser.UserID
                );

                response.StatusCode = System.Net.HttpStatusCode.OK;
                response.Value = beneficiaries.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to get beneficiaries for the selected plan");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Remove a beneficiary to the selected plan
        /// </summary>
        /// <param name="year">The year of the open enrollment</param>
        /// <param name="planName">The plan to add beneficiary to</param>
        /// <param name="ssn">The SSN of the beneficiary</param>
        [HttpDelete]
        public async Task<ActionResult> RemoveBeneficiaryFromPlan(int year, string planName, string ssn)
        {
            var response = new ServiceResponse();
            try
            {
                response = await _mobileClient.RemoveBeneficiaryAsync(
                   employeeId: GlobalVariables.EmployeeID,
                   companyId: GlobalVariables.CompanyID,
                   year: (short)year,
                   planName: planName,
                   sociailSecurityNumber: ssn,
                   userId: GlobalVariables.CurrentUser.UserID
               );

                if (!response.IsSuccessful)
                {
                    _logger.LogError(new Exception(JsonConvert.SerializeObject(response)));
                    response.Errors.Clear();
                    response.Errors.Add("Failed to remove beneficiary from plan");
                }

                response.StatusCode = System.Net.HttpStatusCode.OK;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to get beneficiaries for the selected plan");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }
        
        /// <summary>
        /// Update the beneficiary percentage for a specific plan
        /// </summary>
        /// <param name="year">The year of the open enrollment</param>
        /// <param name="planName">The plan name</param>
        /// <param name="ssn">The social security number of the beneficiary</param>
        /// <param name="value">The new percentage value</param>
        /// <returns>The updated beneficiary information</returns>
        [HttpPut]
        public async Task<ActionResult> UpdateBeneficiaryPercentage(int year, string planName, string ssn, short value)
        {
            var response = new ServiceResponse<object>();
            try
            {
                var result = await _mobileClient.UpdateBeneficiaryPercentageAsync(
                    employeeId: GlobalVariables.EmployeeID,
                    companyId: GlobalVariables.CompanyID,
                    year: (short)year,
                    planName: planName,
                    sociailSecurityNumber: ssn,
                    value: value,
                    userId: GlobalVariables.CurrentUser.UserID
                );

                if (!result.IsSuccessful)
                {
                    response.Errors.Clear();
                    response.Errors.Add("Unhandled exception. Failed to update beneficiary percentage");
                }

                response.StatusCode = System.Net.HttpStatusCode.OK;
                response.Value = result.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to update beneficiary percentage");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        ///  Update the beneficiary type (primary, secondary) for a specific plan
        /// </summary>
        /// <param name="year">The year of the open enrollment</param>
        /// <param name="planName">The plan updating beneficiary for</param>
        /// <param name="ssn">The ssn of the beneficiary</param>
        /// <param name="value">1 is primary, 2 is secondary</param>
        [HttpPut]
        public async Task<ActionResult> UpdateBeneficiaryType(int year, string planName, string ssn, short value)
        {
            var response = new ServiceResponse<object>();
            try
            {
                var result = await _mobileClient.UpdateBeneficiaryTypeAsync(
                    employeeId: GlobalVariables.EmployeeID,
                    companyId: GlobalVariables.CompanyID,
                    year: (short)year,
                    planName: planName,
                    sociailSecurityNumber: ssn,
                    value: value,
                    userId: GlobalVariables.CurrentUser.UserID
                );

                if (!result.IsSuccessful)
                {
                    response.Errors.Clear();
                    response.Errors.Add("Unhandled exception. Failed to update beneficiary type");
                }

                response.StatusCode = System.Net.HttpStatusCode.OK;
                response.Value = result.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to update beneficiary type");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }
        
        /// <summary>
        ///  Create a new beneficiary for a particular plan 
        /// </summary>
        /// <param name="request">The request to make new beneficiary</param>
        /// <param name="year">The year of the open enrollment</param>
        /// <param name="planName">The plan updating beneficiary for</param>
        [HttpPost]
        public async Task<ActionResult> CreateBeneficiary(EmployeeBeneficiaryDTO request, int year, string planName)
        {
            var response = new ServiceResponse<object>();
            try
            {
                var result = await _mobileClient.CreateBeneficiaryAsync
                (
                    request: request,
                    employeeId: GlobalVariables.EmployeeID,
                    companyId: GlobalVariables.CompanyID,
                    clientId: GlobalVariables.Client,
                    year: (short)year,
                    planName: planName,
                    userId: GlobalVariables.CurrentUser.UserID
                );

                if (!result.IsSuccessful)
                {
                    response.Errors.Clear();
                    response.Errors.Add("Unhandled exception. Failed to create new beneficiary");
                }

                response.StatusCode = System.Net.HttpStatusCode.OK;
                response.Value = result.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to create new beneficiary");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// Finalize the open enrollment for the employee
        /// </summary>
        /// <param name="year">The year of the open enrollment</param>
        /// <param name="planName">The plan name</param>
        /// <returns>Success message or error response</returns>
        [HttpPost]
        public async Task<ActionResult> PlanBeneficiariesDone(int year, string planName)
        {
            var response = new ServiceResponse<object>();
            try
            {
                var result = await _mobileClient.PlanBeneficiariesDoneAsync(
                    employeeId: GlobalVariables.EmployeeID,
                    companyId: GlobalVariables.CompanyID,
                    clientId: GlobalVariables.Client,
                    year: (short)year,
                    planName: planName,
                    userId: GlobalVariables.CurrentUser.UserID
                );

                response.StatusCode = System.Net.HttpStatusCode.OK;
                response.Value = result.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                response.Errors.Clear();
                response.Errors.Add("Unhandled exception. Failed to mark plan beneficiaries as done");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }
    }
}