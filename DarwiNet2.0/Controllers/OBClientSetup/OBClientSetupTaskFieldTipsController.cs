using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Data;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers
{
    [IsSessionActive]
    public class OBClientSetupTaskFieldTipsController : Controller
    {
        private DnetEntities dbContext;



        /// <summary>
        /// 
        /// </summary>
        /// <param name="disposing"></param>
        protected override void Dispose(bool disposing)
        {
            dbContext.Dispose();
            base.Dispose(disposing);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestContext"></param>
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            dbContext = new DnetEntities();
            base.Initialize(requestContext);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            return View();
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tid"></param>
        /// <param name="fname"></param>
        /// <returns></returns>
        public ActionResult Edit(int id, int tid, string fname)
        {
            IEnumerable<OBClientSetupTaskField> fields = null;

            fields = dbContext.OBClientSetupTaskFields.Where(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid && x.FName == fname);

            return View(fields);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tid"></param>
        /// <param name="fldToUpdate"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Edit(int id, int tid, OBProfileTaskField fldToUpdate)
        {
            OBClientSetupTaskField originalField = dbContext.OBClientSetupTaskFields
                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid && x.FName == fldToUpdate.FName);

            originalField.FTip = fldToUpdate.FTip;

            dbContext.SaveChanges();

            TempData["Success"] = "The Task Field Tip has been updated.";

            return RedirectToAction("Edit");
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tid"></param>
        /// <param name="fname"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Clear(int id, int tid, string fname)
        {
            OBClientSetupTaskField originalField = dbContext.OBClientSetupTaskFields
                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid && x.FName == fname);

            originalField.FTip = string.Empty;

            dbContext.SaveChanges();

            return RedirectToAction("Edit");
        }
    }
}