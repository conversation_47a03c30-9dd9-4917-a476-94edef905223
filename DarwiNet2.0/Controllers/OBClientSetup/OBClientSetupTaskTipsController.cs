using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers
{
    [IsSessionActive]
    public class OBClientSetupTaskTipsController : Controller
    {
        private DnetEntities dbContext;



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            return View();
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="disposing"></param>
        protected override void Dispose(bool disposing)
        {
            dbContext.Dispose();
            base.Dispose(disposing);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestContext"></param>
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            if (GlobalVariables.Customer == null)
            {
                RedirectToAction("SessionExpired", "Home");
            }

            dbContext = new DnetEntities();
            base.Initialize(requestContext);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tid"></param>
        /// <param name="assignedTo"></param>
        /// <returns></returns>
        public ActionResult Edit(int id, int tid, byte assignedTo)
        {
            var taskTip = dbContext.OBClientSetupTasks.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid);
            int profileId = dbContext.OBClientSetups.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id).ProfileID;

            if (profileId != 0)
            {
                var deftask = dbContext.OBClientSetupTasks.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid);

                if (deftask != null)
                {
                    if (assignedTo == OBAssignTo.Employee)
                    {
                        if (deftask.EEInstruction != null)
                        {
                            ViewBag.DefaultInstaruction = deftask.EEInstruction.Trim();
                        }
                        else
                        {
                            ViewBag.DefaultInstaruction = string.Empty;
                        }
                    }
                    else
                    {
                        if (deftask.CCInstruction != null)
                        {
                            ViewBag.DefaultInstaruction = deftask.CCInstruction.Trim();
                        }
                        else
                        {
                            ViewBag.DefaultInstaruction = string.Empty;
                        }
                    }
                }
            }
            else
            {
                ViewBag.DefaultInstaruction = string.Empty;
            }

            return View(taskTip);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="taskId"></param>
        /// <param name="assignedTo"></param>
        /// <param name="instruction"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Edit(int id, int taskId, byte assignedTo, string instruction)
        {
            OBClientSetupTask originalTask = dbContext.OBClientSetupTasks
                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == taskId);

            if (assignedTo == OBAssignTo.Employee)
            {
                originalTask.EEInstruction = instruction;
            }
            else
            {
                originalTask.CCInstruction = instruction;
            }

            dbContext.SaveChanges();

            return RedirectToAction("Edit");
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="tid"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Clear(int id, int tid)
        {
            OBClientSetupTask originalTask = dbContext.OBClientSetupTasks
                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid);

            originalTask.TipText = string.Empty;
            originalTask.TipImage = string.Empty;

            dbContext.SaveChanges();

            return RedirectToAction("Edit");
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="OBClientSetupToUpdate"></param>
        /// <returns></returns>
        public ActionResult EditInst(FormCollection OBClientSetupToUpdate)
        {
            int id = Int32.Parse(OBClientSetupToUpdate["id"]);
            int tid = Int32.Parse(OBClientSetupToUpdate["tid"]);

            OBClientSetupTask originalTask = dbContext.OBClientSetupTasks
                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid);

            if (OBClientSetupToUpdate["item.EEInstruction"] + "" != "")
            {
                originalTask.EEInstruction = HttpUtility.HtmlDecode(OBClientSetupToUpdate["item.EEInstruction"]) ?? string.Empty;
            }

            if (OBClientSetupToUpdate["item.CCInstruction"] + "" != "")
            {
                originalTask.CCInstruction = HttpUtility.HtmlDecode(OBClientSetupToUpdate["item.CCInstruction"]) ?? string.Empty;
            }

            dbContext.SaveChanges();

            TempData["Success"] = "Onboarding Client Profile Instructions have been updated.";

            return RedirectToAction("Index", "OBClientSetupTasks", new { id = id });
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="OBClientSetupToUpdate"></param>
        /// <param name="file"></param>
        /// <param name="file2"></param>
        /// <returns></returns>
        public ActionResult EditTips(FormCollection OBClientSetupToUpdate, HttpPostedFileBase file,HttpPostedFileBase file2)
        {
            int id = Int32.Parse(OBClientSetupToUpdate["id"]);
            int tid = Int32.Parse(OBClientSetupToUpdate["tid"]);

            OBClientSetupTask originalTask = dbContext.OBClientSetupTasks
                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == id && x.TaskID == tid);

            if (OBClientSetupToUpdate["item.TipText"] + "" != "")
            {
                originalTask.TipText = HttpUtility.HtmlDecode(OBClientSetupToUpdate["item.TipText"]) ?? string.Empty;
            }

            string fileLocation = Path.Combine(GlobalVariables.ClientFolder, Folders.OBTaskTips);
            string DNetUrl = GlobalVariables.DNetURL;
            string CompanyFolder = "/Assets/" + GlobalVariables.Customer + "." + GlobalVariables.Company;
            string ClientFolder = GlobalVariables.Client;
            string DNetFolder = Folders.OBTaskTips;
            bool exists = Directory.Exists(fileLocation);

            foreach (string files in Request.Files)
            {
                var cont = false;

                if (files == "file")
                {
                    if (file != null && file.ContentLength > 0)
                    {
                        cont = true;
                    }
                }

                if (files == "file2")
                {
                    if (file2 != null && file2.ContentLength > 0)
                    {
                        cont = true;
                    }
                }

                if (cont)
                {
                    var PostedFile = Request.Files[files];
                    string name = Path.Combine(fileLocation, Path.GetFileName(PostedFile.FileName));

                    if (System.IO.File.Exists(name))
                    {
                        System.IO.File.Delete(name);
                    }

                    if (!exists)
                    {
                        Directory.CreateDirectory(fileLocation);
                    }

                    PostedFile.SaveAs(name);

                    if (files == "file")
                    {
                        originalTask.TipImage = 
                            DNetUrl + "/Assets/" + CompanyFolder + "/" + GlobalVariables.Client + "/" + DNetFolder + "/" + Path.GetFileName(PostedFile.FileName);
                    }

                    if (files == "file2")
                    {
                        originalTask.InstructionDoc = 
                            DNetUrl + "/Assets/" + CompanyFolder + "/" + GlobalVariables.Client + "/" + DNetFolder + "/" + Path.GetFileName(PostedFile.FileName);
                    }
                }
            }

            dbContext.SaveChanges();

            TempData["Success"] = "Onboarding Client Profile Tips have been updated.";

            return RedirectToAction("Index", "OBClientSetupTasks", new { id = id });
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="task"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Upload(OBClientSetupTask task)
        {
            int id = task.SetupID;
            int tid = task.TaskID;
            string path = Path.Combine(GlobalVariables.ClientFolder, Folders.OBTaskTips);

            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            foreach (string file in Request.Files)
            {

                var PostedFile = Request.Files[file];
                string name = Path.Combine(path, Path.GetFileName(PostedFile.FileName));
                PostedFile.SaveAs(name);
                task.TipImage = name.Replace(GlobalVariables.CompanyFolder, "");
            }

            dbContext.SaveChanges();

            return RedirectToAction("Edit", new { id = id, tid = tid });
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        private List<string> GetExistingTipImages()
        {
            List<string> tips = new List<string>();
            string path = Path.Combine(GlobalVariables.CompanyFolder, Folders.OBTaskTips);
            string cpath = Path.Combine(GlobalVariables.ClientFolder, Folders.OBTaskTips);

            try
            {
                if (Directory.Exists(path))
                {
                    tips = Directory.GetFiles(path).Select(f => f.Replace(GlobalVariables.CompanyFolder, "")).ToList<string>();
                }
                else
                {
                    Directory.CreateDirectory(path);
                }

                if (Directory.Exists(cpath))
                {
                    tips = tips.Concat(Directory.GetFiles(cpath).Select(f => f.Replace(GlobalVariables.CompanyFolder, ""))).ToList<string>();
                }
                else
                {
                    Directory.CreateDirectory(cpath);
                }

            }
            catch
            {

            }

            return tips;
        }
    }
}