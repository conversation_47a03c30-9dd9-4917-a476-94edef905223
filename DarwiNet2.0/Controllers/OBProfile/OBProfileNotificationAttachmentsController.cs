using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Data;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers
{
    [IsSessionActive]
    public class OBProfileNotificationAttachmentsController : Controller
    {
        private DnetEntities dbContext;



        /// <summary>
        /// 
        /// </summary>
        /// <param name="disposing"></param>
        protected override void Dispose(bool disposing)
        {
            dbContext.Dispose();
            base.Dispose(disposing);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestContext"></param>
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            dbContext = new DnetEntities();
            base.Initialize(requestContext);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="profileId"></param>
        /// <param name="notificationType"></param>
        /// <param name="assignedTo"></param>
        /// <returns></returns>
        public ActionResult Index(int profileId, byte notificationType, byte assignedTo)
        {
            List<OBProfileNotificationAttachment> attachments = dbContext.OBProfileNotificationAttachments
                .Where(x => x.CompanyID == GlobalVariables.CompanyID && x.ProfileID == profileId && x.Type == notificationType && x.Assigned == assignedTo)
                .OrderBy(x => x.DocumentID).ToList();

            return View(attachments);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="profileId"></param>
        /// <param name="notificationType"></param>
        /// <param name="assignedTo"></param>
        /// <returns></returns>
        List<OBProfileNotificationAttachment> GetAttachments(int profileId, byte notificationType, byte assignedTo)
        {
            var model = new DnetEntities();

            List<OBProfileNotificationAttachment> attachments = model.OBProfileNotificationAttachments
                .Where(x => x.CompanyID == GlobalVariables.CompanyID && x.ProfileID == profileId && x.Type == notificationType && x.Assigned == assignedTo)
                .OrderBy(x => x.DocumentID).ToList();

            return attachments;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <param name="profileId"></param>
        /// <param name="notificationType"></param>
        /// <param name="assignedTo"></param>
        /// <returns></returns>
        public ActionResult OBProfileNotificationAttachments_Read([DataSourceRequest] DataSourceRequest request, int profileId, byte notificationType, byte assignedTo)
        {
            return Json(GetAttachments(profileId, notificationType, assignedTo).ToDataSourceResult(request));
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            OBProfileNotificationAttachment attachment = dbContext.OBProfileNotificationAttachments.FirstOrDefault(x => x.DocumentID == id);

            return View(attachment);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult Delete(int id)
        {
            OBProfileNotificationAttachment attachment = dbContext.OBProfileNotificationAttachments.FirstOrDefault(obna => obna.DocumentID == id);

            return View(attachment);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="collection"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Delete(int id, FormCollection collection)
        {
            try
            {
                OBProfileNotificationAttachment docToDelete = dbContext.OBProfileNotificationAttachments
                    .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID &&  x.DocumentID == id);

                dbContext.OBProfileNotificationAttachments.Remove(docToDelete);
                dbContext.SaveChanges();

                return RedirectToAction("Index");
            }
            catch
            {
                return View();
            }
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="profileId"></param>
        /// <param name="notificationType"></param>
        /// <param name="assignedTo"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Upload(int profileId, byte notificationType, byte assignedTo)
        {

            foreach (string file in Request.Files)
            {
                var PostedFile = Request.Files[file];
                string filetype = PostedFile.ContentType;
                int filelength = PostedFile.ContentLength;
                Stream filestream = PostedFile.InputStream;
                byte[] filedata = new byte[filelength];
                string filename = Path.GetFileName(PostedFile.FileName);
                string docname = Path.GetFileNameWithoutExtension(PostedFile.FileName);
                filestream.Read(filedata, 0, filelength);

                var data = new OBProfileNotificationAttachment
                {
                    ProfileID = profileId,
                    Type = notificationType,
                    Assigned = assignedTo,
                    DocumentName = docname,
                    DocumentFile = filename,
                    ContentType = filetype,
                    DocumentBody = filedata
                };

                dbContext.OBProfileNotificationAttachments.Add(data);
                dbContext.SaveChanges();
            }

            return RedirectToAction("Index");
        }
    }
}