using System;
using System.Data.Entity;
using System.Threading.Tasks;
using System.Web.Mvc;
using DarwiNet2._0.Core.Options;
using DarwiNet2._0.Data;
using DarwiNet2._0.Logging;
using DarwiNet2._0.Services.Messaging;
using EmployerFlexible.Dnet.DTO;
using EmployerFlexible.Dnet.Responses;
using Newtonsoft.Json;
using Thinkware;

namespace DarwiNet2._0.Controllers
{
    public class TwoFactorAuthenticationApiController : Controller
    {
        private readonly ILogger _logger;
        private readonly DnetEntities _dbContext;
        private readonly TwilioMessagingService _twilioMessagingService;

        public TwoFactorAuthenticationApiController(
            DnetEntities dbContext,
            TwilioMessagingService twilioMessagingService,
            ILogger logger
        )
        {
            _dbContext = dbContext;
            _twilioMessagingService = twilioMessagingService;
            _logger = logger;
        }

        /// <summary>
        /// Get the Twilio configuration for the given company
        /// </summary>
        /// <param name="companyId">The company to get Twilio configuration for</param>
        /// <returns>The Twilio configuration or errors</returns>
        private async Task<Result<TwilioOptions>> GetTwilioOptionsAsync(int companyId)
        {
            Company company = await _dbContext
                .Companies.AsNoTracking()
                .FirstOrDefaultAsync(i => i.CompanyID == companyId);

            return TwilioOptions.Create(company);
        }

        public class SendVerificationCodeDto
        {
            public string PhoneNumber { get; set; }
            public int CompanyId { get; set; }
        }

        /// <summary>
        /// Request to send verification code for the user
        /// </summary>
        [HttpPost]
        public async Task<ActionResult> SendVerificationCodeForMfa(SendVerificationCodeDto request)
        {
            var response = new ServiceResponse<object>();
            try
            {
                Result<TwilioOptions> twilioOptions = await GetTwilioOptionsAsync(
                    request.CompanyId
                );
                if (twilioOptions.IsFailure)
                {
                    _logger.LogError(
                        new InvalidOperationException(
                            $"Twilio configuration missing for company {request.CompanyId}"
                        )
                    );
                    response.Errors.Add(
                        "Error sending verification code. Please contact system administrator for support"
                    );
                    response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                }
                else
                {
                    var result = await _twilioMessagingService.SendVerificationCodeAsync(
                        twilioOptions.Value,
                        request.PhoneNumber
                    );
                    if (result.IsFailure)
                    {
                        _logger.LogError(new Exception(JsonConvert.SerializeObject(result)));
                        response.Errors.Add("Failed to send verification code. Please try again");
                        response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                    }
                    else
                    {
                        response.Value = new VerifyTwoFactorDTO()
                        {
                            VerficationSid = result.Value,
                            VerificationDestination = request.PhoneNumber,
                            VerificationType = "sms",
                        };
                        response.StatusCode = System.Net.HttpStatusCode.OK;
                    }
                }
            }
            catch (Exception)
            {
                _logger.LogError(new Exception(JsonConvert.SerializeObject(response)));
                response.Errors.Add("Failed to send verification code. Please try again");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }

        public class SetupMfaDto
        {
            public Guid UserGuid { get; set; }
            public string PhoneNumber { get; set; }
            public int CompanyId { get; set; }
            public string VerificationCode { get; set; }
            public string VerificationSid { get; set; }
        }

        /// <summary>
        /// Verifies a user's phone number to use for two-factor authentication and set up as well
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> SetupMfaForSms(SetupMfaDto request)
        {
            var response = new ServiceResponse<object>();
            try
            {
                Result<TwilioOptions> twilioOptions = await GetTwilioOptionsAsync(
                    request.CompanyId
                );
                if (twilioOptions.IsFailure)
                {
                    _logger.LogError(
                        new InvalidOperationException(
                            $"Twilio configuration missing for company {request.CompanyId}"
                        )
                    );
                    response.Errors.Add(
                        "Error sending verification code. Please contact system administrator for support"
                    );
                    response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                }
                else
                {
                    // Verify the code before setting up new two factor phone number
                    var result = await _twilioMessagingService.VerifyAsync(
                        twilioOptions.Value,
                        request.VerificationCode,
                        request.VerificationSid
                    );
                    if (result.IsFailure)
                    {
                        _logger.LogError(new Exception(JsonConvert.SerializeObject(response)));
                        response.Errors.Add(
                            "Failed to set up two-factor authentication. Please try again"
                        );
                        response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
                    }

                    // Set up the SMS mobile phone number
                    User user = await _dbContext.Users.SingleOrDefaultAsync(i => i.UserGuid  == request.UserGuid);
                    if (user == null)
                    {
                        _logger.LogError(new Exception(JsonConvert.SerializeObject(response)));
                        response.Errors.Add("Can't find existing user with the given user name.");
                        response.StatusCode = System.Net.HttpStatusCode.BadRequest;
                    }
                    else
                    {
                        user.EnableTwoFactorSms = true;
                        user.TwoFactorPhoneNumber = request.PhoneNumber;
                        user.TwoFactorPhoneNumberVerified = true;
                        response.StatusCode = System.Net.HttpStatusCode.OK;

                        await _dbContext.SaveChangesAsync();

                        // Determine redirect URL based on context
                        // If user already had MFA enabled, redirect back to UserSetup
                        // Otherwise, redirect to the login flow (for initial setup)
                        string redirectUrl = "/Home/Login?userGuid=" + user.UserGuid;

                        // Check if this is an update scenario (user already had MFA enabled)
                        var httpContext = System.Web.HttpContext.Current;
                        if (httpContext?.Request?.UrlReferrer?.AbsolutePath?.Contains("UpdatePhoneNumber") == true)
                        {
                            redirectUrl = "/TwoFactorAuthentication/UserSetup";
                        }

                        response.Value = new
                        {
                            RedirectUrl = redirectUrl,
                        };
                    }
                }
            }
            catch (Exception)
            {
                _logger.LogError(new Exception(JsonConvert.SerializeObject(response)));
                response.Errors.Add("Failed to set up two-factor authentication. Please try again");
                response.StatusCode = System.Net.HttpStatusCode.InternalServerError;
            }

            return Json(response, JsonRequestBehavior.AllowGet);
        }
    }
}
