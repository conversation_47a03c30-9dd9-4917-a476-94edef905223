using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
namespace DarwiNet2._0.Controllers
{
    public class CompanyContextFactory
    {
        private readonly string ContextKey = typeof(DnetEntities).FullName;
        public DnetEntities GetContextPerRequest()
        {
            HttpContext httpContext = HttpContext.Current;
            string conStr = string.Empty;
            try
            {
                // TODO: TW-MF: For the sake of conference purposes, we are disabling this.
                // NOTE: TW-MF: Revisiting this after conference, after merge. Do we need to reverse the change from conference?
                //conStr = GlobalVariables.CompanyConnection;
                conStr = GetDBConnection();
            }
            catch
            {
                conStr = GetDBConnection();
            }
            if (httpContext == null)
            {
                var context = (string.IsNullOrEmpty(conStr)) ? new DnetEntities() : new DnetEntities();
                //var ext = new EntitiesModelExt();
                //ext.TestMethod(context);
                return context;
            }
            else
            {
                DnetEntities context = httpContext.Items[ContextKey] as DnetEntities;
                if (context == null)
                {
                    context = (string.IsNullOrEmpty(conStr)) ? new DnetEntities() : new DnetEntities();
                    httpContext.Items[ContextKey] = context;
                    //var ext = new EntitiesModelExt();
                    //ext.TestMethod(context);
                }
                return context;
            }
        }

        public void Dispose()
        {
            HttpContext httpContext = HttpContext.Current;
            if (httpContext != null)
            {
                DnetEntities context = httpContext.Items[ContextKey] as DnetEntities;
                if (context != null)
                {
                    context.Dispose();
                    httpContext.Items[ContextKey] = null;
                    
                }
            }
        }

        public string GetDBConnection()
        {
            string dnetLocation = AppDomain.CurrentDomain.BaseDirectory;
            string file = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "globalcon.cfg");
            return (System.IO.File.Exists(file)) ? DNetSynch.ProjectSetup.CreateConnectionString(file) : string.Empty;
        }
    }
}