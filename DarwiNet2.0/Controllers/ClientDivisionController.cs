using ComponentSpace.SAML2.Assertions;
using DarwiNet2._0.Controllers.D2;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Logging;
using DarwiNet2._0.Models.D2;
using DarwiNet2._0.ViewModels;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Web;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers
{
    public class ClientDivisionController : D2ControllerBase
    {

        #region Fields
        private readonly DnetEntities _dbContext;
        #endregion

        #region Constructors
        public ClientDivisionController(DnetEntities dbContext, ILogger logger) : base(logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            //_ClientDivisionProvider = ClientDivisionProvider ?? throw new ArgumentNullException(nameof(ClientDivisionProvider));
        }
        #endregion

        public ActionResult Index()
        {
            var clientDivisionViewModel = new ClientDivisionViewModel();
            return View(clientDivisionViewModel);
        }

        [HttpPost]
        public ActionResult GetClientDivisionTableData(TableFilter filters)
        {
            try
            {
                var tableInfo = GetClientDivisionTableData(GlobalVariables.CompanyID, filters);
                tableInfo.Query = GetSortedList(tableInfo.Query, filters);
                tableInfo.Query = tableInfo.Query.Skip(filters.EntriesToSkip).Take(filters.PerPage);
                return JsonSuccess(data: new TableFilterData<ClientDivisionViewModel>
                {
                    Data = tableInfo.Query.ToList(),
                    FilteredEntries = tableInfo.FilteredEntries,
                    TotalEntries = tableInfo.TotalEntries
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult DeleteClientDivision(int companyID, string clientID, string divisionID)
        {
            try
            {
                var clientDivision = _dbContext.ClientDivisions.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID && x.DivisionID == divisionID);
                if (clientDivision != null)
                {
                    _dbContext.ClientDivisions.Remove(clientDivision);
                    _dbContext.SaveChanges();
                    return JsonSuccess(Messages.Success.RecordDeleted);
                }
                else
                {
                    return JsonError("No Record Found", null);
                }
            }
            catch (Exception exception)
            {
                return JsonError("An Error Has Occurred", exception);
            }
        }

        public ActionResult ClientDirectDebitSetup(int companyID, string clientID, string divisionID)
        {
            try
            {
                var clientDivision = _dbContext.ClientDivisions.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID && x.DivisionID == divisionID);
                if (clientDivision == null) return InternalServerError(new Exception("Client Division does not exist."));
                var client = _dbContext.Clients.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID);
                var clientDirectDebitSetupViewModel = new ClientDirectDebitSetupViewModel()
                {
                    CompanyID = clientDivision.CompanyID,
                    ClientID = clientDivision.ClientID,
                    ClientName = client?.ClientName,
                    ClientType = client?.ClientType,
                    DivisionID = clientDivision.DivisionID,
                    DivisionName = clientDivision.Description,
                    UseDirectDebit = clientDivision.UseDirectDebit,
                    PreNote = clientDivision.Precheck,
                    BankID = clientDivision.BankID,
                    AccountType = clientDivision.AccountType != null ? clientDivision.AccountType : (byte?)AccountType.Checking,
                    AccountNumber = clientDivision.AccountNumber,
                    ReceivablesProfileID = clientDivision.ReceivablesProfileID,
                    ReceivablesOffsetRecord = clientDivision.ReceivablesOffsetRecord,
                    ReceivablesOffsetCheckbookID = clientDivision.ReceivablesCheckbookID,
                    ReceivablesOffsetDays = clientDivision.ReceivablesOffsetDays,
                    BypassDirectDebitWindow = clientDivision.ByPassDirectDebitWindow,
                    AutoApplyCashReceipts = clientDivision.AutoApplyCashReceipts,
                    SaveCashReceiptsInBatch = clientDivision.SaveCashReceiptsInBatch,
                    CheckbookID = clientDivision.CheckbookIDCash,
                    AccountTypes = GetAccountTypes(),
                    Checkbooks = GetCheckbooks(),
                    Profiles = GetProfiles()
                };
                return View(clientDirectDebitSetupViewModel);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        public JsonResult SaveClientDirectDebitSetup(ClientDirectDebitSetupViewModel clientDirectDebitSetupVM)
        {
            try
            {
                var clientDivision = _dbContext.ClientDivisions.FirstOrDefault(x => x.CompanyID == clientDirectDebitSetupVM.CompanyID && x.ClientID == clientDirectDebitSetupVM.ClientID && x.DivisionID == clientDirectDebitSetupVM.DivisionID);
                if (clientDivision == null) return JsonInternalServerError(new Exception("Client Division does not exist."));
                clientDivision.UseDirectDebit = clientDirectDebitSetupVM.UseDirectDebit;
                clientDivision.Precheck = clientDirectDebitSetupVM.PreNote;
                clientDivision.BankID = clientDirectDebitSetupVM.BankID;
                clientDivision.AccountType = clientDirectDebitSetupVM.AccountType;
                clientDivision.AccountNumber = clientDirectDebitSetupVM.AccountNumber;
                clientDivision.ReceivablesProfileID = clientDirectDebitSetupVM.ReceivablesProfileID;
                clientDivision.ReceivablesOffsetRecord = clientDirectDebitSetupVM.ReceivablesOffsetRecord;
                clientDivision.ReceivablesCheckbookID = clientDirectDebitSetupVM.ReceivablesOffsetCheckbookID;
                clientDivision.ReceivablesOffsetDays = clientDirectDebitSetupVM.ReceivablesOffsetDays;
                clientDivision.ByPassDirectDebitWindow = clientDirectDebitSetupVM.BypassDirectDebitWindow;
                clientDivision.AutoApplyCashReceipts = clientDirectDebitSetupVM.AutoApplyCashReceipts;
                clientDivision.SaveCashReceiptsInBatch = clientDirectDebitSetupVM.SaveCashReceiptsInBatch;
                clientDivision.CheckbookIDCash = clientDirectDebitSetupVM.CheckbookID;
                _dbContext.SaveChanges();
                return Json(clientDirectDebitSetupVM);
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        [HttpPost]
        public ActionResult GetPagedBanks(PageInfo pageInfo)
        {
            try
            {
                var pagedQuery = _dbContext.Banks.Where(x => x.CompanyID == GlobalVariables.CompanyID);
                var totalCount = pagedQuery.Count();
                var totalFilteredCount = totalCount;
                if (!string.IsNullOrEmpty(pageInfo.SearchText))
                {
                    pagedQuery = pagedQuery.Where(x =>
                        x.BankID.Contains(pageInfo.SearchText) ||
                        x.BankName.Contains(pageInfo.SearchText) ||
                        x.City.Contains(pageInfo.SearchText) ||
                        x.State.Contains(pageInfo.SearchText)
                    );
                    totalFilteredCount = pagedQuery.Count();
                }
                pagedQuery = !string.IsNullOrEmpty(pageInfo.SortColumn)
                    ? pagedQuery.OrderBy(pageInfo.SortColumn, pageInfo.SortDirection == "Descending")
                    : pagedQuery.OrderBy(x => x.BankID);
                var paginatedResult = pagedQuery
                    .Skip((pageInfo.CurrentPage - 1) * pageInfo.PerPage)
                    .Take(pageInfo.PerPage)
                    .Select(x => new BankViewModel()
                    {
                        CompanyID = x.CompanyID,
                        BankID = x.BankID.Trim(),
                        BankName = x.BankName,
                        City = x.City,
                        State = x.State
                    })
                    .ToList();
                pageInfo.TotalItems = totalCount;
                pageInfo.TotalFilteredItems = totalFilteredCount;
                return JsonSuccess(data: new
                {
                    Items = paginatedResult,
                    PageInfo = pageInfo
                });
            }
            catch (Exception ex)
            {
                return JsonInternalServerError(ex);
            }
        }

        public bool IsValidBankID(int companyID, string bankID)
        {
            return _dbContext.Banks.Any(x => x.CompanyID == companyID && x.BankID == bankID);
        }

        #region private methods
        private TableQueryInfo<ClientDivisionViewModel> GetClientDivisionTableData(int companyID, TableFilter tableFilter)
        {
            var nonFilteredQuery = _dbContext.ClientDivisions
                .Join(_dbContext.Clients,
                    d => new { d.CompanyID, d.ClientID },
                    c => new { c.CompanyID, c.ClientID },
                    (d, c) => new { d, c })
                .Join(_dbContext.Banks,
                    x => new { x.d.CompanyID, x.d.BankID },
                    b => new { b.CompanyID, b.BankID },
                    (x, b) => new { x.d, x.c, b })
                .Where(x => x.d.CompanyID == companyID);

            var activeFilters = tableFilter.Filters
                .Where(x => x.ActiveFilters.Count > 0)
                .ToDictionary(x => x.Code, x => x.ActiveFilters);

            var filteredQuery = nonFilteredQuery;

            if (activeFilters.ContainsKey("SearchText"))
            {
                var search = (string)activeFilters["SearchText"].FirstOrDefault().Code;
                filteredQuery = filteredQuery
                    .Where(x =>
                        x.d.ClientID.Contains(search) ||
                        x.c.ClientName.Contains(search) ||
                        x.d.DivisionID.Contains(search) ||
                        x.d.Description.Contains(search) ||
                        x.d.BankID.Contains(search) ||
                        x.b.BankName.Contains(search)
                    );
            }

            return new TableQueryInfo<ClientDivisionViewModel>
            {
                Query = filteredQuery.Select(x => new ClientDivisionViewModel()
                {
                    CompanyID = x.d.CompanyID,
                    ClientID = x.d.ClientID,
                    ClientName = x.c.ClientName,
                    DivisionID = x.d.DivisionID,
                    DivisionName = x.d.Description,
                    BankID = x.d.BankID,
                    BankName = x.b.BankName
                })
                .OrderBy(x => x.ClientID)
                .ThenBy(x => x.DivisionID),
                TotalEntries = nonFilteredQuery.Count(),
                FilteredEntries = filteredQuery.Count()
            };
        }

        private IQueryable<ClientDivisionViewModel> GetSortedList(IQueryable<ClientDivisionViewModel> query, TableFilter tableFilter)
        {
            var sortBuilder = new SortBuilder<ClientDivisionViewModel>();
            return sortBuilder.Sort(query, tableFilter.Sort);
        }

        private List<SelectListItem> GetAccountTypes()
        {
            var type = typeof(AccountType);
            var values = Enum.GetValues(type);
            var names = Enum.GetNames(type);
            var options = new List<SelectListItem>();
            for (int i = 0; i < values.Length; i++)
            {
                options.Add(new SelectListItem
                {
                    Disabled = false,
                    Selected = false,
                    Text = type.GetMember(names.GetValue(i).ToString()).First().GetCustomAttribute<DisplayAttribute>().Name,
                    Value = ((int)values.GetValue(i)).ToString()
                });
            }
            return options;
        }

        private List<SelectListItem> GetCheckbooks()
        {
            return _dbContext.Checkbooks
                .Where(p => p.CompanyID == GlobalVariables.CompanyID)
                .OrderBy(x => x.CheckbookID)
                .Select(x => new SelectListItem
                {
                    Disabled = false,
                    Selected = false,
                    Text = x.CheckbookID + ": " + x.Description,
                    Value = x.CheckbookID
                })
                .ToList();
        }

        private List<SelectListItem> GetProfiles()
        {
            return _dbContext.ACHProfiles
                .OrderBy(p => p.ACHProfileID)
                .Select(ap => new SelectListItem
                {
                    Disabled = false,
                    Selected = false,
                    Text = ap.ACHProfileID,
                    Value = ap.ACHProfileID
                })
                .ToList();
        }
        #endregion
    }
}