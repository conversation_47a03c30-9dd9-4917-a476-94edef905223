using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Interfaces.Services.OB;
using DarwiNet2._0.Models;
using DarwiNet2._0.Providers.API;
using DarwiNet2._0.Services;
using DarwiNet2._0.Services.Authentication;
using DarwiNet2._0.ViewModels;
using DarwiNet2._0.ViewModels.Home;
using DataDrivenViewEngine.Models.Core;
using log4net;
using Newtonsoft.Json;
using Thinkware;
using Thinkware.Pay360.BillingAPI;
using Thinkware.Pay360.Payroll;
using Constants = DarwiNet2._0.Models.API.Constants;
using Notification = DarwiNet2._0.Core.Notifications;
using NotificationType = DarwiNet2._0.Core.NotificationType;
using ProjectSetup = DarwiNet2._0.Data.ProjectSetup;

namespace DarwiNet2._0.Controllers
{
    public class HomeController : Controller
    {
        private readonly DnetEntities _dbContext;
        private readonly IBillingApiService _billingApiService;
        private readonly IChangeRequestsHDRService _changeRequestsHDRService;
        private readonly IOBUserService _obUserService;
        private readonly MfaPolicyService _mfaPolicyService;
        private static readonly ILog log = LogManager.GetLogger(
            MethodBase.GetCurrentMethod().DeclaringType
        );

        public HomeController(
            DnetEntities dbContext,
            IBillingApiService billingApiService,
            IChangeRequestsHDRService changeRequestsHDRService,
            IOBUserService obUserService,
            MfaPolicyService mfaPolicyService
        )
        {
            _dbContext = Guard.ThrowIfNull(dbContext, nameof(dbContext));
            _billingApiService = Guard.ThrowIfNull(billingApiService, nameof(billingApiService));
            _changeRequestsHDRService = Guard.ThrowIfNull(
                changeRequestsHDRService,
                nameof(changeRequestsHDRService)
            );
            _obUserService = obUserService;
            _mfaPolicyService = mfaPolicyService;
        }

        public ActionResult Index(string errorMessage = null, string destinationUrl = null)
        {
            IndexVM vm = new IndexVM();
            if (destinationUrl != null)
            {
                Session["DestinationUrl"] = destinationUrl;
            }
            string dnetLocation = AppDomain.CurrentDomain.BaseDirectory;
            string file = Path.Combine(dnetLocation, "globalcon.cfg");
            bool exists = System.IO.File.Exists(file);
            if (!exists)
            {
                return RedirectToAction("CreateCon");
            }
            DnetEntities db = new DnetEntities();
            var showNewUserButton = false;
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();

            if (thisSetup != null)
            {
                vm.WelcomeTitle = thisSetup.WelcomeTitle;
                vm.Message = thisSetup.WelcomePageText;
                vm.LogoURL =
                    GlobalVariables.DNetURL
                    + "Assets/"
                    + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);

                // Check the Dnet Registration Key
                var projSetup = db.ProjectSetups.FirstOrDefault();
                if (projSetup != null)
                {
                    GlobalVariables.DNetURL = projSetup.DnetAddress;
                    if (
                        string.IsNullOrEmpty(projSetup.DnetRegistrationKey)
                        || !CheckKey(projSetup.DnetRegistrationKey, "RegistrationKey")
                    )
                    {
                        return RedirectToAction("KeyExpired");
                    }
                    if (!string.IsNullOrEmpty(projSetup.AllowOB))
                    {
                        showNewUserButton = CheckKey(projSetup.AllowOB, "Onboarding");
                    }
                    GlobalVariables.ApplicationVersion = projSetup.ApplicationVersion;
                }

                if (!string.IsNullOrEmpty(thisSetup.WelcomeBackground))
                {
                    GlobalVariables.WelcomeBackground =
                        GlobalVariables.DNetURL + "Assets/" + thisSetup.WelcomeBackground;
                }
                else
                {
                    GlobalVariables.WelcomeBackground = null;
                }
            }

            var dnetSetups = db.DarwinetSetups.Where(ds => ds.AllowNewEmployeeSignUp).ToList();
            if (dnetSetups.Any())
            {
                showNewUserButton = true;
            }

            vm.ShowNewUserButton = showNewUserButton;

            if (!string.IsNullOrWhiteSpace(errorMessage))
            {
                TempData["Error"] = errorMessage;
            }

            return View(vm);
        }

        /// <summary>
        ///  Logs a user in from the Login screen.
        /// </summary>
        /// <param name="vm"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> Index(IndexVM vm)
        {
            using (
                AuthenticationProvider authenticationProvider = new AuthenticationProvider(
                    _dbContext
                )
            )
            {
                Result<User> isAuthenticated = await authenticationProvider.AuthenticateAsync(
                    vm.Username,
                    vm.Password
                );

                // Display appropriate error message on login screen
                if (isAuthenticated.IsFailure)
                {
                    TempData["Error"] = isAuthenticated.ErrorMessage;
                    return RedirectToAction(nameof(Index));
                }

                User user = isAuthenticated.Value;

                // Perform MFA if required (email or sms)
                // TODO - need to check the company policy rather than user or
                // make sure when company policy changes, it will cascade down to user correctly
                if (user.EnableTwoFactorSms || user.EnableTwoFactorEmail)
                {
                    // Check if the user is on a trusted device
                    HttpCookie cookie = Request.Cookies[ConCryptor.Encrypt(user.UserID.ToLower())];

                    if (cookie == null || cookie.Value != user.TwoFactorCookieValue.ToString())
                    {
                        // Hack entry into controller with [IsSessionActive]
                        GlobalVariables.Customer = "Mark 'The Hacker' Artmayer";

                        // The user is not on a trusted device
                        return RedirectToAction(
                            "VerifyUser",
                            "TwoFactorAuthentication",
                            new { a = user.UserGuid.ToString() }
                        );
                    }
                }

                // Login otherwise
                return await Login(vm);
            }
        }

        /// <summary>
        ///  Logs a user in.
        /// </summary>
        /// <param name="vm"></param>
        /// <returns></returns>
        public async Task<ActionResult> Login(IndexVM vm, bool continueLoggingIn = false, string userGuid = null)
        {
            var companyId = GlobalVariables.CompanyID;
            var userId = GlobalVariables.DNETOwnerID;
            var destinationUrl = GlobalVariables.DestinationUrl;

            // Un-hack entry into this controller, which we hacked in order to go to VerifyUser in the TwoFactorAuthenticationController
            GlobalVariables.Customer = null;

            if (vm == null)
                vm = new IndexVM();

            // We are getting here because our user session was active on login, but we wanted to continue logging in anyway
            if (continueLoggingIn)
            {
                vm.Username = userId;
                vm.Password = ConCryptor.Decrypt(GlobalVariables.DNETOwnerPWD);
            }

            // We are getting here from the VerifyUser screen (two factor authentication)
            if (!string.IsNullOrEmpty(userGuid))
            {
                var user = await _dbContext.Users
                    .Where(x => x.UserGuid.ToString().ToUpper() == userGuid.ToUpper())
                    .FirstOrDefaultAsync();
                if (user == null)
                {
                    return this.RedirectError();
                }
                
                vm.Username = user.UserID;
                vm.Password = ConCryptor.Decrypt(user.Password);
            }

            // MANDATORY MFA ENFORCEMENT - Check if user needs to set up MFA before proceeding
            if (!string.IsNullOrEmpty(vm.Username))
            {
                var currentUser = _dbContext.Users.FirstOrDefault(u => u.UserID == vm.Username);
                if (currentUser != null)
                {
                    // Find the company user being assigned to that has SMS two-factor authentication set up
                    List<int> companiesUserAssignedTo = await _dbContext.UserRoleClientEmployeeAssignments
                        .Where(i => i.UserID == currentUser.UserID)
                        .Select(i => i.CompanyID)
                        .ToListAsync();
                    Company companyRequiredSmsTwoFactor = _dbContext.Companies
                        .FirstOrDefault(i => companiesUserAssignedTo.Contains(i.CompanyID) && i.EnableTwoFactorSms && i.TwilioAccountVerified);
                    
                    // Check if mandatory MFA setup is required for this user
                    if (companyRequiredSmsTwoFactor != null && 
                        _mfaPolicyService.IsMfaSetupRequired(currentUser, companyRequiredSmsTwoFactor , MfaMethod.Sms))
                    {
                        // Hack entry into controller with [IsSessionActive] for MFA setup
                        GlobalVariables.Customer = "MFA Setup Required";
                        return RedirectToAction(
                            "UserMfaSetup",
                            "TwoFactorAuthentication",
                            new { userGuid = currentUser.UserGuid, companyId = companyRequiredSmsTwoFactor.CompanyID }
                        );
                    }
                }
            }

            var pwd = string.Empty;
            var isSSO = false;
            if (vm.SSOToken != null)
            {
                var ssoUser = _dbContext
                    .Users.Where(u => u.SSOToken == vm.SSOToken)
                    .FirstOrDefault();
                if (ssoUser != null)
                {
                    userId = ssoUser.UserID;
                    pwd = ConCryptor.Decrypt(ssoUser.Password);
                    isSSO = true;
                    ssoUser.SSOToken = null;
                    _dbContext.SaveChanges();
                }
            }
            else
            {
                userId = !string.IsNullOrWhiteSpace(vm.Username) ? vm.Username : string.Empty;
                pwd = !string.IsNullOrWhiteSpace(vm.Password) ? vm.Password : string.Empty;
            }

            if (!string.IsNullOrWhiteSpace(vm.FromPage))
            {
                if (!GlobalVariables.LoggedInAsFromClient)
                {
                    userId = GlobalVariables.LoggedInAsUser;
                    pwd = ConCryptor.Decrypt(GlobalVariables.LoggedInUserPWD);
                }
                else
                {
                    userId = GlobalVariables.LoggedInAsClientUser;
                    pwd = ConCryptor.Decrypt(GlobalVariables.LoggedInAsClientUserPWD);

                    GlobalVariables.LoggedInAsFromClient = false;
                }
            }

            userId = userId.Trim();
            pwd = pwd.Trim();

            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(pwd))
            {
                TempData["Error"] = "You must provide a UserID and Password";
                return RedirectToAction("Index");
            }

            // Clear login as globals for new login
            GlobalVariables.LoggedInAs = false;
            GlobalVariables.LoggedInAsUser = string.Empty;
            GlobalVariables.LoggedInUserPWD = string.Empty;

            GlobalVariables.DNETOwnerID = userId;
            GlobalVariables.DNETOwnerPWD = pwd;
            GlobalVariables.AppTest = string.Empty;
            GlobalVariables.AppTest += "Set Entry Post";
            ModelState.AddModelError("Error", "Got Post");
            var projSetup = new DNetSynch.ProjectSetup(_billingApiService);
            GlobalVariables.AppTest += "Called Proj Setup";
            ModelState.AddModelError("Error", "Created Static");
            var myRes = projSetup.SetParameters(isSSO, out string myString);
            if (!isSSO)
            {
                var cryptPass = ConCryptor.Encrypt(pwd);
                OBUser exuser = _dbContext.OBUsers.FirstOrDefault(x =>
                    x.UserID == userId && x.Password == cryptPass
                );
                if (exuser != null)
                {
                    OBProcessMonitor omonitor = _dbContext.OBProcessMonitors.FirstOrDefault(x =>
                        x.EmployeeID == exuser.EmployeeID && x.IsCompleted == false
                    );
                    if (omonitor != null)
                    {
                        if (omonitor.RehireEmployeeID != null)
                        {
                            GlobalVariables.IsReHire = true;
                            GlobalVariables.ReHireEmployeeID = omonitor.RehireEmployeeID;
                        }
                        else
                        {
                            GlobalVariables.IsReHire = false;
                        }
                    }
                    else
                    {
                        GlobalVariables.IsReHire = false;
                    }
                }
            }
            if (!string.IsNullOrEmpty(myString))
            {
                TempData["Error"] = myString;
                return RedirectToAction("Index");
            }

            //Added by Tecnics on 30JUNE2021 for tkt:1074
            if (myRes == "ForgotPassword")
            {
                User Useredetails = _dbContext.Users.FirstOrDefault(urcea =>
                    urcea.UserID == GlobalVariables.DNETOwnerID
                );
                var userRoleClientEmployeeAssignment =
                    _dbContext.UserRoleClientEmployeeAssignments.FirstOrDefault(urcea =>
                        urcea.UserID == GlobalVariables.DNETOwnerID
                    );

                if (userRoleClientEmployeeAssignment != null)
                {
                    DarwiNetNotification notification =
                        _dbContext.DarwiNetNotifications.FirstOrDefault(n =>
                            n.Type == Core.NotificationType.SecurityLockout
                            && n.CompanyID == userRoleClientEmployeeAssignment.CompanyID
                        );
                    string mailResult = string.Empty;

                    if (notification.Enabled)
                    {
                        GlobalVariables.Section = NotificationLogSection.CredentialsRequest;
                        GlobalVariables.EmailType = "Security Lockout";
                        GlobalVariables.Assigned = 0;

                        Company Compdetails = _dbContext.Companies.FirstOrDefault(urcea =>
                            urcea.CompanyID == userRoleClientEmployeeAssignment.CompanyID
                        );

                        if (!string.IsNullOrEmpty(Useredetails.Email))
                        {
                            SmtpEmail.SendSmtpEmail(
                                Useredetails.Email,
                                "",
                                notification.EmailSubject,
                                notification.EmailBody,
                                (List<Code_Description>)null,
                                (List<Code_Description>)null,
                                (List<System.Net.Mail.Attachment>)null,
                                false,
                                out mailResult
                            );
                        }
                    }
                }
                TempData["Error"] =
                    "You have been locked out due to too many unsuccessful attempts. At this point, you must reset your password for security purposes.";
                return RedirectToAction("ForgotPassword", "Home");
            }
            //end

            GlobalVariables.AppTest += "Calling Set Parameters";
            SetThemeCss(GlobalVariables.ColorSchema);
            ModelState.AddModelError("Error", "Finished Set Parameters");
            GlobalVariables.LanguageID = 1;
            GlobalVariables.ClientMaskSSN = true;

            // Create default documents.
            var defaultDocumentCount = _dbContext.OBDocuments.Count(x =>
                x.CompanyID == companyId && x.Locked
            );
            if (defaultDocumentCount <= 0)
            {
                _dbContext.Database.ExecuteSqlCommand(
                    "usp_OBCreateDefaultDocuments @CompanyID",
                    new SqlParameter("CompanyID", companyId)
                );
                _dbContext.SaveChanges();
            }
            SetLevel(GlobalVariables.DnetRoleRecordID.ToString(), myRes);

            var redirectAction = "Index";
            var redirectController = "Dashboard";
            if (myRes == "isOb")
            {
                redirectAction = "OBProcessTasksIndex";
                redirectController = "OBProcessMonitor";
            }
            var activityStatus = CheckForSession(false);
            if (activityStatus == "OtherUser" && !GlobalVariables.LoggedInAs)
            {
                redirectAction = "ExistingSession";
                redirectController = "Home";
            }
            var result = RedirectToAction(redirectAction, redirectController);

            _changeRequestsHDRService.RestoreDeniedRequestData();

            if (!string.IsNullOrWhiteSpace(destinationUrl))
            {
                var delimitorChars = new char[] { '/', '?' };
                var urlComponents = destinationUrl.Split(
                    delimitorChars,
                    StringSplitOptions.RemoveEmptyEntries
                );
                if (urlComponents.Length == 3)
                {
                    redirectController = urlComponents[0];
                    redirectAction = urlComponents[1];
                    var urlParameters = urlComponents[2].Split('&');
                    var routeValues = new RouteValueDictionary();
                    foreach (var parameter in urlParameters)
                    {
                        if (parameter.Contains("="))
                        {
                            var parameterComponents = parameter.Split('=');
                            var parameterValue = Server.UrlDecode(parameterComponents[1]);
                            var parameterName = Server.UrlDecode(parameterComponents[0]);
                            routeValues.Add(parameterName, parameterValue);
                            if (parameterName.ToLower() == "payrollnumber")
                            {
                                var oPayrollNumber = PayrollNumber.Parse(parameterValue);
                                var urcea = _dbContext
                                    .UserRoleClientEmployeeAssignments.Where(x =>
                                        x.UserID == userId
                                        && x.CompanyID == oPayrollNumber.CompanyId
                                        && x.ClientID == oPayrollNumber.ClientId
                                    )
                                    .FirstOrDefault();
                                if (urcea != null)
                                {
                                    SetLevel(urcea.id.ToString(), myRes);
                                }
                            }
                        }
                        result = RedirectToAction(redirectAction, redirectController, routeValues);
                    }
                }
            }

            var company = _dbContext
                .Companies.Where(x => x.CompanyID == companyId)
                .FirstOrDefault();
            if (company != null && company.UseSecurityQuestions)
            {
                var hasSecurityQuestions = _dbContext.SecurityAnswers.Any(x => x.UserID == userId);
                if (!hasSecurityQuestions)
                {
                    result = RedirectToAction(
                        "Index",
                        "SecurityQuestions",
                        new
                        {
                            userId,
                            redirectAction,
                            redirectController,
                        }
                    );
                }
            }

            return result;
        }

        public static bool CheckKey(string key, string type)
        {
            key = key.Trim();
            key = key.Replace(Environment.NewLine, "");
            bool active = false;
            string url = null;
            object keyCacheType = null;
            string billingApiUrl = ConfigurationManager.AppSettings["BillingAPIUrl"];

            switch (type)
            {
                case "RegistrationKey":
                    url = billingApiUrl + "/API/KeyCheck";
                    keyCacheType = System.Runtime.Caching.MemoryCache.Default["activeKey"];
                    break;
                case "SwipeClock":
                    url = billingApiUrl + "/API/SwipeClockKeyCheck";
                    keyCacheType = System.Runtime.Caching.MemoryCache.Default["activeSwipeKey"];
                    break;
                case "Onboarding":
                    url = billingApiUrl + "/API/OBKeyCheck";
                    keyCacheType = System.Runtime.Caching.MemoryCache.Default[
                        "activeOnboardingKey"
                    ];
                    break;
                /*case "Payroll":
                    url = billingApiUrl + "/API/PayrollCheck";
                    keyCacheType = System.Runtime.Caching.MemoryCache.Default["activePayrollKey"];
                    break;*/
            }
            var activeKey = keyCacheType;
            if (activeKey != null && (string)activeKey == "true")
            {
                return true;
            }

            // Verify key with billing API
            var values = new Dictionary<string, string>
            {
                {
                    "token",
                    "CdBUIVx6WbLRBjsJDscZtv41KXvxuWhvOs5lWKhFJRJhBQyPxRpgJeVZ7wJC6AKryaLo51d2upE9GHGJr8WntQMFPajasQ4F2JjbKrdD1S"
                },
                { "key", key },
            };

            //HttpClient requestClient = new HttpClient();

            try
            {
                using (var client = new HttpClient())
                {
                    var content = new FormUrlEncodedContent(values);

                    var response = client.PostAsync(url, content).GetAwaiter().GetResult();
                    //var response = client.PostAsJsonAsync("api/person", p).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        var json = response.Content.ReadAsStringAsync();
                        var result = "[" + json.Result + "]";
                        var keyInfo = JsonConvert.DeserializeObject<List<DnetRegistrationKey>>(
                            result
                        );

                        var status = keyInfo.First().Status;
                        var message = keyInfo.First().Message;

                        if (status == "Success")
                        {
                            switch (type)
                            {
                                case "RegistrationKey":
                                    System.Runtime.Caching.MemoryCache.Default.Add(
                                        "activeKey",
                                        "true",
                                        new DateTimeOffset(DateTime.Now.AddDays(1))
                                    );
                                    break;
                                case "SwipeClock":
                                    System.Runtime.Caching.MemoryCache.Default.Add(
                                        "activeSwipeKey",
                                        "true",
                                        new DateTimeOffset(DateTime.Now.AddDays(1))
                                    );
                                    break;
                                case "Onboarding":
                                    System.Runtime.Caching.MemoryCache.Default.Add(
                                        "activeOnboardingKey",
                                        "true",
                                        new DateTimeOffset(DateTime.Now.AddDays(1))
                                    );
                                    break;
                                /*case "Payroll":
                                    System.Runtime.Caching.MemoryCache.Default.Add(
                                        "activePayrollKey",
                                        "true",
                                        new DateTimeOffset(DateTime.Now.AddDays(1))
                                    );
                                    break;*/
                            }
                            active = true;
                        }
                    }
                    else
                    {
                        //SlackMessage("", type);
                    }
                }
            }
            catch (Exception e)
            {
                //SlackMessage(e.ToString(), type);
            }

            return active;
        }

        public static void SlackMessage(string exception, string type)
        {
            var slackUrl =
                "*****************************************************************************";
            object showException = null;
            if (!string.IsNullOrEmpty(exception))
            {
                showException = "```\n" + exception + "\n```";
            }
            var payload = new
            {
                text = "The DNet2 "
                    + type
                    + " API cannot be reached from "
                    + GlobalVariables.DNetURL
                    + " or is returning a 404. \n\nException Message (can be blank): "
                    + showException,
            };

            HttpClient _httpClient = new HttpClient();

            var serializedPayload = JsonConvert.SerializeObject(payload);

            var response = _httpClient.PostAsync(
                slackUrl,
                new StringContent(serializedPayload, Encoding.UTF8, "application/json")
            );
        }

        protected bool CheckDate(String date)
        {
            try
            {
                DateTime dt = DateTime.Parse(date);

                return true;
            }
            catch
            {
                return false;
            }
        }

        public ActionResult KeyExpired()
        {
            DnetEntities db = new DnetEntities();
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
            ProjectSetup projSetup = db.ProjectSetups.FirstOrDefault();
            if (string.IsNullOrEmpty(GlobalVariables.DNetURL))
                GlobalVariables.DNetURL = projSetup.DnetAddress;
            ViewBag.LogoURL =
                GlobalVariables.DNetURL
                + "Assets/"
                + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);

            return View();
        }

        [HttpPost]
        public ActionResult AddRegKey(FormCollection collection)
        {
            if (!string.IsNullOrEmpty(collection["newKey"]))
            {
                var legitKey = LegitKey(collection["newKey"]);

                if (!legitKey)
                {
                    TempData["Info"] = "The registration key entered is invalid";
                    return Redirect("KeyExpired");
                }

                DnetEntities db = new DnetEntities();
                var projSetup = db.ProjectSetups.FirstOrDefault();
                if (string.IsNullOrEmpty(GlobalVariables.DNetURL))
                    GlobalVariables.DNetURL = projSetup.DnetAddress;

                projSetup.DnetRegistrationKey = collection["newKey"];

                db.SaveChanges();

                return Redirect("Index");
            }

            TempData["Info"] = "The registration key entered is invalid";
            return Redirect("KeyExpired");
        }

        public bool LegitKey(string key)
        {
            var legit = false;

            var values = new Dictionary<string, string> { { "key", key } };

            using (var client = new HttpClient())
            {
                var content = new FormUrlEncodedContent(values);
                string billingApiUrl = ConfigurationManager.AppSettings["BillingAPIUrl"];
                var response = client.PostAsync(billingApiUrl + "/API/LegitKey", content);
                //var response = client.PostAsJsonAsync("api/person", p).Result;
                if (response.Result.IsSuccessStatusCode)
                {
                    var json = response.Result.Content.ReadAsStringAsync();
                    var result = "[" + json.Result + "]";
                    var keyInfo = JsonConvert.DeserializeObject<List<DnetRegistrationKey>>(result);

                    var status = keyInfo.First().Status;
                    var message = keyInfo.First().Message;

                    if (status.ToLower() == "true")
                    {
                        legit = true;
                    }
                }
                else
                {
                    //SlackMessage("");
                }
            }

            return legit;
        }

        public ActionResult About()
        {
            ViewBag.Message = GlobalVariables.AppTest;
            return View();
        }

        public ActionResult Contact()
        {
            ViewBag.Message = "Your contact page.";
            return View();
        }

        [HttpGet]
        public ViewResult Dynamic(string id)
        {
            return View(id);
        }

        [HttpPost]
        public ActionResult Dynamic(string id, FormCollection collection)
        {
            return View(id);
        }

        public ActionResult SetTheme(string id)
        {
            SetThemeCss(id);
            Session["Theme"] = id;
            return View("Index");
        }

        public void ReloginSystem() { }

        public ActionResult Relog()
        {
            RelogVM vm = new RelogVM();

            if (!GlobalVariables.LoggedInAsFromClient)
            {
                if (!GlobalVariables.LoggedInAs)
                {
                    return RedirectToAction("Index");
                }
            }

            vm.FromPage = "relog";

            return View(vm);
        }

        [HttpPost]
        public ActionResult Relog(RelogVM vm)
        {
            RelogVM indexVM = new RelogVM();

            indexVM.FromPage = vm.FromPage;

            return RedirectToAction("Login", indexVM);
        }

        public void ClearLoginCache()
        {
            GlobalVariables.DependentFirstLoad = false;
            GlobalVariables.SummaryFirstLoad = false;
            GlobalVariables.PlanFirstLoad = false;
            GlobalVariables.QuickSearch_Employees = null;
            Session["CodeDescriptions"] = null;
            GlobalVariables.Benefit_Descriptions = null;
            GlobalVariables.Paycode_Descriptions = null;
            GlobalVariables.Deduction_Descriptions = null;
            GlobalVariables.Department_Descriptions = null;
            GlobalVariables.Position_Descriptions = null;
            GlobalVariables.WC_Descriptions = null;
            GlobalVariables.SUTAState_Descriptions = null;
        }

        public ActionResult SetLevel(string l, string o, bool refreshActivity = true)
        {
            if (GlobalVariables.Customer == null)
            {
                return RedirectToAction("Index");
            }
            GlobalVariables.IsIE = false; // (GlobalVariables.Browser == "InternetExplorer" || GlobalVariables.Browser == "IE");
            //clean up role specific session variables.
            ClearLoginCache();

            GlobalVariables.Browser = HttpContext.Request.Browser.Browser.Trim();
            if (Request.Url != null)
            {
                var contactUsUriString = string.Format(
                    "{0}://{1}{2}",
                    Request.Url.Scheme,
                    Request.Url.Authority,
                    Url.Content("~")
                );

                if (contactUsUriString != null)
                    GlobalVariables.DNetURL = contactUsUriString;

                // Use the test environment instead of localhost
                if (System.Web.HttpContext.Current.Request.IsLocal)
                {
                    GlobalVariables.DNetURL = ConfigurationManager.AppSettings[
                        "DnetTestEnvironmentUrl"
                    ];
                }
            }
            using (DnetEntities db = new DnetEntities())
            {
                if (db != null)
                {
                    var projSetup = db.ProjectSetups.FirstOrDefault();
                    if (projSetup != null)
                    {
                        if (GlobalVariables.CodeVersion != projSetup.CodeVersion)
                        {
                            projSetup.CodeVersion = GlobalVariables.CodeVersion;
                            db.SaveChanges();
                        }

                        // set asset url
                        GlobalVariables.DNetAssets = projSetup.DnetAddress + "Assets/";
                    }
                }
                if (o == "isOb")
                {
                    if (db != null)
                    {
                        DarwinetSetup thisClientSetup = db.DarwinetSetups.FirstOrDefault(ds =>
                            ds.CompanyID == GlobalVariables.CompanyID
                            && ds.ClientID == GlobalVariables.Client
                        );
                        Company thisCompany = db.Companies.FirstOrDefault(c =>
                            c.CompanyID == GlobalVariables.CompanyID
                        );
                        GlobalVariables.DNETLevel = DNetAccessLevel.Employee;
                        GlobalVariables.MaskSSN = (
                            db
                                .DarwinetSetups.First(ds =>
                                    ds.CompanyID == GlobalVariables.CompanyID
                                    && ds.ClientID == GlobalVariables.Client
                                )
                                .MaskSSN
                        );
                        if (thisClientSetup != null)
                        {
                            SetFooter("Employee", thisCompany, thisClientSetup);
                            GlobalVariables.ClientName = db
                                .Clients.Where(c =>
                                    c.CompanyID == GlobalVariables.CompanyID
                                    && c.ClientID == GlobalVariables.Client
                                )
                                .Select(c => c.ClientName)
                                .FirstOrDefault();
                            GlobalVariables.UseD2 = db
                                .Clients.Where(c =>
                                    c.CompanyID == GlobalVariables.CompanyID
                                    && c.ClientID == GlobalVariables.Client
                                )
                                .Select(c => c.UseDnetPayroll)
                                .FirstOrDefault();
                            GlobalVariables.Company = db
                                .Companies.Where(co => co.CompanyID == GlobalVariables.CompanyID)
                                .Select(co => co.DnetCompanyID)
                                .FirstOrDefault();
                            if (thisCompany != null)
                                GlobalVariables.Customer = thisCompany.DnetClientID;
                            GlobalVariables.ColorSchema =
                                (!string.IsNullOrEmpty(thisClientSetup.DefaultColorScheme))
                                    ? thisClientSetup.DefaultColorScheme
                                    : thisCompany.DefaultColorScheme;
                        }
                        if (thisClientSetup.AlwaysTreatAsNotIE)
                            GlobalVariables.IsIE = false;
                    }
                    SetThemeCss(GlobalVariables.ColorSchema);
                    return RedirectToAction("OBProcessTasksIndex", "OBProcessMonitor");
                }
                if (db != null)
                {
                    var convertL = Convert.ToInt32(l);
                    UserRoleClientEmployeeAssignment thisRole =
                        db.UserRoleClientEmployeeAssignments.FirstOrDefault(urcea =>
                            urcea.id == convertL
                        );
                    string level =
                        thisRole != null && thisRole.UserRole.RoleType == UserRoleTypes.System
                            ? "System"
                        : thisRole != null && (thisRole.UserRole.RoleType == UserRoleTypes.Client)
                            ? "Client"
                        : "Employee";
                    Company thisCompany = db.Companies.FirstOrDefault(c =>
                        c.CompanyID == thisRole.CompanyID
                    );
                    User thisUser = db.Users.FirstOrDefault(u => u.UserID == thisRole.UserID);
                    DarwinetSetup thisClientSetup = db.DarwinetSetups.FirstOrDefault(ds =>
                        ds.CompanyID == thisRole.CompanyID && ds.ClientID == thisRole.ClientID
                    );
                    if (thisClientSetup != null && thisClientSetup.AlwaysTreatAsNotIE)
                        GlobalVariables.IsIE = false;
                    GlobalVariables.Customer =
                        (thisCompany != null) ? thisCompany.DnetClientID : string.Empty;
                    GlobalVariables.IsClientAdmin = false;
                    if (level == "System")
                    {
                        if (thisRole != null)
                        {
                            ValidateClientTables(0, "");
                            GlobalVariables.CompanyID = thisRole.CompanyID;
                            GlobalVariables.DNETLevel = DNetAccessLevel.System;
                            GlobalVariables.EmployeeID = string.Empty;
                            GlobalVariables.Client = string.Empty;
                            GlobalVariables.ClientName = string.Empty;
                            GlobalVariables.UseD2 = false;
                            GlobalVariables.MaskSSN = true;
                            var rec = db.Companies.FirstOrDefault(co =>
                                co.CompanyID == thisRole.CompanyID
                            );
                            if (rec != null)
                            {
                                GlobalVariables.Company = rec.DnetCompanyID;
                                GlobalVariables.Customer = rec.DnetClientID;
                                var assets = "Assets";
                                GlobalVariables.CompanyFolder = Path.Combine(
                                    GlobalVariables.DNetLocation,
                                    assets,
                                    GlobalVariables.Customer + "." + GlobalVariables.Company
                                );
                                GlobalVariables.ClientFolder = GlobalVariables.CompanyFolder;
                                GlobalVariables.PEOName = rec.CompanyName;
                                GlobalVariables.PEOMail =
                                    (string.IsNullOrEmpty(rec.Email))
                                        ? string.Empty
                                        : rec.Email.Trim();
                                GlobalVariables.PEOPhone =
                                    (string.IsNullOrEmpty(rec.Phone1))
                                        ? string.Empty
                                        : FieldTranslation.FormatPhone(rec.Phone1);
                                GlobalVariables.EIN =
                                    (string.IsNullOrEmpty(rec.CompanyEIN))
                                        ? string.Empty
                                        : rec.CompanyEIN; // 02/12/2018 DS TFS # 3008
                            }
                        }
                    }
                    else if (level == "Client")
                    {
                        if (thisRole != null)
                        {
                            ValidateClientTables(thisRole.CompanyID, thisRole.ClientID);
                            GlobalVariables.CompanyID = thisRole.CompanyID;
                            GlobalVariables.DNETLevel = DNetAccessLevel.Client;
                            GlobalVariables.DnetRoleID = thisRole.RoleID;
                            GlobalVariables.EmployeeID = string.Empty;
                            GlobalVariables.Client = thisRole.ClientID;
                            GlobalVariables.MaskSSN = db
                                .DarwinetSetups.FirstOrDefault(ds =>
                                    ds.CompanyID == GlobalVariables.CompanyID
                                    && ds.ClientID == GlobalVariables.Client
                                )
                                .MaskSSN;
                        }
                        GlobalVariables.ClientName = db
                            .Clients.Where(c =>
                                c.CompanyID == thisRole.CompanyID && c.ClientID == thisRole.ClientID
                            )
                            .Select(c => c.ClientName)
                            .FirstOrDefault();
                        GlobalVariables.UseD2 = db
                            .Clients.Where(c =>
                                c.CompanyID == thisRole.CompanyID && c.ClientID == thisRole.ClientID
                            )
                            .Select(c => c.UseDnetPayroll)
                            .FirstOrDefault();
                        GlobalVariables.Company = db
                            .Companies.Where(co => co.CompanyID == thisRole.CompanyID)
                            .Select(co => co.DnetCompanyID)
                            .FirstOrDefault();
                        GlobalVariables.IsClientAdmin = thisRole.ClientAdmin;
                        var assets = "Assets";
                        GlobalVariables.CompanyFolder = Path.Combine(
                            GlobalVariables.DNetLocation,
                            assets,
                            GlobalVariables.Customer + "." + GlobalVariables.Company
                        );
                        GlobalVariables.ClientFolder = Path.Combine(
                            GlobalVariables.CompanyFolder,
                            GlobalVariables.Client
                        );
                        GlobalVariables.ExportFolder = Path.Combine(
                            GlobalVariables.DNetLocation,
                            assets,
                            "Export",
                            "Data"
                        );
                        GlobalVariables.ClientName = FieldTranslation.GetClientName(
                            GlobalVariables.CompanyID,
                            GlobalVariables.Client
                        );
                    }
                    else
                    {
                        if (thisRole != null)
                        {
                            ValidateClientTables(thisRole.CompanyID, thisRole.ClientID);
                            GlobalVariables.CompanyID = thisRole.CompanyID;
                            GlobalVariables.DNETLevel = DNetAccessLevel.Employee;
                            GlobalVariables.EmployeeID = thisRole.EmployeeID;
                            GlobalVariables.Client = thisRole.ClientID;
                            GlobalVariables.MaskSSN = db
                                .DarwinetSetups.First(ds =>
                                    ds.CompanyID == GlobalVariables.CompanyID
                                    && ds.ClientID == GlobalVariables.Client
                                )
                                .MaskSSN;
                            GlobalVariables.ClientName = db
                                .Clients.Where(c =>
                                    c.CompanyID == thisRole.CompanyID
                                    && c.ClientID == thisRole.ClientID
                                )
                                .Select(c => c.ClientName)
                                .FirstOrDefault();
                            GlobalVariables.UseD2 = db
                                .Clients.Where(c =>
                                    c.CompanyID == thisRole.CompanyID
                                    && c.ClientID == thisRole.ClientID
                                )
                                .Select(c => c.UseDnetPayroll)
                                .FirstOrDefault();
                            GlobalVariables.Company = db
                                .Companies.Where(co => co.CompanyID == thisRole.CompanyID)
                                .Select(co => co.DnetCompanyID)
                                .FirstOrDefault();
                        }
                        var assets = "Assets";
                        GlobalVariables.CompanyFolder = Path.Combine(
                            GlobalVariables.DNetLocation,
                            assets,
                            GlobalVariables.Customer + "." + GlobalVariables.Company
                        );
                        GlobalVariables.ClientFolder = Path.Combine(
                            GlobalVariables.CompanyFolder,
                            GlobalVariables.Client
                        );
                        GlobalVariables.ExportFolder = Path.Combine(
                            GlobalVariables.DNetLocation,
                            assets,
                            "Export",
                            "Data"
                        );
                        //GlobalVariables.ClientName = FieldTranslation.GetClientName(GlobalVariables.CompanyID, GlobalVariables.Client);
                    }
                    GlobalVariables.ClientMasterClientID = GetClientMasterClientID();
                    GlobalVariables.ClientSubClientID = string.Empty;
                    GlobalVariables.ClientSubClients = GetClientSubClients();
                    if (thisClientSetup != null && thisClientSetup.ShowClientLogo)
                    {
                        GlobalVariables.MenuLogoLarge =
                            (!string.IsNullOrEmpty(thisClientSetup.ImageURl))
                                ? GlobalVariables.DNetURL
                                    + "Assets/"
                                    + GlobalVariables.Customer
                                    + "."
                                    + GlobalVariables.Company
                                    + "/"
                                    + Folders.ClientLogos
                                    + "/"
                                    + Uri.EscapeUriString(thisClientSetup.ImageURl ?? string.Empty)
                                : string.Empty;

                        GlobalVariables.MenuLogoSmall =
                            (!string.IsNullOrEmpty(thisClientSetup.Image_Small))
                                ? GlobalVariables.DNetURL
                                    + "Assets/"
                                    + GlobalVariables.Customer
                                    + "."
                                    + GlobalVariables.Company
                                    + "/"
                                    + Folders.ClientLogos
                                    + "/"
                                    + Uri.EscapeUriString(
                                        thisClientSetup.Image_Small ?? string.Empty
                                    )
                                : string.Empty;
                    }
                    else
                    {
                        GlobalVariables.MenuLogoLarge =
                            (thisCompany != null && !string.IsNullOrEmpty(thisCompany.Image_Big))
                                ? GlobalVariables.DNetURL
                                    + "Assets/"
                                    + GlobalVariables.Customer
                                    + "."
                                    + GlobalVariables.Company
                                    + "/"
                                    + Folders.ClientLogos
                                    + "/"
                                    + Uri.EscapeUriString(thisCompany.Image_Big ?? string.Empty)
                                : string.Empty;

                        GlobalVariables.MenuLogoSmall =
                            (thisCompany != null && !string.IsNullOrEmpty(thisCompany.Image_Small))
                                ? GlobalVariables.DNetURL
                                    + "Assets/"
                                    + GlobalVariables.Customer
                                    + "."
                                    + GlobalVariables.Company
                                    + "/"
                                    + Folders.ClientLogos
                                    + "/"
                                    + Uri.EscapeUriString(thisCompany.Image_Small ?? string.Empty)
                                : string.Empty;
                    }
                    SetFooter(level, thisCompany, thisClientSetup);
                    if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
                    {
                        if (thisCompany != null)
                            GlobalVariables.ColorSchema =
                                thisUser != null && (!string.IsNullOrEmpty(thisUser.ColorScheme))
                                    ? thisUser.ColorScheme
                                    : thisCompany.DefaultColorScheme;
                    }
                    else
                    {
                        if (thisCompany != null)
                            GlobalVariables.ColorSchema =
                                thisUser != null && (!string.IsNullOrEmpty(thisUser.ColorScheme))
                                    ? thisUser.ColorScheme
                                : thisClientSetup != null
                                && (!string.IsNullOrEmpty(thisClientSetup.DefaultColorScheme))
                                    ? thisClientSetup.DefaultColorScheme
                                : thisCompany.DefaultColorScheme;
                    }

                    SetThemeCss(GlobalVariables.ColorSchema);
                    if (thisRole != null)
                        GlobalVariables.DnetRoleRecordID = thisRole.id;
                }

                GlobalVariables.OEEmployeeID = string.Empty;
                GlobalVariables.OEClientID = string.Empty;
                GlobalVariables.DnetRoleMenu = MenuAccess.GetMenu();
                GlobalVariables.AllowedWidgets = MenuAccess.GetWidgets();
                Session["ReportingInitialized"] = null;
                CreateDefaultOBDocuments(GlobalVariables.CompanyID);
                SetHelp(GlobalVariables.CompanyID);
                GlobalVariables.SecurityEmployees = FieldTranslation.GetSecurityEmployees();
                var activityStatus = CheckForSession(refreshActivity);
                if (activityStatus == "NoRec")
                {
                    CreateActivity();
                }
                if (activityStatus == "OtherUser" && !GlobalVariables.LoggedInAs)
                {
                    return RedirectToAction("ExistingSession", "Home");
                }
                APIController.CreateFolders(
                    GlobalVariables.Company,
                    GlobalVariables.Customer,
                    GlobalVariables.Client
                );

                var currentEmployee =
                    GlobalVariables.EmployeeID != null
                        ? db.Employees.FirstOrDefault(em =>
                            em.CompanyID == GlobalVariables.CompanyID
                            && em.EmployeeID == GlobalVariables.EmployeeID
                        )
                        : null;
                if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
                {
                    //check to see if there are any open enrollment plans currently open.
                    new OEController().CheckForOe(currentEmployee, 0, "");
                    return RedirectToAction("Index", "Dashboard");
                }
                else if (GlobalVariables.DNETLevel == DNetAccessLevel.Employee)
                {
                    //check to see if there are any open enrollment plans currently open.
                    new OEController().CheckForOe(currentEmployee, 0, "");
                    return RedirectToAction("EmployeeIndex", "Dashboard");
                }
                else
                {
                    return RedirectToAction("SysIndex", "Dashboard");
                }
            }
        }

        public ActionResult SetSubClientID(string subClientID)
        {
            try
            {
                GlobalVariables.ClientSubClientID = subClientID;
                return Json(new { status = 200 });
            }
            catch (Exception e)
            {
                return Json(new { status = 500, exception = e });
            }
        }

        public void SetHelp(int companyId)
        {
            using (DnetEntities db = new DnetEntities())
            {
                var company = db.Companies.FirstOrDefault(c => c.CompanyID == companyId);
                if (company != null)
                {
                    if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
                    {
                        GlobalVariables.HelpURL =
                            (!company.SystemHelpUrl.Contains("thinkwareinc.com"))
                                ? company.SystemHelpUrl
                                : null;
                    }
                    if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
                    {
                        GlobalVariables.HelpURL =
                            (!company.ClientHelpUrl.Contains("thinkwareinc.com"))
                                ? company.ClientHelpUrl
                                : null;
                    }
                    if (GlobalVariables.DNETLevel == DNetAccessLevel.Employee)
                    {
                        GlobalVariables.HelpURL =
                            (!company.EmployeeHelpUrl.Contains("thinkwareinc.com"))
                                ? company.EmployeeHelpUrl
                                : null;
                    }
                }
            }
        }

        public ActionResult ExistingSession()
        {
            ExistingSessionVM vm = new ExistingSessionVM();

            DnetEntities db = new DnetEntities();
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
            ProjectSetup projSetup = db.ProjectSetups.FirstOrDefault();
            if (projSetup != null)
            {
                GlobalVariables.DNetURL = projSetup.DnetAddress;
            }
            if (thisSetup != null)
            {
                ViewBag.LogoURL =
                    GlobalVariables.DNetURL
                    + "Assets/"
                    + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);
            }

            return View(vm);
        }

        public ActionResult AbortMe()
        {
            //Session.Clear();
            Session.Abandon();
            return RedirectToAction("Index");
        }

        public ActionResult OBNotEnabled()
        {
            return View();
        }

        public ActionResult PayrollNotEnabled()
        {
            int cnt = CurrentPayrollUsers();
            return View(cnt);
        }

        public ActionResult KillExistingSession(bool continueLoggingIn = false)
        {
            try
            {
                var model = new DnetEntities();
                var activity = model.Activities.FirstOrDefault(a =>
                    a.UserID == GlobalVariables.DNETOwnerID
                );
                if (activity != null)
                {
                    model.Activities.Remove(activity);
                    model.SaveChanges();
                    CreateActivity();
                }
                model.Dispose();
                // Log back in
                if (continueLoggingIn == true)
                {
                    return RedirectToAction("Login", "Home", new { continueLoggingIn = true });
                }

                return RedirectToAction("Index");
            }
            catch (Exception)
            {
                // Session.Clear();
                Session.Abandon();
                TempData["Error"] =
                    Messages.Errors.ErrorOccurred + " Could not terminate previous Session";
                return RedirectToAction("Index");
            }
        }

        private void CreateDefaultOBDocuments(int id)
        {
            using (DnetEntities db = new DnetEntities())
            {
                var thisRoleRec = db.UserRoleClientEmployeeAssignments.FirstOrDefault(urcea =>
                    urcea.id == id
                );

                if (thisRoleRec != null)
                {
                    db.Database.ExecuteSqlCommand(
                        "usp_OBCreateDefaultDocuments @CompanyID",
                        new SqlParameter("CompanyID", thisRoleRec.CompanyID)
                    );

                    //using (IDbCommand oaCommand = _dbContext.Database.Connection.CreateCommand())
                    //{
                    //    oaCommand.CommandType = System.Data.CommandType.StoredProcedure;
                    //    oaCommand.CommandText = "usp_OBCreateDefaultDocuments";
                    //    IDbDataParameter oaParameter = oaCommand.CreateParameter();
                    //    oaParameter.ParameterName = "@CompanyID";
                    //    oaParameter.DbType = DbType.Int32;
                    //    oaParameter.Value = thisRoleRec.CompanyID;
                    //    oaCommand.Parameters.Add(oaParameter);
                    //    oaCommand.ExecuteNonQuery();
                    //}
                    db.SaveChanges();
                }
            }
        }

        public static void ValidateClientTables(int c, string cl)
        {
            var model = new DnetEntities();
            var defaultColor = "BLUE";
            List<DarwinetSetup> newSetups = new List<DarwinetSetup>();
            if (c > 0 && !string.IsNullOrEmpty(cl))
            {
                var ds = model.DarwinetSetups.FirstOrDefault(dns =>
                    dns.CompanyID == c && dns.ClientID == cl
                );
                if (ds == null)
                {
                    var comp = model.Companies.FirstOrDefault(co => co.CompanyID == c);
                    if (comp != null)
                    {
                        defaultColor = comp.DefaultColorScheme ?? "BLUE";
                    }
                    var newDs = new DarwinetSetup
                    {
                        CompanyID = c,
                        ClientID = cl,
                        DefaultColorScheme = defaultColor,
                    };
                    newSetups.Add(newDs);
                }
            }
            else if (c == 0 && string.IsNullOrEmpty(cl))
            {
                var cls = model
                    .Clients.Select(x => new Id_Name { ID = x.CompanyID, Name = x.ClientID })
                    .ToList();
                var dss = model.DarwinetSetups.ToList();
                newSetups = cls.Where(x =>
                        !dss.Any(d => d.CompanyID == x.ID && d.ClientID == x.Name)
                    )
                    .Select(x => new DarwinetSetup
                    {
                        CompanyID = x.ID,
                        ClientID = x.Name,
                        DefaultColorScheme = defaultColor,
                    })
                    .ToList();
            }
            if (newSetups.Any())
            {
                model.DarwinetSetups.AddRange(newSetups);
                model.NoRequestSaveChanges();
            }
            // model.Dispose();
        }

        public static void SetFooter(
            string level,
            Company thisCompany,
            DarwinetSetup thisClientSetup
        )
        {
            if (level == "System")
            {
                if (thisCompany != null)
                {
                    GlobalVariables.FaceBook = thisCompany.FooterFacebookURL;
                    GlobalVariables.Twitter = thisCompany.FooterTwitterURL;
                    GlobalVariables.LinkedIn = thisCompany.FooterLinkedInURL;
                    GlobalVariables.ShowTWCopyright = thisCompany.FooterShowTWCopyright;
                    GlobalVariables.FooterPhone1 = thisCompany.FooterPhone1;
                    GlobalVariables.FooterPhone2 = thisCompany.FooterPhone2;
                    GlobalVariables.FooterFax = thisCompany.FooterFax;
                    GlobalVariables.FooterEmail = thisCompany.FooterEmailAddress;
                    GlobalVariables.Website = thisCompany.WebSite;
                }
            }
            else
            {
                if (thisClientSetup != null && thisClientSetup.CanEditFooter)
                {
                    GlobalVariables.FaceBook = thisClientSetup.FooterFacebookURL;
                    GlobalVariables.Twitter = thisClientSetup.FooterTwitterURL;
                    GlobalVariables.LinkedIn = thisClientSetup.FooterLinkedInURL;
                    GlobalVariables.ShowTWCopyright = thisClientSetup.FooterShowTWCopyright;
                    GlobalVariables.FooterPhone1 = thisClientSetup.FooterPhone1;
                    GlobalVariables.FooterPhone2 = thisClientSetup.FooterPhone2;
                    GlobalVariables.FooterFax = thisClientSetup.FooterFax;
                    GlobalVariables.FooterEmail = thisClientSetup.FooterEmailAddress;
                    GlobalVariables.FooterEmail = thisClientSetup.FooterEmailAddress;
                    if (thisCompany != null)
                    {
                        GlobalVariables.Website = thisCompany.WebSite;
                    }
                }
                else
                {
                    if (thisCompany != null)
                    {
                        GlobalVariables.FaceBook = thisCompany.FooterFacebookURL;
                        GlobalVariables.Twitter = thisCompany.FooterTwitterURL;
                        GlobalVariables.LinkedIn = thisCompany.FooterLinkedInURL;
                        GlobalVariables.ShowTWCopyright = thisCompany.FooterShowTWCopyright;
                        GlobalVariables.FooterPhone1 = thisCompany.FooterPhone1;
                        GlobalVariables.FooterPhone2 = thisCompany.FooterPhone2;
                        GlobalVariables.FooterFax = thisCompany.FooterFax;
                        GlobalVariables.FooterEmail = thisCompany.FooterEmailAddress;
                        GlobalVariables.Website = thisCompany.WebSite;
                    }
                }
            }
        }

        [HttpPost]
        public ActionResult Dyn(FormCollection collection)
        {
            return RedirectToAction("PayCodeIndex", "OBProcess");
        }

        public static void SetThemeCss(string theme)
        {
            if (theme == null)
            {
                theme = "BLUE";
            }
            if (theme.ToUpper() == "RED")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-red.min.css";
                GlobalVariables.Css2 = "~/Content/themes/thinkware.red.min.css";
            }
            else if (theme.ToUpper() == "BLUE")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-default.min.css";
                GlobalVariables.Css2 = "~/Content/themes/thinkware.default.min.css";
            }
            else if (theme.ToUpper() == "GREEN")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-green.min.css";
                GlobalVariables.Css2 = "~/Content/themes/thinkware.green.min.css";
            }
            else if (theme.ToUpper() == "BLACK")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-black.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.black.min.css";
            }
            else if (theme.ToUpper() == "BLUEOPAL")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-blueopal.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.blueopal.min.css";
            }
            else if (theme.ToUpper() == "BOOTSTRAP")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-bootstrap.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.bootstrap.min.css";
            }
            else if (theme.ToUpper() == "FIORI")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-fiori.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.fiori.min.css";
            }
            else if (theme.ToUpper() == "FLAT")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-flat.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.flat.min.css";
            }
            else if (theme.ToUpper() == "HIGHCONTRAST")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-highcontrast.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.highcontrast.min.css";
            }
            else if (theme.ToUpper() == "MATERIAL")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-material.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.material.min.css";
            }
            else if (theme.ToUpper() == "MATERIALBLACK")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-materialblack.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.materialblack.min.css";
            }
            else if (theme.ToUpper() == "METRO")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-metro.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.metro.min.css";
            }
            else if (theme.ToUpper() == "METROBLACK")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-metroblack.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.metroblack.min.css";
            }
            else if (theme.ToUpper() == "MOONLIGHT")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-moonlight.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.moonlight.min.css";
            }
            else if (theme.ToUpper() == "UNIFORM")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-uniform.min.css";
                GlobalVariables.Css2 = "~/Content/themes/kendo.uniform.min.css";
            }
            else if (theme.ToUpper() == "MOUNTAIN")
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-mountain.min.css";
                GlobalVariables.Css2 = "~/Content/themes/thinkware.mountain.css";
            }
            else
            {
                GlobalVariables.Css1 = "~/Content/scss/css/thinkware-silver.min.css";
                GlobalVariables.Css2 = "~/Content/themes/thinkware.silver.min.css";
            }
        }

        public ActionResult SetSystem()
        {
            GlobalVariables.DNETLevel = DNetAccessLevel.System;
            return View("Index");
        }

        public ActionResult SetClient()
        {
            GlobalVariables.DNETLevel = DNetAccessLevel.Client;
            return View("Index");
        }

        public ActionResult SetEntry()
        {
            GlobalVariables.AppTest += "Set Entry Get";
            return View();
        }

        public ActionResult SessionExpired(string destinationUrl = null)
        {
            KillExistingSession();
            Session.Clear();
            Session.Abandon();
            TempData["Error"] = "Your session has expired";
            return RedirectToAction("Index", new { destinationUrl = destinationUrl });
        }

        public ActionResult SessionRemoved()
        {
            KillExistingSession();
            Session.Clear();
            Session.Abandon();
            return View();
        }

        public ActionResult SessionDisabled()
        {
            KillExistingSession();
            Session.Clear();
            Session.Abandon();
            return View();
        }

        public ActionResult LogOut()
        {
            // Session.Clear();
            KillExistingSession();
            Session.Abandon();

            return RedirectToAction("Index", "Home");
        }

        public ActionResult SSO(string ssotoken)
        {
            SSOVM vm = new SSOVM();

            vm.SSOToken = ssotoken;

            return View(vm);
        }

        [HttpPost]
        public ActionResult SSO(SSOVM vm)
        {
            IndexVM indexVM = new IndexVM();

            indexVM.SSOToken = vm.SSOToken;

            return RedirectToAction("Login", indexVM);
        }

        public string CheckForSession(bool refresh)
        {
            var model = new DnetEntities();
            var activity = model.Activities.FirstOrDefault(a =>
                a.UserID == GlobalVariables.DNETOwnerID
            );
            // model.Dispose();
            if (activity == null)
                return "NoRec";
            if (activity.UserIP != GlobalVariables.OwnerIP)
                return "OtherUser";
            if (refresh)
            {
                activity.CompanyID = GlobalVariables.CompanyID;
                activity.ClientID = GlobalVariables.Client;
                activity.DateCreated = DateTime.Now;
                model.SaveChanges();
            }
            return "Same";
        }

        public void CreateActivity()
        {
            if (!GlobalVariables.LoggedInAs)
            {
                var model = new DnetEntities();
                var activity = new Activity
                {
                    UserID = GlobalVariables.DNETOwnerID,
                    UserIP = GlobalVariables.OwnerIP,
                    DateCreated = DateTime.Now,
                    CompanyID = GlobalVariables.CompanyID,
                    ClientID =
                        (GlobalVariables.Client == null) ? string.Empty : GlobalVariables.Client,
                };
                model.Activities.Add(activity);
                model.SaveChanges();
                // model.Dispose();
            }
        }

        public ActionResult CreateCon()
        {
            return View();
        }

        [HttpPost]
        public ActionResult CreateCon(FormCollection form)
        {
            string server = form["serv"];
            string user = form["user"];
            string pwd = form["pwd"];
            string db = form["dbname"];
            string con = ConCryptor.Encrypt(
                "Server=" + server + "; Database=" + db + "; UID=" + user + "; PWD=" + pwd
            );
            string dnetLocation = AppDomain.CurrentDomain.BaseDirectory;
            string file = Path.Combine(dnetLocation, "globalcon.cfg");
            bool exists = System.IO.File.Exists(file);
            if (exists)
            {
                System.IO.File.Delete(file);
            }
            using (StreamWriter sw = System.IO.File.CreateText(file))
            {
                sw.WriteLine(con);
            }
            return RedirectToAction("Index");
        }

        // ReSharper disable once InconsistentNaming
        public ActionResult SSRSRV()
        {
            return Redirect("../SSRS/ReportViewer.aspx");
        }

        public ActionResult RecentlyVisited()
        {
            var model = new DnetEntities();
            var activity = model
                .ActivityLogs.Where(al =>
                    al.UserID == GlobalVariables.DNETOwnerID
                    && al.CompanyID == GlobalVariables.CompanyID
                    && al.ClientID == GlobalVariables.Client
                    && al.EmployeeID == GlobalVariables.EmployeeID
                )
                .OrderByDescending(al => al.DateTimeVisited)
                .ToList();
            if (activity.Count > 5)
            {
                activity = activity.Take(5).ToList();
            }
            // model.Dispose();
            return View(activity);
        }

        #region Reset Password
        public ActionResult ForgotPassword()
        {
            DnetEntities db = new DnetEntities();
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
            ProjectSetup projSetup = db.ProjectSetups.FirstOrDefault();
            ForgotPasswordVM view = new ForgotPasswordVM();
            if (projSetup != null)
            {
                GlobalVariables.DNetURL = projSetup.DnetAddress;
            }
            if (thisSetup != null)
            {
                view.WelcomeTitle = thisSetup.WelcomeTitle;
                view.Message = thisSetup.WelcomePageText;
                view.LogoUrl =
                    GlobalVariables.DNetURL
                    + "Assets/"
                    + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);
            }
            return View(view);
        }

        [HttpPost]
        public ActionResult ForgotPassword(ForgotPasswordVM form)
        {
            DnetEntities db = new DnetEntities();
            string userName = string.Empty;
            string userId = form.UserID;
            int compId = GlobalVariables.CompanyID;
            User user = null;
            OBUser obUser = null;
            PasswordToken passwordToken;
            DateTime ValidUntil = DateTime.Now;
            ProjectSetup projectSetup = db.ProjectSetups.FirstOrDefault();
            if (projectSetup != null)
            {
                try
                {
                    if (string.IsNullOrEmpty(GlobalVariables.DNetURL))
                    {
                        GlobalVariables.DNetURL = projectSetup.DnetAddress;
                    }

                    passwordToken = new PasswordToken(form.UserID);
                    ValidUntil = ValidUntil.AddMinutes(30);

                    user = db
                        .Users.Where(usr => usr.Enabled)
                        .ToList()
                        .FirstOrDefault(s =>
                            String.Equals(
                                s.UserID,
                                form.UserID,
                                StringComparison.CurrentCultureIgnoreCase
                            )
                            && String.Equals(
                                s.Email,
                                form.Email,
                                StringComparison.CurrentCultureIgnoreCase
                            )
                        );
                    if (user != null)
                    {
                        userId = user.UserID;
                        userName = user.Name;
                        user.ResetCode = passwordToken.Code;
                    }
                    else
                    {
                        obUser = db.OBUsers.FirstOrDefault(tusr =>
                            tusr.UserID == form.UserID && tusr.Email == form.Email
                        );
                        if (obUser != null)
                        {
                            userId = obUser.UserID;
                            userName = obUser.FirstName + " " + obUser.LastName;
                            obUser.ResetCode = passwordToken.Code;
                        }
                    }
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    TempData["Error"] = string.Format(
                        "An error occurred trying to create token. Please try again. ({0})",
                        ex.Message
                    );
                    return View(form);
                }
            }
            else
            {
                TempData["Error"] = "Project Setup has not yet been completed";
                return View(form);
            }

            if (user != null || obUser != null)
            {
                if (projectSetup.UseCustomMail && !string.IsNullOrEmpty(projectSetup.DnetMailHost))
                {
                    GlobalVariables.MailHost = projectSetup.CustMailHost.Trim();
                    GlobalVariables.MailPort = projectSetup.CustMailPort;
                    GlobalVariables.PEOMailFrom =
                        (string.IsNullOrEmpty(projectSetup.CustMailFromName))
                            ? string.Empty
                            : projectSetup.CustMailFromName.Trim();
                    GlobalVariables.PEOMailFromAddress =
                        (string.IsNullOrEmpty(projectSetup.CustMailFromAddress))
                            ? string.Empty
                            : projectSetup.CustMailFromAddress.Trim();
                    GlobalVariables.MailHostUser =
                        (string.IsNullOrEmpty(projectSetup.CustHostLogin))
                            ? string.Empty
                            : projectSetup.CustHostLogin;
                    GlobalVariables.MailHostPWD =
                        (string.IsNullOrEmpty(projectSetup.CustHostPWD))
                            ? string.Empty
                            : projectSetup.CustHostPWD;
                    GlobalVariables.MailSSL = false;
                }
                else
                {
                    GlobalVariables.MailHost =
                        (string.IsNullOrEmpty(projectSetup.DnetMailHost))
                            ? string.Empty
                            : projectSetup.DnetMailHost.Trim();
                    GlobalVariables.MailPort = string.Empty;
                    GlobalVariables.PEOMailFrom =
                        (string.IsNullOrEmpty(projectSetup.DnetMailFromName))
                            ? string.Empty
                            : projectSetup.DnetMailFromName.Trim();
                    GlobalVariables.PEOMailFromAddress =
                        (string.IsNullOrEmpty(projectSetup.DnetMailFromAddress))
                            ? string.Empty
                            : projectSetup.DnetMailFromAddress.Trim();
                    GlobalVariables.MailHostUser =
                        (string.IsNullOrEmpty(projectSetup.DnetMailUser))
                            ? string.Empty
                            : projectSetup.DnetMailUser.Trim();
                    GlobalVariables.MailHostPWD =
                        (string.IsNullOrEmpty(projectSetup.DnetMailPassword))
                            ? string.Empty
                            : projectSetup.DnetMailPassword.Trim();
                    GlobalVariables.MailSSL = projectSetup.DnetMailSSL;
                }

                List<int> userCompanies = db
                    .UserRoleClientEmployeeAssignments.Where(u => u.UserID == userId)
                    .OrderBy(u => u.CompanyID)
                    .Select(u => u.CompanyID)
                    .Distinct()
                    .ToList();
                if (obUser != null)
                {
                    compId = db
                        .Companies.FirstOrDefault(c => c.DnetCompanyID == obUser.Company)
                        .CompanyID;
                }
                else if (compId <= 0)
                    compId = userCompanies.First();
                else
                {
                    if (userCompanies.Where(uc => uc == compId).Count() <= 0)
                        compId = userCompanies.First();
                }
                Company company = db.Companies.FirstOrDefault(c => c.CompanyID == compId);
                GlobalVariables.CompanyID = company.CompanyID;
                GlobalVariables.Company = company.DnetCompanyID;
                GlobalVariables.Customer = company.DnetClientID;
                GlobalVariables.PEOName = company.CompanyName;
                GlobalVariables.PEOMail =
                    (string.IsNullOrEmpty(company.Email)) ? string.Empty : company.Email.Trim();
                GlobalVariables.PEOPhone =
                    (string.IsNullOrEmpty(company.Phone1))
                        ? string.Empty
                        : FieldTranslation.FormatPhone(company.Phone1);
                GlobalVariables.EIN =
                    (string.IsNullOrEmpty(company.CompanyEIN)) ? string.Empty : company.CompanyEIN; // 02/12/2018 DS TFS # 3008

                string nBody = string.Format(
                    Constants.ForgetPasswordEmailBody,
                    userName,
                    GlobalVariables.DNetURL,
                    passwordToken.EncryptedToken
                );

                string result = string.Empty;
                if (!string.IsNullOrEmpty(form.Email))
                {
                    SmtpEmail.SendSmtpEmail(
                        form.Email,
                        userName,
                        "Password Reset Request",
                        nBody,
                        null,
                        false,
                        null,
                        null,
                        out result
                    );
                    /* var queue = new Queue();
                     if (queue.AddQueue("Password Reset Request", nBody, form.Email, userName, null, null, null, null,
                         null))
                     {
                         result = string.Empty;
                     }
                     else
                     {
                         result = "Error";
                     }*/
                    if (result != string.Empty)
                        TempData["Error"] = "Error occurred Sending Email : " + result;
                    else
                    {
                        form.EmailSent = true;
                        TempData["Success"] =
                            "An email has been sent to the address you provided with further instructions on how to reset your password. The reset code included in the email will expire in 30 minutes.";
                    }
                }
                else
                    TempData["Error"] =
                        "Cannot Send Email : the user '"
                        + userName
                        + "' does not have email address.";
            }
            else
            {
                TempData["Error"] =
                    "Based on the information entered we are unable to find a user.";
            }

            return View(form);
        }

        public ActionResult ResetPassword(string id)
        {
            if (!string.IsNullOrEmpty(id))
            {
                return RedirectToAction("Confirm", "Home", new { id = id });
            }
            else
            {
                TempData["Error"] = "The verification token is empty. Please request a new token";
                return RedirectToAction("ForgotPassword", "Home");
                //                return RedirectToAction("Index");
            }
        }

        public ActionResult Confirm(string id)
        {
            DnetEntities db = new DnetEntities();
            SecurityAnswerVM view = new SecurityAnswerVM();

            PasswordToken passwordToken = new PasswordToken();
            passwordToken.DecryptPasswordToken(id);

            if (passwordToken.ValidateToken(db))
            {
                IEnumerable<SecurityAnswer> securityAnswers = db.SecurityAnswers.Where(x =>
                    x.UserID == passwordToken.UserID
                );
                if (securityAnswers.Any())
                {
                    int rnd = new Random().Next(1, 3);
                    int i = 0;
                    foreach (SecurityAnswer answer in securityAnswers)
                    {
                        if (i++ == rnd)
                        {
                            view.QuestionText = db
                                .SecurityQuestions.Where(x => x.QuestionID == answer.QuestionID)
                                .FirstOrDefault()
                                .Question;
                            view.AnswerID = answer.AnswerID;
                            view.Token = id;
                        }
                    }
                }
                else
                {
                    return RedirectToAction("ChangePassword", "Home", new { id = id });
                }
            }
            else
            {
                TempData["Error"] =
                    "The verification token you provided is no longer valid. Please request a new token";
                return RedirectToAction("ForgotPassword", "Home");
            }
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
            ProjectSetup projSetup = db.ProjectSetups.FirstOrDefault();
            if (projSetup != null)
            {
                GlobalVariables.DNetURL = projSetup.DnetAddress;
            }
            if (thisSetup != null)
            {
                ViewBag.LogoUrl =
                    GlobalVariables.DNetURL
                    + "Assets/"
                    + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);
            }
            return View(view);
        }

        [HttpPost]
        public ActionResult Confirm(SecurityAnswerVM reply)
        {
            DnetEntities db = new DnetEntities();
            PasswordToken passwordToken = new PasswordToken();
            passwordToken.DecryptPasswordToken(reply.Token);

            if (passwordToken.ValidateToken(db))
            {
                SecurityAnswer answer = db
                    .SecurityAnswers.Where(x => x.AnswerID == reply.AnswerID)
                    .FirstOrDefault();
                string answerText = ConCryptor.Decrypt(answer.AnswerText);

                if (answerText == reply.AnswerText)
                {
                    return RedirectToAction("ChangePassword", "Home", new { id = reply.Token });
                }
                passwordToken.ClearToken(db);
            }

            TempData["Error"] = "We were unable to verify your security answer.";
            return RedirectToAction("Index", "Home");
        }

        public ActionResult ChangePassword(string id)
        {
            DnetEntities db = new DnetEntities();
            PasswordToken passwordToken = new PasswordToken();
            passwordToken.DecryptPasswordToken(id);
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
            ProjectSetup projSetup = db.ProjectSetups.FirstOrDefault();
            if (projSetup != null)
            {
                GlobalVariables.DNetURL = projSetup.DnetAddress;
            }
            if (thisSetup != null)
            {
                ViewBag.LogoURL =
                    GlobalVariables.DNetURL
                    + "Assets/"
                    + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);
            }
            if (passwordToken.ValidateToken(new DnetEntities()))
            {
                ChangePasswordVM view = new ChangePasswordVM();
                view.Token = id;
                view.UserID = passwordToken.UserID;

                return View(view);
            }
            else
            {
                return RedirectToAction("Index");
            }
        }

        [HttpPost]
        public ActionResult ChangePassword(ChangePasswordVM form)
        {
            if (ModelState.IsValid)
            {
                PasswordToken passwordToken = new PasswordToken();
                passwordToken.DecryptPasswordToken(form.Token);

                DnetEntities db = new DnetEntities();
                if (passwordToken.ValidateToken(db))
                {
                    passwordToken.ClearToken(db);

                    User user = null;
                    OBUser obUser = null;
                    string userName = string.Empty,
                        userEmail = string.Empty;
                    string userId = form.UserID;
                    WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
                    if (thisSetup != null)
                    {
                        ViewBag.LogoURL =
                            GlobalVariables.DNetURL
                            + "Assets/"
                            + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);
                    }
                    ProjectSetup projectSetup = db.ProjectSetups.FirstOrDefault();
                    if (projectSetup != null)
                    {
                        user = db.Users.Where(x => x.UserID == userId).FirstOrDefault();
                        if (user != null)
                        {
                            user.Password = ConCryptor.Encrypt(form.Password);
                            user.PasswordChanged = DateTime.Now;
                            user.PasswordSetupToken = null;
                            userName = user.Name;
                            userEmail = user.Email;

                            //Added by Tecnics on 28APR2021 for tkt: 978
                            if (string.IsNullOrEmpty(GlobalVariables.DNETOwnerID))
                                GlobalVariables.DNETOwnerID = userId;
                            var existingrecord = db.LoginUsersLockDetails.FirstOrDefault(elu =>
                                elu.UserID == GlobalVariables.DNETOwnerID
                            );
                            if (existingrecord != null)
                            {
                                existingrecord.LoginTime = DateTime.Now;
                                existingrecord.LoginAttempts = 0;
                                existingrecord.AccountLockExpirationDate = null;
                                existingrecord.IsAccountLocked = 0;
                                existingrecord.LastModifiedBy = GlobalVariables.DNETOwnerID;
                                existingrecord.LastModifiedDate = Convert.ToString(DateTime.Now);
                            }
                            //End

                            TempData["Success"] = Messages.Success.PasswordChanged;
                        }
                        else
                        {
                            obUser = db.OBUsers.Where(x => x.UserID == userId).FirstOrDefault();
                            if (obUser != null)
                            {
                                obUser.Password = ConCryptor.Encrypt(form.Password);
                                userName = obUser.FirstName + " " + obUser.LastName;
                                userEmail = obUser.Email;
                                TempData["Success"] = Messages.Success.PasswordChanged;
                            }
                        }
                        db.SaveChanges();
                    }
                    else
                    {
                        TempData["Error"] = "Project Setup has not yet been completed";
                        return View(form);
                    }

                    if (user != null || obUser != null)
                    {
                        if (string.IsNullOrEmpty(GlobalVariables.DNetURL))
                            GlobalVariables.DNetURL = projectSetup.DnetAddress;

                        if (string.IsNullOrEmpty(GlobalVariables.DNETOwnerID))
                            GlobalVariables.DNETOwnerID = userId;

                        if (
                            projectSetup.UseCustomMail
                            && !string.IsNullOrEmpty(projectSetup.DnetMailHost)
                        )
                        {
                            GlobalVariables.MailHost = projectSetup.CustMailHost.Trim();
                            GlobalVariables.MailPort = projectSetup.CustMailPort;
                            GlobalVariables.PEOMailFrom =
                                (string.IsNullOrEmpty(projectSetup.CustMailFromName))
                                    ? string.Empty
                                    : projectSetup.CustMailFromName.Trim();
                            GlobalVariables.PEOMailFromAddress =
                                (string.IsNullOrEmpty(projectSetup.CustMailFromAddress))
                                    ? string.Empty
                                    : projectSetup.CustMailFromAddress.Trim();
                            GlobalVariables.MailHostUser =
                                (string.IsNullOrEmpty(projectSetup.CustHostLogin))
                                    ? string.Empty
                                    : projectSetup.CustHostLogin;
                            GlobalVariables.MailHostPWD =
                                (string.IsNullOrEmpty(projectSetup.CustHostPWD))
                                    ? string.Empty
                                    : projectSetup.CustHostPWD;
                            GlobalVariables.MailSSL = false;
                        }
                        else
                        {
                            GlobalVariables.MailHost =
                                (string.IsNullOrEmpty(projectSetup.DnetMailHost))
                                    ? string.Empty
                                    : projectSetup.DnetMailHost.Trim();
                            GlobalVariables.MailPort = string.Empty;
                            GlobalVariables.PEOMailFrom =
                                (string.IsNullOrEmpty(projectSetup.DnetMailFromName))
                                    ? string.Empty
                                    : projectSetup.DnetMailFromName.Trim();
                            GlobalVariables.PEOMailFromAddress =
                                (string.IsNullOrEmpty(projectSetup.DnetMailFromAddress))
                                    ? string.Empty
                                    : projectSetup.DnetMailFromAddress.Trim();
                            GlobalVariables.MailHostUser =
                                (string.IsNullOrEmpty(projectSetup.DnetMailUser))
                                    ? string.Empty
                                    : projectSetup.DnetMailUser.Trim();
                            GlobalVariables.MailHostPWD =
                                (string.IsNullOrEmpty(projectSetup.DnetMailPassword))
                                    ? string.Empty
                                    : projectSetup.DnetMailPassword.Trim();
                            GlobalVariables.MailSSL = projectSetup.DnetMailSSL;
                        }
                        int compId = GlobalVariables.CompanyID;
                        string dncomp = GlobalVariables.Company;
                        string cust = GlobalVariables.Customer;
                        string peoname = GlobalVariables.PEOName;
                        string peomail = GlobalVariables.PEOMail;
                        string peophone = GlobalVariables.PEOPhone;
                        string ein = GlobalVariables.EIN;
                        if (
                            projectSetup.UseCustomMail
                            && !string.IsNullOrEmpty(projectSetup.DnetMailHost)
                        )
                        {
                            GlobalVariables.MailHost = projectSetup.CustMailHost.Trim();
                            GlobalVariables.MailPort = projectSetup.CustMailPort;
                            GlobalVariables.PEOMailFrom =
                                (string.IsNullOrEmpty(projectSetup.CustMailFromName))
                                    ? string.Empty
                                    : projectSetup.CustMailFromName.Trim();
                            GlobalVariables.PEOMailFromAddress =
                                (string.IsNullOrEmpty(projectSetup.CustMailFromAddress))
                                    ? string.Empty
                                    : projectSetup.CustMailFromAddress.Trim();
                            GlobalVariables.MailHostUser =
                                (string.IsNullOrEmpty(projectSetup.CustHostLogin))
                                    ? string.Empty
                                    : projectSetup.CustHostLogin;
                            GlobalVariables.MailHostPWD =
                                (string.IsNullOrEmpty(projectSetup.CustHostPWD))
                                    ? string.Empty
                                    : projectSetup.CustHostPWD;
                            GlobalVariables.MailSSL = false;
                        }
                        else
                        {
                            GlobalVariables.MailHost =
                                (string.IsNullOrEmpty(projectSetup.DnetMailHost))
                                    ? string.Empty
                                    : projectSetup.DnetMailHost.Trim();
                            GlobalVariables.MailPort = string.Empty;
                            GlobalVariables.PEOMailFrom =
                                (string.IsNullOrEmpty(projectSetup.DnetMailFromName))
                                    ? string.Empty
                                    : projectSetup.DnetMailFromName.Trim();
                            GlobalVariables.PEOMailFromAddress =
                                (string.IsNullOrEmpty(projectSetup.DnetMailFromAddress))
                                    ? string.Empty
                                    : projectSetup.DnetMailFromAddress.Trim();
                            GlobalVariables.MailHostUser =
                                (string.IsNullOrEmpty(projectSetup.DnetMailUser))
                                    ? string.Empty
                                    : projectSetup.DnetMailUser.Trim();
                            GlobalVariables.MailHostPWD =
                                (string.IsNullOrEmpty(projectSetup.DnetMailPassword))
                                    ? string.Empty
                                    : projectSetup.DnetMailPassword.Trim();
                            GlobalVariables.MailSSL = projectSetup.DnetMailSSL;
                        }
                        List<int> userCompanies = db
                            .UserRoleClientEmployeeAssignments.Where(u => u.UserID == userId)
                            .OrderBy(u => u.CompanyID)
                            .Select(u => u.CompanyID)
                            .Distinct()
                            .ToList();
                        if (obUser != null)
                        {
                            compId = db
                                .Companies.FirstOrDefault(c => c.DnetCompanyID == obUser.Company)
                                .CompanyID;
                        }
                        else if (compId <= 0)
                            compId = userCompanies.First();
                        else
                        {
                            if (userCompanies.Where(uc => uc == compId).Count() <= 0)
                                compId = userCompanies.First();
                        }
                        bool changecompany = compId != GlobalVariables.CompanyID;
                        if (changecompany)
                        {
                            Company company = db.Companies.FirstOrDefault(c =>
                                c.CompanyID == compId
                            );
                            GlobalVariables.CompanyID = company.CompanyID;
                            GlobalVariables.Company = company.DnetCompanyID;
                            GlobalVariables.Customer = company.DnetClientID;
                            GlobalVariables.PEOName = company.CompanyName;
                            GlobalVariables.PEOMail =
                                (string.IsNullOrEmpty(company.Email))
                                    ? string.Empty
                                    : company.Email.Trim();
                            GlobalVariables.PEOPhone =
                                (string.IsNullOrEmpty(company.Phone1))
                                    ? string.Empty
                                    : FieldTranslation.FormatPhone(company.Phone1);
                            GlobalVariables.EIN =
                                (string.IsNullOrEmpty(company.CompanyEIN))
                                    ? string.Empty
                                    : company.CompanyEIN; // 02/12/2018 DS TFS # 3008
                        }

                        var email = new Notifications();
                        email.Initialize();
                        bool notificationEnabled = email.NotificationInfo(
                            NotificationType.PasswordChange
                        );
                        if (notificationEnabled)
                        {
                            string result;
                            if (!string.IsNullOrEmpty(userEmail))
                            {
                                SmtpEmail.SendSmtpEmail(
                                    userEmail,
                                    userName,
                                    email.Subject,
                                    email.Body,
                                    null,
                                    false,
                                    NotificationType.PasswordChange,
                                    null,
                                    out result
                                );
                            }
                        }

                        if (changecompany)
                        {
                            GlobalVariables.CompanyID = compId;
                            GlobalVariables.Company = dncomp;
                            GlobalVariables.Customer = cust;
                            GlobalVariables.PEOName = peoname;
                            GlobalVariables.PEOMail = peomail;
                            GlobalVariables.PEOPhone = peophone;
                            GlobalVariables.EIN = ein; // 02/12/2018 DS TFS # 3008
                        }
                        return RedirectToAction("Index", "Home");
                    }
                    else
                    {
                        TempData["Error"] =
                            "Could not find User Record to update with new password.";
                        return RedirectToAction("Index", "Home");
                    }
                }
                else
                {
                    TempData["Error"] =
                        "The verification token you provided is no longer valid. Please request a new token";
                    return RedirectToAction("ForgotPassword", "Home");
                }
            }
            else
            {
                return View(form);
            }
        }
        #endregion

        public ActionResult ForgotUser()
        {
            DnetEntities db = new DnetEntities();
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
            ProjectSetup projSetup = db.ProjectSetups.FirstOrDefault();
            if (projSetup != null)
            {
                GlobalVariables.DNetURL = projSetup.DnetAddress;
            }
            if (thisSetup != null)
            {
                ViewBag.WelcomeTitle = thisSetup.WelcomeTitle;
                ViewBag.Message = thisSetup.WelcomePageText;
                ViewBag.LogoURL =
                    GlobalVariables.DNetURL
                    + "Assets/"
                    + Uri.EscapeUriString(thisSetup.WelcomeLogo ?? string.Empty);
            }
            return View();
        }

        [HttpPost]
        public ActionResult ForgotUser(FormCollection form)
        {
            DnetEntities db = new DnetEntities();
            string logo = string.Empty;
            User thisUser = null;
            WelcomePageSetup thisSetup = db.WelcomePageSetups.FirstOrDefault();
            if (thisSetup != null)
            {
                ViewBag.WelcomeTitle = thisSetup.WelcomeTitle;
                ViewBag.Message = thisSetup.WelcomePageText;
                logo = thisSetup.WelcomeLogo;
            }
            var name = form["username"];
            var email = form["email"];
            string username = string.Empty;
            bool userFound = false;
            bool sendMail = false;
            bool isOBUser = false; // 08/03/2017 DS TFS # 2706
            short err = 0;
            //get list of users from DB
            int compId = GlobalVariables.CompanyID;
            string dncomp = GlobalVariables.Company;
            string cust = GlobalVariables.Customer;
            string peoname = GlobalVariables.PEOName;
            string peomail = GlobalVariables.PEOMail;
            string peophone = GlobalVariables.PEOPhone;
            string ein = GlobalVariables.EIN; // 02/12/2018 DS TFS # 3008

            var ProjSetup = db.ProjectSetups.FirstOrDefault();
            if (ProjSetup == null)
            {
                TempData["Error"] = "Project Setup has not yet been completed";
            }
            else
            {
                if (!string.IsNullOrEmpty(email))
                {
                    if (string.IsNullOrEmpty(GlobalVariables.DNetURL))
                        GlobalVariables.DNetURL = ProjSetup.DnetAddress;
                    ViewBag.LogoURL =
                        GlobalVariables.DNetURL
                        + "Assets/"
                        + Uri.EscapeUriString(logo ?? string.Empty);
                    username = RegUserID(ref name, email, out err);
                    if (err == 1)
                    {
                        username = OBUserID(ref name, email, out err);
                        isOBUser = (err == 0); // 08/03/2017 DS TFS # 2706
                    }
                    if (err == 0)
                    {
                        string result;
                        if (
                            ProjSetup.UseCustomMail && !string.IsNullOrEmpty(ProjSetup.DnetMailHost)
                        )
                        {
                            GlobalVariables.MailHost = ProjSetup.CustMailHost.Trim();
                            GlobalVariables.MailPort = ProjSetup.CustMailPort;
                            GlobalVariables.PEOMailFrom =
                                (string.IsNullOrEmpty(ProjSetup.CustMailFromName))
                                    ? string.Empty
                                    : ProjSetup.CustMailFromName.Trim();
                            GlobalVariables.PEOMailFromAddress =
                                (string.IsNullOrEmpty(ProjSetup.CustMailFromAddress))
                                    ? string.Empty
                                    : ProjSetup.CustMailFromAddress.Trim();
                            GlobalVariables.MailHostUser =
                                (string.IsNullOrEmpty(ProjSetup.CustHostLogin))
                                    ? string.Empty
                                    : ProjSetup.CustHostLogin;
                            GlobalVariables.MailHostPWD =
                                (string.IsNullOrEmpty(ProjSetup.CustHostPWD))
                                    ? string.Empty
                                    : ProjSetup.CustHostPWD;
                            GlobalVariables.MailSSL = false;
                        }
                        else
                        {
                            GlobalVariables.MailHost =
                                (string.IsNullOrEmpty(ProjSetup.DnetMailHost))
                                    ? string.Empty
                                    : ProjSetup.DnetMailHost.Trim();
                            GlobalVariables.MailPort = string.Empty;
                            GlobalVariables.PEOMailFrom =
                                (string.IsNullOrEmpty(ProjSetup.DnetMailFromName))
                                    ? string.Empty
                                    : ProjSetup.DnetMailFromName.Trim();
                            GlobalVariables.PEOMailFromAddress =
                                (string.IsNullOrEmpty(ProjSetup.DnetMailFromAddress))
                                    ? string.Empty
                                    : ProjSetup.DnetMailFromAddress.Trim();
                            GlobalVariables.MailHostUser =
                                (string.IsNullOrEmpty(ProjSetup.DnetMailUser))
                                    ? string.Empty
                                    : ProjSetup.DnetMailUser.Trim();
                            GlobalVariables.MailHostPWD =
                                (string.IsNullOrEmpty(ProjSetup.DnetMailPassword))
                                    ? string.Empty
                                    : ProjSetup.DnetMailPassword.Trim();
                            GlobalVariables.MailSSL = ProjSetup.DnetMailSSL;
                        }
                        if (isOBUser) // 08/03/2017 DS Start TFS # 2706
                        {
                            OBUser user = db.OBUsers.FirstOrDefault(u => u.UserID == username);
                            if (user != null)
                            {
                                if (user.CompanyID != null)
                                    compId = user.CompanyID ?? 0;
                                else
                                {
                                    Company company = db.Companies.FirstOrDefault(c =>
                                        c.DnetClientID == user.Customer
                                        && c.DnetCompanyID == user.Company
                                    );
                                    if (company != null)
                                        compId = company.CompanyID;
                                }
                            }
                        }
                        else // 08/03/2017 DS End TFS # 2706
                        {
                            List<int> userCompanies = db
                                .UserRoleClientEmployeeAssignments.Where(u => u.UserID == username)
                                .OrderBy(u => u.CompanyID)
                                .Select(u => u.CompanyID)
                                .Distinct()
                                .ToList();
                            if (compId <= 0)
                                compId = userCompanies.First();
                            else
                            {
                                if (userCompanies.Where(uc => uc == compId).Count() <= 0)
                                    compId = userCompanies.First();
                            }
                        }
                        bool changecompany = compId != GlobalVariables.CompanyID;
                        if (changecompany)
                        {
                            Company company = db.Companies.FirstOrDefault(c =>
                                c.CompanyID == compId
                            );
                            GlobalVariables.CompanyID = company.CompanyID;
                            GlobalVariables.Company = company.DnetCompanyID;
                            GlobalVariables.Customer = company.DnetClientID;
                            GlobalVariables.PEOName = company.CompanyName;
                            GlobalVariables.PEOMail =
                                (string.IsNullOrEmpty(company.Email))
                                    ? string.Empty
                                    : company.Email.Trim();
                            GlobalVariables.PEOPhone =
                                (string.IsNullOrEmpty(company.Phone1))
                                    ? string.Empty
                                    : FieldTranslation.FormatPhone(company.Phone1);
                            GlobalVariables.EIN =
                                (string.IsNullOrEmpty(company.CompanyEIN))
                                    ? string.Empty
                                    : company.CompanyEIN; // 02/12/2018 DS TFS # 3008
                        }

                        var nBody = string.Format(Constants.ForgotUserIdBody, name, username);

                        if (
                            SmtpEmail.SendSmtpEmail(
                                email,
                                name,
                                "Forgot User ID Request",
                                nBody,
                                null,
                                false,
                                null,
                                null,
                                out result
                            )
                        )
                            result = string.Empty;
                        if (result != string.Empty)
                            TempData["Error"] = "Error occurred Sending Email : " + result;
                        TempData["Success"] = "An email has been sent your email address.";
                        if (changecompany)
                        {
                            GlobalVariables.CompanyID = compId;
                            GlobalVariables.Company = dncomp;
                            GlobalVariables.Customer = cust;
                            GlobalVariables.PEOName = peoname;
                            GlobalVariables.PEOMail = peomail;
                            GlobalVariables.PEOPhone = peophone;
                            GlobalVariables.EIN = ein; // 02/12/2018 DS TFS # 3008
                        }
                    }
                    else
                    {
                        TempData["Error"] =
                            (err == 1)
                                ? "No Active user records could be found using the information you supplied."
                                : "Your request has returned more than one record. Please contact your system administrator.";
                    }
                }
                else
                    TempData["Error"] = "Cannot find recipient email address";
            }
            return RedirectToAction("Index", "Home");
        }

        public ActionResult TWOptiMods()
        {
            return View();
        }

        private string RegUserID(ref string name, string email, out short err)
        {
            string result = string.Empty;
            string uname = name;
            bool userFound = false;
            err = 0;
            DnetEntities db = new DnetEntities();
            try
            {
                var theUsers = db.Users.Where(usr => usr.Enabled && usr.Email == email).ToList();
                userFound = (theUsers.Count() == 1);
                if (!userFound && !string.IsNullOrEmpty(uname))
                {
                    theUsers = theUsers.Where(usr => usr.Name == uname).ToList();
                    userFound = (theUsers.Count() == 1);
                }
                if (!userFound)
                {
                    err = (theUsers.Count() > 1) ? (short)2 : (short)1;
                }
                else
                {
                    result = theUsers.First().UserID.Trim();
                    name = theUsers.First().Name.Trim();
                }
            }
            catch
            {
                err = (short)1;
            }
            return result;
        }

        private string OBUserID(ref string name, string email, out short err)
        {
            string result = string.Empty;
            string uname = name;
            string fn,
                ln;
            bool userFound = false;
            err = (short)0;
            DnetEntities db = new DnetEntities();
            OBUser thisUser = null;
            try
            {
                var theUsers = db.OBUsers.Where(usr => usr.Email == email).ToList();
                userFound = (theUsers.Count() == 1);
                if (!userFound && !string.IsNullOrEmpty(uname))
                {
                    if (uname.Contains(" "))
                    {
                        string[] tmp = uname.Split(' ');
                        fn = tmp[0].Trim();
                        ln = tmp[1].Trim();
                    }
                    else
                    {
                        fn = name.Trim();
                        ln = string.Empty;
                    }
                    if (!string.IsNullOrEmpty(fn))
                        theUsers = theUsers
                            .Where(usr =>
                                String.Equals(
                                    usr.FirstName,
                                    fn,
                                    StringComparison.CurrentCultureIgnoreCase
                                )
                            )
                            .ToList();
                    if (!string.IsNullOrEmpty(ln))
                        theUsers = theUsers
                            .Where(usr =>
                                String.Equals(
                                    usr.LastName,
                                    ln,
                                    StringComparison.CurrentCultureIgnoreCase
                                )
                            )
                            .ToList();
                    userFound = (theUsers.Count() == 1);
                }
                if (!userFound)
                {
                    err = (theUsers.Count() > 1) ? (short)2 : (short)1;
                }
                else
                {
                    OBUser theUser = theUsers.First();
                    result = theUser.UserID.Trim();
                    name = theUser.FirstName.Trim() + " " + theUser.LastName.Trim();
                }
            }
            catch
            {
                err = (short)1;
            }
            return result;
        }

        private DnetEntities GetDBConn()
        {
            string dnetLocation = AppDomain.CurrentDomain.BaseDirectory;
            string file = Path.Combine(dnetLocation, "globalcon.cfg");
            bool exists = System.IO.File.Exists(file);
            if (!exists)
                return new DnetEntities();
            string connString;
            if (Request.Url != null)
            {
                //orignal dnet
                //var contactUsUriString =
                //    Url.RouteUrl("" /* route name, add if needed */,
                //        new // route values, add more if neededT3st5555
                //        {
                //            action = "Index",
                //            controller = "Home"
                //        },
                //        Request.Url.Scheme);
                var contactUsUriString = string.Format(
                    "{0}://{1}{2}",
                    Request.Url.Scheme,
                    Request.Url.Authority,
                    Url.Content("~")
                );
                if (contactUsUriString != null)
                    GlobalVariables.DNetURL = contactUsUriString;

                // Use the test environment instead of localhost
                if (System.Web.HttpContext.Current.Request.IsLocal)
                {
                    GlobalVariables.DNetURL = ConfigurationManager.AppSettings[
                        "DnetTestEnvironmentUrl"
                    ];
                }
            }
            connString = DNetSynch.ProjectSetup.CreateConnectionString(file);
            GlobalVariables.CompanyConnection = connString;
            DnetEntities db = new DnetEntities();
            return db;
        }

        public ActionResult GlobalError()
        {
            return View();
        }

        /*public static bool IsPayrollAllowed(out int maxUsers)
        {
            maxUsers = 0;
            object keyCacheType = System.Runtime.Caching.MemoryCache.Default["activePayrollKey"];
            var activeKey = keyCacheType;
            if (activeKey != null && (string)activeKey == "true")
            {
                int intBuf;
                object cachePayrollUsers = System.Runtime.Caching.MemoryCache.Default["maxPayrollUsers"];
                var maxPayrollUsers = cachePayrollUsers;
                if (maxPayrollUsers != null && int.TryParse((string)maxPayrollUsers, out intBuf)) maxUsers = intBuf;
                return maxUsers >= CurrentPayrollUsers();
            }
            return false;
        }*/

        public static bool IsIpAddressValid(string ipAddress)
        {
            //Split the users IP address into it's 4 octets (Assumes IPv4)
            string[] incomingOctets = ipAddress.Trim().Split(new char[] { '.' });

            //Get the valid IP addresses from the web.config
            string addresses = Convert.ToString(
                ConfigurationManager.AppSettings["AuthorizeIPAddresses"]
            );

            //Store each valid IP address in a string array
            string[] validIpAddresses = addresses.Trim().Split(new char[] { ',' });

            //Iterate through each valid IP address
            foreach (var validIpAddress in validIpAddresses)
            {
                //Return true if valid IP address matches the users
                if (validIpAddress.Trim() == ipAddress)
                {
                    return true;
                }

                //Split the valid IP address into it's 4 octets
                string[] validOctets = validIpAddress.Trim().Split(new char[] { '.' });

                bool matches = true;

                //Iterate through each octet
                for (int index = 0; index < validOctets.Length; index++)
                {
                    //Skip if octet is an asterisk indicating an entire
                    //subnet range is valid
                    if (validOctets[index] != "*")
                    {
                        if (validOctets[index] != incomingOctets[index])
                        {
                            matches = false;
                            break; //Break out of loop
                        }
                    }
                }

                if (matches)
                {
                    return true;
                }
            }

            //Found no matches
            return false;
        }

        public JsonResult ClearOBMAlerts()
        {
            try
            {
                var compModel = new DnetEntities();
                var alerts = compModel
                    .OBProcessMonitors.Where(m =>
                        !m.IsCompleted
                        && m.EESubmitDate != null
                        && m.ClientID == GlobalVariables.Client
                        && m.CompanyID == GlobalVariables.CompanyID
                        && !m.AlertCleared
                    )
                    .ToList();

                foreach (var alert in alerts)
                {
                    alert.AlertCleared = true;
                }

                compModel.SaveChanges();
                // compModel.Dispose();

                return Json("Success", JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                return Json("Fail", JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult ClearOBMODAlerts()
        {
            try
            {
                var compModel = new DnetEntities();
                var alerts = compModel
                    .OBProcessMonitors.Where(obpm =>
                        !obpm.IsCompleted
                        && obpm.DueDate < DateTime.Today
                        && obpm.ClientID == GlobalVariables.Client
                        && obpm.CompanyID == GlobalVariables.CompanyID
                        && !obpm.ODAlertCleared
                    )
                    .ToList();

                foreach (var alert in alerts)
                {
                    alert.ODAlertCleared = true;
                }

                compModel.SaveChanges();
                // compModel.Dispose();

                return Json("Success", JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                return Json("Fail", JsonRequestBehavior.AllowGet);
            }
        }

        //HP
        public JsonResult ClearOBMSGAlerts()
        {
            try
            {
                var compModel = new DnetEntities();
                var alerts = compModel
                    .OBRequests.Where(obr =>
                        obr.Status != OBRequestStatus.Responsed
                        && obr.Status != OBRequestStatus.Seen
                        && obr.EmployeeID.Substring(2, 3) == GlobalVariables.Client
                        && obr.SentLevel.ToString() == "E"
                    )
                    .ToList();

                foreach (var alert in alerts)
                {
                    alert.Status = OBRequestStatus.Seen;
                }

                compModel.SaveChanges();
                // compModel.Dispose();

                return Json("Success", JsonRequestBehavior.AllowGet);
            }
            catch (Exception e)
            {
                return Json("Fail", JsonRequestBehavior.AllowGet);
            }
        }

        public JsonResult ClearNavPayrollNumber()
        {
            try
            {
                GlobalVariables.NavPayrollNumber = null;
                return Json("Success", JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(ex, JsonRequestBehavior.AllowGet);
            }
        }

        // this region has created for TFS # 2802 @ 09/28/2017
        #region Denied Change Request
        private List<Code_Description> RequestDetailsData(ChangeRequestsHDR request)
        {
            var db = new DnetEntities();
            List<Code_Description> result = new List<Code_Description>();
            try
            {
                List<ChangeRequestDetail> details = db
                    .ChangeRequestDetails.Where(d => d.RequestID == request.RequestID)
                    .OrderBy(d => d.SeqNbr)
                    .ToList();
                foreach (ChangeRequestDetail rec in details)
                {
                    if (
                        !string.IsNullOrEmpty(rec.FieldName) /*&& !string.IsNullOrEmpty(rec.OldValue)*/
                    )
                        result.Add(
                            new Code_Description
                            {
                                Code = rec.FieldName,
                                Description = rec.OldValue,
                            }
                        );
                }
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                result = new List<Code_Description>();
            }
            return result;
        }

        private string RestoreDataRecordQuery(ChangeRequestsHDR request)
        {
            string result = string.Empty;
            string tbl = request.TableName;
            string key = request.Key1;
            string key2 = request.Key2;
            bool goodKeys = !string.IsNullOrEmpty(key) && key.Contains("=");
            if (goodKeys && !string.IsNullOrEmpty(key2))
                goodKeys = key2.Contains("=");
            if (!goodKeys)
            {
                var Keys = GetKeyFields(tbl);
                if (!string.IsNullOrEmpty(Keys))
                {
                    var arrKeys = ReorderKeyFields(Keys.Split(','));
                    for (var k = 0; k < arrKeys.Length; k++)
                    {
                        arrKeys[k] = arrKeys[k].Trim();
                    }
                    if (!string.IsNullOrEmpty(key))
                    {
                        key = arrKeys[0] + "=" + key;
                        goodKeys = true;
                    }
                    if (goodKeys && !string.IsNullOrEmpty(key2))
                    {
                        string[] arrValues = key2.Split(',');
                        key2 = string.Empty;
                        if (arrKeys.Length > 1 && arrValues.Length == arrKeys.Length - 1)
                        {
                            for (var k2 = 1; k2 < arrKeys.Length; k2++)
                            {
                                if (!string.IsNullOrEmpty(key2))
                                    key2 += "|";
                                key2 += arrKeys[k2] + "=" + arrValues[k2 - 1];
                            }
                        }
                        else
                            goodKeys = false;
                    }
                }
            }
            if (goodKeys)
            {
                if (!string.IsNullOrEmpty(tbl) && !string.IsNullOrEmpty(key))
                {
                    string fields = string.Empty;
                    string values = string.Empty;
                    List<Code_Description> details = RequestDetailsData(request);
                    switch (request.RequestType)
                    {
                        case "A":
                            key = key.Replace("=", " = '") + "'";
                            if (!string.IsNullOrEmpty(key2))
                                key +=
                                    " AND "
                                    + key2.Replace("|", "' AND ").Replace("=", " = '")
                                    + "'";
                            if (!key.Contains("CompanyID ="))
                                key += " AND (CompanyID = " + request.CompanyID.ToString() + ")";
                            foreach (Code_Description rec in details)
                            {
                                string val = rec.Description.Trim();
                                string valU = val.ToUpper();
                                if (valU == "TRUE" || valU == "FALSE")
                                    val = (valU == "TRUE") ? "1" : "0";
                                else
                                    val = "'" + val + "'";
                                if (!string.IsNullOrEmpty(values))
                                    values += ", ";
                                values += rec.Code + " = " + val;
                            }
                            if (!string.IsNullOrEmpty(values))
                            {
                                result = "UPDATE " + tbl + " SET " + values;
                                result += " WHERE " + key;
                            }
                            break;
                        case "I":
                            key = key.Replace("=", " = '") + "'";
                            if (!string.IsNullOrEmpty(key2))
                                key +=
                                    " AND "
                                    + key2.Replace("|", "' AND ").Replace("=", " = '")
                                    + "'";
                            if (!key.Contains("CompanyID ="))
                                key += " AND (CompanyID = " + request.CompanyID.ToString() + ")";
                            result = "DELETE FROM " + tbl + " WHERE " + key;
                            break;
                        case "D":
                            string[] ar_key = key.Split('=');
                            values = "'" + ar_key[1].Trim() + "'";
                            fields = "[" + ar_key[0].Trim() + "]";
                            if (!string.IsNullOrEmpty(key2))
                            {
                                List<string> ar_keys = key2.Split('|').ToList();
                                foreach (string item in ar_keys)
                                {
                                    ar_key = key.Split('=');
                                    values += ", '" + ar_key[1].Trim() + "'";
                                    fields += ", [" + ar_key[0].Trim() + "]";
                                }
                            }
                            foreach (Code_Description rec in details)
                            {
                                string val = rec.Description.Trim();
                                string valU = val.ToUpper();
                                if (valU == "TRUE" || valU == "FALSE")
                                    val = (valU == "TRUE") ? "1" : "0";
                                else
                                    val = "'" + val + "'";
                                values += ", " + val;
                                fields += ", [" + rec.Code + "]";
                            }
                            result =
                                "INSERT INTO " + tbl + " (" + fields + ") VALUES (" + values + ")";
                            break;
                    }
                }
            }
            return result;
        }

        private string GetKeyFields(string table)
        {
            if (table == "EmployeeLicensesAndCertifications")
                return "EmployeeID,LicenseCertificationID";
            if (table == "ClientJobCostAssignments")
                return "ClientID,JobCostingName";
            string mySql;
            string result = string.Empty;
            mySql = "Select Stuff(";
            mySql += " (";
            mySql += " Select ', ' + C.COLUMN_NAME";
            mySql += " From INFORMATION_SCHEMA.COLUMNS AS C,";
            mySql += " INFORMATION_SCHEMA.TABLE_CONSTRAINTS Tab, ";
            mySql += " INFORMATION_SCHEMA.CONSTRAINT_COLUMN_USAGE Col";
            mySql += " Where C.TABLE_SCHEMA = T.TABLE_SCHEMA";
            mySql += " And C.TABLE_NAME = T.TABLE_NAME";
            mySql += " And C.COLUMN_NAME <> 'CompanyID'";
            mySql += " and    Col.Constraint_Name = Tab.Constraint_Name";
            mySql += " AND Col.Table_Name = Tab.Table_Name";
            mySql += " AND Col.Table_Name = c.TABLE_NAME";
            mySql += " AND Col.COLUMN_NAME = c.COLUMN_NAME";
            mySql += " AND Tab.Constraint_Type = 'PRIMARY KEY'";
            mySql += " ORDER BY C.ORDINAL_POSITION";
            mySql += " For Xml Path('')";
            mySql += " ), 1, 2, '') As Columns";
            mySql += " From INFORMATION_SCHEMA.TABLES As T";
            mySql += " where T.TABLE_NAME = '" + table + "'";

            SqlConnection myConnection = new SqlConnection(GlobalVariables.CompanyConnection);
            SqlCommand myCommand = new SqlCommand(mySql, myConnection);
            myConnection.Open();
            SqlDataReader myReader = myCommand.ExecuteReader();
            if (myReader.HasRows)
            {
                while (myReader.Read())
                {
                    result = myReader.GetString(0);
                }
            }
            return result;
        }

        private string[] ReorderKeyFields(string[] keys)
        {
            int eeIdx = -1;
            int ccIdx = -1;
            for (int i = 0; i < keys.Length; i++)
            {
                keys[i] = keys[i].Trim();
                if (keys[i] == "EmployeeID")
                    eeIdx = i;
                if (keys[i] == "ClientID")
                    ccIdx = i;
            }
            string[] result = keys;
            string tmp = result[0];
            if (eeIdx > 0)
            {
                result[0] = keys[eeIdx];
                result[eeIdx] = tmp;
            }
            else
            {
                if (ccIdx > 0)
                {
                    result[0] = keys[ccIdx];
                    result[ccIdx] = tmp;
                }
            }
            return result;
        }

        public void RestoreDeniedRequestsData()
        {
            try { }
            catch (Exception exception)
            {
                Bugsnag.AspNet.Client.Current.Notify(exception);
            }
        }
        #endregion
        #region Private Methods
        public static int CurrentPayrollUsers()
        {
            var db = new DnetEntities();
            int spayrolls = db
                .Users.Where(x => x.Enabled && x.SystemLevelEnabled && x.CanPayroll)
                .Count();
            var cUsers = db
                .Users.Where(x => x.Enabled && x.ClientLevelEnabled)
                .Select(x => x.UserID)
                .ToList();
            var cRoles = db.UserRoleClientEmployeeAssignments.Where(x => x.ClientPayroll);
            return spayrolls + cRoles.Where(x => cUsers.Any(u => u == x.UserID)).Count();
        }

        private string GetClientMasterClientID()
        {
            var clientStatus = _dbContext
                .Clients.Where(client =>
                    client.CompanyID == GlobalVariables.CompanyID
                    && client.ClientID == GlobalVariables.Client
                )
                .Select(client => client.ClientStatus)
                .FirstOrDefault();
            return clientStatus == 1 // Sub Client
                ? _dbContext
                    .ClientSubClients.Where(csc =>
                        csc.CompanyID == GlobalVariables.CompanyID
                        && csc.SubClientID == GlobalVariables.Client
                    )
                    .Select(client => client.ClientID)
                    .FirstOrDefault()
                : null;
        }

        private List<CodeDescription> GetClientSubClients()
        {
            var subClients = new List<CodeDescription>();
            var clientStatus = _dbContext
                .Clients.Where(client =>
                    client.CompanyID == GlobalVariables.CompanyID
                    && client.ClientID == GlobalVariables.Client
                )
                .Select(client => client.ClientStatus)
                .FirstOrDefault();
            if (clientStatus == 2) // Master Client
            {
                var clientSubClients = _dbContext
                    .ClientSubClients.Where(csc =>
                        csc.CompanyID == GlobalVariables.CompanyID
                        && csc.ClientID == GlobalVariables.Client
                    )
                    .ToList();
                clientSubClients.ForEach(sc =>
                {
                    if (
                        _dbContext.UserRoleClientAccesses.Any(ca =>
                            ca.CompanyID == GlobalVariables.CompanyID
                            && ca.ClientID == sc.SubClientID
                            && ca.UserID == GlobalVariables.CurrentUser.UserID
                        )
                    )
                    {
                        var client = _dbContext
                            .Clients.Where(c =>
                                c.CompanyID == GlobalVariables.CompanyID
                                && c.ClientID == sc.SubClientID
                            )
                            .Select(c => new CodeDescription
                            {
                                Code = c.ClientID,
                                Description = c.ClientID + ": " + c.ClientName,
                            })
                            .FirstOrDefault();
                        subClients.Add(client);
                    }
                });
            }
            return subClients;
        }
        #endregion
        public ActionResult LoginAsFromClient(
            string UserId,
            string EmployeeID = null,
            string transID = null
        )
        {
            try
            {
                var destinationUrl = GlobalVariables.DestinationUrl;
                var companyId = GlobalVariables.CompanyID;
                string password = string.Empty;

                GlobalVariables.Customer = null;

                if (UserId != null)
                {
                    password = _obUserService.GetOBUserByUserId(UserId)?.Password;
                }

                password = ConCryptor.Decrypt(password);

                string userId = "";
                string pwd = "";
                bool isSSO = false;

                userId = UserId.Trim();
                pwd = password.Trim();

                // Clear login as globals for new login
                GlobalVariables.LoggedInAs = false;
                GlobalVariables.LoggedInAsUser = String.Empty;
                GlobalVariables.LoggedInUserPWD = String.Empty;
                GlobalVariables.LoggedInAsClientUser = GlobalVariables.DNETOwnerID;
                GlobalVariables.LoggedInAsClientUserPWD = GlobalVariables.DNETOwnerPWD;

                GlobalVariables.LoggedInAsFromClient = true;

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(pwd))
                {
                    TempData["Error"] = "You must provide a UserID and Password";
                    return RedirectToAction("Index");
                }

                GlobalVariables.DNETOwnerID = userId;
                GlobalVariables.DNETOwnerPWD = pwd;
                GlobalVariables.AppTest = "Set Entry Post";
                ModelState.AddModelError("Error", "Got Post");

                var projSetup = new DNetSynch.ProjectSetup(_billingApiService);
                GlobalVariables.AppTest += "Called Proj Setup";
                ModelState.AddModelError("Error", "Created Static");

                if (!isSSO)
                {
                    string encryptedPwd = ConCryptor.Encrypt(pwd);
                    OBUser exuser = _dbContext.OBUsers.FirstOrDefault(x =>
                        x.UserID == userId && x.Password == encryptedPwd
                    );
                    if (exuser != null)
                    {
                        OBProcessMonitor omonitor = _dbContext.OBProcessMonitors.FirstOrDefault(x =>
                            x.EmployeeID == exuser.EmployeeID && x.IsCompleted == false
                        );
                        if (omonitor != null)
                        {
                            if (omonitor.RehireEmployeeID != null)
                            {
                                GlobalVariables.IsReHire = true;
                                GlobalVariables.ReHireEmployeeID = omonitor.RehireEmployeeID;
                            }
                            else
                            {
                                GlobalVariables.IsReHire = false;
                            }
                        }
                        else
                        {
                            GlobalVariables.IsReHire = false;
                        }
                    }
                }

                var myRes = projSetup.SetParameters(isSSO, out string myString);
                if (!string.IsNullOrEmpty(myString))
                {
                    TempData["Error"] = myString;
                    return RedirectToAction("Index");
                }

                GlobalVariables.AppTest += "Calling Set Parameters";
                SetThemeCss(GlobalVariables.ColorSchema);
                ModelState.AddModelError("Error", "Finished Set Parameters");
                GlobalVariables.LanguageID = 1;
                GlobalVariables.ClientMaskSSN = true;

                // Ensure default documents exist
                var defaultDocumentCount = _dbContext.OBDocuments.Count(x =>
                    x.CompanyID == companyId && x.Locked
                );
                if (defaultDocumentCount <= 0)
                {
                    _dbContext.Database.ExecuteSqlCommand(
                        "usp_OBCreateDefaultDocuments @CompanyID",
                        new SqlParameter("CompanyID", companyId)
                    );
                    _dbContext.SaveChanges();
                }

                SetLevel(GlobalVariables.DnetRoleRecordID.ToString(), myRes);

                var redirectAction = "Index";
                var redirectController = "Dashboard";

                if (myRes == "isOb")
                {
                    redirectAction = "OBProcessTasksIndex";
                    redirectController = "OBProcessMonitor";
                }

                var activityStatus = CheckForSession(false);
                if (activityStatus == "OtherUser" && !GlobalVariables.LoggedInAs)
                {
                    redirectAction = "ExistingSession";
                    redirectController = "Home";
                }

                var result = RedirectToAction(redirectAction, redirectController);

                _changeRequestsHDRService.RestoreDeniedRequestData();

                if (!string.IsNullOrWhiteSpace(destinationUrl))
                {
                    var delimitorChars = new char[] { '/', '?' };
                    var urlComponents = destinationUrl.Split(
                        delimitorChars,
                        StringSplitOptions.RemoveEmptyEntries
                    );
                    if (urlComponents.Length == 3)
                    {
                        redirectController = urlComponents[0];
                        redirectAction = urlComponents[1];
                        var urlParameters = urlComponents[2].Split('&');
                        var routeValues = new RouteValueDictionary();
                        foreach (var parameter in urlParameters)
                        {
                            if (parameter.Contains("="))
                            {
                                var parameterComponents = parameter.Split('=');
                                var parameterValue = Server.UrlDecode(parameterComponents[1]);
                                var parameterName = Server.UrlDecode(parameterComponents[0]);
                                routeValues.Add(parameterName, parameterValue);

                                if (parameterName.ToLower() == "payrollnumber")
                                {
                                    var oPayrollNumber = PayrollNumber.Parse(parameterValue);
                                    var urcea =
                                        _dbContext.UserRoleClientEmployeeAssignments.FirstOrDefault(
                                            x =>
                                                x.UserID == userId
                                                && x.CompanyID == oPayrollNumber.CompanyId
                                                && x.ClientID == oPayrollNumber.ClientId
                                        );
                                    if (urcea != null)
                                    {
                                        SetLevel(urcea.id.ToString(), myRes);
                                    }
                                }
                            }
                            result = RedirectToAction(
                                redirectAction,
                                redirectController,
                                routeValues
                            );
                        }
                    }
                }
                GlobalVariables.LoggedInAsFromClient = true;

                return result;
            }
            catch (Exception ex)
            {
                var userContextInfo =
                    $"UserID: {GlobalVariables.DNETOwnerID}, "
                    + $"CompanyID: {GlobalVariables.CompanyID}, "
                    + $"ClientUser: {GlobalVariables.LoggedInAsClientUser}, "
                    + $"LoggedInAsFromClient: {GlobalVariables.LoggedInAsFromClient}";

                log.Error(
                    "An error occurred in LoginAsFromClient.\nContext: " + userContextInfo,
                    ex
                );
                TempData["Error"] = "An unexpected error occurred: ";
                return RedirectToAction("Index");
            }
        }
        //end
    }
}
