using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Data;
using DataDrivenViewEngine.Models.Core;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers
{
    [IsSessionActive]
    public class DocumentDataFieldController : Controller
    {
        private DnetEntities dbContext;
        private string obEmployee, obAccessLevel;
        private int? obSetup;



        /// <summary>
        /// 
        /// </summary>
        /// <param name="disposing"></param>
        protected override void Dispose(bool disposing)
        {
            dbContext.Dispose();
            base.Dispose(disposing);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestContext"></param>
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            dbContext = new DnetEntities();
            obEmployee = GlobalVariables.EmployeeID;
            obAccessLevel = GlobalVariables.DNETLevel;
            obSetup = dbContext.OBProcessMonitors.First(x => x.CompanyID == GlobalVariables.CompanyID && x.EmployeeID == GlobalVariables.EmployeeID).SetupID;
            base.Initialize(requestContext);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            List<DarwiNet2._0.Data.OBProcessDocument> documents = new List<DarwiNet2._0.Data.OBProcessDocument>();

            documents = dbContext.OBProcessDocuments
                .Where(x => x.CompanyID == GlobalVariables.CompanyID && x.EmployeeID == obEmployee)
                .OrderBy(x => x.DocumentID)
                .ToList();

            return View(documents);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        List<OBProcessDocument> GetProcessDocuments()
        {
            List<OBProcessDocument> documents = new List<OBProcessDocument>();

            documents = dbContext.OBProcessDocuments
                .Where(x => x.CompanyID == GlobalVariables.CompanyID && x.EmployeeID == obEmployee)
                .OrderBy(x => x.DocumentID)
                .ToList();

            return documents;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public ActionResult OBProcessDocuments_Read([DataSourceRequest] DataSourceRequest request)
        {
            return Json(GetProcessDocuments().ToDataSourceResult(request));
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            DarwiNet2._0.Data.OBDocument document = dbContext.OBDocuments.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == id);

            return View(document);
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="PDFFormIn"></param>
        /// <param name="PDFFormOut"></param>
        /// <param name="PDFSubmitUrl"></param>
        private void AddButton(PdfReader PDFFormIn, FileStream PDFFormOut, string PDFSubmitUrl)
        {
            PdfStamper stamper = new PdfStamper(PDFFormIn, PDFFormOut);
            Rectangle ButtonRect = new Rectangle(100, 806, 170, 788);
            PushbuttonField button = new PushbuttonField(stamper.Writer, ButtonRect, "postSubmit");
            button.BackgroundColor = BaseColor.LIGHT_GRAY;
            button.BorderColor = GrayColor.BLACK;
            button.BorderWidth = 1f;
            button.BorderStyle = PdfBorderDictionary.STYLE_BEVELED;
            button.TextColor = GrayColor.DARK_GRAY;
            button.FontSize = 8f;
            button.Text = "Verify";
            button.Visibility = PushbuttonField.VISIBLE_BUT_DOES_NOT_PRINT;
            button.Alignment = Element.ALIGN_CENTER | Element.ALIGN_BOTTOM;
            PdfFormField field = button.Field;
            field.Action = PdfAction.CreateSubmitForm(PDFSubmitUrl, null, PdfAction.SUBMIT_HTML_FORMAT);
            stamper.AddAnnotation(field, 1);
            stamper.Close();
        }



        /// <summary>
        ///     Return the database saved value for PDF document mapped field
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        private string UDFNewName(int docId)
        {
            string name = "UDF1";

            int udf_count = dbContext.OBDocumentMappings
                .Count(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId && x.DB_Table == OBDocSource.DocumentUDF && x.DB_Field.Contains("UDF"));

            bool newLabel = true;

            while (newLabel)
            {
                udf_count++;
                name = "UDF" + udf_count.ToString();

                newLabel = dbContext.OBDocumentMappings
                    .Any(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId && x.DB_Table == OBDocSource.DocumentUDF && x.DB_Field == name);
            }

            return name;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="name"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        private string ParsePDFformClientField(string name, string state)
        {
            string value = string.Empty;
            Client client = this.dbContext.Clients.FirstOrDefault(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == GlobalVariables.Client);
            ClientAddress thisAddress = this.dbContext.ClientAddresses.FirstOrDefault(ca => ca.CompanyID == client.CompanyID && ca.ClientID == client.ClientID && ca.AddressCode == client.AddressCode);
            Company thisCompany = this.dbContext.Companies.FirstOrDefault(c => c.CompanyID == client.CompanyID);
            DarwinetSetup setup = this.dbContext.DarwinetSetups.FirstOrDefault(ds => ds.ClientID == client.ClientID && ds.CompanyID == GlobalVariables.CompanyID);

            byte nameType = setup?.I9ClientName ?? (byte)0;
            switch (name)
            {
                case OBFormClientFields.EIN:
                    value = (client.ClientType == ClientTypes.ASO) ? (this.dbContext.ClientTaxSetups.FirstOrDefault(s => s.ClientID == client.ClientID && s.CompanyID == GlobalVariables.CompanyID && s.StateCode == "FED")?.EPRIDNumber) ?? string.Empty : GlobalVariables.EIN;
                    if (string.IsNullOrEmpty(value) || client.ClientType == ClientTypes.ASO) // 02/12/2018 DS TFS # 3008
                    {
                        value = this.dbContext.UnemploymentSetups.FirstOrDefault(s => s.CompanyID == GlobalVariables.CompanyID && s.FUTASUTA == "FED")?.EmployerIDNumber ?? (setup?.EID ?? string.Empty);
                    }
                    break;
                case OBFormClientFields.StateEIN:
                    value = string.Empty;
                    if (!string.IsNullOrEmpty(state))
                    {
                        value = (client.ClientType == ClientTypes.ASO) ? (this.dbContext.ClientTaxSetups.FirstOrDefault(s => s.ClientID == client.ClientID && s.CompanyID == GlobalVariables.CompanyID && s.StateCode == state)?.ESTIDNumber) ?? string.Empty
                                                                       : (this.dbContext.ClientUnemploymentSetups.FirstOrDefault(s => s.CompanyID == GlobalVariables.CompanyID && s.ClientID == GlobalVariables.Client && s.FutaSuta == state)?.ESTIDNumber) ?? string.Empty;
                        if (string.IsNullOrEmpty(value))
                        {
                            value = this.dbContext.UnemploymentSetups.FirstOrDefault(s => s.CompanyID == GlobalVariables.CompanyID && s.FUTASUTA == state)?.EmployerStateIDNumber ?? string.Empty;
                        }
                    }
                    break;
                case OBFormClientFields.PEO: // 10/04/2017 DS TFS # 2768
                    switch(nameType)
                    { 
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = client.ClientName;
                            }
                            else
                            {
                                value = GlobalVariables.PEOName;
                            }
                            break;
                        case 1:
                            value = client.ClientName;
                            break;
                        case 2:
                            value = GlobalVariables.PEOName;
                            break;
                    }
                    break;
                case OBFormClientFields.Title:
                    value = client.ClientName;
                    break;
                case OBFormClientFields.Street:
                    switch(nameType)
                    { 
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = (thisAddress.Address1 ?? string.Empty) + " " + (thisAddress.Address2 ?? string.Empty);
                            }
                            else
                            {
                                value = (thisCompany.Address1 ?? string.Empty) + " " + (thisCompany.Address2 ?? string.Empty);
                            }
                            break;
                        case 1:
                            value = (thisAddress.Address1 ?? string.Empty) + " " + (thisAddress.Address2 ?? string.Empty);
                            break;
                        case 2:
                            value = (thisCompany.Address1 ?? string.Empty) + " " + (thisCompany.Address2 ?? string.Empty);
                            break;
                    }
                    break;
                case OBFormClientFields.Contact:
                    switch (nameType)
                    {
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = thisAddress.ContactPerson ?? string.Empty;
                            }
                            else
                            {
                                value = (thisCompany.AddressContact ?? string.Empty);
                            }
                            break;
                        case 1:
                            value = thisAddress.ContactPerson ?? string.Empty;
                            break;
                        case 2:
                            value = (thisCompany.AddressContact ?? string.Empty);
                            break;
                    }
                    break;
                case OBFormClientFields.Phone:
                    switch(nameType)
                    { 
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = thisAddress.Phone1 ?? string.Empty;
                            }
                            else
                            {
                                value = (thisCompany.Phone1 ?? string.Empty);
                            }
                            break;
                        case 1:
                            value = thisAddress.Phone1 ?? string.Empty;
                            break;
                        case 2:
                            value = (thisCompany.Phone1 ?? string.Empty);
                            break;
                    }
                    break;
                case OBFormClientFields.City:
                    switch(nameType)
                    { 
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = thisAddress.City ?? string.Empty;
                            }
                            else
                            {
                                value = (thisCompany.City ?? string.Empty);
                            }
                            break;
                        case 1:
                            value = thisAddress.City ?? string.Empty;
                            break;
                        case 2:
                            value = (thisCompany.City ?? string.Empty);
                            break;
                    }
                    break;
                case OBFormClientFields.State:
                    switch(nameType)
                    { 
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = thisAddress.State ?? string.Empty;
                            }
                            else
                            {
                                value = (thisCompany.State ?? string.Empty);
                            }
                            break;
                        case 1:
                            value = thisAddress.State ?? string.Empty;
                            break;
                        case 2:
                            value = (thisCompany.State ?? string.Empty);
                            break;
                    }
                    break;
                case OBFormClientFields.ZIP:
                    switch(nameType)
                    { 
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = thisAddress.Zip ?? string.Empty;
                            }
                            else
                            {
                                value = (thisCompany.Zip ?? string.Empty);
                            }
                            break;
                        case 1:
                            value = thisAddress.Zip ?? string.Empty;
                            break;
                        case 2:
                            value = (thisCompany.Zip ?? string.Empty);
                            break;
                    }
                    break;
                case OBFormClientFields.Address:
                    string ct = string.Empty;
                    string st = string.Empty;
                    switch(nameType)
                    { 
                        case 0:
                            if (client.ClientType == ClientTypes.ASO)
                            {
                                value = (thisAddress.Address1 ?? string.Empty) + " " + (thisAddress.Address2 ?? string.Empty);
                                ct = thisAddress.City ?? string.Empty;
                                st = thisAddress.State ?? string.Empty + " " + thisAddress.Zip ?? string.Empty;
                            }
                            else
                            {
                                value = (thisCompany.Address1 ?? string.Empty) + " " + (thisCompany.Address2 ?? string.Empty);
                                ct = (thisCompany.City ?? string.Empty);
                                st = thisCompany.State ?? string.Empty + " " + thisCompany.Zip ?? string.Empty;
                            }
                            break;
                        case 1:
                            value = (thisAddress.Address1 ?? string.Empty) + " " + (thisAddress.Address2 ?? string.Empty);
                            ct = thisAddress.City ?? string.Empty;
                            st = thisAddress.State ?? string.Empty + " " + thisAddress.Zip ?? string.Empty;
                            break;
                        case 2:
                            value = (thisCompany.Address1 ?? string.Empty) + " " + (thisCompany.Address2 ?? string.Empty);
                            ct = (thisCompany.City ?? string.Empty);
                            st = thisCompany.State ?? string.Empty + " " + thisCompany.Zip ?? string.Empty;
                            break;
                    }
                    if (value == " ") value = string.Empty;
                    if (st == " ") st = string.Empty;
                    if (!string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(ct)) value += ", ";
                    value += ct;
                    if (!string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(st)) value += ", ";
                    value += st;
                    break;
            }
            return value;
        }

        /*private string ParsePDFformClientField(string name, string state)
        {
            string value = string.Empty;
            Clients client = dbContext.Clients.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.ClientID == GlobalVariables.Client);

            if (client != null)
            {
                ClientAddresses thisAddress = client.ClientAddresses.FirstOrDefault(x => x.AddressCode == client.AddressCode);

                switch (name)
                {
                    case OBFormClientFields.PEO:
                        value = GlobalVariables.PEOName;

                        if (client.ClientType == ClientTypes.ASO)
                        {
                            value = client.ClientName;
                        }
                        break;

                    case OBFormClientFields.EIN:
                        value = GlobalVariables.EIN;

                        if (string.IsNullOrEmpty(value) || client.ClientType == ClientTypes.ASO)
                        {
                            value = dbContext.DarwinetSetups.Where(x => x.ClientID == client.ClientID).Select(ds => ds.EID).FirstOrDefault();
                        }
                        break;

                    case OBFormClientFields.StateEIN:
                        value = string.Empty;

                        if (!string.IsNullOrEmpty(state))
                        {
                            var rec = dbContext.ClientUnemploymentSetups
                                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.ClientID == GlobalVariables.Client && x.FutaSuta == state);

                            if (rec != null)
                            {
                                if (string.IsNullOrEmpty(rec.ESTIDNumber))
                                {
                                    value = string.Empty;
                                }
                                else
                                {
                                    value = rec.ESTIDNumber;
                                }
                            }

                            if (string.IsNullOrEmpty(value) || client.ClientType == ClientTypes.ASO)
                            {
                                var rec1 = dbContext.UnemploymentSetups.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.FUTASUTA == state);

                                if (rec1 != null)
                                {
                                    if (string.IsNullOrEmpty(rec1.EmployerStateIDNumber))
                                    {
                                        value = string.Empty;
                                    }
                                    else
                                    {
                                        value = rec1.EmployerStateIDNumber;
                                    }
                                }
                            }
                        }
                        break;

                    case OBFormClientFields.Street:
                        value = (thisAddress.Address1 ?? string.Empty) + " " + (thisAddress.Address2 ?? string.Empty);
                        break;

                    case OBFormClientFields.City:
                        value = thisAddress.City ?? string.Empty;
                        break;

                    case OBFormClientFields.State:
                        value = thisAddress.State ?? string.Empty;
                        break;

                    case OBFormClientFields.ZIP:
                        value = thisAddress.Zip ?? string.Empty;
                        break;
                }
            }

            return value;
        } */

        /// <summary>
        /// 
        /// </summary>
        /// <param name="signType"></param>
        /// <param name="name"></param>
        /// <param name="ip"></param>
        /// <param name="dt"></param>
        /// <returns></returns>
        private string BuildSignature(short signType, string name, string ip, DateTime? dt)
        {
            string result = string.Empty;
            ip = ip ?? GlobalVariables.OwnerIP;
            string signDate = (dt ?? DateTime.Now).ToString("d");

            switch (signType)
            {
                case OBSignatureType.Name:
                    result = name;
                    break;

                case OBSignatureType.Name_IP:
                    result = name + " " + ip;
                    break;

                case OBSignatureType.Name_Date:
                    result = name + " " + signDate;
                    break;

                case OBSignatureType.Name_IP_Date:
                    result = name + " " + ip + " " + signDate;
                    break;
            }

            return result;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="docId"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        private string ParsePDFformSignFields(int docId, string name, string format)
        {
            string value = string.Empty;
            short signType = dbContext.OBClientSetups.First(x => x.CompanyID == GlobalVariables.CompanyID && x.SetupID == obSetup).Signature ?? OBSignatureType.NotUsed;

            if (signType != OBSignatureType.NotUsed)
            {
                OBProcessDocument doc = dbContext.OBProcessDocuments
                    .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId && x.EmployeeID == obEmployee);

                switch (name)
                {
                    case OBFormSignFields.EE_Sign:
                        value = BuildSignature(signType, doc.EEVerifiedBy, doc.EEVerifiedIP, doc.EEVerifiedDate);
                        break;

                    case OBFormSignFields.EE_SignDate:
                        value = (doc.EEVerifiedDate ?? DateTime.Now).ToString("MM/dd/yyyy");
                        break;

                    case OBFormSignFields.ER_Sign:
                        value = BuildSignature(signType, doc.CCVerifiedBy, doc.CCVerifiedIP, doc.CCVerifiedDate);
                        break;

                    case OBFormSignFields.ER_SignDate:
                        value = (doc.CCVerifiedDate ?? DateTime.Now).ToString("MM/dd/yyyy");
                        break;
                    case OBFormSignFields.The_Year:
                        value = DateTime.Now.Year.ToString();
                        break;
                }
            }
            if (!string.IsNullOrEmpty(value) && !string.IsNullOrEmpty(format))
            {
                if (format.Contains(":"))
                {
                    string prefix = format.Substring(0, 2);
                    DateTime dt;
                    int start = 0, ln = 0;
                    bool useLen = false;
                    switch(prefix)
                    { 
                        case "FD":
                            format = format.Replace("FD:",""); 
                            if (DateTime.TryParse(value, out dt))
                            {
                                switch (format)
                                {
                                    case "DAY":
                                        value = dt.Day.ToString("D2");
                                        break;
                                    case "MONTH":
                                        value = dt.Month.ToString("D2");
                                        break;
                                    case "YEAR":
                                        value = dt.Year.ToString("D4");
                                        break;
                                    case "SHORTYR":
                                        value = dt.Year.ToString("D4").Substring(2,2);
                                        break;
                                    default:
                                        value = dt.ToString(format);
                                        break;
                                }
                                            
                            }
                            else value = string.Empty;
                            break;
                        default:
                            string[] pos = format.Split(':');
                            if (!int.TryParse(pos[0], out start)) start = 0;
                            if (int.TryParse(pos[1], out ln)) useLen = (ln > 0) && (ln + start <= value.Length);
                            break;
                    }
                    value = (useLen) ? value.Substring(start, ln) : value.Substring(start);
                }
                else
                    value = (!string.IsNullOrEmpty(value)) ? value : string.Empty;
            }
            return value;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        private string ParsePDFformFieldValue(string value, byte type)
        {
            string result = value.Trim() ?? null;
            DateTime dd;

            switch (type)
            {
                case FieldTypes.CheckBox:
                    if (value.ToUpper() == "FALSE")
                    {
                        value = "0";
                    }
                    else
                    {
                        if (value.ToUpper() == "NO")
                        {
                            value = "0";
                        }
                        else
                        {
                            value = "1" ?? "0";
                        }
                    }
                    break;

                case FieldTypes.Date:
                    if (!DateTime.TryParse(value, out dd))
                    {
                        return string.Empty;
                    }

                    value = dd.ToString("MM/dd/yyyy");
                    break;

                case FieldTypes.DateTime:
                    if (!DateTime.TryParse(value, out dd))
                    {
                        return string.Empty;
                    }

                    value = dd.ToString("MM/dd/yyyy hh:mm:ss tt");
                    break;
            }

            return value;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="mappedField"></param>
        /// <returns></returns>
        private bool IsPDFformFieldEditable(OBDocumentMapping mappedField)
        {
            string dbTable = mappedField.DB_Table;
            bool result = true;

            if (!string.IsNullOrEmpty(dbTable))
            {
                result = (dbTable == OBDocSource.DocumentUDF);
            }

            if (result)
            {
                if (obAccessLevel == DNetAccessLevel.Employee)
                {
                    result = (mappedField.FieldStatus == OBDocFieldStatus.EE_Editable) || (mappedField.FieldStatus == OBDocFieldStatus.EE_ER_Editable);
                }
                else
                {
                    result = (mappedField.FieldStatus == OBDocFieldStatus.ER_Editable) || (mappedField.FieldStatus == OBDocFieldStatus.EE_ER_Editable);
                }
            }

            return result;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="docId"></param>
        /// <param name="state"></param>
        /// <param name="mappedField"></param>
        /// <returns></returns>
        private string PDFformFieldValue(int docId, string state, OBDocumentMapping mappedField)
        {
            var model = new DnetEntities();
            int? obSetup = model.OBProcessMonitors.First(x => x.CompanyID == GlobalVariables.CompanyID && x.EmployeeID == obEmployee).SetupID;
            string value = string.Empty;
            int nRec = mappedField.DB_RecNum;

            if (nRec == null)
            {
                nRec = 0;
            }
            else
            {
                if (nRec > 0)
                {
                    nRec = nRec--;
                }
                else
                {
                    nRec = 0;
                }
            }

            string dbTbl = mappedField.DB_Table;

            if (!string.IsNullOrEmpty(dbTbl))
            {
                switch (dbTbl)
                {
                    case OBDocSource.DocumentUDF:
                        value = model.OBProcessDocumentUDFs
                            .First(x => 
                                x.CompanyID == GlobalVariables.CompanyID && 
                                x.EmployeeID == obEmployee && 
                                x.DocumentID == docId && x.UDFName == mappedField.FormName).UDFValue;
                        break;

                    case OBDocSource.Signature:
                        value = ParsePDFformSignFields(docId, mappedField.DB_Field, mappedField.FieldPresentation);
                        break;

                    case OBDocSource.ClientInfo:
                        value = ParsePDFformClientField(mappedField.DB_Field, state);
                        break;

                    default:
                        OBProcessTaskField taskField = model.OBProcessTaskFields
                            .FirstOrDefault(x => 
                                x.PermanentTBL == mappedField.DB_Table && 
                                x.PermanentFLD == mappedField.DB_Field && 
                                x.CompanyID == GlobalVariables.CompanyID && 
                                x.EmployeeID == obEmployee);

                        List<OBProcessRecordDetail> details = model.OBProcessRecordDetails
                            .Where(x => 
                                x.CompanyID == GlobalVariables.CompanyID && 
                                x.EmployeeID == obEmployee && 
                                x.TaskID == taskField.TaskID && 
                                x.FldName == taskField.FName)
                            .OrderBy(x => x.RecordID).ToList();

                        if (nRec <= details.Count)
                        {
                            value = ParsePDFformFieldValue(details[nRec].FldValue, (byte)taskField.FType);
                        }
                        break;
                }
            }

            return value;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        private string GetDocAssignedState(int docId)
        {
            string result = string.Empty;
            var model = new DnetEntities();

            var doc = model.OBDocuments.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId);

            if (doc != null)
            {
                result = doc.AssignedToState;
            }

            return result;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="docId"></param>
        /// <param name="PDFFormIn"></param>
        /// <param name="PDFFormOut"></param>
        /// <param name="PDFSubmitUrl"></param>
        private void FillPdfDocument(int docId, PdfReader PDFFormIn, FileStream PDFFormOut, string PDFSubmitUrl)
        {
            PdfStamper stamper = new PdfStamper(PDFFormIn, PDFFormOut);
            string state = GetDocAssignedState(docId);
            var model = new DnetEntities();
            List<OBDocumentMapping> formFields = model.OBDocumentMappings.Where(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId).ToList();

            foreach (OBDocumentMapping mappedFld in formFields)
            {
                if (!string.IsNullOrEmpty(mappedFld.DB_Table))
                {
                    string v = PDFformFieldValue(docId, state, mappedFld);

                    if (!string.IsNullOrEmpty(v))
                    {
                        stamper.AcroFields.SetField(mappedFld.FormName, v);
                    }

                    if (!IsPDFformFieldEditable(mappedFld))
                    {
                        stamper.AcroFields.SetFieldProperty(mappedFld.FormName, "setfflags", PdfFormField.FF_READ_ONLY, null);
                    }
                }
            }

            Rectangle ButtonRect = new Rectangle(100, 806, 170, 788);
            PushbuttonField button = new PushbuttonField(stamper.Writer, ButtonRect, "postSubmit");
            button.BackgroundColor = BaseColor.LIGHT_GRAY;
            button.BorderColor = GrayColor.BLACK;
            button.BorderWidth = 1f;
            button.BorderStyle = PdfBorderDictionary.STYLE_BEVELED;
            button.TextColor = GrayColor.DARK_GRAY;
            button.FontSize = 8f;
            button.Text = "Verify";
            button.Visibility = PushbuttonField.VISIBLE_BUT_DOES_NOT_PRINT;
            button.Alignment = Element.ALIGN_CENTER | Element.ALIGN_BOTTOM;
            PdfFormField field = button.Field;
            field.Action = PdfAction.CreateSubmitForm(PDFSubmitUrl, null, PdfAction.SUBMIT_HTML_FORMAT);
            stamper.AddAnnotation(field, 1);
            stamper.Close();
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="docId"></param>
        /// <returns></returns>
        private bool IsDocumentSigned(int docId)
        {
            bool result = true;

            OBProcessDocument doc = dbContext.OBProcessDocuments
                .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId && x.EmployeeID == obEmployee);

            if (doc != null)
            {
                short? verificationType = doc.VerificationType;

                switch (verificationType)
                {
                    case OBDocVerification.EE_Signature:
                        result = !string.IsNullOrEmpty(doc.EEVerifiedIP);
                        break;

                    case OBDocVerification.EE_ER_Signature:
                        if (obAccessLevel == DNetAccessLevel.Employee)
                        {
                            result = !string.IsNullOrEmpty(doc.EEVerifiedIP);
                        }
                        else
                        {
                            result = !(string.IsNullOrEmpty(doc.EEVerifiedIP) && string.IsNullOrEmpty(doc.CCVerifiedIP));
                        }
                        break;

                    case OBDocVerification.ER_Signature:
                        if (obAccessLevel == DNetAccessLevel.Employee)
                        {
                            result = true;
                        }
                        else
                        {
                            result = !string.IsNullOrEmpty(doc.CCVerifiedIP);
                        }
                        break;
                }
            }

            return result;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="docId"></param>
        /// <param name="docData"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult SaveDocument(int docId, FormCollection docData)
        {
            try
            {
                List<OBDocumentMapping> formFields = dbContext.OBDocumentMappings.Where(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId).ToList();

                foreach (OBDocumentMapping mappedFld in formFields)
                {
                    string docField = mappedFld.FormName;
                    string map_Table = string.Empty;

                    if (string.IsNullOrEmpty(mappedFld.DB_Table))
                    {
                        map_Table = "OBProcessDocumentUDF";
                    }
                    else
                    {
                        map_Table = mappedFld.DB_Table;
                    }

                    string map_Field = string.Empty;

                    if (string.IsNullOrEmpty(mappedFld.DB_Field))
                    {
                        map_Field = string.Empty;
                    }
                    else
                    {
                        map_Field = mappedFld.DB_Field;
                    }

                    string docFieldValue = docData[docField].Trim();

                    if (IsPDFformFieldEditable(mappedFld))
                    {
                        if (!string.IsNullOrEmpty(docFieldValue))
                        {
                            if (map_Table == OBDocSource.DocumentUDF)
                            {
                                OBProcessDocumentUDF udfRec;

                                if (map_Field == string.Empty)
                                {
                                    map_Field = UDFNewName(docId);

                                    OBDocumentMapping mapToUpdate = dbContext.OBDocumentMappings
                                        .FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.DocumentID == docId && x.FormName == docField);

                                    mapToUpdate.DB_Table = map_Table;
                                    mapToUpdate.DB_Field = map_Field;
                                    mapToUpdate.DB_RecNum = 0;
                                    dbContext.SaveChanges();

                                    udfRec = new OBProcessDocumentUDF
                                    {
                                        CompanyID = GlobalVariables.CompanyID,
                                        DocumentID = docId,
                                        EmployeeID = obEmployee,
                                        UDFName = docField,
                                        UDFType = mappedFld.FormFieldType,
                                        UDFValue = docFieldValue
                                    };

                                    dbContext.OBProcessDocumentUDFs.Add(udfRec);
                                }
                                else
                                {
                                    udfRec = dbContext.OBProcessDocumentUDFs
                                        .First(x => 
                                            x.CompanyID == GlobalVariables.CompanyID && 
                                            x.EmployeeID == obEmployee && 
                                            x.DocumentID == docId && 
                                            x.UDFName == docField);

                                    udfRec.UDFValue = docFieldValue;
                                }

                                dbContext.SaveChanges();
                            }
                            else
                            {
                                int recNbr = 0;

                                if (mappedFld.DB_RecNum == null)
                                {
                                    recNbr = 0;
                                }
                                else
                                {
                                    recNbr = mappedFld.DB_RecNum;
                                }

                                if (recNbr > 0)
                                {
                                    recNbr--;
                                }

                                OBProcessTaskField taskField = dbContext.OBProcessTaskFields
                                    .FirstOrDefault(x => 
                                        x.CompanyID == GlobalVariables.CompanyID && 
                                        x.PermanentTBL == map_Table && 
                                        x.PermanentFLD == map_Field && 
                                        x.EmployeeID == obEmployee);

                                OBProcessRecordDetail detRec = dbContext.OBProcessRecordDetails
                                    .Where(x => 
                                        x.CompanyID == GlobalVariables.CompanyID && 
                                        x.EmployeeID == obEmployee && 
                                        x.TaskID == taskField.TaskID && 
                                        x.FldName == taskField.FName)
                                    .OrderBy(x => x.RecordID)
                                    .Skip(recNbr)
                                    .First();

                                if (detRec != null)
                                {
                                    bool upd = false;

                                    if (detRec.ReadOnly == null)
                                    {
                                        upd = true;
                                    }
                                    else
                                    {
                                        upd = !detRec.ReadOnly;
                                    }

                                    if (upd)
                                    {
                                        detRec.FldValue = docFieldValue;
                                        dbContext.SaveChanges();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch
            {
                ModelState.AddModelError("savedoc", "Cannot save entered data");

                return View();
            }

            return RedirectToAction("Verify", new { id = docId });
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="docId"></param>
        /// <returns></returns>
        private bool IsPdfDocumentUpdatable(int id, int docId)
        {
            bool result = false;
            return result;
        }
    }
}