using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Web;
using System.Web.Mvc;
using System.Web.UI;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DataDrivenViewEngine.Models.Core;
using Ionic.Zip;

namespace DarwiNet2._0.Controllers
{
    public class W2ImportController : Controller
    {
        // GET: W2Import
        private const string F1095_Schema = "PREFIX-COMPANY-YEAR-PEO/ASO-CLIENT-EMPL";
        private const char F1095_Delimiter = '-';
        private const string W2Zip_Schema = "YEAR_COMPANY";
        private const char W2Zip_Delimiter = '_';
        private const string W2Zip_Password = "w1ll1b1rgel";
        private DnetEntities _dbContext;

        private const string LOCAL_HOST_W2_FOLDER = "\\\\dnet-new-web9\\D\\DNET_WEB\\LOCALTEST\\W2Import\\";
        
        protected override void Dispose(bool disposing)
        {
            _dbContext.Dispose();
            base.Dispose(disposing);
        }
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            _dbContext = new DnetEntities();
            
            base.Initialize(requestContext);
        }

        #region public
        public ActionResult Index()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.SystemW2Import);
            return View();
        }

        [HttpPost]
        public ActionResult Upload(FormCollection collection, HttpPostedFileBase file)
        {
            int companyId, year;
            string path, name, err, msg, result;
            try
            {
                err = string.Empty;
                result = string.Empty;
                if (file != null && file.ContentLength > 0)
                {
                    foreach (string files in Request.Files)
                    {
                        string err_msg = string.Empty;
                        var PostedFile = Request.Files[files];
                        string filename = Path.GetFileName(PostedFile.FileName);
                        if (IsW2FileValid(filename, out companyId, out year, out path, out err))
                        {
                            if (!Directory.Exists(path)) Directory.CreateDirectory(path);
                            name = Path.Combine(path, filename);
                            if (System.IO.File.Exists(name))
                            {
                                System.IO.File.Delete(name);
                            }
                            PostedFile.SaveAs(name);
                            if (!PrepareW2Import(name, path, companyId, year, out msg, out result)) err += msg;
                        }
                   }
                }
                if (err == string.Empty && file != null)
                {
                    TempData["Success"] = (string.IsNullOrEmpty(result)) ? Messages.Success.W2Uploaded : result;
                }
                else
                {
                    TempData["Error"] = err;
                    if (file == null)
                    {
                        TempData["Error"] = "Choose a file to Import";
                    }
                }
                return RedirectToAction("Index");
            }
            catch(Exception e)
            {
                TempData["Error"] = e.Message;
                return RedirectToAction("Index");
            }
        }
        #endregion
        #region private
        #region 1095
        private bool GetInfoFrom1095(string fname, out string client, out string emplId, out string year, out string form)
        {
            client = string.Empty;
            emplId = string.Empty;
            year = string.Empty;
            form = string.Empty;
            bool result = false;
            if(!string.IsNullOrEmpty(fname))
            {
                string extension = Path.GetExtension(fname);
                string name = Path.GetFileNameWithoutExtension(fname);
                if (name != string.Empty && extension.ToUpper() == ".PDF")
                {
                    result = true;
                    string[] schema = F1095_Schema.Split(F1095_Delimiter);
                    string[] parts = name.Split(F1095_Delimiter);
                    int n = (parts.Length >= schema.Length) ? schema.Length : parts.Length;
                    for (int i = 0; i < n; i++)
                    {
                        switch(schema[i])
                        {
                            case "CLIENT":
                                client = parts[i].Trim();
                                break;
                            case "EMPL":
                                emplId = parts[i].Trim();
                                break;
                            case "YEAR":
                                year = parts[i].Trim();
                                break;
                            case "PREFIX":   // 01/16/2020 DS TFS # 5716
                                form = parts[i].Trim().Replace("f", "");
                                break;
                        }
                    }
                }
            }
            return result;
        }

        // 03/18/2020 DS TFS # 5814
        private string SaveEE1095(int companyId, string path, string client, string fname, string emplId, int year, string form)
        {
            string sql = string.Empty;
            path = Path.Combine(path, client);
            if (System.IO.File.Exists(fname))
            {
                string name = Path.GetFileName(fname);
                string target_path = Path.Combine(path, Folders.f1095);
                if (!Directory.Exists(target_path)) Directory.CreateDirectory(target_path);
                target_path = Path.Combine(target_path, year.ToString());
                if (!Directory.Exists(target_path)) Directory.CreateDirectory(target_path);                
                string target_file = Path.Combine(target_path, name);
                if (System.IO.File.Exists(target_file)) System.IO.File.Delete(target_file);
                System.IO.File.Move(fname, target_file);
                sql = "UPDATE EmployeeW2Forms SET [Form" + form.ToUpper() + "] = '" + name + "' WHERE CompanyID = " + companyId.ToString() + " AND EmployeeID = '" + emplId + "' AND ReportingYear = " + year.ToString() + "\n";
            }
            return sql;
        }

        private void SaveEE1095_old(int companyId, string path, string client, string fname, string emplId, int year, string form)
        {
            path = Path.Combine(path, client);
            if (System.IO.File.Exists(fname))
            {
                string name = Path.GetFileName(fname);
                string target_path = Path.Combine(path, Folders.f1095);
                if (!Directory.Exists(target_path)) Directory.CreateDirectory(target_path);
                target_path = Path.Combine(target_path, year.ToString());
                if (!Directory.Exists(target_path)) Directory.CreateDirectory(target_path);                
                string target_file = Path.Combine(target_path, name);
                if (System.IO.File.Exists(target_file)) System.IO.File.Delete(target_file);
                System.IO.File.Move(fname, target_file);
                EmployeeW2Forms rec = this._dbContext.EmployeeW2Forms.FirstOrDefault(w => w.CompanyID == companyId && w.EmployeeID == emplId && w.ReportingYear == year);
                if (rec != null)
                {
                    switch (form.ToUpper())
                    {
                        case "1095B":
                            rec.Form1095B = name;
                            break;
                        case "1095C":
                            rec.Form1095C = name;
                            break;
                    }
                    
                    this._dbContext.SaveChanges();
                }
            }
        }

        private void CleanUpW2Import(string zipfile, string pattern)
        {
            if (System.IO.File.Exists(zipfile)) System.IO.File.Delete(zipfile);
            string file = pattern + ".txt";
            if (System.IO.File.Exists(file)) System.IO.File.Delete(file);
            file = pattern + "C.txt";
            if (System.IO.File.Exists(file)) System.IO.File.Delete(file);

        }

        private string Save1095Forms(int companyId, string company_path, string import_path)
        {
            string result = string.Empty;
            string sql = string.Empty;
            string client, emplId, year_str, frm;
            int year;
            if (Directory.Exists(import_path))
            {
                string [] f1095files = Directory.GetFiles(import_path);
                foreach(string form in f1095files)
                {
                    if (GetInfoFrom1095(form, out client, out emplId, out year_str, out frm))
                    {
                        if (int.TryParse(year_str, out year)) sql += SaveEE1095(companyId, company_path, client, form, emplId, year, frm); // 03/18/2020 DS TFS # 5814
                    }
                }
                if (!string.IsNullOrEmpty(sql)) ExecuteSQLQuery1(sql, out result); // 03/18/2020 DS TFS # 5814
            }
            else Directory.CreateDirectory(import_path);
            return result; // 03/18/2020 DS TFS # 5814
        }
        #endregion
        #region Prepare Import File
        private bool UnZipW2Import(string zipFile, string path, out string err_msg)
        {
            bool result = true;
            err_msg = string.Empty;
            FileInfo file = new FileInfo(zipFile);
            try
            {
                using (ZipFile improtZip = ZipFile.Read(zipFile))
                {
                    improtZip.Password = W2Zip_Password;
                    improtZip.FlattenFoldersOnExtract = true;
                    improtZip.ExtractAll(path, ExtractExistingFileAction.OverwriteSilently);
                }
            }
            catch (Exception e)
            {
                err_msg =  e.Message;
                result = false;
            }
            return result;
        }

        private string RemoteFileName(string file)
        {
            bool isLocal = System.Web.HttpContext.Current.Request.IsLocal;
            if (isLocal)
                return LOCAL_HOST_W2_FOLDER + Path.GetFileName(file);
            else
                return (string.IsNullOrEmpty(GlobalVariables.RemoteServerName)) ? file : GlobalVariables.RemoteServerName + file.Replace(":", "");

        }

        private bool UpdateW2ImportFile(string file, string company, bool addTab, out int rows, out string err_msg)
        {
            bool result = true;
            string prefix = (addTab) ? "-" : "";
            err_msg = string.Empty;
            rows = 0;
            if (System.IO.File.Exists(file))
            { 
                try
                {
                    string fname = Path.GetFileName(file);
                    string target = file.Replace(fname, fname.Replace("_" + company + ".txt", ".txt"));
                    bool isLocal = System.Web.HttpContext.Current.Request.IsLocal;
                    if (isLocal) target = LOCAL_HOST_W2_FOLDER + Path.GetFileName(target);
                    string[] lines = System.IO.File.ReadAllLines(file);

                    using (StreamWriter writer = new StreamWriter(target))
                    {
                        rows = lines.Length;
                        for (int i = 0; i < lines.Length; i++)
                        {
                            string line = prefix + company + "\t" + lines[i];
                            if (addTab) line += "\t\t\t";
                            writer.WriteLine(line);
                        }
                    }
                    if (System.IO.File.Exists(target))
                    {
                        System.IO.File.Delete(file);
                        //System.IO.File.Move(target, file);
                    }
                    else
                    {
                        err_msg = Messages.Errors.CannotUpdateFile + file;
                        result = false;
                    }
                }
                catch(Exception e)
                {
                    err_msg = e.Message;
                    result = false;
                }

            }
            else
            {
                err_msg = Messages.Errors.CannotFindFile + file;
                result = false;
            }
            return result;
        }

        private bool IsW2FileValid(string file, out int companyId, out int year, out string path, out string err_msg)
        {
            companyId = 0;
            year = 0;
            err_msg = string.Empty;
            path = string.Empty;

            string company= string.Empty;
            string year_str= string.Empty;

            if(string.IsNullOrEmpty(file))
            {
                err_msg = Messages.Errors.NoFileSelected;
                return false;
            }
            string extension = Path.GetExtension(file);
            string name = Path.GetFileNameWithoutExtension(file);
            if (extension.ToUpper() != ".ZIP")
            {
                err_msg = Messages.Errors.WrongFile;
                return false;
            }
            string[] schema = W2Zip_Schema.Split(W2Zip_Delimiter);
            string[] parts = name.Split(W2Zip_Delimiter);
            for (int i = 0; i < parts.Length; i++)
            {
                switch(schema[i])
                {
                    case "COMPANY":
                        company = parts[i].Trim();
                        break;
                    case "YEAR":
                        year_str = parts[i].Trim().Replace("W2","");
                        break;
                }
            }
            if (!int.TryParse(company, out companyId)) companyId = GlobalVariables.CompanyID;
            if (!int.TryParse(year_str, out year)) year = 0;
            if (year > 2000 && companyId > 0)
            {
                int id = companyId;
                Company companyRec = _dbContext.Companies.FirstOrDefault(c => c.CompanyID == id);
                if (companyRec == null)
                {
                    err_msg = "CompanyID '" + companyId.ToString() + "' doesn't exist.";
                    return false;
                }
                path = Path.Combine(GlobalVariables.DNetLocation, Folders.Assets, companyRec.DnetClientID + "." + companyRec.DnetCompanyID);
                return true;
            }
            err_msg = Messages.Errors.WrongFile;
            return false;
        }

        private int ExecuteSQLQuery1(string query, out string err_msg)
        {
            err_msg = string.Empty;
            try
            {
                using (SqlConnection connection = new SqlConnection(GlobalVariables.CompanyConnection))
                {
                    SqlCommand command = new SqlCommand(query, connection);
                    command.Connection.Open();
                    return command.ExecuteNonQuery();
                }
            }
            catch (Exception e)
            {
                err_msg = e.Message;
                return -1;
            }
        }

        private void InsertW2(int companyId, int year, string file, out int numee, out int numeec, out string err)
        {
            err = string.Empty;
            numee = 0;
            numeec = 0;
            int dummy;
            string err_msg;
            file = file.Replace(@"\", "|");
            string query = "DELETE FROM EmployeeW2Forms WHERE CompanyID = '-" + companyId.ToString() + "' AND ReportingYear = '" + year.ToString() + "'";
            if (ExecuteSQLQuery1(query, out err_msg) >= 0)
            {
                query = "BULK INSERT EmployeeW2Forms FROM '" + file + ".txt'";
                numee = ExecuteSQLQuery1(query.Replace("|", @"\"), out err_msg);
                if (!string.IsNullOrEmpty(err_msg)) err += err_msg + "<br />";
                if (numee >= 0)
                {
                    query = "DELETE FROM EmployeeW2FormCodes WHERE CompanyID = '" + companyId.ToString() + "' AND ReportingYear = '" + year.ToString() + "' AND EmployeeID IN (SELECT EmployeeID FROM EmployeeW2Forms WHERE CompanyID = '-" + companyId.ToString() + "' AND ReportingYear = '" + year.ToString() + "')";
                    dummy = ExecuteSQLQuery1(query, out err_msg);
                    if (!string.IsNullOrEmpty(err_msg)) err += err_msg + "<br />";
                    query = "DELETE FROM EmployeeW2Forms WHERE CompanyID = '" + companyId.ToString() + "' AND ReportingYear = '" + year.ToString() + "' AND EmployeeID IN (SELECT EmployeeID FROM EmployeeW2Forms WHERE CompanyID = '-" + companyId.ToString() + "' AND ReportingYear = '" + year.ToString() + "')";
                    dummy = ExecuteSQLQuery1(query, out err_msg);
                    if (!string.IsNullOrEmpty(err_msg)) err += err_msg + "<br />";
                    query = "UPDATE EmployeeW2Forms SET CompanyID = '" + companyId.ToString() + "' WHERE CompanyID = '-" + companyId.ToString() + "' AND ReportingYear = '" + year.ToString() + "'";
                    dummy = ExecuteSQLQuery1(query, out err_msg);
                    if (!string.IsNullOrEmpty(err_msg)) err += err_msg + "<br />";
                    query = "BULK INSERT EmployeeW2FormCodes FROM '" + file + "C.txt'";
                    numeec = ExecuteSQLQuery1(query.Replace("|", @"\"), out err_msg);
                    if (!string.IsNullOrEmpty(err_msg)) err += err_msg + "<br />";
                }
            }
            else err = err_msg;
        }

       /* private void BulkInsertW2(int companyId, int year, string file, out int numee, out int numeec, out string err)
        {
            numee = 0;
            numeec = 0;
            err = string.Empty;
            IDbCommand oaCommand = _dbContext.Connection.CreateCommand();
            oaCommand.CommandType = System.Data.CommandType.StoredProcedure;
            oaCommand.CommandText = "usp_W2_BULKINSERT";
            IDbDataParameter oaParamC = oaCommand.CreateParameter();
            oaParamC.ParameterName = "@company";
            oaParamC.DbType = DbType.Int32;
            oaParamC.Value = companyId;
            oaParamC.Direction = ParameterDirection.Input;
            oaCommand.Parameters.Add(oaParamC);
            IDbDataParameter oaParamY = oaCommand.CreateParameter();
            oaParamY.ParameterName = "@year";
            oaParamY.DbType = DbType.Int32;
            oaParamY.Value = year;
            oaParamY.Direction = ParameterDirection.Input;
            oaCommand.Parameters.Add(oaParamY);
            IDbDataParameter oaParamN = oaCommand.CreateParameter();
            oaParamN.ParameterName = "@file";
            oaParamN.DbType = DbType.String;
            oaParamN.Value = file;
            oaParamN.Direction = ParameterDirection.Input;
            oaCommand.Parameters.Add(oaParamN);
            IDbDataParameter oaParamNE = oaCommand.CreateParameter();
            oaParamNE.ParameterName = "@num_ee";
            oaParamNE.DbType = DbType.Int32;
            oaParamNE.Value = numee;
            oaParamNE.Direction = ParameterDirection.Output;
            oaCommand.Parameters.Add(oaParamNE);
            IDbDataParameter oaParamNEC = oaCommand.CreateParameter();
            oaParamNEC.ParameterName = "@num_eecodes";
            oaParamNEC.DbType = DbType.Int32;
            oaParamNEC.Value = numeec;
            oaParamNEC.Direction = ParameterDirection.Output;
            oaCommand.Parameters.Add(oaParamNEC);
            IDbDataParameter oaParamM = oaCommand.CreateParameter();
            oaParamM.ParameterName = "@err_msg";
            oaParamM.DbType = DbType.String;
            oaParamM.Value = err;
            oaParamM.Direction = ParameterDirection.Output;
            oaCommand.Parameters.Add(oaParamM);
            oaCommand.ExecuteNonQuery();
            _dbContext.SaveChanges();
        } */

        private void AddW2ImportData(int companyId, int year, string import_path, string prompt, int n_ee, int n_codes, out string err_msg, out string result_msg)
        {
            err_msg = string.Empty;
            result_msg = string.Empty;
            int n_ee_a = 0;
            int n_codes_a = 0;
            string file = Path.Combine(import_path, prompt);
            InsertW2(companyId, year, RemoteFileName(file), out n_ee_a, out n_codes_a, out err_msg);
            if (string.IsNullOrEmpty(err_msg))
                result_msg = Messages.Success.W2Uploaded + ": added " + n_ee_a.ToString() + " (from " + n_ee.ToString() + ")  W2 From records and " + n_codes_a.ToString() + " (from " + n_codes.ToString() + ") codes";
            else
                err_msg = Messages.Errors.ErrorOccurred + err_msg;

        }

        private bool PrepareW2Import(string zipfile, string company_path, int companyId, int year, out string err_msg, out string result_msg)
        {
            bool result = true;
            int input_recs = 0;
            int input_codes = 0;
            err_msg = string.Empty;
            result_msg = string.Empty;
            string import_path = Path.Combine(company_path, Folders.W2Import);
            string pattern = Path.Combine(import_path, year.ToString() + "W2").ToString();
            string suffix = "_" + companyId.ToString() + ".txt";
            if (!Directory.Exists(import_path)) Directory.CreateDirectory(import_path);
            if (!UnZipW2Import(zipfile, import_path, out err_msg)) return false;
            if (!UpdateW2ImportFile(pattern + suffix, companyId.ToString(), true, out input_recs, out err_msg)) return false;
            if (!UpdateW2ImportFile(pattern + "C" + suffix, companyId.ToString(), false, out input_codes, out err_msg)) return false;
            AddW2ImportData(companyId, year, import_path, pattern, input_recs, input_codes, out err_msg, out result_msg);
            Save1095Forms(companyId, company_path, import_path);
            CleanUpW2Import(zipfile, pattern);
            return string.IsNullOrEmpty(err_msg);
        }
        #endregion
        #endregion
    }
}