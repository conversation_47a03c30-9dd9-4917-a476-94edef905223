using DarwiNet2._0.Core;
using System;
using System.Web.Mvc;

namespace DarwiNet2._0.Controllers
{
    public abstract class DNetController : Controller
    {
        protected void SetAlertMessage(string type, string message)
        {
            TempData[type] = message;
        }

        protected JsonResult JsonSuccess(string message = "", object data = null, bool useTempData = false)
        {
            if (!string.IsNullOrEmpty(message) && useTempData)
                SetAlertMessage(AlertTypes.SUCCESS, message);

            var jsonResult = Json(new
            {
                status = AlertTypes.SUCCESS,
                message = message,
                data = data
            }, JsonRequestBehavior.AllowGet);

            jsonResult.MaxJsonLength = int.MaxValue;
            return jsonResult;
            }

        public JsonResult JsonError(string message = "", object data = null)
        {
            return Json(new
            {
                status = AlertTypes.ERROR,
                message = message,
                data = data
            }, JsonRequestBehavior.AllowGet);
        }

        protected JsonResult JsonInternalServerError(Exception ex)
        {
            //LogException(ex);
            return Json(new
            {
                status = AlertTypes.ERROR,
                message = AlertMessages.INTERNAL_SERVER_ERROR
            }, JsonRequestBehavior.AllowGet);
        }
    }
}