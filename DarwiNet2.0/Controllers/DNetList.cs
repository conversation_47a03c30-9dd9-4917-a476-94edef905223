using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;

namespace DarwiNet2._0.Controllers
{
    public class DNetList
    {
        private static DnetEntities dbCompany; 
            
        public static List<Code_Description> GetDDL(short dnetType)
        {
            dbCompany = new DnetEntities();
            List<Code_Description> result = new List<Code_Description>();
            switch(dnetType)
            {
                case DNetSpecFields.Departments:
                    result = dbCompany.Departments.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.Department1.StartsWith(GlobalVariables.Client)).OrderBy(i => i.Department1).Select(i => new Code_Description() { Code = i.Department1, Description = i.Department1 + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.Positions:
                    result = dbCompany.Positions.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.Position1.StartsWith("~") == false).OrderBy(i => i.Position1).Select(i => new Code_Description() { Code = i.Position1, Description = i.Position1 + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.Banks:
                    result = dbCompany.Banks.Where(i => i.CompanyID == GlobalVariables.CompanyID).OrderBy(i => i.BankName).Select(i => new Code_Description() { Code = i.BankName, Description = i.BankName + " (" + i.TransitNumber + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.Banks1:
                    result = dbCompany.Banks.Where(i => i.CompanyID == GlobalVariables.CompanyID).OrderBy(i => i.TransitNumber).Select(i => new Code_Description() { Code = i.TransitNumber, Description = i.TransitNumber + " (" + i.BankName + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.Benefits:
                    result = dbCompany.Benefits.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.Inactive != true).OrderBy(i => i.Benefit1).Select(i => new Code_Description() { Code = i.Benefit1, Description = i.Benefit1 + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.ClientPTO:
                    result = dbCompany.ClientPTOTypes.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.ClientID == GlobalVariables.Client).OrderBy(i => i.PTOType).Select(i => new Code_Description() { Code = i.PTOType.ToString(), Description = i.PTOType.ToString() + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.Deductions:
                    result = dbCompany.Deductions.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.Inactive != true).OrderBy(i => i.Deduction1).Select(i => new Code_Description() { Code = i.Deduction1, Description = i.Deduction1 + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.Lisenses:
                    result = dbCompany.LicenseTypes.Where(i => i.CompanyID == GlobalVariables.CompanyID).OrderBy(i => i.LicenseCertificationID).Select(i => new Code_Description() { Code = i.LicenseCertificationID, Description = i.LicenseCertificationID + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.LocalTaxes:
                    result = dbCompany.LocalTaxes.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.Inactive != true).OrderBy(i => i.LocalTax1).Select(i => new Code_Description() { Code = i.LocalTax1, Description = i.LocalTax1 + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.Paycodes:
                    result = dbCompany.Paycodes.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.Inactive != true).OrderBy(i => i.PayRecord).Select(i => new Code_Description() { Code = i.PayRecord, Description = i.PayRecord + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.SUTAStates:
                    result = dbCompany.SUTAStates.Where(i => i.CompanyID == GlobalVariables.CompanyID).OrderBy(i => i.StateCode).Select(i => new Code_Description() { Code = i.StateCode, Description = i.StateCode + " (" + i.StateName + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.States:
                    result = dbCompany.SUTAStates.Where(i => i.CompanyID == GlobalVariables.CompanyID).OrderBy(i => i.StateCode).Select(i => new Code_Description() { Code = i.StateCode, Description = i.StateCode + " (" + i.StateName + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.TrainingStatuses:
                    result = dbCompany.ClientTrainingStatuses.Where(i => i.ClientID == GlobalVariables.Client).OrderBy(i => i.TrainingStatus).Select(i => new Code_Description() { Code = i.TrainingStatus, Description = i.TrainingStatus + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.TrainingTypes:
                    result = dbCompany.ClientTrainingTypes.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.ClientID == GlobalVariables.Client).OrderBy(i => i.TrainingType).Select(i => new Code_Description() { Code = i.TrainingType, Description = i.TrainingType + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                case DNetSpecFields.WC:
                    result = dbCompany.WorkersCompCodes.OrderBy(i => i.WorkersComp).Select(i => new Code_Description() { Code = i.WorkersComp, Description = i.WorkersComp + " (" + i.Description + ")" }).ToList<Code_Description>();
                    break;
                    //5983 Bug
                case DNetSpecFields.Supervisors:
                    result = dbCompany.Employees.Where(i => i.CompanyID == GlobalVariables.CompanyID && i.ClientID == GlobalVariables.Client && i.Supervisor).OrderBy(i => i.EmployeeID).Select(i => new Code_Description() { Code = i.EmployeeID, Description = i.FirstName + " " + i.LastName + " (" + i.EmployeeID + ")" }).ToList<Code_Description>();
                    break;
            }
            dbCompany.Dispose();
            return result;
        }
    }
}