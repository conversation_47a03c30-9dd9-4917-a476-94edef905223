using NotificationType = DarwiNet2._0.Core.NotificationType;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Web.Mvc;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.Services.Email;
using DataDrivenViewEngine.Models.Core;
using DarwiNet2._0.ViewModels;
using DarwiNet2._0.Utilities;
using DarwiNet2._0.Services.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using System.Data;
using System.IO;
//using DarwiNet2._0.Controllers.Core;
using Excel = Microsoft.Office.Interop.Excel;



namespace DarwiNet2._0.Controllers.HR
{
    [IsSessionActive]
    public class HRController : Controller
    {
        private DnetEntities _dbContext;
        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            _dbContext = new DnetEntities();
            base.Initialize(requestContext);
        }
        // GET: HR
        #region PTO Plans
        public ActionResult PTO()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientPto);
            return View();
        }

        public ActionResult GetPTO_Read([DataSourceRequest] DataSourceRequest request)
        {
            return Json(GetPTO()
                .ToDataSourceResult(request));
        }

        public static IEnumerable<PTOGroup> GetPTO()
        {
            var model = new DnetEntities();

            List<ClientPTOType> clientPTOTypes = model.ClientPTOTypes.Where
                (c => c.CompanyID == GlobalVariables.CompanyID &&
                c.ClientID == GlobalVariables.Client).ToList();

            var securityEEs = GlobalVariables.SecurityEmployees;

            List<EmployeePTOType> employeePTOTypes = model.EmployeePTOTypes.Where(ept => ept.CompanyID == GlobalVariables.CompanyID).ToList();
            employeePTOTypes = employeePTOTypes.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            List<Employee> employees = model.Employees.Where(e => e.ClientID == GlobalVariables.Client && e.CompanyID == GlobalVariables.CompanyID).ToList();
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var list = (from cpt in clientPTOTypes
                        join ept in employeePTOTypes on
                            new { cpt.CompanyID, cpt.ClientID, cpt.PTOType } equals
                            new { ept.CompanyID, ClientID = GlobalVariables.Client, ept.PTOType }
                        select new { cpt.PTOType, cpt.Description, cpt.AccrualMethod, cpt.AccrualAmount, cpt.AnniversaryMethod, cpt.Inactive }).Distinct().ToList();

            var theView = new List<PTOGroup>();
            foreach (var item in list)
            {
                theView.Add(new PTOGroup
                {
                    PTOID = item.PTOType,
                    PTOPlanID = FieldTranslation.GetEnumDescription(typeof(enPTOTypes), item.PTOType),
                    Description = item.Description,
                    AccrualMethod = FieldTranslation.GetEnumDescription(typeof(enPTOAccrualMethods), (int)item.AccrualMethod),
                    AccrualAmount = String.Format("{0:n2}", item.AccrualAmount),
                    AnniversaryMethod = FieldTranslation.GetEnumDescription(typeof(enPTOAnniversaryMethods), (int)item.AnniversaryMethod),
                    Inactive = item.Inactive
                }
                );
            }
            return theView;
        }

        public ActionResult GetEEPTO_Read([DataSourceRequest] DataSourceRequest request, string P, bool AllDetail = false)
        {

            return Json(GetEEPTO(P, AllDetail)
                .ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        public static IEnumerable<PTOHR> GetEEPTO(string p, bool AllDetail)
        {
            int? value = FieldTranslation.GetEnumValueFromDesc(typeof(enPTOTypes), p);
            var TheView = new List<PTOHR>();
            if (value != null || p == "Dummy")
            {
                var model = new DnetEntities();

                List<ClientPTOType> clientPTOTypes = model.ClientPTOTypes.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == GlobalVariables.Client).ToList();

                var securityEEs = GlobalVariables.SecurityEmployees;

                List<EmployeePTOType> employeePTOTypes = model.EmployeePTOTypes.ToList();
                employeePTOTypes = employeePTOTypes.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

                List<Employee> employees = model.Employees.ToList();
                employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

                var list = from cpt in clientPTOTypes
                           join ept in employeePTOTypes on
                                           new { cpt.CompanyID, cpt.ClientID, cpt.PTOType } equals
                                           new { ept.CompanyID, ClientID = GlobalVariables.Client, ept.PTOType }
                           join emp in employees on
                                           new { ept.CompanyID, ept.EmployeeID } equals
                                           new { emp.CompanyID, emp.EmployeeID }
                           where emp.CompanyID == GlobalVariables.CompanyID && emp.ClientID == GlobalVariables.Client && emp.Inactive == false
                           select new { emp.EmployeeID, emp.FirstName, emp.LastName, ept.AvailableHours, ept.Accrued, emp.Inactive, ept.PTOType, ept.YTDHoursAccrued, ept.PTOAmountTaken };

                if (!AllDetail) list = list.Where(x => x.PTOType == value);

                foreach (var item in list)
                {
                    var pending = PendingRequests(item.EmployeeID, item.PTOType);
                    var approved = ApprovedRequests(item.EmployeeID, item.PTOType, 0);

                    TheView.Add(new PTOHR
                    {
                        EmployeeID = FieldTranslation.EmployeeIDFormatted(item.EmployeeID),
                        EmployeeName = item.FirstName + ' ' + item.LastName,
                        Used = String.Format("{0:N}", ((item.PTOAmountTaken ?? 0) / 100.0)),
                        Available = String.Format("{0:N}", ((item.AvailableHours ?? 0) / 100.0)),
                        Accrued = String.Format("{0:N}", ((item.YTDHoursAccrued ?? 0) / 100.0)),
                        PTOTypeInt = item.PTOType,
                        PTOType = FieldTranslation.GetEnumDescription(typeof(enPTOTypes), item.PTOType),
                        Inactive = Convert.ToBoolean(item.Inactive),
                        Pending = string.Format("{0:n2}", pending),
                        Approved = string.Format("{0:n2}", approved)
                    });
                }
            }

            return TheView;
        }

        public ActionResult PTOHistory()
        {
            return View();
        }

        #endregion PTO Plans

        #region PTO Requests ***Still needs work***



        public ActionResult AddPTORequest(string e, int p)
        {
            List<string> ee = AvailablePTORequestsEE(GlobalVariables.Client);
            var emps =
                _dbContext.Employees.Where(
                    emp => emp.CompanyID == GlobalVariables.CompanyID && emp.ClientID == GlobalVariables.Client && !emp.Inactive)
                    .ToList();
            emps = emps.Where(emp => GlobalVariables.SecurityEmployees.Any(sec => sec == emp.EmployeeID)).ToList();
            emps = emps.Where(emp => ee.Any(empl => empl == emp.EmployeeID)).ToList();
            emps.Insert(0, new Employee { EmployeeID = string.Empty, FirstName = string.Empty, LastName = string.Empty });
            ViewBag.RequestEEs = emps;
            ViewBag.EmployeeID = e;
            ViewBag.SelectedPTOType = p;
            if (!string.IsNullOrEmpty(e))
            {
                var eePtoTypes = _dbContext.EmployeePTOTypes.Where(ept => ept.CompanyID == GlobalVariables.CompanyID && ept.EmployeeID == e).ToList();
                eePtoTypes.Insert(0, new EmployeePTOType { EmployeeID = e, PTOType = 0 });
                if (eePtoTypes.Any())
                {
                    foreach (var item in eePtoTypes)
                    {
                        item.dispType = FieldTranslation.GetEnumDescription(typeof(enPTOTypes), item.PTOType);
                    }
                }
                ViewBag.EEPTOTypes = eePtoTypes;
                if (p != 0)
                {
                    var thisPTOType = eePtoTypes.FirstOrDefault(ept => ept.CompanyID == GlobalVariables.CompanyID && ept.EmployeeID == e && ept.PTOType == p);
                    if (thisPTOType != null)
                    {
                        double ptoTaken = Convert.ToDouble(thisPTOType.PTOAmountTaken ?? 0) / (double)100;
                        ViewBag.PTOID = FieldTranslation.GetEnumDescription(typeof(enPTOTypes), thisPTOType.PTOType);
                        ViewBag.PTODesc = thisPTOType.Description;
                        ViewBag.Available = string.Format("{0:n2}", Convert.ToDouble(thisPTOType.AvailableHours ?? 0) / 100);
                        ViewBag.Used = string.Format("{0:n2}", ptoTaken);
                        ViewBag.Pending = PendingRequests(e, p);
                        ViewBag.Approved = ApprovedRequests(e, p, ptoTaken);
                    }
                }
            }
            ViewBag.StartDate = EEPayPeriodDate(e); //12/27/2017 DS TFS # 2856
            PTORequest newReq = new PTORequest
            {
                CompanyID = GlobalVariables.CompanyID,
                ClientID = GlobalVariables.Client,
                EmployeeID = e,
                PTOType = p
            };
            return View(newReq);
        }

        [HttpPost]
        public ActionResult AddPTORequest(PTORequest newReq, FormCollection form)
        {
            // 11/1/2017 DS TFS # 2856: isValid = false if requests previous dates
            bool isValid;
            newReq.TotalRequestedTime = GetWorkHours(newReq, out isValid);
            if (isValid)
            {
                newReq.CreatedBy = GlobalVariables.DNETOwnerID;
                newReq.DateCreated = DateTime.Now;
                _dbContext.PTORequests.Add(newReq);
                _dbContext.SaveChanges();
                SendNotification(newReq.id);
            }
            else
                TempData["Error"] = "You cannot request PTO for previous pay period dates.";
            return RedirectToAction("PTORequests", "HR");
        }

        public static decimal PendingRequests(string e, int p)
        {
            var model = new DnetEntities();
            var eeReqs = model.PTORequests.Where(ept => ept.CompanyID == GlobalVariables.CompanyID && ept.EmployeeID == e && ept.PTOType == p && ept.Status == TimeRequestStatus.Pending).ToList();
            if (eeReqs.Count > 0)
            {
                return eeReqs.Sum(ept => ept.TotalRequestedTime);
            }
            return 0;
        }

        public static decimal ApprovedRequests(string e, int p, double t)
        {
            try
            {
                decimal result = decimal.Zero;
                var model = new DnetEntities();
                var eeReqs = model.PTORequests.Where(ept => ept.CompanyID == GlobalVariables.CompanyID && ept.EmployeeID == e && ept.PTOType == p && ept.Status == TimeRequestStatus.Accepted).ToList();
                if (eeReqs.Count > 0)
                {
                    result = eeReqs.Sum(ept => ept.TotalRequestedTime) - (decimal)t;
                    if (result < decimal.Zero) result = decimal.Zero;
                }
                return result;
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
                throw;
            }

        }

        public ActionResult PTORequests()
        {
            var vm = new PTORequestsViewModel();
            vm.Departments = _dbContext.ClientDivisionDetails.Where(cdd => cdd.CompanyID == GlobalVariables.CompanyID && cdd.ClientID == GlobalVariables.Client).ToList().Select(cdd => new Code_Description { Code = cdd.Department, Description = FieldTranslation.GetDepartmentDesciption(cdd.Department) + "(" + cdd.Department + ")" }).ToList();
            //get list of employee ids that this client use has Department/Division security to be able to see...
            var securityEEs = GlobalVariables.SecurityEmployees;
            // get all ees for client
            var ees = _dbContext.Employees.Where(emp => emp.CompanyID == GlobalVariables.CompanyID && emp.ClientID == GlobalVariables.Client).Select(e => new { e.EmployeeID, e.FirstName, e.LastName }).ToList();
            //now filter list down to only include viewable employees
            if (securityEEs.Count != ees.Count)
            {
                ees = ees.Where(emp => securityEEs.Any(eid => eid == emp.EmployeeID)).ToList();
            }

            //finaly get just 'Code_Description' object
            var fltrEEs = ees.Select(emp => new Code_Description { Code = emp.EmployeeID, Description = emp.FirstName + " " + emp.LastName + " (" + emp.EmployeeID + ")" }).ToList();
            vm.Employees = fltrEEs;
            vm.PTOPlans = _dbContext.ClientPTOTypes.Where(cpt => cpt.CompanyID == GlobalVariables.CompanyID && cpt.ClientID == GlobalVariables.Client).Select(cpt => new Code_Description { Code = cpt.PTOType.ToString(), Description = cpt.Description }).ToList();

            // 04/12/16 DS bug # 689: user can view just pto request he/she can approve
            //List<PTORequests> theRequests = MyRequests(true);
            //List<PTORequests> theRequests = _dbContext.PTORequests.Where(pt => pt.CompanyID == GlobalVariables.CompanyID && pt.ClientID == GlobalVariables.Client && pt.Status ==0).ToList();
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientPto);
            List<string> ee = AvailablePTORequestsEE(GlobalVariables.Client);
            vm.CanAdd = ee.Any();
            return View(vm);
            //return View(theRequests);
        }

        public ActionResult PTORequestsHist()
        {
            ViewBag.Departments = _dbContext.ClientDivisionDetails.Where(cdd => cdd.CompanyID == GlobalVariables.CompanyID && cdd.ClientID == GlobalVariables.Client).ToList().Select(cdd => new Code_Description { Code = cdd.Department, Description = FieldTranslation.GetDepartmentDesciption(cdd.Department) + "(" + cdd.Department + ")" }).ToList();
            //get list of employee ids that this client use has Department/Division security to be able to see...
            var securityEEs = GlobalVariables.SecurityEmployees;
            // get all ees for client
            var ees = _dbContext.Employees.Where(emp => emp.CompanyID == GlobalVariables.CompanyID && emp.ClientID == GlobalVariables.Client).ToList();
            //now filter list down to only include viewable employees
            ees = ees.Where(emp => securityEEs.Any(eid => eid == emp.EmployeeID)).ToList();
            //finaly get just 'Code_Description' object
            var fltrEEs = ees.Select(emp => new Code_Description { Code = emp.EmployeeID, Description = emp.FirstName + " " + emp.LastName + " (" + emp.EmployeeID + ")" }).ToList();
            ViewBag.Employees = fltrEEs;
            ViewBag.PTOPlans = _dbContext.ClientPTOTypes.Where(cpt => cpt.CompanyID == GlobalVariables.CompanyID && cpt.ClientID == GlobalVariables.Client).Select(cpt => new Code_Description { Code = cpt.PTOType.ToString(), Description = cpt.Description }).ToList();
            // 04/12/16 DS bug # 689: user can view just pto request he/she can approve
            List<PTORequest> theRequests = MyRequests(false);
            //List<PTORequests> theRequests = _dbContext.PTORequests.Where(pt => pt.CompanyID == GlobalVariables.CompanyID && pt.ClientID == GlobalVariables.Client && pt.Status != 0).ToList();
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientPto);
            return View(theRequests);
        }

        public ActionResult PTORequest_Read([DataSourceRequest] DataSourceRequest request)
        {
            // 04/12/16 DS bug # 689: user can view just pto request he/she can approve
            List<PTORequest> theRequests = MyRequests(true);
            //List<PTORequests> theRequests = _dbContext.PTORequests.Where(pt => pt.CompanyID == GlobalVariables.CompanyID && pt.ClientID == GlobalVariables.Client && pt.Status == 0).ToList();
            var returnRequests = new List<PTORequestsVM>();
            var securityEEs = GlobalVariables.SecurityEmployees;
            List<Employee> emps = _dbContext.Employees.Where(x => x.CompanyID == GlobalVariables.CompanyID && x.ClientID == GlobalVariables.Client && !x.Inactive).ToList();
            emps = emps.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            theRequests = theRequests.Where(tr => securityEEs.Any(se => se == tr.EmployeeID)).ToList();
            foreach (var req in theRequests)
            {

                var ee = emps.FirstOrDefault(ep => ep.EmployeeID == req.EmployeeID);
                var eePTO = _dbContext.EmployeePTOTypes.FirstOrDefault(pt => pt.EmployeeID == req.EmployeeID && pt.CompanyID == GlobalVariables.CompanyID && pt.PTOType == req.PTOType);
                if (eePTO == null)
                {
                    eePTO = _dbContext.EmployeePTOTypes.FirstOrDefault(pt => pt.EmployeeID == req.EmployeeID && pt.CompanyID == GlobalVariables.CompanyID);
                    if (eePTO != null)
                    {
                        req.PTOType = eePTO.PTOType;
                        _dbContext.SaveChanges();
                    }
                }
                if (ee != null && eePTO != null)
                {
                    var pending = PendingRequests(ee.EmployeeID, req.PTOType);
                    var approved = ApprovedRequests(ee.EmployeeID, req.PTOType, 0);
                    if (eePTO != null)
                        returnRequests.Add(
                            new PTORequestsVM
                            {
                                Comments = req.Comments ?? string.Empty,
                                EmployeeName = (ee != null) ? ee.FirstName + " " + ee.LastName : string.Empty,
                                dispType = GetPTODisplayType(ee.CompanyID, req.PTOType, ee.ClientID),
                                CompanyID = req.CompanyID,
                                EmployeeID = req.EmployeeID,
                                id = req.id,
                                ClientID = req.ClientID,
                                Department = (ee != null) ? ee.Department : string.Empty,
                                AllDay = req.AllDay,
                                EndDate = (req.EndDate != null)
                                    ? new DateTime(req.EndDate.Year, req.EndDate.Month, req.EndDate.Day, 12,
                                        req.EndDate.Minute, req.EndDate.Second, DateTimeKind.Utc)
                                    : req.EndDate, //.ToUniversalTime(),eechk.CheckDate, req.EndDate,
                                EndTime = req.EndTime ?? "N/A",
                                PTOType = req.PTOType,
                                StartDate = (req.StartDate != null)
                                    ? new DateTime(req.StartDate.Year, req.StartDate.Month, req.StartDate.Day, 12,
                                        req.StartDate.Minute, req.StartDate.Second, DateTimeKind.Utc)
                                    : req.StartDate, //req.StartDate,
                                StartTime = req.StartTime ?? "All Day",
                                Status = req.Status,
                                TotalRequestedTime = req.TotalRequestedTime,
                                dispStatus = FieldTranslation.GetEnumDescription(typeof(enPTOStatus), req.Status),
                                dispAllDay = (req.AllDay) ? "Yes" : "No",
                                dispSDate = req.StartDate.ToShortDateString(),
                                dispEDate = req.EndDate.ToShortDateString(),
                                AvailableHrs = string.Format("{0:n2}",
                                    Convert.ToDouble(eePTO.AvailableHours ?? 0) / 100),
                                AmountTaken =
                                    string.Format("{0:n2}", Convert.ToDouble(eePTO.PTOAmountTaken ?? 0) / 100),
                                Pending = string.Format("{0:n2}", pending),
                                Approved = string.Format("{0:n2}", approved),
                                VerifiedBy = req.VerifiedBy,
                                DateVerified = req.DateVerified
                            });
                }
            }
            return Json(returnRequests.ToDataSourceResult(request));
        }

        public ActionResult PTORequestHist_Read([DataSourceRequest] DataSourceRequest request)
        {
            // 04/12/16 DS bug # 689: user can view just pto request he/she can approve
            List<PTORequest> theRequests = MyRequests(false);
            var returnRequests = new List<PTORequestsVM>();
            var securityEEs = GlobalVariables.SecurityEmployees;

            foreach (var req in theRequests)
            {
                List<Employee> emps = _dbContext.Employees.Where(x => x.CompanyID == req.CompanyID && x.EmployeeID == req.EmployeeID).ToList();
                var ee = emps.FirstOrDefault(s => securityEEs.Any(e => e == s.EmployeeID));
                bool allowDelete = false;
                var timePunches = _dbContext.EmployeeTimePunches.Where(x => x.PTORequestID == req.id);
                if (timePunches.Any() && timePunches.All(p => p.TimeSheetID == 0))
                    allowDelete = true;

                if (ee != null)
                {
                    var eePTO = _dbContext.EmployeePTOTypes.FirstOrDefault(pt => pt.EmployeeID == req.EmployeeID);
                    var pending = PendingRequests(ee.EmployeeID, req.PTOType);
                    if (eePTO != null)
                    {
                        var approved = ApprovedRequests(ee.EmployeeID, req.PTOType, Convert.ToDouble(eePTO.PTOAmountTaken ?? 0) / 100);
                        returnRequests.Add(
                            new PTORequestsVM
                            {
                                Comments = req.Comments ?? string.Empty,
                                EmployeeName = ee.FirstName + " " + ee.LastName,
                                dispType = GetPTODisplayType(ee.CompanyID, req.PTOType, ee.ClientID),
                                CompanyID = req.CompanyID,
                                EmployeeID = req.EmployeeID,
                                id = req.id,
                                ClientID = req.ClientID,
                                Department = ee.Department,
                                AllDay = req.AllDay,
                                EndDate = new DateTime(req.EndDate.Year, req.EndDate.Month, req.EndDate.Day, 12, req.EndDate.Minute, req.EndDate.Second, DateTimeKind.Utc),
                                EndTime = req.EndTime ?? "N/A",
                                PTOType = req.PTOType,
                                StartDate = new DateTime(req.StartDate.Year, req.StartDate.Month, req.StartDate.Day, 12, req.StartDate.Minute, req.StartDate.Second, DateTimeKind.Utc),
                                StartTime = req.StartTime ?? "All Day",
                                Status = req.Status,
                                TotalRequestedTime = req.TotalRequestedTime,
                                dispStatus = FieldTranslation.GetEnumDescription(typeof(enPTOStatus), req.Status),
                                dispAllDay = (req.AllDay) ? "Yes" : "No",
                                dispSDate = req.StartDate.ToShortDateString(),
                                dispEDate = req.EndDate.ToShortDateString(),
                                AllowDelete = allowDelete ? "block" : "none",
                                AvailableHrs = string.Format("{0:n2}", Convert.ToDouble(eePTO.AvailableHours ?? 0) / 100),
                                AmountTaken = string.Format("{0:n2}", Convert.ToDouble(eePTO.PTOAmountTaken ?? 0) / 100),
                                Pending = string.Format("{0:n2}", pending),
                                Approved = string.Format("{0:n2}", approved),
                                ClientNote=req.ClientNote,
                                VerifiedBy = req.VerifiedBy,
                                DateVerified = req.DateVerified
                            });
                    }
                }

            }

            return Json(returnRequests.ToDataSourceResult(request));
        }



        // 04/12/16 DS bug # 689: user can view just pto request he/she can approve
        public List<PTORequest> MyRequests(bool showPending)
        {
            //DG-7457-12/3/2021-Get current year data and order by new to old added.
            List<PTORequest> requests = (showPending) ?
                _dbContext.PTORequests
                    .Where(pt =>
                        pt.CompanyID == GlobalVariables.CompanyID &&
                        pt.ClientID == GlobalVariables.Client &&
                        pt.Status == 0).OrderByDescending(pt => pt.EndDate)
                    .ToList() :
                _dbContext.PTORequests
                    .Where(pt =>
                        pt.CompanyID == GlobalVariables.CompanyID &&
                        pt.ClientID == GlobalVariables.Client &&
                        pt.Status != 0 && pt.EndDate.Year >= DateTime.Now.Year - 1).OrderByDescending(pt => pt.EndDate)
                    .ToList();

            List<string> ee = AvailablePTORequestsEE(GlobalVariables.Client);

            return requests.Where(r => ee.Any(e => e == r.EmployeeID)).ToList();
        }



        public static List<string> PTOSupervisors(string client)
        {
            if (string.IsNullOrEmpty(client)) client = GlobalVariables.Client;
            try
            {
                IUserRoleClientEmployeeAssignmentService userRoleClientEmployeeAssignmentService = new UserRoleClientEmployeeAssignmentService();
                return userRoleClientEmployeeAssignmentService.GetPTOSupervisorUserIds(GlobalVariables.CompanyID, client);

                //var model = new DnetEntities();
                //return model.UserRoleClientEmployeeAssignments.Where(u => u.CompanyID == GlobalVariables.CompanyID && u.ClientID == client && string.IsNullOrEmpty(u.EmployeeID) && u.ApproveAllPTO).OrderBy(u => u.UserID).Select(u => u.UserID).ToList();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return new List<string>();
            }
        }

        private List<string> PTODepartmentManager(string client, string dept)
        {
            List<string> managers = new List<string>();
            bool useDivision = false;
            string div = string.Empty;
            if (string.IsNullOrEmpty(client)) client = GlobalVariables.Client;
            DarwinetSetup setup = _dbContext.DarwinetSetups.FirstOrDefault(s => s.CompanyID == GlobalVariables.CompanyID && s.ClientID == client);
            if (setup != null) useDivision = ClientUtilities.IsDivisionSecurityEnabled(GlobalVariables.CompanyID, GlobalVariables.Client, GlobalVariables.CurrentUser.UserID);
            try
            {
                if (useDivision)
                {
                    div = _dbContext.ClientDivisionDetails.First(d => d.CompanyID == GlobalVariables.CompanyID && d.ClientID == client && d.Department == dept).DivisionID;
                    return _dbContext.UserDeptDivSecurities.Where(u => u.CompanyID == GlobalVariables.CompanyID && u.ClientID == client && (u.AllAccess || u.ApprovePTO) && u.DivisionID == div).OrderBy(u => u.UserID).Select(u => u.UserID).ToList();
                }
                else
                    return _dbContext.UserDeptDivSecurities.Where(u => u.CompanyID == GlobalVariables.CompanyID && u.ClientID == client && (u.AllAccess || u.ApprovePTO) && u.Department == dept).OrderBy(u => u.UserID).Select(u => u.UserID).ToList();
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return new List<string>();
            }
        }
        //DG-12/2/2021-7457-optimization PTO.
        public static List<string> AvailablePTORequestsEE(string client)
        {
            IEmployeeService employeeService = new EmployeeService();
            List<string> ee = new List<string>();
            var employees = employeeService.GetActiveEmployees(GlobalVariables.CompanyID, client);
            //var ees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == client && !e.Inactive).Select(e => new { e.EmployeeID }).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            ee = employees.Where(employee => securityEEs.Contains(employee)).ToList();
            //ee = ees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).Select(e => e.EmployeeID).ToList();
            return ee;
        }
        private bool CanApproveEmployee(string emplId)
        {
            bool result = false;
            Employee ee = _dbContext.Employees.FirstOrDefault(e => e.CompanyID == GlobalVariables.CompanyID && e.EmployeeID == emplId && !e.Inactive);
            if (ee != null)
            {
                result = PTODepartmentManager(ee.ClientID, ee.Department).Exists(element => element.ToUpper() == GlobalVariables.DNETOwnerID.ToUpper());
                if (!result) result = PTOSupervisors(ee.ClientID).Exists(element => element.ToUpper() == GlobalVariables.DNETOwnerID.ToUpper());
            }
            return result;
        }

        // 04/12/16 DS - end changes for bug #689
        public string GetPTODisplayType(int co, int p, string cl)
        {
            var clientPtOs = _dbContext.ClientPTOTypes.FirstOrDefault(cpt => cpt.CompanyID == co && cpt.ClientID == cl && cpt.PTOType == p);
            if (clientPtOs == null) return FieldTranslation.GetEnumDescription(typeof(enPTOTypes), p);
            return clientPtOs.Description;
        }

        public ActionResult ApprovePTO(int id)
        {
            ViewBag.ShowError = false;
            PTORequest thisrequest = _dbContext.PTORequests.FirstOrDefault(pt => pt.id == id);
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientPto);
            if (thisrequest != null)
            {
                if (CanApproveEmployee(thisrequest.EmployeeID))
                {
                    Employee thisEmployee = _dbContext.Employees.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID && x.ClientID == GlobalVariables.Client && x.EmployeeID == thisrequest.EmployeeID);
                    DarwiNetCode ptoType = _dbContext.DarwiNetCodes.FirstOrDefault(x => x.CodeType == "PTOTypes" && x.Value == thisrequest.PTOType);

                    PtoRequestApproval request = new PtoRequestApproval();
                    request.PtoRequestID = thisrequest.id;
                    request.Department = thisEmployee != null ? thisEmployee.Department : string.Empty;
                    request.PtoType = ptoType != null ? string.Format("{0}{1}", ptoType.CodeText, string.IsNullOrEmpty(ptoType.CodeDescription) ? string.Empty : " - " + ptoType.CodeDescription) : string.Empty;
                    request.StartDate = thisrequest.StartDate.ToShortDateString();
                    request.EndDate = thisrequest.EndDate.ToShortDateString();
                    request.IsAllDay = thisrequest.AllDay;
                    request.StartTime = thisrequest.StartTime;
                    request.EndTime = thisrequest.EndTime;
                    request.Comment = thisrequest.Comments;
                    var payType = MapPTOType(thisrequest.PTOType);
                    request.EmployeePaycodes = _dbContext.EmployeePaycodes
                        .Where(c => c.CompanyID == GlobalVariables.CompanyID && c.EmployeeID == thisrequest.EmployeeID && !c.Inactive && c.PayType == payType)
                        .OrderBy(c => c.PayRecord).AsEnumerable()
                        .Select(c => new SelectListItem { Text = c.PayRecord, Value = c.PayRecord });

                    return View(request);
                }
                else
                {
                    TempData["ApproveError"] = Messages.Errors.InsufficientPrivileges;
                    ViewBag.ShowError = true;
                    return View();
                }
            }
            else
            {
                TempData["ApproveError"] = Messages.Errors.CantFindRecord;
                ViewBag.ShowError = true;
                return View();
            }

        }

        [HttpPost]
        public ActionResult ApprovePTO(int ptoRequestID, string note, string paycode)
        {
            PTORequest thisrequest = _dbContext.PTORequests.FirstOrDefault(pt => pt.id == ptoRequestID);
            if (thisrequest != null && !string.IsNullOrEmpty(paycode)) // 06/09/2017 DS TFS # 2579 - cannot accept PTO request without PTO Code
            {
                thisrequest.PTOCode = paycode;
                thisrequest.ClientNote = note;
                thisrequest.Status = 1;
                thisrequest.VerifiedBy = GlobalVariables.DNETOwnerID;
                thisrequest.DateVerified = DateTime.Now;
                _dbContext.SaveChanges();
                SendNotification(thisrequest.id);
            }
            return RedirectToAction("GetPTORequest", "TimePunches", new { id = ptoRequestID });
        }

        public ActionResult DenyPTO(int id)
        {
            ViewBag.ShowError = false;
            PTORequest thisrequest = _dbContext.PTORequests.FirstOrDefault(pt => pt.id == id);
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientPto);
            if (thisrequest != null)
            {
                if (CanApproveEmployee(thisrequest.EmployeeID))
                    return View(thisrequest);
                else
                {
                    TempData["DenyError"] = Messages.Errors.InsufficientPrivileges;
                    ViewBag.ShowError = true;
                    return View();
                }
            }
            else
            {
                TempData["DenyError"] = Messages.Errors.CantFindRecord;
                ViewBag.ShowError = true;
                return View();
            }

        }

        [HttpPost]
        public ActionResult DenyPTO(int id, string note)
        {
            PTORequest thisrequest = _dbContext.PTORequests.FirstOrDefault(pt => pt.id == id);
            if (thisrequest != null)
            {
                thisrequest.ClientNote = note;
                thisrequest.Status = 2;
                thisrequest.VerifiedBy = GlobalVariables.DNETOwnerID;
                thisrequest.DateVerified = DateTime.Now;
                _dbContext.SaveChanges();
                SendNotification(thisrequest.id);
            }
            return RedirectToAction("PTORequests");
        }

        public ActionResult EditPTO(int id)
        {
            PTORequest thisRequest = _dbContext.PTORequests.FirstOrDefault(pt => pt.id == id && pt.ClientID == GlobalVariables.Client && pt.CompanyID == GlobalVariables.CompanyID);
            string pto = string.Empty;
            if (thisRequest != null) pto = EEPTODescription(thisRequest.EmployeeID, thisRequest.PTOType);
            ViewBag.PTODescr = pto;
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientPto);
            return View(thisRequest);
        }

        [HttpPost]
        public ActionResult EditPTO(PTORequest ed, FormCollection form)
        {
            bool isValid;
            PTORequest OrigRequest = _dbContext.PTORequests.FirstOrDefault(pt => pt.id == ed.id);
            if (OrigRequest != null)
            {
                OrigRequest.StartDate = ed.StartDate;
                OrigRequest.EndDate = ed.EndDate;
                OrigRequest.AllDay = ed.AllDay;
                OrigRequest.StartTime = ed.StartTime;
                OrigRequest.EndTime = ed.EndTime;
                OrigRequest.Comments = ed.Comments;
                //OrigRequest.TotalRequestedTime = ed.TotalRequestedTime;
                OrigRequest.Status = ed.Status;
                OrigRequest.TotalRequestedTime = GetWorkHours(OrigRequest, out isValid);
                if (!isValid && !OrigRequest.ClientNote.Contains("Invalid request:")) OrigRequest.ClientNote = "Invalid request: the request uses invalid dates range or uses just holiday/weeked dates! " + OrigRequest.ClientNote;

                _dbContext.SaveChanges();
            }
            return RedirectToAction("PTORequests", "HR");
        }

        private string EEPTODescription(string emplId, int type)
        {
            EmployeePTOType rec = _dbContext.EmployeePTOTypes.FirstOrDefault(p => p.CompanyID == GlobalVariables.CompanyID && p.EmployeeID == emplId && p.PTOType == type);
            return (rec != null) ? rec.Description : string.Empty;
        }

        private int MapPTOType(int ptotype)
        {
            int codetype = 0;
            switch (ptotype)
            {
                case PTOTypes.One:
                    codetype = PayTypes.Vacation;
                    break;
                case PTOTypes.Two:
                    codetype = PayTypes.Sick;
                    break;
                case PTOTypes.PTO1:
                    codetype = PayTypes.PTO1;
                    break;
                case PTOTypes.PTO2:
                    codetype = PayTypes.PTO2;
                    break;
                case PTOTypes.PTO3:
                    codetype = PayTypes.PTO3;
                    break;
                case PTOTypes.PTO4:
                    codetype = PayTypes.PTO4;
                    break;
                case PTOTypes.PTO5:
                    codetype = PayTypes.PTO5;
                    break;
                case PTOTypes.PTO6:
                    codetype = PayTypes.PTO6;
                    break;
                case PTOTypes.PTO7:
                    codetype = PayTypes.PTO7;
                    break;
                case PTOTypes.PTO8:
                    codetype = PayTypes.PTO8;
                    break;
                case PTOTypes.PTO9:
                    codetype = PayTypes.PTO9;
                    break;
                case PTOTypes.PTO10:
                    codetype = PayTypes.PTO10;
                    break;
            }
            return codetype;
        }

        private int CountHolidays(DateTime start, DateTime end)
        {
            try
            {
                return this._dbContext.CalendarCustomEvents.Count(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == GlobalVariables.Client && c.DayType == CalendarDayType.Holiday && c.Day >= start && c.Day <= end);
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return 0;
            }
        }

        private int CountDays(DayOfWeek day, DateTime start, DateTime end)
        {
            TimeSpan ts = end - start;
            int count = (int)Math.Floor(ts.TotalDays / 7);
            int remainder = (int)(ts.TotalDays % 7);
            int sinceLastDay = (int)(end.DayOfWeek - day);
            if (sinceLastDay < 0) sinceLastDay += 7;

            // If the days in excess of an even week are greater than or equal to the number days since the last [day], then count this one, too.
            if (remainder >= sinceLastDay) count++;

            return count;
        }

        private int WorkDays(DateTime start, DateTime end,short shift, out decimal hours)
        {
            hours = 8;
            int days = 0;
            if (end >= start)
            {
                days = (int)(end - start).TotalDays + 1;
                days -= CountHolidays(start, end);
                ClientWorkSchedule wssetup = this._dbContext.ClientWorkSchedules.FirstOrDefault(w => w.CompanyID == GlobalVariables.CompanyID && w.ClientID == GlobalVariables.Client && string.IsNullOrEmpty(w.DivisionID));
                if (wssetup != null)
                {
                    if (!wssetup.WorkDay1) days -= CountDays(DayOfWeek.Sunday, start, end);
                    if (!wssetup.WorkDay2) days -= CountDays(DayOfWeek.Monday, start, end);
                    if (!wssetup.WorkDay3) days -= CountDays(DayOfWeek.Tuesday, start, end);
                    if (!wssetup.WorkDay4) days -= CountDays(DayOfWeek.Wednesday, start, end);
                    if (!wssetup.WorkDay5) days -= CountDays(DayOfWeek.Thursday, start, end);
                    if (!wssetup.WorkDay6) days -= CountDays(DayOfWeek.Friday, start, end);
                    if (!wssetup.WorkDay7) days -= CountDays(DayOfWeek.Saturday, start, end);
                    switch (shift)
                    {
                        case 3:
                            hours = (decimal)wssetup.WorkHours3;
                            break;
                        case 2:
                            hours = (decimal)wssetup.WorkHours2;
                            break;
                        default:
                            hours = (decimal)wssetup.WorkHours1;
                            break;
                    }
                }
                else
                {
                    days -= CountDays(DayOfWeek.Sunday, start, end);
                    days -= CountDays(DayOfWeek.Saturday, start, end);
                }
                if (days < 0) days = 0;
            }
            return days;
        }

        private decimal GetWorkHours(PTORequest rec, out bool isDatesValid)
        {
            DateTime start, end, stamp1, stamp2, startPeriod;
            decimal hours;
            DateTime today = DateTime.Now.Date;
            start = rec.StartDate.Date;
            end = rec.EndDate.Date;
            short shift = 1;
            //1/15/2018 DS TFS # 2856 - start changes
            string eeId = rec.EmployeeID;
            startPeriod = EEPayPeriodDate(eeId);
            isDatesValid = (GlobalVariables.DNETLevel == DNetAccessLevel.Employee) ? (start >= startPeriod && end >= startPeriod) : true; // 11/1/2017 DS TFS # 2856
            EmployeeTimePunchesSetup tpSetup = _dbContext.EmployeeTimePunchesSetups.FirstOrDefault(x => x.CompanyID == rec.CompanyID && x.ClientID == rec.ClientID && x.EmployeeID == rec.EmployeeID);
            if (tpSetup != null) shift = tpSetup.Shift ?? 1;
            if (isDatesValid)
            {
                decimal workDays = (decimal)WorkDays(start, end, shift, out hours);
                if (!rec.AllDay)
                {
                    stamp1 = (!string.IsNullOrEmpty(rec.StartTime)) ? SetDayTime(today, rec.StartTime) : today; // (rec.StartTime)) : today;
                    stamp2 = (!string.IsNullOrEmpty(rec.EndTime)) ? SetDayTime(today, rec.EndTime) : today; // today.Add(TimeSpan.Parse(rec.EndTime)) : today;
                    hours = Convert.ToDecimal(stamp2.Subtract(stamp1).TotalMinutes) / 60;
                }
                return workDays * hours;
            }
            else
            {
                return decimal.Zero;
            }
        }

        private DateTime SetDayTime(DateTime day, string time)
        {
            string datetm = day.ToString("MM/dd/yyyy") + " " + time;
            try
            {
                return Convert.ToDateTime(datetm);
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                return day;
            }
        }

        private void SendNotification(int id)
        {
            // Please don't change any of this unless you talk to me - DR
            var thisNotification = new Notifications();
            thisNotification.Initialize(id, NotificationType.CCPTOResponse);
            bool notificationEnabled = thisNotification.NotificationInfo(NotificationType.CCPTOResponse);
            if (notificationEnabled)
            {
                var result = string.Empty;
                var requestUser = _dbContext.PTORequests.FirstOrDefault(p => p.id == id);
                //07/20/2017 DS TFS# 2686
                if (requestUser != null && requestUser.CreatedBy != null)
                {
                    var user = _dbContext.Users.FirstOrDefault(p => p.UserID.ToLower() == requestUser.CreatedBy.ToLower());
                    if (user != null)
                    {
                        var isSubscribed = Subscribers.IsSubscribed(requestUser.CreatedBy, "CCPTORequest");

                        string name = user.Name;
                        if (isSubscribed)
                        {
                            var token = Subscribers.GetToken(user.UserID);

                            if (SmtpEmail.SendSmtpEmail(user.Email, name, thisNotification.Subject, thisNotification.Body, null, false, NotificationType.CCPTOResponse, token, out result)) result = string.Empty;
                        }
                    }

                }
            }
        }

        //07/20/2017 DS TFS# 2686
        public Employee GetEmployeeUser(int id)
        {
            var thisPtoRequest = _dbContext.PTORequests.FirstOrDefault(ept => ept.id == id);
            if (thisPtoRequest == null) return null;
            Employee employee = _dbContext.Employees.FirstOrDefault(emp => emp.CompanyID == thisPtoRequest.CompanyID && emp.ClientID == thisPtoRequest.ClientID && emp.EmployeeID == thisPtoRequest.EmployeeID);
            if (employee == null) return null;
            if (string.IsNullOrEmpty(employee.Email))
            {
                var eeUserAssignment = _dbContext.UserRoleClientEmployeeAssignments.FirstOrDefault(urcea => urcea.CompanyID == employee.CompanyID && urcea.ClientID == employee.ClientID && urcea.EmployeeID == employee.EmployeeID && urcea.User.Enabled);
                if (eeUserAssignment != null)
                {
                    var eeUser = _dbContext.Users.FirstOrDefault(u => u.UserID == eeUserAssignment.UserID);
                    if (eeUser != null) employee.Email = eeUser.Email;
                }
            }
            return employee;
        }
        public List<UserRoleClientEmployeeAssignment> GetEmployeeUsers(int id)
        {
            //get the PTO request record
            var thisPtoRequest = _dbContext.PTORequests.FirstOrDefault(ept => ept.id == id);
            if (thisPtoRequest == null) return new List<UserRoleClientEmployeeAssignment>();
            //get the Employee record for the request
            var employee = _dbContext.Employees.FirstOrDefault(emp => emp.CompanyID == thisPtoRequest.CompanyID && emp.EmployeeID == thisPtoRequest.EmployeeID);
            if (employee == null) return new List<UserRoleClientEmployeeAssignment>();
            //get the employees home department.
            var dept = employee.Department;
            var allUsers = _dbContext.UserRoleClientEmployeeAssignments.Where(urcea => urcea.CompanyID == employee.CompanyID && urcea.ClientID == employee.ClientID && urcea.User.Enabled).ToList();
            var allApproveUsers = allUsers.Where(urcea => urcea.ApproveAllPTO).ToList();
            var clientSetup = _dbContext.DarwinetSetups.FirstOrDefault(ds => ds.CompanyID == employee.CompanyID && ds.ClientID == employee.ClientID);
            if (clientSetup == null) return new List<UserRoleClientEmployeeAssignment>();
            {
                bool isDepartmentSecurityEnabled = ClientUtilities.IsDepartmentSecurityEnabled(employee.CompanyID, employee.ClientID, employee.UserID);
                bool isDivisionSecurityEnabled = ClientUtilities.IsDivisionSecurityEnabled(employee.CompanyID, employee.ClientID, employee.UserID);
                bool isSupervisorSecurityEnabled = ClientUtilities.IsSupervisorSecurityEnabled(employee.CompanyID, employee.ClientID, employee.UserID);

                if (isDepartmentSecurityEnabled)
                {
                    var deptSecurityUsers = _dbContext.UserDeptDivSecurities.Where(udds => udds.CompanyID == employee.CompanyID && udds.ClientID == employee.ClientID && udds.Department == dept && (udds.AllAccess || udds.ApprovePTO)).ToList();
                    var deptApproveUsers = allUsers.Where(au => deptSecurityUsers.Any(ds => ds.UserID == au.UserID)).ToList();

                    //object concatUsers = null;
                    // check for existing user
                    foreach (var deptUser in deptApproveUsers)
                    {
                        if (allApproveUsers.All(ap => ap.UserID != deptUser.UserID))
                        {
                            allApproveUsers.Add(deptUser);
                        }
                    }

                    return allApproveUsers;
                }
                if (isDivisionSecurityEnabled)
                {
                    var division = _dbContext.ClientDivisionDetails.FirstOrDefault(cdd => cdd.CompanyID == employee.CompanyID && cdd.ClientID == employee.ClientID && cdd.Department == dept);
                    if (division == null) return new List<UserRoleClientEmployeeAssignment>();
                    var divSecurityUsers = _dbContext.UserDeptDivSecurities.Where(udds => udds.CompanyID == employee.CompanyID && udds.ClientID == employee.ClientID && udds.DivisionID == division.DivisionID && (udds.AllAccess || udds.ApprovePTO)).ToList();
                    var divApproveUsers = allUsers.Where(au => divSecurityUsers.Any(ds => ds.UserID == au.UserID)).ToList();
                    //var concatUsers = allApproveUsers.Concat(divApproveUsers).ToList();

                    foreach (var divUser in divApproveUsers)
                    {
                        if (allApproveUsers.All(ap => ap.UserID != divUser.UserID))
                        {
                            allApproveUsers.Add(divUser);
                        }
                    }

                    return allApproveUsers;
                    //return concatUsers;
                }
                if (isSupervisorSecurityEnabled)
                {
                    var supervisorLookup = _dbContext.UserSupervisorSecurities.Where(s => s.CompanyID == employee.CompanyID && s.ClientID == employee.ClientID && s.EmployeeID == employee.EmployeeID).ToList();
                    var supervisorList = new List<UserRoleClientEmployeeAssignment>();
                    foreach (var supervisor in supervisorLookup)
                    {
                        var supervisorEmail = _dbContext.Users.FirstOrDefault(u => u.UserID == supervisor.UserID);

                        if (supervisorEmail == null) return new List<UserRoleClientEmployeeAssignment>();
                        if (string.IsNullOrEmpty(supervisorEmail.Email)) return new List<UserRoleClientEmployeeAssignment>();

                        supervisorList.Add(new UserRoleClientEmployeeAssignment
                        {
                            EmployeeName = supervisorEmail.Name,
                            Email = supervisorEmail.Email,
                            UserID = supervisorEmail.UserID,
                            DateCreated = DateTime.Now
                        });
                    }


                    return supervisorList;
                }
            }
            return allApproveUsers;
        }


        #endregion PTO Requests

        #region Trainings
        public ActionResult Trainings()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientTrainings);
            return View();
        }

        public ActionResult GetTraining_Read([DataSourceRequest] DataSourceRequest request)
        {

            return Json(GetTraining()
                .ToDataSourceResult(request));
        }

        public static IEnumerable<TrainingGroup> GetTraining()
        {
            var model = new DnetEntities();

            List<ClientTrainingType> clientTrainingTypes = model.ClientTrainingTypes.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            var theView = new List<TrainingGroup>();

            foreach (var item in clientTrainingTypes)
            {
                theView.Add(new TrainingGroup
                {
                    TrainingID = item.TrainingType,
                    Description = item.Description,
                    EncryptedID = ConCryptor.Encrypt(item.TrainingType)
                }
                );
            }

            return theView;
        }

        public ActionResult GetEETraining_Read([DataSourceRequest] DataSourceRequest request, string T, bool AllDetail = false)
        {

            return Json(GetEETraining(T, AllDetail)
                .ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        public static IEnumerable<Training> GetEETraining(string t, bool AllDetail)
        {
            var model = new DnetEntities();

            List<EmployeeTrainingComment> employeeTrainingComments = model.EmployeeTrainingComments.Where(e => e.CompanyID == GlobalVariables.CompanyID).ToList();
            List<Employee> employees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            List<EmployeeTraining> employeeTrainings = model.EmployeeTrainings.Where(e => e.CompanyID == GlobalVariables.CompanyID).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var list = from etc in employeeTrainingComments
                       join emp in employees on new { etc.CompanyID, etc.EmployeeID } equals new { emp.CompanyID, emp.EmployeeID }
                       join et in employeeTrainings on new { etc.CompanyID, etc.EmployeeID, etc.TrainingType } equals new { et.CompanyID, et.EmployeeID, et.TrainingType }
                       select new { emp.EmployeeID, emp.FirstName, emp.LastName, etc.TrainingGradeRating, et.TrainingStatus, etc.CompletionDate, etc.RenewalDate, etc.TrainingType, emp.Inactive };

            //DG-12/30/2021-DNET56-START
            if (!AllDetail)
                list = list.Where(x => x.TrainingType == ConCryptor.Decrypt(t)).ToList();
            //DG-12/30/2021-DNET56-END

            var TheView = new List<Training>();
            foreach (var item in list)
            {
                TheView.Add(new Training
                {
                    EmployeeName = item.FirstName + ' ' + item.LastName,
                    Rate = item.TrainingGradeRating,
                    Status = item.TrainingStatus,
                    CompletedDate = (item.CompletionDate == null || item.CompletionDate.ToString() == "1/1/1900 12:00:00 AM") ? string.Empty : item.CompletionDate.Value.ToString("MM/dd/yyyy"), // 10/04/2017 DS TFS # 2787
                    RenewalDate = (item.RenewalDate == null || item.RenewalDate.ToString() == "1/1/1900 12:00:00 AM") ? string.Empty : item.RenewalDate.Value.ToString("MM/dd/yyyy"), // 10/04/2017 DS TFS # 2787
                    Inactive = Convert.ToBoolean(item.Inactive),
                    TrainingType = item.TrainingType,
                    EmployeeID = item.EmployeeID
                    //keep adding additional fields here
                });
            }
            return TheView;
        }


        #endregion Trainings

        #region Reviews
        public ActionResult Reviews()
        {
            ViewBag.Managers = _dbContext.ClientManagers.Where(cm => cm.CompanyID == GlobalVariables.CompanyID && cm.ClientID == GlobalVariables.Client).Select(cm => new Code_Description { Code = cm.HRManager, Description = cm.HRManager }).ToList();
            List<Code_Description> theDepts = _dbContext.ClientDivisionDetails.Where(cdd => cdd.CompanyID == GlobalVariables.CompanyID && cdd.ClientID == GlobalVariables.Client).Select(cdd => new Code_Description { Code = cdd.Department, Description = cdd.Department }).ToList();
            ViewBag.TheDepts = theDepts.GroupBy(tp => tp.Code).Select(x => x.First()).ToList<Code_Description>();
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientReviews);
            return View();
        }

        public ActionResult DeleteReview(string ee,int seq)
        {

            var thereview = _dbContext.EmployeeReviews.FirstOrDefault(r => r.CompanyID == GlobalVariables.CompanyID && r.EmployeeID == ee && r.SequenceNumber == seq );
                if (thereview != null)
                {
                    
                    try
                    {
                        _dbContext.EmployeeReviews.Remove(thereview);
                        _dbContext.SaveChanges();
                    }
                    catch (Exception ex)
                    {
                        TempData["Error"] = "Cannot delete Review.";
                    }
                }
                else
                {
                    TempData["Error"] = "Cannot delete Review.";
                }
            return RedirectToAction("Reviews", "HR");

        }

        public ActionResult GetNextReview_Read([DataSourceRequest] DataSourceRequest request)
        {

            return Json(GetNextReview()
                .ToDataSourceResult(request));
        }

        public static IEnumerable<ReviewGroup> GetNextReview()
        {
            var model = new DnetEntities();

            List<EmployeeReview> employeeReviews = model.EmployeeReviews.Where(e => e.CompanyID == GlobalVariables.CompanyID).ToList();
            List<Employee> employees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var list = (from er in employeeReviews
                        join emp in employees on new { er.CompanyID, er.EmployeeID } equals new { emp.CompanyID, emp.EmployeeID }
                        select new { emp.EmployeeID, emp.FirstName, emp.LastName, emp.NextReviewDate, emp.DateOfLastReview, emp.Department, emp.Position, er.HRManager, emp.Inactive, er.Date,er.SequenceNumber }).Distinct();

            var theView = new List<ReviewGroup>();
            foreach (var item in list)
            {
                var LastReview = item.Date;
                var FinalLastReview = "";
                if (LastReview != null)
                {
                    FinalLastReview = LastReview.Value.ToString("MM/dd/yyyy");
                }
                theView.Add(new ReviewGroup
                {
                    EmployeeID = item.EmployeeID,
                    EmployeeName = item.FirstName + ' ' + item.LastName,
                    SeqNumber = item.SequenceNumber,
                    NextReview = item.NextReviewDate.Value.ToString("MM/dd/yyyy"),
                    LastReview = FinalLastReview,
                    Department = item.Department,
                    Position = item.Position,
                    Manager = item.HRManager,
                    Inactive = item.Inactive
                }
                );

            }
            return theView;
        }

        public ActionResult GetEENextReview_Read([DataSourceRequest] DataSourceRequest request, string R, bool AllDetail = false)
        {
            return Json(GetEENextReview(R, AllDetail)
                .ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        public static IEnumerable<ReviewHR> GetEENextReview(string r, bool AllDetail)
        {
            var model = new DnetEntities();

            List<EmployeeReview> employeeReviews = model.EmployeeReviews.Where(e => e.CompanyID == GlobalVariables.CompanyID).ToList();
            List<Employee> employees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var list = from er in employeeReviews
                       join emp in employees on new { er.CompanyID, er.EmployeeID } equals new { emp.CompanyID, emp.EmployeeID }
                       select new { emp.EmployeeID, emp.FirstName, emp.LastName, er.Date, er.HRManager, er.ReviewPerformanceType, er.EmployeeRating, er.ReviewPerformanceAction, er.SequenceNumber };

            if (!AllDetail)
                list = list.Where(x => x.EmployeeID == r);

            var TheView = new List<ReviewHR>();
            foreach (var item in list)
            {
                TheView.Add(new ReviewHR
                {
                    EmployeeID = item.EmployeeID,
                    EmployeeName = item.FirstName + ' ' + item.LastName,
                    ReviewDate = item.Date.Value.ToString("MM/dd/yyyy"),
                    Manager = item.HRManager,
                    Type = item.ReviewPerformanceType,
                    Rating = item.EmployeeRating,
                    Action = item.ReviewPerformanceAction,
                    SequenceNumber = item.SequenceNumber
                    //keep adding additional fields here
                });
            }
            return TheView;
        }

        //private List<Code_Description> GetEmployeePTOCodes(string emplId, int type)
        //{
        //    try
        //    {
        //        return _dbContext.EmployeePaycodes.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.EmployeeID == emplId && !c.Inactive && c.PayType == type).OrderBy(c => c.PayRecord).Select(c => new Code_Description { Code = c.PayRecord, Description = c.PayRecord }).ToList();
        //    }
        //    catch { return new List<Code_Description>(); }
        //}

        #endregion Reviews

        #region Licenses
        public ActionResult Licenses()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientLicenses);
            return View();
        }

        public ActionResult GetLicenses_Read([DataSourceRequest] DataSourceRequest request)
        {
            return Json(GetLicenses().ToDataSourceResult(request));
        }

        public static IEnumerable<LicenseGroup> GetLicenses()
        {
            var model = new DnetEntities();

            List<LicenseType> licenseTypes = model.LicenseTypes.Where(x => x.CompanyID == GlobalVariables.CompanyID).ToList();
            List<EmployeeLicensesAndCertification> employeeLicensesAndCertifications = model.EmployeeLicensesAndCertifications.Where(x => x.CompanyID == GlobalVariables.CompanyID).ToList();
            List<Employee> employees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var theLicenses = from lt in licenseTypes
                              join elc in employeeLicensesAndCertifications on new { lt.LicenseCertificationID, lt.CompanyID } equals new { elc.LicenseCertificationID, elc.CompanyID }
                              join e in employees on elc.EmployeeID equals e.EmployeeID
                              select new { lt.LicenseCertificationID, lt.Description, e.EmployeeID };

            var theView = new List<LicenseGroup>();
            theLicenses = theLicenses.GroupBy(p => p.LicenseCertificationID).Select(g => g.First());
            foreach (var licenses in theLicenses)
            {
                theView.Add(new LicenseGroup
                {
                    EmployeeID = licenses.EmployeeID,
                    LicenseID = licenses.LicenseCertificationID,
                    Description = licenses.Description,
                    EncryptedID = ConCryptor.Encrypt(licenses.LicenseCertificationID)
                }
                );
            }

            return theView;
        }

        public ActionResult GetEELicenses_Read([DataSourceRequest] DataSourceRequest request, string L, bool AllDetail = false)
        {
            return Json(GetEELicenses(L, AllDetail)
                .ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        public static IEnumerable<LicenseHR> GetEELicenses(string l, bool AllDetail)
        {
            var model = new DnetEntities();

            List<LicenseType> licenseTypes = model.LicenseTypes.Where(x => x.CompanyID == GlobalVariables.CompanyID).ToList();
            List<EmployeeLicensesAndCertification> employeeLicensesAndCertifications = model.EmployeeLicensesAndCertifications.Where(x => x.CompanyID == GlobalVariables.CompanyID).ToList();
            List<Employee> employees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var theLicenses = from lt in licenseTypes
                              join elc in employeeLicensesAndCertifications on new { lt.LicenseCertificationID, lt.CompanyID } equals new { elc.LicenseCertificationID, elc.CompanyID }
                              join emp in employees on elc.EmployeeID equals emp.EmployeeID
                              select new { emp.EmployeeID, emp.FirstName, emp.LastName, elc.LicenseCertificationID, elc.LicenseCertificationNumber, elc.EffectiveDate, elc.ExpirationDate, elc.Inactive };

            if (!AllDetail)
                theLicenses = theLicenses.Where(x => x.LicenseCertificationID == ConCryptor.Decrypt(l));

            var TheView = new List<LicenseHR>();
            foreach (var emp in theLicenses)
            {
                TheView.Add(new LicenseHR
                {
                    EmployeeID = emp.EmployeeID,
                    EmployeeName = emp.FirstName + " " + emp.LastName,
                    LicenseID = emp.LicenseCertificationID,
                    LicenseNumber = emp.LicenseCertificationNumber,
                    EffectiveDate = emp.EffectiveDate.Value.ToString("MM/dd/yyyy"),
                    ExpirationDate = emp.ExpirationDate.Value.ToString("MM/dd/yyyy"),
                    Inactive = Convert.ToBoolean(emp.Inactive)
                    //keep adding additional fields here
                });
            }

            return TheView;
        }

        #endregion Licenses

        #region I9
        public ActionResult I9()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientI9);
            return View();
        }

        public ActionResult GetI9Info_Read([DataSourceRequest] DataSourceRequest request)
        {
            var theI9s =
              (from ver in _dbContext.EmployeeEligibilityVerifications
               from emp in _dbContext.Employees.Where(emp => emp.EmployeeID == ver.EmployeeID && emp.CompanyID == ver.CompanyID)
               from doc in _dbContext.EmployeeOBDocuments.Where(doc => doc.EmployeeID == ver.EmployeeID && doc.CompanyID == ver.CompanyID && doc.DocumentName == "I-9").DefaultIfEmpty()
               select new EmployeeEligibilityVerificationVM
               {
                   SSN = ver.Employee.SSN,
                   ResidentStatus = ver.ResidentStatus,
                   dispResStatus = string.Empty,
                   dispSSN = string.Empty,
                   dispName = ver.Employee.FirstName + " " + ver.Employee.LastName,
                   CompanyID = ver.CompanyID,
                   EmployeeID = ver.EmployeeID,
                   OtherName = ver.OtherName ?? string.Empty,
                   AlienNbr = ver.AlienNbr ?? string.Empty,
                   WorkExpDate = ver.WorkExpDate,
                   dispWorkExpDate = string.Empty,
                   I94Nbr = ver.I94Nbr ?? string.Empty,
                   ForeignPassportNbr = ver.ForeignPassportNbr ?? string.Empty,
                   PassportCountry = ver.PassportCountry ?? string.Empty,
                   DocA1Type = ver.DocA1Type,
                   dispDocA1Type = string.Empty,
                   DocA1Title = ver.DocA1Title ?? string.Empty,
                   DocA1Authority = ver.DocA1Authority ?? string.Empty,
                   DocA1Number = ver.DocA1Number ?? string.Empty,
                   DocA1ExpDate = ver.DocA1ExpDate,
                   DocA2Type = ver.DocA2Type,
                   dispDocA1ExpDate = string.Empty,
                   dispDocA2Type = string.Empty,
                   DocA2Title = ver.DocA2Title ?? string.Empty,
                   DocA2Authority = ver.DocA2Authority ?? string.Empty,
                   DocA2Number = ver.DocA2Number ?? string.Empty,
                   DocA2ExpDate = ver.DocA2ExpDate,
                   DocA3Type = ver.DocA3Type,
                   dispDocA2Expdate = string.Empty,
                   dispDocA3Type = string.Empty,
                   DocA3Title = ver.DocA3Title ?? string.Empty,
                   DocA3Authority = ver.DocA3Authority ?? string.Empty,
                   DocA3Number = ver.DocA3Number ?? string.Empty,
                   DocBType = ver.DocBType,
                   dispDocA3ExpDate = string.Empty,
                   dispDocBType = string.Empty,
                   DocBTitle = ver.DocBTitle ?? string.Empty,
                   DocBAuthority = ver.DocBAuthority ?? string.Empty,
                   DocBNumber = ver.DocBNumber ?? string.Empty,
                   DocBExpDate = ver.DocBExpDate,
                   DocCType = ver.DocCType,
                   dispDocBExpDate = string.Empty,
                   dispDocCType = string.Empty,
                   DocCTitle = ver.DocCTitle ?? string.Empty,
                   DocCAuthority = ver.DocCAuthority ?? string.Empty,
                   DocCNumber = ver.DocCNumber ?? string.Empty,
                   dispDocCExpDate = string.Empty,
                   UsePreparer = ver.UsePreparer,
                   PrepLastName = ver.PrepLastName ?? string.Empty,
                   PrepFirstName = ver.PrepFirstName ?? string.Empty,
                   PrepAddress = ver.PrepAddress ?? string.Empty,
                   PrepAddress2 = ver.PrepAddress2 ?? string.Empty,
                   PrepCity = ver.PrepCity ?? string.Empty,
                   PrepState = ver.PrepState ?? string.Empty,
                   PrepZip = ver.PrepZIP ?? string.Empty,
                   CCTitle = ver.CCTitle ?? string.Empty,
                   CCLastName = ver.CCLastName ?? string.Empty,
                   CCFirstName = ver.CCFirstName ?? string.Empty,
                   RHLastName = ver.RHLastName ?? string.Empty,
                   RHFirstName = ver.RHFirstName ?? string.Empty,
                   RHMiddle = ver.RHMiddle ?? string.Empty,
                   RHDate = ver.RHDate,
                   dispRHDate = string.Empty,
                   dispRHDocType = string.Empty,
                   RHDocTitle = ver.RHDocTitle ?? string.Empty,
                   RHDocNumber = ver.RHDocNumber ?? string.Empty,
                   RHDocExpDate = ver.RHDocExpDate,
                   dispRHDocExpDate = string.Empty,
                   RHERName = ver.RHERName ?? string.Empty,
                   CCCertdate = ver.CCCertdate,
                   CCVerifiedDate = doc.CCVerifiedDate,
                   dispCCCertdate = string.Empty,
                   dispEECompleteDate = string.Empty,
                   CCCertIPAddr = (ver.CCCertIPAddr == null) ? (doc.CCVerifiedIP == null) ? null : doc.CCVerifiedIP : ver.CCCertIPAddr,
                   EECompleteDate = ver.EECompleteDate,
                   EEVerifiedDate = doc.EEVerifiedDate,
                   PrepMiddle4 = ver.PrepMiddle4 ?? string.Empty


               })
             .Where(x =>
                  x.CompanyID == GlobalVariables.CompanyID &&
                  x.EmployeeID.Substring(2, 3) == GlobalVariables.Client
              ).ToList();
            foreach (var i9 in theI9s.ToList())
            {
                i9.dispSSN = FieldTranslation.GetMaskSSN(i9.SSN);
                i9.dispResStatus = (i9.ResidentStatus != null) ? FieldTranslation.GetEnumDescription(typeof(I9ResidentStatus), (int)i9.ResidentStatus) : string.Empty;
                i9.dispWorkExpDate = FieldTranslation.ToShortDate(i9.WorkExpDate.ToString());
                i9.dispDocA1Type = FieldTranslation.GetEnumDescription(typeof(I9ListADocument), i9.DocA1Type ?? 0);
                i9.dispDocA1ExpDate = FieldTranslation.ToShortDate(i9.DocA1ExpDate.ToString());
                i9.dispDocA2Type = FieldTranslation.GetEnumDescription(typeof(I9ListADocument), i9.DocA2Type ?? 0);
                i9.dispDocA2Expdate = FieldTranslation.ToShortDate(i9.DocA2ExpDate.ToString());
                i9.dispDocA3Type = FieldTranslation.GetEnumDescription(typeof(I9ListADocument), i9.DocA3Type ?? 0);
                i9.dispDocA3ExpDate = FieldTranslation.ToShortDate(i9.DocA3ExpDate.ToString());
                i9.dispDocBType = FieldTranslation.GetEnumDescription(typeof(I9DocBUnder18), i9.DocBType ?? 0);
                i9.dispDocBExpDate = FieldTranslation.ToShortDate(i9.DocBExpDate.ToString());
                i9.dispDocCType = FieldTranslation.GetEnumDescription(typeof(I9DocC), i9.DocCType ?? 0);
                i9.dispDocCExpDate = FieldTranslation.ToShortDate(i9.DocCExpDate.ToString());
                i9.dispRHDate = FieldTranslation.ToShortDate(i9.RHDate.ToString());
                i9.dispRHDocExpDate = FieldTranslation.ToShortDate(i9.RHDocExpDate.ToString());
                i9.dispCCCertdate = (i9.CCCertdate == null) ? (i9.CCVerifiedDate == null) ? null : FieldTranslation.ToShortDate(i9.CCVerifiedDate.ToString()) : FieldTranslation.ToShortDate(i9.CCCertdate.ToString());
                i9.dispEECompleteDate = (i9.EECompleteDate == null) ? (i9.EEVerifiedDate == null) ? null : FieldTranslation.ToShortDate(i9.EEVerifiedDate.ToString()) : FieldTranslation.ToShortDate(i9.EECompleteDate.ToString());
            }
            return Json(theI9s.ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        public static IEnumerable<I9Group> GetI9Info()
        {
            var model = new DnetEntities();
            List<EmployeeEligibilityVerification> employeeEligibilityVerifications = model.EmployeeEligibilityVerifications.Where(eev => eev.CompanyID == GlobalVariables.CompanyID && eev.Employee.ClientID == GlobalVariables.Client).ToList();
            List<Employee> employees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var theEmployees = from eev in employeeEligibilityVerifications
                               join emp in employees on eev.EmployeeID equals emp.EmployeeID
                               select new { emp.EmployeeID, emp.FirstName, emp.LastName, emp.SSN, eev.ResidentStatus, emp.Inactive };

            var TheView = new List<I9Group>();

            if (theEmployees != null)
            {
                foreach (var Employee in theEmployees)
                {
                    string ssn = FieldTranslation.GetMaskSSN(Employee.SSN);
                    TheView.Add(new I9Group
                    {
                        EmployeeID = Employee.EmployeeID,
                        EmployeeName = Employee.FirstName + ' ' + Employee.LastName,
                        SSN = ssn,
                        ResidentStatus = FieldTranslation.GetEnumDescription(typeof(I9ResidentStatus), (int)Employee.ResidentStatus),
                        Inactive = Convert.ToBoolean(Employee.Inactive)
                        //keep adding additional fields here
                    });
                }
            }
            return TheView;
        }

        #endregion I9

        #region FMLA/LOA
        public ActionResult FMLALOA()
        {
            ViewBag.Access = MenuAccess.PageSecurity(MenuItemIds.ClientFmlaloa);
            return View();
        }

        public ActionResult GetFMLALOA_Read([DataSourceRequest] DataSourceRequest request)
        {
            return Json(GetFMLALOA().ToDataSourceResult(request), JsonRequestBehavior.AllowGet);
        }

        public static IEnumerable<FMLALOAGroup> GetFMLALOA()
        {
            var model = new DnetEntities();

            List<EmployeeLOAMaster> employeeLOAMasters = model.EmployeeLOAMasters.Where(x => x.CompanyID == GlobalVariables.CompanyID).ToList();
            List<EmployeeFMLAMaster> employeeFMLAMasters = model.EmployeeFMLAMasters.Where(x => x.CompanyID == GlobalVariables.CompanyID).ToList();
            List<Employee> employees = model.Employees.Where(e => e.CompanyID == GlobalVariables.CompanyID && e.ClientID == GlobalVariables.Client).ToList();
            var securityEEs = GlobalVariables.SecurityEmployees;
            employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();

            var theEmployees = from loa in employeeLOAMasters
                                   //join fmla in employeeFMLAMasters on loa.EmployeeID equals fmla.EmployeeID
                               join emp in employees on loa.EmployeeID equals emp.EmployeeID
                               select new { emp.EmployeeID, emp.FirstName, emp.LastName, loa.Inactive_Reason_ID, loa.StartDate, loa.EndDate, emp.Department, emp.Position, loa.Is_FMLA, loa.Approval, loa.Index };

            var theView = new List<FMLALOAGroup>();

            if (theEmployees.Any())
            {
                foreach (var employee in theEmployees)
                {
                    theView.Add(new FMLALOAGroup
                    {
                        EmployeeName = employee.FirstName + ' ' + employee.LastName,
                        EmployeeID = employee.EmployeeID,
                        Reason = employee.Inactive_Reason_ID,
                        BeginDate = employee.StartDate.ToString("MM/dd/yyyy"),
                        EndDate = employee.EndDate.ToString("MM/dd/yyyy"),
                        Dept = employee.Department,
                        Position = employee.Position,
                        FMLA = Convert.ToBoolean(employee.Is_FMLA),
                        Approved = Convert.ToBoolean(employee.Approval),
                        Index = employee.Index
                        //keep adding additional fields here
                    });
                }
            }
            return theView;
        }

        #endregion FMLA/LOA

        //12/27/2017 DS TFS # 2856
        #region Start Pay Period Date
        private DateTime LastCheckDate(string emplId, DateTime defStart)
        {
            DateTime result = DateTime.Today.AddDays(-1);
            EmployeeCheckHistory check = _dbContext.EmployeeCheckHistories.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.EmployeeID == emplId).OrderByDescending(c => c.CheckDate).FirstOrDefault();
            if (check != null)
            {
                result = check.CheckDate ?? DateTime.Today.AddDays(-1);
            }
            return (result > defStart) ? defStart : result;
        }

        private DateTime LastTimesheetDate(string emplId, DateTime defStart)
        {
            DateTime result = defStart;
            try
            {
                List<int> eeTimesheets = _dbContext.TimeSheetDetails.Where(t => t.EmployeeID == emplId).Select(t => t.TimeSheetID).Distinct().ToList();
                if (eeTimesheets.Any())
                {
                    List<DarwiNet2._0.Data.TimeSheet> ccTimesheets = _dbContext.TimeSheets.Where(t => t.CompanyID == GlobalVariables.CompanyID && t.ClientID == GlobalVariables.Client && t.Status != TimeSheetStatus.New && t.Status != TimeSheetStatus.Edit && t.Status != TimeSheetStatus.Empty && t.Status != TimeSheetStatus.Active).ToList();
                    DarwiNet2._0.Data.TimeSheet tsheet = ccTimesheets.Where(t => eeTimesheets.Any(ts => ts == t.TimeSheetID)).OrderByDescending(t => t.DateTo).FirstOrDefault();
                    if (tsheet != null) result = tsheet.DateTo ?? defStart;
                    if (result > defStart) result = defStart;
                }
            }
            catch (Exception ex)
            {
                Bugsnag.AspNet.Client.Current.Notify(ex);
                result = defStart;
            }
            return result;
        }

        private DateTime EEPayPeriodDate(string emplId)
        {
            DateTime defStart = DateTime.Today.AddDays(-1);
            DateTime d = LastTimesheetDate(emplId, defStart);
            DateTime result = (d >= defStart) ? LastCheckDate(emplId, defStart) : d;
            return result.AddDays(1);
        }
        #endregion
        //DG - 7346 -09/13/2021 - START- PTO Custom filters
        [HttpPost]
        public ActionResult ExportRequestsHRList(string flt, string order)
        {
            try
            {
                IEnumerable<PTOHR> requests = null;
                IEnumerable<PTOGroup> requests1 = null;
                string exportFile = string.Empty;
                Excel.Application excelApp = new Excel.Application();
                Excel.Workbook excelWorkBook = null;
                Excel.Worksheet excelWorkSheet = null;
                object misValue = System.Reflection.Missing.Value;
                excelWorkBook = excelApp.Workbooks.Add(misValue);
                excelWorkSheet = excelWorkBook.Worksheets.get_Item(1); // Get the first worksheet
                                                                       //FOR PARENT GRID
                int xRow = 1;
                PopulateExcelCell(excelWorkSheet, xRow, 1, "PTO Plan ID", true);
                PopulateExcelCell(excelWorkSheet, xRow, 2, "Description", true);
                PopulateExcelCell(excelWorkSheet, xRow, 3, "Accrual Method", true);
                PopulateExcelCell(excelWorkSheet, xRow, 4, "Accrual Amount", true);
                PopulateExcelCell(excelWorkSheet, xRow, 5, "Anniversary Method", true);
                PopulateExcelCell(excelWorkSheet, xRow, 6, "Inactive", true);

                if ((flt != "" && flt != null) || (order != "" && order != null))
                {
                    requests1 = GetPTOFilter(flt, order);
                }
                else
                {
                    requests1 = GetPTO();
                }
                foreach (PTOGroup request34 in requests1)
                {
                    xRow++;
                    PopulateExcelCell(excelWorkSheet, xRow, 1, request34.PTOPlanID, false);
                    PopulateExcelCell(excelWorkSheet, xRow, 2, request34.Description, false);
                    PopulateExcelCell(excelWorkSheet, xRow, 3, request34.AccrualMethod, false);
                    PopulateExcelCell(excelWorkSheet, xRow, 4, request34.AccrualAmount, false);
                    PopulateExcelCell(excelWorkSheet, xRow, 5, request34.AnniversaryMethod.ToString(), false);
                    PopulateExcelCell(excelWorkSheet, xRow, 6, request34.Inactive.ToString(), false);
                    xRow++;
                    PopulateExcelCell(excelWorkSheet, xRow, 1, "EmployeeID", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 2, "EmployeeName", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 3, "Used", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 4, "Accrued", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 5, "Available", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 6, "PTOTypeInt", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 7, "PTOType", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 8, "Inactive", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 9, "Pending", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 10, "Approved", true);
                    requests = GetEEPTO1(request34.PTOID);
                    foreach (PTOHR request35 in requests)
                    {
                        xRow++;
                        PopulateExcelCell(excelWorkSheet, xRow, 1, request35.EmployeeID.ToString(), false);
                        PopulateExcelCell(excelWorkSheet, xRow, 2, request35.EmployeeName, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 3, request35.Used, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 4, request35.Accrued, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 5, request35.Available, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 6, request35.PTOTypeInt.ToString(), false);
                        PopulateExcelCell(excelWorkSheet, xRow, 7, request35.PTOType, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 8, request35.Inactive.ToString(), false);
                        PopulateExcelCell(excelWorkSheet, xRow, 9, request35.Pending, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 10, request35.Approved, false);

                    }
                }
                // Fix column widths
                Excel.Range startCell = excelWorkSheet.Cells[1, 1];
                Excel.Range endCell = excelWorkSheet.Cells[requests.Count() + 1, 9];
                Excel.Range aRange = excelWorkSheet.get_Range(startCell, endCell);
                aRange.Columns.AutoFit();
                var path = Path.Combine(GlobalVariables.CompanyFolder, Folders.PTOGrid);
                bool exists = Directory.Exists(path);
                if (!exists)
                {
                    Directory.CreateDirectory(path);
                }
                string newDocName = "Temp-" + DateTime.Now.ToString("yyyyMMddHHmmssffff") + ".xls";
                exportFile = Path.Combine(path, newDocName);

                excelWorkBook.SaveAs(exportFile, Excel.XlFileFormat.xlWorkbookNormal, misValue, misValue, misValue, misValue, Excel.XlSaveAsAccessMode.xlExclusive, misValue, misValue, misValue, misValue, misValue);
                excelWorkBook.Close(true, misValue, misValue);
                excelApp.Quit();
                return Json(exportFile, JsonRequestBehavior.AllowGet);

            }
            catch (Exception exception)
            {
                return Json(exception.ToString(), JsonRequestBehavior.AllowGet);
            }
        }
        public static IEnumerable<PTOHR> GetEEPTO1(byte PtoType)
        {
            string p = "Dummy";
            var TheView = new List<PTOHR>();
            if (p == "Dummy")
            {
                var model = new DnetEntities();
                List<ClientPTOType> clientPTOTypes = model.ClientPTOTypes.Where(c => c.CompanyID == GlobalVariables.CompanyID && c.ClientID == GlobalVariables.Client).ToList();
                var securityEEs = GlobalVariables.SecurityEmployees;

                List<EmployeePTOType> employeePTOTypes = model.EmployeePTOTypes.Where(c => c.CompanyID == GlobalVariables.CompanyID
                && c.PTOType == PtoType).ToList();

                employeePTOTypes = employeePTOTypes.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();
                List<Employee> employees = model.Employees.ToList();
                employees = employees.Where(s => securityEEs.Any(e => e == s.EmployeeID)).ToList();
                var list = from cpt in clientPTOTypes
                           join ept in employeePTOTypes on
                                           new { cpt.CompanyID, cpt.ClientID, cpt.PTOType } equals
                                           new { ept.CompanyID, ClientID = GlobalVariables.Client, ept.PTOType }
                           join emp in employees on
                                           new { ept.CompanyID, ept.EmployeeID } equals
                                           new { emp.CompanyID, emp.EmployeeID }
                           where emp.CompanyID == GlobalVariables.CompanyID && emp.ClientID == GlobalVariables.Client && emp.Inactive == false
                           select new { emp.EmployeeID, emp.FirstName, emp.LastName, ept.AvailableHours, ept.Accrued, emp.Inactive, ept.PTOType, ept.YTDHoursAccrued, ept.PTOAmountTaken };

                foreach (var item in list)
                {
                    var pending = PendingRequests(item.EmployeeID, item.PTOType);
                    var approved = ApprovedRequests(item.EmployeeID, item.PTOType, 0);

                    TheView.Add(new PTOHR
                    {
                        EmployeeID = FieldTranslation.EmployeeIDFormatted(item.EmployeeID),
                        EmployeeName = item.FirstName + ' ' + item.LastName,
                        Used = String.Format("{0:N}", ((item.PTOAmountTaken ?? 0) / 100.0)),
                        Available = String.Format("{0:N}", ((item.AvailableHours ?? 0) / 100.0)),
                        Accrued = String.Format("{0:N}", ((item.YTDHoursAccrued ?? 0) / 100.0)),
                        PTOTypeInt = item.PTOType,
                        PTOType = FieldTranslation.GetEnumDescription(typeof(enPTOTypes), item.PTOType),
                        Inactive = Convert.ToBoolean(item.Inactive),
                        Pending = string.Format("{0:n2}", pending),
                        Approved = string.Format("{0:n2}", approved)
                    });
                }
            }
            return TheView;
        }
        public static IEnumerable<PTOGroup> GetPTOFilter(string filter, string order)
        {

            var model = new DnetEntities();
            var securityEEs = GlobalVariables.SecurityEmployees;
            string strSecurityEEs = "'" + string.Join("','", securityEEs) + "'";
            string sql = string.Format("select * from ( SELECT cpt.CompanyID,cpt.ClientID,ept.EmployeeID,(case when cpt.PTOType=1 then 'Vacation' when cpt.PTOType = 2 then 'Sick' end) as PTOPlanID, cpt.PTOType, cpt.Description, cpt.AccrualMethod,cpt.AccrualAmount, cpt.AnniversaryMethod, cpt.Inactive FROM ClientPTOTypes cpt");
            sql = sql + " " + string.Format("join EmployeePTOTypes ept on cpt.CompanyID = ept.CompanyID and cpt.PTOType = ept.PTOType");
            sql = sql + " " + string.Format(" ) data where data.CompanyID = {0} and data.ClientID = '{1}' and data.EmployeeID in ({2})", GlobalVariables.CompanyID, GlobalVariables.Client, strSecurityEEs);
            if (filter != "")
            {
                sql += "AND (" + filter + ") ";
            }
            if (order != "")
            {
                sql += order;
            }
            else
            {
                sql += "ORDER BY data.EmployeeID DESC";
            }
            DataTable data = new DataTable();
            string msg = string.Empty;
            int err = 0;
            data = DataDriver.GetTable("ClientPTOTypes", sql, out err, out msg);
            List<PTOGroup> theView = new List<PTOGroup>();
            foreach (DataRow dr in data.Rows)
            {
                PTOGroup item = new PTOGroup();
                item.PTOID = Convert.ToByte(dr["PTOType"]);
                item.PTOPlanID = FieldTranslation.GetEnumDescription(typeof(enPTOTypes), dr["PTOType"].ToString());
                item.Description = dr["Description"].ToString();
                item.AccrualMethod = FieldTranslation.GetEnumDescription(typeof(enPTOAccrualMethods), Convert.ToInt32(dr["AccrualMethod"]));
                item.AccrualAmount = String.Format("{0:n2}", dr["AccrualAmount"]);
                item.AnniversaryMethod = FieldTranslation.GetEnumDescription(typeof(enPTOAnniversaryMethods), Convert.ToInt32(dr["AnniversaryMethod"]));
                item.Inactive = Convert.ToBoolean(dr["Inactive"]);
                theView.Add(item);
            }
            return theView;
        }
        public void PopulateExcelCell(Excel.Worksheet excelWorkSheet, int xRow, int xCol, string value, bool isBold)
        {
            Excel.Range cell = excelWorkSheet.Cells[xRow, xCol];
            cell.Value2 = value;
            if (isBold)
                cell.Font.Bold = true;
        }
        [HttpPost]
        public void ExportFile(string filePath)
        {
            System.IO.FileInfo file = new System.IO.FileInfo(filePath);
            string outgoingfile = "PTOGrid-" + DateTime.Now.ToString("yyyyMMdd") + ".xls";
            if (file.Exists)
            {
                Response.Clear();
                Response.ClearContent();
                Response.ClearHeaders();
                Response.AddHeader("Content-Disposition", "attachment; filename=" + outgoingfile);
                Response.AddHeader("Content-Length", file.Length.ToString());
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.WriteFile(file.FullName);
                Response.Flush();
                Response.Close();
                System.IO.File.Delete(filePath);
            }
            else
            {
                Response.Write("This file does not exist.");
            }
        }
        //DG - 7346 -09/13/2021 - END

        //DG - 7346 -09/13/2021 - START- Licenses Custom filters
        [HttpPost]
        public ActionResult ExportRequestsLicensesList(string flt, string order)
        {
            try
            {
                IEnumerable<LicenseGroup> requests1 = null;
                IEnumerable<LicenseHR> requests = null;
                string exportFile = string.Empty;
                Excel.Application excelApp = new Excel.Application();
                Excel.Workbook excelWorkBook = null;
                Excel.Worksheet excelWorkSheet = null;
                object misValue = System.Reflection.Missing.Value;
                excelWorkBook = excelApp.Workbooks.Add(misValue);
                excelWorkSheet = excelWorkBook.Worksheets.get_Item(1); // Get the first worksheet
                                                                       //FOR PARENT GRID
                int xRow = 1;
                PopulateExcelCell(excelWorkSheet, xRow, 1, "License ID", true);
                PopulateExcelCell(excelWorkSheet, xRow, 2, "Description", true);
                if ((flt != "" && flt != null) || (order != "" && order != null))
                {
                    requests1 = GetLicenseFilter(flt, order);
                }
                else
                {
                    requests1 = GetLicenses();
                }
                foreach (LicenseGroup request34 in requests1)
                {
                    xRow++;
                    PopulateExcelCell(excelWorkSheet, xRow, 1, request34.LicenseID, false);
                    PopulateExcelCell(excelWorkSheet, xRow, 2, request34.Description, false);
                    xRow++;
                    PopulateExcelCell(excelWorkSheet, xRow, 1, "EmployeeID", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 2, "EmployeeName", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 3, "LicenseNumber", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 4, "EffectiveDate", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 5, "ExpirationDate", true);
                    PopulateExcelCell(excelWorkSheet, xRow, 6, "Inactive", true);
                    requests = GetEELicenses(request34.EncryptedID, false);
                    foreach (LicenseHR request35 in requests)
                    {
                        xRow++;
                        PopulateExcelCell(excelWorkSheet, xRow, 1, request35.EmployeeID.ToString(), false);
                        PopulateExcelCell(excelWorkSheet, xRow, 2, request35.EmployeeName, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 3, request35.LicenseNumber, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 4, request35.EffectiveDate, false);
                        PopulateExcelCell(excelWorkSheet, xRow, 5, request35.ExpirationDate.ToString(), false);
                        PopulateExcelCell(excelWorkSheet, xRow, 6, request35.Inactive.ToString(), false);
                    }
                }
                // Fix column widths
                Excel.Range startCell = excelWorkSheet.Cells[1, 1];
                Excel.Range endCell = excelWorkSheet.Cells[requests.Count() + 1, 9];
                Excel.Range aRange = excelWorkSheet.get_Range(startCell, endCell);
                aRange.Columns.AutoFit();
                var path = Path.Combine(GlobalVariables.CompanyFolder, Folders.PTOGrid);
                bool exists = Directory.Exists(path);
                if (!exists)
                {
                    Directory.CreateDirectory(path);
                }
                string newDocName = "Temp-" + DateTime.Now.ToString("yyyyMMddHHmmssffff") + ".xls";
                exportFile = Path.Combine(path, newDocName);
                excelWorkBook.SaveAs(exportFile, Excel.XlFileFormat.xlWorkbookNormal, misValue, misValue, misValue, misValue, Excel.XlSaveAsAccessMode.xlExclusive, misValue, misValue, misValue, misValue, misValue);
                excelWorkBook.Close(true, misValue, misValue);
                excelApp.Quit();
                return Json(exportFile, JsonRequestBehavior.AllowGet);

            }
            catch (Exception exception)
            {
                return Json(exception.ToString(), JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public void ExportFile1(string filePath)
        {
            System.IO.FileInfo file = new System.IO.FileInfo(filePath);
            string outgoingfile = "License-" + DateTime.Now.ToString("yyyyMMdd") + ".xls";
            if (file.Exists)
            {
                Response.Clear();
                Response.ClearContent();
                Response.ClearHeaders();
                Response.AddHeader("Content-Disposition", "attachment; filename=" + outgoingfile);
                Response.AddHeader("Content-Length", file.Length.ToString());
                Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                Response.WriteFile(file.FullName);
                Response.Flush();
                Response.Close();
                System.IO.File.Delete(filePath);
            }
            else
            {
                Response.Write("This file does not exist.");
            }
        }
        public static IEnumerable<LicenseGroup> GetLicenseFilter(string filter, string order)
        {
            var model = new DnetEntities();
            var securityEEs = GlobalVariables.SecurityEmployees;
            string strSecurityEEs = "'" + string.Join("','", securityEEs) + "'";
            string sql = string.Format("select * from ( select e.EmployeeID, e.FirstName, e.LastName, elc.LicenseCertificationNumber, elc.EffectiveDate, elc.ExpirationDate, elc.Inactive,elc.CompanyID,e.ClientID,lt.Description,lt.LicenseCertificationID as LicenseID  from LicenseTypes lt");
            sql = sql + " " + string.Format("join EmployeeLicensesAndCertifications elc on lt.CompanyID= elc.CompanyID and lt.LicenseCertificationID=elc.LicenseCertificationID");
            sql = sql + " " + string.Format("join Employees e on e.EmployeeID= elc.EmployeeID");
            sql = sql + " " + string.Format(" ) data where data.CompanyID = {0} and data.ClientID = '{1}' and data.EmployeeID in ({2})", GlobalVariables.CompanyID, GlobalVariables.Client, strSecurityEEs);
            if (filter != "")
            {
                sql += "AND (" + filter + ") ";
            }
            if (order != "")
            {
                sql += order;
            }
            else
            {
                sql += "ORDER BY data.EmployeeID DESC";
            }
            DataTable data = new DataTable();
            string msg = string.Empty;
            int err = 0;
            data = DataDriver.GetTable("EmployeeLicensesAndCertifications", sql, out err, out msg);
            List<LicenseGroup> theView = new List<LicenseGroup>();
            foreach (DataRow dr in data.Rows)
            {
                LicenseGroup item = new LicenseGroup();
                item.EmployeeID = dr["EmployeeID"].ToString();
                item.LicenseID = dr["LicenseID"].ToString();
                item.Description = dr["Description"].ToString();
                theView.Add(item);
            }
            return theView;
        }
        //DG - 7346 -09/13/2021 - END
    }
}
