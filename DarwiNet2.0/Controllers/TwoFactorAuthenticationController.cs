using DarwiNet2._0.Core;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Providers.API;
using DarwiNet2._0.Utilities;
using DarwiNet2._0.ViewModels.TwoFactorAuthentication;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Providers.D2;
using DarwiNet2._0.Services.Authentication;
using NotificationType = DarwiNet2._0.Core.NotificationType;

namespace DarwiNet2._0.Controllers
{
    /// <summary>
    ///     Handles all actions relating to two factor authentication.
    /// </summary>
    [IsSessionActive]
    public class TwoFactorAuthenticationController : Controller
    {
        private DnetEntities _dbContext;
        private MfaPolicyService _mfaPolicyService;
        private ICompanyProvider _companyProvider;

        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {

            _dbContext = new DnetEntities();
            _mfaPolicyService = new MfaPolicyService();
            _companyProvider = new CompanyProvider(_dbContext);
            base.Initialize(requestContext);
        }
        protected override void Dispose(bool disposing)
        {
            _dbContext.Dispose();
            base.Dispose(disposing);
        }



        public TwoFactorAuthenticationController()
        {
        }



        public TwoFactorAuthenticationController(DnetEntities dbContext)
        {
            _dbContext = dbContext;
            _mfaPolicyService = new MfaPolicyService();
        }



        /// <summary>
        ///     Provides options to a system level user for configuring two factor authentication.
        /// </summary>
        /// <returns></returns>
        public ActionResult SystemSetup()
        {
            SystemSetupVM vm = new SystemSetupVM();

            // User must be system level
            if (GlobalVariables.DNETLevel != DNetAccessLevel.System)
            {
                return RedirectToAction("Index", "Dashboard");
            }

            Company company = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID);

            // Confirm a company was found
            if (company == null)
            {
                return RedirectToAction("Index", "Error");
            }

            vm.Access = MenuAccess.PageSecurity(MenuItemIds.SystemTwoFactorAuthentication);

            // Check if a SMS provider is connected
            if (!string.IsNullOrEmpty(company.TwilioSID))
            {
                vm.IsSmsProviderConnected = true;
            }

            if (vm.IsSmsProviderConnected)
            {
                vm.EnableTwoFactorSms = company.EnableTwoFactorSms;
            }

            vm.EnableTwoFactorEmail = company.EnableTwoFactorEmail;

            return View(vm);
        }



        /// <summary>
        ///     Saves changes to the system level's two factor authentication configuration.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult SystemSetup(SystemSetupVM vm)
        {
            Company company = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID);

            if (company == null)
            {
                return RedirectToAction("Index", "Error");
            }

            // Disable user access for disabled options
            if (!vm.EnableTwoFactorSms)
            {
                DisableSmsAuthenticationForCompanyUsers();
            }

            if (!vm.EnableTwoFactorEmail)
            {
                DisableEmailAuthenticationForCompanyUsers();
            }

            // Make company changes
            company.EnableTwoFactorSms = vm.EnableTwoFactorSms;
            company.EnableTwoFactorEmail = vm.EnableTwoFactorEmail;

            // Save changes
            _dbContext.SaveChanges();

            // Success!
            vm.SuccessMessage = "All changes were saved successfully!";

            return View(vm);
        }



        /// <summary>
        ///     Disabled SMS authentication for all users of the company who have it enabled and notified them.
        /// </summary>
        /// <returns></returns>
        public void DisableSmsAuthenticationForCompanyUsers()
        {
            List<string> companyUsers = _dbContext.UserRoleClientEmployeeAssignments
                .Where(x => x.CompanyID == GlobalVariables.CompanyID)
                .Select(y => y.UserID)
                .Distinct()
                .ToList();

            List<User> smsUsers = _dbContext.Users.Where(x => x.EnableTwoFactorSms && companyUsers.Contains(x.UserID)).ToList();
            List<KeyValuePair<User, string>> affectedSmsUsers = new List<KeyValuePair<User, string>>();

            foreach (User user in smsUsers)
            {
                // Only disable Text/SMS if this company is the only company through which the user has Text/SMS enabled
                List<int> smsEnabledCompanies = _dbContext.Companies.Where(x => x.EnableTwoFactorSms).Select(y => y.CompanyID).ToList();

                List<int> smsEnabledCompanyUsers = _dbContext.UserRoleClientEmployeeAssignments
                    .Where(x => smsEnabledCompanies.Contains(x.CompanyID) && x.UserID == user.UserID && user.EnableTwoFactorSms)
                    .Select(y => y.CompanyID)
                    .Distinct()
                    .ToList();

                if (smsEnabledCompanyUsers.Count() == 1)
                {
                    // Preserve the user's two factor email to use when sending the notification
                    string previousTwoFactorEmail = user.TwoFactorEmail;

                    user.EnableTwoFactorSms = false;
                    user.TwoFactorPhoneNumber = null;
                    user.TwoFactorSmsToken = null;
                    user.TwoFactorSmsTokenExpirationDate = null;
                    user.TwoFactorPhoneNumberVerified = false;

                    KeyValuePair<User, string> pair = new KeyValuePair<User, string>(user, previousTwoFactorEmail);
                    affectedSmsUsers.Add(pair);
                }
            }

            // Save changes
            _dbContext.SaveChanges();

            // Send notifications to affected users
            foreach (KeyValuePair<User, string> user in affectedSmsUsers)
            {
                SendSmsDisabledByCompanyNotification(user);
            }
        }

        /// <summary>
        ///     Disabled email authentication for all users of the company who have it enabled and notified them.
        /// </summary>
        /// <returns></returns>
        public void DisableEmailAuthenticationForCompanyUsers()
        {
            List<string> companyUsers = _dbContext.UserRoleClientEmployeeAssignments
                .Where(x => x.CompanyID == GlobalVariables.CompanyID)
                .Select(y => y.UserID)
                .Distinct()
                .ToList();

            List<User> emailUsers = _dbContext.Users.Where(x => x.EnableTwoFactorEmail && companyUsers.Contains(x.UserID)).ToList();
            List<KeyValuePair<User, string>> affectedEmailUsers = new List<KeyValuePair<User, string>>();

            foreach (User user in emailUsers)
            {
                // Only disable email if this company is the only company through which the user has email enabled
                List<int> emailEnabledCompanies = _dbContext.Companies.Where(x => x.EnableTwoFactorEmail).Select(y => y.CompanyID).ToList();

                List<int> emailEnabledCompaniesUsers = _dbContext.UserRoleClientEmployeeAssignments
                    .Where(x => emailEnabledCompanies.Contains(x.CompanyID) && x.UserID == user.UserID && user.EnableTwoFactorEmail)
                    .Select(y => y.CompanyID)
                    .Distinct()
                    .ToList();

                if (emailEnabledCompaniesUsers.Count() == 1)
                {
                    // Preserve the user's two factor email to use when sending the notification
                    string previousTwoFactorEmail = user.TwoFactorEmail;

                    user.EnableTwoFactorEmail = false;
                    user.TwoFactorEmail = null;
                    user.TwoFactorEmailToken = null;
                    user.TwoFactorEmailTokenExpirationDate = null;
                    user.TwoFactorEmailVerified = false;

                    KeyValuePair<User, string> pair = new KeyValuePair<User, string>(user, previousTwoFactorEmail);
                    affectedEmailUsers.Add(pair);
                }
            }

            // Save changes
            _dbContext.SaveChanges();

            // Send notifications to affected users
            foreach (KeyValuePair<User, string> user in affectedEmailUsers)
            {
                SendEmailDisabledByCompanyNotification(user);
            }
        }

        /// <summary>
        /// Provides options to a user for configuring their own two factor authentication.
        /// </summary>
        /// <returns></returns>
        public ActionResult UserSetup(VerifyPhoneNumberVM verifyPhoneNumberVm, VerifyEmailVM verifyEmailVm)
        {
            UserSetupVM vm = new UserSetupVM();

            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);

            if (user == null)
            {
                return RedirectToAction("Index", "Error");
            }

            List<int> userCompanies = _dbContext.UserRoleClientEmployeeAssignments
                .Where(x => x.UserID.ToLower() == GlobalVariables.CurrentUser.UserID.ToLower())
                .Select(y => y.CompanyID)
                .Distinct()
                .ToList();
            
            // Check for company MFA policy
            Company companyRequiredSmsTwoFactor = _companyProvider
                .FindCompanyUserAssignedToWithSmsPolicy(user.UserID)
                .FirstOrDefault();

            // Set MFA policy properties
            vm.IsMfaMandatoryForUser = _mfaPolicyService.IsMfaMandatoryForUser(companyRequiredSmsTwoFactor, MfaMethod.Sms);

            // Check if each authentication method is enabled by any of the user's companies
            bool isTwoFactorSmsEnabled = _dbContext.Companies.Any(x => userCompanies.Contains(x.CompanyID) && x.EnableTwoFactorSms);
            vm.IsTwoFactorSmsEnabledByCompany = isTwoFactorSmsEnabled;
            vm.IsTwoFactorEmailEnabledByCompany = _dbContext.Companies.Any(x => userCompanies.Contains(x.CompanyID) && x.EnableTwoFactorEmail);

            if (verifyPhoneNumberVm.RedirectedFromPhoneNumberVerifyModal || verifyEmailVm.RedirectedFromEmailVerifyModal)
            {
                // We are being redirected from a verification screen, so get the existing values from the page
                if (verifyPhoneNumberVm.RedirectedFromPhoneNumberVerifyModal)
                {
                    // Two factor SMS policy is enforced based on company
                    vm.EnableTwoFactorSms = isTwoFactorSmsEnabled; 
                    vm.TwoFactorPhoneNumber = verifyPhoneNumberVm.TwoFactorPhoneNumber;
                    vm.TwoFactorPhoneNumberOriginal = verifyPhoneNumberVm.TwoFactorPhoneNumber;

                    vm.EnableTwoFactorEmail = verifyPhoneNumberVm.EnableTwoFactorEmail;
                    vm.TwoFactorEmail = verifyPhoneNumberVm.TwoFactorEmail;
                    vm.TwoFactorEmailOriginal = verifyPhoneNumberVm.TwoFactorEmail;

                    vm.IsTwoFactorPhoneNumberVerified = verifyPhoneNumberVm.IsTwoFactorPhoneNumberVerified;
                    vm.IsTwoFactorEmailVerified = verifyPhoneNumberVm.IsTwoFactorEmailVerified;
                }

                if (verifyEmailVm.RedirectedFromEmailVerifyModal)
                {
                    vm.EnableTwoFactorSms = verifyEmailVm.EnableTwoFactorSms;
                    vm.TwoFactorPhoneNumber = verifyEmailVm.TwoFactorPhoneNumber;
                    vm.TwoFactorPhoneNumberOriginal = verifyEmailVm.TwoFactorPhoneNumber;

                    vm.EnableTwoFactorEmail = verifyEmailVm.EnableTwoFactorEmail;
                    vm.TwoFactorEmail = verifyEmailVm.TwoFactorEmail;
                    vm.TwoFactorEmailOriginal = verifyEmailVm.TwoFactorEmail;

                    vm.IsTwoFactorPhoneNumberVerified = verifyEmailVm.IsTwoFactorPhoneNumberVerified;
                    vm.IsTwoFactorEmailVerified = verifyEmailVm.IsTwoFactorEmailVerified;
                }

                // Retain all validation errors from before
                TryUpdateModel(vm);
            }
            else
            {
                // We are navigating directly to the User Authentication Settings screen, so load page values from the database
                if (vm.IsTwoFactorSmsEnabledByCompany)
                {
                    vm.EnableTwoFactorSms = user.EnableTwoFactorSms;
                    vm.TwoFactorPhoneNumber = TwoFactorAuthUtilities.GetTwoFactorPhoneNumber(user);
                    vm.TwoFactorPhoneNumberOriginal = vm.TwoFactorPhoneNumber;
                    vm.IsTwoFactorPhoneNumberVerified = user.TwoFactorPhoneNumberVerified;
                }

                if (vm.IsTwoFactorEmailEnabledByCompany)
                {
                    vm.EnableTwoFactorEmail = user.EnableTwoFactorEmail;
                    vm.TwoFactorEmail = TwoFactorAuthUtilities.GetTwoFactorEmail(user);
                    vm.TwoFactorEmailOriginal = vm.TwoFactorEmail;
                    vm.IsTwoFactorEmailVerified = user.TwoFactorEmailVerified;
                }
            }

            vm.SuccessMessage = verifyEmailVm.SuccessMessage;
            vm.ErrorMessage = verifyEmailVm.ErrorMessage;

            // Remove the validator for Text/SMS if Text/SMS is not enabled
            if (!vm.EnableTwoFactorSms)
            {
                ModelState.Remove("TwoFactorPhoneNumber");
            }

            // Remove the validator for email if email is not enabled
            if (!vm.EnableTwoFactorEmail)
            {
                ModelState.Remove("TwoFactorEmail");
            }

            return View(vm);
        }

        /// <summary>
        /// Render the page forcing user to set up and verify their phone number for MFA
        /// </summary>
        public ActionResult UserMfaSetup(int companyId, string userGuid)
        {
            // Disable any caching for this HTTP get
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            Response.Cache.SetNoStore();

            ViewBag.AppContext = new
            {
                CompanyId = companyId,
                UserGuid = userGuid,
            };

            return View("MfaSetup");
        }

        /// <summary>
        /// Render the page allowing user to update their phone number for MFA
        /// </summary>
        public ActionResult UpdatePhoneNumber()
        {
            // Disable any caching for this HTTP get
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            Response.Cache.SetNoStore();

            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);
            if (user == null)
            {
                return RedirectToAction("Index", "Error");
            }

            // Find the company user being assigned to that has SMS two-factor authentication set up
            List<int> companiesUserAssignedTo = _dbContext.UserRoleClientEmployeeAssignments
                .Where(i => i.UserID == user.UserID)
                .Select(i => i.CompanyID)
                .ToList();
            Company companyRequiredSmsTwoFactor = _dbContext.Companies
                .FirstOrDefault(i => companiesUserAssignedTo.Contains(i.CompanyID) && i.EnableTwoFactorSms && i.TwilioAccountVerified);

            if (companyRequiredSmsTwoFactor == null)
            {
                // If no company requires MFA, redirect back to user setup
                return RedirectToAction("UserSetup");
            }

            ViewBag.AppContext = new
            {
                CompanyId = companyRequiredSmsTwoFactor.CompanyID,
                UserGuid = user.UserGuid.ToString(),
            };

            return View("MfaSetup");
        }

        /// <summary>
        ///     Determines which action was taken in the view and performs it.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult UserSetup(UserSetupVM vm)
        {
            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);

            if (user == null)
            {
                return RedirectToAction("Index", "Error");
            }

            // Remove the validator for Text/SMS if Text/SMS is not enabled
            if (!vm.EnableTwoFactorSms)
            {
                ModelState.Remove("TwoFactorPhoneNumber");
            }

            // Remove the validator for email if email is not enabled
            if (!vm.EnableTwoFactorEmail)
            {
                ModelState.Remove("TwoFactorEmail");
            }

            // Which action was taken in the view?
            if (vm.UpdatePhoneNumber)
            {
                // Redirect to phone number update flow
                return RedirectToAction("UpdatePhoneNumber");
            }
            else if (vm.OpenVerifyPhoneNumberModal)
            {
                // Verify phone number
                vm = OpenVerifyPhoneNumberModal(vm, user);
            }
            else if (vm.OpenVerifyEmailModal)
            {
                // Verify email
                vm = OpenVerifyEmailModal(vm);
            }
            else
            {
                // Save
                vm = Save(vm, user);
            }

            return View(vm);
        }



        /// <summary>
        ///     Collects the data needed and opens the verify SMS modal.
        /// </summary>
        /// <param name="vm"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        private UserSetupVM OpenVerifyPhoneNumberModal(UserSetupVM vm, User user)
        {
            // Repopulate the saved email if it was not returned (due to being hidden) on postback
            if (vm.TwoFactorEmail == null)
            {
                vm.TwoFactorEmail = TwoFactorAuthUtilities.GetTwoFactorEmail(user);
            }

            // Stop if the data is invalid
            if (!ModelState.IsValid)
            {
                vm.OpenVerifyPhoneNumberModal = false;
                return vm;
            }

            return vm;
        }



        /// <summary>
        ///     Collects the data needed and opens the verify email modal.
        /// </summary>
        /// <param name="vm"></param>
        /// <returns></returns>
        private UserSetupVM OpenVerifyEmailModal(UserSetupVM vm)
        {
            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);

            // Repopulate the saved phone number if it was not returned (due to being hidden) on postback
            if (vm.TwoFactorPhoneNumber == null)
            {
                vm.TwoFactorPhoneNumber = TwoFactorAuthUtilities.GetTwoFactorPhoneNumber(user);
            }

            // Stop if the data is invalid
            if (!ModelState.IsValid)
            {
                vm.OpenVerifyEmailModal = false;
                return vm;
            }

            return vm;
        }



        /// <summary>
        ///     Saves the information submitted on the UserSetup view.
        /// </summary>
        /// <param name="vm"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        private UserSetupVM Save(UserSetupVM vm, User user)
        {
            string phoneNumber = null;

            // Add a validator for Text/SMS if it is enabled but not verified
            if (vm.EnableTwoFactorSms && vm.SmsNeedsVerification)
            {
                ModelState.AddModelError("TwoFactorPhoneNumber", "Please verify your phone number.");
            }

            // Add a validator for email if it is enabled but not verified
            if (vm.EnableTwoFactorEmail && vm.EmailNeedsVerification)
            {
                ModelState.AddModelError("TwoFactorEmail", "Please verify your email address.");
            }

            // Stop if the data is invalid
            if (!ModelState.IsValid)
            {
                return vm;
            }

            // Send user setup change notifications
            if (!user.EnableTwoFactorSms && vm.EnableTwoFactorSms) { SendSmsEnabledByUserNotification(user, vm.TwoFactorEmail); }
            if (!user.EnableTwoFactorEmail && vm.EnableTwoFactorEmail) { SendEmailEnabledByUserNotification(user, vm.TwoFactorEmail); }
            if (user.EnableTwoFactorSms && !vm.EnableTwoFactorSms) { SendSmsDisabledByUserNotification(user, vm.TwoFactorEmail); }
            if (user.EnableTwoFactorEmail && !vm.EnableTwoFactorEmail) { SendEmailDisabledByUserNotification(user); }

            // Update the user
            user.EnableTwoFactorSms = vm.EnableTwoFactorSms;

            // Strip extra characters from the phone number
            if (!string.IsNullOrEmpty(vm.TwoFactorPhoneNumber))
            {
                phoneNumber = vm.TwoFactorPhoneNumber;
                phoneNumber = phoneNumber.Replace("(", "");
                phoneNumber = phoneNumber.Replace(")", "");
                phoneNumber = phoneNumber.Replace(" ", "");
                phoneNumber = phoneNumber.Replace(".", "");
                phoneNumber = phoneNumber.Replace("-", "");
            }

            user.TwoFactorPhoneNumber = phoneNumber;

            user.EnableTwoFactorEmail = vm.EnableTwoFactorEmail;
            user.TwoFactorEmail = vm.TwoFactorEmail;

            // Un-verify authentication methods if they are disabled
            if (!user.EnableTwoFactorSms)
            {
                user.TwoFactorPhoneNumberVerified = false;
                vm.IsTwoFactorPhoneNumberVerified = false;
            }
            else
            {
                user.TwoFactorPhoneNumberVerified = vm.IsTwoFactorPhoneNumberVerified;
            }

            if (!user.EnableTwoFactorEmail)
            {
                user.TwoFactorEmailVerified = false;
                vm.IsTwoFactorEmailVerified = false;
            }
            else
            {
                user.TwoFactorEmailVerified = vm.IsTwoFactorEmailVerified;
            }

            // Invalidate all cookies if the user turns off all authentication methods
            if (!user.EnableTwoFactorSms && !user.EnableTwoFactorEmail)
            {
                user.TwoFactorCookieValue = null;
            }

            // Save changes
            //_dbContext.Users.Add(user);
            _dbContext.SaveChanges();

            // Return formatted and/or repopulate phone number and email
            vm.TwoFactorPhoneNumber = TwoFactorAuthUtilities.GetTwoFactorPhoneNumber(user);
            vm.TwoFactorEmail = TwoFactorAuthUtilities.GetTwoFactorEmail(user);
            ModelState.Clear();

            // Success!
            vm.SuccessMessage = "All changes were saved successfully!";

            return vm;
        }



        /// <summary>
        ///     Verifies a user's phone number to use for two factor authentication.
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <param name="c"></param>
        /// <param name="d"></param>
        /// <param name="f"></param>
        /// <param name="g"></param>
        /// <returns></returns>
        public ActionResult VerifyPhoneNumber(bool a, string b, bool c, bool d, string f, bool g)
        {
            // NOTE: *** cannot use parameter "e" in a query string here due to logic in IsSessionActiveController.OnActionExecuting() ***

            bool enableTwoFactorSms = a;
            string twoFactorPhoneNumber = b;
            bool isTwoFactorPhoneNumberVerified = c;
            bool enableTwoFactorEmail = d;
            string twoFactorEmail = f;
            bool isTwoFactorEmailVerified = g;

            VerifyPhoneNumberVM vm = new VerifyPhoneNumberVM();

            Company company = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID);
            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);

            if (user == null || company == null)
            {
                return RedirectToAction("Index", "Error");
            }

            // Send authentication code
            var twoFactorAuthProvider = new TwoFactorAuthenticationProvider(_dbContext);
            if (bool.TryParse(ConfigurationManager.AppSettings["UseTwilioVerify"], out var useTwilioVerify) && useTwilioVerify)
            {
                vm.VerificationSid = twoFactorAuthProvider.SendTwilioTwoFactor(company.TwilioSID,
                                                                               company.TwilioAuthToken,
                                                                               company.TwilioVerificationSID,
                                                                               string.Format("+1{0}", twoFactorPhoneNumber),
                                                                               "sms");
            }
            else
            {
                twoFactorAuthProvider.SendTwoFactorCodeSmsMessage(company, user, twoFactorPhoneNumber);
            }

            // Preserve data needed to keep the state of the UserSetup view
            vm.EnableTwoFactorSms = enableTwoFactorSms;
            vm.TwoFactorPhoneNumber = twoFactorPhoneNumber;
            vm.IsTwoFactorPhoneNumberVerified = isTwoFactorPhoneNumberVerified;

            vm.EnableTwoFactorEmail = enableTwoFactorEmail;
            vm.TwoFactorEmail = twoFactorEmail;
            vm.IsTwoFactorEmailVerified = isTwoFactorEmailVerified;

            return View(vm);
        }



        /// <summary>
        ///     Verifies a user's phone number to use for two factor authentication.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult VerifyPhoneNumber(VerifyPhoneNumberVM vm)
        {
            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);

            if (user == null)
            {
                return RedirectToAction("Index", "Error");
            }

            // Did the user enter the correct and valid authentication code?
            Company company = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == GlobalVariables.CompanyID);
            if (company != null && bool.TryParse(ConfigurationManager.AppSettings["UseTwilioVerify"], out var useTwilioVerify) && useTwilioVerify)
            {
                var twoFactorAuthProvider = new TwoFactorAuthenticationProvider(_dbContext);
                if (twoFactorAuthProvider.VerifyTwilioTwoFactor(company.TwilioSID, company.TwilioAuthToken, company.TwilioVerificationSID, vm.TwoFactorSmsToken, vm.VerificationSid))
                {
                    // Yes, so save all their SMS authentication settings
                    vm.IsTwoFactorPhoneNumberVerified = true;

                    user.TwoFactorSmsToken = null;
                    user.TwoFactorSmsTokenExpirationDate = null;

                    vm.InfoMessage = "Your phone number has been successfully verified!  Make sure to save your changes before leaving.";
                    vm.SuccessfulVerification = true;

                    //_dbContext.Users.Add(user);
                    _dbContext.SaveChanges();
                }
                else
                {
                    vm.ErrorMessage = "Invalid verification code.  Please try verifying again.";
                    vm.SuccessfulVerification = false;
                }
            }
            else if (vm.TwoFactorSmsToken == user.TwoFactorSmsToken &&
                user.TwoFactorSmsTokenExpirationDate != null &&

                user.TwoFactorSmsTokenExpirationDate > DateTime.Now)
            {
                // Yes, so save all their SMS authentication settings
                vm.IsTwoFactorPhoneNumberVerified = true;

                user.TwoFactorSmsToken = null;
                user.TwoFactorSmsTokenExpirationDate = null;

                vm.InfoMessage = "Your phone number has been successfully verified!  Make sure to save your changes before leaving.";
                vm.SuccessfulVerification = true;

                //_dbContext.Users.Add(user);
                _dbContext.SaveChanges();
            }
            else
            {
                // No
                vm.ErrorMessage = "Invalid verification code.  Please try verifying again.";
                vm.SuccessfulVerification = false;
            }

            vm.RedirectedFromPhoneNumberVerifyModal = true;

            return RedirectToAction("UserSetup", vm);
        }



        /// <summary>
        ///     Verifies a user's email address to use for two factor authentication.
        /// </summary>
        /// <returns></returns>
        public ActionResult VerifyEmail(bool a, string b, bool c, bool d, string f, bool g)
        {
            // NOTE: *** cannot use parameter "e" in a query string here due to logic in IsSessionActiveController.OnActionExecuting() ***

            bool enableTwoFactorSms = a;
            string twoFactorPhoneNumber = b;
            bool isTwoFactorPhoneNumberVerified = c;
            bool enableTwoFactorEmail = d;
            string twoFactorEmail = f;
            bool isTwoFactorEmailVerified = g;

            VerifyEmailVM vm = new VerifyEmailVM();

            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);

            // Send authentication code
            var twoFactorAuthProvider = new TwoFactorAuthenticationProvider(_dbContext);
            twoFactorAuthProvider.SendTwoFactorCodeEmailMessage(user, twoFactorEmail);

            // Preserve data needed to keep the state of the UserSetup view
            vm.EnableTwoFactorSms = enableTwoFactorSms;
            vm.TwoFactorPhoneNumber = twoFactorPhoneNumber;
            vm.IsTwoFactorPhoneNumberVerified = isTwoFactorPhoneNumberVerified;

            vm.EnableTwoFactorEmail = enableTwoFactorEmail;
            vm.TwoFactorEmail = twoFactorEmail;
            vm.IsTwoFactorEmailVerified = isTwoFactorEmailVerified;

            return View(vm);
        }



        /// <summary>
        ///     Verifies a user's email address to use for two factor authentication.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult VerifyEmail(VerifyEmailVM vm)
        {
            User user = _dbContext.Users.FirstOrDefault(x => x.UserID == GlobalVariables.CurrentUser.UserID);

            if (user == null)
            {
                return RedirectToAction("Index", "Error");
            }

            // Did the user enter the correct and valid authentication code?

            if (vm.TwoFactorEmailToken == user.TwoFactorEmailToken &&
                user.TwoFactorEmailTokenExpirationDate != null &&
                user.TwoFactorEmailTokenExpirationDate > DateTime.Now)
            {
                // Yes, so save all their email authentication settings
                vm.IsTwoFactorEmailVerified = true;

                user.TwoFactorEmailToken = null;
                user.TwoFactorEmailTokenExpirationDate = null;

                vm.InfoMessage = "Your email address has been successfully verified!  Make sure to save your changes before leaving.";
                vm.SuccessfulVerification = true;

                //_dbContext.Users.Add(user);
                _dbContext.SaveChanges();
            }
            else
            {
                // No
                vm.ErrorMessage = "Invalid verification code.  Please try verifying again.";
                vm.SuccessfulVerification = false;
            }

            vm.RedirectedFromEmailVerifyModal = true;

            return RedirectToAction("UserSetup", vm);
        }



        /// <summary>
        ///     Sends an email notification that the SMS authentication method set up by a user has been disabled.
        /// </summary>
        /// <returns></returns>
        private void SendSmsDisabledByCompanyNotification(KeyValuePair<User, string> user)
        {
            Notifications notification = new Notifications();
            notification.Initialize(NotificationType.TwoFactorSmsDisabledByCompany);
            notification.NotificationInfo(NotificationType.TwoFactorSmsDisabledByCompany, user.Key);
            string mailResult = "";
            string notificationEmail = "";

            // Determine where to send the notification
            if (!string.IsNullOrEmpty(user.Key.Email))
            {
                notificationEmail = user.Key.Email;
            }
            else
            {
                // We do this because not all users have an Email listed.  However, this may also be empty.
                notificationEmail = user.Value;
            }

            // *** Note: We are not even going to try to send the user a text notification, because that will charge the company who disabled 
            //           the texting service, and the cost is most likely the reason why they have turned it off.

            if (!string.IsNullOrEmpty(notificationEmail))
            {
                // Send the notification
                if (!SmtpEmail.SendSmtpEmail(notificationEmail, user.Key.Name, notification.Subject, notification.Body, null, false,
                    NotificationType.TwoFactorSmsDisabledByCompany, null, out mailResult))
                {
                    // Log failed emails
                    Exception ex = new Exception("Failed sending \"Two Factor Authentication SMS Disabled By Company\" notification to " + notificationEmail);
                }
            }
        }



        /// <summary>
        ///     Sends an email notification that the email authentication method set up by a user has been disabled.
        /// </summary>
        /// <returns></returns>
        private void SendEmailDisabledByCompanyNotification(KeyValuePair<User, string> user)
        {
            Notifications notification = new Notifications();
            notification.Initialize(NotificationType.TwoFactorEmailDisabledByCompany);
            notification.NotificationInfo(NotificationType.TwoFactorEmailDisabledByCompany, user.Key);
            string mailResult = "";
            string notificationEmail = "";

            // Determine where to send the notification
            if (!string.IsNullOrEmpty(user.Key.Email))
            {
                notificationEmail = user.Key.Email;
            }
            else
            {
                // We do this because not all users have an Email listed
                notificationEmail = user.Value;
            }

            // Send the notification
            if (!SmtpEmail.SendSmtpEmail(notificationEmail, user.Key.Name, notification.Subject, notification.Body, null, false,
                NotificationType.TwoFactorEmailDisabledByCompany, null, out mailResult))
            {
                // Log failed emails

                Exception ex = new Exception("Failed sending \"Two Factor Authentication Email Disabled By Company\" notification to " + notificationEmail);


            }
        }



        /// <summary>
        ///     Sends an email notification that the SMS authentication method set up by a user has been enabled.
        /// </summary>
        /// <returns></returns>
        private void SendSmsEnabledByUserNotification(User user, string twoFactorEmail)
        {
            Notifications notification = new Notifications();
            notification.Initialize(NotificationType.TwoFactorSmsEnabledByUser);
            notification.NotificationInfo(NotificationType.TwoFactorSmsEnabledByUser, user);
            string mailResult = "";
            string notificationEmail = "";

            // Determine where to send the notification
            if (!string.IsNullOrEmpty(user.Email))
            {
                notificationEmail = user.Email;
            }
            else
            {
                // We do this because not all users have an Email listed.  However, this may also be empty.
                notificationEmail = twoFactorEmail;
            }

            if (!string.IsNullOrEmpty(notificationEmail))
            {
                // Send the notification
                if (!SmtpEmail.SendSmtpEmail(notificationEmail, user.Name, notification.Subject, notification.Body, null, false,
                    NotificationType.TwoFactorSmsEnabledByUser, null, out mailResult))
                {
                    // Log failed emails
                    Exception ex = new Exception("Failed sending \"Two Factor Authentication SMS Enabled By User\" notification to " + notificationEmail);


                }
            }
        }



        /// <summary>
        ///     Sends an email notification that the email authentication method set up by a user has been enabled.
        /// </summary>
        /// <returns></returns>
        private void SendEmailEnabledByUserNotification(User user, string twoFactorEmail)
        {
            Notifications notification = new Notifications();
            notification.Initialize(NotificationType.TwoFactorEmailEnabledByUser);
            notification.NotificationInfo(NotificationType.TwoFactorEmailEnabledByUser, user);
            string mailResult = "";
            string notificationEmail = "";

            // Determine where to send the notification
            if (!string.IsNullOrEmpty(user.Email))
            {
                notificationEmail = user.Email;
            }
            else
            {
                // We do this because not all users have an Email listed
                notificationEmail = twoFactorEmail;
            }

            // Send the notification
            if (!SmtpEmail.SendSmtpEmail(notificationEmail, user.Name, notification.Subject, notification.Body, null, false,
                NotificationType.TwoFactorEmailEnabledByUser, null, out mailResult))
            {
                // Log failed emails
                Exception ex = new Exception("Failed sending \"Two Factor Authentication Email Enabled By User\" notification to " + notificationEmail);


            }
        }



        /// <summary>
        ///     Sends an email notification that the SMS authentication method set up by a user has been disabled.
        /// </summary>
        /// <returns></returns>
        private void SendSmsDisabledByUserNotification(User user, string twofactorEmail)
        {
            Notifications notification = new Notifications();
            notification.Initialize(NotificationType.TwoFactorSmsDisabledByUser);
            notification.NotificationInfo(NotificationType.TwoFactorSmsDisabledByUser, user);
            string mailResult = "";
            string notificationEmail = "";

            // Determine where to send the notification
            if (!string.IsNullOrEmpty(user.Email))
            {
                notificationEmail = user.Email;
            }
            else
            {
                // We do this because not all users have an Email listed.  However, this may also be empty.
                notificationEmail = twofactorEmail;
            }

            if (!string.IsNullOrEmpty(notificationEmail))
            {
                // Send the notification
                if (!SmtpEmail.SendSmtpEmail(notificationEmail, user.Name, notification.Subject, notification.Body, null, false,
                    NotificationType.TwoFactorSmsDisabledByUser, null, out mailResult))
                {
                    // Log failed emails
                    Exception ex = new Exception("Failed sending \"Two Factor Authentication SMS Disabled By User\" notification to " + notificationEmail);


                }
            }
        }



        /// <summary>
        ///     Sends an email notification that the email authentication method set up by a user has been disabled.
        /// </summary>
        /// <returns></returns>
        private void SendEmailDisabledByUserNotification(User user)
        {
            Notifications notification = new Notifications();
            notification.Initialize(NotificationType.TwoFactorEmailDisabledByUser);
            notification.NotificationInfo(NotificationType.TwoFactorEmailDisabledByUser, user);
            string mailResult = "";
            string notificationEmail = "";

            // Determine where to send the notification
            if (!string.IsNullOrEmpty(user.Email))
            {
                notificationEmail = user.Email;
            }
            else
            {
                // We do this because not all users have an Email listed
                notificationEmail = user.TwoFactorEmail;
            }

            // Send the notification
            if (!SmtpEmail.SendSmtpEmail(notificationEmail, user.Name, notification.Subject, notification.Body, null, false,
                NotificationType.TwoFactorEmailDisabledByUser, null, out mailResult))
            {
                // Log failed emails
                Exception ex = new Exception("Failed sending \"Two Factor Authentication Email Disabled By User\" notification to " + notificationEmail);


            }
        }

        // <summary>
        //     Specifies an SMS message to be returned to the user when the user sends an SMS message to our Twilio phone number.
        //     
        //     NOTE: 
        //     Return messages are turned off (When on, we are charged for every message sent to our number).
        //     To turn this feature back on, enter a Webhook under Messaging in our Twilio account at https://www.twilio.com/console/phone-numbers/
        //     
        //     Webhook value example: https://d3.darwinet.com/DNET_TESTd15/Sms/ReceiveSms
        // </summary>
        // <returns></returns>
        //public ActionResult ReceiveSms()
        //{
        // *** KEEP for the comments ***
        //}


        /// <summary>
        ///     Sends an authentication code to the user and redirects to a page where they can enter it to authenticate.
        /// </summary>
        /// <param name="a"></param>
        /// <param name="b"></param>
        /// <param name="c"></param>
        /// <returns></returns>
        public ActionResult VerifyUser(string a, bool b = false)
        {
            VerifyUserVM vm = new VerifyUserVM();

            // NOTE: A Guid is used here to identify the user so that it is nearly impossible for a user to casually 
            //       manipulate the query string and impersonate another user.
            string userGuid = a;
            bool attemptEmailOverride = b;

            // Get User
            User user = _dbContext.Users
                .FirstOrDefault(x =>
                    x.UserGuid.ToString().ToUpper() == userGuid.ToUpper() &&
                    x.Enabled);

            if (user == null)
            {
                return RedirectToAction("Index", "Error");
            }

            // Get companies
            List<int> userCompanyIds = _dbContext.UserRoleClientEmployeeAssignments.Where(x => x.UserID == user.UserID).Select(x => x.CompanyID).ToList();

            // Find the first company with a verified Twilio Account
            Company companyWithTwilioEnabled = null;

            foreach (int companyId in userCompanyIds)
            {
                companyWithTwilioEnabled = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == companyId && x.TwilioAccountVerified);

                if (companyWithTwilioEnabled != null)
                {
                    break;
                }
            }

            // Find the first company with Two Factor Email enabled
            Company companyWithTwoFactorEmailEnabled = null;

            foreach (int companyId in userCompanyIds)
            {
                companyWithTwoFactorEmailEnabled = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == companyId && x.EnableTwoFactorEmail);

                if (companyWithTwoFactorEmailEnabled != null)
                {
                    break;
                }
            }

            WelcomePageSetup thisSetup = _dbContext.WelcomePageSetups.FirstOrDefault();

            RegexUtilities regexUtilities = new RegexUtilities();

            // Get logos
            if (thisSetup != null)
            {
                vm.LogoURL = GlobalVariables.DNetURL + "Assets/" + thisSetup.WelcomeLogo;


                if (!string.IsNullOrEmpty(thisSetup.WelcomeBackground))
                {
                    GlobalVariables.WelcomeBackground = GlobalVariables.DNetURL + "Assets/" + thisSetup.WelcomeBackground;
                }
                else
                {
                    GlobalVariables.WelcomeBackground = null;
                }
            }

            vm.UserGuid = userGuid;

            vm.EnableTwoFactorSms = user.EnableTwoFactorSms;
            vm.EnableTwoFactorEmail = user.EnableTwoFactorEmail;

            bool emailOverride = user.EnableTwoFactorEmail &&
                companyWithTwoFactorEmailEnabled != null &&
                attemptEmailOverride == true;

            // The user wants to use email authentication instead of SMS, so clear the SMS token data
            if (emailOverride)
            {
                user.TwoFactorSmsToken = null;
                user.TwoFactorSmsTokenExpirationDate = null;
            }

            // Determine which two factor authentication method to use
            if (user.EnableTwoFactorSms && companyWithTwilioEnabled != null && companyWithTwilioEnabled.EnableTwoFactorSms && companyWithTwilioEnabled.TwilioAccountVerified && !emailOverride)
            {
                // Text/SMS
                vm.UsingTwoFactorSms = true;
                vm.TwoFactorPhoneNumber = regexUtilities.MaskPhoneNumber(user.TwoFactorPhoneNumber);

                // Send authentication code
                var twoFactorAuthProvider = new TwoFactorAuthenticationProvider(_dbContext);

                if (bool.TryParse(ConfigurationManager.AppSettings["UseTwilioVerify"], out var useTwilioVerify) && useTwilioVerify)
                {
                    vm.VerificationSid = twoFactorAuthProvider.SendTwilioTwoFactor(companyWithTwilioEnabled.TwilioSID, 
                                                                                   companyWithTwilioEnabled.TwilioAuthToken, 
                                                                                   companyWithTwilioEnabled.TwilioVerificationSID, 
                                                                                   string.Format("+1{0}", user.TwoFactorPhoneNumber), 
                                                                                   "sms");
                    return View(vm);
                }

                twoFactorAuthProvider.SendTwoFactorCodeSmsMessage(companyWithTwilioEnabled, user, user.TwoFactorPhoneNumber);
                return View(vm);
            }
            else if (user.EnableTwoFactorEmail && companyWithTwoFactorEmailEnabled != null)
            {
                // Email
                vm.UsingTwoFactorEmail = true;
                vm.TwoFactorEmail = regexUtilities.MaskEmail(user.TwoFactorEmail);

                // Send authentication code
                var twoFactorAuthProvider = new TwoFactorAuthenticationProvider(_dbContext);
                twoFactorAuthProvider.SendTwoFactorCodeEmailMessage(user, user.TwoFactorEmail);

                return View(vm);
            }
            else
            {
                // None
                return RedirectToAction("Login", "Home", new { b = vm.UserGuid });
            }
        }



        /// <summary>
        ///     Verifies that the user entered the correct authentication
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult VerifyUser(VerifyUserVM vm)
        {
            // Get user
            User user = _dbContext.Users
                .FirstOrDefault(x =>
                    x.UserGuid.ToString().ToUpper() == vm.UserGuid.ToUpper() &&
                    x.Enabled);

            // Get company
            Company company = null;
            List<int> userCompanyIds = _dbContext.UserRoleClientEmployeeAssignments.Where(x => x.UserID == user.UserID).Select(x => x.CompanyID).ToList();

            foreach (int companyId in userCompanyIds)
            {
                if (vm.UsingTwoFactorSms)
                {
                    // Find the first company with a verified Twilio Account
                    company = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == companyId && x.TwilioAccountVerified);

                    if (company != null)
                    {
                        break;
                    }
                }

                if (vm.UsingTwoFactorEmail)
                {
                    // Find the first company with a Two Factor Email enabled
                    company = _dbContext.Companies.FirstOrDefault(x => x.CompanyID == companyId && x.EnableTwoFactorEmail);

                    if (company != null)
                    {
                        break;
                    }
                }
            }

            if (user == null || company == null)
            {
                vm.ErrorMessage = "An error has occurred. Please try again. If the issue continues please contact support.";

                return View(vm);
            }

            bool.TryParse(ConfigurationManager.AppSettings["UseTwilioVerify"], out var useTwilioVerify);

            // Resending the authentication code
            if (vm.ResendAuthenticationCode)
            {
                var twoFactorAuthProvider = new TwoFactorAuthenticationProvider(_dbContext);
                if (vm.UsingTwoFactorSms && useTwilioVerify)
                {
                    vm.VerificationSid = twoFactorAuthProvider.SendTwilioTwoFactor(company.TwilioSID, 
                                                                                   company.TwilioAuthToken, 
                                                                                   company.TwilioVerificationSID, 
                                                                                   string.Format("+1{0}", user.TwoFactorPhoneNumber), 
                                                                                   "sms");
                }
                else if (vm.UsingTwoFactorSms)
                {
                    twoFactorAuthProvider.SendTwoFactorCodeSmsMessage(company, user, user.TwoFactorPhoneNumber);
                }
                else if (vm.UsingTwoFactorEmail)
                {
                    twoFactorAuthProvider.SendTwoFactorCodeEmailMessage(user, user.TwoFactorEmail);
                }

                vm.ResendAuthenticationCode = false;
                vm.InfoMessage = "Your verification code has been re-sent.";

                return View(vm);
            }

            // Validate the selected authentication method, prioritizing Text/SMS before Email
            if (vm.UsingTwoFactorSms && useTwilioVerify)
            {
                var twoFactorAuthProvider = new TwoFactorAuthenticationProvider(_dbContext);
                if (!twoFactorAuthProvider.VerifyTwilioTwoFactor(company.TwilioSID, company.TwilioAuthToken, company.TwilioVerificationSID, vm.TwoFactorSmsToken, vm.VerificationSid))
                {
                    vm.ErrorMessage = "Invalid verification code.";
                    vm.TwoFactorSmsToken = "";
                    return View(vm);
                }
            }
            else if (vm.UsingTwoFactorSms)
            {
                if (vm.TwoFactorSmsToken != user.TwoFactorSmsToken ||
                    user.TwoFactorSmsTokenExpirationDate == null ||
                    user.TwoFactorSmsTokenExpirationDate <= DateTime.Now)
                {
                    vm.ErrorMessage = "Invalid verification code.";
                    vm.TwoFactorSmsToken = "";

                    return View(vm);
                }
            }
            else if (vm.UsingTwoFactorEmail)
            {
                if (vm.TwoFactorEmailToken != user.TwoFactorEmailToken ||
                    user.TwoFactorEmailTokenExpirationDate == null ||
                    user.TwoFactorEmailTokenExpirationDate <= DateTime.Now)
                {
                    vm.ErrorMessage = "Invalid verification code.";
                    vm.TwoFactorEmailToken = "";

                    return View(vm);
                }
            }

            // Clear token data
            user.TwoFactorSmsToken = null;
            user.TwoFactorSmsTokenExpirationDate = null;

            user.TwoFactorEmailToken = null;
            user.TwoFactorEmailTokenExpirationDate = null;

            //_dbContext.Users.Add(user);
            _dbContext.SaveChanges();

            // Create a cookie to indicate a trusted device
            if (vm.TrustedDevice)
            {
                string cookieName = ConCryptor.Encrypt(user.UserID.ToLower());
                Guid? cookieValue = GetCookieValue(user);

                HttpCookie cookie = new HttpCookie(cookieName, cookieValue.ToString());
                cookie.Expires = DateTime.Now.AddDays(30);

                Response.Cookies.Add(cookie);
            }

            return RedirectToAction("Login", "Home", new { b = vm.UserGuid });
        }



        /// <summary>
        ///     Gets the user's saved cookie value or creates a new one.
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public Guid? GetCookieValue(User user)
        {
            Guid? cookieValue;
            // Create a new value if one does not exist.  Otherwise use the existing value.
            if (string.IsNullOrEmpty(user.TwoFactorCookieValue.ToString()))
            {
                cookieValue = Guid.NewGuid();

                user.TwoFactorCookieValue = cookieValue;

                _dbContext.SaveChanges();
            }
            else
            {
                cookieValue = user.TwoFactorCookieValue;
            }

            return cookieValue;
        }
    }
}