using DarwiNet2._0.Data;

namespace DarwiNet2._0.Services.Authentication
{
    public enum MfaMethod
    {
        Sms,
        Email
    }

    public class MfaPolicyService
    {
        /// <summary>
        /// Check if a user is required to set up their MFA per their company policy in case they don't have one already
        /// </summary>
        /// <param name="user">The user to check</param>
        /// <param name="userAssignedCompany">The company that the user being assigned to</param>
        /// <param name="mfaMethod">The MFA policy type (email/sms/etc...)</param>
        /// <returns>Whether the user should set up their MFA method now</returns>
        public bool IsMfaSetupRequired(User user, Company userAssignedCompany, MfaMethod mfaMethod)
        {
            // Email MFA set up is not yet implemented
            if (mfaMethod == MfaMethod.Email)
            {
                return false;
            }
            
            // Mfa is considered correctly set up if flag is turned on and phone number is valid
            bool isUserMfaSetup = user.TwoFactorPhoneNumberVerified && !string.IsNullOrEmpty(user.TwoFactorPhoneNumber);

            // Set up is required if company enforces policy and user has not set it up
            return IsMfaMandatoryForUser(userAssignedCompany, mfaMethod) && isUserMfaSetup == false;
        }

        /// <summary>
        /// Check if a user has mandatory MFA enforced by company policy
        /// </summary>
        /// <param name="userAssignedCompany">The company that the user being assigned to</param>
        /// <param name="mfaMethod">The MFA policy type (email/sms/etc...)</param>
        /// <returns>Whether MFA is mandatory for this user</returns>
        public bool IsMfaMandatoryForUser(Company userAssignedCompany, MfaMethod mfaMethod)
        {
            if (userAssignedCompany == null)
            {
                return false;
            }
            
            return mfaMethod == MfaMethod.Email ? 
                userAssignedCompany.EnableTwoFactorEmail :
                userAssignedCompany.EnableTwoFactorSms;
        }
    }
}