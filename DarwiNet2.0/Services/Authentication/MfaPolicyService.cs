using DarwiNet2._0.Data;

namespace DarwiNet2._0.Services.Authentication
{
    public enum MfaMethod
    {
        Sms,
        Email
    }

    public class MfaPolicyService
    {
        /// <summary>
        /// Check if a user is required to set up their MFA per their company policy in case they don't have one already 
        /// </summary>
        /// <param name="user">The user to check</param>
        /// <param name="companyRequiredSmsTwoFactor">The company that the user being assigned to</param>
        /// <param name="mfaMethod">The MFA policy type (email/sms/etc...)</param>
        /// <returns>Whether the user should set up their MFA method now</returns>
        public bool IsMfaSetupRequired(User user, Company companyRequiredSmsTwoFactor, MfaMethod mfaMethod)
        {
            // Email MFA is not currently supported
            if (mfaMethod == MfaMethod.Email)
            {
                return false;
            }

            // Mfa is considered correctly set up if flag is turned on and phone number is valid
            bool isUserMfaSetup = user.TwoFactorPhoneNumberVerified && !string.IsNullOrEmpty(user.TwoFactorPhoneNumber);

            // Set up is required if company enforces policy and user has not set it up
            return companyRequiredSmsTwoFactor != null && isUserMfaSetup == false;
        }

        // /// <summary>
        // /// Check if a user is required to set up their MFA per their company policy in case they don't have one already 
        // /// </summary>
        // /// <param name="user">The user to check</param>
        // /// <param name="mfaMethod">The MFA policy type (email/sms/etc...)</param>
        // /// <returns>Whether the user should set up their MFA method now</returns>
        // public async Task<bool> IsMfaSetupRequiredAsync(User user, MfaMethod mfaMethod)
        // {
        //     // Email MFA is not currently supported
        //     if (mfaMethod == MfaMethod.Email)
        //     {
        //         return false;
        //     }
        //
        //     // Find the company user being assigned to that has SMS two-factor authentication set up
        //     List<int> companiesUserAssignedTo = await _dbContext.UserRoleClientEmployeeAssignments
        //         .Where(i => i.UserID == user.UserID)
        //         .Select(i => i.CompanyID)
        //         .ToListAsync();
        //     Company companyRequiredSmsTwoFactor = _dbContext.Companies
        //         .FirstOrDefault(i =>
        //             companiesUserAssignedTo.Contains(i.CompanyID) && i.EnableTwoFactorSms && i.TwilioAccountVerified);
        //
        //     // Mfa is considered correctly set up if flag is turned on and phone number is valid
        //     bool isUserMfaSetup = user.TwoFactorPhoneNumberVerified && !string.IsNullOrEmpty(user.TwoFactorPhoneNumber);
        //
        //     // Set up is required if company enforces policy and user has not set it up
        //     return companyRequiredSmsTwoFactor != null && isUserMfaSetup == false;
        // }
    }
}