using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.OB;
using DarwiNet2._0.Interfaces.Services;
using DarwiNet2._0.Models.Onboard;
using DarwiNet2._0.ViewModels.Onboard;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Services
{
    public class OnboardService : IOnboardService
    {
        private readonly IOBProcessTaskFieldProvider _obProcessTaskFieldProvider;
        private readonly IOBProcessRecordDetailProvider _obProcessRecordDetailProvider;
        private readonly IOBProcessMonitorProvider _oBProcessMonitorProvider;

        public OnboardService(IOBProcessTaskFieldProvider obProcessTaskFieldProvider, IOBProcessRecordDetailProvider obProcessRecordDetailProvider, IOBProcessMonitorProvider obProcessMonitorProvider)
        {
            _obProcessTaskFieldProvider = obProcessTaskFieldProvider;
            _obProcessRecordDetailProvider = obProcessRecordDetailProvider;
            _oBProcessMonitorProvider = obProcessMonitorProvider;
        }

        public bool IsOnboardFormDataValid(int companyId, string employeeId, string email, DateTime birthDate, string ssn)
        {
            bool isValid = true;
            DateTime recordBirthDate;

            try
            {
                List<OBProcessTaskField> theSignOnFields = _obProcessTaskFieldProvider.GetOBProcessTaskFields(companyId, employeeId, OBTaskType.SignOn);
                List<OBProcessRecordDetail> thePersonalTask = _obProcessRecordDetailProvider.GetTaskDetails(companyId, employeeId, 10);

                if (FieldRequired(theSignOnFields.Where(t => t.FName == "Email").First().FRequired))
                {
                    var tmpEmail = thePersonalTask.Where(tp => tp.FldName == "EMail").Select(tp => tp.FldValue.ToUpper()).FirstOrDefault() ?? string.Empty;
                    isValid = (isValid) && (email.ToUpper() == tmpEmail.ToUpper());
                }
                if (FieldRequired(theSignOnFields.Where(t => t.FName == "BirthDate").First().FRequired))
                {
                    string dbBirthDate = thePersonalTask.Where(tp => tp.FldName == "BirthDate").Select(tp => tp.FldValue).FirstOrDefault();
                    if (!DateTime.TryParse(dbBirthDate, out recordBirthDate)) recordBirthDate = DateTime.MinValue;
                    isValid = (isValid) && (birthDate.Date == recordBirthDate.Date);
                }
                if (FieldRequired(theSignOnFields.Where(t => t.FName == "SocSecNumber").First().FRequired))
                {
                    isValid = (isValid) && (ssn == thePersonalTask.Where(tp => tp.FldName == "SocialSecNumber").Select(tp => tp.FldValue).FirstOrDefault());
                }

                // COH-567
                //if (FieldRequired(theSignOnFields.Where(t => t.FName == "FirstName").First().FRequired))
                //{
                //    var fName = thePersonalTask.Where(tp => tp.FldName == "Firstname").Select(tp => tp.FldValue.ToUpper()).FirstOrDefault() ?? string.Empty;
                //    valid = (valid) && (FirstName.ToUpper() == fName.ToUpper());
                //}
                //if (FieldRequired(theSignOnFields.Where(t => t.FName == "LastName").First().FRequired))
                //{
                //    var lName = thePersonalTask.Where(tp => tp.FldName == "Lastname").Select(tp => tp.FldValue.ToUpper()).FirstOrDefault() ?? string.Empty;
                //    valid = (valid) && (LastName.ToUpper() == lName.ToUpper());
                //}
            }
            catch (Exception ex)
            {
                // TODO: log exception
                isValid = false;
            }

            return isValid;
        }

        public OnboardVerifySetupDTO GetOnboardFormFieldsSetup(int companyId, string employeeId)
        {
            OnboardVerifySetupDTO setup = new OnboardVerifySetupDTO();

            try
            {
                OBProcessMonitor thisMonitor = _oBProcessMonitorProvider.GetOBEmployeeProcessMonitor(companyId, employeeId);

                if (thisMonitor != null)
                {
                    List<OBProcessTaskField> theSignOnFields = _obProcessTaskFieldProvider.GetOBProcessTaskFields(companyId, employeeId, OBTaskType.SignOn);

                    // COH-567
                    //ViewBag.FNameUsed = FieldUsed(theSignOnFields.First(t => t.FName == "FirstName").EEAccess);
                    //ViewBag.FNameRequired = FieldRequired(theSignOnFields.First(t => t.FName == "FirstName").FRequired);

                    //ViewBag.LNameUsed = FieldUsed(theSignOnFields.First(t => t.FName == "LastName").EEAccess);
                    //ViewBag.LNameRequired = FieldRequired(theSignOnFields.First(t => t.FName == "LastName").FRequired);

                    setup.EmailUsed = FieldUsed(theSignOnFields.First(t => t.FName == "Email").EEAccess);
                    setup.EmailRequired = FieldRequired(theSignOnFields.First(t => t.FName == "Email").FRequired);

                    setup.BirthDateUsed = FieldUsed(theSignOnFields.First(t => t.FName == "BirthDate").EEAccess);
                    setup.BirthDateRequired = FieldRequired(theSignOnFields.First(t => t.FName == "BirthDate").FRequired);

                    setup.SocSecNumberUsed = FieldUsed(theSignOnFields.First(t => t.FName == "SocSecNumber").EEAccess);
                    setup.SocSecNumberRequired = FieldRequired(theSignOnFields.First(t => t.FName == "SocSecNumber").FRequired);
                }
            }
            catch (Exception ex)
            {
                // TODO: log exception
            }

            return setup;
        }

        private bool FieldRequired(short? RequirementSetting)
        {
            if (RequirementSetting != OBFieldRequirement.Neither && RequirementSetting != OBFieldRequirement.ClientOnly)
                return true;
            else
                return false;
        }

        private bool FieldUsed(short? AccessSetting)
        {
            if (AccessSetting != OBFieldAccess.Hidden && AccessSetting != OBFieldAccess.ReadOnly)
                return true;
            else
                return false;
        }
    }
}