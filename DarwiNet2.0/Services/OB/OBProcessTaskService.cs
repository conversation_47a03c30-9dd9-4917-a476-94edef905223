using DarwiNet2._0.Interfaces.Providers.OB;
using DarwiNet2._0.Interfaces.Services.OB;
using DarwiNet2._0.Providers.OB;
using System;
using System.Collections.Generic;
using System.Linq;
using static DarwiNet2._0.Controllers.OBProcessController;

namespace DarwiNet2._0.Services.OB
{
    public class OBProcessTaskService : IOBProcessTaskService
    {
        #region Fields

        private readonly IOBProcessTaskProvider _obProcessTaskProvider;

        #endregion

        #region Constructors 

        public OBProcessTaskService()
        {
            _obProcessTaskProvider = new OBProcessTaskProvider();
        }

        public OBProcessTaskService(IOBProcessTaskProvider obProcessTaskProvider)
        {
            _obProcessTaskProvider = obProcessTaskProvider ?? throw new ArgumentNullException(nameof(obProcessTaskProvider));
        }

        #endregion

        public int GetTaskID(int companyId, string obEmployeeId, short taskType)
        {
            return _obProcessTaskProvider.GetTaskID(companyId, obEmployeeId, taskType);
        }

        public short GetTaskType(int companyId, string obEmployeeId, int taskId)
        {
            return _obProcessTaskProvider.GetTaskType(companyId, obEmployeeId, taskId);
        }

        public List<ProcessTasksBar> GetProcessTasks(int companyId, string obEmployeeId, string dnetLevel)
        {
            return _obProcessTaskProvider.GetProcessTasks(companyId, obEmployeeId, dnetLevel);
        }
    }
}