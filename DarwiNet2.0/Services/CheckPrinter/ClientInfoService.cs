using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DarwiNet2._0.Interfaces.Providers.CheckPrinter;
using DarwiNet2._0.Interfaces.Services.CheckPrinter;
using DarwiNet2._0.DTOs.CheckPrinter;
using DarwiNet2._0.ViewModels.CheckPrinter;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Services.CheckPrinter
{
    public class ClientInfoService : IClientInfoService
    {
        #region Fields
        private readonly IClientInfoProvider _clientProvider;

        #endregion

        #region Constructors

        /// <summary>
        ///     Default constructor.
        /// </summary>
        /// <history>
        ///     
        /// </history>
        public ClientInfoService(IClientInfoProvider clientProvider)
        {
            _clientProvider = clientProvider ?? throw new ArgumentNullException(nameof(clientProvider));
        }

        #endregion

        #region Public Functions
        public Bank GetBankRecord(int companyId, string bankId)
        {
            return _clientProvider.GetBankRecord(companyId, bankId);
        }

        public Checkbook GetCheckbookRecord(int companyId, string cbId)
        {
            return _clientProvider.GetCheckbookRecord(companyId, cbId);
        }

        public Company GetCompanyRecord(int companyId)
        {
            return _clientProvider.GetCompanyRecord(companyId);
        }

        public Employee GetEmployeeRecord(int companyid, string employeeid)
        {
            return _clientProvider.GetEmployeeRecord(companyid, employeeid);
        }

        public Client GetClientRecord(int companyId, string clientId)
        {
            return _clientProvider.GetClientRecord(companyId, clientId);
        }

        public CheckNotesModel GetClientNotes(int companyId, string clientId, DateTime checkDate)
        {
            return _clientProvider.GetClientNotes(companyId, clientId, checkDate);
        }

        public CheckNotesModel GetCompanyNotes(int companyId, DateTime checkDate)
        {
            return _clientProvider.GetCompanyNotes(companyId, checkDate);
        }
        public CompanyACHSetupModel CompanyACHSetup(int companyId)
        {
            return _clientProvider.CompanyACHSetup(companyId);
        }

        public ClientDivision DivisionInfo(int companyid, string clientid, string department)
        {
            string divid = _clientProvider.GetDivisionByDepartment(companyid, clientid, department);
            return _clientProvider.DivisionInfo(companyid, clientid, divid);
        }

        public ClientAddress ClientAddress(int companyid, string clientid, string code)
        {
            return _clientProvider.ClientAddress(companyid, clientid, code);
        }

        public List<string> GetAvailableEmployeeIDs(int companyid, string employeeid, bool summarize)
        {
            return _clientProvider.GetAvailableEmployeeIDs(companyid, employeeid, summarize);
        }
        #endregion
    }
}