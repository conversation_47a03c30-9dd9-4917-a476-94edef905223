using DarwiNet2._0.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Web;

namespace DarwiNet2._0.Services
{
    public class PayrollUserService
    {
        public int GetMaximumCount()
        {
            var db = GetDB.GetDBConn();
            int count = 0;
            var projSetup = db.ProjectSetups.FirstOrDefault();
            string billingApiUrl = ConfigurationManager.AppSettings["BillingAPIUrl"];
            string url = billingApiUrl + "/API/GetPayrollUserCount";
#if DEBUG
            url = "https://dev1.darwinet.com/billingapitest" + "/API/GetPayrollUserCount";
#endif
            var values = new Dictionary<string, string>
            {
                {"token", "CdBUIVx6WbLRBjsJDscZtv41KXvxuWhvOs5lWKhFJRJhBQyPxRpgJeVZ7wJC6AKryaLo51d2upE9GHGJr8WntQMFPajasQ4F2JjbKrdD1S"},
                {"key", projSetup.DnetRegistrationKey},
            };

            try
            {
                using (var client = new HttpClient())
                {
                    var content = new FormUrlEncodedContent(values);

                    var response = client.PostAsync(url, content).GetAwaiter().GetResult();
                    //var response = client.PostAsJsonAsync("api/person", p).Result;
                    if (response.IsSuccessStatusCode)
                    {
                        var json = response.Content.ReadAsStringAsync();
                        if (!string.IsNullOrEmpty(json.Result))
                        {
                            var result = json.Result;
                            var keyInfo = JsonConvert.DeserializeObject<PayrollUserCounts>(result);
                            count = keyInfo.PayrollUserCount;
                        }
                        
                    }
                }
            }
            catch (Exception e)
            {
                //SlackMessage(e.ToString(), type);
            }

            return count;
        }
    }
}