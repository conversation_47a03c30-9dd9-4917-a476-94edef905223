using System;
using System.IO;
using System.Linq;
using DarwiNet2._0.Controllers;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DataDrivenViewEngine.Models.Core;
using ProjectSetup = DarwiNet2._0.DNetSynch.ProjectSetup;
using NotificationType = DarwiNet2._0.Core.NotificationType;

namespace DarwiNet2._0.Services.Email
{
    public class NonSessionSmartTags
    {
        private static DnetEntities db = GetDBConn();

        private static int PTORequestID;
        private static int ChangeRequestID;
        private static int UserRecordID;
        private static PTORequest thisPTORequest;
        private static ChangeRequestsHDR thisRequest;
        private static short thisNotificationType;
        private static string invCheckDate;
        private static string invInvoiceDate;
        private static string invComment;
        private static string invUserId;
        private static string invUserName;
        private static string invPreviewStatus;
        private static string divisionID;
        private static int invoiceNumber;
        private static int oeYear;
        private static EmployeeCheckHistory thisCheck;
        private static int PaymentAdjustmentNumber;
        private static EmployeeEnrollmentStatu thisMonitor;

        public string Subject { get; set; }
        public string Body { get; set; }
        public int Type { get; set; }

        private static string GetSetupDNetURL()
        {
            try
            {
                return db.ProjectSetups.Select(s => s.DnetAddress).First();
            }
            catch
            { return string.Empty; }
        }

        public static void Initialize(string cdate, string idate, string div, int inv, short type)
        {
            thisNotificationType = type;
            //dbContext = new DnetEntities();
            var tmpIDate = Convert.ToDateTime(idate);
            var tmpCDate = Convert.ToDateTime(cdate);
            invInvoiceDate = tmpIDate.ToString("MM/dd/yyyy");
            invCheckDate = tmpCDate.ToString("MM/dd/yyyy");
            invoiceNumber = inv;
            divisionID = div;
        }

        public static void Initialize(short type)
        {
            thisNotificationType = type;
        }

        private static string SmartTagValue(string tag, int co, string cl, out string CloseTag)
        {
            string result = string.Empty;
            string theUrl = GetSetupDNetURL();
            if (string.IsNullOrEmpty(theUrl)) theUrl = GetSetupDNetURL();

            Uri link;
            string EEID = string.Empty;
            string clientId = cl;
            //if (GlobalVariables.DNETLevel == DNetAccessLevel.Employee) EEID = GlobalVariables.EmployeeID;
            if (thisNotificationType == NotificationType.CCPTOResponse ||
                thisNotificationType == NotificationType.EEPTORequest)
            {
                thisPTORequest = db.PTORequests.FirstOrDefault(ptr => ptr.id == PTORequestID);
                if (thisPTORequest != null) EEID = thisPTORequest.EmployeeID;
            }
            if (thisNotificationType == NotificationType.ChangeRequests)
            {
                thisRequest = db.ChangeRequestsHDRs.FirstOrDefault(crh => crh.RequestID == ChangeRequestID);
                if (thisRequest != null) EEID = (thisRequest.Key1.Contains("EmployeeID")) ? thisRequest.Key1.Replace("EmployeeID=", "") : string.Empty; //get EmployeeID from request;
                if (thisRequest != null) clientId = thisRequest.ClientID;
            }
            if (thisNotificationType == NotificationType.EEPayroll)
            {
                thisCheck = db.EmployeeCheckHistories.FirstOrDefault(ech => ech.CompanyID == co && ech.PaymentAdjustmentNumber == PaymentAdjustmentNumber);
                if (thisCheck != null) EEID = thisCheck.EmployeeID;
                if (thisCheck != null) clientId = cl;
            }
            if (thisNotificationType == NotificationType.DarwinEmployeeEmail)
            {
                var sendingEmp = db.Employees.FirstOrDefault(s => s.SendingImportWelcome);
                if (sendingEmp != null)
                {
                    EEID = sendingEmp.EmployeeID;
                }
            }
            if (thisNotificationType == NotificationType.InvoicePreviewApproval)
            {
                var thisInvoice = db.Invoices.FirstOrDefault(i => i.DarwinInvoiceNumber == invoiceNumber && i.ClientID == cl && i.CompanyID == co);

                invComment = thisInvoice.PreviewComments;
                invUserName = thisInvoice.PreviewUser;
                invUserId = thisInvoice.UserID;
                invPreviewStatus = thisInvoice.PreviewStatus == 3 ? "Approved" : "Denied";
            }

            var theEmp = new Employee();
            if (EEID != string.Empty)
            {
                theEmp = db.Employees.FirstOrDefault(emp => emp.CompanyID == co && emp.EmployeeID == EEID);
            }
            CloseTag = string.Empty;
            switch (tag)
            {
                case SmartTags.EELoginLink:
                    if (string.IsNullOrEmpty(theUrl))
                    {
                        link = new Uri(theUrl);
                        result = "<a href='" + link + "'>";
                        CloseTag = SmartTags.CloseEELoginLink;
                    }
                    else
                    {
                        result = "<a href='" + theUrl + "'>" + theUrl;
                        CloseTag = SmartTags.CloseEELoginLink;
                    }
                    break;
                case SmartTags.URL:
                    if (string.IsNullOrEmpty(theUrl))
                    {
                        link = new Uri(theUrl);
                        result = "<a href='" + link + "'>";
                        CloseTag = SmartTags.CloseURL;
                    }
                    else
                    {
                        result = "<a href='" + theUrl + "'>" + theUrl;
                        CloseTag = SmartTags.CloseURL;
                    }
                    break;
                case SmartTags.EmployeeID:
                    result = (theEmp != null) ? theEmp.EmployeeID : string.Empty;
                    break;
                case SmartTags.EEFirstName:
                    result = (theEmp != null) ? theEmp.FirstName : string.Empty;
                    break;
                case SmartTags.EELastName:
                    result = (theEmp != null) ? theEmp.LastName : string.Empty;
                    break;
                case SmartTags.EEName:
                    result = (theEmp != null) ? theEmp.FirstName + ' ' + theEmp.LastName : string.Empty;
                    break;
                case SmartTags.EEMail:
                    if (!string.IsNullOrEmpty(theEmp.Email))
                    {
                        result = (theEmp != null) ? theEmp.Email : string.Empty;
                    }
                    if (!string.IsNullOrEmpty(theEmp.DefaultEmailAddress))
                    {
                        result = (theEmp != null) ? theEmp.DefaultEmailAddress : string.Empty;
                    }
                    if (!string.IsNullOrEmpty(theEmp.DarwinetEmail))
                    {
                        result = (theEmp != null) ? theEmp.DarwinetEmail : string.Empty;
                    }
                    break;
                case SmartTags.CCID:
                    result = clientId;
                    break;
                case SmartTags.CCUserID:
                    //if (GlobalVariables.DNETLevel == DNetAccessLevel.Client) result = GlobalVariables.DNETOwnerID;
                    break;
                case SmartTags.CCName:
                    result = db.Clients.FirstOrDefault(c => c.CompanyID == co && c.ClientID == clientId).ClientName;
                    break;
                case SmartTags.CCPhone:
                    Client theClient = db.Clients.FirstOrDefault(c => c.CompanyID == co && c.ClientID == clientId);
                    ClientAddress thisAddress = db.ClientAddresses.FirstOrDefault(ca => ca.CompanyID == theClient.CompanyID && ca.ClientID == theClient.ClientID && ca.AddressCode == theClient.AddressCode);
                    if (thisAddress != null) result = FieldTranslation.FormatPhone(thisAddress.Phone1 ?? string.Empty);
                    break;
                case SmartTags.CCMail:
                    //NEEDS REVIEWED!!!!
                    result = string.Empty;
                    break;
                case SmartTags.PEOName:
                    var company = db.Companies.FirstOrDefault(p => p.CompanyID == co);
                    if (company != null)
                        result = company.CompanyName ?? string.Empty;
                    break;
                case SmartTags.PEOPhone:
                    var company1 = db.Companies.FirstOrDefault(p => p.CompanyID == co);
                    if (company1 != null)
                        result = FieldTranslation.FormatPhone(company1.Phone1 ?? string.Empty);
                    //result = string.Empty;
                    break;
                case SmartTags.PEOMail:
                    var company2 = db.Companies.FirstOrDefault(p => p.CompanyID == co);
                    if (company2 != null)
                        result = company2.Email ?? string.Empty;
                    break;
                case SmartTags.CompanyID:
                    result = co.ToString() ?? string.Empty;
                    break;
                case SmartTags.CompanyName:
                    var firstOrDefault = db.Companies.FirstOrDefault(p => p.CompanyID == co);
                    if (firstOrDefault != null)
                        result = firstOrDefault.CompanyName ?? string.Empty;
                    break;
                case SmartTags.PTOPlanID:
                    if (thisPTORequest != null)
                        result = FieldTranslation.GetEnumDescription(typeof(enPTOTypes), thisPTORequest.PTOType);
                    break;
                case SmartTags.PTOPlanDescr:
                    if (thisPTORequest != null)
                    {
                        var clientPlan = db.ClientPTOTypes.FirstOrDefault(cpt => cpt.CompanyID == thisPTORequest.CompanyID && cpt.ClientID == clientId && cpt.PTOType == thisPTORequest.PTOType);
                        result = (clientPlan != null) ? clientPlan.Description : string.Empty;
                    }
                    break;
                case SmartTags.PTOReqComment:
                    if (thisPTORequest != null) result = thisPTORequest.Comments;
                    break;
                case SmartTags.PTOReqEndDate:
                    if (thisPTORequest != null) result = thisPTORequest.EndDate.ToString("MM/dd/yyyy");
                    break;
                case SmartTags.PTOReqHours:
                    if (thisPTORequest != null) result = thisPTORequest.TotalRequestedTime.ToString();
                    break;
                case SmartTags.PTOTimeStart:
                    if (thisPTORequest != null)
                    {
                        result = !string.IsNullOrEmpty(thisPTORequest.StartTime) ? thisPTORequest.StartTime : "N/A";

                    }
                    break;
                case SmartTags.PTOTimeEnd:
                    if (thisPTORequest != null)
                    {
                        result = !string.IsNullOrEmpty(thisPTORequest.EndTime) ? thisPTORequest.EndTime : "N/A";
                    };
                    break;
                case SmartTags.PTOReqStartDate:
                    if (thisPTORequest != null) result = thisPTORequest.StartDate.ToString("MM/dd/yyyy");
                    break;
                case SmartTags.PTOReqStatus:
                    if (thisPTORequest != null) result = FieldTranslation.GetEnumDescription(typeof(enTimeRequestStatus), thisPTORequest.Status);
                    break;
                case SmartTags.ChangeReqCat:
                    if (thisRequest != null) result = FieldTranslation.GetEnumDescription(typeof(enChangeRequestAssignmentTypes), (int)thisRequest.AssignmentType);
                    break;
                case SmartTags.ChangeReqID:
                    if (thisRequest != null) result = thisRequest.RequestID.ToString();
                    break;
                case SmartTags.UserID:
                    /*result = GlobalVariables.DNETOwnerID;
                    if (UserRecordID != 0)
                    {
                        var user = db.Users.FirstOrDefault(u => u.Id == UserRecordID);
                        if (user != null) result = user.UserID;
                    }*/
                    break;
                case SmartTags.InvoiceChkDate:
                    result = invCheckDate;
                    break;
                case SmartTags.InvoiceDate:
                    result = invInvoiceDate;
                    break;
                case SmartTags.InvoiceComment:
                    result = invComment;
                    break;
                case SmartTags.InvoiceUserID:
                    result = invUserId;
                    break;
                case SmartTags.InvoiceUserName:
                    result = invUserName;
                    break;
                case SmartTags.InvoiceAprrovalTime:
                    result = DateTime.Now.ToString("MM-dd-yyyy h:mm tt");
                    break;
                case SmartTags.InvoicePreviewStatus:
                    result = invPreviewStatus;
                    break;
                case SmartTags.InvoiceNbr:
                    result = invoiceNumber.ToString();
                    break;
                case SmartTags.PayPeriodStart:
                    result = tag;
                    if (thisCheck != null)
                    {
                        if (string.IsNullOrEmpty(thisCheck.PayrollNumber))
                        {
                            var eeWorkCheck = db.EmployeeInvoiceCheckHistories.FirstOrDefault(eich => eich.CompanyID == thisCheck.CompanyID && eich.PaymentAdjustmentNumber == thisCheck.PaymentAdjustmentNumber);
                            if (eeWorkCheck != null)
                            {
                                result = eeWorkCheck.StartPayPeriod.Value.ToString("MM/dd/yyyy");
                            }
                        }
                        else
                        {
                            result = thisCheck.PayPeriodBeginDate.Value.ToString("MM/dd/yyyy");
                        }
                    }
                    break;
                case SmartTags.PayPeriodEnd:
                    result = tag;
                    if (thisCheck != null)
                    {
                        if (string.IsNullOrEmpty(thisCheck.PayrollNumber))
                        {
                            var eeWorkCheck = db.EmployeeInvoiceCheckHistories.FirstOrDefault(eich => eich.CompanyID == thisCheck.CompanyID && eich.PaymentAdjustmentNumber == thisCheck.PaymentAdjustmentNumber);
                            if (eeWorkCheck != null)
                            {
                                result = eeWorkCheck.EndPayPeriod.Value.ToString("MM/dd/yyyy");
                            }
                        }
                        else
                        {
                            result = thisCheck.PayPeriodEndDate.Value.ToString("MM/dd/yyyy");
                        }
                    }
                    break;
                case SmartTags.CheckDate:
                    result = (thisCheck != null) ? thisCheck.CheckDate.Value.ToString("MM/dd/yyyy") : tag;
                    break;
                case SmartTags.OEStartDate:
                    result = (thisMonitor != null && thisMonitor.StartDate != null) ? thisMonitor.StartDate.Value.ToShortDateString() : string.Empty;
                    break;
                case SmartTags.OEEndDate:
                    result = (thisMonitor != null && thisMonitor.EndDate != null) ? thisMonitor.EndDate.Value.ToShortDateString() : string.Empty;
                    break;
            }
            return result;
        }

        private static string FillSmartTag(string text, string tag, int co, string cl)
        {
            if (text.Contains(tag))
            {
                string closeTag = string.Empty;
                string tagValue = SmartTagValue(tag, co, cl, out closeTag);
                string result = text;
                if (!string.IsNullOrEmpty(closeTag))
                {
                    if (result.Contains(closeTag))
                        result = result.Replace(closeTag, "</a>");
                    else
                    {
                        tagValue += "</a>";
                    }
                }
                return result.Replace(tag, tagValue);
            }
            return text;
        }

        public static string FillSmartTags(string text, int co, string cl)
        {
            string result = FillSmartTag(text, SmartTags.URL, co, cl);
            result = FillSmartTag(result, SmartTags.EELoginLink, co, cl);
            result = FillSmartTag(result, SmartTags.EmployeeID, co, cl);
            result = FillSmartTag(result, SmartTags.EEFirstName, co, cl);
            result = FillSmartTag(result, SmartTags.EELastName, co, cl);
            result = FillSmartTag(result, SmartTags.EEName, co, cl);
            result = FillSmartTag(result, SmartTags.EEMail, co, cl);
            result = FillSmartTag(result, SmartTags.CCUserID, co, cl);
            result = FillSmartTag(result, SmartTags.CCName, co, cl);
            result = FillSmartTag(result, SmartTags.CCPhone, co, cl);
            result = FillSmartTag(result, SmartTags.CCMail, co, cl);
            result = FillSmartTag(result, SmartTags.PEOName, co, cl);
            result = FillSmartTag(result, SmartTags.PEOPhone, co, cl);
            result = FillSmartTag(result, SmartTags.PEOMail, co, cl);
            result = FillSmartTag(result, SmartTags.CompanyID, co, cl);
            result = FillSmartTag(result, SmartTags.PTOPlanDescr, co, cl);
            result = FillSmartTag(result, SmartTags.PTOPlanID, co, cl);
            result = FillSmartTag(result, SmartTags.PTOReqComment, co, cl);
            result = FillSmartTag(result, SmartTags.PTOReqEndDate, co, cl);
            result = FillSmartTag(result, SmartTags.PTOReqStartDate, co, cl);
            result = FillSmartTag(result, SmartTags.PTOReqStatus, co, cl);
            result = FillSmartTag(result, SmartTags.PTOReqHours, co, cl);
            result = FillSmartTag(result, SmartTags.PTOTimeStart, co, cl);
            result = FillSmartTag(result, SmartTags.PTOTimeEnd, co, cl);
            result = FillSmartTag(result, SmartTags.UserID, co, cl);
            result = FillSmartTag(result, SmartTags.CCID, co, cl);
            result = FillSmartTag(result, SmartTags.ChangeReqCat, co, cl);
            result = FillSmartTag(result, SmartTags.ChangeReqID, co, cl);
            result = FillSmartTag(result, SmartTags.InvoiceChkDate, co, cl);
            result = FillSmartTag(result, SmartTags.InvoiceDate, co, cl);
            result = FillSmartTag(result, SmartTags.InvoiceNbr, co, cl);
            result = FillSmartTag(result, SmartTags.InvoiceAprrovalTime, co, cl);
            result = FillSmartTag(result, SmartTags.InvoiceComment, co, cl);
            result = FillSmartTag(result, SmartTags.InvoiceUserID, co, cl);
            result = FillSmartTag(result, SmartTags.InvoiceUserName, co, cl);
            result = FillSmartTag(result, SmartTags.InvoicePreviewStatus, co, cl);
            result = FillSmartTag(result, SmartTags.PayPeriodStart, co, cl);
            result = FillSmartTag(result, SmartTags.PayPeriodEnd, co, cl);
            result = FillSmartTag(result, SmartTags.CheckDate, co, cl);
            result = FillSmartTag(result, SmartTags.OEStartDate, co, cl);
            result = FillSmartTag(result, SmartTags.OEEndDate, co, cl);
            return FillSmartTag(result, SmartTags.CompanyName, co, cl);
        }

        private static DnetEntities GetDBConn()
        {
            string dnetLocation = AppDomain.CurrentDomain.BaseDirectory;
            string file = Path.Combine(dnetLocation, "globalcon.cfg");
            bool exists = File.Exists(file);
            if (!exists) return new DnetEntities();
            string connString;
            connString = ProjectSetup.CreateConnectionString(file);
            DnetEntities db = new DnetEntities();

            return db;
        }
    }
}