using DarwiNet2._0.Core;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using static DarwiNet2._0.Core.Constants.PayrollProfileApprovalTypes;
using Thinkware.Pay360.Messaging;
using Thinkware.Cohesion.Payroll;
using DarwiNet2._0.Controllers.D2;
using Thinkware.Pay360.Payroll;
using DarwiNet2._0.Providers.D2;

namespace DarwiNet2._0.Services.D2
{
    public class ClientJobCostAssignmentsService : IClientJobCostAssignmentsService
    {
        #region Fields

        private readonly IClientJobCostAssignmentsProvider _clientJobCostAssignmentsProvider;
        private readonly ICompanyService _companyService;
        private readonly IClientService _clientService;

        #endregion

        #region Constructors

        public ClientJobCostAssignmentsService(IClientJobCostAssignmentsProvider clientJobCostAssignmentsProvider, ICompanyService companyService, IClientService clientService)
        {
            _clientJobCostAssignmentsProvider = clientJobCostAssignmentsProvider ?? throw new ArgumentNullException(nameof(clientJobCostAssignmentsProvider));
            _companyService = companyService ?? throw new ArgumentNullException(nameof(companyService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));

        }

        #endregion

        #region IClientJobCostAssignmentsService Implementation

        public List<string> GetJobIDsByUserCompany()
        {
            return _clientJobCostAssignmentsProvider.GetJobIDsByUserCompany();
        }

        public TableQueryInfo<ClientJobCostAssignmentTableRowDTO> GetClientJobCostAssignmentsTableRows(TableFilter filters, string userId)
        {
            return _clientJobCostAssignmentsProvider.GetClientJobCostAssignmentsTableRows(filters, userId);
        }

        public void ToggleCertify(int companyId, string clientId, string jobId)
        {
            _clientJobCostAssignmentsProvider.ToggleCertify(companyId, clientId, jobId);
        }

        public void CreateClientJobCostAssignment(ClientJobCostAssignmentFormDTO job)
        {
            _clientJobCostAssignmentsProvider.CreateClientJobCostAssignment(job);
        }

        public void UpdateClientJobCostAssignment(ClientJobCostAssignmentFormDTO job, string clientId, string jobId)
        {
            _clientJobCostAssignmentsProvider.UpdateClientJobCostAssignment(job, clientId, jobId);
        }

        public ClientJobCostAssignmentFormDTO GetClientJobCostAssignment(int companyId, string clientId, string jobCostingName)
        {
            var clientJobCostAssignment = _clientJobCostAssignmentsProvider.GetClientJobCostAssignment(companyId, clientId, jobCostingName);

            var jobLevelCount = 1;

            for (var i = 2; i < 7; i++)
            {
                var property = clientJobCostAssignment.GetType().GetProperty("UseArray" + i);
                var value = property.GetValue(clientJobCostAssignment);
                if ((bool)value == true)
                {
                    jobLevelCount++;
                }
                else { break; }
            }


            return new ClientJobCostAssignmentFormDTO
            {
                CompanyID = clientJobCostAssignment.CompanyID,
                CompanyName = _companyService.FindCompanyNameById(companyId),
                ClientName = _clientService.GetClientNameById(companyId, clientId),
                JobID = clientJobCostAssignment.JobCostingName,
                Description = clientJobCostAssignment.LongDescription,
                Active = !clientJobCostAssignment.Inactive,
                Certified = clientJobCostAssignment.ThinkwareGenerateCertified,
                Phone = clientJobCostAssignment.Phone,
                ProjectManager = clientJobCostAssignment.ProjectManager,
                EmployeeOfClient = clientJobCostAssignment.ClientID,
                Email = clientJobCostAssignment.Email,
                EstimatedStart = clientJobCostAssignment.EstimatedStartDate,
                EstimatedHours = clientJobCostAssignment.EstimatedHours,
                EstimatedEnd = clientJobCostAssignment.EstimatedCompletionDate,
                EstimatedCost = clientJobCostAssignment.Cost,
                SummarizeFlag = clientJobCostAssignment.NoEstimateSum,
                JobLevel2 = clientJobCostAssignment.JobLevel2,
                JobLevel2UseArray = clientJobCostAssignment.UseArray2,
                JobLevel3 = clientJobCostAssignment.JobLevel3,
                JobLevel3UseArray = clientJobCostAssignment.UseArray3,
                JobLevel4 = clientJobCostAssignment.JobLevel4,
                JobLevel4UseArray = clientJobCostAssignment.UseArray4,
                JobLevel5 = clientJobCostAssignment.JobLevel5,
                JobLevel5UseArray = clientJobCostAssignment.UseArray5,
                JobLevel6 = clientJobCostAssignment.JobLevel6,
                JobLevel6UseArray = clientJobCostAssignment.UseArray6,
                HourlyPayCode = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.DefaultHourlyCode,
                        Display = clientJobCostAssignment.DefaultHourlyCode
                    }
                },
                OvertimePayCode = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.DefaultOTCode,
                        Display = clientJobCostAssignment.DefaultOTCode
                    }
                },
                SalaryPayCode = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.DefaultSalaryCode,
                        Display = clientJobCostAssignment.DefaultSalaryCode
                    }
                },
                SalaryOTPayCode = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.DefaultSalaryOTCode,
                        Display = clientJobCostAssignment.DefaultSalaryOTCode
                    }
                },
                Department = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.Department,
                        Display = clientJobCostAssignment.Department
                    }
                },
                WorkersComp = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.WorkersComp,
                        Display = clientJobCostAssignment.WorkersComp
                    }
                },
                StateTax = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.StateCode,
                        Display = clientJobCostAssignment.StateCode
                    }
                },
                LocalTax = new List<MultiselectOptionsDTO>()
                {
                    new MultiselectOptionsDTO
                    {
                        Code = clientJobCostAssignment.LocalTax,
                        Display = clientJobCostAssignment.LocalTax
                    }
                },
                JobLevelCount = jobLevelCount
            };
        }

        public JobCostAssignmentDefaultCodes GetClientJobCostAssignmentDefaultCodes(int companyId, string clientId, string jobCostingName)
        {
            return _clientJobCostAssignmentsProvider.GetClientJobCostAssignmentDefaultCodes(companyId, clientId, jobCostingName);
        }

        public List<EmployeeJobCostAssignment> GetEmployeeJobCostAssignments(string clientId, string jobCostingName)
        {
            return _clientJobCostAssignmentsProvider.GetEmployeeJobCostAssignments(clientId, jobCostingName);
        }

        public ClientJobCostAssignmentDetailLevelDTO GetClientJobCostAssignmentDetailLevels(int companyId, string clientId, string jobCostingName)
        {
            return _clientJobCostAssignmentsProvider.GetClientJobCostAssignmentDetailLevels(companyId, clientId, jobCostingName);
        }

        public List<ClientJobCostAssignmentDetail> GetClientJobCostAssignmentDetails(int companyId, string clientId, string jobCostingName)
        {
            return _clientJobCostAssignmentsProvider.GetClientJobCostAssignmentDetails(companyId, clientId, jobCostingName);
        }

        public List<MultiselectOptionsDTO> GetJobLevel2Options(int companyId, string clientId, string jobCostingName)
        {
            return _clientJobCostAssignmentsProvider.GetJobLevel2Options(companyId, clientId, jobCostingName);
        }

        public List<MultiselectOptionsDTO> GetJobLevel3(int companyId, string clientId, string jobName, List<string> level2)
        {
            return _clientJobCostAssignmentsProvider.GetJobLevel3(companyId, clientId, jobName, level2);
        }
        
        public List<MultiselectOptionsDTO> GetJobLevel4(int companyId, string clientId, string jobName, List<string> level2, List<string> level3)
        {
            return _clientJobCostAssignmentsProvider.GetJobLevel4(companyId, clientId, jobName, level2, level3);
        }

        public List<MultiselectOptionsDTO> GetJobLevel5(int companyId, string clientId, string jobName, List<string> level2, List<string> level3, List<string> level4)
        {
            return _clientJobCostAssignmentsProvider.GetJobLevel5(companyId, clientId, jobName, level2, level3, level4);
        }

        public List<MultiselectOptionsDTO> GetJobLevel6(int companyId, string clientId, string jobName, List<string> level2, List<string> level3, List<string> level4, List<string> level5)
        {
            return _clientJobCostAssignmentsProvider.GetJobLevel6(companyId, clientId, jobName, level2, level3, level4, level5);
        }

        public void RemoveClientJobCostAssignment(int companyId, string clientId, string jobCostingName)
        {
            _clientJobCostAssignmentsProvider.RemoveClientJobCostAssignment(companyId, clientId, jobCostingName);
        }

        public bool CheckJobIDName(int companyId, string clientId, string jobCostingName)
        {
            return _clientJobCostAssignmentsProvider.CheckJobIDName(companyId, clientId, jobCostingName);
        }

        #endregion
    }
}