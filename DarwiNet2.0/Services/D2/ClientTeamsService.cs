using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Providers;
using DarwiNet2._0.Providers.D2;
using DarwiNet2._0.ViewModels.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using DarwiNet2._0.DTOs;
using DataDrivenViewEngine.Models.Core;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Controllers;
using System.Linq.Expressions;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Services.D2
{
    public class ClientTeamsService : IClientTeamsService
    {
        #region Fields

        private readonly DnetEntities _dbContext;
        private readonly IClientTeamsProvider _teamsProvider;
        private readonly IClientTeamMembersProvider _teamMembersProvider;
        private readonly IClientTeamTypesProvider _teamTypesProvider;

        #endregion

        #region Constructors

        /*public ClientTeamsService(DnetEntities dbContext, IClientTeamsProvider teamsProvider, IClientTeamMembersProvider teamMembersProvider, IClientTeamTypesProvider teamTypesProvider)
        {
            _dbContext = dbContext;
            _teamsProvider = teamsProvider ?? throw new ArgumentNullException(nameof(teamsProvider));
            _teamMembersProvider = teamMembersProvider ?? throw new ArgumentNullException(nameof(teamMembersProvider));
            _teamTypesProvider = teamTypesProvider ?? throw new ArgumentNullException(nameof(teamMembersProvider));
        }*/
        public ClientTeamsService()
        {
            _teamsProvider = new ClientTeamsProvider();
            _teamMembersProvider = new ClientTeamMembersProvider();
            _teamTypesProvider = new ClientTeamTypesProvider();
        }
        #endregion

        #region Public Functions
        public bool CreateClientTeam(int companyId, string clientId, string id, string name, string type = "")
        {
            ClientTeam team = new ClientTeam()
            {
                CompanyID = companyId,
                ClientID = clientId,
                TeamID = id,
                TeamName = name,
                ClientTeamType = type
            };
            return _teamsProvider.CreateClientTeam(team);
        }

        public List<ClientTeamViewModel> ClientTeamsList(int companyId, string clientId)
        {
            return _teamsProvider.ClientTeamsList(companyId, clientId);
        }


        public void DeleteTeam(int companyId, string clientId, string id)
        {
            _teamsProvider.DeleteTeam(companyId, clientId, id);
        }

        public List<Code_Description> GetAvailableClientTeams(int companyId, string clientId)
        {
            return _teamsProvider.GetAvailableClientTeams(companyId, clientId);
        }

        public ClientTeam GetClientTeamById(int companyId, string clientId, string id)
        {
            return _teamsProvider.GetClientTeamById(companyId, clientId, id);
        }

        public IEnumerable<ClientTeam> GetClientTeams(int companyId, string clientId)
        {
            return _teamsProvider.GetClientTeams(companyId, clientId);
        }

        public void UpdateClientTeam(int companyId, string clientId, string id, string name, string type = "")
        {
            ClientTeam team = new ClientTeam()
            {
                CompanyID = companyId,
                ClientID = clientId,
                TeamID = id,
                TeamName = name,
                ClientTeamType = type
            };
            _teamsProvider.UpdateClientTeam(team);
        }
        public List<ClientTeamMemberViewModel> GetClientTeamMembers(int companyId, string clientId, string id)
        {
            return _teamMembersProvider.GetClientTeamMembers(companyId, clientId, id);
        }
        public List<Code_Description> AvailableTeamMembers(int companyId, string clientId, string id)
        {
            return _teamMembersProvider.AvailableTeamMembers(companyId, clientId, id);
        }
        public void AddTeamMembers(int companyId, string clientId, string id, List<string> users)
        {
            _teamMembersProvider.AddTeamMembers(companyId, clientId, id, users);
        }

        public string GetNameByTeamID(int companyId, string clientId, string id)
        {
            return _teamsProvider.GetNameByTeamID(companyId, clientId, id);
        }
        public void DeleteTeamMembers(int companyId, string clientId, string id, List<string> users)
        {
            _teamMembersProvider.DeleteTeamMembers(companyId, clientId, id, users);
        }

        public void DeleteTeamMember(int companyId, string clientId, string id, string uid)
        {
            _teamMembersProvider.DeleteTeamMember(companyId, clientId, id, uid);
        }
        public bool IsTeamTypeTaken(int companyId, string clientId, string type)
        {
            return _teamsProvider.IsTeamTypeTaken(companyId, clientId, type);
        }

        /*public bool DoesUserBelongToValidTeam(string payrollNumber, string userId)
        {
            var oClientNumber = ClientNumber.Parse(payrollNumber);
            var companyId = oClientNumber.CompanyId;
            var clientId = oClientNumber.ClientId;
            var profileId = oClientNumber.ProfileId;
            var userTeamId = _teamMembersProvider.GetClientTeamIdForUserId(userId);
            var payrollTeamId =
                _dbContext.ClientProfileSettings.Where(x => x.CompanyID == companyId && x.ClientID == clientId && x.ProfileID == profileId).Select(x => x.ClientTeamID).FirstOrDefault()
                ?? _teamMembersProvider.GetClientTeamIdForUserId(_dbContext.ClientClientSchedules.Where(x => x.ClientNumber == payrollNumber).Select(x => x.Responsible).FirstOrDefault());
            return userTeamId == payrollTeamId;
        }*/

        public IEnumerable<ClientTeamType> GetTeamTypes(int companyId, string clientId)
        {
            return _teamTypesProvider.GetTeamTypes(companyId, clientId);
        }

        public List<ClientTeamTypeViewModel> TeamTypesList(int companyId, string clientId)
        {
            List<ClientTeamTypeViewModel> result = new List<ClientTeamTypeViewModel>();
            var types = _teamTypesProvider.GetTeamTypes(companyId, clientId);
            foreach (ClientTeamType type in types)
            {
                result.Add(new ClientTeamTypeViewModel
                {
                    CompanyID = type.CompanyID,
                    ClientID = type.ClientID,
                    TeamType = type.TypeID,
                    TypeName = type.TypeName,
                    CanDelete = !_teamsProvider.IsTeamTypeTaken(type.CompanyID, type.ClientID, type.TypeID)
                });
            }
            return result;
        }

        public ClientTeamType GetTeamType(int companyId, string clientId, string type)
        {
            return _teamTypesProvider.GetTeamType(companyId, clientId, type);
        }

        public void DeleteTeamType(int companyId, string clientId, string type)
        {
            if (!_teamsProvider.IsTeamTypeTaken(companyId, clientId, type)) _teamTypesProvider.DeleteTeamType(companyId, clientId, type);
        }

        public bool UpdateTeamType(int companyId, string clientId, string type, string descr)
        {
            var teamtype = new ClientTeamType
            {
                CompanyID = companyId,
                ClientID = clientId,
                TypeID = type,
                TypeName = descr
            };
            bool result = _teamTypesProvider.GetTeamType(companyId, clientId, type) != null;
            if (result)
            {
                _teamTypesProvider.UpcertTeamType(teamtype);
            }
            return result;
        }

        public bool CreateTeamType(int companyId, string clientId, string type, string descr)
        {
            var teamtype = new ClientTeamType
            {
                CompanyID = companyId,
                ClientID = clientId,
                TypeID = type,
                TypeName = descr
            };
            bool result = _teamTypesProvider.GetTeamType(companyId, clientId, type) == null;
            if (result)
            {
                _teamTypesProvider.UpcertTeamType(teamtype);
            }
            return result;
        }
        /*public IEnumerable<ClientTeam> GetClientTeams(List<int> companyIds, List<string> clientIds, List<string> profileIds)
        {
            return _teamsProvider.GetClientTeams(companyIds, clientIds, profileIds);
        }*/

        #endregion

    }
}