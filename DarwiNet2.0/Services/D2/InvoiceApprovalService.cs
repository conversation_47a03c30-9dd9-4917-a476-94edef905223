using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using Thinkware.Cohesion.Payroll;
using DarwiNet2._0.Core;
using static DarwiNet2._0.Core.Constants.PayrollProfileApprovalTypes;
using DarwiNet2._0.Interfaces.Services;
using System.Text.RegularExpressions;
using DarwiNet2._0.Controllers.D2;
using Thinkware.Pay360.Messaging;
using Thinkware.Pay360.Payroll;
using DarwiNet2._0.Enumerations;

namespace DarwiNet2._0.Services.D2
{
    public class InvoiceApprovalService : IInvoiceApprovalService
    {
        private readonly IInvoiceApprovalProvider _invoiceApprovalProvider;
        private readonly IInvoicePayrollProvider _invoicePayrollProvider;
        private readonly IPayrollScheduleService _payrollScheduleService;
        private readonly IInvoiceApprovalSetupSequenceProvider _invoiceApprovalSetupSequenceProvider;
        private readonly ISmartTagProvider _smartTagProvider;
        private readonly IInvoiceApprovalRecipientProvider _invoiceApprovalRecipientProvider;
        private readonly IInvoiceApprovalSetupProvider _invoiceApprovalSetupProvider;
        private readonly ISMTPClientService _smtpClientService;
        private readonly IClientPayrollScheduleProvider _clientPayrollScheduleProvider;
        private readonly IUserProvider _userProvider;
        private readonly IInvoiceApprovalEmailTokenProvider _invoiceApprovalEmailTokenProvider;
        private readonly IPayrollProfileSettingProvider _payrollProfileSettingProvider;
        private readonly DnetEntities _dbContext;
        private readonly IPayrollTeamsService _payrollTeamsService;
        private readonly IPayrollProfileSettingService _payrollProfileSettingService;
        private readonly string _dnetUrl;
        private readonly IControlAndVarianceProvider _controlAndVarianceProvider;
        private readonly IPayrollWorkMasterProvider _payrollWorkMasterProvider;
        private readonly IPayrollFinalizeService _payrollFinalizeService;
        private readonly IPayrollProfileControlService _payrollProfileControlService;
        private readonly IPayrollProfileVarianceService _payrollProfileVarianceService;
        private readonly IPayrollSnapshotService _payrollSnapshotService;

        public InvoiceApprovalService(
            IInvoiceApprovalProvider invoiceApprovalProvider,
            IInvoicePayrollProvider invoicePayrollProvider,
            IPayrollScheduleService payrollScheduleService,
            IInvoiceApprovalSetupSequenceProvider invoiceApprovalSetupSequenceProvider,
            ISmartTagProvider smartTagProvider,
            IInvoiceApprovalRecipientProvider invoiceApprovalRecipientProvider,
            IInvoiceApprovalSetupProvider invoiceApprovalSetupProvider,
            ISMTPClientService sMTPClientService,
            IClientPayrollScheduleProvider clientPayrollScheduleProvider,
            IUserProvider userProvider,
            IInvoiceApprovalEmailTokenProvider invoiceApprovalEmailTokenProvider,
            IPayrollProfileSettingProvider payrollProfileSettingProvider,
            IPayrollProfileSettingService payrollProfileSettingService,
            IPayrollTeamsService payrollTeamsService,
            IControlAndVarianceProvider controlAndVarianceProvider,
            IPayrollWorkMasterProvider payrollWorkMasterProvider,
            IPayrollFinalizeService payrollFinalizeService,
            IPayrollProfileControlService payrollProfileControlService,
            IPayrollProfileVarianceService payrollProfileVarianceService,
            IPayrollSnapshotService payrollSnapshotService,
            DnetEntities dbContext,
            string dnetUrl = "")
        {
            _invoiceApprovalProvider = invoiceApprovalProvider ?? throw new ArgumentNullException(nameof(invoiceApprovalProvider));
            _invoicePayrollProvider = invoicePayrollProvider ?? throw new ArgumentNullException(nameof(invoicePayrollProvider));
            _payrollScheduleService = payrollScheduleService ?? throw new ArgumentNullException(nameof(payrollScheduleService));
            _invoiceApprovalSetupSequenceProvider = invoiceApprovalSetupSequenceProvider ?? throw new ArgumentNullException(nameof(invoiceApprovalSetupSequenceProvider));
            _smartTagProvider = smartTagProvider ?? throw new ArgumentNullException(nameof(smartTagProvider));
            _invoiceApprovalRecipientProvider = invoiceApprovalRecipientProvider ?? throw new ArgumentNullException(nameof(invoiceApprovalRecipientProvider));
            _invoiceApprovalSetupProvider = invoiceApprovalSetupProvider ?? throw new ArgumentNullException(nameof(invoiceApprovalSetupProvider));
            _smtpClientService = sMTPClientService ?? throw new ArgumentNullException(nameof(sMTPClientService));
            _clientPayrollScheduleProvider = clientPayrollScheduleProvider ?? throw new ArgumentNullException(nameof(clientPayrollScheduleProvider));
            _userProvider = userProvider ?? throw new ArgumentNullException(nameof(userProvider));
            _invoiceApprovalEmailTokenProvider = invoiceApprovalEmailTokenProvider ?? throw new ArgumentNullException(nameof(invoiceApprovalEmailTokenProvider));
            _payrollProfileSettingProvider = payrollProfileSettingProvider ?? throw new ArgumentNullException(nameof(payrollProfileSettingProvider));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _payrollProfileSettingService = payrollProfileSettingService;
            _payrollTeamsService = payrollTeamsService;
            _dnetUrl = string.IsNullOrEmpty(dnetUrl) ? GlobalVariables.DNetURL : dnetUrl;
            _controlAndVarianceProvider = controlAndVarianceProvider;
            _payrollWorkMasterProvider = payrollWorkMasterProvider ?? throw new ArgumentNullException(nameof(_payrollWorkMasterProvider));
            _payrollFinalizeService = payrollFinalizeService ?? throw new ArgumentNullException(nameof(payrollFinalizeService));
            _payrollProfileControlService = payrollProfileControlService ?? throw new ArgumentNullException(nameof(payrollProfileControlService));
            _payrollProfileVarianceService = payrollProfileVarianceService ?? throw new ArgumentNullException(nameof(payrollProfileVarianceService));
            _payrollSnapshotService = payrollSnapshotService ?? throw new ArgumentNullException(nameof(payrollSnapshotService));
        }

        #region Public Methods

        public TableQueryInfo<ApprovalsTableRowDTO> GetListInvoiceApprovals(string currentUser, TableFilter filters)
        {
            return _invoiceApprovalProvider.GetListInvoiceApprovals(currentUser, filters);
        }

        public bool CanMakeApprovalActions(int companyID, string clientID, int invoiceNumber, string dnetUserId)
        {
            if(_invoiceApprovalProvider.GetInvoiceApprovalSetupSequenceId(companyID, clientID, invoiceNumber, dnetUserId) > 0) return true;
            var rec = GetInvoiceApproval(companyID, clientID, invoiceNumber, dnetUserId);
            if (rec != null && !rec.Inactive && !rec.Approved && !rec.Rejected) return true;
            return false;
        }

        public void ApproveInvoice(int companyID, string clientID, int invoiceNumber, string dnetUserId)
        {
            int invoiceApprovalSetupSequenceId = _invoiceApprovalProvider.GetInvoiceApprovalSetupSequenceId(companyID, clientID, invoiceNumber, dnetUserId);

            ApproveInvoice(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequenceId);
        }

        public string GetApprovalApprover(int sequenceId)
        {
            return _invoiceApprovalProvider.GetApprovalApprover(sequenceId);
        }

        public void ApproveInvoice(int companyID, string clientID, int invoiceNumber, int invoiceApprovalSetupSequenceId)
        {
            _invoiceApprovalProvider.ApproveInvoice(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequenceId);
            _invoiceApprovalEmailTokenProvider.RemoveUnusedEmailToken(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequenceId);

            // invoice has been approved, submit for next round of approvals, if any
            var invoiceApprovalSetupSequence = _invoiceApprovalSetupSequenceProvider.GetInvoiceApprovalSetupSequence(invoiceApprovalSetupSequenceId);

            if (!_invoiceApprovalProvider.IsInvoiceApprovalSequenceComplete(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequence.InvoiceApprovalSetupSequenceID))
            {
                _invoiceApprovalProvider.RemoveUnusedInvoiceApprovalsBySetupID(companyID, clientID, invoiceApprovalSetupSequence.InvoiceApprovalSetupID, invoiceNumber);
                var invoiceApprovalSetup = invoiceApprovalSetupSequence.InvoiceApprovalSetup;
                if (invoiceApprovalSetup.ApprovalModel != InvoiceApprovalModel.Parallel.ToString()) 
                {
                    new SubmitInvoiceApprovalRequestHandler(_dbContext).Handle(new SubmitInvoiceApprovalRequest(companyID, clientID, invoiceNumber));
                }
            }
            else
            {
                _invoiceApprovalProvider.RemoveUnusedInvoiceApprovalsBySetupID(companyID, clientID, invoiceApprovalSetupSequence.InvoiceApprovalSetupID, invoiceNumber);
                string payrollNumber = _invoicePayrollProvider.FindPayrollNumberByDarwinInvoiceNumber(companyID, clientID, invoiceNumber);
                _invoicePayrollProvider.UpdateStatus(companyID, clientID, payrollNumber, invoiceNumber, PayrollStatus.InvoiceApproved);
                NotifyInvoiceApproved(companyID, clientID, invoiceNumber, payrollNumber, invoiceApprovalSetupSequence);
                PayrollSchedule payrollSchedule = _payrollScheduleService.FindPayrollSchedule(payrollNumber);
                if (payrollSchedule.AutoPayOptions.TryGetValue(Constants.AutoPayOptions.AUTO_PROCESS_FINALIZE, out bool autoProcessFinalize))
                {
                    if (autoProcessFinalize)
                    {
                        List<InvoicePayroll> invoicePayrolls = _invoicePayrollProvider.GetInvoicePayrolls(payrollNumber);
                        InvoicePayroll nextInvoiceToApprove = invoicePayrolls.FirstOrDefault(x => PayrollStatusDefinition.Parse((short)x.Status).Status != PayrollStatus.InvoiceApproved);
                        if (nextInvoiceToApprove == null)
                        {
                            foreach (var invoicePayroll in invoicePayrolls)
                            {
                                _invoicePayrollProvider.UpdateStatus(invoicePayroll.CompanyID, invoicePayroll.ClientID, payrollNumber, invoicePayroll.DarwinInvoiceNumber, PayrollStatus.InvoiceCompleted);
                            }

                            var payrollWorkMaster = _payrollWorkMasterProvider.GetPayrollWorkMaster(payrollNumber);
                            string userId = payrollWorkMaster != null ? payrollWorkMaster.UserID : GlobalVariables.DNETOwnerID ?? Thinkware.Pay360.CohesionDefaults.UserId;
                            ProcessPayrollResult result = new ProcessPayrollHandler(_dbContext, _payrollTeamsService, _payrollProfileSettingService, _controlAndVarianceProvider, _payrollWorkMasterProvider, _payrollFinalizeService, _payrollProfileControlService, _payrollProfileVarianceService).Handle(new ProcessPayrollRequest(payrollNumber, userId));
                        }
                        else
                        {
                            new PayrollAutomationRequestHandler(_dbContext, _payrollTeamsService, _payrollProfileSettingService, _controlAndVarianceProvider, _payrollWorkMasterProvider, _payrollFinalizeService, _payrollProfileControlService, _payrollProfileVarianceService).Handle(new PayrollAutomationRequest(companyID, clientID, nextInvoiceToApprove.DarwinInvoiceNumber, payrollNumber, StatusEventTypes.ProcessInvoiceResultComplete));
                        }
                    }
                }
            }
        }

        public void RejectInvoice(int companyID, string clientID, int invoiceNumber, string rejectMessage, string dnetUserId)
        {
            int invoiceApprovalSetupSequenceId = _invoiceApprovalProvider.GetInvoiceApprovalSetupSequenceId(companyID, clientID, invoiceNumber, dnetUserId);

            RejectInvoice(companyID, clientID, invoiceNumber, rejectMessage, invoiceApprovalSetupSequenceId);
        }

        public void RejectInvoice(int companyID, string clientID, int invoiceNumber, string rejectMessage, int invoiceApprovalSetupSequenceId)
        {
            _invoiceApprovalProvider.RejectInvoice(companyID, clientID, invoiceNumber, rejectMessage, invoiceApprovalSetupSequenceId);
            _invoiceApprovalEmailTokenProvider.RemoveUnusedEmailToken(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequenceId);
            _invoiceApprovalProvider.RemoveUnusedInvoiceApprovals(companyID, clientID, invoiceNumber);
            _invoiceApprovalProvider.SetInvoiceApprovalsInactive(companyID, clientID, invoiceNumber);
            string payrollNumber = _invoicePayrollProvider.FindPayrollNumberByDarwinInvoiceNumber(companyID, clientID, invoiceNumber);
            _invoicePayrollProvider.UpdateStatus(companyID, clientID, payrollNumber, invoiceNumber, Thinkware.Pay360.Payroll.PayrollStatus.InvoiceDeclined);
            var schedule = _payrollScheduleService.FindSchedule(payrollNumber);
            var notifyOnInvoiceDeclined = _payrollProfileSettingProvider.NotifyOnInvoiceDeclined(payrollNumber);
            if (notifyOnInvoiceDeclined)
            {
                new SMTPNotificationHandler(_dbContext)
                    .Handle(new SMTPNotificationRequest(payrollNumber, schedule.CompanyID, schedule.ClientID, schedule.ProfileID, invoiceNumber, rejectMessage, PayrollProfileApprovalNotificationTypes.INVOICE_DECLINED));
            }
        }

        public InvoiceApproval GetInvoiceApproval(int companyID, string clientID, int invoiceNumber, string dnetUserId)
        {
            return _invoiceApprovalProvider.GetInvoiceApproval(invoiceNumber, companyID, clientID, dnetUserId);
        }

        public bool IsInvoiceApprover(int companyID, string clientID, int invoiceNumber, string dnetUserId)
        {
            return _invoiceApprovalProvider.IsInvoiceApprover(invoiceNumber, companyID, clientID, dnetUserId);
        }

        public void DeleteInvoiceApprovals(int companyID, string clientID, int? darwinInvoiceNumber = null)
        {
            _invoiceApprovalProvider.RemoveUnusedInvoiceApprovals(companyID, clientID, darwinInvoiceNumber);
        }

        public void SetInvoiceApprovalsInactive(int companyID, string clientID, int? darwinInvoiceNumber = null)
        {
            _invoiceApprovalProvider.SetInvoiceApprovalsInactive(companyID, clientID, darwinInvoiceNumber);
        }

        public List<MultiselectOptionsDTO> GetAllInvoiceApprovalRecipients()
        {
            return _invoiceApprovalProvider.GetAllInvoiceApprovalRecipients();
        }

        public ApprovalInformationDTO GetApprovalInformation(int companyId, string clientId, int invoiceNumber)
        {
            return _invoiceApprovalProvider.GetApprovalInformation(companyId, clientId, invoiceNumber);
        }

        #endregion

        #region Private Methods

        private void NotifyInvoiceApproved(int companyID, string clientID, int invoiceNumber, string payrollNumber, InvoiceApprovalSetupSequence invoiceApprovalSetupSequence)
        {
            bool notifyInvoiceApproved = _invoiceApprovalSetupProvider.NotifyInvoiceApproved(companyID, clientID, invoiceNumber, payrollNumber);

            if (notifyInvoiceApproved)
            {
                var invoiceApprovalSetup = invoiceApprovalSetupSequence.InvoiceApprovalSetup;
                int companyId = invoiceApprovalSetup.CompanyID;
                string clientId = invoiceApprovalSetup.ClientID;
                string profileId = invoiceApprovalSetup.ProfileID;

                var notifyInvoiceApprovedInvoiceApprovalSetup = _invoiceApprovalSetupProvider.GetInvoiceApprovalSetupByType(companyId, clientId, profileId, PayrollProfileApprovalNotificationTypes.INVOICE_APPROVED);
                string emailMessage = notifyInvoiceApprovedInvoiceApprovalSetup.EmailMessage ?? "";

                var approver = invoiceApprovalSetupSequence.InvoiceApprovalRecipient.RecipientID;

                var subject = $"Invoice Approved";
                var emailBody = _smartTagProvider.WithApprover(approver)
                                                 .FillSmartTags(companyId, clientId, profileId, payrollNumber, emailMessage, invoiceNumber);

                Notify(companyId, clientId, invoiceNumber, payrollNumber, PayrollProfileApprovalNotificationTypes.INVOICE_APPROVED, subject, emailBody);
            }
        }

        private void NotifyInvoiceRejected(int companyID, string clientID, int invoiceNumber, string rejectMessage, int invoiceApprovalSetupSequenceId)
        {
            // if NotifyOnRejection, notify processor and all approvers
            string payrollNumber = _invoicePayrollProvider.FindPayrollNumberByDarwinInvoiceNumber(companyID, clientID, invoiceNumber);
            if (_invoiceApprovalSetupProvider.NotifyOnRejection(companyID, clientID, invoiceNumber, payrollNumber))
            {
                string processor = _clientPayrollScheduleProvider.GetPayrollProcessor(payrollNumber);
                UserModel processorDetails = _userProvider.GetUserDetailsByUserId(processor);

                if (!string.IsNullOrEmpty(processorDetails.Email))
                {
                    var invoiceApprovalSetup = _invoiceApprovalSetupProvider.GetInvoiceApprovalSetup(invoiceApprovalSetupSequenceId);

                    var recipients = new List<UserModel>();
                    recipients.Add(processorDetails);
                    var approvers = invoiceApprovalSetup.InvoiceApprovalRecipients.Where(r => !r.Inactive && !string.IsNullOrEmpty(r.RecipientID));
                    if (approvers.Any())
                    {
                        var approverDetails = approvers.Where(r => !string.IsNullOrEmpty(r.User.Email))
                            .Select(r => new UserModel
                            {
                                UserID = r.RecipientID,
                                Name = r.User.Name,
                                Email = r.User.Email
                            });

                        recipients.AddRange(approverDetails);
                    }

                    var subject = "Payroll Rejected";
                    var emailBody = FillSmartTagsForRejection(payrollNumber, invoiceApprovalSetup, invoiceApprovalSetupSequenceId, PayrollProfilePayrollApprovalTypes.APPROVE_INVOICE, rejectMessage, invoiceNumber);

                    SendMessage(recipients, subject, emailBody, payrollNumber);
                }
            }
        }

        private void Notify(int companyID, string clientID, int invoiceNumber, string payrollNumber, string payrollProfileApprovalNotificationType, string subject, string emailBody = null)
        {
            var notificationRecipients = _invoiceApprovalRecipientProvider.GetInvoiceApprovalSetupNotificationRecipients(companyID, clientID, invoiceNumber, payrollProfileApprovalNotificationType);

            if (notificationRecipients.Any())
            {
                if (string.IsNullOrEmpty(emailBody))
                {
                    emailBody = FillSmartTags(payrollNumber, notificationRecipients.First().InvoiceApprovalSetup, invoiceNumber);
                }

                var recipients = notificationRecipients.Where(r => !string.IsNullOrEmpty(r.User.Email) && !r.Inactive)
                        .Select(r => new UserModel
                        {
                            UserID = r.RecipientID,
                            Name = r.User.Name,
                            Email = r.User.Email
                        });

                SendMessage(recipients, subject, emailBody, payrollNumber);
            }
        }

        public string FillSmartTags(string payrollNumber, InvoiceApprovalSetup invoiceApprovalSetup, int invoiceNumber)
        {
            int companyId = invoiceApprovalSetup.CompanyID;
            string clientId = invoiceApprovalSetup.ClientID;
            string profileId = invoiceApprovalSetup.ProfileID;
            string emailMessage = invoiceApprovalSetup.EmailMessage ?? "";

            if (!string.IsNullOrWhiteSpace(emailMessage))
            {
                return _smartTagProvider.FillSmartTags(companyId, clientId, profileId, payrollNumber, emailMessage, invoiceNumber);
            }

            return emailMessage;
        }

        private void SendMessage(IEnumerable<UserModel> recipients, string subject, string body, string payrollNumber)
        {
            body = InsertDnetUrl(body);

            if (RecipientDetails(body))
            {
                foreach (var recipient in recipients)
                {
                    body = InsertRecipientDetails(body, recipient.UserID, recipient.Name);
                    _smtpClientService.SendSMTPMail(recipient.Email, subject, body, payrollNumber);
                }
            }
            else
            {
                _smtpClientService.SendSMTPMail(recipients.Select(r => r.Email), subject, body, payrollNumber);
            }
        }

        private string InsertDnetUrl(string body)
        {
            if (Regex.Match(body, @"\[DNetURL\]").Success)
            {
                body = _smartTagProvider.InsertDnetUrl(body, _dnetUrl);
            }
            return body;
        }

        private bool RecipientDetails(string body)
        {
            return (Regex.Match(body, @"\[RecipientName\]").Success ||
                    Regex.Match(body, @"\[RecipientUserID\]").Success);
        }

        private string InsertRecipientDetails(string body, string userId, string name)
        {
            return _smartTagProvider.InsertRecipientDetails(body, userId, name);
        }

        private string FillSmartTagsForRejection(string payrollNumber, InvoiceApprovalSetup invoiceApprovalSetup, int invoiceApprovalSetupSequenceId, string rejectionType, string rejectReason, int invoiceNumber)
        {
            int companyId = invoiceApprovalSetup.CompanyID;
            string clientId = invoiceApprovalSetup.ClientID;
            string profileId = invoiceApprovalSetup.ProfileID;
            string emailMessage = invoiceApprovalSetup.EmailMessage ?? "";

            var rejectedBy = invoiceApprovalSetup.InvoiceApprovalSetupSequences
                        .FirstOrDefault(ss => ss.InvoiceApprovalSetupSequenceID == invoiceApprovalSetupSequenceId)
                        ?.InvoiceApprovalRecipient;

            return _smartTagProvider.ForRejection(rejectionType, rejectedBy.RecipientID, rejectReason)
                                    .FillSmartTags(companyId, clientId, profileId, payrollNumber, emailMessage, invoiceNumber);
        }

        public bool IsTokenValid(int companyID, string clientID, int invoiceNumber, int invoiceApprovalSetupSequenceId, string token)
        {
            return _invoiceApprovalEmailTokenProvider.IsTokenValid(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequenceId, token);
        }

        public void SetTokenDateUsed(int companyID, string clientID, int invoiceNumber, int payrollApprovalSetupSequenceId, string token)
        {
            _invoiceApprovalEmailTokenProvider.SetDateUsed(companyID, clientID, invoiceNumber, payrollApprovalSetupSequenceId, token);
        }

        public IEnumerable<InvoiceApproval> GetExpiredApprovals()
        {
            return _invoiceApprovalProvider.GetExpiredApprovals();
        }

        public void ProcessAutoApprovals(IEnumerable<InvoiceApproval> expiredApprovals)
        {
            // auto approve
            var expiredApprovalsToAutoApprove = expiredApprovals.Where(a => a.InvoiceApprovalSetupSequence != null && a.InvoiceApprovalSetupSequence.InvoiceApprovalSetup.AutoApprovalAction == "Approve");
            var invoicesToApprove = expiredApprovalsToAutoApprove.Select(x => new { 
                CompanyID = x.CompanyID,
                ClientID = x.ClientID,
                InvoiceNumber = x.InvoiceNumber 
            }).Distinct();
            List<InvoiceApproval> approvalsToAutoApprove = new List<InvoiceApproval>();
            foreach (var invoiceApproval in invoicesToApprove)
            {
                approvalsToAutoApprove.Add(expiredApprovals.FirstOrDefault(x => x.CompanyID == invoiceApproval.CompanyID &&
                                                                                x.ClientID == invoiceApproval.ClientID &&
                                                                                x.InvoiceNumber == invoiceApproval.InvoiceNumber));
            }


            foreach (var invoiceApproval in approvalsToAutoApprove)
            {
                int companyID = invoiceApproval.CompanyID;
                string clientID = invoiceApproval.ClientID;
                int invoiceNumber = invoiceApproval.InvoiceNumber;
                string payrollNumber = _invoicePayrollProvider.FindPayrollNumberByDarwinInvoiceNumber(companyID, clientID, invoiceNumber);
                var invoiceApprovalSetupSequence = invoiceApproval.InvoiceApprovalSetupSequence;

                if (!_invoiceApprovalProvider.IsInvoiceApprovalSequenceComplete(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequence.InvoiceApprovalSetupSequenceID))
                {
                    var invoiceApprovalSetup = invoiceApprovalSetupSequence.InvoiceApprovalSetup;
                    _invoiceApprovalProvider.ProcessAutoApproval(invoiceApproval);
                    _invoiceApprovalProvider.RemoveUnusedInvoiceApprovals(companyID, clientID, invoiceNumber);
                    //InitiateInvoiceApprovalsNotifications(companyID, clientID, invoiceNumber, invoiceApprovalSetup.ApprovalType);
                    if (invoiceApprovalSetup.ApprovalModel != InvoiceApprovalModel.Parallel.ToString())
                    {
                        new SubmitInvoiceApprovalRequestHandler(_dbContext).Handle(new SubmitInvoiceApprovalRequest(companyID, clientID, invoiceNumber));
                    }
                }
                else
                {
                    _invoiceApprovalProvider.ProcessAutoApproval(invoiceApproval);
                    _invoiceApprovalProvider.RemoveUnusedInvoiceApprovals(companyID, clientID, invoiceNumber);
                    _invoicePayrollProvider.UpdateStatus(companyID, clientID, payrollNumber, invoiceNumber, PayrollStatus.InvoiceApproved);
                    NotifyInvoiceApproved(companyID, clientID, invoiceNumber, payrollNumber, invoiceApprovalSetupSequence);
                    PayrollSchedule payrollSchedule = _payrollScheduleService.FindPayrollSchedule(payrollNumber);
                    if (payrollSchedule.AutoPayOptions.TryGetValue(Constants.AutoPayOptions.AUTO_PROCESS_FINALIZE, out bool autoProcessFinalize))
                    {
                        if (autoProcessFinalize)
                        {
                            List<InvoicePayroll> invoicePayrolls = _invoicePayrollProvider.GetInvoicePayrolls(payrollNumber);
                            InvoicePayroll nextInvoiceToApprove = invoicePayrolls.FirstOrDefault(x => PayrollStatusDefinition.Parse((short)x.Status).Status != PayrollStatus.InvoiceApproved &&
                                                                                                      x.DarwinInvoiceNumber != invoiceNumber);
                            if (nextInvoiceToApprove == null)
                            {
                                List<int> invoicesToMarkComplete = _invoicePayrollProvider.GetInvoicePayrolls(payrollNumber).Select(x => x.DarwinInvoiceNumber).ToList();
                                foreach (int completedInvoiceNumber in invoicesToMarkComplete)
                                {
                                    _invoicePayrollProvider.UpdateStatus(companyID, clientID, payrollNumber, completedInvoiceNumber, PayrollStatus.InvoiceCompleted);
                                }

                                var payrollWorkMaster = _payrollWorkMasterProvider.GetPayrollWorkMaster(payrollNumber);
                                string userId = payrollWorkMaster != null ? payrollWorkMaster.UserID : GlobalVariables.DNETOwnerID ?? Thinkware.Pay360.CohesionDefaults.UserId;
                                ProcessPayrollResult result = new ProcessPayrollHandler(_dbContext, _payrollTeamsService, _payrollProfileSettingService, _controlAndVarianceProvider, _payrollWorkMasterProvider, _payrollFinalizeService, _payrollProfileControlService, _payrollProfileVarianceService).Handle(new ProcessPayrollRequest(payrollNumber, userId));
                            }
                            else
                            {
                                new PayrollAutomationRequestHandler(_dbContext, _payrollTeamsService, _payrollProfileSettingService, _controlAndVarianceProvider, _payrollWorkMasterProvider, _payrollFinalizeService, _payrollProfileControlService, _payrollProfileVarianceService).Handle(new PayrollAutomationRequest(companyID, clientID, nextInvoiceToApprove.DarwinInvoiceNumber, payrollNumber, StatusEventTypes.ProcessInvoiceResultComplete));
                            }
                        }
                    }
                }

            }


            // auto reject
            var expiredApprovalsToAutoReject = expiredApprovals.Where(a => a.InvoiceApprovalSetupSequence != null && a.InvoiceApprovalSetupSequence.InvoiceApprovalSetup.AutoApprovalAction == "Reject").ToList();
            var invoiceApprovalsToReject = expiredApprovalsToAutoReject.Select(x => new {
                CompanyID = x.CompanyID,
                ClientID = x.ClientID,
                InvoiceNumber = x.InvoiceNumber
            }).Distinct();
            List<InvoiceApproval> approvalsToAutoReject = new List<InvoiceApproval>();
            foreach (var invoiceApproval in invoiceApprovalsToReject)
            {
                approvalsToAutoReject.Add(expiredApprovals.FirstOrDefault(x => x.CompanyID == invoiceApproval.CompanyID &&
                                                                               x.ClientID == invoiceApproval.ClientID &&
                                                                               x.InvoiceNumber == invoiceApproval.InvoiceNumber));
            }

            // _payrollApprovalProvider.ProcessAutoRejection(approvalsToAutoReject);

            foreach (var invoiceApproval in approvalsToAutoReject)
            {
                int companyID = invoiceApproval.CompanyID;
                string ClientID = invoiceApproval.ClientID;
                int invoiceNumber = invoiceApproval.InvoiceNumber;
                var payrollNumber = _invoicePayrollProvider.FindPayrollNumberByDarwinInvoiceNumber(companyID, ClientID, invoiceNumber);
                var invoiceApprovalSetupSequence = invoiceApproval.InvoiceApprovalSetupSequence;
                _invoiceApprovalProvider.ProcessAutoRejection(invoiceApproval);
                _invoiceApprovalProvider.RemoveUnusedInvoiceApprovals(companyID, ClientID, invoiceNumber);
                _invoicePayrollProvider.UpdateStatus(companyID, ClientID, payrollNumber, invoiceNumber, PayrollStatus.InvoiceDeclined);
                //NotifyInvoiceRejected(companyID, ClientID, invoiceNumber, "Expired", invoiceApprovalSetupSequence.InvoiceApprovalSetupSequenceID);
                var schedule = _payrollScheduleService.FindSchedule(payrollNumber);
                var notifyOnPayrollDeclined = _payrollProfileSettingProvider.NotifyOnPayrollDeclined(payrollNumber);
                if (notifyOnPayrollDeclined)
                {
                    new SMTPNotificationHandler(_dbContext)
                        .Handle(new SMTPNotificationRequest(payrollNumber, schedule.CompanyID, schedule.ClientID, schedule.ProfileID, "Approval Declined", PayrollProfileApprovalNotificationTypes.INVOICE_DECLINED));
                }
            }
        }

        #endregion
    }
}