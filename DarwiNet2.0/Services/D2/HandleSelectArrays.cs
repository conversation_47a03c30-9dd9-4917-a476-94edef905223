using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Providers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Web;

namespace DarwiNet2._0.Services.D2
{
    public class HandleSelectArrays
    {
        private readonly BaseProvider _baseProvider;

        public HandleSelectArrays()
        {
            _baseProvider = new BaseProvider();
        }

        public void HandleSelectArray(string[] selectedCodes, string type, string clientId, string profileId, int companyId)
        {
            Delete(companyId, clientId, profileId, type);
            Insert(companyId, clientId, profileId, type, selectedCodes);
        }

        public void HandleSecondCheckSelectArrays(string[] array, string type, string clientId, string buildId, int companyId)
        {
            var _dbContext = Services.GetDB.GetDBConn();
            var itemList = _dbContext.PayrollProfileSecondCheckOptions.Where(cr => cr.SelectionType == type && cr.CompanyID == companyId && cr.ClientID == clientId && cr.ProfileID == buildId).ToList();
            _baseProvider.RemoveItems(itemList);

            if (array == null) return;
            foreach (var item in array)
            {
                var newSite = new PayrollProfileSecondCheckOption();
                newSite.SelectedCode = item;
                newSite.SelectionType = type;
                newSite.CompanyID = companyId;
                newSite.ClientID = clientId;
                newSite.ProfileID = buildId;

                _baseProvider.AddItem(newSite);
            }
        }

        public void HandleEmployeeSelectArrays(string[] array, string employeeId, string clientId, string buildId, int companyId)
        {
            var _dbContext = Services.GetDB.GetDBConn();
            var itemList = _dbContext.PayrollProfileEmployees.Where(cr => cr.CompanyID == companyId && cr.ClientID == clientId).ToList();
            _baseProvider.RemoveItems(itemList);

            if (array == null) return;
            foreach (var item in array)
            {
                var newSite = new PayrollProfileEmployee();
                newSite.EmployeeID = item;
                newSite.CompanyID = companyId;
                newSite.ClientID = clientId;
                newSite.ProfileID = buildId;

                _baseProvider.AddItem(newSite);
            }
        }

        private void Delete(int companyId, string clientId, string profileId, string type)
        {
            SqlParameter[] parameters = new SqlParameter[4]
            {
                new SqlParameter("CompanyID", companyId),
                new SqlParameter("ClientID", clientId),
                new SqlParameter("ProfileID", profileId),
                new SqlParameter("SelectionType", type)
            };

            string deleteQuery = "DELETE FROM PayrollProfileEmployeeCriterias WHERE CompanyID = @CompanyID AND ClientID = @ClientID AND ProfileID = @ProfileId AND SelectionType = @SelectionType;";
            _baseProvider.ExecuteNonQuery(deleteQuery, parameters);
        }

        private void Insert(int companyId, string clientId, string profileId, string type, string[] selectedCodes)
        {
            if (selectedCodes.Count() == 0)
                return;

            BaseProvider baseProvider = new BaseProvider();
            //var _dbContext = Services.GetDB.GetDBConn();

            SqlParameter[] parameters = new SqlParameter[4]
            {
                new SqlParameter("CompanyID", companyId),
                new SqlParameter("ClientID", clientId),
                new SqlParameter("ProfileID", profileId),
                new SqlParameter("SelectionType", type)
            };

            StringBuilder insertQuery = new StringBuilder("INSERT INTO PayrollProfileEmployeeCriterias (CompanyID, ClientID, ProfileID, SelectionType, SelectedCode) VALUES ");
            foreach (string code in selectedCodes)
            {
                insertQuery.AppendLine(string.Format("(@CompanyID, @ClientID, @ProfileID, @SelectionType, '{0}'),", code));
            }
            insertQuery.Remove(insertQuery.Length - 3, 3);
            insertQuery.Append(";");

            baseProvider.ExecuteNonQuery(insertQuery.ToString(), parameters);
            //_dbContext.SaveChanges();
        }
    }
}