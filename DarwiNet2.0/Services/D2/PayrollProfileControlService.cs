using DarwiNet2._0.Controllers;
using DarwiNet2._0.Core;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Services.D2
{
    /// <summary>
    ///     Provides functionality for Payroll Profile Control related functions.
    /// </summary>
    /// <history>
    ///     [lyeager]   06/30/20    Task-5863: Created.
    /// </history>
    public class PayrollProfileControlService : IPayrollProfileControlService
    {
        #region Fields

        private readonly IPayrollProfileControlProvider _payrollProfileControlProvider;

        #endregion

        #region Constructors

        /// <summary>
        ///     Default constructor.
        /// </summary>
        /// <history>
        ///     [lyeager]    06/30/20    Task-5863: Created.
        /// </history>
        public PayrollProfileControlService(IPayrollProfileControlProvider payrollProfileControlProvider)
        {
            _payrollProfileControlProvider = payrollProfileControlProvider ?? throw new ArgumentNullException(nameof(payrollProfileControlProvider));
        }

        #endregion

        #region Public Functions

        public List<PayrollProfileControl> List(int companyId, string clientId, string profileId)
        {
            return _payrollProfileControlProvider.List(companyId, clientId, profileId);
        }

        public void Update(int companyId, string clientId, string profileId, List<PayrollProfileControl> controls)
        {
            _payrollProfileControlProvider.Update(companyId, clientId, profileId, controls);
        }

        public bool HasProfileControls(int companyID, string clientID, string profileID, string controlType)
        {
            return _payrollProfileControlProvider.HasProfileControls(companyID, clientID, profileID, controlType);
        }

        #endregion
    }
}