using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;

namespace DarwiNet2._0.Services.D2
{
    public class CommissionsService : ICommissionsService
    {
        #region Fields

        private readonly ICommissionsProvider _commissionsProvider;
        private readonly IInvoicePayrollDetailsProvider _invoicePayrollDetailsProvider;
        private readonly IInvoiceProvider _invoiceProvider;
        private readonly IPayrollBatchProvider _payrollBatchProvider;
        private readonly IPayrollBatchDetailProvider _payrollBatchDetailProvider;
        private readonly ISalespersonsProvider _salespersonsProvider;
        private readonly ICommissionAmountsProvider _commissionAmountsProvider;
        private readonly IClientCommissionsProvider _clientCommissionsProvider;

        #endregion

        #region Constructors

        public CommissionsService (ICommissionsProvider commissionsProvider, 
                                   IInvoicePayrollDetailsProvider invoicePayrollDetailsProvider,
                                   IInvoiceProvider invoiceProvider,
                                   IPayrollBatchProvider payrollBatchProvider,
                                   IPayrollBatchDetailProvider payrollBatchDetailProvider,
                                   ISalespersonsProvider salespersonsProvider,
                                   ICommissionAmountsProvider commissionAmountsProvider,
                                   IClientCommissionsProvider clientCommissionsProvider)
        {
            _commissionsProvider = commissionsProvider ?? throw new ArgumentNullException(nameof(commissionsProvider));
            _invoicePayrollDetailsProvider = invoicePayrollDetailsProvider ?? throw new ArgumentNullException(nameof(invoicePayrollDetailsProvider));
            _invoiceProvider = invoiceProvider ?? throw new ArgumentNullException(nameof(invoiceProvider));
            _payrollBatchProvider = payrollBatchProvider ?? throw new ArgumentNullException(nameof(payrollBatchProvider));
            _payrollBatchDetailProvider = payrollBatchDetailProvider ?? throw new ArgumentNullException(nameof(payrollBatchDetailProvider));
            _salespersonsProvider = salespersonsProvider ?? throw new ArgumentNullException(nameof(salespersonsProvider));
            _commissionAmountsProvider = commissionAmountsProvider ?? throw new ArgumentNullException(nameof(commissionAmountsProvider));
            _clientCommissionsProvider = clientCommissionsProvider ?? throw new ArgumentNullException(nameof(clientCommissionsProvider));
        }

        #endregion

        #region Public Methods

        public TableQueryInfo<CommissionsCalculationTableRowModel> GetListCommissionsCalculations(TableFilter filters)
        {
            return _commissionsProvider.GetListCommissionsCalculations(filters);
        }

        public TableQueryInfo<CommissionsCalculationTableRowModel> GetListCalculatedCommissions(TableFilter filters)
        {
            return _commissionsProvider.GetListCalculatedCommissions(filters);
        }

        public TableQueryInfo<CommissionsCalculationTableRowModel> GetListPaidCommissions(TableFilter filters)
        {
            return _commissionsProvider.GetListPaidCommissions(filters);
        }

        public List<ClientCommissionTier> GetListCommissionTiersBySalespersonID(int companyID, string clientID, string salespersonID, int method)
        {
            return _commissionsProvider.GetListCommissionTiersBySalespersonID(companyID, clientID, salespersonID, method);
        }

        public List<SalespersonSetupDTO> GetListClientCommissions(int commpanyID, string clientID)
        {
            return _clientCommissionsProvider.GetListClientCommissions(commpanyID, clientID);
        }

        public CommissionAmount CreateCommissionAmount(CommissionDTO commission)
        {
            decimal commissionable = GetCommissionableAmount(commission.CompanyID, commission.ClientID, commission.InvoiceNumber, commission.CommissionMethod);
            decimal commissionTotal = GetCommissonTotal(commission.CompanyID, commission.ClientID, commission.SalespersonID, commission.CommissionMethod, (decimal)commissionable);
            DateTime.TryParse(commission.InvoiceDate, out DateTime date);
            CommissionAmount commissionAmount = new CommissionAmount
            {
                CompanyID = commission.CompanyID,
                ClientID = commission.ClientID,
                InvoiceNumber = commission.InvoiceNumber,
                Salesperson = commission.SalespersonID,
                InvoiceDate = date,
                CommissionMethod = commission.CommissionMethod,
                Commissionable = commissionable,
                Commission = commissionTotal,
                Paid = false,
                PaidDate = null,
                PaymentMethod = null,
                Checkbook = null,
                CheckNumber = null,
                Vendor = null,
                CommissionScale = commission.CommissionScale,
                CommissionPercent = commission.CommissionPercent,
            };
            return commissionAmount;
        }

        public decimal GetCommissionableAmount(int companyID, string clientID, int invoiceNumber, int method)
        {
            var invoicePayrollDetails = _invoicePayrollDetailsProvider.ListInvoicePayrollDetailsByDarwinInvoiceNumber(companyID, clientID, invoiceNumber);
            switch (method)
            {
                case (int)CommissionMethods.GrossWages:
                    return (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "GROSS WAGES").Sum(x => x.ChargeTotal);
                case (int)CommissionMethods.ClientCharges:
                    return (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "ADMIN" && x.Commission == true).Sum(x => x.ChargeTotal);
                case (int)CommissionMethods.GrossMargin:
                    decimal grossWages = (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "GROSS WAGES").Sum(x => x.ChargeTotal - x.ChargeCost);
                    decimal futa = (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "FUTA").Sum(x => x.ChargeTotal - x.ChargeCost);
                    decimal suta = (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "SUTA").Sum(x => x.ChargeTotal - x.ChargeCost);
                    decimal workersComp = (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "WORKERS COMP").Sum(x => x.ChargeTotal - x.ChargeCost);
                    decimal benefits = (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "BENEFITS").Sum(x => x.ChargeTotal - x.ChargeCost);
                    decimal charges = (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "ADMIN" && x.Commission == true).Sum(x => x.ChargeTotal - x.ChargeCost);
                    return grossWages + futa + suta + workersComp + benefits + charges;
                case (int)CommissionMethods.TotalInvoice:
                    Invoice invoice = _invoiceProvider.FindInvoiceByInvoiceNumber(companyID, clientID, invoiceNumber);
                    return (decimal)invoice.Total;
                case (int)CommissionMethods.WCBilling:
                    return (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "WORKERS COMP").Sum(x => x.ChargeTotal);
                        //TODO: WC Billing By Code
                case (int)CommissionMethods.WCMargin:
                    return (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "WORKERS COMP").Sum(x => x.ChargeTotal - x.ChargeCost);
                        //TODO: WC Margin By Code
                case (int)CommissionMethods.BenefitsBilling:
                    return (decimal)invoicePayrollDetails.Where(x => x.ComponentType == "BENEFITS").Sum(x => x.ChargeTotal);
                        //TODO: Benefits Billing By Plan
                default:
                    return 0;
            }
        }

        public decimal GetCommissonTotal(int companyID, string clientID, string salespersonID, int method, decimal commissionable)
        {
            var commissionTiers = _commissionsProvider.GetListCommissionTiersBySalespersonID(companyID, clientID, salespersonID, method);
            decimal currentPercent = 0;
            foreach (ClientCommissionTier tier in commissionTiers)
            {
                if (tier.StartDate != null)
                {
                    DateTime startDate = (DateTime)tier.StartDate;
                    if (DateTime.Now > startDate.AddDays(tier.Days))
                    {
                        currentPercent = (decimal)tier.CommissionPercent;
                    }
                }
            }
            return commissionable * (currentPercent/100);
        }

        public void SaveListCommissionAmounts(List<CommissionAmount> commissionAmountsToSave)
        {
            _commissionsProvider.SaveListCommissionAmounts(commissionAmountsToSave);
        }

        public void RemoveCommissionAmount(int companyID, string clientID, int invoiceNumber, string salespersonID)
        {
            var commissionAmountRecord = _commissionAmountsProvider.GetCommissionAmount(companyID, clientID, invoiceNumber, salespersonID);
            _commissionAmountsProvider.DeleteCommissionAmount(commissionAmountRecord);
        }

        public void ProcessCommissionPayrollPayments(List<CommissionDTO> commissions)
        {
            List<CommissionDTO> payrollDestinationCommissions = commissions.Where(x => x.CommissionDestination == 1).ToList();
            var commissionsByCompanyID = payrollDestinationCommissions.GroupBy(x => x.CompanyID);
            foreach (IGrouping<int, CommissionDTO> company in commissionsByCompanyID)
            {
                int computerTRXNumber = _payrollBatchDetailProvider.GetLastTransactionNumber(company.First().CompanyID);
                var companiesByClientID = company.GroupBy(x => x.ClientID);
                foreach (IGrouping<string, CommissionDTO> client in companiesByClientID)
                {
                    int companyID = client.First().CompanyID;
                    string clientID = client.First().ClientID;
                    int batchIteration = _payrollBatchProvider.GetDailyCommissionBatchCount(companyID) + 1;
                    string iterationSuffix = batchIteration > 9 ? batchIteration.ToString() : "0" + batchIteration.ToString();
                    string date = DateTime.Now.ToString("MMddyyyy");
                    string batchNumber = $"{companyID}_COMM_{date}_{iterationSuffix}";

                    PayrollBatch batch = new PayrollBatch()
                    {
                        CompanyID = companyID,
                        ClientID = clientID,
                        BatchNumber = batchNumber,
                        UserID = GlobalVariables.DNETOwnerID,
                        BatchOrigin = 1,
                        BatchMarked = false,
                        Approval = false,
                    };
                    _payrollBatchProvider.CreatePayrollBatchRecord(batch);

                    DateTime? beginDate = GetEarliestInvoiceDate(client.ToList());
                    DateTime? endDate = GetLatestInvoiceDate(client.ToList());
                    List<PayrollBatchDetail> payrollBatchDetails = new List<PayrollBatchDetail>();
                    foreach (CommissionDTO invoice in client)
                    {
                        Salesperson salesperson = _salespersonsProvider.GetSalesperson(companyID, invoice.SalespersonID);
                        payrollBatchDetails.Add(new PayrollBatchDetail
                        {
                            #region Field Assignments

                            CompanyID = companyID,
                            BatchNumber = batchNumber,
                            EmployeeID = invoice.EmployeeID,
                            UPRTRXCode = salesperson.CommissionCode,
                            JBSID = null,
                            SalaryTRXType = null,
                            JobCostingMode = null,
                            JobCostingName = null,
                            JobLevel2 = null,
                            JobLevel3 = null,
                            JobLevel4 = null,
                            JobLevel5 = null,
                            JobLevel6 = null,
                            PayrollCode = null,
                            HoursWorked = invoice.HoursWorkedDefault,
                            LDLabel1 = null,
                            LDLabel2 = null,
                            LDLabel3 = null,
                            LDLabel4 = null,
                            LDLabel5 = null,
                            LDLabel6 = null,
                            LDLabel7 = null,
                            LDLabel8 = null,
                            LDValue1 = null,
                            LDValue2 = null,
                            LDValue3 = null,
                            LDValue4 = null,
                            LDValue5 = null,
                            LDValue6 = null,
                            LDValue7 = null,
                            LDValue8 = null,
                            FringeWage = null,
                            ComputerTRXType = null,
                            SalaryChange = null,
                            TRXBeginningDate = beginDate,
                            TRXEndingDate = endDate,
                            TRXHoursUnits = invoice.HoursWorkedDefault,
                            HourlyPayRate = null,
                            PayRateAmount = null,
                            VariableDedBenAmount = null,
                            VariableDedBenPercent = null,
                            DaysWorked = null,
                            WeeksWorked = null,
                            Department = null,
                            JobTitle = null,
                            StateCode = null,
                            LocalTax = null,
                            SutaState = null,
                            WorkersComp = null,
                            LastUser = null,
                            LastDateEdited = null,
                            TRXSource = null,
                            DocumentType = null,
                            Voided = false,
                            InAdditionToSalary = false,
                            ShiftCode = null,
                            ShiftPremium = null,
                            JobNumber = null,
                            UnionCode = null,
                            Receipts = null,

                            #endregion
                        });
                        _commissionAmountsProvider.CommissionAmountPaid(invoice.CompanyID, invoice.ClientID, invoice.InvoiceNumber, invoice.SalespersonID);
                    }
                    _payrollBatchDetailProvider.CreatePayrollBatchDetailsRecords(companyID, payrollBatchDetails);
                }
            }
        }

        public void ProcessCommissionVendorPayments(List<CommissionDTO> commissions)
        {
            List<CommissionDTO> vendorAPDestinationCommissions = commissions.Where(x => x.CommissionDestination == 2).ToList();

            //TODO: Create Vendor AP Batch records from vendorAPDestinationCommissions
        }

        public void ProcessCommissionOtherPayments(List<CommissionDTO> commissions)
        {
            _commissionAmountsProvider.PayByOther(commissions);
        }

        #endregion

        #region Private Methods

        private DateTime? GetEarliestInvoiceDate(List<CommissionDTO> invoices)
        {
            DateTime? result = null;
            foreach (CommissionDTO invoice in invoices)
            {
                string invoiceDate = invoice.InvoiceDate;
                if (invoiceDate.IsNotNullOrEmpty() && invoiceDate.StartsWith("/"))
                {
                    invoiceDate = invoiceDate.Replace("/Date(", "").Replace(")/", "");
                }
                if (DateTime.TryParse(invoiceDate, out DateTime date)){
                    if (result == null)
                    {
                        result = date;
                    }
                    else if (date < result)
                    {
                        result = date;
                    }
                }
            }
            return result;
        }

        private DateTime? GetLatestInvoiceDate(List<CommissionDTO> invoices)
        {
            DateTime? result = null;
            foreach (CommissionDTO invoice in invoices)
            {
                string invoiceDate = invoice.InvoiceDate;
                if (invoiceDate.IsNotNullOrEmpty() && invoiceDate.StartsWith("/"))
                {
                    invoiceDate = invoiceDate.Replace("/Date(", "").Replace(")/", "");
                }
                if (DateTime.TryParse(invoiceDate, out DateTime date)){
                    if (result == null)
                    {
                        result = date;
                    }
                    else if (date > result)
                    {
                        result = date;
                    }
                }
            }
            return result;
        }

        #endregion
    }
}