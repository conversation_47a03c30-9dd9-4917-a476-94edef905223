using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
﻿using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Providers.D2;
using DarwiNet2._0.ViewModels.D2.InvoiceReview;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Services.D2
{
    public class InvoicePayrollService : IInvoicePayrollService
    {
        #region Fields

        private readonly IInvoicePayrollProvider _invoicePayrollProvider;

        #endregion

        #region Constructors

        public InvoicePayrollService(IInvoicePayrollProvider invoicePayrollProvider)
        {
            _invoicePayrollProvider = invoicePayrollProvider ?? throw new ArgumentNullException(nameof(invoicePayrollProvider));
        }

        #endregion

        #region Public Methods

        public InvoiceMergeInvoicePayrollDTO GetInvoicePayrollDTO(int companyID, string clientID, int darwinInvoiceNumber)
        {
            return _invoicePayrollProvider.GetInvoicePayrollDTO(companyID, clientID, darwinInvoiceNumber);
        }
        
        public List<InvoicePayroll> GetInvoicePayrollsByPayrollNumber(string payrollNumber)
        {
            return _invoicePayrollProvider.GetInvoicePayrollsByPayrollNumber(payrollNumber);
        }

        public void SaveManualInvoicePayroll(InvoicePayroll invoicePayroll)
        {
            _invoicePayrollProvider.SaveManualInvoicePayroll(invoicePayroll);
        }

        public void UpdatePayrollStatus(int companyID, string clientID, string payrollNumber, int invoiceNumber, PayrollStatus status)
        {
            _invoicePayrollProvider.UpdateStatus(companyID, clientID, payrollNumber, invoiceNumber, status);
        }

        public List<string> GetAssociatedInvoicePayrollPayrollNumbers(int companyId, string clientId, int invoiceNum)
        {
            return _invoicePayrollProvider.GetAssociatedInvoicePayrollPayrollNumbers(companyId, clientId, invoiceNum);
        }

        public string GetPayrollNumber (int companyID, string clientID, int darwinInvoiceNumber)
        {
            return _invoicePayrollProvider.GetPayrollNumber(companyID, clientID, darwinInvoiceNumber);
        }

        public void DeleteManualInvoicePayroll(int companyId, string clientId, int darwinInvoiceNumber)
        {
            _invoicePayrollProvider.DeleteManualInvoicePayroll(companyId, clientId, darwinInvoiceNumber);
        }

        public List<int> GetDarwinInvoiceNumbersByPayroll(string payrollNumber)
        {
            return _invoicePayrollProvider.GetDarwinInvoiceNumbersByPayroll(payrollNumber);
        }

        public bool PayrollHasInvoicePayroll(string payrollNumber)
        {
            return _invoicePayrollProvider.PayrollHasInvoicePayroll(payrollNumber);
        }

        public int? GetInvoiceStatus(int companyId, string clientId, string divisionId, int darwinInvoiceNumber)
        {
            return _invoicePayrollProvider.GetInvoiceStatus(companyId, clientId, divisionId, darwinInvoiceNumber);
        }

        #endregion

        #region Public Functions

        #endregion
    }
}