using DarwiNet2._0.Controllers;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Interfaces.Services.D2.TimeSheet;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2.TimeSheet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Services.D2.TimeSheet
{
    public class TimeSheetDeleteService : ITimeSheetDeleteService
    {
        public TimeSheetDeleteService()
        {

        }

        public List<TimeSheetRowKeyDTO> DeleteTimeSheetRowData(int companyId, int timeSheetId, List<TimeSheetRowActionDTO> timeSheetRowData)
        {
            var timeSheetDeletes = ConvertToTimeSheetRows(timeSheetRowData.Where(r => r.ActionDTO.Action.Equals("DELETE")).Select(r => r.TimeSheetRow).ToList());
            return DeleteTimeSheetRows(companyId, timeSheetId, timeSheetDeletes);
        }

        private List<TimeSheetRowModel> ConvertToTimeSheetRows(List<TimeSheetRowDTO> timeSheetRowDTOs)
        {
            return timeSheetRowDTOs.Select(r => new TimeSheetRowModel()
            {
                RowID = r.RowID,
                EmployeeID = r.EmployeeID,
                Job = r.Job,
                Details = r.Details,
                IsVisible = r.IsVisible,
                TEConsolidated = r.IsConsolidated,
                OriginRow = r.OriginRow,
                SortValue = r.SortValue,
                TotalHours = r.TotalHours,
                TotalGross = r.TotalGross
            }).ToList();
        }

        private List<TimeSheetRowKeyDTO> DeleteTimeSheetRows(int companyId, int timeSheetId, List<TimeSheetRowModel> timeSheetDeletes)
        {
            if (timeSheetDeletes.IsNullOrEmpty())
                return null;

            return timeSheetDeletes.Select(r => new TimeSheetRowKeyDTO { TimeSheetID = timeSheetId, RowID = r.RowID }).ToList();
            //_timeSheetProvider.DeleteTimeSheetRowData(timeSheetId, deleteKeys);
        }
    }
}