using DarwiNet2._0.Controllers;
using DarwiNet2._0.Core;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Interfaces.Services.D2.TimeSheet;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2.TimeSheet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Services.D2.TimeSheet
{
    public class TimeSheetCreateService : ITimeSheetCreateService
    {
        private ITimeSheetFillService _timeSheetFillService;

        public TimeSheetCreateService(ITimeSheetFillService timeSheetFillService)
        {
            _timeSheetFillService = timeSheetFillService ?? throw new ArgumentNullException(nameof(timeSheetFillService));
        }

        public List<TimeSheetDetail> CreateTimeSheetRowData(int companyId, int timeSheetId, IEnumerable<TimeSheetRowActionDTO> timeSheetRowData, int currentRowId)
        {
            var timeSheetAdditions = ConvertToTimeSheetRows(timeSheetRowData.Where(r => r.ActionDTO.Action.Equals(Constants.TimeSheetRowActions.Add)).Select(r => r.TimeSheetRow).ToList());
            return AddTimeSheetRows(companyId, timeSheetId, timeSheetAdditions, currentRowId);
        }

        private List<ITimeSheetRow> ConvertToTimeSheetRows(IEnumerable<TimeSheetRowDTO> timeSheetRowDTOs)
        {
            return timeSheetRowDTOs.Select(r => new TimeSheetRowModel()
            {
                RowID = r.RowID,
                EmployeeID = r.EmployeeID,
                Job = r.Job,
                Details = r.Details,
                IsVisible = r.IsVisible,
                TEConsolidated = r.IsConsolidated,
                OriginRow = r.OriginRow,
                SortValue = r.SortValue,
                TotalHours = r.TotalHours,
                TotalGross = r.TotalGross
            }).ToList<ITimeSheetRow>();
        }

        private List<TimeSheetDetail> AddTimeSheetRows(int companyId, int timeSheetId, List<ITimeSheetRow> timeSheetAdditions, int startRowId)
        {
            _timeSheetFillService.FillEmployeeData(companyId, timeSheetId, timeSheetAdditions);
            return CreateTimeSheetDetails(timeSheetId, timeSheetAdditions, startRowId);
        }

        private List<TimeSheetDetail> CreateTimeSheetDetails(int timeSheetId, IEnumerable<ITimeSheetRow> timeSheetRows, int startRowId)
        {
            var timeSheetDetails = new List<TimeSheetDetail>();

            foreach (var row in timeSheetRows)
            {
                for (int i = 0; i < row.Details.Length; i++)
                {
                    timeSheetDetails.Add(new TimeSheetDetail()
                    {
                        TimeSheetID = timeSheetId,
                        RowID = startRowId,
                        ColID = i + 1,
                        CellValue = row.Details[i],
                        EmployeeID = row.EmployeeID,
                        OriginRow = row.OriginRow,
                        TEConsolidated = row.TEConsolidated
                    });
                }

                startRowId++;
            }

            return timeSheetDetails;
        }
    }
}