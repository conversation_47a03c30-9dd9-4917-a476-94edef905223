using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Providers.D2;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace DarwiNet2._0.Services.D2
{
    public class ClientService : IClientService
    {
        #region Fields

        private readonly IClientProvider _clientProvider;
        private readonly IAppConfig _appConfigService;
        private readonly IDarwinetSetupProvider _darwinetSetupProvider;
        private readonly IClientDivisionDetailProvider _clientDivisionDetailProvider;
        private readonly IEmployeeJobCostAssignmentProvider _employeeJobCostAssignmentProvider;
        private readonly IPayrollUserClientAccessService _payrollUserClientAccessService;
        private readonly IUserService _userService;
        private readonly IClientDivisionProvider _clientDivisionProvider;

        #endregion

        #region Constructors
        
        public ClientService()
        {
            _clientProvider = new ClientProvider();
            _clientDivisionDetailProvider = new ClientDivisionDetailProvider();
            _employeeJobCostAssignmentProvider = new EmployeeJobCostAssignmentProvider();
            _appConfigService = new AppConfig();
            _userService = new UserService();
            _payrollUserClientAccessService = new PayrollUserClientAccessService();
            _darwinetSetupProvider = new DarwinetSetupProvider();
            _clientDivisionProvider = new ClientDivisionProvider();
        }

        public ClientService(
            IClientProvider clientProvider, 
            IClientDivisionDetailProvider clientDivisionDetailProvider, 
            IEmployeeJobCostAssignmentProvider employeeJobCostAssignmentProvider, 
            IUserService userService, 
            IPayrollUserClientAccessService payrollUserClientAccessService,
            IAppConfig appConfigService,
            IClientDivisionProvider clientDivisionProvider)
        {
            _clientProvider = clientProvider ?? throw new ArgumentNullException(nameof(clientProvider));
            _clientDivisionDetailProvider = clientDivisionDetailProvider ?? throw new ArgumentNullException(nameof(clientDivisionDetailProvider));
            _employeeJobCostAssignmentProvider = employeeJobCostAssignmentProvider ?? throw new ArgumentNullException(nameof(employeeJobCostAssignmentProvider));
            _appConfigService = appConfigService ?? throw new ArgumentNullException(nameof(appConfigService));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _payrollUserClientAccessService = payrollUserClientAccessService ?? throw new ArgumentNullException(nameof(payrollUserClientAccessService));
            _clientDivisionProvider = clientDivisionProvider ?? throw new ArgumentNullException(nameof(clientDivisionProvider));
        }

        #endregion

        #region Public Functions

        /// <summary>
        ///     Gets a list of <see cref="Code_Description"/> containing client id's and names.
        /// </summary>
        /// <history>
        ///     [lyeager]   06/17/20    Task-5863: Created.  Replaces implementation of _clientProvider.GetClientAccess(companyId, userid).
        /// </history>
        public List<Code_Description> GetClientAccess(int companyId, string userId)
        {
            return GetClientsWithAccessByCompanyId(companyId, userId).Select(c => new Code_Description
            {
                Code = c.ClientID,
                Description = $"({c.ClientID}) {c.ClientName}"
            })
            .ToList();
        }

        public Client FindClientById(int companyId, string clientId)
        {
            var client = _clientProvider.GetClientById(companyId, clientId);

            if (client == null)
            {
                throw new ApplicationException("Unable to find client.");
            }

            return client;
        }

        public string GetClientNameById(int companyId, string clientId)
        {
            return _clientProvider.GetClientNameById(companyId, clientId);
        }

        public IEnumerable<Tuple<int, string, string>> GetClientNamesByIds(IEnumerable<int> companyIds, IEnumerable<string> clientIds)
        {
            return _clientProvider.GetClientNamesByIds(companyIds, clientIds);
        }

        public bool? IsUseCanPayroll(string userID) =>
           _clientProvider.IsUserCanPayroll(userID);


        public List<Client> GetClientsByCompanyId(int companyId,string userId)
        {
            var clients = _clientProvider.GetClientsByCompanyId(companyId, userId);

            return clients.ToList();
        }

        public List<Code_Description> GetClientDescriptionsByCompanyId(int companyId)
        {
            return _clientProvider.GetClientDescriptionsByCompanyId(companyId);
        }

        public List<Client> GetClientsWithAccessByCompanyId(int companyId, string userId)
        {
            var clients = GetClientsByCompanyId(companyId, userId);
            if (userId.ToLower() != _appConfigService.DefaultSystemUser.ToLower())
            {
                var setups = _darwinetSetupProvider.GetByCompanyId(companyId);
                clients.RemoveAll(c => setups.Where(s => s.AdminOnly).Select(s => s.ClientID).Any(s => s == c.ClientID));
                var user = _userService.GetUserInfoByUsername(userId);
                if (user.CanPayroll)
                {
                    var clientList = _payrollUserClientAccessService.GetClientAccessList(userId, companyId);
                    clients.RemoveAll(item => !clientList.Any(c => c == item.ClientID));
                }
            }
            return clients;
        }

        public List<string> GetClientIds(int companyId,string userId)
        {
            return _clientProvider.GetClientIds(companyId, userId);
        }

        public List<string> GetClientNames(int companyId)
        {
            return _clientProvider.GetClientNames(companyId);
        }

        public List<Client> GetClientsWithUserAccessByCompanyId(int companyID, string userID)
        {
            return _clientProvider.GetClientsByCompanyIDAvailableToUser(companyID, userID);
        }
        
        public List<ClientDivisionDetail> GetClientDivisionDetails(int companyId, string clientId) =>
            _clientDivisionDetailProvider.GetAllClientDivisionDetails(companyId, clientId);

        public string GetClientAddressCode(int companyId, string clientId) =>
            _clientProvider.GetClientAddressCode(companyId, clientId);

        public List<EmployeeJobCostAssignment> GetActiveEmployeeJobCostAssignments(int companyId, string clientId) =>
            _employeeJobCostAssignmentProvider.GetActiveEmployeeJobCostAssignments(companyId, clientId);

        public bool IsUseJBS(int companyId, string clientId) =>
            _clientProvider.IsUseJBS(companyId, clientId);

        public void UpdateContactPerson(int companyId, string clientId, string addressCode, string contactPerson)
        {
            _clientProvider.Update(companyId, clientId, addressCode, contactPerson);
        }

        public Code_Description GetClientShortNameById(int companyId, string clientId)
        {
            return _clientProvider.GetClientShortNameById(companyId, clientId);
        }

        public void UpdateNextEmployeeID(int companyId, string clientId, string nextEmployeeId)
        {
            _clientProvider.UpdateNextEmployeeID(companyId, clientId, nextEmployeeId);
        }

        public int? GetNewHireEnrollmentPeriod(int companyId, string clientId)
        {
            return _clientProvider.GetNewHireEnrollmentPeriod(companyId, clientId);
        }

        public List<Client> GetClientsById(int companyId, string clientId)
        {
            return _clientProvider.GetClientsById(companyId, clientId);
        }

        public List<Client> GetClients()
        {
            return _clientProvider.GetClients();
        }

        public List<ClientShort> GetClientsShortByCompanyId(int companyId)
        {
            return _clientProvider.GetClientsShortByCompanyId(companyId);
        }

        public List<Code_Description> GetClientNamesByCompanyId(int companyId)
        {
            return _clientProvider.GetClientNamesByCompanyId(companyId);
        }

        public List<Code_Description> GetClientWithDescriptionsByCompanyId(int companyId)
        {
            return _clientProvider.GetClientWithDescriptionsByCompanyId(companyId);
        }

        public Dictionary<string, List<string>> GetLaborDistributionCollection(int companyId, string clientId, IEnumerable<string> labelDescriptions) =>
            _clientProvider.GetLaborDistributionCollection(companyId, clientId, labelDescriptions);

        public List<ClientLaborDistributionValue> GetClientLaborDistributionValues(int companyId, string clientId) =>
            _clientProvider.GetClientLaborDistributionValues(companyId, clientId);

        public bool CanAccessASOCashRequirementsReport(string clientId)
        {
            return _clientProvider.CanAccessASOCashRequirementsReport(clientId);
        }

        public List<MultiselectOptionsDTO> GetClientsByCompanyIdsAndAccess(List<int> companyIds, string userId)
        {
            return _clientProvider.GetClientsByCompanyIdsAndAccess(companyIds, userId);
        }

        public List<CompanyIDMultiselectOptionsDTO> GetClientsByCompanyIdsAndAccessWithCompanyCode(List<int> companyIds, string userId)
        {
            return _clientProvider.GetClientsByCompanyIdsAndAccessWithCompanyCode(companyIds, userId);
        }

        public IEnumerable<MultiselectOptionsDTO> GetClientDivisions(List<int> companyIds, List<string> clientIds)
        {
            return _clientDivisionProvider.GetClientDivisions(companyIds, clientIds);
        }

        public bool IsTimeImportModelExists()
        {
            return _clientProvider.IsTimeImportModelExists();
        }

        #endregion
    }
}