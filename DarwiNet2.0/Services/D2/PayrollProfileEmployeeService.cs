using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using DarwiNet2._0.Providers.D2;

namespace DarwiNet2._0.Services.D2
{
    public class PayrollProfileEmployeeService : IPayrollProfileEmployeeService
    {
        #region Fields

        private readonly IPayrollProfileEmployeeProvider _payrollProfileEmployeeProvider;
        private readonly IPayrollProfileEmployeeCriteriaProvider _payrollProfileEmployeeCriteriaProvider;
        private readonly IPayrollProfileSettingProvider _payrollProfileSettingProvider;
        private readonly IEmployeeProvider _employeeProvider;

        #endregion

        #region Constructors

        public PayrollProfileEmployeeService()
        {
            _payrollProfileEmployeeProvider = new PayrollProfileEmployeeProvider();
            _payrollProfileEmployeeCriteriaProvider = new PayrollProfileEmployeeCriteriaProvider();
            _payrollProfileSettingProvider = new PayrollProfileSettingsProvider();
            _employeeProvider = new EmployeeProvider();
        }

        public PayrollProfileEmployeeService(IPayrollProfileEmployeeProvider payrollProfileEmployeeProvider,
                                             IPayrollProfileEmployeeCriteriaProvider payrollProfileEmployeeCriteriaProvider,
                                             IPayrollProfileSettingProvider payrollProfileSettingProvider,
                                             IEmployeeProvider employeeProvider)
        {
            _payrollProfileEmployeeProvider = payrollProfileEmployeeProvider ?? throw new ArgumentNullException(nameof(payrollProfileEmployeeProvider));
            _payrollProfileEmployeeCriteriaProvider = payrollProfileEmployeeCriteriaProvider ?? throw new ArgumentNullException(nameof(payrollProfileEmployeeCriteriaProvider));
            _payrollProfileSettingProvider = payrollProfileSettingProvider ?? throw new ArgumentNullException(nameof(payrollProfileSettingProvider));
            _employeeProvider = employeeProvider ?? throw new ArgumentNullException(nameof(employeeProvider));
        }

        #endregion

        #region Public Functions

        public List<PayrollProfileEmployee> List(int companyId, string clientId, string profileId)
        {
            return _payrollProfileEmployeeProvider.List(companyId, clientId, profileId);
        }

        public List<string> ListCodes(int companyId, string clientId, string profileId)
        {
            List<PayrollProfileEmployee> payrollProfileEmployees = List(companyId, clientId, profileId);

            return payrollProfileEmployees
                .Select(p => p.EmployeeID)
                .ToList();
        }

        /// <summary>
        ///     
        /// </summary>
        /// <history>
        ///     [mframe]    07/14/20    Task-6345: Created history.
        ///     [lyeager]   10/29/20    Task-7151: Implemented manuallyAdded and ignoreManuallyAdded fields.
        /// </history>
        public void Update(int companyId, string clientId, string profileId, IEnumerable<string> ids, bool manuallyAdded, bool? ignoreManuallyAdded = null)
        {
            if (ignoreManuallyAdded == null)
            {
                ignoreManuallyAdded = _payrollProfileSettingProvider.IgnoreManuallyAddedEmployees(companyId, clientId, profileId);
            }

            _payrollProfileEmployeeProvider.Update(companyId, clientId, profileId, ids, manuallyAdded, (bool)ignoreManuallyAdded);
        }

        public void FilterEmployeeCriteria(int companyId, string clientId, string profileId, string employeeWorkStatus, bool? ignoreManuallyAdded)
        {
            List<PayrollProfileEmployeeCriteria> employeeCriteria = _payrollProfileEmployeeCriteriaProvider.ListCriteria(companyId, clientId, profileId);
            IEnumerable<string> employees = _employeeProvider.FilterEmployees(companyId, clientId, employeeWorkStatus, employeeCriteria);

            Update(companyId, clientId, profileId, employees, false, ignoreManuallyAdded);
        }

        public bool HasSelectedEmployeesOusideOfProfile(int companyId, string clientId, string profileID)
        {
            var payrollProfileSetting = _payrollProfileSettingProvider.GetPayrollProfileSettings(companyId, clientId, profileID);
            var payrollProfileEmployeeCriteria = _payrollProfileEmployeeCriteriaProvider.ListCriteria(companyId, clientId, profileID);

            return _payrollProfileEmployeeProvider.HasSelectedEmployeesOusideOfProfile(companyId, clientId, profileID, payrollProfileSetting.EmployeeWorkStatus, payrollProfileEmployeeCriteria);
        }

        #endregion
    }
}