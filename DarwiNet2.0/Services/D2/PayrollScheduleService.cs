using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Form.D2.PayrollSchedule;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Providers.D2;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Mvc;
using System.Web.Mvc.Html;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Services.D2
{
    public class PayrollScheduleService : IPayrollScheduleService
    {
        private readonly IClientPayrollScheduleProvider _clientPayrollScheduleProvider;
        private readonly CreatePayrollScheduleForm _createPayrollScheduleForm;
        private readonly DnetEntities _dbContext;
        private readonly EditPayrollScheduleForm _editPayrollScheduleForm;
        private readonly IInvoicePayrollProvider _invoicePayrollProvider;
        private readonly UpdateFuturePayrollScheduleForm _updateFuturePayrollScheduleForm;
        private readonly IPayrollWorkMasterProvider _payrollWorkMasterProvider;

        public PayrollScheduleService(
            DnetEntities dbContext,
            IClientPayrollScheduleProvider clientPayrollScheduleProvider,
            IInvoicePayrollProvider invoicePayrollProvider,
            CreatePayrollScheduleForm createPayrollScheduleForm,
            EditPayrollScheduleForm editPayrollScheduleForm,
            UpdateFuturePayrollScheduleForm updateFuturePayrollScheduleForm,
            IPayrollWorkMasterProvider workMasterProvider)
        {
            _dbContext = dbContext;
            _clientPayrollScheduleProvider = clientPayrollScheduleProvider;
            _invoicePayrollProvider = invoicePayrollProvider;
            _createPayrollScheduleForm = createPayrollScheduleForm;
            _editPayrollScheduleForm = editPayrollScheduleForm;
            _updateFuturePayrollScheduleForm = updateFuturePayrollScheduleForm;
            _payrollWorkMasterProvider = workMasterProvider;
        }

        public bool CreateSchedule(ClientPayrollSchedule schedule)
        {
            var payrollProfile = _dbContext.PayrollProfileSettings.Where(c => c.CompanyID == schedule.CompanyID && c.ClientID == schedule.ClientID && c.ProfileID == schedule.ProfileID).FirstOrDefault();
            if (payrollProfile == null)
                return false;
            schedule.Schedule_Status = Convert.ToInt16(PayrollStatus.Available);
            schedule.UseClientCheckbookInfo = payrollProfile.UseClientCheckbookInfo;
            //schedule.Division_ID = payrollProfile.DivisionID ?? "001", //nullable issue will be resolved later
            _clientPayrollScheduleProvider.CreateManualSchedules(schedule.CompanyID, schedule.ClientID, schedule.ProfileID, schedule.Schedule_ID, new List<ClientPayrollSchedule>() { schedule });
            return true;
        }

        public bool CreateSchedule(PayrollSchedules schedules, out ModelStateDictionary errors)
        {
            _createPayrollScheduleForm.Initialize(schedules);
            _createPayrollScheduleForm.Validate(out errors);

            return _createPayrollScheduleForm.IsValid &&
                    _createPayrollScheduleForm.Save();
        }

        public int Delete(ClientPayrollSchedule schedule)
        {
            if (schedule == null)
                return 0;

            return Delete(new List<ClientPayrollSchedule>(1) { schedule });
        }

        public int Delete(IEnumerable<ClientPayrollSchedule> schedules)
        {
            if (schedules.IsNullOrEmpty())
                return 0;

            RemoveSelectedPayrollBatches(schedules);

            int result = 0;
            foreach (ClientPayrollSchedule schedule in schedules)
            {
                result += _clientPayrollScheduleProvider.DeleteScheduleByPayrollNumber(schedule);
            }

            SetNextPendingScheduleToAvailable(schedules.First());

            return result;
        }

        public ClientPayrollSchedule FindSchedule(string payrollNumber)
        {
            return _dbContext.ClientPayrollSchedules
                .Where(x => x.PayrollNumber == payrollNumber)
                .FirstOrDefault();
        }

        public List<ClientPayrollSchedule> FindSchedules(int companyId, string clientId, string profileId, string scheduleId = null)
        {
            Expression<Func<ClientPayrollSchedule, bool>> whereFilter;
            if (string.IsNullOrWhiteSpace(scheduleId))
                whereFilter = (x) => x.CompanyID == companyId && x.ClientID == clientId && x.ProfileID == profileId && !x.Deleted;
            else
                whereFilter = (x) => x.CompanyID == companyId && x.ClientID == clientId && x.ProfileID == profileId && x.Schedule_ID == scheduleId && !x.Deleted;
            return _dbContext.ClientPayrollSchedules.Where(whereFilter).ToList();
        }

        public PayrollSchedule FindPayrollSchedule(string payrollNumber)
        {
            ClientPayrollSchedule schedule = _dbContext.ClientPayrollSchedules.FirstOrDefault(x => x.PayrollNumber == payrollNumber);
            if (schedule == null)
                throw new ApplicationException($"Unable to find payroll {payrollNumber}");

            PayrollProfileSetting payrollProfileSettings = _dbContext.PayrollProfileSettings.FirstOrDefault(c => c.CompanyID == schedule.CompanyID && c.ClientID == schedule.ClientID && c.ProfileID == schedule.ProfileID);
            if (payrollProfileSettings == null)
                throw new ArgumentNullException("Unable to find [dbo].[PayrollProfileSettings] record");

            return new PayrollSchedule(schedule, payrollProfileSettings);
        }

        public ClientPayrollSchedule GetClientPayrollSchedule(int companyId, string clientId, string profileId, string userId, string scheduleId, int scheduleKey) =>
            (from urca in _dbContext.UserRoleClientAccesses
             join cps in _dbContext.ClientPayrollSchedules on new { urca.CompanyID, urca.ClientID } equals new { cps.CompanyID, cps.ClientID }
             where urca.CompanyID == companyId && urca.ClientID == clientId && urca.UserID == userId && cps.ProfileID == profileId && cps.Schedule_ID == scheduleId && cps.Schedule_Key == scheduleKey
             select cps)
            .FirstOrDefault();

        public IEnumerable<string> GetClientPayrollScheduleIds(int companyId, string clientId, string profileId) =>
            (from urca in _dbContext.UserRoleClientAccesses
             join cps in _dbContext.ClientPayrollSchedules on new { urca.CompanyID, urca.ClientID } equals new { cps.CompanyID, cps.ClientID }
             where urca.CompanyID == companyId && urca.ClientID == clientId && cps.ProfileID == profileId
             select cps.Schedule_ID)
            .Distinct()
            .ToHashSet();

        public void DeleteScheduleByPayrollNumber(ClientPayrollSchedule cps)
        {
            _clientPayrollScheduleProvider.DeleteScheduleByPayrollNumber(cps);
        }

        public TableQueryInfo<PayrollScheduleModel> GetFilteredPayrollSchedules(int companyId, string clientId, string profileId, Expression<Func<PayrollScheduleModel, bool>> filterClause)
        {
            return _clientPayrollScheduleProvider.GetFilteredPayrollSchedules(companyId, clientId, profileId, filterClause);
        }

        public ClientPayrollSchedule GetFirstPendingSchedule(int companyId, string clientId, string profileId, string scheduleId) =>
            _clientPayrollScheduleProvider.GetFirstPendingSchedule(companyId, clientId, profileId, scheduleId);

        public PayrollStatus GetNextPayrollScheduleInvoiceStatus(IDictionary<string, bool> payrollScheduleAutoPayOptions)
        {
            bool isInvoiceApprovalRequired = payrollScheduleAutoPayOptions[Constants.AutoPayOptions.INVOICE_APPROVAL_REQUIRED];
            bool isAutoProcessFinalize = payrollScheduleAutoPayOptions[Constants.AutoPayOptions.AUTO_PROCESS_FINALIZE];

            PayrollStatus status = PayrollStatus.InvoiceCompleted;
            if (isInvoiceApprovalRequired)
            {
                //status = PayrollStatus.InvoicePending;
            }
            else if (isAutoProcessFinalize)
            {
                //status = PayrollStatus.Finalized;
            }

            return status;
        }

        public int[] GetNumberOfYearsArray() =>
            new int[] { 1, 2, 3, 4, 5 };

        public IList<SelectListItem> GetPayFrequenciesAsSelectList() =>
            EnumHelper.GetSelectList(typeof(PaySchedulePayPeriods));

        public TableQueryInfo<ClientPayrollSchedule> GetSchedules(Expression<Func<ClientPayrollSchedule, bool>> filterClause, bool excludeFinalized, string UserID, int companyId)
        {
            return _clientPayrollScheduleProvider.GetSchedules(filterClause, excludeFinalized, UserID, companyId);
        }

        public void InvoiceComplete(string payrollNumber, int companyID, string clientID, string profileID, int darwinInvoiceNumber)
        {
            _invoicePayrollProvider.UpdateStatus(companyID, clientID, payrollNumber, darwinInvoiceNumber, PayrollStatus.InvoiceCompleted);
            var invoicePayroll = _dbContext.InvoicePayrolls.Where(x => x.PayrollNumber == payrollNumber).First();
            if (invoicePayroll == null) return;
            var payrollWorkMaster = _payrollWorkMasterProvider.GetPayrollWorkMaster(payrollNumber);
            string userId = payrollWorkMaster != null ? payrollWorkMaster.UserID : GlobalVariables.DNETOwnerID ?? Thinkware.Pay360.CohesionDefaults.UserId;
            invoicePayroll.InvoiceProcessCompletedBy = userId;
            invoicePayroll.InvoiceProcessCompletedDate = DateTime.Now;
            _dbContext.SaveChanges();

            bool allInvoicesComplete = !_invoicePayrollProvider.GetInvoicePayrollsByPayrollNumber(payrollNumber).Any(x => x.Status < 3031);
            if (allInvoicesComplete)
            {
                var payrollSchedule = FindPayrollSchedule(payrollNumber);
                PayrollStatus status = GetNextPayrollScheduleInvoiceStatus(payrollSchedule.AutoPayOptions);
                UpdatePayrollStatus(payrollNumber, status);
            }
        }

        public void SetInactive(string payrollNumber, bool inactive)
        {
            _clientPayrollScheduleProvider.SetInactive(payrollNumber, inactive);
        }

        public bool UpdateFutureSchedule(PayrollSchedules schedule, out ModelStateDictionary errors)
        {
            _updateFuturePayrollScheduleForm.Initialize(schedule);
            _updateFuturePayrollScheduleForm.Validate(out errors);

            return _updateFuturePayrollScheduleForm.IsValid &&
                    _updateFuturePayrollScheduleForm.Save();
        }

        public void UpdateOffCyclePayroll(int companyId, string payrollNumber, bool isOffCyclePayroll)
        {
            _clientPayrollScheduleProvider.UpdateOffCyclePayroll(companyId, payrollNumber, isOffCyclePayroll);
        }

        public void UpdatePayrollScheduleDates(int companyId, string payrollNumber, string checkDate, string debitDate, string ppBeginDate, string ppEndDate, string processDate)
        {
            _clientPayrollScheduleProvider.UpdatePayrollScheduleDates(companyId, payrollNumber, checkDate, debitDate, ppBeginDate, ppEndDate, processDate);
        }

        public void UpdatePayrollStatus(string payrollNumber, PayrollStatus status)
        {
            _clientPayrollScheduleProvider.UpdateStatus(payrollNumber, status);
        }

        public void UpdatePriorityPayroll(int companyId, string payrollNumber, bool priority)
        {
            _clientPayrollScheduleProvider.UpdatePriorityPayroll(companyId, payrollNumber, priority);
        }

        public bool UpdateSchedule(EditPayrollSchedule schedule, out ModelStateDictionary errors)
        {
            _editPayrollScheduleForm.Initialize(schedule);
            _editPayrollScheduleForm.Validate(out errors);

            return _editPayrollScheduleForm.IsValid &&
                    _editPayrollScheduleForm.Save();
        }

        public byte GetManualCheckType(string payrollNumber)
        {
            return _clientPayrollScheduleProvider.GetManualCheckType(payrollNumber);
        }

        private List<PayrollBatch> RemoveSelectedPayrollBatches(IEnumerable<ClientPayrollSchedule> schedules)
        {
            var payrollNumbers = schedules.Select(x => x.PayrollNumber).ToList();
            var batches = _dbContext.PayrollBatches.Where(x => payrollNumbers.Contains(x.PayrollNumber)).ToList();
            if (!batches.Any())
                return batches;

            foreach (var batch in batches)
                batch.PayrollNumber = null;

            _dbContext.SaveChanges();
            return batches;
        }

        public Dictionary<string, ShippingMethod> GetShippingMethods(int companyId)
        {
            var shippingMethods = (from sm in _dbContext.ShippingMethods
                                   where sm.CompanyID == companyId
                                   select sm)
                                   .ToDictionary(sm => sm.ShippingMethod1, sm => sm);

            shippingMethods.Add("N/A", new ShippingMethod { ShippingMethod1 = "N/A", Description = "N/A" });

            return shippingMethods;
        }

        private ClientPayrollSchedule SetNextPendingScheduleToAvailable(ClientPayrollSchedule schedule)
        {
            var statusPending = (short)PayrollStatus.Pending;
            var statusAvailable = (short)PayrollStatus.Available;

            bool hasExistingAvailable = _dbContext.ClientPayrollSchedules
                .Where(x => x.CompanyID == schedule.CompanyID && x.ClientID == schedule.ClientID && x.ProfileID == schedule.ProfileID && x.Schedule_ID == schedule.Schedule_ID && !(bool)x.Deleted && x.Schedule_Status == statusAvailable)
                .Any();
            if (hasExistingAvailable) 
                return null;

            var nextSchedule = _dbContext.ClientPayrollSchedules
                .Where(x => x.CompanyID == schedule.CompanyID && x.ClientID == schedule.ClientID && x.ProfileID == schedule.ProfileID && x.Schedule_ID == schedule.Schedule_ID && !(bool)x.Deleted && x.Schedule_Status == statusPending)
                .OrderBy(x => x.Process_Date)
                .FirstOrDefault();
            if (nextSchedule == null)
                return null;

            new UpdatePayrollStatusHandler(_dbContext)
                .Handle(PayrollNumber.Parse(schedule.PayrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.Available));
            return nextSchedule;
        }

        public void SetAvailablePayrollIfNeeded(int companyId, string clientId, string profileId, string scheduleId)
        {
            // Check for any schedules that are set to a status not 1001 and less than 4021
            // if none exist, we need to set the next closest check date schedule from pending to available
            var schedules = _dbContext.ClientPayrollSchedules
                .Where(x => x.CompanyID == companyId &&
                            x.ClientID == clientId &&
                            x.ProfileID == profileId &&
                            x.Schedule_ID == scheduleId &&
                            x.Inactive == false &&
                            x.Deleted == false);

            // If there are any schedules that are not pending and are < Finalized, then we don't need to set
            // a payroll to available
            var hasAvailableSchedules = schedules
                .Where(x => x.Schedule_Status != (int)PayrollStatus.Pending &&
                            x.Schedule_Status < (int)PayrollStatus.Finalized).Any();

            if (!hasAvailableSchedules)
            {
                // Check for pending payrolls
                var pendingSchedles = schedules
                    .Where(x => x.Schedule_Status == (int)PayrollStatus.Pending);

                // If there are pending schedules, get the who has a checkDate closest to right now
                // and set it to available
                if (pendingSchedles.Any())
                {
                    var now = DateTime.Now;
                    var closestCheckDateSchedule = pendingSchedles
                        .Where(x => x.CheckDate.HasValue)
                        .ToList()
                        .OrderBy(x => x.CheckDate.Value)
                        .FirstOrDefault();

                    if (closestCheckDateSchedule != null)
                    {
                        closestCheckDateSchedule.Schedule_Status = (int)PayrollStatus.Available;
                        _dbContext.SaveChanges();
                    }
                }
            }
        }
    }
}