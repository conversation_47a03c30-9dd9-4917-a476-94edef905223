using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Models.API;
using DarwiNet2._0.Providers.D2;
using DataDrivenViewEngine.Models.Core;
using DocumentFormat.OpenXml.Wordprocessing;
using EO.Pdf.Internal;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Services.D2
{
    public class DealAthleteTaxPaymentsService : IDealAthleteTaxPaymentsService
    {
        #region Fields
        private readonly IDealAthleteTaxPaymentsProvider _dealAthleteTaxPaymentsProvider;
        private readonly IEmployeeProvider _employeeProvider;
        #endregion

        #region Contructors
        public DealAthleteTaxPaymentsService(DnetEntities dbContext, IDealAthleteTaxPaymentsProvider dealAthleteTaxPaymentsProvider, IEmployeeProvider employeeProvider)
        {
            _dealAthleteTaxPaymentsProvider = dealAthleteTaxPaymentsProvider ?? throw new ArgumentNullException(nameof(dealAthleteTaxPaymentsProvider));
            _employeeProvider = employeeProvider ?? throw new ArgumentNullException(nameof(employeeProvider));
        }
        #endregion

        #region Public Methods
        public DealAthleteTaxPaymentServiceResponse AddDealAthleteTaxPayment(DealAthleteTaxPaymentDTO dealAthleteTaxPaymentDto)
        {
            var dealAthleteTaxPayment = Convert(dealAthleteTaxPaymentDto);
            dealAthleteTaxPayment.TaxPaymentID = _dealAthleteTaxPaymentsProvider.GenerateTaxPaymentID(dealAthleteTaxPaymentDto.CompanyID.Value, dealAthleteTaxPaymentDto.ClientID, dealAthleteTaxPaymentDto.EmployeeID);

            var errors = Validate(dealAthleteTaxPayment);
            if (errors.Any()) return new DealAthleteTaxPaymentServiceResponse { dealAthleteTaxPayment = dealAthleteTaxPaymentDto, errors = errors };

            _dealAthleteTaxPaymentsProvider.AddDealAthleteTaxPayment(dealAthleteTaxPayment);
            dealAthleteTaxPaymentDto = Convert(dealAthleteTaxPayment);
            return new DealAthleteTaxPaymentServiceResponse { dealAthleteTaxPayment = dealAthleteTaxPaymentDto, errors = errors };
        }

        public DealAthleteTaxPaymentServiceResponse UpdateDealAthleteTaxPayment(DealAthleteTaxPaymentDTO dealAthleteTaxPaymentDto)
        {
            var dealAthleteTaxPayment = Convert(dealAthleteTaxPaymentDto);

            var errors = Validate(dealAthleteTaxPayment);
            if (errors.Any()) return new DealAthleteTaxPaymentServiceResponse { dealAthleteTaxPayment = dealAthleteTaxPaymentDto, errors = errors };

            _dealAthleteTaxPaymentsProvider.UpdateDealAthleteTaxPayment(dealAthleteTaxPayment);
            return new DealAthleteTaxPaymentServiceResponse { dealAthleteTaxPayment = dealAthleteTaxPaymentDto, errors = errors };
        }

        public void DeleteDealAthleteTaxPayment(DealAthleteTaxPaymentDTO dealAthleteTaxPaymentDto)
        {
            _dealAthleteTaxPaymentsProvider.DeleteDealAthleteTaxPayment(Convert(dealAthleteTaxPaymentDto));
        }

        public DealAthleteTaxPaymentDTO GetDealAthleteTaxPayment(int companyID, string clientID, string employeeID, long taxPaymentID)
        {
            var dealAthleteTaxPayment = _dealAthleteTaxPaymentsProvider.GetDealAthleteTaxPayment(companyID, clientID, employeeID, taxPaymentID);
            return Convert(dealAthleteTaxPayment);
        }

        public List<DealAthleteTaxPaymentDTO> GetDealAthleteTaxPayments(int companyID, string clientID, string employeeID)
        {
            return _dealAthleteTaxPaymentsProvider.GetDealAthleteTaxPayments(companyID, clientID, employeeID);
        }

        public Employee GetEmployee(int companyID, string clientID, string employeeID)
        {
            return _employeeProvider.GetEmployee(companyID, clientID, employeeID);
        }

        public List<Code_Description> GetStates(int companyID)
        {
            return _dealAthleteTaxPaymentsProvider.GetStates(companyID);
        }

        public TableQueryInfo<TaxMonitorTableRowDTO> GetTaxMonitorTableData(int companyID, string clientID, TableFilter filters)
        {
            return _dealAthleteTaxPaymentsProvider.GetTaxMonitorTableData(companyID, clientID, filters);
        }

        public TableQueryInfo<DealAthleteTaxPaymentDTO> GetDealAthleteTaxPaymentsTableData(int companyID, string clientID, string employeeID, TableFilter filters)
        {
            return _dealAthleteTaxPaymentsProvider.GetDealAthleteTaxPaymentsTableData(companyID, clientID, employeeID, filters);
        }
        #endregion

        #region Private Methods
        private static DealAthleteTaxPayment Convert(DealAthleteTaxPaymentDTO dealAthleteTaxPaymentDto)
        {
            return new DealAthleteTaxPayment()
            {
                CompanyID = dealAthleteTaxPaymentDto.CompanyID != null ? dealAthleteTaxPaymentDto.CompanyID.Value : 0,
                ClientID = dealAthleteTaxPaymentDto.ClientID,
                EmployeeID = dealAthleteTaxPaymentDto.EmployeeID,
                TaxPaymentID = dealAthleteTaxPaymentDto.TaxPaymentID != null ? dealAthleteTaxPaymentDto.TaxPaymentID.Value : 0,
                PaymentAmount = dealAthleteTaxPaymentDto.PaymentAmount != null ? dealAthleteTaxPaymentDto.PaymentAmount.Value : 0,
                PaymentDate = dealAthleteTaxPaymentDto.PaymentDate,
                TaxType = dealAthleteTaxPaymentDto.TaxType != null ? dealAthleteTaxPaymentDto.TaxType.Value : (short?)null,
                TaxEntity = dealAthleteTaxPaymentDto.TaxEntity,
                TaxYear = dealAthleteTaxPaymentDto.TaxYear,
                TaxPeriod = dealAthleteTaxPaymentDto.TaxPeriod
            };
        }

        private static DealAthleteTaxPaymentDTO Convert(DealAthleteTaxPayment dealAthleteTaxPayment)
        {
            return new DealAthleteTaxPaymentDTO()
            {
                CompanyID = dealAthleteTaxPayment.CompanyID,
                ClientID = dealAthleteTaxPayment.ClientID,
                EmployeeID = dealAthleteTaxPayment.EmployeeID,
                TaxPaymentID = dealAthleteTaxPayment.TaxPaymentID,
                PaymentAmount = dealAthleteTaxPayment.PaymentAmount,
                PaymentDate = dealAthleteTaxPayment.PaymentDate,
                TaxType = dealAthleteTaxPayment.TaxType.Value,
                TaxEntity = dealAthleteTaxPayment.TaxEntity,
                TaxYear = dealAthleteTaxPayment.TaxYear,
                TaxPeriod = dealAthleteTaxPayment.TaxPeriod
            };
        }

        private static Dictionary<string, string> Validate(DealAthleteTaxPayment dealAthleteTaxPayment)
        {
            var errors = new Dictionary<string, string>();
            if (dealAthleteTaxPayment.CompanyID < 1) errors.Add("companyID", "Company ID is required.");
            if (string.IsNullOrWhiteSpace(dealAthleteTaxPayment.ClientID)) errors.Add("clientID", "Client ID is required.");
            if (string.IsNullOrWhiteSpace(dealAthleteTaxPayment.EmployeeID)) errors.Add("employeeID", "Employee ID is required.");
            if (dealAthleteTaxPayment.TaxPaymentID < 1) errors.Add("taxPaymentID", "Tax Payment ID is required.");
            if (dealAthleteTaxPayment.PaymentAmount < 1) errors.Add("paymentAmount", "Payment Amount is required.");
            if (dealAthleteTaxPayment.PaymentDate == null) errors.Add("paymentDate", "Payment Date is required.");
            if (dealAthleteTaxPayment.TaxType == null) errors.Add("taxType", "Tax Type is required.");
            if (dealAthleteTaxPayment.TaxType == (short)TaxType.State && string.IsNullOrWhiteSpace(dealAthleteTaxPayment.TaxEntity)) errors.Add("taxEntity", "State is required.");
            if (dealAthleteTaxPayment.TaxType == (short)TaxType.Local && string.IsNullOrWhiteSpace(dealAthleteTaxPayment.TaxEntity)) errors.Add("taxEntity", "Local Tax Code is required.");
            if (string.IsNullOrWhiteSpace(dealAthleteTaxPayment.TaxYear)) errors.Add("taxYear", "Tax Year is required.");
            return errors;
        }
        #endregion
    }
}