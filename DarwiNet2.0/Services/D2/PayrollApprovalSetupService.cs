using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;

namespace DarwiNet2._0.Services.D2
{
    public class PayrollApprovalSetupService : IPayrollApprovalSetupService
    {
        #region Fields

        private readonly IPayrollApprovalSetupProvider _payrollApprovalSetupProvider;
        private readonly IPayrollApprovalRecipientProvider _payrollApprovalRecipientProvider;
        private readonly ISmartTagProvider _smartTagProvider;
        private readonly IPayrollNotificationGroupProvider _payrollNotificationGroupProvider;
        private readonly IUserProvider _userProvider;

        #endregion

        #region Constructors

        public PayrollApprovalSetupService(IPayrollApprovalSetupProvider payrollApprovalSetupProvider,
                                           IPayrollApprovalRecipientProvider payrollApprovalRecipientProvider,
                                           ISmartTagProvider smartTagProvider,
                                           IPayrollNotificationGroupProvider payrollNotificationGroupProvider,
                                           IUserProvider userProvider)
        {
            _payrollApprovalSetupProvider = payrollApprovalSetupProvider ?? throw new ArgumentNullException(nameof(payrollApprovalSetupProvider));
            _payrollApprovalRecipientProvider = payrollApprovalRecipientProvider ?? throw new ArgumentNullException(nameof(payrollApprovalRecipientProvider));
            _smartTagProvider = smartTagProvider ?? throw new ArgumentNullException(nameof(smartTagProvider));
            _payrollNotificationGroupProvider = payrollNotificationGroupProvider ?? throw new ArgumentNullException(nameof(payrollNotificationGroupProvider));
            _userProvider = userProvider ?? throw new ArgumentNullException(nameof(userProvider));
        }

        #endregion

        public List<UserModel> GetInternalNotificationUsers(int companyID, string clientID)
        {
            return _userProvider.GetInternalNotificationUsers(companyID, clientID);
        }

        public List<UserModel> GetExternalNotificationUsers(int companyId, string clientId)
        {
            return _userProvider.GetExternalNotificationUsers(companyId, clientId);
        }

        public PayrollApprovalSetupsModel Update(PayrollApprovalSetupsModel payrollApprovalSetups)
        {
            int companyId = payrollApprovalSetups.CompanyID;
            string clientId = payrollApprovalSetups.ClientID;
            string profileId = payrollApprovalSetups.ProfileID;
            string approvalType = payrollApprovalSetups.ApprovalType;

            foreach (var setup in payrollApprovalSetups.PayrollApprovalSetups)
            {
                setup.CompanyID = companyId;
                setup.ClientID = clientId;
                setup.ProfileID = profileId;
                setup.ApprovalType = approvalType;

                if (setup.PayrollApprovalSetupID > 0)
                {
                    var payrollApprovalSetup = _payrollApprovalSetupProvider.Update(setup);

                    // update Recipients
                    _payrollApprovalRecipientProvider.UpdateRecipients(setup, payrollApprovalSetup.PayrollApprovalRecipients.ToList());
                }
                else
                {
                    var payrollApprovalSetup = _payrollApprovalSetupProvider.Create(setup);
                    setup.PayrollApprovalSetupID = payrollApprovalSetup.PayrollApprovalSetupID;

                    if (setup.InternalApproval && setup.ApprovalModel == "GroupSequential" && setup.PayrollApprovalTeams.Any())
                    {
                        _payrollApprovalRecipientProvider.AddTeamRecipients(setup);
                    }
                    else
                    {
                        // recipient groups
                        if (setup.PayrollApprovalRecipientGroups != null && setup.PayrollApprovalRecipientGroups.Any())
                        {
                            _payrollApprovalRecipientProvider.AddGroupRecipients(setup);
                        }

                        // recipients
                        if (setup.PayrollApprovalRecipients != null && setup.PayrollApprovalRecipients.Any())
                        {
                            _payrollApprovalRecipientProvider.AddRecipients(payrollApprovalSetup.PayrollApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, setup.PayrollApprovalRecipients.ToList());
                        }
                    }
                }
            }

            return GetPayrollApprovalSetupByType(companyId, clientId, profileId, approvalType);
        }

        public PayrollApprovalSetupsModel GetPayrollApprovalSetupByType(int companyId, string clientId, string profileId, string approvalType)
        {
            return _payrollApprovalSetupProvider.GetPayrollApprovalSetupsByType(companyId, clientId, profileId, approvalType);
        }

        public IEnumerable<PayrollNotificationGroupModel> ListPayrollNotificationGroups()
        {
            return _payrollNotificationGroupProvider.ListPayrollNotificationGroups();
        }

        public IEnumerable<PayrollNotificationGroupModel> ListInvoiceNotificationGroups()
        {
            return _payrollNotificationGroupProvider.ListInvoiceNotificationGroups();
        }

        public TableQueryInfo<PayrollApprovalRecipientModel> GetPayrollApprovalRecipients(int companyId, string clientId, string profileId, string approvalType, Expression<Func<PayrollApprovalRecipientModel, bool>> filterClause)
        {
            return _payrollApprovalRecipientProvider.GetPayrollApprovalRecipients(companyId, clientId, profileId, approvalType, filterClause);
        }

        public List<SmartTagModel> GetPayrollApprovalSmartTags()
        {
            return _smartTagProvider.GetPayrollApprovalSmartTags();
        }

        public List<string> GetPayrollApprovalRecipientsName(int companyId, string clientId, string profileId, string payrollNumber)
        {
            return _payrollApprovalSetupProvider.GetPayrollApprovalRecipientsName(companyId, clientId, profileId, payrollNumber);
        }
    }
}