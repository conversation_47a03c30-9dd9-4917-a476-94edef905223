using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.CheckPrinter;
using System;
using System.Linq;

namespace DarwiNet2._0.Services.D2
{
    public class CheckPrinterService : ICheckPrinterService
    {
        private readonly DnetEntities _dbContext;
        private readonly ICheckSetupsProvider _checkSetupsProvider;

        public CheckPrinterService(DnetEntities dbContext, ICheckSetupsProvider checkSetupsProvider)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _checkSetupsProvider = checkSetupsProvider ?? throw new ArgumentNullException(nameof(checkSetupsProvider));
        }

        public dynamic GetCheckPrinterData(int companyId, string clientId, string profileId)
        {
            var setupData = _checkSetupsProvider.GetCheckPrinterProfile(companyId, clientId, profileId);
            var checkFiles = _dbContext.CheckPrinterCrystalFiles.Where(x => x.CompanyID == companyId && (x.ClientID == clientId || x.ClientID == "***")).OrderBy(x => x.FileName).Select(x => new { x.CheckFileID, x.FileName }).ToList();
            var data = (from cps in _dbContext.CheckPrinterSetups
                        join co in _dbContext.Companies on cps.CompanyID equals co.CompanyID
                        join cl in _dbContext.Clients on new { cps.CompanyID, cps.ClientID } equals new { cl.CompanyID, cl.ClientID }
                        join pps in _dbContext.PayrollProfileSettings on new { cps.CompanyID, cps.ClientID, cps.ProfileID } equals new { pps.CompanyID, pps.ClientID, pps.ProfileID }
                        where cps.CompanyID == companyId && cps.ClientID == clientId && cps.ProfileID == profileId
                        select new
                        {
                            CompanyId = co.CompanyID,
                            CompanyName = $"{co.DnetClientID} - {co.CompanyName}", 
                            ClientId = cl.ClientID,
                            ClientName = $"{cl.ClientID} - {cl.ClientName}",
                            ProfileId = pps.ProfileID,
                            ProfileName = $"{pps.ProfileID} - {pps.Description}"
                        })
                        .FirstOrDefault();
            return new
            {
                CompanyId = data.CompanyId,
                CompanyName = data.CompanyName,
                ClientId = data.ClientId,
                ClientName = data.ClientName,
                ProfileId = data.ProfileId,
                ProfileName = data.ProfileName,
                Setup = setupData,
                CheckFiles = checkFiles,
                SetDefault = setupData.Default
            };
        }
    }
}