using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Models.D2.EditPayrollCalculation;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2.PayrollPreview;
using DarwiNet2._0.ViewModels.D2.PayrollReview;
using System;
using System.Collections.Generic;
using System.Linq;
using static DarwiNet2._0.DTOs.PayrollReviewBenefitsDTO;
using static DarwiNet2._0.ViewModels.D2.PayrollReview.PayrollReviewBenefitsTotalsViewModel;
using DarwiNet2._0.Providers;

namespace DarwiNet2._0.Services.D2
{
    /// <summary>
    ///     Provides functionality for Benfits values after the payroll calculation has taken place.
    /// </summary>
    /// <history>
    ///     [jwitcher]   07/30/20    Task-6554: Created.
    /// </history>
    public class PayrollWorkBenefitService : DbContextBaseProvider, IPayrollWorkBenefitService
    {
        #region Fields

        private readonly IPayrollWorkBenefitProvider _benefitProvider;
        private readonly IEmployeeService _employeeService;
        private readonly IEmployeeBenefitProvider _employeeBenefitProvider;

        #endregion

        #region Constructors

        public PayrollWorkBenefitService(DnetEntities dbContext,
                                         IPayrollWorkBenefitProvider benefitProvider,
                                         IEmployeeService employeeService,
                                         IEmployeeBenefitProvider employeeBenefitProvider) : base(dbContext)
        {
            _benefitProvider = benefitProvider ?? throw new ArgumentNullException(nameof(benefitProvider));
            _employeeService = employeeService;
            _employeeBenefitProvider = employeeBenefitProvider ?? throw new ArgumentNullException(nameof(employeeBenefitProvider));
        }

        #endregion

        #region Public Functions

        /// <summary>
        ///     Provides Benfit code information after the payroll calculation has taken place.
        /// </summary>
        /// <history>
        ///     [jwitcher]   07/30/20    Task-6554: Created.
        /// </history>
        public List<BenefitCode> ListCodes(int companyId, string employeeId, string payrollNumber)
        {
            return _benefitProvider.ListCodes(companyId, employeeId, payrollNumber);
        }

        public PayrollReviewBenefitsTotalsViewModel GetBenefitsTotals(string payrollNumber)
        {
            var benefitsTotals = new PayrollReviewBenefitsTotalsViewModel();

            var payrollWorkBenefits = _benefitProvider.Get(payrollNumber);
            var benefitCodes = payrollWorkBenefits.Select(b => b.Benefit).ToHashSet();

            benefitsTotals.Benefits = new List<BenefitsTotalsViewModel>();

            foreach (var benefit in benefitCodes)
            {
                var benefitTotal = new BenefitsTotalsViewModel();
                benefitTotal.Benefit = benefit;
                benefitTotal.Amount = payrollWorkBenefits.Where(b => b.Benefit.Equals(benefit)).Sum(b => b.TotalBenefit + b.Recapture);
                benefitsTotals.Benefits.Add(benefitTotal);
            }

            benefitsTotals.Benefits = benefitsTotals.Benefits.Where(x => x.Amount != 0).ToList();

            return benefitsTotals;
        }

        public TableFilterData<PayrollReviewBenefitsDTO> GetBenefits(int companyId, string clientId, string payrollNumber, TableFilter filters)
        {
            var queryInfo = _benefitProvider.GetBenefitsEmployees(payrollNumber, filters);

            var benefits =
                queryInfo.Query.
                    ToList().
                    Select(pb =>
                    {
                        var employeeDetail = _employeeService.GetEmployeeDetail(companyId, pb.EmployeeID);

                        return new PayrollReviewBenefitsDTO
                        {
                            Benefit = pb.Benefit,
                            Description = GetCodeDescription(clientId, companyId, pb.Benefit),
                            EmployeeID = pb.EmployeeID,
                            EmployeeName = $"{employeeDetail.FirstName} {employeeDetail.LastName}",
                            Amount = pb.TotalBenefit
                        };
                    });

            return new TableFilterData<PayrollReviewBenefitsDTO>
            {
                Data = benefits.ToList(),
                FilteredEntries = queryInfo.FilteredEntries,
                TotalEntries = queryInfo.TotalEntries
            };
        }

        public string GetCodeDescription(string clientId, int companyId, string code)
        {
            var description =
                (
                    from dbCode in _dbContext.ClientDivisionPayrollCodes
                    where dbCode.ClientID.Equals(clientId) && dbCode.CompanyID.ToString().Equals(companyId.ToString()) && dbCode.PayrollCode.Equals(code) && dbCode.PayrollCodeType.ToString().Equals("2")
                    select dbCode
                ).First().Description;

            return description;
        }

        public List<PayrollWorkBenefitTotals> GetEmployeesBenefits(string payrollNumber, HashSet<string> employeeIds = null) =>
            _benefitProvider.Get(payrollNumber, employeeIds);

        public List<PayrollWorkBenefitTotals> GetEmployeesBenefits(string clientID, string companyID, int payrollCodeType, string payrollNumber, HashSet<string> employeeIds = null) =>
            _benefitProvider.Get(clientID, companyID, payrollCodeType, payrollNumber, employeeIds);

        public List<BenefitsHeaderViewModel> EmployeeBenefits(string payrollNumber, string employeeId, string clientID, string companyID, int payrollCodeType)
        {
            var benefits = GetEmployeesBenefits(clientID, companyID, payrollCodeType, payrollNumber, new HashSet<string> { employeeId });
            return EmployeeBenefits(employeeId, benefits);
        }

        public List<BenefitsHeaderViewModel> EmployeeBenefits(string employeeId, List<PayrollWorkBenefitTotals> benefits)
        {
            var benefitList =
            (
                from b in benefits
                where b.EmployeeID == employeeId
                group b by b.Benefit into gp
                select new BenefitsHeaderViewModel
                {
                    Code = gp.Key,
                    WithHeld = gp.Sum(b => b.TotalBenefit).GetValueOrDefault(),
                    YTD = gp.Sum(b => b.YTD).GetValueOrDefault(),
                    Recapture = gp.Sum(b => b.Recapture).GetValueOrDefault(),
                }
            ).ToList();

            var iterator = 0;
            foreach (var benefit in benefitList)
            {
                if (benefits[iterator].Description != null)
                {
                    benefit.Description = benefits[iterator].Description;
                }
                else
                {
                    benefit.Description = benefit.Code;
                }
                iterator++;
            }

            return benefitList;

        }

        public List<PayrollPreviewCodesViewModel> GetPayrollPreviewBenefitCodes(string payrollNumber)
        {
            var results = _benefitProvider.GetPayrollPreviewBenefitCodes(payrollNumber);

            return
                (
                    from code in results
                    group code by code.Benefit into gp
                    select new PayrollPreviewCodesViewModel
                    {
                        Code = gp.Key,
                        Amount = gp.Sum(w => w.TotalBenefit).GetValueOrDefault(),
                    }
                ).ToList();
        }

        public List<PayrollPreviewCodesViewModel> GetPayrollPreviewBenefitCodes(string payrollNumber, string employeeId)
        {
            var results = _benefitProvider.GetPayrollPreviewBenefitCodesByEmployee(payrollNumber, employeeId);

            return
                (
                    from code in results
                    group code by code.Benefit into gp
                    select new PayrollPreviewCodesViewModel
                    {
                        Code = gp.Key,
                        Amount = gp.Sum(w => w.TotalBenefit).GetValueOrDefault(),
                    }
                ).ToList();
        }

        public decimal GetTotalAmount(int companyId, string employeeId, string payrollNumber)
        {
            return
                _benefitProvider
                    .ListCodes(companyId, employeeId, payrollNumber)
                    .Sum(c => c.Amount + c.RecaptureAmount)
                    .GetValueOrDefault();
        }

        public List<PayrollWorkBenefit> ListBenefits(string payrollNumber, string employeeID)
        {
            return _benefitProvider.ListBenefits(payrollNumber, employeeID); 
        }

        public void CombineProratedBenefits (int companyID, string payrollNumber, string employeeID)
        {
            var employeeBenefits = _employeeBenefitProvider.GetEmployeeBenefits(companyID, employeeID);
            var payrollWorkBenefits = _benefitProvider.ListBenefits(payrollNumber, employeeID);
            employeeBenefits = employeeBenefits.Where(x => x.AssignToHomeDepartment == false).ToList();
            List<PayrollWorkBenefit> recordsToRemove = new List<PayrollWorkBenefit>();
            foreach (var employeeBenefit in employeeBenefits)
            {
                var payrollWorkBenefitsByCode = payrollWorkBenefits.Where(x => x.Benefit == employeeBenefit.Benefit);
                if (payrollWorkBenefitsByCode.Any())
                {
                    bool benefitHasEditedComponent = payrollWorkBenefitsByCode.Any(x => x.Edit == true);
                    if (benefitHasEditedComponent)
                    {
                        foreach (var benefit in payrollWorkBenefitsByCode)
                        {
                            benefit.Edit = true;
                        }
                    }
                    else
                    {
                        var recordToKeep = payrollWorkBenefitsByCode.FirstOrDefault();
                        decimal totalBenefit = (decimal)payrollWorkBenefitsByCode.Sum(x => x.TotalBenefit);
                        recordToKeep.TotalBenefit = totalBenefit;
                        recordToKeep.Department = String.Empty;
                        recordsToRemove.AddRange(payrollWorkBenefitsByCode.Where(x => x.TRXNumber != recordToKeep.TRXNumber));
                    }
                }
            }
            if (recordsToRemove.Any())
            {
                _dbContext.PayrollWorkBenefits.RemoveRange(recordsToRemove);
            }
            _dbContext.SaveChanges();
        }

        #endregion
    }
}