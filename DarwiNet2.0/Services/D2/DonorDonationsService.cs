using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Models.API;
using DarwiNet2._0.Providers.D2;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Services.D2
{
    public class DonorDonationsService : IDonorDonationsService
    {
        #region Fields
        private readonly IDonorDonationsProvider _donorDonationsProvider;
        #endregion

        #region Contructors
        public DonorDonationsService(DnetEntities dbContext, IDonorDonationsProvider donorDonationsProvider)
        {
            _donorDonationsProvider = donorDonationsProvider ?? throw new ArgumentNullException(nameof(donorDonationsProvider));
        }
        #endregion

        #region Public Methods        
        public DonorDonationsServiceResponse AddDonorDonation(DonorDonationDTO donorDonationDto)
        {
            var donorDonation = Convert(donorDonationDto);
            donorDonation.DonationID = _donorDonationsProvider.GenerateDonationID(donorDonationDto.CompanyID.Value, donorDonationDto.ClientID, donorDonationDto.DonorID.Value);

            var errors = Validate(donorDonation);
            if (errors.Any()) return new DonorDonationsServiceResponse { donorDonation = donorDonationDto, errors = errors };

            _donorDonationsProvider.AddDonorDonation(donorDonation);
            donorDonationDto = Convert(donorDonation);
            return new DonorDonationsServiceResponse { donorDonation = donorDonationDto, errors = errors };
        }

        public DonorDonationsServiceResponse UpdateDonorDonation(DonorDonationDTO donorDonationDto)
        {
            var donorDonation = Convert(donorDonationDto);

            var errors = Validate(donorDonation);
            if (errors.Any()) return new DonorDonationsServiceResponse { donorDonation = donorDonationDto, errors = errors };

            _donorDonationsProvider.UpdateDonorDonation(donorDonation);
            return new DonorDonationsServiceResponse { donorDonation = donorDonationDto, errors = errors };
        }

        public void DeleteDonorDonation(DonorDonationDTO donorDonationDto)
        {
            _donorDonationsProvider.DeleteDonorDonation(Convert(donorDonationDto));
        }

        public DonorDonationDTO GetDonorDonation(int companyID, string clientID, long donorID, long donationID)
        {
            var donorDonation = _donorDonationsProvider.GetDonorDonation(companyID, clientID, donorID, donationID);
            return Convert(donorDonation);
        }

        public TableQueryInfo<DonorDonationDTO> GetDonorDonationsTableData(int companyID, string clientID, long donorID, TableFilter filters)
        {
            return _donorDonationsProvider.GetDonorDonationsTableData(companyID, clientID, donorID, filters);
        }
        #endregion

        #region Private Methods
        private static DonorDonation Convert(DonorDonationDTO donorDonationDto)
        {
            return new DonorDonation()
            {
                CompanyID = donorDonationDto.CompanyID != null ? donorDonationDto.CompanyID.Value : 0,
                ClientID = donorDonationDto.ClientID,
                DonorID =  donorDonationDto.DonorID != null ? donorDonationDto.DonorID.Value : 0,
                DonationID = donorDonationDto.DonationID != null ? donorDonationDto.DonationID.Value : 0,
                DonationType = donorDonationDto.DonationType != null ? donorDonationDto.DonationType.Value : (byte)0,
                DonationAmount = donorDonationDto.DonationAmount != null ? donorDonationDto.DonationAmount.Value : 0,
                DonationDate = donorDonationDto.DonationDate != null ? donorDonationDto.DonationDate.Value : DateTime.MinValue.Date,
                Description = donorDonationDto.Description,
                EmployeeID = donorDonationDto.EmployeeID,
                Position = donorDonationDto.Position,
                PaymentMethod = donorDonationDto.PaymentMethod,
                CreditCardName = donorDonationDto.CreditCardName,
                CreditCardNumber = donorDonationDto.CreditCardNumber,
                CreditCardExpirationDate = donorDonationDto.CreditCardExpirationDate,
                CreditCardCVC = donorDonationDto.CreditCardCVC,
                BankRoutingNumber = donorDonationDto.BankRoutingNumber,
                BankAccountNumber = donorDonationDto.BankAccountNumber,
                CheckbookID = donorDonationDto.CheckbookID,
                CheckNumber = donorDonationDto.CheckNumber
            };
        }

        private static DonorDonationDTO Convert(DonorDonation donorDonation)
        {
            return new DonorDonationDTO()
            {
                CompanyID = donorDonation.CompanyID,
                ClientID = donorDonation.ClientID,
                DonorID = donorDonation.DonorID,
                DonationID = donorDonation.DonationID,
                DonationType = donorDonation.DonationType,
                DonationAmount = donorDonation.DonationAmount,
                DonationDate = donorDonation.DonationDate,
                Description = donorDonation.Description,
                EmployeeID = donorDonation.EmployeeID,
                Employee = new DonationEmployeeDTO()
                {
                    EmployeeID = donorDonation.EmployeeID,
                    FirstName = donorDonation.Employee?.FirstName,
                    LastName = donorDonation.Employee?.LastName,
                    FullName = donorDonation.Employee?.LastName + ", " + donorDonation.Employee?.FirstName,
                },
                Position = donorDonation.Position,
                PaymentMethod = donorDonation.PaymentMethod,
                CreditCardName = donorDonation.CreditCardName,
                CreditCardNumber = donorDonation.CreditCardNumber,
                CreditCardExpirationDate = donorDonation.CreditCardExpirationDate,
                CreditCardCVC = donorDonation.CreditCardCVC,
                BankRoutingNumber = donorDonation.BankRoutingNumber,
                BankAccountNumber = donorDonation.BankAccountNumber,
                CheckbookID = donorDonation.CheckbookID,
                CheckNumber = donorDonation.CheckNumber
            };
        }

        private static Dictionary<string, string> Validate(DonorDonation donorDonation)
        {
            var errors = new Dictionary<string, string>();
            if (donorDonation.CompanyID < 1) errors.Add("companyID", "Company ID is required.");
            if (string.IsNullOrWhiteSpace(donorDonation.ClientID)) errors.Add("clientID", "Client ID is required.");
            if (donorDonation.DonorID < 1) errors.Add("donorID", "Donor ID is required.");
            if (donorDonation.DonationType == 0) errors.Add("donationType", "Donation Type is required.");
            if (donorDonation.DonationDate == null || donorDonation.DonationDate == DateTime.MinValue.Date) errors.Add("donationDate", "Donation Date is required.");
            if (donorDonation.DonationAmount < 1) errors.Add("donationAmount", "Donation Amount is required.");
            if (donorDonation.DonationType == (byte)DonationType.Athlete && string.IsNullOrEmpty(donorDonation.EmployeeID)) errors.Add("employeeID", "Athlete is required.");
            if (donorDonation.DonationType == (byte)DonationType.Sport && string.IsNullOrEmpty(donorDonation.Position)) errors.Add("position", "Sport is required.");
            return errors;
        }
        #endregion
    }
}