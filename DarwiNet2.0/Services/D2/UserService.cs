using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Providers.D2;
using DarwiNet2._0.ViewModels.Clients;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.DTOs.CheckPrinter;
using DarwiNet2._0.Interfaces.Services;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Services.D2
{
    public class UserService : IUserService
    {
        #region Fields 

        private IUserProvider _userProvider;
        private IUserSupervisorSecurityService _userSupervisorSecurityService;
        private IUserRoleClientEmployeeAssignmentProvider _userRoleClientEmployeeAssignmentProvider;
        private IDarwinetSetupProvider _darwinetSetupProvider;

        #endregion

        #region Constructors

        public UserService()
        {
            _userProvider = new UserProvider();
            _userSupervisorSecurityService = new UserSupervisorSecurityService();
            _darwinetSetupProvider = new DarwinetSetupProvider();
            _userRoleClientEmployeeAssignmentProvider = new UserRoleClientEmployeeAssignmentProvider();
        }

        public UserService(IUserProvider userProvider,
            IUserSupervisorSecurityService userSupervisorSecurityService)
        {
            _userProvider = userProvider ?? throw new ArgumentNullException(nameof(userProvider));
            _userSupervisorSecurityService = userSupervisorSecurityService ?? throw new ArgumentNullException(nameof(userSupervisorSecurityService));
            _userRoleClientEmployeeAssignmentProvider = new UserRoleClientEmployeeAssignmentProvider();
            _darwinetSetupProvider = new DarwinetSetupProvider();
        }

        public UserService(IUserProvider userProvider,
                           IUserRoleClientEmployeeAssignmentProvider userRoleClientEmployeeAssignmentProvider,
                           IDarwinetSetupProvider darwinetSetupProvider)
        {
            _userProvider = userProvider ?? throw new ArgumentNullException(nameof(userProvider));
            _userRoleClientEmployeeAssignmentProvider = userRoleClientEmployeeAssignmentProvider ?? throw new ArgumentNullException(nameof(userRoleClientEmployeeAssignmentProvider));
            _darwinetSetupProvider = darwinetSetupProvider ?? throw new ArgumentNullException(nameof(darwinetSetupProvider));
        }

        #endregion

        public List<Id_Code_Description> ListUserNamesWithIds()
        {
            return _userProvider.ListUserNamesWithIds();
        }

        public string FindUserIdById(int id)
        {
            return _userProvider.FindUserIdById(id);
        }

        public List<QuicklistGroupUsers> GetSupervisorUsers(int companyId, string clientId)
        {
            List<Id_Code_Description> users = ListUserNamesWithIds();
            List<string> supervisorUserIds = _userSupervisorSecurityService.ListUserIds(companyId, clientId);

            users.RemoveAll(u => !supervisorUserIds.Contains(u.Code));

            List<QuicklistGroupUsers> quicklistGroupUsers = new List<QuicklistGroupUsers>();
            quicklistGroupUsers = users.Select(u => new QuicklistGroupUsers
            {
                UID = u.ID,
                GroupName = u.Code,
                GroupDescription = u.Description
            }).ToList();

            return quicklistGroupUsers;
        }
        public List<Code_Description> GetBackendUsers() =>
            _userProvider.GetBackendUsers()
                         .Select(x => new Code_Description()
                         {
                             Code = x.UserID,
                             Description = $"{x.Name} ({x.UserID})"
                         })
                         .ToList();

        public List<Code_Description> GetSystemLevelUserNames() =>
            _userProvider.GetSystemLevelUserNames()
                         .Select(x => new Code_Description()
                         {
                             Code = x.UserID,
                             Description = $"{x.Name} ({x.UserID})"
                         })
                         .ToList();

        public string GetNameByUserId(string userId)
        {
            string name = "";
            User u = _userProvider.GetBackendUserByUserId(userId);
            if (u != null)
            {
                name = u.Name;
            }
            return name;
        }

        public List<User> GetClientUsers(int companyId, string clientId, string dNetOwnerId, string dNetLevel)
        {
            List<string> userIds = _userRoleClientEmployeeAssignmentProvider.GetClientUserIds(companyId, clientId);

            List<User> users;
            if (dNetLevel == DNetAccessLevel.System)
            {
                users = _userProvider.GetClientUsersList(dNetOwnerId);
            }
            else
            {
                users = _userProvider.GetClientUsersExcludeSystemLevel(dNetOwnerId);
            }

            users = users.Where(u => userIds.Contains(u.UserID)).ToList();

            return users;
        }

        public List<User> GetClientUsers(int companyId, string clientId, string dNetOwnerId, string dNetLevel, bool menuAccess, bool showDisabled)
        {
            var loginAsEnabled = _darwinetSetupProvider.AllowLoginAs(companyId, clientId);
            var userLoginAs = _userProvider.EnableLoginAs(dNetOwnerId);

            List<User> users = GetClientUsers(companyId, clientId, dNetOwnerId, dNetLevel);
            bool syslevel = false;
            if (dNetLevel == DNetAccessLevel.System)
            {
                syslevel = true;
            }

            if (!showDisabled)
            {
                users.RemoveAll(u => u.Enabled == false);
            }

            var selectedUsers = users.Select(u => new User
            {
                UserDefId = u.Id,
                UserID = u.UserID,
                Name = u.Name,
                Email = u.Email,
                LoginCount = u.LoginCount,
                LastLogin = u.LastLogin,
                Enabled = u.Enabled,
                ClientID = clientId,
                Level = syslevel,
                LoginAsEnabled = loginAsEnabled,
                UserLoginAs = userLoginAs,
                MenuAccess = menuAccess
            }).ToList();

            return selectedUsers;
        }

        public UsersVM GetUsersViewModel(int companyId, string clientId, string dNetOwnerId, string dNetLevel)
        {
            ClientProvider clientProvider = new ClientProvider();
            var clients = clientProvider.GetClientNamesByCompanyId(companyId);
            if (string.IsNullOrEmpty(clientId))
            {
                clientId = !string.IsNullOrEmpty(GlobalVariables.Client) ? GlobalVariables.Client : clients.Select(c => c.Code).FirstOrDefault();
            }

            DarwinetSetupProvider darwinetSetupProvider = new DarwinetSetupProvider();
            var loginAsEnabled = darwinetSetupProvider.GetDarwinetSetup(companyId, clientId).AllowLoginAs;

            var users = GetClientUsers(companyId, clientId, dNetOwnerId, dNetLevel);

            UsersVM usersViewModel = new UsersVM();

            usersViewModel.ClientIDs = clients;
            usersViewModel.SelectedClient = clientId;
            usersViewModel.LoginAsEnabled = loginAsEnabled;
            usersViewModel.Users = users;

            return usersViewModel;
        }
        public ClientUsersModel GetClientUsersModel(int companyId, string clientId, string dNetOwnerId, string dNetLevel)
        {
            var loginAsEnabled = _darwinetSetupProvider.AllowLoginAs(companyId, clientId);

            var users = GetClientUsers(companyId, clientId, dNetOwnerId, dNetLevel);

            return new ClientUsersModel
            {
                SelectedClient = clientId,
                LoginAsEnabled = loginAsEnabled,
                Users = users
            };
        }

        public List<Code_Description> GetClientUsers(int companyId, string clientId)
        {
            return _userProvider.GetClientUsers(companyId, clientId);
        }

        public void RemoveUser(string userId)
        {
            _userProvider.RemoveUser(userId);
        }

        public User GetUserInfoByUsername(string userId)
        {
            return _userProvider.GetBackendUserByUserId(userId);
        }

        public bool IsPayrollUser()
        {
            return _userProvider.IsPayrollUser();
        }

        public UserEEDepositMailModel GetUserDepositMailInfo(int companyId, string clientId, string employeeId)
        {
            IEmployeeProvider eprovider = new EmployeeProvider();
            var ee = eprovider.GetEmployee(companyId, employeeId);
            if (ee != null)
            {
                var eeEmail = new EmployeeEmail();
                var emails = eeEmail.EEEmails(ee);
                var users = _userProvider.GetEmployeeUserIdNameEmailByUserRoleClientEmployeeAssignmentJoin(ee.CompanyID, ee.ClientID, ee.EmployeeID);
                if (emails.Any()) users = users.Where(u => emails.Any(e => e == u.Description)).ToList();
                if (users.Count() > 1) users = users.Where(u => u.Code.Contains(ee.FirstName) || u.Code.Contains(ee.LastName)).ToList();
                if (users.Any())
                {
                    var user = users.First();
                    var pwd = _userProvider.GetUserPWD(user.ID);
                    return new UserEEDepositMailModel { UserID = user.ID, Name = user.Code, Email = user.Description, Password = pwd };
                }
            }
            return null;
        }

        public List<MultiselectOptionsDTO> GetExternalUsers()
        {
            return _userProvider.GetExternalUsers();
        }
        public List<User> GetPeoUserRole(string ownerID)
        {
            return _userProvider.GetPeoUserRole(ownerID);
        }
        public User GetUserByUserId(string userId)
        {
            return _userProvider.GetUserByUserId(userId);
        }

        public void AddAllClientAccess(string UserID, string RoleID)
        {
            _userProvider.AddAllClientAccess(UserID, RoleID);
        }

        public bool IsPEOUser(string dnetOwnerId)
        {
            return _userProvider.IsPEOUser(dnetOwnerId);
        }
    }
}