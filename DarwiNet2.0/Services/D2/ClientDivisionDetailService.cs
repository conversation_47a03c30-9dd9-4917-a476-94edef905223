using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Services.D2
{
    public class ClientDivisionDetailService : IClientDivisionDetailService
    {
        #region Fields

        private readonly IClientDivisionDetailProvider _clientDivisionDetailProvider;

        #endregion

        #region Constructors

        /// <summary>
        ///     Default constructor.
        /// </summary>
        /// <history>
        ///     [lyeager]    06/17/20    Task-5863: Created.
        /// </history>
        public ClientDivisionDetailService()
        {
            _clientDivisionDetailProvider = new ClientDivisionDetailProvider();
        }

        public ClientDivisionDetailService(IClientDivisionDetailProvider clientDivisionDetailProvider)
        {
            _clientDivisionDetailProvider = clientDivisionDetailProvider ?? throw new ArgumentNullException(nameof(clientDivisionDetailProvider));
        }

        #endregion

        #region Public Functions

        public List<string> ListCodes(int companyId, string clientId)
        {
            return _clientDivisionDetailProvider.ListDepartmentCodes(companyId, clientId);
        }

        public List<DivisionShort> GetDivisionsShortByCompanyAndClient(int companyId, string clientId)
        {
            return _clientDivisionDetailProvider.GetDivisionsShortByCompanyAndClient(companyId, clientId);
        }

        #endregion
    }
}