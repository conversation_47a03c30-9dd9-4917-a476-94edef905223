using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Services.D2
{
    public class PayrollWorkMissedCodeService : IPayrollWorkMissedCodeService
    {
        #region Fields

        private readonly IPayrollWorkMissedCodeProvider _payrollWorkMissedCodeProvider;

        #endregion

        #region Constructors

        public PayrollWorkMissedCodeService(IPayrollWorkMissedCodeProvider payrollWorkMissedCodeProvider)
        {
            _payrollWorkMissedCodeProvider = payrollWorkMissedCodeProvider ?? throw new ArgumentNullException(nameof(payrollWorkMissedCodeProvider));
        }

        #endregion

        #region Public Methods

        public List<PayrollWorkMissedCode> ListMissedCodes(string payrollNumber, string employeeID)
        {
            return _payrollWorkMissedCodeProvider.ListMissedCodes(payrollNumber, employeeID);
        }

        public void Delete(string payrollNumber, string employeeID)
        {
            _payrollWorkMissedCodeProvider.Delete(payrollNumber, employeeID);
        }

        #endregion
    }
}