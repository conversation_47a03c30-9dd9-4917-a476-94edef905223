using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Services.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollWorkHeaderDivisionalService : IPayrollWorkHeaderDivisionalService
    {
        #region Fields

        private readonly IPayrollWorkHeaderDivisionalService _workHeaderDivisionalService;

        #endregion

        #region Constructors

        public PayrollWorkHeaderDivisionalService(IPayrollWorkHeaderDivisionalService headerDivisionalService)
        {
            _workHeaderDivisionalService = headerDivisionalService;
        }

        #endregion

        #region Public Methods



        #endregion
    }
}