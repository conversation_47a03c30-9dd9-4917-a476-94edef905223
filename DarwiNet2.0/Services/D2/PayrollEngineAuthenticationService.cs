using DarwiNet2._0.Interfaces.Services.D2;
using Newtonsoft.Json;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Threading.Tasks;

namespace DarwiNet2._0.Services.D2
{
    /// <summary>
    /// 
    /// </summary>
    /// <history>
    ///     [mframe]    07/01/20    Task-6295: Created history. Removed Authenticate function.
    /// </history>
    public class PayrollEngineAuthenticationService : IPayrollEngineAuthenticationService
    {
        #region Fields

        private readonly HttpClient _httpClient;

        #endregion

        #region Constructors

        /// <summary>
        /// 
        /// </summary>
        /// <history>
        ///     [mframe]    07/01/20    Task-6295: Created history.
        /// </history>
        public PayrollEngineAuthenticationService(HttpClient httpClient)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        }

        #endregion

        #region Public Functions

        /// <summary>
        /// 
        /// </summary>
        /// <history>
        ///     [mframe]    07/01/20    Task-6295: Created history.
        /// </history>
        public async Task<string> AuthenticateAsync(string username, string password)
        {
            var credentials = 
                new StringContent(
                    JsonConvert.SerializeObject(new { username, password })
                );

            var response = await _httpClient.PostAsync("Authentication", credentials);

            if (response.IsSuccessStatusCode)
            {
                string json = await response.Content.ReadAsStringAsync();
                return (string)JsonConvert.DeserializeObject<dynamic>(json)["token"];
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <history>
        ///     [mframe]    07/01/20    Task-6295: Created.
        /// </history>
        public DateTime GetTokenExpirationDateTime(string token)
        {
            return new JwtSecurityTokenHandler().ReadJwtToken(token).ValidTo.ToLocalTime();
        }

        #endregion
    }
}