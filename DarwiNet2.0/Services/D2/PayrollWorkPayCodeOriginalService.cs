using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Services.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollWorkPayCodeOriginalService : IPayrollWorkPayCodeOriginalService
    {
        #region Fields

        private readonly IPayrollWorkPayCodeOriginalService _workPayCodeOriginalService;

        #endregion

        #region Constructors

        public PayrollWorkPayCodeOriginalService(IPayrollWorkPayCodeOriginalService payCodeOriginalService)
        {
            _workPayCodeOriginalService = payCodeOriginalService;
        }

        #endregion

        #region Public Methods



        #endregion
    }
}