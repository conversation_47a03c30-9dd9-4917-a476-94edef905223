using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2.PayrollReview;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using static DarwiNet2._0.ViewModels.D2.PayrollReview.PayrollReviewTaxesTotalsViewModel;
using DarwiNet2._0.DNetSynch;

namespace DarwiNet2._0.Services.D2
{
    public class PayrollReviewTaxService : IPayrollReviewTaxService
    {

        #region Fields

        private readonly IPayrollWorkHeaderService _payrollWorkHeaderService;
        private readonly IPayrollWorkFutaSutaWCService _payrollWorkFutaSutaWcService;
        private readonly IPayrollWorkStateTaxService _payrollWorkStateTaxService;
        private readonly IPayrollWorkLocalTaxService _payrollWorkLocalTaxService;
        private readonly IPayrollWorkPayCodeService _payrollWorkPayCodeService;

        #endregion

        #region Constructors

        public PayrollReviewTaxService(IPayrollWorkHeaderService payrollWorkHeaderService,
                                       IPayrollWorkFutaSutaWCService payrollWorkFutaSutaWcService,
                                       IPayrollWorkStateTaxService payrollWorkStateTaxService,
                                       IPayrollWorkLocalTaxService payrollWorkLocalTaxService,
                                       IPayrollWorkPayCodeService payrollWorkPayCodeService)
        {
            _payrollWorkHeaderService = payrollWorkHeaderService;
            _payrollWorkFutaSutaWcService = payrollWorkFutaSutaWcService;
            _payrollWorkStateTaxService = payrollWorkStateTaxService;
            _payrollWorkLocalTaxService = payrollWorkLocalTaxService;
            _payrollWorkPayCodeService = payrollWorkPayCodeService;
        }

        #endregion

        #region Public Methods

        public PayrollReviewTaxesTotalsViewModel GetTaxesTotals()
        {
            var taxTotals = new PayrollReviewTaxesTotalsViewModel();

            taxTotals.Taxes = new List<TaxesViewModel>
            {
                new TaxesViewModel { Tax = "Federal", TaxAmount = 75000m, TaxableWages = 75000m },
                new TaxesViewModel { Tax = "FICA SS", TaxAmount = 75000m, TaxableWages = 75000m },
                new TaxesViewModel { Tax = "FICA Med", TaxAmount = 75000m, TaxableWages = 75000m },
                new TaxesViewModel { Tax = "FUTA", TaxAmount = 75000m, TaxableWages = 75000m },
                new TaxesViewModel { Tax = "SUTA", TaxAmount = 75000m, TaxableWages = 75000m },
                new TaxesViewModel { Tax = "State", TaxAmount = 75000m, TaxableWages = 75000m },
                new TaxesViewModel { Tax = "Local", TaxAmount = 75000m, TaxableWages = 75000m },
                new TaxesViewModel { Tax = "W/C", TaxAmount = 75000m, TaxableWages = 75000m }
            };

            return taxTotals;
        }

        public PayrollReviewTaxesTotalsViewModel GetTaxesTotals(string payrollNumber)
        {
            var taxTotals = new PayrollReviewTaxesTotalsViewModel();

            var federalTaxes = _payrollWorkHeaderService.GetFederalTaxTotals(payrollNumber);
            var futaSutaWcTaxes = _payrollWorkFutaSutaWcService.GetFutaSutaWcTaxTotals(payrollNumber);
            var stateTaxes = _payrollWorkStateTaxService.GetStateTaxTotals(payrollNumber);
            var localTaxes = _payrollWorkLocalTaxService.GetLocalTaxTotals(payrollNumber);

            taxTotals.Taxes = new List<TaxesViewModel> // Populate with above data
            {
                new TaxesViewModel { Tax = "Federal", TaxAmount = federalTaxes.FederalTaxAmount, TaxableWages = federalTaxes.FederalTaxableWages },
                new TaxesViewModel { Tax = "FICA SS", TaxAmount = federalTaxes.FICASSTaxAmount, TaxableWages = federalTaxes.FICASSTaxableWages },
                new TaxesViewModel { Tax = "FICA Med", TaxAmount = federalTaxes.FICAMedTaxAmount, TaxableWages = federalTaxes.FICAMedTaxableWages },
                new TaxesViewModel { Tax = "State", TaxAmount = stateTaxes.TaxAmount, TaxableWages = stateTaxes.TaxableWages, MultiTaxableWages = stateTaxes.MultiTaxableWages },
                new TaxesViewModel { Tax = "Local", TaxAmount =  localTaxes.TaxAmount, TaxableWages = localTaxes.TaxableWages, MultiTaxableWages = localTaxes.MultiTaxableWages },
            };

            if (GlobalVariables.CurrentUser.SystemLevelEnabled)
            {
                var wcTotal = _payrollWorkPayCodeService.GetWcTotal(payrollNumber);
                taxTotals.Taxes.Add( new TaxesViewModel { Tax = "FUTA", TaxAmount = futaSutaWcTaxes.FUTAAmount, TaxableWages = futaSutaWcTaxes.FutaTaxableWages } );
                taxTotals.Taxes.Add( new TaxesViewModel { Tax = "SUTA", TaxAmount = futaSutaWcTaxes.SUTAAmount, TaxableWages = futaSutaWcTaxes.SutaTaxableWages } );
                taxTotals.Taxes.Add( new TaxesViewModel { Tax = "W/C", TaxAmount = wcTotal, TaxableWages = futaSutaWcTaxes.WCTaxableWages } );
                taxTotals.Taxes = taxTotals.Taxes.OrderBy(x => x.Tax).ToList();
            }

            return taxTotals;
        }

        #endregion
    }
}