using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.ViewModels.D2;

namespace DarwiNet2._0.Services.D2
{
    /// <summary>
    ///     Provides functionality for PayrollNotificationGroup related functions.
    /// </summary>
    /// <history>
    ///     [lyeager]   06/19/20    Task-5848: Created.
    ///     [lyeager]   12/01/20    Task-7108: Implemented PayrollNotificationGroupMemberProvider. Removed unused services PayrollNotificationGroupMemberService and SelectArrayService.
    /// </history>
    public class PayrollNotificationGroupService : IPayrollNotificationGroupService
    {
        #region Fields

        private readonly IPayrollNotificationGroupProvider _payrollNotificationGroupProvider;
        private readonly IPayrollNotificationGroupMemberProvider _payrollNotificationGroupMemberProvider;
        private readonly IPayrollApprovalSetupProvider _payrollApprovalSetupProvider;
        private readonly IPayrollApprovalRecipientProvider _payrollApprovalRecipientProvider;

        #endregion

        #region Constructors

        public PayrollNotificationGroupService(
            IPayrollNotificationGroupProvider payrollNotificationGroupProvider,
            IPayrollNotificationGroupMemberProvider payrollNotificationGroupMemberProvider,
            IPayrollApprovalSetupProvider payrollApprovalSetupProvider,
            IPayrollApprovalRecipientProvider payrollApprovalRecipientProvider)
        {
            _payrollNotificationGroupProvider = payrollNotificationGroupProvider ?? throw new ArgumentNullException(nameof(payrollNotificationGroupProvider));
            _payrollNotificationGroupMemberProvider = payrollNotificationGroupMemberProvider ?? throw new ArgumentNullException(nameof(payrollNotificationGroupMemberProvider));
            _payrollApprovalSetupProvider = payrollApprovalSetupProvider ?? throw new ArgumentNullException(nameof(payrollApprovalSetupProvider));
            _payrollApprovalRecipientProvider = payrollApprovalRecipientProvider ?? throw new ArgumentNullException(nameof(payrollApprovalRecipientProvider));
        }

        #endregion

        #region Public Functions

        public List<PayrollNotificationGroup> GetPayrollNotificationGroups()
        {
            var payrollNotificationGroups = _payrollNotificationGroupProvider.ListGroups();
            return payrollNotificationGroups.ToList();
        }

        public List<PayrollNotificationGroupModel> ListPayrollNotificationGroups()
        {
            return _payrollNotificationGroupProvider.ListPayrollNotificationGroups();
        }

        public PayrollNotificationGroup GetById(int payrollNotificationGroupId)
        {
            PayrollNotificationGroup payrollNotificationGroup = _payrollNotificationGroupProvider.GetById(payrollNotificationGroupId);

            if (payrollNotificationGroup == null)
            {
                throw new ApplicationException("Unable to find payroll notification group.");
            }

            return payrollNotificationGroup;
        }

        public int Create(string createdBy, string groupName, bool shared)
        {
            var payrollNotificationGroup = new PayrollNotificationGroup();
            payrollNotificationGroup.CreatedBy = createdBy;
            payrollNotificationGroup.GroupName = groupName;
            payrollNotificationGroup.Shared = shared;

            int id = _payrollNotificationGroupProvider.Create(payrollNotificationGroup);

            return id;
        }

        public void Update(int payrollNotificationGroupId, string groupName, bool shared, IEnumerable<string> users)
        {
            var payrollNotificationGroup = GetById(payrollNotificationGroupId);

            if (payrollNotificationGroup != null)
            {
                _payrollNotificationGroupProvider.Update(payrollNotificationGroup, groupName, shared);
                _payrollNotificationGroupMemberProvider.Update(payrollNotificationGroupId, users);
                _payrollApprovalRecipientProvider.UpdatePayrollApprovalSetupNotificationRecipients(payrollNotificationGroupId, users);
            }
        }

        public void Delete(int payrollNotificationGroupId, string dnetOwnerId)
        {
            var payrollNotificationGroup = GetById(payrollNotificationGroupId);

            if (payrollNotificationGroup != null)
            {
                if (dnetOwnerId == payrollNotificationGroup.CreatedBy)
                {
                    // delete group members
                    _payrollNotificationGroupMemberProvider.DeleteMembersByGroupId(payrollNotificationGroupId);

                    // delete group
                    _payrollNotificationGroupProvider.Delete(payrollNotificationGroup);
                }
            }
        }

        public TableQueryInfo<ExternallApprovalGroupViewModel> GetExternalApprovalGroups(TableFilter filters)
        {
            return _payrollNotificationGroupProvider.GetExternalApprovalGroups(filters);
        }

        public TableQueryInfo<ExternallApprovalGroupViewModel> GetExternalInvoiceApprovalGroups(TableFilter filters)
        {
            return _payrollNotificationGroupProvider.GetExternalInvoiceApprovalGroups(filters);
        }
        

        public void AddGroupWithUsers(string groupName, List<MultiselectOptionsDTO> groupUsers)
        {
            _payrollNotificationGroupProvider.AddGroupWithUsers(groupName, groupUsers);
        }

        public void AddInvoiceGroupWithUsers(string groupName, List<MultiselectOptionsDTO> groupUsers)
        {
            _payrollNotificationGroupProvider.AddInvoiceGroupWithUsers(groupName, groupUsers);
        }

        #endregion
    }
}