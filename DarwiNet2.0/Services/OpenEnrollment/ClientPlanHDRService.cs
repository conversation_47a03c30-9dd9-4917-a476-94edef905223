using DarwiNet2._0.Controllers;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Providers.OpenEnrollment;
using DarwiNet2._0.Interfaces.Services.OpenEnrollment;
using DarwiNet2._0.Providers.D2;
using DarwiNet2._0.Providers.OpenEnrollment;
using DarwiNet2._0.DNetSynch;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Services.OpenEnrollment
{
    public class ClientPlanHDRService : IClientPlanHDRService
    {
        #region Fields

        private readonly IClientPlanHDRProvider _clientPlanHDRProvider;
        private readonly IClientProvider _clientProvider;
        private readonly IEmployeeProvider _employeeProvider;
        private readonly IEmployeeAssignedPlanProvider _employeeAssignedPlanProvider;
        private readonly IEmployeeEnrollmentStatusProvider _employeeEnrollmentStatusProvider;
        private readonly IClientPlanEligibilityCodeProvider _clientPlanEligibilityCodeProvider;

        private readonly IEmployeePlanEligibilityService _employeePlanEligibilityService;
        private readonly IClientPlanRateGridService _clientPlanRateGridService;

        #endregion

        #region Constructors

        public ClientPlanHDRService()
        {
            _clientPlanHDRProvider = new ClientPlanHDRProvider();
            _employeeAssignedPlanProvider = new EmployeeAssignedPlanProvider();
            _employeeProvider = new EmployeeProvider();
            _employeeEnrollmentStatusProvider = new EmployeeEnrollmentStatusProvider();
            _employeePlanEligibilityService = new EmployeePlanEligibilityService();
            _clientPlanEligibilityCodeProvider = new ClientPlanEligibilityCodeProvider();
            _clientProvider = new ClientProvider();
            _clientPlanRateGridService = new ClientPlanRateGridService();
        }

        public ClientPlanHDRService(IClientPlanHDRProvider clientPlanHDRProvider,
            IEmployeeAssignedPlanProvider employeeAssignedPlanProvider,
            IEmployeeProvider employeeProvider,
            IEmployeeEnrollmentStatusProvider employeeEnrollmentStatusProvider,
            IEmployeePlanEligibilityService employeePlanEligibilityService,
            IClientPlanEligibilityCodeProvider clientPlanEligibilityCodeProvider,
            IClientProvider clientProvider,
            IClientPlanRateGridService clientPlanRateGridService)
        {
            _clientPlanHDRProvider = clientPlanHDRProvider ?? throw new ArgumentNullException(nameof(clientPlanHDRProvider));
            _employeeAssignedPlanProvider = employeeAssignedPlanProvider ?? throw new ArgumentNullException(nameof(employeeAssignedPlanProvider));
            _employeeProvider = employeeProvider ?? throw new ArgumentNullException(nameof(employeeProvider));
            _employeeEnrollmentStatusProvider = employeeEnrollmentStatusProvider ?? throw new ArgumentNullException(nameof(employeeEnrollmentStatusProvider));
            _employeePlanEligibilityService = employeePlanEligibilityService ?? throw new ArgumentNullException(nameof(employeePlanEligibilityService));
            _clientPlanEligibilityCodeProvider = clientPlanEligibilityCodeProvider ?? throw new ArgumentNullException(nameof(clientPlanEligibilityCodeProvider));
            _clientProvider = clientProvider ?? throw new ArgumentNullException(nameof(clientProvider));
            _clientPlanRateGridService = clientPlanRateGridService ?? throw new ArgumentNullException(nameof(clientPlanRateGridService));
        }

        #endregion

        public short GetCurrentPlanYear(int companyId, string oeClientId, string enrollmentType, bool[] levents = null)
        {
            return _clientPlanHDRProvider.GetCurrentPlanYear(companyId, oeClientId, enrollmentType, levents);
        }

        public ClientPlanHDR GetPlanHDR(EmployeeEnrollmentEligiblePlan thisPlan, out bool grandfathered)
        {
            grandfathered = false;
            ClientPlanHDR plan = _clientPlanHDRProvider.GetPlanHDR(thisPlan);

            if (plan != null && plan.Grandfathered)
                grandfathered = _employeeAssignedPlanProvider.IsPlanGrandfathered(thisPlan.CompanyID, thisPlan.ClientID, thisPlan.EmployeeID, thisPlan.PlanName, thisPlan.SelectedCategory, thisPlan.YEAR);
            return plan;
        }

        public IEnumerable<ClientPlanHDR> GetEmployeeEligiblePlans(int companyId, string oeClientId, string oeEmployeeId, int oeId, string oeType, bool[] levents = null)
        {
            var currentEmployee = _employeeProvider.GetEmployeeEnrollmentEmployee(companyId, oeEmployeeId);
            var clientId = (currentEmployee != null) ? currentEmployee.ClientID : oeClientId;

            var employeeEnrollmentType = _employeeEnrollmentStatusProvider.GetEmployeeEnrollmentType(companyId, oeEmployeeId, oeId, oeType);

            var date = DateTime.Today;
            DateTime eestart, eehire, eerehire;
            var allPlans = _clientPlanHDRProvider.GetOpenEnrollmentPlansByClient(companyId, clientId);
            if (GlobalVariables.AdminEvent != "AdminEvent")
            {
                switch (employeeEnrollmentType)
                {
                    case Constants.EmployeeEnrollmentStatusEnrollmentTypes.Open:
                        allPlans = allPlans.Where(cph => cph.BeginEnrollment <= date && cph.EndEnrollment >= date).ToList();
                        break;
                    case Constants.EmployeeEnrollmentStatusEnrollmentTypes.NewHire:
                        var period = GetNewHireEnrollmentPeriod(companyId, clientId);
                        short dateIndex = GetNewHireDatesIndex(currentEmployee.StartDate, currentEmployee.OriginalHireDate, currentEmployee.RehireDate, period, out eestart, out eehire, out eerehire);
                        allPlans = NewHireAvailablePlans(allPlans, dateIndex, eestart, eehire, eerehire);
                        break;
                    case Constants.EmployeeEnrollmentStatusEnrollmentTypes.LifeEvent:
                        IEnumerable<ClientPlanHDR> avPlans = allPlans.Where(cph => cph.StartDate <= date && cph.EndDate >= date);
                        allPlans = _clientPlanHDRProvider.AvailablePlansForLifeEvent(avPlans, levents).ToList();
                        break;
                }
            }
            var plans = allPlans.Where(plan => IsEligiblePlan(companyId, oeEmployeeId, plan, currentEmployee)).ToList();

            return plans;
        }

        public bool IsEligiblePlan(int companyId, string oeEmployeeId, ClientPlanHDR plan, EmployeeEnrollmentEmployeeModel currentEmployee)
        {
            if (plan == null) return false;

            //Check is Plan Valid based on Eligibility Codes
            var planIsValid = _employeePlanEligibilityService.IsValidEligibilityCodes(companyId, plan.ClientID, oeEmployeeId, plan.PlanName, plan.YEAR1, Convert.ToChar("E"));
            if (!planIsValid) return false;

            //Check is Plan Valid based on Employee Class Codes/Department/Position
            planIsValid = _clientPlanEligibilityCodeProvider.IsEmployeeEligible(plan.PlanName, plan.YEAR1, currentEmployee);
            if (!planIsValid) return false;

            //check to see if plan restricts full/part time ee's
            planIsValid = IsWorkStatusEligible(plan, currentEmployee.PartTime, currentEmployee.WorkHoursPerYear);
            if (!planIsValid) return false;

            return _employeePlanEligibilityService.PlanIsValid(companyId, oeEmployeeId, plan, currentEmployee);
        }

        public bool IsWorkStatusEligible(ClientPlanHDR plan, byte? employeePartTime, int? employeeWorkHoursPerYear)
        {
            if (employeePartTime == null) return false;
            if (plan.FullTimeEmployeeEligible && (employeePartTime == 0 || employeePartTime == 1))
            {
                if ((plan.MinHoursFullTime ?? 0) == 0) return true;
                if (employeeWorkHoursPerYear >= (plan.MinHoursFullTime ?? 0)) return true;
            }
            if (plan.TempEmployeeEligible && employeePartTime == 2)
            {
                if ((plan.MinHoursTemp ?? 0) == 0) return true;
                if (employeeWorkHoursPerYear >= (plan.MinHoursTemp ?? 0)) return true;
            }
            if (plan.PartTimeEmployeeEligible && (employeePartTime == 3))
            {
                if ((plan.MinHoursPartTime ?? 0) == 0) return true;
                if (employeeWorkHoursPerYear >= (plan.MinHoursPartTime ?? 0)) return true;
            }
            if (plan.HourlyEmployeeEligible && employeePartTime == 4)
            {
                if ((plan.MinHoursHourly ?? 0) == 0) return true;
                if (employeeWorkHoursPerYear >= (plan.MinHoursHourly ?? 0)) return true;
            }
            if (plan.LOAEmployeeEligible && (employeePartTime == 6))
            {
                if ((plan.MinHoursLOA ?? 0) == 0) return true;
                if (employeeWorkHoursPerYear >= (plan.MinHoursLOA ?? 0)) return true;
            }
            if (plan.InternEmployeeEligible && (employeePartTime == 5))
            {
                if ((plan.MinHoursIntern ?? 0) == 0) return true;
                if (employeeWorkHoursPerYear >= (plan.MinHoursIntern ?? 0)) return true;
            }
            if (plan.PerDiemEmployeeEligible && (employeePartTime == 7))
            {
                if ((plan.MinHoursPerDiem ?? 0) == 0) return true;
                if (employeeWorkHoursPerYear >= (plan.MinHoursPerDiem ?? 0)) return true;
            }

            if (!plan.FullTimeEmployeeEligible && !plan.PartTimeEmployeeEligible && !plan.TempEmployeeEligible &&
                !plan.LOAEmployeeEligible && !plan.InternEmployeeEligible && !plan.PerDiemEmployeeEligible && !plan.HourlyEmployeeEligible) return true;

            return false;
        }

        public bool OpenEnrollmentAvailable(int companyId, string clientId)
        {
            return _clientPlanHDRProvider.OpenEnrollmentAvailable(companyId, clientId);
        }

        public int GetNewHireEnrollmentPeriod(int companyId, string clientId)
        {
            return _clientProvider.GetNewHireEnrollmentPeriod(companyId, clientId) ?? 0;
        }

        public bool GetOpenEnrollmentAvailable(int companyId, string clientId, string oeEmployeeId, short year, Employee currentEmployee, int oeId, string oeType, bool[] levents)
        {
            bool openEnrollmentAvailable = false;
            if (string.IsNullOrEmpty(oeType)) oeType = Constants.EmployeeEnrollmentStatusEnrollmentTypes.Open;
            if (currentEmployee != null)
            {
                DateTime? currentEmployeeStartDate = currentEmployee.StartDate;
                string socialSecurityNumber = currentEmployee.SSN;
                var employeeName = currentEmployee.FirstName + " " + currentEmployee.LastName;

                int newHireEnrollmentPeriod = GetNewHireEnrollmentPeriod(companyId, clientId);
                if (newHireEnrollmentPeriod > 0)
                {
                    double period = (double)newHireEnrollmentPeriod;
                    DateTime d1, d2, d3;
                    DateTime today = DateTime.Now.Date;
                    DateTime nullDate = new DateTime(1900, 1, 1);
                    DateTime eesDate = currentEmployeeStartDate ?? nullDate;
                    DateTime eehDate = currentEmployee.OriginalHireDate ?? nullDate;
                    DateTime eerDate = currentEmployee.RehireDate ?? nullDate;
                    DateTime eeStartDate = eesDate.AddDays(period);
                    DateTime eeHireDate = (eehDate).AddDays(period); // 04/18/2021 DS TFS # 7184
                    DateTime eeReHireDate = (eerDate).AddDays(period); // 04/18/2021 DS TFS # 7184
                    short dateIndex = GetNewHireDatesIndex(currentEmployeeStartDate, currentEmployee.OriginalHireDate, currentEmployee.RehireDate, (int)period, out d1, out d2, out d3);
                    short datetype = 0;
                    if (dateIndex > 0)
                    {
                        var nhPlanYear = _clientPlanHDRProvider.GetNewHirePlanYear(companyId, clientId, dateIndex, out datetype);
                        if (nhPlanYear != null)
                        {
                            switch (datetype)
                            {
                                case 2:
                                    eesDate = eehDate;
                                    eeStartDate = eeHireDate;
                                    break;
                                case 3:
                                    eehDate = eerDate != today ? eerDate : eehDate;
                                    eeStartDate = eerDate != today ? eeReHireDate : eeHireDate;
                                    break;
                            }
                            _employeeEnrollmentStatusProvider.CreateEmployeeEnrollmentStatusRecord(companyId, clientId, oeEmployeeId, (int)nhPlanYear, eeStartDate, eesDate, employeeName, socialSecurityNumber);
                            return true;
                        }
                    }
                }
            }

            bool openEnrollmentPlansAvailable = _clientPlanHDRProvider.OpenEnrollmentAvailable(companyId, clientId);
            var employeeEnrollmentType = _employeeEnrollmentStatusProvider.GetActiveEmployeeOpenEnrollmentStatus(companyId, oeEmployeeId);

            openEnrollmentAvailable = openEnrollmentPlansAvailable || employeeEnrollmentType != null;

            var isThisPersonEligible = GetEmployeeEligiblePlans(companyId, clientId, oeEmployeeId, oeId, oeType, levents);
            if (openEnrollmentPlansAvailable && isThisPersonEligible.Any(e => e.YEAR1 == year))
            {
                openEnrollmentAvailable = true;
            }
            else
            {
                openEnrollmentAvailable = false;
            }

            if (oeType == Constants.EmployeeEnrollmentStatusEnrollmentTypes.LifeEvent)
            {
                var lifeEventPlansAvailable = _clientPlanHDRProvider.LifeEventPlansAvailable(companyId, clientId, levents);

                if (lifeEventPlansAvailable && isThisPersonEligible.Any())
                {
                    openEnrollmentAvailable = true;
                }
            }

            return openEnrollmentAvailable;
        }

        public bool GetAdminOpenEnrollmentAvailable(int companyId, string clientId, short year)
        {
            var allPlans = _clientPlanHDRProvider.GetOpenEnrollmentPlansByClientAndYear(companyId, clientId, year);
            return allPlans != null && (allPlans.ToList()).Any();
        }
        public List<ClientPlanHDR> GetAdminOpenEnrollmentPlans(int companyId, string clientId, short year)
        {
            return _clientPlanHDRProvider.GetOpenEnrollmentPlansByClientAndYear(companyId, clientId, year);
        }

        public List<ClientPlanHDR> GetOpenEnrollmentPlansByClient(int companyId, string clientId)
        {
            return _clientPlanHDRProvider.GetOpenEnrollmentPlansByClient(companyId, clientId);
        }

        public List<ClientPlanHDR> GetOpenEnrollmentPlans()
        {
            return _clientPlanHDRProvider.GetOpenEnrollmentPlans();
        }

        public DateTime? OEEndDate(int companyId, string clientId)
        {
            return _clientPlanHDRProvider.OEEndDate(companyId, clientId);
        }

        public int GetAge(DateTime TheBirthDate, ClientPlanHDR Sabo)
        {
            DateTime compareDate = DateTime.Today;
            if (Sabo.AgeBanded)
                compareDate = DateTime.Today;
            else if (Sabo.SetAgeBasedOnEmployeeAgeAtOE)
                compareDate = (DateTime)Sabo.BeginEnrollment;
            else if (Sabo.EffectiveStartDate.HasValue)
                compareDate = (DateTime)Sabo.EffectiveStartDate;

            var currentAge = compareDate.Year - TheBirthDate.Year;
            if (TheBirthDate > compareDate.AddYears(-currentAge)) currentAge--;

            return currentAge;
        }

        public DateTime PlanEffectiveDate(int companyId, string clientId, string employeeId, ClientPlanHDR plan, out DateTime startDateOut) // 03/09/2018 DS TFS # 3070
        {
            var employeeStartDate = _employeeProvider.GetEmployeeStartDate(companyId, clientId, employeeId);
            var planEffectiveDate = PlanEffectiveDate(employeeStartDate, plan, out DateTime startDate);

            startDateOut = startDate;
            return planEffectiveDate;
        }

        public DateTime PlanEffectiveDate(DateTime? employeeStartDate, ClientPlanHDR plan, out DateTime startDate) 
        {
            DateTime firstDayCurrentYear = new DateTime(DateTime.Now.Year, 1, 1);
            DateTime result = plan.EffectiveStartDate ?? firstDayCurrentYear;
            startDate = plan.StartDate ?? firstDayCurrentYear;

            if (employeeStartDate > result) result = employeeStartDate ?? firstDayCurrentYear;
            if (employeeStartDate > startDate) startDate = employeeStartDate ?? firstDayCurrentYear;

            if (plan.WaitingPeriodRequired)
            {
                DateTime tempStartDate = AdjustDateWithWaitingPeriod(startDate, plan.WaitingPeriod ?? 0, plan.WaitingPeriodDaysMonth, plan.PTOFirstOfNextMonth1);
                DateTime tempResult = AdjustDateWithWaitingPeriod(result, plan.BenefitAfterWaitingPeriod ?? 0, plan.BenefitWaitingPeriodDate, plan.PTOFirstOfNextMonth2);
                if (tempStartDate > startDate) startDate = tempStartDate;
                if (tempResult > result) result = tempResult;
            }
            return result;
        }

        public decimal GetSTDPremium(int companyId, ClientPlanHDR thePlan, short category, EmployeeEnrollmentEmployeeModel employee)
        {
            decimal thePremium = decimal.Zero;
            if (employee == null) return thePremium;

            decimal factor = thePlan.BenefitFactor.GetValueOrDefault();
            if (factor == decimal.Zero) factor = (decimal)0.10;

            var theEEAnnualWage = GetEEAnnualWage(employee, thePlan.UsePrimaryCode);

            var theWeeklyWage = (theEEAnnualWage / (decimal)52) * ((decimal)thePlan.BenefitPercent / (decimal)10000);
            if (theWeeklyWage > thePlan.MaxBenefitWeekly) theWeeklyWage = thePlan.MaxBenefitWeekly.GetValueOrDefault();
            var benefitwage = (theWeeklyWage * factor); // ???

            var eeMods = SetModifiers(employee, thePlan);

            var theRates = (category == 0) ? _clientPlanRateGridService.GetRates(companyId, thePlan, 1, eeMods) : _clientPlanRateGridService.GetRates(companyId, thePlan, category, eeMods);
            thePremium = (theRates.PlanAmount ?? 0) * benefitwage;
            return thePremium;
        }

        public decimal GetLTDPremium(int companyId, ClientPlanHDR thePlan, short category, EmployeeEnrollmentEmployeeModel employee)
        {
            decimal thePremium = decimal.Zero;
            if (employee == null) return thePremium;

            decimal factor = thePlan.BenefitFactor.GetValueOrDefault();
            if (factor == decimal.Zero) factor = (decimal)0.01;

            var theEEAnnualWage = GetEEAnnualWage(employee, thePlan.UsePrimaryCode);

            var theMonthlyWage = (theEEAnnualWage / (decimal)12) * ((decimal)thePlan.BenefitPercent / (decimal)10000);
            if (theMonthlyWage > thePlan.MaxBenefitMonthly) theMonthlyWage = thePlan.MaxBenefitMonthly.GetValueOrDefault();
            var benefitwage = theMonthlyWage * factor;

            var eeMods = SetModifiers(employee, thePlan);

            var theRates = (category == 0) ? _clientPlanRateGridService.GetRates(companyId, thePlan, 1, eeMods) : _clientPlanRateGridService.GetRates(companyId, thePlan, category, eeMods);
            thePremium = (theRates.PlanAmount ?? 0) * benefitwage;
            return thePremium;
        }

        public decimal GetEEAnnualWage(EmployeeEnrollmentEmployeeModel employee, bool usePrimaryCode)
        {
            int workHoursPerYear = employee.WorkHoursPerYear ?? 0;
            if (usePrimaryCode)
            {
                var primaryCode = employee.EmployeePaycodes.FirstOrDefault(epc => epc.PayRecord == employee.PrimaryPayRecord);
                if (primaryCode != null) //return theEEAnnualWage;
                {
                    decimal payRate = decimal.Zero;
                    if (primaryCode.PayType == PayTypes.Hourly) payRate = primaryCode.PayRateAmount ?? decimal.Zero;
                    if (primaryCode.PayType == PayTypes.Salary)
                    {
                        payRate = primaryCode.PayRateAmount ?? decimal.Zero;
                        payRate =
                            (decimal)
                                GetSalaryHourlyRate(primaryCode.PayUnitPeriod ?? 0, Convert.ToDouble(payRate), workHoursPerYear);
                    }
                    return (decimal)(payRate * workHoursPerYear);
                }
            }
            return employee.AnnualWages ?? decimal.Zero;
        }

        private RateGridModifiers SetModifiers(EmployeeEnrollmentEmployeeModel employee, ClientPlanHDR plan)
        {
            return new RateGridModifiers
            {
                Smoker = Convert.ToBoolean(employee.Smoker),
                Wellness = Convert.ToBoolean(employee.Wellness),
                State = employee.EmployeeAddresses.FirstOrDefault(ea => ea.AddressCode == employee.AddressCode).State ?? string.Empty,
                Other1 = employee.Other1,
                Other2 = employee.Other2,
                Other3 = employee.Other3,
                Other4 = employee.Other4,
                Age = (employee.BirthDate == null) ? 0 : GetAge((DateTime)employee.BirthDate, plan)
            };
        }

        public double GetSalaryHourlyRate(short frequency, double rate, string employeeId, int companyId)
        {
            var employee = _employeeProvider.GetEmployeeEnrollmentEmployee(companyId, employeeId);
            return GetSalaryHourlyRate(frequency, rate, employee.WorkHoursPerYear);
        }

        public double GetSalaryHourlyRate(short frequency, double rate, int? employeeWorkHoursPerYear)
        {
            var hoursPerYear = employeeWorkHoursPerYear ?? 0;
            if (hoursPerYear == 0) return rate;
            var tmpRate = rate;
            switch (frequency)
            {
                case PayPeriods.Empty:
                    break;
                case PayPeriods.Weekly:
                    tmpRate = (rate * 52) / hoursPerYear;
                    break;
                case PayPeriods.Biweekly:
                    tmpRate = (rate * 26) / hoursPerYear;
                    break;
                case PayPeriods.Semimonthly:
                    tmpRate = (rate * 24) / hoursPerYear;
                    break;
                case PayPeriods.Monthly:
                    tmpRate = (rate * 12) / hoursPerYear;
                    break;
                case PayPeriods.Quarterly:
                    tmpRate = (rate * 4) / hoursPerYear;
                    break;
                case PayPeriods.Semiannually:
                    tmpRate = (rate * 2) / hoursPerYear;
                    break;
                case PayPeriods.Annually:
                    tmpRate = (rate) / hoursPerYear;
                    break;
                case PayPeriods.DailyMiscellaneous:
                    tmpRate = (rate * 365) / hoursPerYear;
                    break;
            }
            return tmpRate;
        }

        public List<ClientPlanHDR> NewHireAvailablePlans(List<ClientPlanHDR> avPlans, short index, DateTime eestart, DateTime eehire, DateTime eerehire)
        {
            List<ClientPlanHDR> result = avPlans.OrderByDescending(p => p.YEAR1).ToList();
            if (avPlans.Any())
            {
                switch (index)
                {
                    case 1:
                        result = avPlans.Where(p => p.PlanDatesHireDateStartDate != 0 && p.StartDate <= eestart && p.EndDate >= eestart).OrderByDescending(p => p.YEAR1).ToList();
                        break;
                    case 2:
                        result = avPlans.Where(p => p.PlanDatesHireDateStartDate == 0 && p.StartDate <= eehire && p.EndDate >= eehire).OrderByDescending(p => p.YEAR1).ToList();
                        break;
                    case 3:
                        result = avPlans.Where(p => (p.StartDate <= eestart && p.EndDate >= eestart) || (p.StartDate <= eehire && p.EndDate >= eehire)).OrderByDescending(p => p.YEAR1).ToList();
                        break;
                    case 4:
                        result = avPlans.Where(p => p.PlanDatesBasedDateOfHire && (p.StartDate <= eerehire && p.EndDate >= eerehire)).OrderByDescending(p => p.YEAR1).ToList();
                        break;
                    case 5:
                        result = avPlans.Where(p => (p.PlanDatesHireDateStartDate != 0 || p.PlanDatesBasedDateOfHire) || ((p.StartDate <= eestart && p.EndDate >= eestart) || (p.StartDate <= eerehire && p.EndDate >= eerehire))).OrderByDescending(p => p.YEAR1).ToList();
                        break;
                    case 6:
                        result = avPlans.Where(p => (p.PlanDatesHireDateStartDate == 0 || p.PlanDatesBasedDateOfHire) && ((p.StartDate <= eehire && p.EndDate >= eehire) || (p.StartDate <= eerehire && p.EndDate >= eerehire))).OrderByDescending(p => p.YEAR1).ToList();
                        break;
                    case 7:
                        result = avPlans.Where(p => (p.StartDate <= eestart && p.EndDate >= eestart) || (p.StartDate <= eehire && p.EndDate >= eehire) || (p.StartDate <= eerehire && p.EndDate >= eerehire)).OrderByDescending(p => p.YEAR1).ToList();
                        break;
                }
            }
            return result;
        }

        public short GetNewHireDatesIndex(DateTime? start, DateTime? hire, DateTime? rehire, int period, out DateTime eeStartDate, out DateTime eeHireDate, out DateTime eeReHireDate)
        {
            short index = 0;
            DateTime today = DateTime.Now.Date;
            DateTime nullDate = new DateTime(1900, 1, 1);
            DateTime eesDate = start ?? nullDate;
            DateTime eehDate = hire ?? nullDate;
            DateTime eerDate = rehire ?? nullDate;
            eeStartDate = eesDate.AddDays(period);
            eeHireDate = (eehDate).AddDays(period);
            eeReHireDate = (eerDate).AddDays(period);
            if (eeStartDate >= today) index++;
            if (eeHireDate >= today) index += 2;
            if (eeReHireDate >= today) index += 4;
            return index;
        }

        private DateTime AdjustDateWithWaitingPeriod(DateTime date, short period, short? method, bool firstDay)
        {
            DateTime result = date;
            if (period > 0) result = (method == 2) ? date.AddMonths(period) : date.AddDays(period);
            if (firstDay && result.Day > 1)
            {
                result = result.AddMonths(1);
                result = new DateTime(result.Year, result.Month, 1, 0, 0, 0, result.Kind);
            }
            return result;
        }
    }
}