using System;
using System.Collections.Generic;

namespace Thinkware.Pay360
{
    // TODO: Figure out why we use short to define ColType properties but define byte here.
    public struct PaycodeType
    {
        public const byte Unknown = 0;
        public const byte Hourly = 1;
        public const byte Salary = 2;
        public const byte Piecework = 3;
        public const byte Commission = 4;
        public const byte BusinessExp = 5;
        public const byte Overtime = 6;
        public const byte DoubleTime = 7;
        public const byte Vacation = 8;
        public const byte Sick = 9;
        public const byte Holiday = 10;
        public const byte Pension = 11;
        public const byte EIC = 13;
        public const byte Other = 12;
        public const byte ChargedTips = 14;
        public const byte ReportedTips = 15;
        public const byte MinWageBalance = 16;
        public const byte PTO1 = 17;
        public const byte PTO2 = 18;
        public const byte PTO3 = 19;
        public const byte PTO4 = 20;
        public const byte PTO5 = 21;
        public const byte PTO6 = 22;
        public const byte PTO7 = 23;
        public const byte PTO8 = 24;
        public const byte PTO9 = 25;
        public const byte PTO10 = 26;
        public const byte Misc1 = 27;
        public const byte Misc2 = 28;
        public const byte Misc3 = 29;
        public const byte Misc4 = 30;
        public const byte Misc5 = 31;
        public const byte Misc6 = 32;
        public const byte Misc7 = 33;
        public const byte Misc8 = 34;
        public const byte Misc9 = 35;
        public const byte Misc10 = 36;
        public const byte Benefit = 254;
        public const byte Deduction = 255;

        public static readonly HashSet<byte> ShiftedCodes = new HashSet<byte>
        (
            new byte[]
            {
                    PaycodeType.Hourly,
                    PaycodeType.Commission,
                    PaycodeType.Piecework,
                    PaycodeType.BusinessExp,
                    PaycodeType.Overtime,
                    PaycodeType.DoubleTime,
                    PaycodeType.Vacation,
                    PaycodeType.Sick,
                    PaycodeType.Holiday,
                    PaycodeType.Other
            }
        );

        public static readonly HashSet<byte> JustRateCodes = new HashSet<byte>
        (
            new byte[]
            {
                    PaycodeType.Commission,
                    PaycodeType.BusinessExp,
                    PaycodeType.Other,
                    PaycodeType.MinWageBalance,
                    PaycodeType.ChargedTips,
                    PaycodeType.ReportedTips,
                    PaycodeType.Benefit,
                    PaycodeType.Deduction
            }
        );

        public static readonly HashSet<byte> TipsCodes = new HashSet<byte>
        (
            new byte[]
            {
                    PaycodeType.ChargedTips,
                    PaycodeType.ReportedTips
            }
        );

        public static readonly HashSet<byte> PTOCodes = new HashSet<byte>
        (
            new byte[]
            {
                    PaycodeType.PTO1,
                    PaycodeType.PTO2,
                    PaycodeType.PTO3,
                    PaycodeType.PTO4,
                    PaycodeType.PTO5,
                    PaycodeType.PTO6,
                    PaycodeType.PTO7,
                    PaycodeType.PTO8,
                    PaycodeType.PTO9,
                    PaycodeType.PTO10,
                    PaycodeType.Vacation,
                    PaycodeType.Sick,
                    PaycodeType.Misc1,
                    PaycodeType.Misc2,
                    PaycodeType.Misc3,
                    PaycodeType.Misc4,
                    PaycodeType.Misc5,
                    PaycodeType.Misc6,
                    PaycodeType.Misc7,
                    PaycodeType.Misc8,
                    PaycodeType.Misc9,
                    PaycodeType.Misc10
            }
        );

        public static bool IsShiftedCode(short codeType) => ShiftedCodes.Contains((byte)codeType);
        public static bool IsPTOCode(short codeType) => PTOCodes.Contains((byte)codeType);
        public static bool IsJustRateCode(short codeType) => JustRateCodes.Contains((byte)codeType);
        public static bool IsBusinessExp(short codeType) => codeType == BusinessExp;
    }
}
