using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.ViewModels.Dashboard
{
    public class IndexVM
    {
        public List<UserToDoItem> ToDoItems { get; set; }
        public string NewsItems { get; set; }
        public int ReadNews { get; set; }
        public List<NewsItem> TheNews { get; set; }
        public bool NoPreview { get; set; }
        public DBrdLastInvoice LastInvoice { get; set; }
        public DBrdLastInvoice PreviousInvoice { get; set; }
        public int InvoiceCount { get; set; }
        public string DashboardItems { get; set; }
        public bool NoPending { get; set; }
        public string PendingPTORequests { get; set; }
        public int Access { get; set; }
        public List<Announcement> Announcements { get; set; }
    }
}