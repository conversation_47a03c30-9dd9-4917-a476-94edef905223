using System;
using System.Linq;

namespace DarwiNet2._0.ViewModels
{
    public class SSRSReportViewerVM
    {
        public int ReportID { get; set; }
        public string ClientID { get; set; }
        public string company { get; set; }

        public int Invoice { get; set; }
        public string StartDate { get; set; }
        public string EndDate { get; set; }
        public int InvoiceEnd { get; set; }
        public string Division { get; set; }
        public string EmployeeIDStart { get; set; }
        public string EmployeeIDEnd { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }

        public string PlanType { get; set; }
        public string BenefitType { get; set; }
        public string PlanName { get; set; }
        public int Year { get; set; }
        public int Month { get; set; }
        public int DisplayType { get; set; }
        public string EmployeeId { get; set; }
        public string HasDetail { get; set; }

        public string Codes { get; set; }
        public string Codes2 { get; set; }
        public string PayrollNo { get; set; }
        public string Migrated { get; set; }
        public string Preview { get; set; }
        


    }
}