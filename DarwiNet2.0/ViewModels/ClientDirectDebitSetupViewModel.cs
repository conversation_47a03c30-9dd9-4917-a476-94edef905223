using System;
using System.Collections.Generic;
using System.Web;
using System.Web.Mvc;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.ViewModels
{
    public class ClientDirectDebitSetupViewModel
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public byte? ClientType { get; set; }
        public string DivisionID { get; set; }
        public string DivisionName { get; set; }
        public bool UseDirectDebit { get; set; }
        public bool PreNote { get; set; }
        public string BankID { get; set; }
        public string BankName { get; set; }
        public byte? AccountType { get; set; }
        public string AccountNumber { get; set; }
        public string ReceivablesProfileID { get; set; }
        public bool ReceivablesOffsetRecord { get; set; }
        public string ReceivablesOffsetCheckbookID { get; set; }
        public int? ReceivablesOffsetDays { get; set; }
        public bool BypassDirectDebitWindow { get; set; }
        public bool AutoApplyCashReceipts { get; set; }
        public bool SaveCashReceiptsInBatch { get; set; }
        public string CheckbookID { get; set; }
        public IList<SelectListItem> AccountTypes { get; set; }
        public IList<SelectListItem> Checkbooks { get; set; }
        public IList<SelectListItem> Profiles { get; set; }
    }
}