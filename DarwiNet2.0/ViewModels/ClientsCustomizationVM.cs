using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.Clients;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.ViewModels
{
    public class ClientsCustomizationVM
    {
        public List<Code_Description> SSNMaskType;
        public List<string> ClientIDList;
        public DarwinetSetup ClientSetup { get; set; }
        public List<string> AvailableEmployeeRoles { get; set; }
        public List<Code_Description> ClientIDs { get; set; }
        public List<Code_Description> OBProfiles { get; set; }
        public string SelectedClient { get; set; }
        public string SelectedOBProfile { get; set; }
        public List<Code_Description> CheckPrinterProfiles { get; set; }
        public CheckPrinterPreference SelectedProfile { get; set; }
        public string LogoURLBIG { get; set; }
        public string LogoURLSmall { get; set; }
        public int ClientImageLarge { get; set; }
        public int ClientImageSmall { get; set; }
        public string ClientPayStub { get; set; }
        public List<string> AvailablePayStubs { get; set; }
        public int Access { get; set; }
        public List<Code_Description> GH_Users { get; set; }
        public string GH_EmployerID { get; set; }
        public bool ManageLoginAs { get; set; }
        public bool CanSetImport { get; set; }
        public KronosIntegrationSettingsVM KronosIntegrationSettings { get; set; }
        public HireologyIntegrationSettingsVM HireologyIntegrationSettings { get; set; }
    }
}