using System;
using System.Collections.Generic;
using System.Linq;
using DarwiNet2._0.Models;
using DarwiNet2._0.Data;
using System.ComponentModel;
using System.Web.Mvc;

namespace DarwiNet2._0.ViewModels
{
    public class OptionsVM
    {
        public ExportSolutionRpt ExportSolutionRpts { get; set; }

        [DisplayName("Detail Level")]
        public int DetailLevel { get; set; }
        public List<SelectListItem> DetailLevels { get; set; }

        public IEnumerable<DisplayOption> DisplayOptions { get; set; }
        

        [DisplayName("Subtotal Options")]
        public int SubTotalOption { get; set; }
        public List<SelectListItem> SubTotalOptions { get; set; }
    }
}