using DarwiNet2._0.Controllers;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Models.Core;
using System;
using System.Collections.Generic;

namespace DarwiNet2._0.ViewModels.NIL
{
    public class DealDetailsViewModel
    {
        public DealViewModel Deal { get; set; }
        public List<EnumCodeText> DealStatus { get; set; }
        public List<EnumCodeText> DealAthletePaymentStatus { get; set; }
        public List<Code_Description> DealTypes { get; set; }
        public List<Code_Description> DealActivityTypes { get; set; }
        public List<EmployeeFilterDetail> Employees { get; set; }
        public List<Code_Description> Positions { get; set; }
    }

    public class DealViewModel
    {
        public int? CompanyID { get; set; }
        public string ClientID { get; set; }
        public long? DealID { get; set; }
        public string Title { get; set; }
        public string Type { get; set; }
        public string ActivityType { get; set; }
        public short? Status { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public decimal? OfferAmount { get; set; }
        public string Description { get; set; }
        public string Position { get; set; }
        public DealContactViewModel Contact { get; set; }
        public int? AmountType { get; set; }
        public bool CompletedMessage { get; set; }
        public List<DealAthletePaymentViewModel> DealAthletePayments { get; set; }
    }

    public class DealContactViewModel
    {
        public string ContactID { get; set; }
        public string Name { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
    }

    public class DealAthletePaymentViewModel
    {
        public int? CompanyID { get; set; }
        public string ClientID { get; set; }
        public long? DealID { get; set; }
        public string EmployeeID { get; set; }
        public DealEmployeeDTO Employee { get; set; }
        public short? Status { get; set; }
        public decimal? DealAmount { get; set; }
    }
}