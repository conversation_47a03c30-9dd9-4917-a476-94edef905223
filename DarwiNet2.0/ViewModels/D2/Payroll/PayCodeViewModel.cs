using System.ComponentModel.DataAnnotations;

namespace DarwiNet2._0.ViewModels.D2
{
    /// <summary>
    ///     
    /// </summary>
    /// <history>
    ///     [mframe]    04/15/20    Task-5935: Created.
    /// </history>
    public class PayCodeViewModel
    {
        public string Id { get; set; }

        [Display(Name = "Pay Code")]
        public string Name { get; set; }

        [Display(Name = "Hours")]
        public decimal Hours { get; set; }

        [Display(Name = "Rate/Amount")]
        public decimal RateOrAmount { get; set; }

        [Display(Name = "Department")]
        public string Department { get; set; }

        [Display(Name = "Position")]
        public string Position { get; set; }

        [Display(Name = "Gross Wages")]
        public decimal GrossWages { get { return Hours == decimal.Zero ? RateOrAmount : decimal.Multiply(Hours, RateOrAmount); } }
    }
}