using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.ViewModels.D2
{
    public class PayrollFinalizeViewModel
    {
        public string CurrentPayrollNumber { get; set; }
        public List<TableFilterWithName> StoredFilters { get; set; }
        public List<FinalizeTableRow> TableData { get; set; }
        public bool IsClient { get; set; }
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
    }

    public class FinalizeTableRow
    {
        public int CompanyID { get; set; }
        public string CompanyName { get; set; }
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public string DivisionID { get; set; }
        public List<FinalizePayrollDetails> PayrollDetails {get; set;}
    }

    public class FinalizePayrollDetails
    {
        public string ProfileID { get; set; }
        public string PayrollNumber { get; set; }
        public string CheckDate { get; set; }
        public string Processor { get; set; }
        public double GrossWages { get; set; }
        public double InvoiceTotal { get; set; }
        public int InvoiceNumber { get; set; }
        public int NumberofEE { get; set; }
        public int NumberofChecks { get; set; }
        public string PayPeriodBegin { get; set; }
        public string PayPeriodEnd { get; set; }
        public double NetWages { get; set; }
        public string ProcessDate { get; set; }
        public FinalizeApprovalDetails PayrollApprovalDetails { get; set; }
        public FinalizeApprovalDetails InvoiceApprovalDetails { get; set; }
        public List<FinalizeDivisionalInvoice> DivisionalInvoices { get; set; }
    }

    public class FinalizeApprovalDetails
    {
        public string Recipient { get; set; }
        public string ApprovedDateTime { get; set; }
        public string Comments { get; set; }
        public string ContactMethod { get; set; }
    }

    public class FinalizeDivisionalInvoice
    {
        public int InvoiceNumber { get; set; }
        public string DivisionID { get; set; }
        public double GrossWages { get; set; }
        public string InvoiceDate { get; set; }
        public double InvoiceTotal { get; set; }
        public int NumberofEE { get; set; }
        public int NumberofChecks { get; set; }
    }
}