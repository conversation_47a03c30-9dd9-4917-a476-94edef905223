using DarwiNet2._0.Controllers;
using System.Collections.Generic;
using System.Web.Mvc;

namespace DarwiNet2._0.ViewModels.D2.PayrollProfileSettings
{
    /// <summary>
    ///     
    /// </summary>
    /// <history>
    ///     [mframe]    03/27/20    Task-5879: Created history. Removed PageAccess property.
    ///     [lyeager]   10/01/20    Task-6861: Add properties from PayrollProfileSettingsCreateViewModel.
    /// </history>
    public partial class PayrollProfileSettingsIndexViewModel
    {
        public int CompanyID { get; set; }
        public PayrollProfileSettingsFiltersViewModel PayrollProfileSettingsFilters { get; set; }
        public bool IsClient { get; set; }
    }
}