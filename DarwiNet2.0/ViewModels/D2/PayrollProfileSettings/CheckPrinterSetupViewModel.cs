using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;

namespace DarwiNet2._0.ViewModels.D2.PayrollProfileSettings
{
    public class CheckPrinterSetupViewModel
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string ProfileID { get; set; }
        public bool Inactive { get; set; }
        public bool Default { get; set; }
        public Nullable<int> CheckFileID { get; set; }
        public bool SummarizeByRate { get; set; }
        public bool SummarizeByDepartment { get; set; }
        public bool SummarizeByPosition { get; set; }
        public bool SplitByJobCosting { get; set; }
        public bool IncludeManualChecks { get; set; }
        public bool PrintInactive { get; set; }
        public bool PrintYTD { get; set; }
        public bool PrintDeposits { get; set; }
        public bool UseTransactionDepartment { get; set; }
        public byte NameFormat { get; set; }
        public byte MaxPaycodes { get; set; }
        public byte MaxBenefits { get; set; }
        public byte MaxDeductions { get; set; }
        public byte MaxStateTaxes { get; set; }
        public byte MaxLocalTaxes { get; set; }
        public byte MaxDeposits { get; set; }
        public bool UseDepositPrinter { get; set; }
        public bool UseDefaultPrinterForChecks { get; set; }
        public bool UseDefaultPrinterForDeposits { get; set; }
        public List<Code_Description> CheckFiles { get; set; }
        public PayrollProfileSettingsHeaderModel PayrollProfileSettingsHeaderViewModel { get; set; }
    }
}