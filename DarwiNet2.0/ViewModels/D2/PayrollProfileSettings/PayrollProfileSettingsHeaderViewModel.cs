using DarwiNet2._0.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.ViewModels.D2.PayrollProfileSettings
{
    public class PayrollProfileSettingsHeaderViewModel
    {
        public int CompanyID { get; set; }
        public string CompanyName { get; set; }
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public string ProfileID { get; set; }
        public string CheckCheckbookID { get; set; }
        public string DepositCheckbokID { get; set; }
        public bool Inactive { get; set; }
        public int? PayrollTeamID { get; set; }
        public string PayrollTeamName { get; set; }
        public bool PriorityPayroll { get; set; }
        public string FirstPayroll { get; set; }
        public bool ManualCheckProfile { get; set; }
        public List<Code_Description> PayrollTeamsList { get; set; }
    }
}