using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.ViewModels.D2.InvoiceReview
{
    public class TaxViewModel
    {
        public List<CodeViewModel> Taxes;
        public decimal TaxesTotal => Taxes.Sum(t => t.Amount);
        public decimal PreviousTaxesTotal => Taxes.Sum(t => t.PreviousAmount);
    }

    public class TaxesViewModel
    {
        public TaxesRecords FicaMedicare { get; set; }
        public TaxesRecords FicaSocialSecurity { get; set; }
        public TaxesRecords Futa { get; set; }
        public TaxesRecords Suta { get; set; }
        public TaxesRecords WorkersComp { get; set; }
        public decimal? TaxesTotal { get; set; }
    }

    public class TaxesRecords
    {
        public bool VarianceTripped { get; set; }
        public List<VarianceRecord> VarianceRecords { get; set; }
        public bool ControlTripped { get; set; }
        public List<ControlRecord> ControlRecords { get; set; }
        public List<EmployeeRecords> EmployeeRecords { get; set; }
        public decimal Total { get; set; }
    }

    public class EmployeeRecords
    {
        public string FeeCode { get; set; }
        public string FeeDescription { get; set; }
        public string EmployeeID { get; set; }
        public string CodeType { get; set; }
        public string EmployeeName { get; set; }
        public decimal? Amount { get; set; }
        public string State { get; set; }
    }
}