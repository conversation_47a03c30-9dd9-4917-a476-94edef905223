using DarwiNet2._0.ViewModels.D2.PayrollReview;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.ViewModels.D2.PayrollReview
{
    public class PayrollReviewViewModel : PayrollViewModelBase
    {
        public string PayrollNumber { get; set; }
        public PayrollReviewWagesTotalsViewModel WagesTotals { get; set; }
        public PayrollReviewTaxesTotalsViewModel TaxesTotals { get; set; }
        public PayrollReviewBenefitsTotalsViewModel BenefitsTotals { get; set; }
        public PayrollReviewDeductionsTotalsViewModel DeductionsTotals { get; set; }
        public PayrollReviewChecksListViewModel ChecksList { get; set; }
        public int PCDecimals { get; set; }
        public int BenDecimals { get; set; }
        public int DedDecimals { get; set; }
        public int NumNotes { get; set; }
        public List<ControlsAndVariance> ControlsAndVariances { get; set; }
        public List<ControlRecord> ControlRecords { get; set; }
        public List<VarianceRecord> VarianceRecords { get; set; }
        public bool HasProfileControlsVariances { get; set; }
        public bool IsClient { get; set; }
        public bool IsVoid { get; set; }
        public List<PayrollProfileReport> PayrollProfileReportControls { get; set; }
        public bool CanViewItemsByApproval { get; set; }
        public bool fromApprovals { get; set; }
        public bool fromCompletedPayrolls { get; set; }
        public bool fromPayrollDashboard { get; set; }
        public bool fromPayrollProfile { get; set; }
        public int fromPayrollProfileCompany { get; set; }
        public string fromPayrollProfileProfile { get; set; }
        public string fromPayrollProfileClient { get;  set; }
        public ProfileSettingsViewModel ProfileSettingsViewModel { get; set; }
        public bool CanPayroll { get; set; }
        public List<Data.SSRSReport> SSRSReportControls { get; set; }
        public bool RequiresRecalculation { get; set; }
        public string UserID { get; set; }
        public bool AutoInvoiceApprovals { get; set; }
        public bool IsPayrollUser { get; set; }
        public bool AutoMissedCodes { get; set; }
        public MergedInvoicesInfoViewModel MergedInvoicesInfo { get; set; }
        public byte ManualCheckType { get; set; }
    }


}