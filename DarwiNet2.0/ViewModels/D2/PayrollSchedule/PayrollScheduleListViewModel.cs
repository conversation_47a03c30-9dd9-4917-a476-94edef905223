using DarwiNet2._0.DTOs;
using DarwiNet2._0.ViewModels.D2.PayrollProfileSettings;
using System.Collections.Generic;

namespace DarwiNet2._0.ViewModels.D2.PayrollSchedule
{
    /// <summary>
    ///     
    /// </summary>
    /// <history>
    ///     [mframe]    04/07/20    Task-5929: Created.
    ///     [lyeager]   09/14/20    Task-6852: Replaced PayrollScheduleViewModelBase with IPayrollViewModel, IHaveControllerAndAction. Implemented PayrollProfileSettingsHeaderModel. Added Controller and Action.
    /// </history>
    //public class PayrollScheduleListViewModel : PayrollScheduleViewModelBase
    public class PayrollScheduleListViewModel : IPayrollViewModel, IHaveControllerAndAction
    {
        public List<PayrollScheduleTableRow> TableData { get; set; }
        public PayrollProfileSettingsHeaderViewModel PayrollProfileSettingsHeader { get; set; }
        public string Controller { get; set; }
        public string Action { get; set; }
    }
}