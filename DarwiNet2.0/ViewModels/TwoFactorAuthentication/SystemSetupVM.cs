using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.ViewModels.TwoFactorAuthentication
{
    public class SystemSetupVM
    {
        public int Access { get; set; }

        public string SuccessMessage { get; set; }

        [DisplayName("Enable Text/SMS Authentication")]
        public bool EnableTwoFactorSms { get; set; }

        [DisplayName("Enable Email Authentication")]
        public bool EnableTwoFactorEmail { get; set; }

        public bool IsSmsProviderConnected { get; set; }
    }
}