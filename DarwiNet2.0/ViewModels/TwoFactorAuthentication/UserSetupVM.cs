using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DarwiNet2._0.ViewModels.TwoFactorAuthentication
{
    public class UserSetupVM
    {
        public string SuccessMessage { get; set; }
        public string InfoMessage { get; set; }
        public string ErrorMessage { get; set; }

        [Display(Name = "Enable Text/SMS Authentication")]
        public bool EnableTwoFactorSms { get; set; }

        [Display(Name = "Phone Number")]
        [Required(ErrorMessage = "Please enter a phone number.")]
        [RegularExpression(@"^((\(\d{3}\))|(\d{3}))[\s.-]?\d{3}[\s.-]?\d{4}$", ErrorMessage = "Please enter a valid phone number.")]
        public string TwoFactorPhoneNumber { get; set; }
        
        public string TwoFactorPhoneNumberOriginal { get; set; }

        [Display(Name = "Enable Email Authentication")]
        public bool EnableTwoFactorEmail { get; set; }

        [Display(Name = "Email")]
        [Required(ErrorMessage = "Please enter an email address.")]
        [RegularExpression(@"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$", ErrorMessage = "Please enter a valid email address.")]
        public string TwoFactorEmail { get; set; }
        
        public string TwoFactorEmailOriginal { get; set; }

        public bool IsTwoFactorSmsEnabledByCompany { get; set; }
        public bool IsTwoFactorEmailEnabledByCompany { get; set; }

        public bool IsTwoFactorPhoneNumberVerified { get; set; }
        public bool IsTwoFactorEmailVerified { get; set; }

        public bool SmsNeedsVerification { get; set; }
        public bool EmailNeedsVerification { get; set; }
        
        public bool OpenVerifyPhoneNumberModal { get; set; }
        public bool OpenVerifyEmailModal { get; set; }

        public bool IsMfaMandatoryForUser { get; set; }
        public bool UpdatePhoneNumber { get; set; }
    }
}