using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.ViewModels
{
    public class EmployeesInfoVM
    {
        public Employee Employee { get; set; }
        public bool CanRollDownWC { get; set; }
        public bool CanRollDownSUTA { get; set; }

        public string Key1 { get; set; }
        public string Key2 { get; set; }
        public string Key3 { get; set; }
        public string Key4 { get; set; }
        public int Access { get; set; }
        public EmployeeAddress EEAddress { get; set; }
        public EmployeeSuccessorSUTATax EmployeeSuccessorSUTATax { get; set; }
        public EmployeeSuccessorTax EmployeeSuccessorTaxes { get; set; }
        public int NoteCount { get; set; }
        public int DocumentCount { get; set; }
        public List<Code_Description> States { get; set; }
        public List<Code_Description> DivisionalSUTAStates { get; set; }
        public List<Code_Description> WorkersComp { get; set; }
        public List<Code_Description> DivisionalWorkersComp { get; set; }
        public List<Code_Description> SUTAState { get; set; }
        public List<Code_Description> SUTAState1 { get; set; }
        public List<Code_Description> SUTAYear { get; set; }
        public List<Item_Description> SUTAYear1 { get; set; }
        public List<Code_Description> Departments { get; set; }
        public List<Code_Description> Positions { get; set; }
        public List<Code_Description> EmployeeClass { get; set; }
        public List<Code_Description> WorksiteAddresses { get; set; }
        public List<Code_Description> HomeAddresses { get; set; }
        public EmployeeTermsList EmpTerm { get; set; }
        public string BEClass { get; set; }
        public List<Code_Description> Locations { get; set; }
        public EmployeePaycode EmpPayCodes { get; set; }
        public Client clients { get; set; }
        public bool AllowEmployeePhotoUploadsClient { get; set; }
        public int SSNDisplay { get; set; }
        public int SuccessorSutaTaxCount { get; set; }
        public string PayrollNumber {  get; set; }
    }
}