using System.Collections.Specialized;
using System.Configuration;
using DarwiNet2._0.Data;
using Thinkware;

namespace DarwiNet2._0.Core.Options
{
    public class TwilioOptions
    {
        public bool UseAsFallback { get; private set; }
        public bool EnableOnNonProd { get; private set; }
        public string VerifyServiceSid { get; private set; }
        public string AccountSid { get; private set; }
        public string AuthToken { get; private set; }

        private TwilioOptions()
        {
            NameValueCollection twilioOption = (NameValueCollection)ConfigurationManager.GetSection("twilio");

            UseAsFallback = bool.Parse(twilioOption.Get("UseAsFallback"));
            EnableOnNonProd = bool.Parse(twilioOption.Get("EnableOnNonProd"));
            VerifyServiceSid = twilioOption.Get("VerifyServiceSid");
            AccountSid = twilioOption.Get("AccountSid");
            AuthToken = twilioOption.Get("AuthToken");
        }

        /// <summary>
        /// The Twilio option will be configured at the company level or using default settings if not configured 
        /// </summary>
        /// <returns>The Twilio options</returns>
        public static Result<TwilioOptions> Create(Company company)
        {
            TwilioOptions twilioOption = new TwilioOptions();
            bool isCompanyLevelTwilioConfigurationValid = company != null &&
                                                          !string.IsNullOrWhiteSpace(company.TwilioSID) &&
                                                          !string.IsNullOrWhiteSpace(company.TwilioAuthToken) &&
                                                          !string.IsNullOrWhiteSpace(company.TwilioVerificationSID);
            
            // Use company level Twilio configuration if valid
            if (isCompanyLevelTwilioConfigurationValid)
            {
                twilioOption.VerifyServiceSid = company.TwilioVerificationSID;
                twilioOption.AccountSid = company.TwilioSID;
                twilioOption.AuthToken = company.TwilioAuthToken;

                return Result.Success(twilioOption);
            }

            // Otherwise, use default Twilio configuration 
            bool isDefaultTwilioConfigurationValid = !string.IsNullOrEmpty(twilioOption.VerifyServiceSid) &&
                                                     !string.IsNullOrEmpty(twilioOption.AccountSid) &&
                                                     !string.IsNullOrEmpty(twilioOption.AuthToken);
            if (isDefaultTwilioConfigurationValid)
            {
                return Result.Success(twilioOption);
            }

            return Result.Failure<TwilioOptions>(
                "VerifyServiceSID or ApiKeySID or ApiKeySecret are not configured. Missing configuration for Twilio");
        }
    }
}