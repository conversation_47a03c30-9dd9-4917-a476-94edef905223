using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.JobCosting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.JobCosting
{
    public class EmployeeJobCostAssignmentsProvider : DbContextBaseProvider, IEmployeeJobCostAssignmentsProvider
    {
        public EmployeeJobCostAssignmentsProvider()
            : base()
        {

        }

        public EmployeeJobCostAssignmentsProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        public EmployeeJobCostAssignment FindJobAssignment(int companyid, string employeeid, string job, string l2, string l3, string l4, string l5, string l6) =>
            _dbContext.EmployeeJobCostAssignments.FirstOrDefault(jc => jc.CompanyID == companyid
                                                                    && jc.EmployeeID == employeeid
                                                                    && jc.JobCostingName == job
                                                                    && jc.JobLevel2 == l2
                                                                    && jc.JobLevel3 == l3
                                                                    && jc.JobLevel4 == l4
                                                                    && jc.JobLevel5 == l5
                                                                    && jc.JobLevel6 == l6);
        public List<EmployeeJobCostAssignment> EmployeeAssignments(int companyid, string employeeid, List<string> jobs = null)
        {
            List<EmployeeJobCostAssignment> result = _dbContext.EmployeeJobCostAssignments
                                                               .Where(jc => jc.CompanyID == companyid
                                                                        && jc.EmployeeID == employeeid)
                                                               .ToList();
            if (jobs != null)
            {
                foreach (string job in jobs)
                {
                    if (!string.IsNullOrEmpty(job))
                    {
                        string[] keys = job.Split('.');
                        string l1, l2, l3, l4, l5, l6;
                        switch (keys.Length)
                        {
                            case 2:
                                l1 = keys[0];
                                l2 = keys[1];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2)
                                               .ToList();
                                break;
                            case 3:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3)
                                               .ToList();
                                break;
                            case 4:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                l4 = keys[3];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3 &&
                                                            jc.JobLevel4 == l4)
                                               .ToList();
                                break;
                            case 5:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                l4 = keys[3];
                                l5 = keys[4];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3 &&
                                                            jc.JobLevel4 == l4 &&
                                                            jc.JobLevel5 == l5)
                                               .ToList();
                                break;
                            case 6:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                l4 = keys[3];
                                l5 = keys[4];
                                l6 = keys[5];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3 &&
                                                            jc.JobLevel4 == l4 &&
                                                            jc.JobLevel5 == l5 &&
                                                            jc.JobLevel6 == l6)
                                               .ToList();
                                break;
                            default:
                                l1 = keys[0];
                                result = result.Where(jc => jc.JobCostingName == l1)
                                               .ToList();
                                break;
                        }
                    }
                }
            }
            return result.OrderBy(jc => jc.JobCostingName)
                               .ThenBy(jc => jc.JobLevel2)
                               .ThenBy(jc => jc.JobLevel3)
                               .ThenBy(jc => jc.JobLevel4)
                               .ThenBy(jc => jc.JobLevel5)
                               .ThenBy(jc => jc.JobLevel6)
                               .ToList(); ;
        }

        public List<EmployeeJobCostAssignment> ClientEmployeesAssignments(int companyid, string clientid, List<string> jobs = null)
        {
            var result = _dbContext.EmployeeJobCostAssignments
                    .Where(jc => jc.CompanyID == companyid
                            && jc.ClientID == clientid)
                    .ToList();
            if (jobs != null)
            {
                foreach (string job in jobs)
                {
                    if (!string.IsNullOrEmpty(job))
                    {
                        string[] keys = job.Split('.');
                        string l1, l2, l3, l4, l5, l6;
                        switch (keys.Length)
                        {
                            case 2:
                                l1 = keys[0];
                                l2 = keys[1];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2)
                                               .ToList();
                                break;
                            case 3:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3)
                                               .ToList();
                                break;
                            case 4:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                l4 = keys[3];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3 &&
                                                            jc.JobLevel4 == l4)
                                               .ToList();
                                break;
                            case 5:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                l4 = keys[3];
                                l5 = keys[4];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3 &&
                                                            jc.JobLevel4 == l4 &&
                                                            jc.JobLevel5 == l5)
                                               .ToList();
                                break;
                            case 6:
                                l1 = keys[0];
                                l2 = keys[1];
                                l3 = keys[2];
                                l4 = keys[3];
                                l5 = keys[4];
                                l6 = keys[5];
                                result = result.Where(jc => jc.JobCostingName == l1 &&
                                                            jc.JobLevel2 == l2 &&
                                                            jc.JobLevel3 == l3 &&
                                                            jc.JobLevel4 == l4 &&
                                                            jc.JobLevel5 == l5 &&
                                                            jc.JobLevel6 == l6)
                                               .ToList();
                                break;
                            default:
                                l1 = keys[0];
                                result = result.Where(jc => jc.JobCostingName == l1)
                                               .ToList();
                                break;
                        }
                    }
                }
            }
            return result;
        }

        public List<EmployeeJobCostAssignment> ClientJobAssignments(int companyid, string clientid, string key)
        {
            List<EmployeeJobCostAssignment> result = _dbContext.EmployeeJobCostAssignments
                    .Where(jc => jc.CompanyID == companyid
                            && jc.ClientID == clientid)
                    .ToList();
            if (!string.IsNullOrEmpty(key))
            {
                string[] keys = key.Replace("|",".").Split('.');
                string l1, l2, l3, l4, l5, l6;
                switch (keys.Length)
                {
                    case 2:
                        l1 = keys[0];
                        l2 = keys[1];
                        result = result.Where(jc => jc.JobCostingName == l1 &&
                                                    jc.JobLevel2 == l2)
                                        .ToList();
                        break;
                    case 3:
                        l1 = keys[0];
                        l2 = keys[1];
                        l3 = keys[2];
                        result = result.Where(jc => jc.JobCostingName == l1 &&
                                                    jc.JobLevel2 == l2 &&
                                                    jc.JobLevel3 == l3)
                                        .ToList();
                        break;
                    case 4:
                        l1 = keys[0];
                        l2 = keys[1];
                        l3 = keys[2];
                        l4 = keys[3];
                        result = result.Where(jc => jc.JobCostingName == l1 &&
                                                    jc.JobLevel2 == l2 &&
                                                    jc.JobLevel3 == l3 &&
                                                    jc.JobLevel4 == l4)
                                        .ToList();
                        break;
                    case 5:
                        l1 = keys[0];
                        l2 = keys[1];
                        l3 = keys[2];
                        l4 = keys[3];
                        l5 = keys[4];
                        result = result.Where(jc => jc.JobCostingName == l1 &&
                                                    jc.JobLevel2 == l2 &&
                                                    jc.JobLevel3 == l3 &&
                                                    jc.JobLevel4 == l4 &&
                                                    jc.JobLevel5 == l5)
                                        .ToList();
                        break;
                    case 6:
                        l1 = keys[0];
                        l2 = keys[1];
                        l3 = keys[2];
                        l4 = keys[3];
                        l5 = keys[4];
                        l6 = keys[5];
                        result = result.Where(jc => jc.JobCostingName == l1 &&
                                                    jc.JobLevel2 == l2 &&
                                                    jc.JobLevel3 == l3 &&
                                                    jc.JobLevel4 == l4 &&
                                                    jc.JobLevel5 == l5 &&
                                                    jc.JobLevel6 == l6)
                                        .ToList();
                        break;
                    default:
                        l1 = keys[0];
                        result = result.Where(jc => jc.JobCostingName == l1)
                                        .ToList();
                        break;
                }
            }
            return result;
        }

    }
}