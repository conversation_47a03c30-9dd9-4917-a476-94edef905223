using DarwiNet2._0.Controllers;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Providers.OB;
using DarwiNet2._0.Providers.D2;
using DataDrivenViewEngine.Models.Core;
using EO.Pdf.Internal;
using Kendo.Mvc.Extensions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using DarwiNet2._0.DNetSynch;

namespace DarwiNet2._0.Providers.OB
{
    public class OBProcessMonitorProvider : DbContextBaseProvider, IOBProcessMonitorProvider
    {
        #region Constructors

        public OBProcessMonitorProvider()
            : base()
        {

        }

        public OBProcessMonitorProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }


        #endregion

        public List<OBProcessMonitor> GetEmployees(int companyId, string employeeId, bool completed)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                    .Where(x => x.CompanyID == companyId &&
                                x.IsCompleted == completed &&
                                x.EmployeeID == employeeId)
                    .ToList();
            }
        }

        //DG-11/16/2021-7419-START
        public IEnumerable<OBProcessMonitorVM> GetEmployees(int companyId, string clientId, bool completed, string dnetLevel)
        {
            List<OBProcessMonitorVM> result = null;
            if (dnetLevel != DNetAccessLevel.System)
            {
                using (var _dbContext = new DnetEntities())
                {

                    result = _dbContext.Database.SqlQuery<OBProcessMonitorVM>
         ("sp_GetEEProcessMonitorClient @company, @client,@userid,@completed",
         new SqlParameter("@company", companyId),
         new SqlParameter("@client", clientId),
         new SqlParameter("@userid", GlobalVariables.DNETOwnerID),
         new SqlParameter("@completed", completed)
        ).ToList();
                }
            }
            else
            {
                using (var _dbContext = new DnetEntities())
                {
                    result = _dbContext.Database.SqlQuery<OBProcessMonitorVM>
             ("sp_GetEEProcessMonitorSystem @company,@completed",
             new SqlParameter("@company", companyId),
                new SqlParameter("@completed", completed)
            ).ToList();
                }
            }
            return result;
        }




        //DG-11/16/2021-7419-END

        //private string OBProcessMonitorQuery(int companyId, string clientId, string dnetLevel, bool completed, string securDepts)
        //{
        //    string complFlag = "0";

        //    if (completed)
        //    {
        //        complFlag = "1";
        //    }

        //    string query =
        //        "SELECT " +
        //            "a.ClientID, " +
        //            "a.EmployeeID, " +
        //            "a.EmployeeName, " +
        //            "a.DivisionID as Division, " +
        //            "b.Email as EmailAddress, " +
        //            "b.BirthDate, " +
        //            "b.LoginCode, " +
        //            "a.SetupID, " +
        //            "a.EmployeeClass, " +
        //            "a.PayPeriod, " +
        //            "a.DueDate, " +
        //            "a.EEStartDate, " +
        //            "(SELECT TOP 1 " +
        //                "TaskName " +
        //             "FROM " +
        //                "OBClientSetupTasks as ETasks " +
        //             "WHERE " +
        //                    "SetupID = a.SetupID " +
        //                "AND CompanyID = a.CompanyID " +
        //                "AND TaskID = a.EECurrentTask) as eeTaskName, " +
        //            "CASE a.EEStatus WHEN -2 THEN 'Not Available' " +
        //            "WHEN -1 THEN 'Not Started' " +
        //            "WHEN 0 THEN 'In Progress' " +
        //            "WHEN 1 THEN 'Form Verification' " +
        //            "WHEN 2 THEN 'Completed' " +
        //            "WHEN 3 THEN 'Wait EE' " +
        //            "ELSE '' END as eeTaskStatus, " +
        //            "a.EESubmitDate, " +
        //            "a.CCStartDate, " +
        //            "a.CCStartBy, " +
        //            "(SELECT TOP 1 " +
        //                "TaskName " +
        //             "FROM " +
        //                "OBClientSetupTasks as CTasks " +
        //             "WHERE " +
        //                    "SetupID = a.SetupID " +
        //                "AND CompanyID = a.CompanyID " +
        //                "AND TaskID = a.CCCurrentTask) as ccTaskName, " +
        //            "CASE a.CCStatus WHEN -2 THEN 'Not Available' " +
        //            "WHEN -1 THEN 'Not Started' " +
        //            "WHEN 0 THEN 'In Progress' " +
        //            "WHEN 1 THEN 'Form Verification' " +
        //            "WHEN 2 THEN 'Completed' " +
        //            "WHEN 3 THEN 'Wait EE' " +
        //            "ELSE '' END as ccTaskStatus, " +
        //            "a.CCSubmitDate, " +
        //            "a.CCSubmitBy, " +
        //            "a.WorkState, " +
        //            "ProfileName = (SELECT OS.ProfileName FROM OBClientSetup OS WHERE OS.SetupID = a.SetupID) ";

        //    query +=
        //        "FROM " +
        //            "OBProcessMonitor as a " +
        //                "INNER JOIN OBUsers  as b " +
        //                "ON a.EmployeeID = b.EmployeeID " +
        //                "AND a.CompanyID = b.CompanyID " +
        //                "AND a.ClientID = b.Client " +
        //        "WHERE     " +
        //                "(a.IsCompleted = " + complFlag + ") " +
        //            "AND (a.CompanyID = " + companyId.ToString() + ")";

        //    if (dnetLevel != DNetAccessLevel.System)
        //    {
        //        query +=
        //            " AND a.ClientID = '" + clientId + "'";

        //        if (!string.IsNullOrEmpty(securDepts))
        //        {
        //            query +=
        //                " AND a.EmployeeID IN (" +
        //                    "SELECT DISTINCT " +
        //                        "EmployeeID " +
        //                    "FROM " +
        //                        "OBProcessRecordDetails " +
        //                    "WHERE " +
        //                            "CompanyID = a.CompanyID " +
        //                        "AND EmployeeID = a.EmployeeID " +
        //                        "AND TaskID = 10 " +
        //                        "AND FldName = 'Department' " +
        //                        "AND FldValue IN ('" + securDepts + "'))";
        //        }
        //    }

        //    return query;
        //}

        public IEnumerable<OBProcessMonitorVM> GetEESingleInfo(int companyId, string employeeId, bool completed)
        {
            string clientid = employeeId.Substring(2, 3);
            IEnumerable<OBProcessMonitorVM> obProcessMonitorVM = GetEmployees(companyId, clientid, completed, DNetAccessLevel.Client);
            return obProcessMonitorVM.Where(x => x.EmployeeID == employeeId);
            /*            List<OBProcessMonitor> employees = new List<OBProcessMonitor>();
                        employees = GetEmployees(companyId, employeeId, completed);
                        employees.RemoveAll(e => !_dbContext.OBProcessRecordDetails
                            .Any(x => x.CompanyID == companyId &&
                                        x.TaskID == 10 &&
                                        x.FldName == "Department" &&
                                        x.EmployeeID == e.EmployeeID));

                        List<OBProcessMonitorVM> obProcessMonitorVM = new List<OBProcessMonitorVM>();

                        obProcessMonitorVM = employees
                            .Join(_dbContext.OBUsers,
                                e => e.EmployeeID,
                                u => u.EmployeeID,
                                (e, u) => new { e, u })
                            .Join(_dbContext.OBClientSetupTasks.DefaultIfEmpty(),
                                e => e.e.EECurrentTask,
                                t => t.TaskID,
                                (eu, t) => new { eu, t }
                            )
                            .Where(eut => eut.t.CompanyID == companyId && eut.t.SetupID == eut.eu.e.SetupID)
                            .Select(eut => new { employee = eut.eu.e, obUser = eut.eu.u, eeTaskName = eut.t?.TaskName ?? string.Empty })
                            .Join(_dbContext.OBClientSetupTasks.DefaultIfEmpty(),
                                eut => eut.employee.CCCurrentTask,
                                t => t.TaskID,
                                (eut, t) => new { eut, t }
                            )
                            .Where(eutt => eutt.t.CompanyID == companyId && eutt.t.SetupID == eutt.eut.employee.SetupID)
                            .Select(eutt => new { eutt.eut.employee, eutt.eut.obUser, eutt.eut.eeTaskName, ccTaskName = eutt.t?.TaskName ?? string.Empty })
                            .Join(_dbContext.OBClientSetups.DefaultIfEmpty(),
                                eutt => eutt.employee.SetupID,
                                s => s.SetupID,
                                (eutt, s) => new { eutt, s }
                            )
                            .Select(eutts => new OBProcessMonitorVM
                            {
                                EmployeeID = eutts.eutt.employee.EmployeeID,
                                EmployeeIDDisplay = FieldTranslation.EmployeeIDFormatted(eutts.eutt.employee.EmployeeID),
                                eeTaskStatus = FieldTranslation.GetEnumDescription(typeof(enOBProcessTaskStatus), (int)eutts.eutt.employee.EEStatus),
                                ccTaskStatus = FieldTranslation.GetEnumDescription(typeof(enOBProcessTaskStatus), (int)eutts.eutt.employee.CCStatus),
                                EEStartDate = eutts.eutt.employee.EEStartDate,
                                CCStartDate = eutts.eutt.employee.CCStartDate,
                                eeTaskName = eutts.eutt.eeTaskName,
                                //eeTaskName = GetTaskName(eu.e.SetupID, eu.e.EECurrentTask),
                                ccTaskname = eutts.eutt.ccTaskName,
                                //ccTaskname = GetTaskName(eu.e.SetupID, eu.e.CCCurrentTask),
                                DueDate = eutts.eutt.employee.DueDate,
                                CCSubmitDate = eutts.eutt.employee.CCSubmitDate,
                                EmployeeName = eutts.eutt.employee.EmployeeName,
                                EmailAddress = eutts.eutt.obUser.Email,
                                LoginCode = eutts.eutt.obUser.LoginCode,
                                BirthDate = eutts.eutt.obUser.BirthDate,
                                SetupID = eutts.eutt.employee.SetupID,
                                EmployeeClass = eutts.eutt.employee.EmployeeClass,
                                PayPeriod = eutts.eutt.employee.PayPeriod,
                                WorkState = eutts.eutt.employee.WorkState,
                                ClientID = eutts.eutt.employee.ClientID,
                                CCSubmitBy = eutts.eutt.employee.CCSubmitBy,
                                Division = eutts.eutt.employee.DivisionID,
                                ProfileName = eutts.s.ProfileName ?? string.Empty
                                //ProfileName = FieldTranslation.GetProfileName((int)eu.ett.employee.SetupID)
                            })
                            .ToList();

                        return obProcessMonitorVM;*/
        }

        public int? GetSetupId(int companyId, string employeeId)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                    .FirstOrDefault(x => x.CompanyID == companyId &&
                                         x.EmployeeID == employeeId &&
                                         x.IsCompleted == false)?.SetupID;
            }
        }

        public DateTime? GetDueDate(int companyId, string employeeId)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                    .FirstOrDefault(p => p.CompanyID == companyId &&
                                         p.EmployeeID == employeeId)?.DueDate;
            }
        }

        public KeyValuePair<string, string> GetEmployeeNameEmailAddress(int companyId, string employeeId)
        {
            string name = _dbContext.OBProcessMonitors
                .Where(m => m.CompanyID == companyId && m.EmployeeID == employeeId)
                .FirstOrDefault()
                .EmployeeName;

            string emailAddress = _dbContext.OBProcessRecordDetails
                .Where(d => d.CompanyID == companyId &&
                            d.EmployeeID == employeeId &&
                            d.TaskID == 10 &&
                            d.FldName == "EMail")
                .FirstOrDefault()
                .FldValue;

            return new KeyValuePair<string, string>(name, emailAddress);
        }

        public string GetCCStartByUserId(int companyId, string employeeId)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                    .First(p => p.CompanyID == companyId &&
                                p.EmployeeID == employeeId &&
                                p.IsCompleted != true)
                    .CCStartBy
                    .Trim();
            }
        }

        public KeyValuePair<string, string> GetNotificationNameEmailAddress(int companyId, string clientId, string employeeId)
        {
            string name = string.Empty;
            string mail = string.Empty;

            string userId = GetCCStartByUserId(companyId, employeeId);

            using (var _dbContext = new DnetEntities())
            {
                var user = _dbContext.Users
                    .Where(u => u.UserID == userId &&
                                u.Enabled == true)
                    .Select(u => new
                    {
                        u.Name,
                        u.Email
                    })
                    .FirstOrDefault();

                if (user != null)
                {
                    if (string.IsNullOrEmpty(user.Email))
                    {
                        mail = _dbContext.ClientAssignments
                            .First(a => a.CompanyID == companyId &&
                                        a.ClientID == clientId &&
                                        a.AssignmentType == ChangeRequestAssignmentTypes.Employee)
                            .Email ?? string.Empty;
                        mail = mail.Trim();
                    }

                    if (mail == string.Empty)
                    {
                        mail = (user.Email ?? string.Empty).Trim();

                        IClientProvider clientProvider = new ClientProvider();
                        name = clientProvider.GetClientNameById(companyId, clientId);
                    }
                    else name = user.Name;
                }
            }

            return new KeyValuePair<string, string>(name, mail);
        }

        public bool GetMaskSSNInSign(int companyId, string employeeId)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                .FirstOrDefault(x => x.CompanyID == companyId &&
                                     x.EmployeeID == employeeId).MaskSSNInSign;
            }
        }

        public KeyValuePair<string, bool> GetEmployeeMaskSSNInSign(int companyId, string employeeId)
        {
            using (var _dbContext = new DnetEntities())
            {
                var result = _dbContext.OBProcessMonitors
                    .Where(x => x.CompanyID == companyId &&
                                         x.EmployeeID == employeeId)
                    .Select(x => new
                    {
                        x.EmployeeName,
                        x.MaskSSNInSign
                    })
                    .FirstOrDefault();

                return new KeyValuePair<string, bool>(result.EmployeeName, result.MaskSSNInSign);
            }
        }

        public IEnumerable<OBProcessMonitor> GetPendingEmployees(int companyId)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                    .Where(x => x.CompanyID == companyId &&
                                x.IsCompleted == false)
                    .OrderBy(x => x.EmployeeID);
            }
        }

        public OBProcessMonitor GetOBProcessMonitorEmployeeDetails(int companyId, string employeeId)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                    .FirstOrDefault(x => x.CompanyID == companyId &&
                                         x.EmployeeID == employeeId &&
                                         x.IsCompleted != true);
            }
        }

        public void ClearOBMAlerts(int companyId, string clientId)
        {
            var monitors = _dbContext.OBProcessMonitors
                .Where(m => !m.IsCompleted &&
                            m.EESubmitDate != null &&
                            m.ClientID == clientId &&
                            m.CompanyID == companyId &&
                            !m.AlertCleared);

            foreach (var monitor in monitors)
            {
                monitor.AlertCleared = true;
            }

            _dbContext.SaveChanges();
        }
        public bool IsEmployeeRehired(int companyId, string employeeId)
        {
            var eProcessMonitor = _dbContext.OBProcessMonitors.FirstOrDefault(x => x.CompanyID == companyId && x.RehireEmployeeID == employeeId && x.IsCompleted);

            return eProcessMonitor != null;
        }

        public OBProcessMonitor GetOBEmployeeProcessMonitor(int companyId, string employeeId)
        {
            using (var _dbContext = new DnetEntities())
            {
                return _dbContext.OBProcessMonitors
                    .FirstOrDefault(x => x.CompanyID == companyId &&
                                         x.EmployeeID == employeeId);
            }
        }

        public OBProcessMonitor GetOBProcessMonitorByEmployeeID(int companyId, string employeeId)
        {
            return _dbContext.OBProcessMonitors.FirstOrDefault(x => x.CompanyID == companyId && x.EmployeeID == employeeId);
        }

        public OBProcessMonitor GetOBProcessMonitorByEmployeeAndStatus(int companyId, string employeeId, bool isCompleted)
        {
            return _dbContext.OBProcessMonitors
                .FirstOrDefault(x => x.CompanyID == companyId && x.EmployeeID == employeeId && x.IsCompleted == isCompleted);
        }
        public OBProcessMonitor GetRehireOBProcessMonitor(string employeeId, int companyId)
        {

            var rec = _dbContext.OBProcessMonitors.OrderByDescending(x => x.CCSubmitDate)
             .FirstOrDefault(x => x.CompanyID == companyId && x.RehireEmployeeID == employeeId && !x.IsCompleted);
            return rec;
        }
    }
}