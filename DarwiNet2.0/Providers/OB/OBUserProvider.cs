using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Interfaces.Providers.OB;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;

namespace DarwiNet2._0.Providers.OB
{
    public class OBUserProvider : DbContextBaseProvider, IOBUserProvider
    {
        #region Constructors

        public OBUserProvider()
            : base()
        {

        }

        #endregion

        public OBUser GetOBUserByEmployeeId(int companyId, string employeeId)
        {
            return _dbContext.OBUsers.FirstOrDefault(u => u.CompanyID == companyId && u.EmployeeID == employeeId);
        }

        public OBUser GetOBUserByStatus(int companyId, string employeeId, short status)
        {
            return _dbContext.OBUsers.FirstOrDefault(x => x.CompanyID == companyId && x.EmployeeID == employeeId && x.Status == status);
        }

        public void UpdateOBUser(int companyId, string employeeId, string socialSecurityNumber, string email, string dateOfBirth, out string oldssn, out string oldemail, out string olddob)
        {
            using (var _dbContext = new DnetEntities())
            {
                OBUser thisUser = _dbContext.OBUsers.First(x => x.CompanyID == companyId && x.EmployeeID == employeeId);

                oldssn = thisUser.SocSecNumber;
                if (!string.IsNullOrEmpty(socialSecurityNumber))
                {
                    thisUser.SocSecNumber = socialSecurityNumber;
                }

                oldemail = thisUser.Email;

                if (!string.IsNullOrEmpty(email))
                {
                    thisUser.Email = email;
                }

                olddob = thisUser.BirthDate.ToString();

                if (!string.IsNullOrEmpty(dateOfBirth))
                {
                    thisUser.BirthDate = Convert.ToDateTime(dateOfBirth);
                }

                _dbContext.SaveChanges();
            }
        }

        public bool CreateNewOBUser(int companyId, string company, string clientId, string customer, string loginCode, string emplId, string eeFirstName, string eeLastName, string eeMail, string eeSSN, string eeBirthDate)
        {
            bool result = false;
            using (var dbContext = new DnetEntities())
            {
                DateTime? eeBD;

                if (string.IsNullOrEmpty(eeBirthDate))
                {
                    eeBD = null;
                }
                else
                {
                    eeBD = Convert.ToDateTime(eeBirthDate);
                }

                if (!GlobalVariables.IsReHire)
                {
                    OBUser newUser = new OBUser()
                    {
                        LoginCode = loginCode,
                        Customer = customer,
                        Company = company,
                        CompanyID = companyId,
                        Client = clientId,
                        EmployeeID = emplId,
                        FirstName = eeFirstName,
                        LastName = eeLastName,
                        SocSecNumber = eeSSN,
                        BirthDate = eeBD,
                        Email = eeMail,
                        Status = 0
                    };
                    try
                    {
                        OBUser userToDelete = dbContext.OBUsers.FirstOrDefault(x => x.EmployeeID == emplId);
                        if (userToDelete != null) dbContext.OBUsers.Remove(userToDelete);

                        dbContext.OBUsers.Add(newUser);
                        dbContext.SaveChanges();
                        result = true;
                    }
                    catch { result = false; }
                    return result;
                }
                else
                {
                    List<UserRoleClientEmployeeAssignment> usersroleassign = dbContext.UserRoleClientEmployeeAssignments
                        .Where(urcea => urcea.CompanyID == GlobalVariables.CompanyID && urcea.EmployeeID == GlobalVariables.ReHireEmployeeID)
                        .OrderByDescending(urcea => urcea.id)
                        .ToList();

                    User Existinguser = new User();
                    if (usersroleassign.Count > 0)
                    {
                        string userId = usersroleassign[0].UserID; 
                        Existinguser = dbContext.Users.FirstOrDefault(x => x.UserID == userId);
                    }

                    OBUser Existingobuser = dbContext.OBUsers.FirstOrDefault(x => x.EmployeeID == GlobalVariables.ReHireEmployeeID);

                    OBUser newUser = new OBUser()
                    {
                        UserID = Existinguser.UserID,
                        Password = Existinguser.Password,
                        LoginCode = loginCode,
                        Customer = GlobalVariables.Customer,
                        Company = GlobalVariables.Company,
                        CompanyID = GlobalVariables.CompanyID,
                        Client = GlobalVariables.Client,
                        EmployeeID = emplId,
                        FirstName = eeFirstName,
                        LastName = eeLastName,
                        SocSecNumber = eeSSN,
                        BirthDate = eeBD,
                        Email = eeMail.Trim(),
                        Status = 0
                    };

                    dbContext.OBUsers.Add(newUser);

                    if (Existingobuser != null)
                    {
                        Existingobuser.UserID = null;
                        Existingobuser.Password = null;
                    }

                    dbContext.SaveChanges();
                    result = true; 
                }
            }

            return result;
        }

        public OBUser GetOBUserByUserId(string userId)
        {
            return _dbContext.OBUsers.FirstOrDefault(u => u.UserID == userId);
        }
        public OBUser GetOBUserWithLoginCode(string employeeId)
        {
            return _dbContext.OBUsers.Where(x => !string.IsNullOrEmpty(x.EmployeeID) && x.EmployeeID.Equals(employeeId) && !string.IsNullOrEmpty(x.LoginCode)).FirstOrDefault();
        }

        public int GetCountOfOBUsersWithCode(string code)
        {
            return _dbContext.OBUsers.Count(x => x.LoginCode == code);
        }
    }
}