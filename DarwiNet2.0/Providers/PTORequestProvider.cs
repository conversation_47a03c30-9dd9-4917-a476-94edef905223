using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers
{   
    
        public class PTORequestProvider : DbContextBaseProvider, IPTORequestProvider
        {
            public List<PTORequest> GetPTORequestsFromDateRange(int companyID, string employeeID, string clientID, DateTime date)
            {
                return _dbContext.PTORequests.Where(epto =>
                        epto.CompanyID == companyID && epto.EmployeeID == employeeID &&
                        epto.ClientID == clientID &&
                        (epto.Status == (int) enPtoRequestType.Approved && epto.StartDate >= date ||  epto.Status == (int)enPtoRequestType.Pending)).ToList();
            }
        }
    }
