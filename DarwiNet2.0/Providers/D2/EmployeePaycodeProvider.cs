using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace DarwiNet2._0.Providers.D2
{
    public class EmployeePaycodeProvider : DbContextBaseProvider, IEmployeePaycodeProvider
    {
        #region Constructors

        public EmployeePaycodeProvider()
            : base()
        {

        }

        public EmployeePaycodeProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        public bool Any(Expression<Func<EmployeePaycode, bool>> expression) =>
            _dbContext.EmployeePaycodes.Any(expression);

        public List<EmployeePaycode> List(int companyId)
        {
            List<EmployeePaycode> employeePaycodes = new List<EmployeePaycode>();
            employeePaycodes = _dbContext.EmployeePaycodes
                .Where(e => e.CompanyID == companyId)
                .ToList();

            return employeePaycodes;
        }

        public List<EmployeePaycode> ListAutoPayEmployeePaycodes(int companyId, string clientId, string profileId)
        {
            return _dbContext.EmployeePaycodes
                .Where(p => p.CompanyID == companyId)
                .Join(
                    _dbContext.PayrollProfileAutoPayEmployees,
                    p => new { p.CompanyID, p.EmployeeID },
                    e => new { e.CompanyID, e.EmployeeID },
                    (p, e) => new { EmployeePaycodes = p, e })
                .Where(pe => pe.e.ClientID == clientId && pe.e.ProfileID == profileId)
                .Select(pe => pe.EmployeePaycodes)
                .ToList();
        }

        public byte? GetEmployeePaycodePayPeriod(int companyId, string clientId, string employeeId, string employeePrimaryPayRecord)
        {
            byte? payPeriod = null;
            payPeriod = _dbContext.EmployeePaycodes
                .FirstOrDefault(epc => epc.EmployeeID == employeeId &&
                                       epc.CompanyID == companyId &&
                                       epc.PayRecord == employeePrimaryPayRecord)?
                .PayPeriod;

            if (payPeriod == null || payPeriod == PayPeriods.Empty)
            {
                payPeriod = _dbContext.Clients
                .FirstOrDefault(c => c.CompanyID == companyId &&
                                     c.ClientID == clientId)?
                                     .ClientFrequency;
            }

            return payPeriod;
        }

        public List<EmployeePaycode> FindByEmployeeId(int companyId, string employeeId)
        {
            List<EmployeePaycode> employeePaycodes = new List<EmployeePaycode>();
            employeePaycodes = _dbContext.EmployeePaycodes
                .Where(e => e.CompanyID == companyId
                          && e.EmployeeID == employeeId
                          && !e.Inactive)
                .ToList();

            return employeePaycodes;
        }

        public List<EmployeePaycode> GetPayrateWithFrequencyByEmployeeIds(List<string> employeeIds)
        {
            List<EmployeePaycode> employeePaycodes = _dbContext.EmployeePaycodes
                .Where(p => employeeIds.Contains(p.EmployeeID)).AsEnumerable()
                .Select(p => new EmployeePaycode
                {
                    EmployeeID = p.EmployeeID,
                    PayRateAmount = p.PayRateAmount,
                    PayPeriod = p.PayPeriod
                })
                .ToList();

            return employeePaycodes;
        }

        public bool IsValidEmployeePaycode(int companyId, string employeeId, string paycode)
        {
            return _dbContext.EmployeePaycodes
                .Any(x => x.CompanyID == companyId &&
                          x.EmployeeID == employeeId &&
                          x.PayRecord == paycode &&
                          !x.Inactive);
        }

        //public string GetEmployeePaycodePieceworkCode(int companyId, string employeeId, string paycode)
        //{
        //    return _dbContext.EmployeePaycodes
        //        .FirstOrDefault(p => p.CompanyID == companyId &&
        //                             p.EmployeeID == employeeId &&
        //                             p.PayRecord == paycode &&
        //                             p.PayType == PaycodeType.Piecework &&
        //                             !p.Inactive);
        //        //?.;     // should be special new field like TW_Hourly_Code


        //}

        //public void GetEmployeePaycodeWCSuta(int companyId, string employeeId, string paycode, out string wc, out string suta)
        //{
        //    var record = _dbContext.EmployeePaycodes
        //        .Where(x => x.CompanyID == companyId &&
        //                    x.EmployeeID == employeeId &&
        //                    x.PayRecord == paycode)
        //        .Select(x => new
        //        {
        //            suta = x.SUTAState ?? string.Empty,
        //            wc = x.WorkersComp ?? string.Empty
        //        })
        //        .FirstOrDefault();

        //    suta = record.suta;
        //    wc = record.wc;
        //}

        public List<EmployeePaycode> EmployeePaycodesWhere(Expression<Func<EmployeePaycode, bool>> expression) =>
            _dbContext.EmployeePaycodes.Where(expression).ToList();

        public EmployeePaycode EmployeePaycodesFirstOrDefault(Expression<Func<EmployeePaycode, bool>> expression) =>
            _dbContext.EmployeePaycodes.FirstOrDefault(expression);

        public bool IsSalaryEmployee(int companyId, string emplId, out bool isOT, out bool isReg)
        {
            bool result = false;
            isOT = false;
            isReg = false;
            try
            {
                List<EmployeePaycode> eecodes = _dbContext.EmployeePaycodes.Where(e => e.CompanyID == companyId && e.EmployeeID == emplId && !e.Inactive && (e.PayType == PaycodeType.Overtime || e.PayType == PaycodeType.Hourly || e.PayType == PaycodeType.Salary)).ToList();
                List<string> eereg = eecodes.Where(c => string.IsNullOrEmpty(c.BasePayRecord) && c.PayType != PaycodeType.Overtime).Select(c => c.PayRecord).ToList();
                isReg = eereg.Any();
                if (isReg)
                {
                    result = !eecodes.Any(c => string.IsNullOrEmpty(c.BasePayRecord) && c.PayType == PaycodeType.Hourly);
                    isOT = eecodes.Any(c => eereg.Any(r => r == c.BasePayRecord) && c.PayType == PaycodeType.Overtime);
                }
                return result;
            }
            catch
            {
                isOT = false;
                isReg = false;
                return false;
            }
        }

        public int GetActiveEmployeePaycodeBasePayRecordCount(int companyId, string employeeId, string code) =>
            _dbContext.EmployeePaycodes.Count(c =>
                c.CompanyID == companyId &
                c.EmployeeID == employeeId &&
                c.BasePayRecord == code &&
                !c.Inactive);

        public EmployeePaycode GetEmployeePaycode(Expression<Func<EmployeePaycode, bool>> expression) =>
            _dbContext.EmployeePaycodes
            .FirstOrDefault(expression);

        public EmployeePaycode GetEmployeePaycode(int companyId, string emplId, string code, byte? type) =>
            _dbContext.EmployeePaycodes
            .FirstOrDefault(c =>
                c.CompanyID == companyId &&
                c.EmployeeID == emplId &&
                c.BasePayRecord == code &&
                c.PayType == type);

        public EmployeePaycode GetEmployeePaycode(int companyId, string employeeID, string code) =>
            _dbContext.EmployeePaycodes
            .FirstOrDefault(c =>
                c.CompanyID.Equals(companyId) &&
                c.EmployeeID.Equals(employeeID) &&
                c.PayRecord.Equals(code));

        public List<EmployeePaycode> GetEmployeePaycodes(int companyId, string emplId, string code) =>
            _dbContext.EmployeePaycodes
            .Where(c =>
                c.CompanyID == companyId &&
                c.EmployeeID == emplId &&
                c.BasePayRecord == code)
            .ToList();

        public int BCodesNbr(int companyId, string emplId, string code)
        {
            try
            {
                return _dbContext.EmployeePaycodes.Count(c => c.CompanyID == companyId & c.EmployeeID == emplId && c.BasePayRecord == code && !c.Inactive);
            }
            catch { return -1; }
        }

        // TODO: since BasePayRecord is not used in the second query (regardless if?), this can be simplified to use a single db query
        //  TODO: (cont'd) return _dbContext.EmployeePaycodes.Any(p => p.CompanyID == companyId && p.PayRecord == code && !p.Inactive && p.EmployeeID == emplId && !string.IsNullOrEmpty(p.BasePayRecord) && p.PayType == PaycodeType.Salary) (use unit test to verify results are as expected)
        public bool IsEmployeeCodeBasedOnSalary(int companyId, string emplId, string code)
        {
            string basecode = _dbContext.EmployeePaycodes
                .First(p => p.CompanyID == companyId &&
                            p.PayRecord == code &&
                            !p.Inactive &&
                            p.EmployeeID == emplId)
                .BasePayRecord;

            if (!(string.IsNullOrEmpty(code) && string.IsNullOrEmpty(basecode)))
            {
                return _dbContext.EmployeePaycodes
                    .Any(p => p.CompanyID == companyId &&
                              p.PayRecord == code &&
                              p.PayType == PaycodeType.Salary &&
                              !p.Inactive &&
                              p.EmployeeID == emplId);
            }

            return false;
        }

        public void UpdateEmployeeCafePlan(int companyId, string oeEmployeeId, string excessCode, decimal remainder)
        {
            var employeePaycodes = _dbContext.EmployeePaycodes
                .FirstOrDefault(e => e.CompanyID == companyId &&
                                     e.EmployeeID == oeEmployeeId &&
                                     e.PayRecord == excessCode);

            if (employeePaycodes != null)
            {
                employeePaycodes.PayRateAmount = remainder;
                _dbContext.SaveChanges();
            }
        }

        //DG-7335-08/09/2021
        public List<string> GetEmployeePaycodePayRecords(int companyId, string employeeId)
        {
            try
            {
                return _dbContext.EmployeePaycodes
                                .Where(p => p.CompanyID == companyId &&
                                            p.EmployeeID == employeeId &&
                                            p.Inactive == false)
                                .Select(p => p.PayRecord)
                                .ToList();
            }
            catch { return new List<string>(); }
        }

        public bool IsSubjectToFUTA(string employeeID, string payRecord)
        {
            return _dbContext.EmployeePaycodes.Where(x => x.EmployeeID == employeeID &&
                                                          x.PayRecord == payRecord)
                .FirstOrDefault()
                .SubjectToFUTA;
        }

        public bool IsSubjectToSUTA(string employeeID, string payRecord)
        {
            return _dbContext.EmployeePaycodes.Where(x => x.EmployeeID == employeeID &&
                                                          x.PayRecord == payRecord)
                .FirstOrDefault()
                .SubjectToSUTA;
        }

        public void CreatePaycode(EmployeePaycode paycode)
        {
            _dbContext.EmployeePaycodes.Add(paycode);
            _dbContext.SaveChanges();
        }
        public void RollDownPayCodes(Employee employee, bool updFlag, string type)
        {
            if (updFlag)
            {
                List<EmployeePaycode> eePayCodes = _dbContext.EmployeePaycodes.Where(epc => epc.CompanyID == employee.CompanyID && epc.EmployeeID == employee.EmployeeID).ToList();
                foreach (EmployeePaycode ee_code in eePayCodes)
                {
                    switch (type)
                    {
                        case "WC":
                            ee_code.WorkersComp = employee.WorkersComp;
                            break;
                        case "SUTA":
                            ee_code.SUTAState = employee.SUTAState;
                            break;
                    }
                }
                _dbContext.SaveChanges();
            }
        }
        public List<EmployeePaycode> GetEmployeePaycodes(int companyId, string emplId) =>
          _dbContext.EmployeePaycodes
          .Where(c =>
              c.CompanyID == companyId &&
              c.EmployeeID == emplId)
          .ToList();
    }
 }
