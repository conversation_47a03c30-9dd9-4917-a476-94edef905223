using DarwiNet2._0.Interfaces.Providers;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using Thinkware.Cohesion.Payroll.Processor;
using EO.Pdf.Internal;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollBatchDetailProvider : DbContextBaseProvider, IPayrollBatchDetailProvider
    {
        #region Fields

        private readonly ISqlConnectionProvider _sqlConnectionProvider;

        #endregion

        #region Constructors

        public PayrollBatchDetailProvider()
            : base()
        {
            _sqlConnectionProvider = new SqlConnectionProvider();
        }
        public PayrollBatchDetailProvider(DnetEntities dbContext)
            : base(dbContext)
        {
            _sqlConnectionProvider = new SqlConnectionProvider();
        }

        #endregion

        public int GetLastTransactionNumber(int companyId)
        {
            int result = 0;
            try
            {
                result = _dbContext.PayrollBatchDetails
                    .Where(x => x.CompanyID == companyId)
                    .OrderByDescending(x => x.ComputerTRXNumber)
                    .Select(x => x.ComputerTRXNumber)
                    .First();
            }
            catch
            {
                result = 0;
            }
            return result;
        }

        public void GetTransactionCounts(int companyId, string batchNumber, out int controlTrxCount, out int controlEmployeeCount)
        {
            var payrollBatchDetails = _dbContext.PayrollBatchDetails
                .Where(b => b.CompanyID == companyId &&
                            b.BatchNumber == batchNumber)
                .ToList();

            controlTrxCount = payrollBatchDetails.Count();
            controlEmployeeCount = payrollBatchDetails.Select(b => b.EmployeeID).Distinct().Count();
        }

        public Dictionary<string, Dictionary<string, int>> GetBatchTransactionCounts(int companyId, IEnumerable<string> batchNumbers)
        {
            var payrollBatchDetails = _dbContext.PayrollBatchDetails
                .Where(b => b.CompanyID == companyId &&
                            batchNumbers.Contains(b.BatchNumber));

            Dictionary<string, Dictionary<string, int>> batchDetails = new Dictionary<string, Dictionary<string, int>>();

            foreach (string batchNumber in batchNumbers)
            {
                var batch = payrollBatchDetails.Where(p => p.BatchNumber == batchNumber);

                Dictionary<string, int> transactionCounts = new Dictionary<string, int>();
                transactionCounts.Add("ControlTrxCount", batch.Count());
                transactionCounts.Add("ControlEmployeeCount", batch.Select(b => b.EmployeeID).Distinct().Count());

                batchDetails.Add(batchNumber, transactionCounts);
            }

            return batchDetails;
        }

        public void CreatePayrollBatchDetailsRecords(int companyId, List<PayrollBatchDetail> payrollBatchDetails)
        {
            int trxNumber = GetLastTransactionNumber(companyId);

            foreach (var d in payrollBatchDetails)
            {
                trxNumber++;
                d.ComputerTRXNumber = trxNumber;
            }

            this.AddItems(payrollBatchDetails);
        }

        public List<PayrollBatchDetail> GetBatchDetails(PayrollBatch batch)
        {
            return _dbContext.PayrollBatchDetails.Where(b => b.CompanyID == batch.CompanyID && b.BatchNumber == b.BatchNumber).ToList();
        }

        public class TaxableBenefitResponse
        {
            public bool ContainsTaxableOnly { get; set; }
            public List<string> EmployeesWithTaxableOnly { get; set; }
        }

        public TaxableBenefitResponse EmployeeContainsTaxableOnlyBenefitsForBenefitOnlyPayroll(string payrollNumber)
        {
            var resp = new TaxableBenefitResponse
            {
                ContainsTaxableOnly = true,
                EmployeesWithTaxableOnly = new List<string>()
            };

            PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
            int companyId = oPayrollNumber.CompanyId;

            var batchEmployeesWithPaycodeOrBenefits = _dbContext.PayrollBatchDetails
                .Join(_dbContext.PayrollBatches,
                    pbd => new { pbd.CompanyID, pbd.BatchNumber },
                    pb => new { pb.CompanyID, pb.BatchNumber },
                    (pbd, pb) => new { pbd, pb })
                .Join(_dbContext.Employees,
                    batch => new { batch.pbd.CompanyID, batch.pbd.EmployeeID },
                    e => new { e.CompanyID, e.EmployeeID },
                    (batch, e) => new { batch, e })
                .Where(x => (x.batch.pbd.ComputerTRXType == 1 || x.batch.pbd.ComputerTRXType == 3) &&
                            x.batch.pb.PayrollNumber == payrollNumber &&
                            x.e.Inactive == false &&
                            x.batch.pbd.CompanyID == companyId)
                .Select(x => new
                {
                    x.batch.pbd.EmployeeID,
                    x.batch.pbd.ComputerTRXType,
                    x.batch.pbd.UPRTRXCode
                })
                .Distinct()
                .ToList();

            var payCodeEmps = batchEmployeesWithPaycodeOrBenefits
                .Where(x => x.ComputerTRXType == 1)
                .Select(x => x.EmployeeID)
                .Distinct()
                .ToList();

            var benefitOnlyEmployees = batchEmployeesWithPaycodeOrBenefits
                .Where(x => x.ComputerTRXType == 3 &&
                            !payCodeEmps.Contains(x.EmployeeID))
                .Select(x => new
                {
                    x.EmployeeID,
                    x.UPRTRXCode
                })
                .ToList();

            if (!benefitOnlyEmployees.Any())
            {
                resp.ContainsTaxableOnly = false;
            }

            // For each benefit only payroll, check to ensure at least one employeebenefit is taxable
            // If all are taxable, return true
            foreach (var emp in benefitOnlyEmployees)
            {
                var employeeBenefits = _dbContext.EmployeeBenefits
                    .Where(x => x.CompanyID == companyId &&
                                x.EmployeeID == emp.EmployeeID);

                if (employeeBenefits.Any(x => !x.Taxable))
                {
                    resp.ContainsTaxableOnly = false;
                } else
                {
                    resp.EmployeesWithTaxableOnly.Add(emp.EmployeeID);
                }
            }

            return resp;
        }

        public void RemoveBatchDetailsByPayrollNumber(string payrollNumber)
        {
            var batchNumbers = _dbContext.PayrollBatches.Where(b => b.PayrollNumber == payrollNumber).Select(b => b.BatchNumber).Distinct();
            var recordsToRemove = _dbContext.PayrollBatchDetails.Where(d => batchNumbers.Contains(d.BatchNumber));
            _dbContext.PayrollBatchDetails.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }
        public int GetPayrollWorkHeadersWithClientPayRollSchedules(int companyID, string employeeID, int scheduleStatusMin, int scheduleStatusMax)
        {
            return _dbContext.PayrollWorkHeaders
                        .Join(_dbContext.ClientPayrollSchedules,
                                pwh => new { pwh.CompanyID, pwh.PayrollNumber },
                                cps => new { cps.CompanyID, cps.PayrollNumber },
                                (pwh, cps) => new { pwh, cps })
                        .Where(x => x.pwh.EmployeeID == employeeID &&
                                    x.pwh.CompanyID == companyID &&
                                    (x.cps.Schedule_Status > scheduleStatusMin && x.cps.Schedule_Status < scheduleStatusMax))
                        .Count();
        }
        public List<PayrollBatchDetail> GetPayrollBatchDetailsByEmployee(int companyID, string employeeID)
        {
            return _dbContext.PayrollBatchDetails.Where(x => x.CompanyID == companyID &&
                                                                        x.EmployeeID == employeeID)
                                                            .ToList();
        }
    }
}