using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class EditCalcLocksProvider : DbContextBaseProvider, IEditCalcLocksProvider
    {
        public EditCalcLocksProvider(DnetEntities dbContext) 
            : base(dbContext)
        {
        }

        public List<EditCalcLock> GetEmployeesEditCalcLocks(string payrollNumber, HashSet<string> employeeIds = null)
        {
            if (employeeIds == null)
            {
                employeeIds = new HashSet<string>();
            }

            return
                (
                    from editCalcLock in _dbContext.EditCalcLocks
                    where editCalcLock.PayrollNumber == payrollNumber &&
                        (employeeIds.Count == 0 || employeeIds.Contains(editCalcLock.EmployeeID))
                    select editCalcLock
                ).ToList();
        }

        public void LockEmployee(string employeeId, string payrollNumber, string userId)
        {
            var hasExistingLock = _dbContext.EditCalcLocks
                .Any(ecl => ecl.PayrollNumber == payrollNumber && 
                            ecl.EmployeeID == employeeId && 
                            ecl.UserID == userId);

            if (!hasExistingLock)
            {
                var editCalcLock = new EditCalcLock
                {
                    PayrollNumber = payrollNumber,
                    EmployeeID = employeeId,
                    UserID = userId,
                };

                this.AddItem(editCalcLock);
            }
        }

        public void UnlockEmployee(string employeeId, string payrollNumber)
        {
            var editCalcLock = _dbContext.EditCalcLocks
                .FirstOrDefault(ecl => ecl.EmployeeID == employeeId && 
                                       ecl.PayrollNumber == payrollNumber);

            this.RemoveItem(editCalcLock);
        }

        public void UnlockEmployeesByPayroll(string payrollNumber)
        {
            var editCalcLocks = _dbContext.EditCalcLocks
                .Where(ecl => ecl.PayrollNumber == payrollNumber)
                .ToList();

            this.RemoveItems(editCalcLocks);
        }

        public EditCalcLock Get(string employeeId, string payrollNumber)
        {
            return
                (
                    from editCalcLock in _dbContext.EditCalcLocks
                    where editCalcLock.PayrollNumber == payrollNumber &&
                        editCalcLock.EmployeeID == employeeId
                    select editCalcLock
                ).FirstOrDefault();
        }
    }
}