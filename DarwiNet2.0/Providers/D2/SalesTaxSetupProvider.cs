using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class SalesTaxSetupProvider : DbContextBaseProvider, ISalesTaxSetupProvider
    {
        #region Fields


        #endregion

        #region Constructors

        public SalesTaxSetupProvider(DnetEntities dbContext) 
            :base(dbContext)
        {
            
        }

        #endregion

        #region Public Methods

        public SalesTaxSetup GetSalesTaxSetup (int companyID, string taxDetailID)
        {
            return _dbContext.SalesTaxSetups
                .FirstOrDefault(s => s.CompanyID == companyID &&
                                     s.TaxDetailID == taxDetailID);
        }

        #endregion


    }
}