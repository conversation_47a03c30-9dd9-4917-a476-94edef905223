using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers;
using DarwiNet2._0.Interfaces.Providers.D2;
using EO.Pdf.Internal;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace DarwiNet2._0.Providers.D2
{
    /// <summary>
    ///     
    /// </summary>
    /// <history>
    ///     [mframe]    03/16/20    Task-5812: Created.
    ///     [lyeager]   06/16/20    Task-5863: Implemented ICompanyProvider, GetCompanies.
    ///     [lyeager]   08/03/20    Task-6296: Adapted for Entity Framework.
    /// </history>
    public class CompanyProvider : DbContextBaseProvider, ICompanyProvider
    {
        #region Fields 

        private readonly ISqlConnectionProvider _sqlConnectionProvider;
        private readonly string _connectionString;

        #endregion

        #region Constructors

        public CompanyProvider()
            : base()
        {
            _sqlConnectionProvider = new SqlConnectionProvider();
        }

        public CompanyProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        public CompanyProvider(string connectionString)
        {
            _connectionString = connectionString;
        }

        public CompanyProvider(DnetEntities dbContext, ISqlConnectionProvider sqlConnectionProvider)
            : base(dbContext)
        {
            _sqlConnectionProvider = sqlConnectionProvider;
        }

        #endregion

        #region Public Functions

        /// <summary>
        ///     Finds a <see cref="Companies"/> by id.
        /// </summary>
        /// <history>
        ///     [mframe]    03/16/20    Task-5812: Created.
        /// </history>
        public Company FindCompanyById(int companyId)
        {
            return _dbContext.Companies.FirstOrDefault(c => c.CompanyID == companyId);
        }

        /// <summary>
        ///     Finds the <see cref="Companies.CompanyName"/> using provided company id.
        ///     If no company is found, will return null.
        /// </summary>
        public string FindCompanyNameById(int companyId)
        {
            return _dbContext.Companies.FirstOrDefault(x => x.CompanyID == companyId)?.CompanyName;
        }

        public Dictionary<int, string> FindCompanyNamesByIds(IEnumerable<int> companyIds)
        {
            // NOTE: Duplicate IDs are not an issue in SQL. The list is flattened when query is ran.
            // EXAMPLE: [1, 1, 1, 2, 2, 2, 3, 3, 3] = [1, 2, 3]
            string query = "SELECT c.CompanyID, c.CompanyName FROM Companies c WHERE c.CompanyID IN (" + string.Join(",", companyIds) + ");";
            //IEnumerable<Company> result = _dbContext.ExecuteQuery<Company>(query);

            //IDataReader reader = _sqlConnectionProvider.ExecuteReader(query, CommandType.Text, new SqlParameter[0]);

            var companies = _dbContext.Companies.SqlQuery(query)
                .ToDictionary(x => x.CompanyID, x => x.CompanyName);

            //Dictionary<int, string> companies = new Dictionary<int, string>();
            //using (SqlConnection connection = new SqlConnection(_connectionString))
            //{
            //    SqlCommand sqlCommand = new SqlCommand(query, connection);
            //    sqlCommand.CommandType = CommandType.Text;
            //    connection.Open();
            //    var reader = sqlCommand.ExecuteReader();

            //    while (reader.Read())
            //    {
            //        var companyId = reader.GetInt32(0);
            //        var companyName = reader.GetString(1);

            //        companies.Add(companyId, companyName);
            //    }
            //}

            return companies;
        }

        public List<Company> GetCompanies()
        {
            return _dbContext.Companies.ToList();
        }

        public List<CompanyShort> GetCompaniesShort()
        {
            var companies = _dbContext.Companies.ToList();
            var companyShorts = new List<CompanyShort>();

            foreach (var company in companies)
            {
                companyShorts.Add(new CompanyShort
                {
                    CompanyID = company.CompanyID,
                    CompanyName = company.CompanyName
                });
            }

            return companyShorts;
        }

        public List<Code_Description> GetCompaniesAvailableToUser(string userID)
        {
            List<int> companyIDs = _dbContext.UserRoleClientEmployeeAssignments.Where(x => x.UserID == userID).Select(x => x.CompanyID).Distinct().ToList();
            var companies = _dbContext.Companies.Where(x => companyIDs.Contains(x.CompanyID)).Select(x => new Code_Description { Code = x.CompanyID.ToString(), Description = x.CompanyName }).ToList();
            return companies;
        }

        public Dictionary<int, string> GetCompanyNames()
        {
            return _dbContext.Companies.ToDictionary(c => c.CompanyID, c => c.CompanyName);
        }

        public Dictionary<int, Code_Description> GetCompanyNamesWithIds()
        {
            return _dbContext.Companies
                .ToDictionary(
                    c => c.CompanyID,
                    c => new Code_Description { Code = c.DnetCompanyID, Description = c.CompanyName }
                );
        }

        public string GetDNetClientId(int companyId)
        {
            return _dbContext.Companies.FirstOrDefault(x => x.CompanyID == companyId)?.DnetClientID;
        }

        public string GetFullDNetCompany(int companyId)
        {
            var comp = FindCompanyById(companyId);
            return (comp != null) ? comp.DnetCompanyID + "." + comp.DnetClientID : "*.*";
        }

        public string GetDNetUrl()
        {
            return _dbContext.ProjectSetups.FirstOrDefault()?.DnetAddress; // Hardcoded so it will work, need to rework URLs
        }

        public Code_Description GetCompanyIntercompanyIdWithNameById(int companyId)
        {
            return _dbContext.Companies
                .Where(c => c.CompanyID == companyId)
                .Select(c => new Code_Description
                { 
                    Code = c.IntercompanyID, 
                    Description = c.CompanyName 
                })
                .FirstOrDefault();
        }

        public CompanyContactDetailsModel GetCompanyContactDetails(int companyId)
        {
            return _dbContext.Companies
                .Where(c => c.CompanyID == companyId)
                .Select(c => new CompanyContactDetailsModel
                {
                    CompanyID = c.CompanyID,
                    CompanyName = c.CompanyName,
                    CompanyEIN = c.CompanyEIN,
                    Email = c.Email,
                    Phone1 = c.Phone1
                })
                .FirstOrDefault();
        }

        public bool HasSwipeClockCredentials(int companyId)
        {
            return _dbContext.Companies
                .Any(c => c.CompanyID == companyId &&
                          !string.IsNullOrEmpty(c.SwipeClockLogin) &&
                          !string.IsNullOrEmpty(c.SwipeClockPwd));
        }

        public string GetReprintPassword(int companyId)
        {
            return _dbContext.CompanyImages
                .FirstOrDefault(c => c.CompanyID == companyId)
                ?.ReprintPassword;
        }

        public bool CheckElectronicConsent(int companyId)
        {
            return _dbContext.Companies
                .FirstOrDefault(c => c.CompanyID == companyId)
                .EnableElectronicConsent;
        }

        public string FindInterCompanyById(int companyId)
        {
            return _dbContext.Companies.FirstOrDefault(c => c.CompanyID == companyId).IntercompanyID;
        }

        
        /// <summary>
        /// Find all the companies user being assigned to that have SMS two-factor authentication required 
        /// </summary>
        /// <param name="userName">The user name</param>
        /// <returns>A list of companies user belongs to with SMS policy requires</returns>
        public List<Company> FindCompanyUserAssignedToWithSmsPolicy(string userName)
        {
            // Find the company user being assigned to that has SMS two-factor authentication set up
            List<int> companiesUserAssignedTo = _dbContext.UserRoleClientEmployeeAssignments
                .Where(i => i.UserID == userName)
                .Select(i => i.CompanyID)
                .ToList();
            
           return _dbContext.Companies
               .Where(i => companiesUserAssignedTo.Contains(i.CompanyID) && i.EnableTwoFactorSms && i.TwilioAccountVerified)
               .ToList();
        }

        #endregion
    }
}