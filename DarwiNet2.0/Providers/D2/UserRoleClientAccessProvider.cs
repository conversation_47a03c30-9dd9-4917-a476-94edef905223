using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class UserRoleClientAccessProvider : DbContextBaseProvider, IUserRoleClientAccessProvider
    {
        #region Constructors

        public UserRoleClientAccessProvider()
            : base()
        {

        }

        public UserRoleClientAccessProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Functions

        public List<UserRoleClientAccess> GetUserClientAccesses(string userId)
        {
            return _dbContext.UserRoleClientAccesses
                .Where(u => u.UserID == userId)
                .ToList();
        }

        public List<int> GetUserAccessCompanyIds(string userId)
        {
            return GetUserClientAccesses(userId)
                .Select(u => u.CompanyID)
                .ToList();
        }

        public bool CheckCompanyAccessByUser(int companyId, string userId)
        {
            return GetUserAccessCompanyIds(userId)
                .Any(u => u == companyId);
        }

        public List<string> GetClientAccessUserIds(int companyId, string clientId)
        {
            return _dbContext.UserRoleClientAccesses
                .Where(u => u.CompanyID == companyId &&
                            u.ClientID == clientId)
                .Select(u => u.UserID)
                .Distinct()
                .ToList();
        }

        public List<string> GetClientAccessClientIds(int companyId, string userId)
        {
            return _dbContext.UserRoleClientAccesses
                .Where(x => x.CompanyID == companyId && x.UserID == userId)
                .OrderBy(x => x.ClientID)
                .Select(x => x.ClientID)
                .ToList();
        }

        public List<MultiselectOptionsDTO> GetClientPayrollAccessClientIds(int companyId, string userId)
        {
            return _dbContext.UserRoleClientAccesses
                .Where(x => x.CompanyID == companyId && x.UserID == userId && x.OverridePayroll)
                .Join(
                    _dbContext.Clients,
                    usr => new { usr.ClientID },
                    c => new { c.ClientID },
                    (usr, c) => new { usr, c }
                )
                .Select(x => new MultiselectOptionsDTO
                {
                    Code = x.c.ClientID,
                    Display = x.c.ClientName + " (" + x.c.ClientID + ")"
                })
                .ToList();
        }

        public List<MultiselectOptionsDTO> GetClientInvoiceAccessClientIds(int companyId, string userId)
        {
            return _dbContext.UserRoleClientAccesses
                .Where(x => x.CompanyID == companyId && x.UserID == userId && x.OverrideInvoice)
                .Join(
                    _dbContext.Clients,
                    usr => new { usr.ClientID },
                    c => new { c.ClientID },
                    (usr, c) => new { usr, c }
                )
                .Select(x => new MultiselectOptionsDTO
                {
                    Code = x.c.ClientID,
                    Display = x.c.ClientName + " (" + x.c.ClientID + ")"
                })
                .ToList();
        }


        public void AddClientAccesses(int companyId, string userId, string roleId, List<string> clients)
        {
            List<UserRoleClientAccess> result = new List<UserRoleClientAccess>();
            foreach (string client in clients)
            {
                if (!string.IsNullOrEmpty(client))
                {
                    result.Add(new UserRoleClientAccess
                    {
                        RoleID = roleId,
                        CompanyID = companyId,
                        ClientID = client,
                        UserID = userId,
                        OverridePayroll = false,
                        OverrideInvoice = false
                    });
                }
            }
            _dbContext.UserRoleClientAccesses.AddRange(result);
            _dbContext.SaveChanges();
        }

        public void UpdateUserRoleClientAccess(int companyId, string userId, string roleId, string clientId, bool overridePayroll, bool overrideInvoice)
        {
            var origSec = _dbContext.UserRoleClientAccesses.FirstOrDefault(udds => udds.CompanyID == companyId && udds.ClientID == clientId && udds.UserID == userId);
            if (origSec != null)
            {
                origSec.OverrideInvoice = overrideInvoice;
                origSec.OverridePayroll = overridePayroll;
            }
            else
            {
                var newClientSec = new UserRoleClientAccess
                {
                    CompanyID = companyId,
                    ClientID = clientId,
                    RoleID = roleId,
                    UserID = userId,
                    OverrideInvoice = overrideInvoice,
                    OverridePayroll = overridePayroll
                };
                _dbContext.UserRoleClientAccesses.Add(newClientSec);
            }
            _dbContext.SaveChanges();
        }

        public void RemoveSelectedClientAccessByIds(List<int> Ids)
        {
            var records = _dbContext.UserRoleClientAccesses.AsEnumerable().Where(x => Ids.Any(id => id == x.id)).ToList();
            if (records.Any())
            {
                _dbContext.UserRoleClientAccesses.RemoveRange(records);
                _dbContext.SaveChanges();
            }
        }

        public void RemoveClientAccessById(int id)
        {
            var rec = _dbContext.UserRoleClientAccesses.FirstOrDefault(urca => urca.id == id);
            if (rec != null)
            {
                _dbContext.UserRoleClientAccesses.Remove(rec);
                _dbContext.SaveChanges();
            }
        }

        public bool UserHasAccessToClient(int companyId, string clientId)
        {
            return _dbContext.UserRoleClientAccesses.Any(x => x.CompanyID == companyId && x.ClientID == clientId && x.UserID == GlobalVariables.DNETOwnerID);
        }

        public bool UserHasAccessToBillCost(int companyId, string clientId, string userId, string roleId)
        {
            return (_dbContext.UserRoleClientAccesses.FirstOrDefault(x => x.CompanyID == companyId && x.ClientID == clientId && x.UserID == userId && x.RoleID == roleId)?.BillCostAccess) ?? false;
        }
        #endregion
    }
}