using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class ClientPTOTypeProvider : DbContextBaseProvider, IClientPTOTypeProvider
    {
        #region Constructors 

        public ClientPTOTypeProvider()
            : base()
        {

        }

        public ClientPTOTypeProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        public ClientPTOType GetClientPTOType(int companyId, string clientId, string code)
        {
            return _dbContext.ClientPTOTypes
                .FirstOrDefault(x => x.CompanyID == companyId &&
                                     x.ClientID == clientId &&
                                     x.PTOType.ToString() == code);
        }

        public List<ClientPTOType> GetClientPTOTypes(int companyId, string clientId, List<string> codes)
        {
            return _dbContext.ClientPTOTypes
                .Where(c => c.CompanyID == companyId &&
                            c.ClientID == clientId &&
                            codes.Contains(c.PTOType.ToString()))
                .ToList();
        }
    }
}