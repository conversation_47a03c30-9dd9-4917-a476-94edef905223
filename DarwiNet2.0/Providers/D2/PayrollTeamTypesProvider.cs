using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.ViewModels.D2;
using Kendo.Mvc.Extensions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollTeamTypesProvider : DbContextBaseProvider, IPayrollTeamTypesProvider
    {
        #region Constructors

        public PayrollTeamTypesProvider()
            : base() { }

        public PayrollTeamTypesProvider(DnetEntities dbContext)
            : base(dbContext)
        {
        }

        #endregion

        #region Public Functions

        public void DeleteTeamType(string type)
        {
            var rec = _dbContext.PayrollTeamTypes.FirstOrDefault(x => x.TeamType == type);
            if (rec != null)
            {
                _dbContext.PayrollTeamTypes.Remove(rec);
                _dbContext.SaveChanges();
            }
        }

        public PayrollTeamType GetTeamType(string type)
        {
            return _dbContext.PayrollTeamTypes.FirstOrDefault(x => x.TeamType == type);
        }


        public IEnumerable<PayrollTeamType> GetTeamTypes()
        {
            return _dbContext.PayrollTeamTypes.OrderBy(x => x.TeamType);
        }

        public void UpcertTeamType(string type, string descr)
        {
            var rec = _dbContext.PayrollTeamTypes.FirstOrDefault(x => x.TeamType == type);
            if (rec == null)
            {
                PayrollTeamType newrec = new PayrollTeamType
                {
                    TeamType = type,
                    Description = descr
                };
                _dbContext.PayrollTeamTypes.Add(newrec);
            }
            else
            {
                rec.Description = descr;
            }
            _dbContext.SaveChanges();
        }
        #endregion
    }
}