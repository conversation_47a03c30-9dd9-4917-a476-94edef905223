using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DarwiNet2._0.Controllers;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Providers.D2
{
    //TODO: QUESTION: Do we need this?
    public class LocationCodeProvider
    {
        //public IEnumerable<Code_Description> GetAvailableEmployeeSelectLocations(string clientID, EntitiesModel _dbContext)
        //{
        //    var departments = _dbContext.Departments.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.Department.StartsWith(clientID)).ToList();

        //    var availableDepartments = departments.Select(dep => new Code_Description
        //    {
        //        Code = dep.Department,
        //        Description = dep.Description
        //    });

        //    return availableDepartments;
        //}

        //public IEnumerable<Code_Description> GetSelectedEmployeeSelectLocations(string clientID,EntitiesModel _dbContext,List<PayrollBuildEmployeeCriterias> criterias)
        //{
        //    //var departments = _dbContext.Departments.Where(p => p.CompanyID == GlobalVariables.CompanyID && p.Department.StartsWith(clientID)).ToList();

        //    var selectedDepartments = criterias.Where(cr => cr.SelectionType == "Locations").Select(na => new Code_Description
        //    {
        //        Code = na.CodeID
        //    });

        //    //foreach (var item in selectedDepartments)
        //    //{
        //    //    var dp = departments.FirstOrDefault(dpt => dpt.Department == item.Code);
        //    //    if (dp != null)
        //    //    {
        //    //        item.Description = dp.Description;
        //    //    }
        //    //}

        //    return selectedDepartments;

        //}
    }
}