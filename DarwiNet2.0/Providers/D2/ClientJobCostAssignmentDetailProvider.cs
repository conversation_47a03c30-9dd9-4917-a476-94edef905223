using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.ViewModels.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace DarwiNet2._0.Providers.D2
{
    public class ClientJobCostAssignmentDetailProvider : DbContextBaseProvider, IClientJobCostAssignmentDetailProvider
    {
        #region Constructors

        public ClientJobCostAssignmentDetailProvider()
            : base()
        {

        }

        public ClientJobCostAssignmentDetailProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        public TableQueryInfo<ClientJobCostAssignmentDetailDTO> GetJobLevelTableData(TableFilter filters, string userId)
        {
            var activeFilters = filters.Filters
                .Where(x => x.ActiveFilters.Count > 0)
                .ToDictionary(x => x.Code, x => x.ActiveFilters);

            var companyId = activeFilters["CompanyID"]
                .Select(x => (int)x.Code)
            .FirstOrDefault();

            var clientId = activeFilters["ClientID"]
                .Select(x => (string)x.Code)
            .FirstOrDefault();

            var jobCostingName = activeFilters["JobCostingName"]
                    .Select(x => (string)x.Code)
                .FirstOrDefault();

            var nonFilteredQuery = _dbContext.ClientJobCostAssignmentDetails
                .Join(_dbContext.UserRoleClientAccesses,
                        detail => new { detail.CompanyID, detail.ClientID },
                        urca => new { urca.CompanyID, urca.ClientID },
                        (detail, urca) => new { detail, urca })
                .Where(x => x.detail.CompanyID == companyId && x.detail.ClientID == clientId && x.detail.JobCostingName == jobCostingName && x.urca.UserID == userId);

            if (GlobalVariables.CurrentUser.ClientLevelEnabled)
            {
                nonFilteredQuery = nonFilteredQuery.Where(x => x.urca.ClientID == GlobalVariables.Client);
            };

            var filteredQuery = nonFilteredQuery.Select(x => x.detail);

            if (activeFilters.ContainsKey("JobLevel2"))
            {
                var level2s = activeFilters["JobLevel2"]
                    .Select(x => (string)x.Code)
                .ToList();

                filteredQuery = filteredQuery.Where(x => level2s.Contains(x.JobLevel2));
            }

            if (activeFilters.ContainsKey("JobLevel3"))
            {
                var level3s = activeFilters["JobLevel3"]
                    .Select(x => (string)x.Code)
                .ToList();

                filteredQuery = filteredQuery.Where(x => level3s.Contains(x.JobLevel3));
            }

            if (activeFilters.ContainsKey("JobLevel4"))
            {
                var level4s = activeFilters["JobLevel4"]
                    .Select(x => (string)x.Code)
                .ToList();

                filteredQuery = filteredQuery.Where(x => level4s.Contains(x.JobLevel4));
            }

            if (activeFilters.ContainsKey("JobLevel5"))
            {
                var level5s = activeFilters["JobLevel5"]
                    .Select(x => (string)x.Code)
                .ToList();

                filteredQuery = filteredQuery.Where(x => level5s.Contains(x.JobLevel5));
            }

            if (activeFilters.ContainsKey("JobLevel6"))
            {
                var level6s = activeFilters["JobLevel6"]
                    .Select(x => (string)x.Code)
                .ToList();

                filteredQuery = filteredQuery.Where(x => level6s.Contains(x.JobLevel6));
            }

            return new TableQueryInfo<ClientJobCostAssignmentDetailDTO>
            {
                Query = filteredQuery.Select(x => new ClientJobCostAssignmentDetailDTO()
                {
                    JBSID = x.JBSID,
                    CompanyID = x.CompanyID,
                    ClientID = x.ClientID,
                    JobCostingName = x.JobCostingName,
                    JobLevel2 = x.JobLevel2,
                    JobLevel3 = x.JobLevel3,
                    JobLevel4 = x.JobLevel4,
                    JobLevel5 = x.JobLevel5,
                    JobLevel6 = x.JobLevel6,
                    DefaultHourlyCode = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.DefaultHourlyCode,
                            Display = x.DefaultHourlyCode
                        }
                    },
                    DefaultHourlyRate = x.RegularRate ?? 0,
                    DefaultOTCode = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.DefaultOTCode,
                            Display = x.DefaultOTCode
                        }
                    },
                    DefaultOTRate = x.OTRate ?? 0,
                    DefaultSalaryCode = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.DefaultSalaryCode,
                            Display = x.DefaultSalaryCode
                        }
                    },
                    DefaultSalaryRate = x.DefaultSalaryRate ?? 0,
                    DefaultSalaryOTCode = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.DefaultSalOTCode,
                            Display = x.DefaultSalOTCode
                        }
                    },
                    DefaultSalaryOTRate = x.DefaultSalaryOTRate ?? 0,
                    Department = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.Department,
                            Display = x.Department
                        }
                    },
                    StateTax = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.StateCode,
                            Display = x.StateCode
                        }
                    },
                    WorkersComp = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.WorkersComp,
                            Display = x.WorkersComp
                        }
                    },
                    LocalTax = new List<MultiselectOptionsDTO>
                    {
                        new MultiselectOptionsDTO
                        {
                            Code = x.LocalTax,
                            Display = x.LocalTax
                        }
                    },
                    EstimatedStart = x.EstimatedStartDate,
                    EstimatedEnd = x.EstimatedCompletion,
                    EstimatedHours = x.EstimatedHours,
                    EstimatedCosts = x.Cost
                }),
                TotalEntries = nonFilteredQuery.Count(),
                FilteredEntries = filteredQuery.Count(),
            };
        }
        public ClientJobCostAssignmentDetail GetClientJobCostAssignmentDetail(int companyId, string clientId, string jobCostingName, string jobLevel2, string jobLevel3, string jobLevel4, string jobLevel5, string jobLevel6)
        {
            return _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == companyId &&
                            x.ClientID == clientId &&
                            x.JobCostingName == jobCostingName &&
                            x.JobLevel2 == jobLevel2 &&
                            x.JobLevel3 == jobLevel3 &&
                            x.JobLevel4 == jobLevel4 &&
                            x.JobLevel5 == jobLevel5 &&
                            x.JobLevel6 == jobLevel6)
                .FirstOrDefault();
        }

        public bool CheckClientJobCostAssignmentDetailForUniqueness(int companyId, string clientId, string jobCostingName, string jobLevel2, string jobLevel3, string jobLevel4, string jobLevel5, string jobLevel6)
        {
            var records = _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == companyId &&
                            x.ClientID == clientId &&
                            x.JobCostingName == jobCostingName &&
                            x.JobLevel2.ToLower() == jobLevel2.ToLower() &&
                            x.JobLevel3.ToLower() == jobLevel3.ToLower() &&
                            x.JobLevel4.ToLower() == jobLevel4.ToLower() &&
                            x.JobLevel5.ToLower() == jobLevel5.ToLower() &&
                            x.JobLevel6.ToLower() == jobLevel6.ToLower());

            // If there are any records, it is not unique
            if (records.Any())
            {
                return false;
            }

            return true;
        }

        public List<MultiselectOptionsDTO> GetClientJobCostAssignmentDetailLevelOptions(int companyId, string clientId, string jobCostingName, int level)
        {
            var clientJobCostAssignmentDetails = _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == companyId &&
                            x.ClientID == clientId &&
                            x.JobCostingName == jobCostingName)
                .ToList();

            var result = new List<string>();

            if (level == 2)
            {
                result = clientJobCostAssignmentDetails.Select(x => x.JobLevel2).Distinct().ToList();
            }
            if (level == 3)
            {
                result = clientJobCostAssignmentDetails.Select(x => x.JobLevel3).Distinct().ToList();
            }
            if (level == 4)
            {
                result = clientJobCostAssignmentDetails.Select(x => x.JobLevel4).Distinct().ToList();
            }
            if (level == 5)
            {
                result = clientJobCostAssignmentDetails.Select(x => x.JobLevel5).Distinct().ToList();
            }
            if (level == 6)
            {
                result = clientJobCostAssignmentDetails.Select(x => x.JobLevel6).Distinct().ToList();
            }

            return result.Select(x => new MultiselectOptionsDTO
            {
                Code = x,
                Display = x
            }).ToList();
        }

        public void UpdateClientJobCostAssignmentDetailPaycodeInformation(ClientJobCostAssignmentDetailPaycodeDTO form, int companyId, string clientId, string jobCostingName, string jobLevel2, string jobLevel3, string jobLevel4, string jobLevel5, string jobLevel6)
        {
            var clientJobCostAssignmentDetail = _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == companyId &&
                            x.ClientID == clientId &&
                            x.JobCostingName == jobCostingName &&
                            x.JobLevel2 == jobLevel2 &&
                            x.JobLevel3 == jobLevel3 &&
                            x.JobLevel4 == jobLevel4 &&
                            x.JobLevel5 == jobLevel5 &&
                            x.JobLevel6 == jobLevel6)
                .FirstOrDefault();

            if (clientJobCostAssignmentDetail != null)
            {
                clientJobCostAssignmentDetail.DefaultHourlyCode = form.HourlyPaycode != null ? form.HourlyPaycode.First().Code : "";
                clientJobCostAssignmentDetail.RegularRate = form.HourlyRate;
                clientJobCostAssignmentDetail.DefaultOTCode = form.OvertimePaycode != null ? form.OvertimePaycode.First().Code : "";
                clientJobCostAssignmentDetail.OTRate = form.OvertimeRate;
                clientJobCostAssignmentDetail.DefaultSalaryCode = form.SalaryPaycode != null ? form.SalaryPaycode.First().Code : "";
                clientJobCostAssignmentDetail.DefaultSalaryRate = form.SalaryRate;
                clientJobCostAssignmentDetail.DefaultSalOTCode = form.SalaryOTPaycode != null ? form.SalaryOTPaycode.First().Code : "";
                clientJobCostAssignmentDetail.DefaultSalaryOTRate = form.SalaryOTRate;
                clientJobCostAssignmentDetail.Department = form.Department != null ? form.Department.First().Code : "";
                clientJobCostAssignmentDetail.StateCode = form.StateTax.First().Code;
                clientJobCostAssignmentDetail.WorkersComp = form.WorkersComp != null ? form.WorkersComp.First().Code : "";
                clientJobCostAssignmentDetail.LocalTax = form.LocalTax != null ? form.LocalTax.First().Code : "";
                clientJobCostAssignmentDetail.EstimatedStartDate = form.EstimatedStart;
                clientJobCostAssignmentDetail.EstimatedHours = form.EstimatedHours;
                clientJobCostAssignmentDetail.EstimatedCompletion = form.EstimatedEnd;
                clientJobCostAssignmentDetail.Cost = form.EstimatedCosts;

                _dbContext.SaveChanges();
            }
        }

        public void CreateClientJobCostAssignmentDetail(CreateClientJobCostAssignmentDetailFormDTO form)
        {
            // Ensure job levels do not already exist (needs to have different lowest level job)
            var existingRecord = _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == form.CompanyID &&
                            x.ClientID == form.ClientID &&
                            x.JobCostingName == form.JobCostingName &&
                            x.JobLevel2 == (form.Level2 ?? "") &&
                            x.JobLevel3 == (form.Level3 ?? "") &&
                            x.JobLevel4 == (form.Level4 ?? "") &&
                            x.JobLevel5 == (form.Level5 ?? "") &&
                            x.JobLevel6 == (form.Level6 ?? ""));

            if (existingRecord.Any())
            {
                throw new Exception("Job with those levels already exist. Please select a new lowest level job.");
            }

            var jbsValue = 1;

            // Get distinct JBS ids for company
            var jbsIds = _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == form.CompanyID)
                .Select(x => x.JBSID)
                .Distinct()
                .ToList();

            var intJbsIds = new List<int>();

            // because they are a string in the DB, try and parse them into ints
            foreach (var jbsId in jbsIds)
            {
                if (int.TryParse(jbsId, out int parsed))
                {
                    intJbsIds.Add(parsed);
                }
            }

            // if there are any valid int jbs ids then set the new jbs value we will use to the max + 1
            if (intJbsIds.Count > 0)
            {
                jbsValue = intJbsIds.Max() + 1;
            }

            var clientJobCostAssignmentDetail = new ClientJobCostAssignmentDetail
            {
                CompanyID = form.CompanyID,
                ClientID = form.ClientID,
                JobCostingName = form.JobCostingName,
                JobLevel2 = form.Level2 ?? "",
                JobLevel3 = form.Level3 ?? "",
                JobLevel4 = form.Level4 ?? "",
                JobLevel5 = form.Level5 ?? "",
                JobLevel6 = form.Level6 ?? "",
                JBSID = jbsValue.ToString(),
                EstimatedStartDate = form.EstimatedStart,
                EstimatedCompletion = form.EstimatedEnd,
                EstimatedHours = form.EstimatedHours,
                Cost = form.EstimatedCost,
                ProjectManager = form.ProjectManager,
                Phone1 = form.Phone,
                Email = form.Email,
                WorkersComp = form.WorkersComp != null ? form.WorkersComp.FirstOrDefault().Code : "",
                Department = form.Department != null ? form.Department.FirstOrDefault().Code : "",
                DefaultHourlyCode = form.HourlyPaycode != null ? form.HourlyPaycode.FirstOrDefault().Code : "",
                DefaultOTCode = form.OvertimePaycode != null ? form.OvertimePaycode.FirstOrDefault().Code : "",
                DefaultSalaryCode = form.SalaryPaycode != null ? form.SalaryPaycode.FirstOrDefault().Code : "",
                DefaultSalOTCode = form.SalaryOTPaycode != null ? form.SalaryOTPaycode.FirstOrDefault().Code : "",
                StateCode = form.StateTax != null ? form.StateTax.FirstOrDefault().Code : "",
                LocalTax = form.LocalTax != null ? form.LocalTax.FirstOrDefault().Code : "",
                RegularRate = form.HourlyRate,
                OTRate = form.OvertimeRate,
                DefaultSalaryRate = form.SalaryRate,
                DefaultSalaryOTRate = form.SalaryOTRate
            };

            _dbContext.ClientJobCostAssignmentDetails.Add(clientJobCostAssignmentDetail);
            _dbContext.SaveChanges();
        }

        public void RemoveClientJobCostAssignmentDetail(int companyId, string clientId, string jobCostingName, string jobLevel2, string jobLevel3, string jobLevel4, string jobLevel5, string jobLevel6)
        {
            var clientJobCostAssignmentDetails = _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == companyId &&
                            x.ClientID == clientId &&
                            x.JobCostingName == jobCostingName &&
                            x.JobLevel2 == jobLevel2 &&
                            x.JobLevel3 == jobLevel3 &&
                            x.JobLevel4 == jobLevel4 &&
                            x.JobLevel5 == jobLevel5 &&
                            x.JobLevel6 == jobLevel6)
                .FirstOrDefault();

            if (clientJobCostAssignmentDetails != null)
            {
                _dbContext.ClientJobCostAssignmentDetails.Remove(clientJobCostAssignmentDetails);
            }

            _dbContext.SaveChanges();
        }

        public void RemoveClientJobCostAssignmentDetailsByJob(int companyId, string clientId, string jobCostingName)
        {
            var clientJobCostAssignmentDetails = _dbContext.ClientJobCostAssignmentDetails
                .Where(x => x.CompanyID == companyId &&
                            x.ClientID == clientId &&
                            x.JobCostingName == jobCostingName);

            if (clientJobCostAssignmentDetails.Any())
            {
                _dbContext.ClientJobCostAssignmentDetails.RemoveRange(clientJobCostAssignmentDetails);
            }

            _dbContext.SaveChanges();
        }
    }
}