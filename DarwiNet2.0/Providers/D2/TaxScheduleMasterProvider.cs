using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Data.Entity.Migrations;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class TaxScheduleMasterProvider : DbContextBaseProvider, ITaxScheduleMasterProvider
    {
        #region Fields



        #endregion

        #region Constructors

        public TaxScheduleMasterProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public TaxScheduleMaster FindTaxMasterSchedule (int companyID, string taxScheduleID, string taxDetailID)
        {
            return _dbContext.TaxScheduleMasters.FirstOrDefault(x => x.CompanyID == companyID &&
                                                                    x.TaxScheduleID == taxScheduleID &&
                                                                    x.TaxDetailID == taxDetailID);
        }

        public List<TaxScheduleMaster> ListTaxScheduleMasters (int companyID, string taxScheduleID)
        {
            return _dbContext.TaxScheduleMasters.Where(x => x.CompanyID == companyID &&
                                                            x.TaxScheduleID == taxScheduleID)
                                                .ToList() ?? new List<TaxScheduleMaster> { };
        }

        #endregion
    }
}