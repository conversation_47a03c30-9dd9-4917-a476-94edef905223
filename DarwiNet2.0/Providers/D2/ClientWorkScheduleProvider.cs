using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class ClientWorkScheduleProvider : DbContextBaseProvider, IClientWorkScheduleProvider
    {
        #region Constructors

        public ClientWorkScheduleProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region IClientWorkScheduleProvider Implementation
        /// <summary>
        /// Get client work schedule based off company, client, and division.  If there was no schedule found
        /// for a division it goes to the client level.
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="clientId"></param>
        /// <param name="divisionId"></param>
        /// <history>
        ///     [dkocher]   07/15/20    Task-6347: Add logic to create new payroll schedule and future schedules
        /// </history>
        /// <returns></returns>
        public List<DayOfWeek> GetClientWorkSchedule(int companyId, string clientId, string divisionId)
        {
            var clientWorkSchedule =
                _dbContext.ClientWorkSchedules.
                FirstOrDefault(cws =>
                    cws.CompanyID == companyId &&
                    cws.ClientID == clientId &&
                    cws.DivisionID == divisionId
                );

            if (clientWorkSchedule == null) 
                clientWorkSchedule =
                    _dbContext.ClientWorkSchedules.
                    FirstOrDefault(cws =>
                        cws.CompanyID == companyId &&
                        cws.ClientID == clientId &&
                        string.IsNullOrEmpty(cws.DivisionID)
                    );

            return GetWorkSchedule(clientWorkSchedule);
        }

        /// <summary>
        /// Takes the work schedule and turns it into a list of DayOfWeek values
        /// </summary>
        /// <param name="clientWorkSchedule"></param>
        /// <history>
        ///     [dkocher]   07/15/20    Task-6347: Add logic to create new payroll schedule and future schedules
        /// </history>
        /// <returns></returns>
        public List<DayOfWeek> GetWorkSchedule(ClientWorkSchedule clientWorkSchedule)
        {
            if (clientWorkSchedule == null)
            {
                return new List<DayOfWeek>
                {
                    DayOfWeek.Monday,
                    DayOfWeek.Tuesday,
                    DayOfWeek.Wednesday,
                    DayOfWeek.Thursday,
                    DayOfWeek.Friday
                };
            }

            var workSchedule = new List<DayOfWeek>();
            if (clientWorkSchedule.WorkDay1)
            {
                workSchedule.Add(DayOfWeek.Sunday);
            }
            if (clientWorkSchedule.WorkDay2)
            {
                workSchedule.Add(DayOfWeek.Monday);
            }
            if (clientWorkSchedule.WorkDay3)
            {
                workSchedule.Add(DayOfWeek.Tuesday);
            }
            if (clientWorkSchedule.WorkDay4)
            {
                workSchedule.Add(DayOfWeek.Wednesday);
            }
            if (clientWorkSchedule.WorkDay5)
            {
                workSchedule.Add(DayOfWeek.Thursday);
            }
            if (clientWorkSchedule.WorkDay6)
            {
                workSchedule.Add(DayOfWeek.Friday);
            }
            if (clientWorkSchedule.WorkDay7)
            {
                workSchedule.Add(DayOfWeek.Saturday);
            }

            return workSchedule;
        }

        #endregion
    }
}