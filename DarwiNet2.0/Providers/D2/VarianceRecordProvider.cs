using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Providers.D2
{
    public class VarianceRecordProvider : DbContextBaseProvider, IVarianceRecordProvider
    {
        #region Constructors

        public VarianceRecordProvider()
            : base()
        {

        }

        public VarianceRecordProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Functions

        public List<VarianceRecord> GetVarianceRecords(string payrollNumber, IEnumerable<string> employeeIds)
        {
            var set = new HashSet<string>(employeeIds);
            return _dbContext.VarianceRecords
                .Where(x => x.PayrollNumber == payrollNumber
                    && set.Contains(x.EmployeeID)
                    && x.Acknowledged == 0)
                .ToList();
        }

        public List<VarianceRecord> GetVarianceRecordsByType(string payrollNumber, IEnumerable<string> varianceTypes, string field)
        {
            var set = new HashSet<string>(varianceTypes);
            if (field != null)
            {
                return _dbContext.VarianceRecords
                    .Where(x => x.PayrollNumber == payrollNumber
                        && set.Contains(x.VarianceType)
                        && x.Field == field
                        && x.Acknowledged == 0)
                    .ToList();
            } 
            else
            {
                return _dbContext.VarianceRecords
                    .Where(x => x.PayrollNumber == payrollNumber
                        && set.Contains(x.VarianceType)
                        && x.Acknowledged == 0)
                    .ToList();
            }
        }

        public List<VarianceRecord> GetVarianceRecordsByCompanyClientDivisionInvoiceNum(int companyId, string clientId, string division, int invoiceNum)
        {
            return _dbContext.VarianceRecords
                .Where(x => x.CompanyID == companyId)
                .Where(x => x.ClientID == clientId)
                .Where(x => x.DivisionID == division)
                .Where(x => x.InvoiceNumber == invoiceNum)
                .Where(x => x.Acknowledged == 0)
                .ToList();
        }

        public List<VarianceRecord> GetVarianceRecordsByCompanyClientDivisionInvoiceNumType(int companyId, string clientId, string division, int invoiceNum, IEnumerable<string> varianceTypes)
        {
            var set = new HashSet<string>(varianceTypes);
            return _dbContext.VarianceRecords
                .Where(x => x.CompanyID == companyId)
                .Where(x => x.ClientID == clientId)
                .Where(x => x.DivisionID == division)
                .Where(x => x.InvoiceNumber == invoiceNum)
                .Where(x => x.Acknowledged == 0)
                .Where(x => set.Contains(x.VarianceType))
                .ToList();
        }

        public VarianceRecord GetVarianceRecord(int id)
        {
            return _dbContext.VarianceRecords.FirstOrDefault(x => x.ID == id);
        }

        public List<VarianceRecord> GetVarianceRecords(string payrollNumber)
        {
            return _dbContext.VarianceRecords
                .Where(x => x.PayrollNumber == payrollNumber
                    && x.InvoiceNumber == null
                    && x.Acknowledged == 0)
                .ToList();
        }

        public List<VarianceRecord> GetVarianceRecords(string payrollNumber, int invoiceNumber)
        {
            return _dbContext.VarianceRecords
                .Where(x => x.PayrollNumber == payrollNumber
                    && x.InvoiceNumber == invoiceNumber
                    && x.Acknowledged == 0)
                .ToList();
        }

        public void AcknowledgeVarianceRecord(string payrollNumber, string employeeId, string varianceType, string field)
        {
            VarianceRecord varianceRecord = _dbContext.VarianceRecords
                .SingleOrDefault(x => x.PayrollNumber == payrollNumber
                    && x.EmployeeID == employeeId
                    && x.VarianceType == varianceType
                    && x.Field == field);

            varianceRecord.Acknowledged = 1;
            varianceRecord.AcknowledgeDate = DateTime.Now;
            varianceRecord.AcknowledgeUserId = GlobalVariables.DNETOwnerID;
            _dbContext.SaveChanges();
        }
        
        public void AcknowledgeInvoiceVarianceRecord(string payrollNumber, string employeeId, string varianceType, string field, int invoiceNumber)
        {
            VarianceRecord varianceRecord = _dbContext.VarianceRecords
                .SingleOrDefault(x => x.PayrollNumber == payrollNumber
                    && x.EmployeeID == employeeId
                    && x.VarianceType == varianceType
                    && x.Field == field
                    && x.InvoiceNumber == invoiceNumber);

            varianceRecord.Acknowledged = 1;
            varianceRecord.AcknowledgeDate = DateTime.Now;
            varianceRecord.AcknowledgeUserId = GlobalVariables.DNETOwnerID;
            _dbContext.SaveChanges();
        }

        public void AcknowledgeVarianceRecordById(int id)
        {
            VarianceRecord varianceRecord = _dbContext.VarianceRecords
                .SingleOrDefault(x => x.ID== id);

            varianceRecord.Acknowledged = 1;
            varianceRecord.AcknowledgeDate = DateTime.Now;
            varianceRecord.AcknowledgeUserId = GlobalVariables.DNETOwnerID;
            _dbContext.SaveChanges();
        }

        public void AcknowledgeAll(PayrollNumber oPayrollNumber, int? invoiceNumber = null, string employeeID = null)
        {
            var payrollNumber = oPayrollNumber.ToString();
            var entities = _dbContext.VarianceRecords
                .Where(x =>
                    x.PayrollNumber == payrollNumber &&
                    x.InvoiceNumber == invoiceNumber &&
                    x.Acknowledged == 0)
                .ToList();

            if (employeeID != null)
            {
                entities = entities.Where(x => x.EmployeeID== employeeID).ToList();
            }

            if (!entities.Any())
                return;

            foreach (var entity in entities)
            {
                entity.Acknowledged = 1;
                entity.AcknowledgeDate = DateTime.Now;
                entity.AcknowledgeUserId = GlobalVariables.DNETOwnerID;
            }

            _dbContext.SaveChanges();
        }

        public bool AnyUnacknowledgedRecords(PayrollNumber oPayrollNumber, int? invoiceNumber = null)
        {
            var payrollNumber = oPayrollNumber.ToString();
            return _dbContext.VarianceRecords
                .Where(x =>
                    x.PayrollNumber == payrollNumber &&
                    x.InvoiceNumber == invoiceNumber &&
                    x.Acknowledged == 0)
                .Any();
        }

        public TableQueryInfo<VarianceRecordDisplayTableDTO> GetPayrollVarianceAuditTableData(TableFilter filters)
        {
            var activeFilters = filters.Filters
                .Where(x => x.ActiveFilters.Count > 0)
                .ToDictionary(x => x.Code, x => x.ActiveFilters);

            var payrollNumber = activeFilters["PayrollNumber"]
                .Select(x => (string)x.Code)
                .FirstOrDefault();

            var query = _dbContext.VarianceRecords
                .Where(vr => vr.PayrollNumber == payrollNumber &&
                             vr.InvoiceNumber == null)
                .Select(vr => new VarianceRecordDisplayTableDTO()
                {
                    ID = vr.ID,
                    PayrollNumber = payrollNumber,
                    EmployeeID = vr.EmployeeID,
                    VarianceName = vr.VarianceType,
                    Field = vr.Field,
                    CalculatedVariance = vr.CalculatedVariance.ToString(),
                    VarianceAmount = vr.VarianceAmount.ToString(),
                    TotalVariance = vr.TotalVariance.ToString(),
                    AcknowledgeDate = vr.AcknowledgeDate,
                    AcknowledgeUserId = vr.AcknowledgeUserId,
                });

            return new TableQueryInfo<VarianceRecordDisplayTableDTO>()
            {
                Query = query,
                FilteredEntries = query.Count(),
                TotalEntries = query.Count()
            };
        }

        public TableQueryInfo<VarianceRecordDisplayTableDTO> GetInvoiceVarianceAuditTableData(TableFilter filters)
        {
            var activeFilters = filters.Filters
                .Where(x => x.ActiveFilters.Count > 0)
                .ToDictionary(x => x.Code, x => x.ActiveFilters);

            var companyId = activeFilters["CompanyID"]
                    .Select(x => x.Code)
                    .Select(x => (int)x)
                    .FirstOrDefault();

            var clientId = activeFilters["ClientID"]
                .Select(x => (string)x.Code)
                .FirstOrDefault();

            var divisionId = activeFilters["DivisionID"]
                .Select(x => (string)x.Code)
                .FirstOrDefault();

            var invoiceNumber = activeFilters["InvoiceNumber"]
                    .Select(x => int.Parse(x.Code))
                    .Select(x => (int)x)
                    .FirstOrDefault();

            var query = _dbContext.VarianceRecords
                .Where(vr => vr.CompanyID == companyId &&
                             vr.ClientID == clientId &&
                             vr.DivisionID == divisionId &&
                             vr.InvoiceNumber == invoiceNumber &&
                             vr.Acknowledged == 1)
                .Select(vr => new VarianceRecordDisplayTableDTO()
                {
                    ID = vr.ID,
                    PayrollNumber = vr.PayrollNumber,
                    InvoiceNumber = vr.InvoiceNumber,
                    EmployeeID = vr.EmployeeID,
                    VarianceName = vr.VarianceType,
                    Field = vr.Field,
                    CalculatedVariance = vr.CalculatedVariance.ToString(),
                    VarianceAmount = vr.VarianceAmount.ToString(),
                    TotalVariance = vr.TotalVariance.ToString(),
                    AcknowledgeDate = vr.AcknowledgeDate,
                    AcknowledgeUserId = vr.AcknowledgeUserId,
                });

            return new TableQueryInfo<VarianceRecordDisplayTableDTO>()
            {
                Query = query,
                FilteredEntries = query.Count(),
                TotalEntries = query.Count()
            };
        }

        #endregion
    }
}