using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Interfaces.Providers.D2;
using Kendo.Mvc.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace DarwiNet2._0.Providers.D2
{
    public class DeductionProvider : DbContextBaseProvider, IDeductionProvider
    {
        #region Constructors 

        public DeductionProvider()
            : base()
        {

        }

        public DeductionProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        public IEnumerable<Code_Description> GetDeductions(int companyId, string clientId)
        {
            return _dbContext.Deductions
                .Where(d => d.CompanyID == companyId &&
                            d.Inactive != true)
                .Join(
                    _dbContext.ClientDivisionPayrollCodes,
                    d => new { d.CompanyID, DeductionCode = d.Deduction1 },
                    c => new { c.<PERSON>, DeductionCode = c.PayrollCode },
                    (d, c) => new { d, c })
                .Where(dc => dc.c.ClientID == clientId &&
                             dc.c.PayrollCodeType == (byte)PayrollCodeType.Deductions)
                .OrderBy(dc => dc.d.Deduction1)
                .Select(dc => new Code_Description
                {
                    Code = dc.d.Deduction1,
                    Description = dc.d.Description
                })
                .GroupBy(dc => dc.Code)
                .Select(x => x.FirstOrDefault());
        }

        public List<Deduction> List(int companyId)
        {
            return _dbContext.Deductions
                .Where(d => d.CompanyID == companyId &&
                            !d.Inactive)
                .OrderBy(d => d.Deduction1)
                .ToList();
        }

        public List<Deduction> FindDeductionsByCodes(int companyId, List<string> deductionCodes)
        {
            return _dbContext.Deductions
                .Where(x => x.CompanyID == companyId &&
                            deductionCodes.Contains(x.Deduction1))
                .ToList();
        }

        public Deduction GetDeduction(int companyID, string deduction)
        {
            return _dbContext.Deductions.First(x => x.CompanyID == companyID && x.Deduction1 == deduction);
        }

        public List<string> ListUniqueDeductionCodes(int companyID)
        {
            return _dbContext.Deductions
                .Where(x => x.CompanyID == companyID)
                .Select(x => x.Deduction1)
                .Distinct()
                .ToList();
        }
    }
}