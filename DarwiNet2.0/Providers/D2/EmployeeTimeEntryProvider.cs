using DarwiNet2._0.Controllers;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using DarwiNet2._0.Extensions;

namespace DarwiNet2._0.Providers.D2
{
    public class EmployeeTimeEntryProvider : DbContextBaseProvider, IEmployeeTimeEntryProvider
    {
        #region Constructors

        public EmployeeTimeEntryProvider()
            : base()
        {

        }

        public EmployeeTimeEntryProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        public void UpdateTimeEntries(int companyId, int timeSheetId)
        {
            List<EmployeeTimeEntry> tes = _dbContext.EmployeeTimeEntries
                .Where(t => t.CompanyID == companyId &&
                            t.TimeSheetID == timeSheetId &&
                            t.Status != TimeEntryStatus.Locked &&
                            t.Status != TimeEntryStatus.Released &&
                            t.Status != TimeEntryStatus.Deleted)
                .ToList();

            foreach (EmployeeTimeEntry te in tes)
            {
                te.OriginStatus = te.Status;
                te.Status = TimeEntryStatus.Locked;
                te.LastUpdate = DateTime.Now;
            }
            _dbContext.SaveChanges();
        }

        public List<EmployeeTimeEntry> GetEmployeeTimeEntries(int companyId, string clientId, int timeSheetId) =>
            _dbContext.EmployeeTimeEntries
            .Where(t =>
                t.CompanyID == companyId &&
                t.ClientID == clientId &&
                t.TimeSheetID == timeSheetId)
            .ToList();

        public List<EmployeeTimeEntry> GetEmployeeTimeEntries(int companyId, string clientId, int timeSheetId, IEnumerable<string> employeeIds, short status) =>
            _dbContext.EmployeeTimeEntries
            .Where(x =>
                x.CompanyID == companyId &&
                x.ClientID == clientId &&
                x.TimeSheetID == timeSheetId &&
                employeeIds.Contains(x.EmployeeID) &&
                x.Status == status)
            .ToList();

        public List<EmployeeTimeEntry> GetEmployeeTimeEntries(Expression<Func<EmployeeTimeEntry, bool>> expression) =>
            _dbContext.EmployeeTimeEntries
            .Where(expression)
            .ToList();

        public EmployeeTimeEntry GetEmployeeTimeEntry(Expression<Func<EmployeeTimeEntry, bool>> expression) =>
            _dbContext.EmployeeTimeEntries.FirstOrDefault(expression);

        public List<int> GetEmployeeTimeEntryIds(int companyId, int timeSheetId)
        {
            return _dbContext.TimeSheetDetails
                .Where(d => d.TimeSheetID == timeSheetId)
                .Select(d => d.EmployeeID)
                .Distinct()
                .Join(_dbContext.EmployeeTimeEntries,
                    d => new { EmployeeID = d },
                    e => new { e.EmployeeID },
                    (d, e) => new { d, e })
                .Where(de => de.e.CompanyID == companyId &&
                             de.e.TimeSheetID == timeSheetId)
                .Select(de => de.e.TimeEntryID)
                .ToList();
        }

        public List<Code_Description> GetTimeSheetTEEmployees(int companyId, int timeSheetId)
        {
            try
            {
                return _dbContext.EmployeeTimeEntries
                    .Where(t => t.TimeSheetID == timeSheetId)
                    .Select(t => t.EmployeeID)
                    .Join(_dbContext.Employees,
                        t => new { EmployeeID = t },
                        e => new { EmployeeID = e.EmployeeID },
                        (t, e) => new { t, Employee = e })
                    .Where(te => te.Employee.CompanyID == companyId &&
                                 !string.IsNullOrEmpty(te.Employee.Email) &&
                                 !te.Employee.Inactive)
                    .Select(te => new Code_Description
                    {
                        Code = te.Employee.FirstName + " " + te.Employee.LastName,
                        Description = te.Employee.Email
                    })
                    .ToList();
            }
            catch
            {
                return new List<Code_Description>(0);
            }
        }

        public void InsertColumnSetup(EmployeeTimeEntryColumnSetup setup)
        {
            AddItem(setup);
        }
    }
}