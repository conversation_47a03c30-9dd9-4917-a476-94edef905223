using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollWorkPayCodeOriginalProvider : DbContextBaseProvider, IPayrollWorkPayCodeOriginalProvider
    {
        #region Fields



        #endregion

        #region Constructors

        public PayrollWorkPayCodeOriginalProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public void SaveOriginals(List<PayrollWorkPayCodesOriginal> payrollWorkPayCodesOriginals, DnetEntities context)
        {
            if (context == null) context = _dbContext;
            context.PayrollWorkPayCodesOriginals.AddRange(payrollWorkPayCodesOriginals);
            context.SaveChanges();
        }

        public List<PayrollWorkPayCodesOriginal> ListPayCodeOriginals(string payrollNumber, string employeeID, DnetEntities context = null)
        {
            if (context == null) context = _dbContext;
            return context.PayrollWorkPayCodesOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeID).ToList();
        }

        public void DeleteFromPayroll(string payrollNumber)
        {
            var recordsToRemove = _dbContext.PayrollWorkPayCodesOriginals.Where(x => x.PayrollNumber == payrollNumber).ToList();

            _dbContext.PayrollWorkPayCodesOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        public void DeleteFromPayroll(string payrollNumber, string employeeId)
        {
            var recordsToRemove = _dbContext.PayrollWorkPayCodesOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeId).ToList();

            _dbContext.PayrollWorkPayCodesOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        #endregion
    }
}