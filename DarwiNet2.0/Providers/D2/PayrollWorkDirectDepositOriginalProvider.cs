using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

//SDJ CH-1232
namespace DarwiNet2._0.Providers.D2
{
    public class PayrollWorkDirectDepositOriginalProvider : DbContextBaseProvider, IPayrollWorkDirectDepositOriginalProvider
    {
        #region Fields



        #endregion

        #region Constructors

        public PayrollWorkDirectDepositOriginalProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public void SaveOriginals (List<PayrollWorkDirectDepositsOriginal> payrollWorkDirectDepositsOriginals, DnetEntities context)
        {
            if (context == null) context = _dbContext;
            context.PayrollWorkDirectDepositsOriginals.AddRange(payrollWorkDirectDepositsOriginals);
            context.SaveChanges();
        }

        public List<PayrollWorkDirectDepositsOriginal> ListDirectDepositOriginals(string payrollNumber, string employeeID, DnetEntities context = null)
        {
            if (context == null) context = _dbContext;
            return context.PayrollWorkDirectDepositsOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeID).ToList();
        }

        public void DeleteFromPayroll(string payrollNumber)
        {
            var recordsToRemove = _dbContext.PayrollWorkDirectDepositsOriginals.Where(x => x.PayrollNumber == payrollNumber).ToList();

            _dbContext.PayrollWorkDirectDepositsOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        public void DeleteFromPayroll(string payrollNumber, string employeeId)
        {
            var recordsToRemove = _dbContext.PayrollWorkDirectDepositsOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeId).ToList();

            _dbContext.PayrollWorkDirectDepositsOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        #endregion
    }
}