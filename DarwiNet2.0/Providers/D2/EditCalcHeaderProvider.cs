using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services;
using DarwiNet2._0.Models.D2.EditPayrollCalculation;
using DarwiNet2._0.Services.D2;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    /// <summary>
    ///     Provider for EditCalcHeaders table.
    /// </summary>
    /// <history>
    ///     [jwitcher]   08/03/20    Task-6568: Created.
    /// </history>
    public class EditCalcHeaderProvider : DbContextBaseProvider, IEditCalcHeaderProvider
    {
        #region Constructors

        public EditCalcHeaderProvider(DnetEntities dbContext) 
            : base(dbContext)
        {
        }

        #endregion

        #region Public Functions

        /// <summary>
        ///     Create new entities for all edit calc udpate events.
        /// </summary>
        /// <history>
        ///     [jwitcher]   07/30/20    Task-6568: Created.
        /// </history>
        public string Create(EditCalcHeader editCalcHeader)
        {
            var headerId = Guid.NewGuid().ToString();
            editCalcHeader.EditCalcHeaderID = headerId;

            SqlParameter[] parameters = new SqlParameter[8]
            {
                new SqlParameter("editCalcHeaderId", editCalcHeader.EditCalcHeaderID),
                new SqlParameter("payrollNumber", editCalcHeader.PayrollNumber),
                new SqlParameter("snapshotId", editCalcHeader.SnapshotID),
                new SqlParameter("employeeId", editCalcHeader.EmployeeID),
                new SqlParameter("tableName", editCalcHeader.TableName),
                new SqlParameter("actionType", editCalcHeader.ActionType),
                new SqlParameter("code", editCalcHeader.Code),
                new SqlParameter("changedDateTime", editCalcHeader.ChangedDateTime),
            };

            var sql = "INSERT INTO EditCalcHeader (EditCalcHeaderID, PayrollNumber, SnapshotID, EmployeeID, TableName, ActionType, Code, ChangedDateTime) VALUES (@editCalcHeaderId, @payrollNumber, @snapshotId, @employeeId, @tableName, @actionType, @code, @changedDateTime );";

            string insertQuery = string.Join("\n", sql);

            this.ExecuteNonQuery(insertQuery, parameters);

            return headerId;
        }

        public HashSet<string> GetEmployeesBySnapshot(string payrollNumber, string snapshotId)
        {
            var query = (from ech in _dbContext.EditCalcHeaders
                         where ech.SnapshotID == snapshotId
                         && ech.PayrollNumber == payrollNumber
                         select ech.EmployeeID);

            var employees = new HashSet<string>(query);

            return employees;
        }

        public List<EditCalcDetailActionDTO> GetHeadersByEmployee(string employeeId, string payrollNumber, string snapshotId)
        {
            var result = (from ech in _dbContext.EditCalcHeaders
                          join ecd in _dbContext.EditCalcDetails
                          on ech.EditCalcHeaderID equals ecd.EditCalcHeaderID
                          where ech.EmployeeID == employeeId
                          && ech.PayrollNumber == payrollNumber
                          && ech.SnapshotID == snapshotId
                          select new EditCalcDetailActionDTO
                          {
                              TableName = ech.TableName,
                              ActionType = ech.ActionType,
                              FieldValue = ecd.FieldValue
                          }).ToList();

            return result;
        }

        public HashSet<string> GetHeadersByPayroll(string payrollNumber)
        {
            return new HashSet<string>(_dbContext.EditCalcHeaders.Where(ech => ech.PayrollNumber.Equals(payrollNumber)).Select(ech => ech.EditCalcHeaderID));
        }

        public void Delete(string headerId)
        {
            SqlParameter[] parameters = new SqlParameter[1]
            {
                new SqlParameter("editCalcHeaderId", headerId)
            };

            var sql = "DELETE FROM EditCalcHeader WHERE EditCalcHeaderID = @editCalcHeaderId;";

            string deleteStatement = string.Join("\n", sql);

            this.ExecuteNonQuery(deleteStatement, parameters);
        }

        public void DeleteByPayroll(string payrollNumber)
        {
            SqlParameter[] parameters = new SqlParameter[1]
            {
                new SqlParameter("payrollNumber", payrollNumber),
            };

            var deleteStatement = "DELETE FROM EditCalcHeader WHERE PayrollNumber = @payrollNumber;";

            this.ExecuteNonQuery(deleteStatement, parameters);
        }

        public HashSet<string> CheckForUnprocessedAdds(string payrollNumber, string snapshotId, PayrollWorkTable table, string partialKey)
        {
            switch (table)
            {
                case PayrollWorkTable.PayCodes:
                    return CheckPayCodes(payrollNumber, snapshotId, partialKey);
                    break;

                case PayrollWorkTable.Deductions:
                    return CheckDeductions(payrollNumber, snapshotId, partialKey);
                    break;

                case PayrollWorkTable.Benefits:
                    return CheckBenefits(payrollNumber, snapshotId, partialKey);
                    break;

                case PayrollWorkTable.StateTaxes:
                    return CheckStateTaxes(payrollNumber, snapshotId, partialKey);
                    break;

                case PayrollWorkTable.LocalTaxes:
                    return CheckLocalTaxes(payrollNumber, snapshotId, partialKey);
                    break;

                default:
                    return new HashSet<string>();
                    break;
            }
        }

        #endregion

        #region Private Functions

        private HashSet<string> CheckPayCodes(string payrollNumber, string snapshotId, string partialKey)
        {
            var query = (from ech in _dbContext.EditCalcHeaders
                         join ecd in _dbContext.EditCalcDetails on ech.EditCalcHeaderID equals ecd.EditCalcHeaderID
                         join pwpc in _dbContext.PayrollWorkPayCodes
                            on new { PayrollNumber = ech.PayrollNumber, SnapshotID = ech.SnapshotID }
                            equals new { PayrollNumber = pwpc.PayrollNumber, SnapshotID = pwpc.SnapshotID }
                         where ech.SnapshotID == snapshotId
                         && ech.PayrollNumber == payrollNumber
                         && ecd.TableKey.Contains(partialKey)
                         && pwpc.PayRecord == partialKey
                         && ech.ActionType == "Add"
                         && ech.TableName == "PayrollWorkPayCodes"
                         
                         select ech.EditCalcHeaderID
                         ).ToList();

            HashSet<string> headers = new HashSet<string>(query);

            return headers;
        }

        private HashSet<string> CheckDeductions(string payrollNumber, string snapshotId, string partialKey)
        {
            var query = (from ech in _dbContext.EditCalcHeaders
                         join ecd in _dbContext.EditCalcDetails on ech.EditCalcHeaderID equals ecd.EditCalcHeaderID
                         join pwd in _dbContext.PayrollWorkDeductions
                            on new { PayrollNumber = ech.PayrollNumber, SnapshotID = ech.SnapshotID }
                            equals new { PayrollNumber = pwd.PayrollNumber, SnapshotID = pwd.SnapshotID }
                         where ech.SnapshotID == snapshotId
                         && ech.PayrollNumber == payrollNumber
                         && ecd.TableKey.Contains(partialKey)
                         && pwd.Deduction == partialKey
                         && ech.ActionType == "Add"
                         && ech.TableName == "PayrollWorkDeductions"

                         select ech.EditCalcHeaderID
                         ).ToList();

            HashSet<string> headers = new HashSet<string>(query);

            return headers;
        }
        private HashSet<string> CheckBenefits(string payrollNumber, string snapshotId, string partialKey)
        {
            var query = (from ech in _dbContext.EditCalcHeaders
                         join ecd in _dbContext.EditCalcDetails on ech.EditCalcHeaderID equals ecd.EditCalcHeaderID
                         join pwb in _dbContext.PayrollWorkBenefits
                            on new { PayrollNumber = ech.PayrollNumber, SnapshotID = ech.SnapshotID }
                            equals new { PayrollNumber = pwb.PayrollNumber, SnapshotID = pwb.SnapshotID }
                         where ech.SnapshotID == snapshotId
                         && ech.PayrollNumber == payrollNumber
                         && ecd.TableKey.Contains(partialKey)
                         && pwb.Benefit == partialKey
                         && ech.ActionType == "Add"
                         && ech.TableName == "PayrollWorkBenefits"

                         select ech.EditCalcHeaderID
                         ).ToList();

            HashSet<string> headers = new HashSet<string>(query);

            return headers;
        }

        private HashSet<string> CheckStateTaxes(string payrollNumber, string snapshotId, string partialKey)
        {
            var query = (from ech in _dbContext.EditCalcHeaders
                         join ecd in _dbContext.EditCalcDetails on ech.EditCalcHeaderID equals ecd.EditCalcHeaderID
                         join pwst in _dbContext.PayrollWorkStateTaxes
                            on new { PayrollNumber = ech.PayrollNumber, SnapshotID = ech.SnapshotID }
                            equals new { PayrollNumber = pwst.PayrollNumber, SnapshotID = pwst.SnapshotID }
                         where ech.SnapshotID == snapshotId
                         && ech.PayrollNumber == payrollNumber
                         && ecd.TableKey.Contains(partialKey)
                         && pwst.StateCode == partialKey
                         && ech.ActionType == "Add"
                         && ech.TableName == "PayrollWorkStateTaxes"

                         select ech.EditCalcHeaderID
                         ).ToList();

            HashSet<string> headers = new HashSet<string>(query);

            return headers;
        }
        private HashSet<string> CheckLocalTaxes(string payrollNumber, string snapshotId, string partialKey)
        {
            var query = (from ech in _dbContext.EditCalcHeaders
                         join ecd in _dbContext.EditCalcDetails on ech.EditCalcHeaderID equals ecd.EditCalcHeaderID
                         join pwlt in _dbContext.PayrollWorkLocalTaxes
                            on new { PayrollNumber = ech.PayrollNumber, SnapshotID = ech.SnapshotID }
                            equals new { PayrollNumber = pwlt.PayrollNumber, SnapshotID = pwlt.SnapshotID }
                         where ech.SnapshotID == snapshotId
                         && ech.PayrollNumber == payrollNumber
                         && ecd.TableKey.Contains(partialKey)
                         && pwlt.LocalTax == partialKey
                         && ech.ActionType == "Add"
                         && ech.TableName == "PayrollWorkLocalTaxes"

                         select ech.EditCalcHeaderID
                         ).ToList();

            HashSet<string> headers = new HashSet<string>(query);

            return headers;
        }

        #endregion
    }
}