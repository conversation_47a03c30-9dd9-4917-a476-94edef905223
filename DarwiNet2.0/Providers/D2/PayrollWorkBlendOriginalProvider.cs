using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollWorkBlendOriginalProvider : DbContextBaseProvider, IPayrollWorkBlendOriginalProvider
    {
        #region Fields



        #endregion

        #region Constructors

        public PayrollWorkBlendOriginalProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public void SaveOriginals(List<PayrollWorkBlendOriginal> payrollWorkBlendOriginals, DnetEntities context)
        {
            if (context == null) context = _dbContext;
            context.PayrollWorkBlendOriginals.AddRange(payrollWorkBlendOriginals);
            context.SaveChanges();
        }

        public List<PayrollWorkBlendOriginal> ListBlendOriginals(string payrollNumber, string employeeID, DnetEntities context = null)
        {
            if (context == null) context = _dbContext;
            return context.PayrollWorkBlendOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeID).ToList();
        }

        public void DeleteFromPayroll(string payrollNumber)
        {
            var recordsToRemove = _dbContext.PayrollWorkBlendOriginals.Where(x => x.PayrollNumber == payrollNumber).ToList();

            _dbContext.PayrollWorkBlendOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        public void DeleteFromPayroll(string payrollNumber, string employeeId)
        {
            var recordsToRemove = _dbContext.PayrollWorkBlendOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeId).ToList();

            _dbContext.PayrollWorkBlendOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        #endregion
    }
}