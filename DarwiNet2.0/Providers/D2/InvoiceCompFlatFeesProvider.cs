using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;

namespace DarwiNet2._0.Providers.D2
{
    public class InvoiceCompFlatFeesProvider : DbContextBaseProvider, IInvoiceCompFlatFeesProvider
    {
        public InvoiceCompFlatFeesProvider()
        {
        }
        #region Fields



        #endregion

        #region Constructors

        public InvoiceCompFlatFeesProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers)
        {
            var recordsToRemove = _dbContext.InvoiceCompFlatFees.Where(x => x.CompanyID == companyId
                                                                    && x.ClientID == clientId
                                                                    && invoiceNumbers.Contains((int)x.DarwinInvoiceNumber)).ToList();

            _dbContext.InvoiceCompFlatFees.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        public List<InvoiceCompFlatFee> ListInvoiceCompFlatFeesByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            return dbContext.InvoiceCompFlatFees.Where(x => x.CompanyID == companyID
                                                            && x.ClientID == clientID
                                                            && x.DarwinInvoiceNumber == darwinInvoiceNumber).ToList();
        }

        public List<InvoiceCompFlatFee> ListInvoiceCompFlatFeesByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            return dbContext.InvoiceCompFlatFees.Where(x => x.CompanyID == companyID
                                                            && x.ClientID == clientID
                                                            && (x.MergedInvoiceNumber == mergedInvoiceNumber ||
                                                                x.DarwinInvoiceNumber == mergedInvoiceNumber)).ToList();
        }
        
        public List<InvoiceCompFlatFee> ListInvoiceCompFlatFeesByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            return dbContext.InvoiceCompFlatFees.Where(x => x.CompanyID == companyID
                                                            && x.ClientID == clientID
                                                            && darwinInvoiceNumbers.Contains(x.DarwinInvoiceNumber)).ToList();
        }

        public void UpdateMergedInvoiceNumberRaw(InvoiceCompFlatFee invoiceCompFlatFee, int mergedInvoiceNumber, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            dbContext.Database.ExecuteSqlCommand("UPDATE InvoiceCompFlatFees SET MergedInvoiceNumber = @param1 " +
                                                 "WHERE CompanyID = @param2 " +
                                                 "AND ClientID = @param3 " +
                                                 "AND DarwinInvoiceNumber = @param4 " +
                                                 "AND ChargeType = @param5 " +
                                                 "AND DarwinItemNumber = @param6;",
                                                  new SqlParameter("param1", mergedInvoiceNumber),
                                                  new SqlParameter("param2", invoiceCompFlatFee.CompanyID),
                                                  new SqlParameter("param3", invoiceCompFlatFee.ClientID),
                                                  new SqlParameter("param4", invoiceCompFlatFee.DarwinInvoiceNumber),
                                                  new SqlParameter("param5", invoiceCompFlatFee.ChargeType),
                                                  new SqlParameter("param6", invoiceCompFlatFee.DarwinItemNumber));
        }

        public void SaveInvoiceCompFlatFee(InvoiceCompFlatFee invoiceCompFlatFeeToSave, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            dbContext.InvoiceCompFlatFees.Add(invoiceCompFlatFeeToSave);
        }

        public void DeleteListInvoiceCompFlatFees(List<InvoiceCompFlatFee> invoiceCompFlatFees, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            dbContext.InvoiceCompFlatFees.RemoveRange(invoiceCompFlatFees);
        }

        #endregion
    }
}