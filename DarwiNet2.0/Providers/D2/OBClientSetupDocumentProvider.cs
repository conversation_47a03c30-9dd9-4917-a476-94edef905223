using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class OBClientSetupDocumentProvider: DbContextBaseProvider, IOBClientSetupDocumentProvider
    {

        #region Constructors
        public OBClientSetupDocumentProvider()
           : base()
        {

        }

        public OBClientSetupDocumentProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        public bool IsConditionalDocsExists(int companyId, int setupId, bool useAssignments)
        {
            return _dbContext.OBClientSetupDocuments.Where(d => d.CompanyID == companyId && d.SetupID == setupId && d.UseAssignments == useAssignments).Any();
        }

        #endregion
    }
}