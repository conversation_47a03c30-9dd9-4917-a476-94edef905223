using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Interfaces.Providers.D2;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollWorkPostProvider : DbContextBaseProvider, IPayrollWorkPostProvider
    {
        public PayrollWorkPostProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        public List<PayrollWorkPost> ListPosts(string payrollNumber, string employeeID, DnetEntities context = null)
        {
            if (context == null) context = _dbContext;
            return context.PayrollWorkPosts.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeID).ToList();
        }

        public void SaveVoided(List<PayrollWorkPost> voidedPayrollWorkPosts, DnetEntities context)
        {
            if (context == null)
            {
                context = _dbContext;
            }
            context.PayrollWorkPosts.AddRange(voidedPayrollWorkPosts);
        }

        public int GetPreviousVoidCount(string payrollNumber, string employeeID)
        {
            return _dbContext.PayrollWorkPosts.Where(x => x.PayrollNumber.StartsWith(payrollNumber) && x.EmployeeID == employeeID).Count();
        }

        public void Reset(string payrollNumber)
        {
            try
            {
                this.ExecuteNonQuery("DELETE FROM PayrollWorkPosts WHERE PayrollNumber = @payrollNumber;", new SqlParameter("payrollNumber", payrollNumber));
            }
            catch (Exception ex)
            {
                //_dbContext.ClearChanges();
                throw ex;
            }
        }

        public void DeleteFromPayroll(string payrollNumber)
        {
            var recordsToRemove = _dbContext.PayrollWorkPosts.Where(x => x.PayrollNumber == payrollNumber).ToList();

            _dbContext.PayrollWorkPosts.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        public void Delete(string payrollNumber, string employeeID)
        {
            List<PayrollWorkPost> workPosts = _dbContext.PayrollWorkPosts
                                                          .Where(x => x.PayrollNumber == payrollNumber &&
                                                          x.EmployeeID == employeeID).ToList();
            if (workPosts.Count != 0)
            {
                foreach (PayrollWorkPost post in workPosts)
                {
                    this.RemoveItem(post);
                }
            }
        }

        public void UpdateAmountToPost(string payrollNumber, string employeeId, int? accountIndex, decimal? oldAmount, decimal? newAmount, bool deleteRecord = false)
        {
            var schedule = _dbContext.ClientPayrollSchedules
                                     .FirstOrDefault(x => x.PayrollNumber == payrollNumber);
            var manualCheckType = schedule != null ? schedule.ManualCheckType : 0;

            PayrollWorkPost existingRecord = null;
            if (manualCheckType == (int)ManualCheckTypes.Adjustment)
            {
                if (oldAmount < 0 || (deleteRecord && newAmount > 0))
                {
                    existingRecord = _dbContext.PayrollWorkPosts
                    .FirstOrDefault(x => x.PayrollNumber == payrollNumber &&
                                         x.EmployeeID == employeeId &&
                                         x.AccountIndex == accountIndex &&
                                         x.AmountToPost < 0);
                }
                else
                {
                    existingRecord = _dbContext.PayrollWorkPosts
                        .FirstOrDefault(x => x.PayrollNumber == payrollNumber &&
                                             x.EmployeeID == employeeId &&
                                             x.AccountIndex == accountIndex &&
                                             x.AmountToPost > 0);
                }
            }
            else
            {
                existingRecord = _dbContext.PayrollWorkPosts
                        .FirstOrDefault(x => x.PayrollNumber == payrollNumber &&
                                             x.EmployeeID == employeeId &&
                                             x.AccountIndex == accountIndex);
            }

            if (existingRecord == null)
            {
                var snapshotId = _dbContext.PayrollSnapshots
                    .FirstOrDefault(x => x.PayrollNumber == payrollNumber)?
                    .SnapshotID ?? "";
                PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                Employee employee = _dbContext.Employees.Where(x => x.EmployeeID == employeeId && x.CompanyID == oPayrollNumber.CompanyId).First();
                CreateOrUpdatePayrollWorkPostAmount(payrollNumber, employeeId, employee.Department, employee.Position, schedule.PostedDate, snapshotId, GlobalVariables.DNETOwnerID, accountIndex, newAmount, existingRecord);
            }

            if (existingRecord != null && !deleteRecord)
            {
                existingRecord.AmountToPost -= oldAmount;
                existingRecord.AmountToPost += newAmount;
            }

            if (existingRecord != null && (existingRecord.AmountToPost == 0 || deleteRecord))
            {
                _dbContext.PayrollWorkPosts.Remove(existingRecord);
            }
            _dbContext.SaveChanges();
        }

        public void CreateOrUpdatePayrollWorkPostAmount(string payrollNumber, string employeeId, string department, string position, DateTime? postedDate, string snapshotId, string userId, int? accountIndex, decimal? amount, PayrollWorkPost record = null)
        {
            var manualCheckType = _dbContext.ClientPayrollSchedules
                .FirstOrDefault(x => x.PayrollNumber == payrollNumber)?
                .ManualCheckType ?? 0;

            PayrollWorkPost existingRecord = record;
            if (existingRecord == null)
            {
                if (manualCheckType == (int)ManualCheckTypes.Adjustment)
                {
                    if (amount < 0)
                    {
                        existingRecord = _dbContext.PayrollWorkPosts
                        .FirstOrDefault(x => x.PayrollNumber == payrollNumber &&
                                             x.EmployeeID == employeeId &&
                                             x.AccountIndex == accountIndex &&
                                             x.AmountToPost < 0);
                    }
                    else
                    {
                        existingRecord = _dbContext.PayrollWorkPosts
                            .FirstOrDefault(x => x.PayrollNumber == payrollNumber &&
                                                 x.EmployeeID == employeeId &&
                                                 x.AccountIndex == accountIndex &&
                                                 x.AmountToPost > 0);
                    }
                }
                else
                {
                    existingRecord = _dbContext.PayrollWorkPosts
                            .FirstOrDefault(x => x.PayrollNumber == payrollNumber &&
                                                 x.EmployeeID == employeeId &&
                                                 x.AccountIndex == accountIndex);
                }
            }

            if (existingRecord != null)
            {
                existingRecord.AmountToPost += amount;
            }
            else
            {
                PayrollNumber oPayrollNumber = PayrollNumber.Parse(payrollNumber);
                PayrollWorkPost newRecord = new PayrollWorkPost()
                {
                    CompanyID = oPayrollNumber.CompanyId,
                    UserID = userId,
                    EmployeeID = employeeId,
                    PayRunType = 0,
                    PayrollRecordType = 0,
                    TRXNumber = GetNextTransactionNumber(payrollNumber, employeeId, accountIndex),
                    SequenceNumber = (int)accountIndex,
                    PaymentAdjustmentNumber = 0,
                    AccountIndex = (int)accountIndex,
                    AmountToPost = amount,
                    CheckNumber = "",
                    PostedDate = postedDate ?? new DateTime(1900, 1, 1),
                    Department = department,
                    JobTitle = position,
                    PayrollCode = "",
                    UPRAccountType = 0,
                    ProfileID = oPayrollNumber.ProfileId,
                    PayrollNumber = payrollNumber,
                    SnapshotID = snapshotId,
                    IsLatest = true
                };
                _dbContext.PayrollWorkPosts.Add(newRecord);
            }
            _dbContext.SaveChanges();
        }

        public int GetNextTransactionNumber(string payrollNumber, string employeeId, int? accountIndex)
        {
            var records = _dbContext.PayrollWorkPosts
                .Where(x => x.PayrollNumber == payrollNumber &&
                            x.EmployeeID == employeeId &&
                            x.AccountIndex == accountIndex);
            if (records.Any()) return records.Max(x => x.TRXNumber) + 1;
            return 0;
        }
    }
}