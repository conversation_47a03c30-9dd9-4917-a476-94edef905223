using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class InvoiceApprovalRecipientProvider : DbContextBaseProvider, IInvoiceApprovalRecipientProvider
    {
        #region Fields



        #endregion

        #region Constructors

        public InvoiceApprovalRecipientProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public void AddRecipients(int invoiceApprovalSetupId, string approvalType, string approvalModel, List<InvoiceApprovalRecipientModel> recipientsToAdd)
        {
            List<InvoiceApprovalRecipient> invoiceApprovalRecipients = AddInvoiceApprovalRecipients(invoiceApprovalSetupId, recipientsToAdd);
            AddInvoiceApprovalSetupSequences(invoiceApprovalSetupId, approvalType, approvalModel, invoiceApprovalRecipients, recipientsToAdd);
        }


        public void UpdateRecipients(InvoiceApprovalSetupModel setup, List<InvoiceApprovalRecipient> currentRecipients)
        {
            if (setup.InternalApproval && setup.ApprovalModel == "GroupSequential" && setup.PayrollApprovalTeams.Any())
            {
                // Can only be one selected payroll approval team (the team assigned to payroll profile
                Int32 teamid = setup.PayrollApprovalTeams.FirstOrDefault().Code;

                // Get associated users for selected payroll team
                var teamUsers = _dbContext.PayrollTeamMembers
                .Where(x => x.PayrollTeamID == teamid)
                .Select(x => x.UserID)
                .ToList();

                // Get each userID existing setup sequences
                var payrollTeamMembersInSequence = _dbContext.InvoiceApprovalSetupSequences
                    .Join(_dbContext.InvoiceApprovalRecipients,
                        iass => new { iass.InvoiceApprovalRecipientID },
                        iar => new { iar.InvoiceApprovalRecipientID },
                        (iass, iar) => new { iass, iar })
                    .Join(_dbContext.PayrollTeamMembers,
                        aprv => new { User = aprv.iar.RecipientID },
                        ptm => new { User = ptm.UserID },
                        (aprv, ptm) => new { aprv, ptm })
                    .Where(x => x.aprv.iass.InvoiceApprovalSetupID == setup.InvoiceApprovalSetupID &&
                                x.ptm.PayrollTeamID == teamid)
                    .ToList();

                var invoiceApprovalRecipients = _dbContext.InvoiceApprovalRecipients
                    .Where(x => x.InvoiceApprovalSetupID == setup.InvoiceApprovalSetupID)
                    .ToList();

                var invoiceApprovalSetupSequences = _dbContext.InvoiceApprovalSetupSequences
                    .Where(x => x.InvoiceApprovalSetupID == setup.InvoiceApprovalSetupID)
                    .ToList();

                // Set all sequences to 0, only one team can be selected so sequence doesn't matter here
                foreach (var seq in invoiceApprovalSetupSequences)
                {
                    seq.Sequence = 0;
                }

                // deactivate existing recipients
                foreach (var u in invoiceApprovalRecipients)
                {
                    u.Inactive = true;
                }

                // activate payroll team members already in sequence
                foreach (var u in payrollTeamMembersInSequence)
                {
                    u.aprv.iar.Inactive = false;
                    u.aprv.iar.GroupID = null;
                    u.aprv.iar.TeamID = teamid;
                }

                foreach (var u in teamUsers)
                {
                    // If user is not in recipients, we need to add them
                    if (invoiceApprovalRecipients.Where(x => x.RecipientID == u && x.TeamID == teamid).Count() == 0)
                    {
                        var newRecipient = new InvoiceApprovalRecipient
                        {
                            InvoiceApprovalSetupID = setup.InvoiceApprovalSetupID,
                            RecipientID = u,
                            GroupID = null,
                            GroupRequireAll = null,
                            Inactive = false,
                            TeamID = teamid
                        };

                        _dbContext.InvoiceApprovalRecipients.Add(newRecipient);

                        var newSequence = new InvoiceApprovalSetupSequence
                        {
                            InvoiceApprovalSetupID = setup.InvoiceApprovalSetupID,
                            InvoiceApprovalRecipientID = newRecipient.InvoiceApprovalRecipientID,
                            Sequence = 0
                        };

                        _dbContext.InvoiceApprovalSetupSequences.Add(newSequence);
                    }
                }
                _dbContext.SaveChanges();
            }
            else if (setup.InternalApproval && setup.ApprovalModel == "GroupSequential" && setup.PayrollApprovalTeams.Count() == 0)
            {
                // Means we deselected all so we should deactive all 
                var recipientsToDeactive = _dbContext.InvoiceApprovalRecipients
                    .Where(x => x.InvoiceApprovalSetupID == setup.InvoiceApprovalSetupID &&
                                x.TeamID != null);

                foreach (var r in recipientsToDeactive)
                {
                    r.Inactive = true;
                }

                _dbContext.SaveChanges();
            }
            else
            {
                if (!setup.InternalApproval && setup.ApprovalModel == "GroupSequential" && setup.InvoiceApprovalRecipientGroups.Any())
                {
                    List<InvoiceApprovalRecipientModel> invoiceApprovalRecipientGroups = setup.InvoiceApprovalRecipientGroups
                   .Select(g => new InvoiceApprovalRecipientModel
                   {
                       InvoiceApprovalRecipientID = g.InvoiceApprovalRecipientID,
                       GroupID = (int)g.GroupID,
                       Sequence = g.Sequence,
                       GroupRequireAll = g.GroupRequireAll
                   })
                   .ToList();

                    var currentGroupRecipients = currentRecipients.Where(c => c.GroupID != null).ToList();
                    if (currentGroupRecipients.Any())
                    {
                        var groupRecipientsToRemove = currentGroupRecipients
                        .Where(c => !c.Inactive &&
                                    !invoiceApprovalRecipientGroups.Select(n => n.GroupID).Contains(c.GroupID))
                        .Select(c => c.InvoiceApprovalRecipientID);

                        var currentGroupRecipientsToUpdate = currentGroupRecipients
                            .Where(c => !groupRecipientsToRemove.Contains(c.InvoiceApprovalRecipientID) &&
                                        !c.Inactive).ToList();
                        if (currentGroupRecipientsToUpdate.Any())
                        {
                            var updatedGroupRecipients = invoiceApprovalRecipientGroups.Where(n => currentGroupRecipientsToUpdate.Select(c => c.GroupID).Contains(n.GroupID)).ToList();
                            if (updatedGroupRecipients != null)
                            {
                                List<int> currentGroupRecipientsToUpdateIds = currentGroupRecipientsToUpdate.Select(c => c.InvoiceApprovalRecipientID).ToList();
                                UpdateInvoiceApprovalSetupSequences(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, currentGroupRecipientsToUpdateIds, updatedGroupRecipients);
                            }
                        }

                        InactivateInvoiceApprovalRecipients(groupRecipientsToRemove);
                    }

                    InsertGroupRecipients(setup, invoiceApprovalRecipientGroups, currentGroupRecipients);
                }

                else if (!setup.InternalApproval && setup.ApprovalModel == "GroupSequential" && setup.InvoiceApprovalRecipientGroups.Count == 0)
                {
                    // Means we deselected all so we should deactive all 
                    var recipientsToDeactive = _dbContext.InvoiceApprovalRecipients
                        .Where(x => x.InvoiceApprovalSetupID == setup.InvoiceApprovalSetupID &&
                                    x.GroupID != null);

                    foreach (var r in recipientsToDeactive)
                    {
                        r.Inactive = true;
                    }

                    _dbContext.SaveChanges();
                }

                    var currentUserRecipients = currentRecipients.Where(c => c.GroupID == null).ToList();
                if (currentUserRecipients.Any())
                {
                    var recipientsToRemove = currentUserRecipients
                    .Where(c => !c.Inactive &&
                                !setup.InvoiceApprovalRecipients.Select(n => n.RecipientID ?? n.UserID).Contains(c.RecipientID))
                    .Select(c => c.InvoiceApprovalRecipientID);

                    var currentRecipientsToUpdate = currentUserRecipients
                        .Where(c => !recipientsToRemove.Contains(c.InvoiceApprovalRecipientID) &&
                                    !c.Inactive)
                        .ToList();
                    if (currentRecipientsToUpdate.Any())
                    {
                        var updatedRecipients = setup.InvoiceApprovalRecipients.Where(n => currentRecipientsToUpdate.Select(c => c.RecipientID).Contains(n.RecipientID ?? n.UserID)).ToList();
                        if (updatedRecipients != null)
                        {
                            List<int> currentRecipientsToUpdateIds = currentRecipientsToUpdate.Select(c => c.InvoiceApprovalRecipientID).ToList();
                            UpdateInvoiceApprovalSetupSequences(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, currentRecipientsToUpdateIds, updatedRecipients);
                        }
                    }

                    InactivateInvoiceApprovalRecipients(recipientsToRemove);
                }

                InsertRecipients(setup, setup.InvoiceApprovalRecipients, currentUserRecipients);
            }
        }

        public void AddGroupRecipients(InvoiceApprovalSetupModel setup)
        {
            List<InvoiceApprovalRecipientModel> invoiceApprovalRecipientGroups = setup.InvoiceApprovalRecipientGroups
                    .Select(g => new InvoiceApprovalRecipientModel
                    {
                        InvoiceApprovalRecipientID = g.InvoiceApprovalRecipientID,
                        GroupID = (int)g.GroupID,
                        Sequence = g.Sequence,
                        GroupRequireAll = g.GroupRequireAll
                    })
                    .ToList();

            InsertGroupRecipients(setup, invoiceApprovalRecipientGroups, new List<InvoiceApprovalRecipient>());
        }

        public void AddTeamRecipients(InvoiceApprovalSetupModel setup)
        {
            // Can only be one selected payroll approval team (the team assigned to payroll profile
            Int32 teamid;
            if (Int32.TryParse(setup.PayrollApprovalTeams.FirstOrDefault().Code, out teamid))
            {
                // Get associated users for selected payroll team
                var teamUsers = _dbContext.PayrollTeamMembers
                .Where(x => x.PayrollTeamID == teamid)
                .Select(x => x.UserID)
                .ToList();

                foreach (var u in teamUsers)
                {
                    var newRecipient = new InvoiceApprovalRecipient
                    {
                        InvoiceApprovalSetupID = setup.InvoiceApprovalSetupID,
                        RecipientID = u,
                        GroupID = null,
                        GroupRequireAll = null,
                        Inactive = false,
                        TeamID = teamid
                    };

                    _dbContext.InvoiceApprovalRecipients.Add(newRecipient);

                    var newSequence = new InvoiceApprovalSetupSequence
                    {
                        InvoiceApprovalSetupID = setup.InvoiceApprovalSetupID,
                        InvoiceApprovalRecipientID = newRecipient.InvoiceApprovalRecipientID,
                        Sequence = 0
                    };

                    _dbContext.InvoiceApprovalSetupSequences.Add(newSequence);
                }
                _dbContext.SaveChanges();
            }
        }

        #endregion

        #region Private Methods

        private List<InvoiceApprovalRecipient> AddInvoiceApprovalRecipients(int invoiceApprovalSetupId, List<InvoiceApprovalRecipientModel> recipientsToAdd)
        {
            List<InvoiceApprovalRecipient> invoiceApprovalRecipients = recipientsToAdd
                    .Select(r => new InvoiceApprovalRecipient
                    {
                        InvoiceApprovalSetupID = invoiceApprovalSetupId,
                        RecipientID = r.RecipientID ?? r.UserID,
                        GroupID = r.GroupID,
                        GroupRequireAll = r.GroupRequireAll
                    }).ToList();
            this.AddItems(invoiceApprovalRecipients);

            return invoiceApprovalRecipients;
        }

        private void AddInvoiceApprovalSetupSequences(int invoiceApprovalSetupId, string approvalType, string approvalModel, List<InvoiceApprovalRecipient> invoiceApprovalRecipients, List<InvoiceApprovalRecipientModel> recipientsToAdd)
        {
            // sequences are not needed for notifications
            if (approvalType == Constants.PayrollProfileApprovalTypes.PayrollProfilePayrollApprovalTypes.APPROVE_INVOICE)
            {
                List<InvoiceApprovalSetupSequence> invoiceApprovalSetupSequences = invoiceApprovalRecipients
                    .Select(r => new InvoiceApprovalSetupSequence
                    {
                        InvoiceApprovalSetupID = invoiceApprovalSetupId,
                        InvoiceApprovalRecipientID = r.InvoiceApprovalRecipientID,
                        //Sequence = r.Sequence
                        //Sequence = approvalModel == "Sequential" || approvalModel == "GroupSequential" ? recipientsToAdd.FirstOrDefault(x => x.UserID == r.RecipientID)?.Sequence ?? 0 : 1
                        Sequence = approvalModel == "Sequential" ? recipientsToAdd.FirstOrDefault(x => x.UserID == r.RecipientID)?.Sequence ?? 1 : approvalModel == "GroupSequential" ? recipientsToAdd.FirstOrDefault(y => y.GroupID == r.GroupID)?.Sequence ?? 1 : 1
                    })
                    .ToList();
                this.AddItems(invoiceApprovalSetupSequences);
            }
        }

        private void UpdateInvoiceApprovalSetupSequences(int invoiceApprovalSetupId, string approvalType, string approvalModel, List<int> invoiceApprovalRecipientIds, List<InvoiceApprovalRecipientModel> recipientsToUpdate)
        {
            // sequences are not needed for notifications
            if (approvalType == Constants.PayrollProfileApprovalTypes.PayrollProfilePayrollApprovalTypes.APPROVE_INVOICE)
            {
                List<InvoiceApprovalSetupSequence> invoiceApprovalSetupSequences = _dbContext.InvoiceApprovalSetupSequences
                    .Where(s => s.InvoiceApprovalSetupID == invoiceApprovalSetupId &&
                                invoiceApprovalRecipientIds.Contains(s.InvoiceApprovalRecipientID))
                    .ToList();

                foreach (var invoiceApprovalRecipientId in invoiceApprovalRecipientIds)
                {
                    var invoiceApprovalSetupSequence = invoiceApprovalSetupSequences
                            .Where(s => s.InvoiceApprovalRecipientID == invoiceApprovalRecipientId)
                            .FirstOrDefault();

                    int? updatedRecipientSequence = 1;
                    if (approvalModel == "Sequential" || approvalModel == "GroupSequential")
                    {
                        if (approvalModel == "Sequential")
                        {
                            updatedRecipientSequence = recipientsToUpdate.Where(i => (i.RecipientID ?? i.UserID) == invoiceApprovalSetupSequence.InvoiceApprovalRecipient.RecipientID).FirstOrDefault()?.Sequence;
                        }

                        if (approvalModel == "GroupSequential")
                        {
                            updatedRecipientSequence = recipientsToUpdate.Where(i => i.GroupID == invoiceApprovalSetupSequence.InvoiceApprovalRecipient.GroupID).FirstOrDefault()?.Sequence;
                        }
                    }

                    invoiceApprovalSetupSequence.Sequence = updatedRecipientSequence ?? 1;
                }
                _dbContext.SaveChanges();

                if (recipientsToUpdate != null)
                {
                    // make sure there are no recipients with missing InvoiceApprovalSetupSequence records
                    var sequencesToAdd = invoiceApprovalRecipientIds.Where(i => !invoiceApprovalSetupSequences.Select(s => s.InvoiceApprovalRecipientID).Contains(i));
                    if (sequencesToAdd.Any())
                    {
                        List<InvoiceApprovalRecipient> recipients = _dbContext.InvoiceApprovalRecipients.Where(r => sequencesToAdd.Contains(r.InvoiceApprovalRecipientID)).ToList();
                        var recipientsToAdd = recipientsToUpdate
                            .Where(i => recipients.Select(r => r.InvoiceApprovalRecipientID).Contains(i.InvoiceApprovalRecipientID))
                            .Select(i => new InvoiceApprovalRecipientModel
                            {
                                InvoiceApprovalSetupID = invoiceApprovalSetupId,
                                InvoiceApprovalRecipientID = i.InvoiceApprovalRecipientID,
                                RecipientID = i.RecipientID,
                                UserID = i.RecipientID ?? i.UserID,
                                Sequence = i.Sequence,
                                GroupID = i.GroupID,
                                GroupRequireAll = i.GroupRequireAll
                            })
                            .ToList();
                        AddInvoiceApprovalSetupSequences(invoiceApprovalSetupId, approvalType, approvalModel, recipients, recipientsToAdd);
                    }
                }
            }
        }

        private void InactivateInvoiceApprovalRecipients(IEnumerable<int> recipientsToRemove)
        {
            if (recipientsToRemove.Any())
            {
                string recipients = string.Join(", ", recipientsToRemove);

                string sql = $"UPDATE InvoiceApprovalRecipients SET Inactive = 1 WHERE InvoiceApprovalRecipientID IN ({recipients})";

                _sqlConnectionProvider.ExecuteNonQuery(sql, System.Data.CommandType.Text, null);

                // check if any pending PayrollApproval records, if so, disable email token (set token expired date)
                DisableApprovalToken(recipientsToRemove);
            }
        }

        private void DisableApprovalToken(IEnumerable<int> recipientsToRemove)
        {
            var tokensToDisable = _dbContext.InvoiceApprovalSetupSequences
                .Where(s => recipientsToRemove.Contains(s.InvoiceApprovalRecipientID))
                .AsEnumerable()
                .Join(_dbContext.InvoiceApprovals,
                     s => s.InvoiceApprovalSetupSequenceID,
                     a => a.InvoiceApprovalSetupSequenceID,
                     (s, a) => new { s, a })
                .Where(sa => sa.a.Approved == false && sa.a.Rejected == false)
                .AsEnumerable()
                .Join(_dbContext.InvoiceApprovalEmailTokens,
                    sa => new { sa.s.InvoiceApprovalSetupSequenceID, sa.a.InvoiceNumber, sa.a.CompanyID, sa.a.ClientID },
                     t => new { t.InvoiceApprovalSetupSequenceID, t.InvoiceNumber, t.CompanyID, t.ClientID},
                    (sa, t) => new { sa, t })
                .Where(sat => sat.t.DateUsed == null &&
                              sat.t.Expires > DateTime.Now)
                .Select(sat => sat.t);

            if (tokensToDisable.Any())
            {
                foreach (var token in tokensToDisable)
                {
                    token.Expires = DateTime.Now;
                }
                _dbContext.SaveChanges();
            }
        }

        private void InsertGroupRecipients(InvoiceApprovalSetupModel setup, IList<InvoiceApprovalRecipientModel> recipientGroups, List<InvoiceApprovalRecipient> currentGroupRecipients)
        {
            var recipientsToAdd = recipientGroups.Where(n => !currentGroupRecipients.Select(c => c.GroupID).Contains(n.GroupID)).ToList();
            if (recipientsToAdd.Any())
            {
                List<InvoiceApprovalRecipientModel> groupRecipientsToAdd = new List<InvoiceApprovalRecipientModel>();
                groupRecipientsToAdd.AddRange(recipientsToAdd);
                foreach (InvoiceApprovalRecipientModel group in recipientsToAdd)
                {
                    int groupId = (int)group.GroupID;
                    int sequence = group.Sequence;
                    bool requireAll = (bool)group.GroupRequireAll;

                    // create a recipient (InvoiceApprovalRecipients) and sequence (InvoiceApprovalSetupSequences) record for each current member of the each group
                    var groupMembers = GetInvoiceNotificationGroupRecipients(groupId);

                    var invoiceApprovalGroupRecipients = groupMembers
                        .Select(m => new InvoiceApprovalRecipientModel
                        {
                            GroupID = groupId,
                            RecipientID = m.UserID,
                            UserID = m.UserID,
                            Sequence = sequence,
                            GroupRequireAll = requireAll
                        });

                    groupRecipientsToAdd.AddRange(invoiceApprovalGroupRecipients);
                }

                AddRecipients(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, groupRecipientsToAdd);
            }

            var inactiveGroupRecipientsToActivate = currentGroupRecipients.Where(n => recipientGroups.Select(c => c.GroupID).Contains(n.GroupID) && n.Inactive).ToList();
            if (inactiveGroupRecipientsToActivate.Any())
            {
                List<InvoiceApprovalRecipientModel> groupRecipientsToAdd = new List<InvoiceApprovalRecipientModel>();
                List<InvoiceApprovalRecipient> inactiveRecipientGroups = inactiveGroupRecipientsToActivate.Where(n => n.RecipientID == null).ToList();
                foreach (var group in inactiveRecipientGroups)
                {
                    int groupId = (int)group.GroupID;
                    // don't activate inactive group recipients that are no longer group members
                    var inactiveGroupRecipients = inactiveGroupRecipientsToActivate.Where(n => n.GroupID == groupId && n.RecipientID != null);

                    List<UserModel> currentGroupMembers = GetInvoiceNotificationGroupRecipients(groupId);

                    List<string> recipientsRemovedFromGroup = inactiveGroupRecipients.Where(n => !currentGroupMembers.Select(c => c.UserID).Contains(n.RecipientID)).Select(n => n.RecipientID).ToList();
                    inactiveGroupRecipientsToActivate.RemoveAll(n => n.GroupID == groupId && recipientsRemovedFromGroup.Contains(n.RecipientID));

                    // have new members been added to the group?  if so, create recipient and sequence records
                    var groupMembersToAdd = currentGroupMembers.Where(c => !inactiveGroupRecipients.Select(n => n.RecipientID).Contains(c.UserID));     // current group members that are not in the inactiveGroupRecipients list

                    if (groupMembersToAdd.Any())
                    {
                        var recipientGroup = recipientGroups.FirstOrDefault(g => g.GroupID == groupId);
                        int sequence = recipientGroup.Sequence;
                        bool requireAll = (bool)recipientGroup.GroupRequireAll;

                        var invoiceApprovalGroupRecipients = groupMembersToAdd
                        .Select(m => new InvoiceApprovalRecipientModel
                        {
                            GroupID = groupId,
                            RecipientID = m.UserID,
                            UserID = m.UserID,
                            Sequence = sequence,
                            GroupRequireAll = requireAll
                        });

                        groupRecipientsToAdd.AddRange(invoiceApprovalGroupRecipients);
                    }
                }

                if (groupRecipientsToAdd.Any())
                {
                    AddRecipients(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, groupRecipientsToAdd);
                }

                List<InvoiceApprovalRecipient> invoiceApprovalRecipients = ActivateInvoiceApprovalRecipients(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, inactiveGroupRecipientsToActivate);

                var recipientsToUpdate = recipientGroups.Where(n => inactiveGroupRecipientsToActivate.Select(c => c.GroupID).Contains(n.GroupID)).ToList();

                List<InvoiceApprovalRecipientModel> invoiceApprovalRecipientsToUpdate = recipientsToUpdate
                    .Join(invoiceApprovalRecipients,
                        u => u.GroupID,
                        r => r.GroupID,
                        (u, r) => new { u, r })
                    .Where(ur => ur.r.InvoiceApprovalSetupID == setup.InvoiceApprovalSetupID)
                    .Select(ur => new InvoiceApprovalRecipientModel
                    {
                        InvoiceApprovalSetupID = ur.r.InvoiceApprovalSetupID,
                        InvoiceApprovalRecipientID = ur.r.InvoiceApprovalRecipientID,
                        Sequence = ur.u.Sequence,
                        GroupID = ur.u.GroupID,
                        GroupRequireAll = ur.u.GroupRequireAll
                    }).ToList();

                List<int> inactiveGroupRecipientsToActivateIds = inactiveGroupRecipientsToActivate.Select(a => a.InvoiceApprovalRecipientID).ToList();
                UpdateInvoiceApprovalSetupSequences(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, inactiveGroupRecipientsToActivateIds, invoiceApprovalRecipientsToUpdate);
            }
        }

        private List<UserModel> GetInvoiceNotificationGroupRecipients(int invoiceNotificationGroupId)
        {
            return _dbContext.InvoiceNotificationGroupMembers
                .Where(g => g.GroupID == invoiceNotificationGroupId)
                .Join(_dbContext.Users,
                    g => g.UserID,
                    u => u.UserID,
                    (g, u) => new { g, u })
                .Select(gu => new UserModel
                {
                    UserID = gu.g.UserID,
                    Email = gu.u.Email,
                    Name = gu.u.Name
                })
                .ToList();
        }

        private List<InvoiceApprovalRecipient> ActivateInvoiceApprovalRecipients(int invoiceApprovalSetupId, string approvalType, string approvalModel, List<InvoiceApprovalRecipient> inactiveRecipients)
        {
            IEnumerable<int> inactiveInvoiceApprovalRecipientIds = inactiveRecipients.Select(x => x.InvoiceApprovalRecipientID);

            if (inactiveRecipients.Any())
            {
                string recipients = string.Join(", ", inactiveInvoiceApprovalRecipientIds);

                string sql = $"UPDATE InvoiceApprovalRecipients SET Inactive = 0 WHERE InvoiceApprovalRecipientID IN ({recipients})";

                _sqlConnectionProvider.ExecuteNonQuery(sql, System.Data.CommandType.Text, null);
            }

            List<InvoiceApprovalRecipient> invoiceApprovalRecipients = _dbContext.InvoiceApprovalRecipients
                .Where(a => a.InvoiceApprovalSetupID == invoiceApprovalSetupId &&
                            inactiveInvoiceApprovalRecipientIds.Contains(a.InvoiceApprovalRecipientID))
                .ToList();

            return invoiceApprovalRecipients;
        }

        private void InsertRecipients(InvoiceApprovalSetupModel setup, IList<InvoiceApprovalRecipientModel> recipients, List<InvoiceApprovalRecipient> currentRecipients)
        {
            var recipientsToAdd = recipients.Where(n => !currentRecipients.Select(c => c.RecipientID).Contains(n.RecipientID ?? n.UserID)).ToList();
            if (recipientsToAdd.Any())
            {
                AddRecipients(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, recipientsToAdd);
            }

            var inactiveRecipientsToActivate = currentRecipients.Where(n => recipients.Select(c => c.RecipientID ?? c.UserID).Contains(n.RecipientID) && n.Inactive).ToList();
            if (inactiveRecipientsToActivate.Any())
            {
                List<InvoiceApprovalRecipient> invoiceApprovalRecipients = ActivateInvoiceApprovalRecipients(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, inactiveRecipientsToActivate);

                UpdateInvoiceApprovalRecipientSetupSequences(setup, recipients, inactiveRecipientsToActivate, invoiceApprovalRecipients);
            }
        }

        private void UpdateInvoiceApprovalRecipientSetupSequences(InvoiceApprovalSetupModel setup, IList<InvoiceApprovalRecipientModel> recipients, List<InvoiceApprovalRecipient> inactiveRecipientsToActivate, List<InvoiceApprovalRecipient> invoiceApprovalRecipients)
        {
            var recipientsToUpdate = recipients.Where(n => inactiveRecipientsToActivate.Select(c => c.RecipientID).Contains(n.RecipientID ?? n.UserID)).ToList();
            List<InvoiceApprovalRecipientModel> invoiceApprovalRecipientsToUpdate = recipientsToUpdate
                .Join(invoiceApprovalRecipients,
                    r => new { RecipientID = r.UserID },
                    a => new { a.RecipientID },
                    (r, a) => new { r, a })
                .Where(ra => ra.a.InvoiceApprovalSetupID == setup.InvoiceApprovalSetupID)
                .Select(ra => new InvoiceApprovalRecipientModel
                {
                    InvoiceApprovalSetupID = ra.a.InvoiceApprovalSetupID,
                    InvoiceApprovalRecipientID = ra.a.InvoiceApprovalRecipientID,
                    RecipientID = ra.r.UserID,
                    UserID = ra.r.UserID ?? ra.r.RecipientID ?? ra.a.RecipientID,
                    Inactive = ra.a.Inactive,
                    Sequence = ra.r.Sequence,
                    GroupID = ra.r.GroupID,
                    GroupRequireAll = ra.r.GroupRequireAll
                }).ToList();

            List<int> inactiveRecipientsToActivateIds = inactiveRecipientsToActivate.Select(a => a.InvoiceApprovalRecipientID).ToList();
            UpdateInvoiceApprovalSetupSequences(setup.InvoiceApprovalSetupID, setup.ApprovalType, setup.ApprovalModel, inactiveRecipientsToActivateIds, invoiceApprovalRecipientsToUpdate);
        }

        public IEnumerable<InvoiceApprovalRecipient> GetInvoiceApprovalSetupNotificationRecipients(int companyID, string clientID, int invoiceNumber, string invoiceProfileApprovalNotificationType)
        {
            return _dbContext.InvoiceApprovalRecipients
                .Join(
                    _dbContext.InvoiceApprovals,
                    r => new { r.InvoiceApprovalSetup.CompanyID, r.InvoiceApprovalSetup.ClientID},
                    a => new { a.CompanyID, a.ClientID},
                    (r, a) => new { r, a })
                .Where(ra => ra.a.CompanyID == companyID &&
                             ra.a.ClientID == clientID &&
                             ra.a.InvoiceNumber == invoiceNumber &&
                             ra.r.InvoiceApprovalSetup.ApprovalType == invoiceProfileApprovalNotificationType &&
                             !ra.a.Inactive)
                .Select(ra => ra.r);
        }

        #endregion
    }
}