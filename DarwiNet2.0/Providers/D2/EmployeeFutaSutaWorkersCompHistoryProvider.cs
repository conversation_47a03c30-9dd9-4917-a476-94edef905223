using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers;
using DarwiNet2._0.Interfaces.Providers.D2;
using EO.Pdf.Internal;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class EmployeeFutaSutaWorkersCompHistoryProvider : DbContextBaseProvider, IEmployeeFutaSutaWorkersCompHistoryProvider
    {
        #region Constructors

        public EmployeeFutaSutaWorkersCompHistoryProvider()
            : base()
        {

        }

        public EmployeeFutaSutaWorkersCompHistoryProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        public EmployeeFutaSutaWorkersCompHistoryProvider(DnetEntities dbContext, ISqlConnectionProvider sqlConnectionProvider)
            : base(dbContext, sqlConnectionProvider)
        {

        }

        #endregion

        //public List<EmployeeFutaSutaWorkersCompHistory> ListEmployeeFutaSutaWorkersCompHistory(string employeeId)
        //{
        //    return _dbContext.EmployeeFutaSutaWorkersCompHistories
        //        .Where(e => e.EmployeeID == employeeId)
        //        .ToList();
        //}

        //public List<EmployeeFutaSutaWorkersCompHistory> ListEmployeeFutaSutaWorkersCompHistory(string employeeId, int year)
        //{
        //    return _dbContext.EmployeeFutaSutaWorkersCompHistories
        //        .Where(e => e.EmployeeID == employeeId &&
        //                    e.Year == year)
        //        .ToList();
        //}

        public List<EmployeeFutaSutaWorkersCompHistoryModel> GetEmployeeFutaSutaWorkersCompHistory(int companyId, string employeeId, int? year)
        {
            return _dbContext.EmployeeFutaSutaWorkersCompHistories
                .Where(e => e.CompanyID == companyId &&
                            e.EmployeeID == employeeId &&
                            (year == null || (year != null && e.Year == year)))
                .Select(e => new EmployeeFutaSutaWorkersCompHistoryModel
                {
                    PayrollRecordType = e.PayrollRecordType,
                    FUTABilling = e.FUTABilling,
                    AdditionalFUTABilling = e.AdditionalFUTABilling
                }).ToList();
        }

        public EmployeeFutaSutaWorkersCompHistoryTotalsModel GetPayrollTaxes(int companyId, string employeeId, int? year = null)
        {
            EmployeeFutaSutaWorkersCompHistoryTotalsModel employeeFutaSutaWorkersCompHistory = new EmployeeFutaSutaWorkersCompHistoryTotalsModel();

            List<EmployeeFutaSutaWorkersCompHistoryModel> records = GetEmployeeFutaSutaWorkersCompHistory(companyId, employeeId, year);

            employeeFutaSutaWorkersCompHistory.SUTATotal = records.Where(e => e.PayrollRecordType == 1).Sum(e => (e.FUTABilling ?? 0) + (e.AdditionalFUTABilling ?? 0));
            employeeFutaSutaWorkersCompHistory.FUTATotal = records.Where(e => e.PayrollRecordType == 2).Sum(e => (e.FUTABilling ?? 0) + (e.AdditionalFUTABilling ?? 0));
            employeeFutaSutaWorkersCompHistory.WCTotal = records.Where(e => e.PayrollRecordType != 1 && e.PayrollRecordType != 2).Sum(e => (e.FUTABilling ?? 0) + (e.AdditionalFUTABilling ?? 0));

            return employeeFutaSutaWorkersCompHistory;
        }

        public void SaveListEmployeeFutaSutaWCHistories(List<EmployeeFutaSutaWorkersCompHistory> employeeFutaSutaWorkersCompHistories)
        {
            _dbContext.EmployeeFutaSutaWorkersCompHistories.AddRange(employeeFutaSutaWorkersCompHistories);
            _dbContext.SaveChanges();
        }

        public decimal GetNextRecordNumber()
        {
            return Convert.ToDecimal(_sqlConnectionProvider.ExecuteScalar("SELECT NEXT VALUE FOR EEFutaSutaWCHistoryRecordNumberSequence;", CommandType.Text, null));
        }
    }
}