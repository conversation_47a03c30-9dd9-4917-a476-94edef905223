using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class LocalTaxProvider : DbContextBaseProvider, ILocalTaxProvider
    {
        #region Constructors 

        public LocalTaxProvider()
            : base()
        {

        }

        public LocalTaxProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        public List<LocalTax> ListLocalTaxCodes(int companyId, List<string> codes)
        {
            return _dbContext.LocalTaxes
                .Where(c => c.CompanyID == companyId &&
                            codes.Contains(c.LocalTax1))
                .ToList();
        }
        
        public List<string> ListAvailableLocalTaxCodes(int companyId)
        {
            return _dbContext.LocalTaxes
                .Where(t => t.CompanyID == companyId)
                .OrderBy(t => t.LocalTax1)
                .Select(t => t.LocalTax1)
                .ToList();
        }

        public List<MultiselectOptionsDTO> ListAvailableLocalTaxCodesMultiSelect(int companyId)
        {
            return _dbContext.LocalTaxes
                .Where(t => t.CompanyID == companyId)
                .OrderBy(t => t.LocalTax1)
                .Select(t => new MultiselectOptionsDTO
                {
                    Code = t.LocalTax1,
                    Display = t.LocalTax1
                })
                .ToList();
        }

        public List<MultiselectOptionsDTO> ListAvailableLocalTaxCodesMultiSelectByUserCompany()
        {
            var companyId = GlobalVariables.CompanyID;

            return _dbContext.LocalTaxes
                .Where(t => t.CompanyID == companyId)
                .OrderBy(t => t.LocalTax1)
                .Select(t => new MultiselectOptionsDTO
                {
                    Code = t.LocalTax1,
                    Display = t.LocalTax1
                })
                .ToList();
        }
    }
}