using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class EmployeeDirectDepositProvider : DbContextBaseProvider, IEmployeeDirectDepositProvider
    {
        #region Constructors

        public EmployeeDirectDepositProvider()
            : base()
        {

        }

        public EmployeeDirectDepositProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion
        public List<EmployeeDirectDeposit> FindByEmployeeId(int companyId, string employeeId)
        {
            List<EmployeeDirectDeposit> result = new List<EmployeeDirectDeposit>();
            result = _dbContext.EmployeeDirectDeposits
                .Where(e => e.CompanyID == companyId
                          && e.EmployeeID == employeeId
                          && !e.Inactive)
                .ToList();

            return result;
        }

        public List<EmployeeDirectDeposit> GetEmployeeDirectDepositsByEmployee(int companyID, string employeeID)
        {
            return _dbContext.EmployeeDirectDeposits.Where(edd =>
                    edd.CompanyID == companyID && edd.EmployeeID == employeeID).ToList();
        }
    }
}