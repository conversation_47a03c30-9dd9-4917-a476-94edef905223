using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;

namespace DarwiNet2._0.Providers.D2
{
    public class InvoiceAgencyCreditHistoryOriginalsProvider : DbContextBaseProvider, IInvoiceAgencyCreditHistoryOriginalsProvider
    {
        #region Fields



        #endregion

        #region Constructors

        public InvoiceAgencyCreditHistoryOriginalsProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public string SaveOriginals(List<InvoiceAgencyCreditHistoryOriginal> invoiceAgencyCreditHistoryOriginalsToSave, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            dbContext.InvoiceAgencyCreditHistoryOriginals.AddRange(invoiceAgencyCreditHistoryOriginalsToSave);
            return "Success";
        }

        public List<InvoiceAgencyCreditHistoryOriginal> ListInvoiceAgencyCreditHistoryOriginalsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            return dbContext.InvoiceAgencyCreditHistoryOriginals.Where(x => x.CompanyID == companyID
                                                                            && x.ClientID == clientID
                                                                            && darwinInvoiceNumbers.Contains(x.DarwinInvoiceNumber)).ToList();
        }

        public void DeleteOriginalsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            var query = $"DELETE FROM InvoiceAgencyCreditHistoryOriginals " +
                        $"WHERE CompanyID = @companyId AND ClientID = @clientId AND DarwinInvoiceNumber IN ({string.Join(", ", darwinInvoiceNumbers.Select((darwinInvoiceNumber, i) => "@darwinInvoiceNumber" + i))});";

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("@companyId", companyID),
                new SqlParameter("@clientId", clientID),
            }.ToArray();
            var darwinInvoiceNumberParameters = darwinInvoiceNumbers.Select((darwinInvoiceNumber, i) => new SqlParameter("@darwinInvoiceNumber" + i, SqlDbType.Int) { Value = darwinInvoiceNumber }).ToArray();
            var allParameters = parameters.Concat(darwinInvoiceNumberParameters).ToArray();

            dbContext.Database.ExecuteSqlCommand(query, allParameters);
        }

        #endregion
    }
}