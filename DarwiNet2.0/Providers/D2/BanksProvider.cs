using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class BanksProvider : DbContextBaseProvider, IBanksProvider
    {
        #region Constructors

        public BanksProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Functions

        public String GetBankName(int companyId, string bankId) =>
            _dbContext.Banks.FirstOrDefault(pc => pc.CompanyID == companyId && pc.BankID == bankId)?.BankName;

        #endregion


    }
}