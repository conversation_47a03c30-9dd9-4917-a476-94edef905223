using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollWorkStateTaxOriginalProvider : DbContextBaseProvider, IPayrollWorkStateTaxOriginalProvider
    {
        #region Fields



        #endregion

        #region Constructors

        public PayrollWorkStateTaxOriginalProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public void SaveOriginals (List<PayrollWorkStateTaxesOriginal> payrollWorkStateTaxesOriginals, DnetEntities context)
        {
            if (context == null) context = _dbContext;
            context.PayrollWorkStateTaxesOriginals.AddRange(payrollWorkStateTaxesOriginals);
            context.SaveChanges();
        }

        public List<PayrollWorkStateTaxesOriginal> ListStateTaxOriginals(string payrollNumber, string employeeID, DnetEntities context = null)
        {
            if (context == null) context = _dbContext;
            return context.PayrollWorkStateTaxesOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeID).ToList();
        }

        public void DeleteFromPayroll(string payrollNumber)
        {
            var recordsToRemove = _dbContext.PayrollWorkStateTaxesOriginals.Where(x => x.PayrollNumber == payrollNumber).ToList();

            _dbContext.PayrollWorkStateTaxesOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        public void DeleteFromPayroll(string payrollNumber, string employeeId)
        {
            var recordsToRemove = _dbContext.PayrollWorkStateTaxesOriginals.Where(x => x.PayrollNumber == payrollNumber && x.EmployeeID == employeeId).ToList();

            _dbContext.PayrollWorkStateTaxesOriginals.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        #endregion
    }
}