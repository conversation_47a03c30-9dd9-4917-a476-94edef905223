using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class InvoiceApprovalSetupSequenceProvider: DbContextBaseProvider, IInvoiceApprovalSetupSequenceProvider
    {
        public InvoiceApprovalSetupSequenceProvider(DnetEntities dbContext) : base(dbContext) { }
        public InvoiceApprovalSetupSequence GetInvoiceApprovalSetupSequence(int invoiceApprovalSetupSequenceId)
        {
            return _dbContext.InvoiceApprovalSetupSequences
                .FirstOrDefault(x => x.InvoiceApprovalSetupSequenceID == invoiceApprovalSetupSequenceId);
        }
    }
}