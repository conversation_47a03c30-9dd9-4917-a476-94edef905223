using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;

namespace DarwiNet2._0.Providers.D2
{
    public class InvoiceASOSetupAccountHistoryProvider : DbContextBaseProvider, IInvoiceASOSetupAccountHistoryProvider
    {
        public InvoiceASOSetupAccountHistoryProvider()
        {
        }
        #region Fields



        #endregion

        #region Constructors

        public InvoiceASOSetupAccountHistoryProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers)
        {
            var recordsToRemove = _dbContext.InvoiceASOAccountSetupHistories.Where(x => x.CompanyID == companyId
                                                                    && x.ClientID == clientId
                                                                    && invoiceNumbers.Contains((int)x.DarwinInvoiceNumber)).ToList();

            _dbContext.InvoiceASOAccountSetupHistories.RemoveRange(recordsToRemove);
            _dbContext.SaveChanges();
        }

        public List<InvoiceASOAccountSetupHistory> ListInvoiceASOAccountSetupHistoriesByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            return dbContext.InvoiceASOAccountSetupHistories.Where(x => x.CompanyID == companyID
                                                                        && x.ClientID == clientID
                                                                        && x.DarwinInvoiceNumber == darwinInvoiceNumber).ToList();
        }

        public List<InvoiceASOAccountSetupHistory> ListInvoiceASOAccountSetupHistoriesByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            return dbContext.InvoiceASOAccountSetupHistories.Where(x => x.CompanyID == companyID
                                                                        && x.ClientID == clientID
                                                                        && (x.MergedInvoiceNumber == mergedInvoiceNumber ||
                                                                            x.DarwinInvoiceNumber == mergedInvoiceNumber)).ToList();
        }
        
        public List<InvoiceASOAccountSetupHistory> ListInvoiceASOAccountSetupHistoriesByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            return dbContext.InvoiceASOAccountSetupHistories.Where(x => x.CompanyID == companyID
                                                                        && x.ClientID == clientID
                                                                        && darwinInvoiceNumbers.Contains(x.DarwinInvoiceNumber)).ToList();
        }

        public void UpdateMergedInvoiceNumberRaw(InvoiceASOAccountSetupHistory invoiceASOAccountSetupHistory, int mergedInvoiceNumber, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            dbContext.Database.ExecuteSqlCommand("UPDATE InvoiceASOAccountSetupHistory SET MergedInvoiceNumber = @param1 " +
                                                 "WHERE CompanyID = @param2 " +
                                                 "AND ClientID = @param3 " +
                                                 "AND DarwinInvoiceNumber = @param4 " +
                                                 "AND AccountType = @param5;",
                                                 new SqlParameter("param1", mergedInvoiceNumber),
                                                 new SqlParameter("param2", invoiceASOAccountSetupHistory.CompanyID),
                                                 new SqlParameter("param3", invoiceASOAccountSetupHistory.ClientID),
                                                 new SqlParameter("param4", invoiceASOAccountSetupHistory.DarwinInvoiceNumber),
                                                 new SqlParameter("param5", invoiceASOAccountSetupHistory.AccountType));
        }

        public void SaveInvoiceASOAccountSetupHistory(InvoiceASOAccountSetupHistory invoiceASOAccountSetupHistoryToSave, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            dbContext.InvoiceASOAccountSetupHistories.Add(invoiceASOAccountSetupHistoryToSave);
        }

        public void DeleteListInvoiceASOAccountSetupHistories(List<InvoiceASOAccountSetupHistory> invoiceASOAccountSetupHistories, DnetEntities dbContext = null)
        {
            if (dbContext == null) dbContext = _dbContext;
            dbContext.InvoiceASOAccountSetupHistories.RemoveRange(invoiceASOAccountSetupHistories);
        }

        #endregion
    }
}