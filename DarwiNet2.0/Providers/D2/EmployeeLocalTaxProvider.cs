using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.D2
{
    public class EmployeeLocalTaxProvider : DbContextBaseProvider, IEmployeeLocalTaxProvider
    {
        #region Constructors

        public EmployeeLocalTaxProvider()
            : base()
        {

        }

        public EmployeeLocalTaxProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        public List<EmployeeLocalTax> FindByEmployeeId(int companyId, string employeeId)
        {
            List<EmployeeLocalTax> result = new List<EmployeeLocalTax>();
            result = _dbContext.EmployeeLocalTaxes
                .Where(e => e.CompanyID == companyId
                          && e.EmployeeID == employeeId
                          && !e.Inactive)
                .ToList();

            return result;
        }
        #endregion
    }
}