using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.D2
{
    public class PayrollUserClientAccessProvider : DbContextBaseProvider, IPayrollUserClientAccessProvider
    {    

        public PayrollUserClientAccessProvider()
            : base()
        {
            
        }

        public PayrollUserClientAccessProvider(DnetEntities dbContext)
            : base(dbContext)
        {
            _dbContext = dbContext;
        }
        public List<string> GetClientAccessList(string userId, int companyId)
        {
            return _dbContext.UserRoleClientAccesses.Where(c => c.UserID == userId && c.CompanyID == companyId).Select(x => x.ClientID).ToList();
        }

        public bool CanAccessClient(string userId, int companyId, string clientId)
        {
            return _dbContext.UserRoleClientAccesses.Any(c => c.UserID == userId && c.CompanyID == companyId && c.ClientID == clientId);
        }
    }
}