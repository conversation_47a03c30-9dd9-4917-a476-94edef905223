using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers
{
    public class DocumentUploadDetailsProvider : DbContextBaseProvider, IDocumentUploadDetailsProvider
    {
        public DocumentUploadDetailsProvider()
           : base()
        {

        }

        public DocumentUploadDetail GetEmployeeFileByName(string employeeID, string client, string fileName, string formatedFileName, int transId)
        {
            var file = _dbContext.DocumentUploadDetails.Where(doc => doc.EmployeeId == employeeID
            && doc.ClientId == client
            && doc.FileName == fileName
            && doc.FormattedFileName == formatedFileName.ToString()
            && doc.TransactionID == transId)
                .FirstOrDefault();
            return file;

            
        }


        public List<DocumentUploadDetail> GetPendingEmployeeDocumentsByTransaction(
            string employeeID,
            string client,
            int eventType,
            int transactionId)
        {
            return _dbContext.DocumentUploadDetails
                .Where(doc => doc.EmployeeId == employeeID
                    && doc.ClientId == client
                    && doc.EventType == eventType
                    && doc.ApprovalStatus != "Submitted-Approved"
                    && doc.TransactionID == transactionId)
                .ToList();
        }

        public List<DocumentUploadDetail> GetEmployeeDocumentByFileAndId(string employeeId, string clientId, string fileName, int transactionId, int id)
        {
            return _dbContext.DocumentUploadDetails
                .Where(record => record.EmployeeId == employeeId
                    && record.ClientId == clientId
                    && record.FileName == fileName
                    && record.TransactionID == transactionId
                    && record.Id == id)
                .ToList();
        }

        public List<DocumentUploadDetail> GetEmployeeDocumentByFormattedFileNameAndId(string employeeId, string clientId, string fileName, int id)
        {
            return _dbContext.DocumentUploadDetails
                .Where(record => record.EmployeeId == employeeId
                    && record.ClientId == clientId
                    && record.FormattedFileName == fileName
                    && record.Id == id)
                .ToList();
        }
    }
}