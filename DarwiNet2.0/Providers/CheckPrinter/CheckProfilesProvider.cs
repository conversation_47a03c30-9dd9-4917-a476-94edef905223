using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.CheckPrinter;
using System.Linq;

namespace DarwiNet2._0.Providers.CheckPrinter
{
    public class CheckProfilesProvider : DbContextBaseProvider, ICheckProfilesProvider
    {
        public CheckProfilesProvider(DnetEntities dbContext)
            : base(dbContext)
        {
        }

        public CheckPrinterProfile GetProfileByName(int companyid, string clientid, string name) =>
            _dbContext.CheckPrinterProfiles.FirstOrDefault(x => x.CompanyID == companyid && (x.ClientID == clientid || x.ClientID == "***") && x.RPTFile == name);

        public CheckPrinterProfile GetProfileById(int companyid, string clientid, int id) =>
            _dbContext.CheckPrinterProfiles.FirstOrDefault(x => x.CompanyID == companyid && (x.ClientID == clientid || x.ClientID == "***") && x.ProfileID == id);
        
        public bool IsProfileExists(int companyid, string clientid, string name) =>
            _dbContext.CheckPrinterProfiles.Any(x => x.CompanyID == companyid && (x.ClientID == clientid || x.ClientID == "***") && x.RPTFile == name);
    }
}