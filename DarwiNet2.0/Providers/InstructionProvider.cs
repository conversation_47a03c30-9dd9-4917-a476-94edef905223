using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers
{
    public class InstructionProvider : DbContextBaseProvider, IInstructionProvider
    {
        public InstructionProvider()
            : base()
        {

        }
        public Instruction GetInstructionByType(int  insType, int company )
        {
            return _dbContext.Instructions.FirstOrDefault(oe => oe.Type == insType  && oe.CompanyID == company);
        }

    }

}