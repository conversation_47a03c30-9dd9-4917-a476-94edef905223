using DarwiNet2._0.Models.API;
using System;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Providers.API.Interfaces
{
    public interface ITwoFactorAuthenticationProvider : IDisposable
    {
        TwoFactorAuthenticationDTO GetUserTwoFactorInfo(string user, out string twoFactorTmpToken);
        bool Required(string user, string twoFactorRememberMeToken);
        bool SendSmsMessage(string accountSid, string authToken, string body, string to, string from);
        void SendTwoFactorAuthCode(string user, string twoFactorTmpToken);
        void SendTwoFactorCodeEmailMessage(User user, string email);
        void SendTwoFactorCodeSmsMessage(Company company, User user, string phoneNumber);
        bool Verified(TwoFactorAuthenticationDTO user, out string sessionToken, out string twoFactorRememberMeToken);
        string SendTwilioTwoFactor(string user, string password, string serviceSid, string to, string channel);
        bool VerifyTwilioTwoFactor(string user, string password, string serviceSid, string code, string verificationSid);
    }
}