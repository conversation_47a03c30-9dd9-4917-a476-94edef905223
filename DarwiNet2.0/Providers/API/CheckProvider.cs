using DarwiNet2._0.Factories;
using DarwiNet2._0.Factories.CheckDetails;
using DarwiNet2._0.Models.API;
using DarwiNet2._0.Utilities;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using DarwiNet2._0.Data;
using static DarwiNet2._0.Factories.CheckDetail;

namespace DarwiNet2._0.Providers.API
{
    public class CheckProvider : ICheckProvider
    {
        private DarwinetSetup ClientSetup { get; set; }
        private DnetEntities _dbContext;
        private ICodeDescriptionsProvider _codeDescriptionsProvider;

        public CheckProvider(DnetEntities dbContext)
        {
            _dbContext = dbContext;
        }

        public CheckProvider(DnetEntities dbContext, ICodeDescriptionsProvider codeDescriptionsProvider)
        {
            _dbContext = dbContext;
            _codeDescriptionsProvider = codeDescriptionsProvider;
        }

        public YearToDateCheckHistoryDTO GetEmployeeCheckHistoryForYear(int companyId, string clientId, string employeeId, int year)
        {
            var checks = GetChecks(companyId, clientId, employeeId, year);
            var ytdCheckHistoryTotals = PopulateYearToDateCheckHistoryTotalsDTO(companyId, employeeId, year);
            var ytdCheckHistory = YearToDateCheckHistoryDTO.PopulateYearToDateCheckHistoryDTO(checks, year, ytdCheckHistoryTotals);

            return ytdCheckHistory;
        }

        private List<CheckDTO> GetChecks(int companyId, string clientId, string employeeId, int year)
        {
            var checks = GetAllEmployeeChecksForYear(companyId, employeeId, year);
            RetrieveClientSetup(companyId, clientId);
            checks = RemoveManualChecksIfNeeded(checks, companyId, clientId);
            checks = RemoveVoidChecksIfNeeded(checks);
            checks = RemoveFutureChecksIfNeeded(checks);

            return checks;
        }

        private List<CheckDTO> GetAllEmployeeChecksForYear(int companyId, string employeeId, int year)
        {
                var checks =
                    _dbContext.EmployeeCheckHistories
                        .Where(check => check.CompanyID == companyId && 
                                        check.EmployeeID == employeeId && 
                                        check.Year == year)
                        .Select(check =>
                            new CheckDTO
                            {
                                PaymentAdjustmentNumber = check.PaymentAdjustmentNumber,
                                Date = check.CheckDate,
                                CheckNumber = check.CheckNumber,
                                GrossPay = check.GrossWagesPayRun,
                                NetPay = check.NetWagesPayRun,
                                Taxes = check.TotalTaxes,
                                Deductions = check.TotalDeductions,
                                Benefits = check.TotalBenefits,
                                AuditControlCode = check.AuditControlCode,
                                Voided = check.Voided
                            }
                        ).ToList();

                return checks;
        }

        private List<CheckDTO> RemoveManualChecksIfNeeded(List<CheckDTO> checks, int companyId, string clientId)
        {
            if (ClientSetup != null && !ClientSetup.ShowAllManualChecks)
            {
                checks = checks.Where(c => !c.AuditControlCode.StartsWith("UPRMC")).ToList();
            }

            return checks;
        }

        private List<CheckDTO> RemoveVoidChecksIfNeeded(List<CheckDTO> checks)
        {
            if (ClientSetup != null && !ClientSetup.ShowVoidedInvoices)
            {
                checks = RemoveChecksWithVoidedAuditControlCode(checks);
                checks = checks.Where(check => !check.Voided).ToList();
            }

            return checks;
        }

        private List<CheckDTO> RemoveChecksWithVoidedAuditControlCode(List<CheckDTO> checks)
        {
            var checksToCut =
                checks.
                    Where(check => check.AuditControlCode.StartsWith("UPRVC")).
                    Select(check =>
                    {
                        return checks.FirstOrDefault(c => c.Number == check.Number && c.AuditControlCode.StartsWith("UPRCC"));
                    }).
                    Where(check => check != null).
                    ToList();

            checks = checks.Where(check => !checksToCut.Contains(check)).ToList();

            return checks;
        }

        private List<CheckDTO> RemoveFutureChecksIfNeeded(List<CheckDTO> checks)
        {
            if (ClientSetup != null && ClientSetup.HideEmployeeChecksUntilCheckDate)
            {
                checks = checks.Where(check => check.Date != null && check.Date <= DateTime.Now).ToList();
            }

            return checks;
        }

        private void RetrieveClientSetup(int companyId, string clientId)
        {
            ClientSetup = _dbContext.DarwinetSetups.FirstOrDefault(setup => setup.CompanyID == companyId && setup.ClientID.Equals(clientId));
        }

        public CheckDetailsDTO GetCheckDetails(int companyId, string clientId, string employeeId, int paymentAdjustmentNumber)
        {
            var check = GetCheck(companyId, employeeId, paymentAdjustmentNumber);
            var invoiceCheck = GetInvoiceCheck(companyId, employeeId, paymentAdjustmentNumber);
            var directDeposits = GetDirectDeposits(companyId, employeeId, paymentAdjustmentNumber, check.AuditControlCode);
            var checkDetails = CheckDetailsDTO.PopulateCheckDetailsDTO(check, invoiceCheck, directDeposits, _codeDescriptionsProvider);

            return checkDetails;
        }

        public bool IsLastEECheck(int companyId, string clientId, string employeeId, int paymentAdjustmentNumber, DateTime? checkDate)
        {
            if (checkDate == null) return false;
            return !_dbContext.EmployeeCheckHistories
                .Where(check => check.CompanyID == companyId &&
                                            check.EmployeeID == employeeId &&
                                            (check.CheckDate > checkDate ||
                                            (check.CheckDate == checkDate &&
                                            check.PaymentAdjustmentNumber > paymentAdjustmentNumber))).Any();
        }

        private EmployeeCheckHistory GetCheck(int companyId, string employeeId, int paymentAdjustmentNumber)
        {
            return _dbContext.EmployeeCheckHistories
                .FirstOrDefault(check => check.CompanyID == companyId && 
                                            check.EmployeeID == employeeId && 
                                            check.PaymentAdjustmentNumber == paymentAdjustmentNumber);
        }

        public EmployeeInvoiceCheckHistory GetInvoiceCheck(int companyId, string employeeId, int paymentAdjustmentNumber)
        {
            return _dbContext.Database.SqlQuery<EmployeeInvoiceCheckHistory>(
                    "sp_GetWorkCheck @company, @employee, @paynumber",
                    new SqlParameter("company", companyId),
                    new SqlParameter("employee", employeeId),
                    new SqlParameter("paynumber", paymentAdjustmentNumber)
                ).First();

        }

        private List<DirectDepositDTO> GetDirectDeposits(int companyId, string employeeId, int paymentAdjustmentNumber, string auditControlCode)
        {
            return _dbContext.EmployeeDirectDepositHistories
                .Where(d => d.CompanyID == companyId && 
                            d.EmployeeID == employeeId && 
                            d.PaymentAdjustmentNumber == paymentAdjustmentNumber && 
                            d.AuditControlCode == auditControlCode)
                .AsEnumerable()
                .Select(d =>
                    new DirectDepositDTO
                    {
                        Bank = GetBankName(d.BankID),
                        Amount = d.ActualDeposit ?? 0,
                        AccountNumberUnformatted = d.AccountNumber,
                        SequenceNumber = d.SequenceNumber
                    }).ToList();
        }

        private string GetBankName(string bankId)
        {
            return _dbContext.Banks.FirstOrDefault(b => b.BankID == bankId).BankName;
        }

        private List<YearToDateCheckHistoryTotalsDTO> PopulateYearToDateCheckHistoryTotalsDTO(int companyId, string employeeId, int year)
        {
            return
                (
                    from eth in _dbContext.EmployeeTransactionHistories
                    where eth.CompanyID == companyId &&
                            eth.EmployeeID == employeeId &&
                            eth.Year == year
                    group eth.TRXAmount by eth.PayrollRecordType into gp
                    select new YearToDateCheckHistoryTotalsDTO
                    {
                        PayrollRecordType = gp.Key,
                        Total = (decimal)gp.Sum(trxAmount => trxAmount)
                    }
                ).
                ToList();
        }

        public void Dispose()
        {
            _dbContext.Dispose();
        }
    }
}