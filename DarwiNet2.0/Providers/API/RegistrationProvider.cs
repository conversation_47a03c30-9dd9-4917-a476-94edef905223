using DarwiNet2._0.Forms;
using DarwiNet2._0.Models.API;
using DarwiNet2._0.Providers.API.Interfaces;
using Microsoft.Office.Interop.Excel;
using Microsoft.ReportingServices.RdlExpressions.ExpressionHostObjectModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http.ModelBinding;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Providers.API
{
    public class RegistrationProvider : IRegistrationProvider
    {
        private DnetEntities _dbContext;

        public RegistrationProvider(DnetEntities dbContext)
        {
            _dbContext = dbContext;
        }

        public (bool, Employee) ValidEmployee(EmployeeRegistrationDTO employeeInfo, out ModelStateDictionary errors)
        {
            var employeeRetrieveForm = new RetrieveEmployeeForm(_dbContext, employeeInfo);

            if (employeeRetrieveForm.Valid(out errors))
                return (true, employeeRetrieveForm.Employee);

            return (false, null);
        }

        public (bool, User) CreateUser(UserRegistrationDTO userInfo, out ModelStateDictionary errors)
        {
            var createUserForm = new CreateUserForm(_dbContext, userInfo);

            if (createUserForm.Valid(out errors))
                return (true, createUserForm.Create());

            return (false, null);
        }

        public void Dispose()
        {
            _dbContext.Dispose();
        }
    }
}