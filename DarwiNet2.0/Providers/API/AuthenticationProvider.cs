using DarwiNet2._0.Core;
using DarwiNet2._0.Models;
using DarwiNet2._0.Models.API;
using DarwiNet2._0.Utilities;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using Thinkware;
using Constants = DarwiNet2._0.Models.API.Constants;

namespace DarwiNet2._0.Providers.API
{
    public class AuthenticationProvider : IAuthenticationProvider
    {
        private DnetEntities _dbContext;

        public AuthenticationProvider(DnetEntities dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Given the username and password, make sure it is valid and authenticate the user
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <returns>Whether the user login is valid</returns>
        public async Task<Result<User>> AuthenticateAsync(string username, string password)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                return Result.Failure<User>("Please provide Username and Password");
            }
            
            // Find the user given their username and password  
            var userPassword = ConCryptor.Encrypt(password);
            User user = await _dbContext.Users
                .FirstOrDefaultAsync(x =>
                    x.UserID.ToLower() == username.ToLower() &&
                    x.Password == userPassword &&
                    x.Enabled);

            return user == null ? 
                Result.Failure<User>("User not found with the given username and password") : 
                Result.Success(user);
        }

        public bool Login(UserDTO user, out string sessionToken)
        {
            using (var dbContext = new DnetEntities())
            {
                sessionToken = null;

                if (!user.ContainsUsernameAndPassword())
                {
                    return false;
                }

                var currUser = GetUser(dbContext, user);
                if (currUser == null)
                {
                    return false;
                }

                sessionToken = GenerateUniqueSessionToken();
                currUser.Token = sessionToken;
                dbContext.SaveChanges();

                return true;
            }
        }

        public void Logout(string username)
        {
            using (var dbContext = new DnetEntities())
            {
                var currUser = GetUser(dbContext, username);
                currUser.Token = null;
                dbContext.SaveChanges();
            }
        }

        public bool ForgotPassword(string username, out string errMsg)
        {
            using (var dbContext = new DnetEntities())
            {
                var currUser = GetUser(dbContext, username);

                if (string.IsNullOrEmpty(currUser?.Email))
                {
                    errMsg = $"User {username} does not have a valid email address.";
                    return false;
                }

                var globalInfo = DbUtilities.GetGlobalInfo(username);
                var passwordToken = new PasswordToken(username);

                SavePasswordResetCode(dbContext, currUser, passwordToken);
                if (!SendPasswordResetEmail(currUser, globalInfo, passwordToken, out errMsg))
                {
                    errMsg = $"Error occurred Sending Email: {errMsg}";
                    return false;
                }

                return true;
            }
        }

        private void SavePasswordResetCode(DnetEntities dbContext, User user, PasswordToken passwordToken)
        {
            user.ResetCode = passwordToken.Code;
            dbContext.SaveChanges();
        }

        private bool SendPasswordResetEmail(User user, GlobalInfoDTO globalInfo, PasswordToken passwordToken, out string errMsg)
        {
            var nBody = string.Format(Constants.ForgetPasswordEmailBody, user.Name, globalInfo.Url, passwordToken.EncryptedToken);
            SmtpEmail.SendSmtpEmail(user.Email, user.Name, "Password Reset Request", nBody, null, false, null, null, out errMsg, globalInfo);

            return string.IsNullOrEmpty(errMsg);
        }

        public bool ForgotUserId(string email, out string errMsg)
        {
            var users = GetUserByEmail(email);

            if (users.Count == 0)
            {
                errMsg = "No Active user records could be found using the information you supplied.";
                return false;
            }

            if (users.Count > 1)
            {
                errMsg = "Your request has returned more than one record. Please contact your system administrator.";
                return false;
            }

            var currUser = users.FirstOrDefault();

            if (string.IsNullOrEmpty(currUser.Email))
            {
                errMsg = "Cannot find recipient email address";
                return false;
            }

            var globalInfo = DbUtilities.GetGlobalInfo(currUser.UserID);

            if (!SendForgotUserIdEmail(currUser, globalInfo, out errMsg))
            {
                errMsg = $"Error occurred Sending Email: {errMsg}";
                return false;
            }

            return true;
        }


        private bool SendForgotUserIdEmail(User user, GlobalInfoDTO globalInfo, out string errMsg)
        {
            var nBody = string.Format(Constants.ForgotUserIdBody, user.Name, user.UserID);
            SmtpEmail.SendSmtpEmail(user.Email, user.Name, "Forgot User ID Request", nBody, null, false, null, null, out errMsg, globalInfo);

            return string.IsNullOrEmpty(errMsg);
        }

        private User GetUser(DnetEntities dbContext, UserDTO user) =>
            dbContext.Users.
                FirstOrDefault(usr => usr.UserID.Equals(user.Username, StringComparison.CurrentCultureIgnoreCase) && 
                                        usr.Password.Equals(user.EncryptedPassword) && 
                                        usr.Enabled);

        private User GetUser(DnetEntities dbContext, string username)
        {
            return dbContext.Users.FirstOrDefault(usr => usr.UserID.Equals(username, StringComparison.CurrentCultureIgnoreCase) && usr.Enabled);
        }

        private List<User> GetUserByEmail(string email)
        {
            using (var dbContext = new DnetEntities())
            {
                return dbContext.Users.Where(usr => usr.Email.Equals(email, StringComparison.CurrentCultureIgnoreCase) && usr.Enabled).ToList();
            }
        }

        private string GenerateUniqueSessionToken()
        {
            return Guid.NewGuid().ToString();
        }

        public void Dispose()
        {
            _dbContext.Dispose();
        }
    }
}