using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.Documents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Providers.Documents
{
    public class CompanyDocumentDataFieldsProvider : DbContextBaseProvider, ICompanyDocumentDataFieldsProvider
    {
        #region Constructors 

        public CompanyDocumentDataFieldsProvider()
            : base()
        {

        }

        public CompanyDocumentDataFieldsProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Methods

        public CompanyDocumentDataField GetDocumentdataField(string tbl, string fld)
        {
            return _dbContext.CompanyDocumentDataFields
                .FirstOrDefault(x => x.TBL == tbl && x.FLD == fld);
        }

        public CompanyDocumentDataField GetDocumentDataFieldByLabel(string tbl, string fld)
        {
            return _dbContext.CompanyDocumentDataFields
                .FirstOrDefault(x => x.TBL == tbl && x.Label == fld);
        }

        public CompanyDocumentDataField GetDocumentdataFieldWithOptions(string tbl, string fld)
        {
            return _dbContext.CompanyDocumentDataFields
                .FirstOrDefault(x => x.TBL == tbl && x.FLD == fld && !string.IsNullOrEmpty(x.FValueOptions));
        }

        public List<string> GetDocumentSourceFields(string tbl)
        {
            return _dbContext.CompanyDocumentDataFields.Where(f => f.TBL == tbl).OrderBy(f => f.SeqNbr).Select(f => f.Label).ToList();

        }

        #endregion
    }
}