using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Providers.OpenEnrollment;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.OpenEnrollment
{
    public class ClientPlanRateGridCategoryProvider : DbContextBaseProvider, IClientPlanRateGridCategoryProvider
    {
        #region Constructors

        public ClientPlanRateGridCategoryProvider()
            : base()
        {

        }

        #endregion

        #region Public Functions

        public List<ClientPlanRateGridCategory> GetClientPlanRateGridCategories(int companyId, string clientId)
        {
            return _dbContext.ClientPlanRateGridCategories
                .Where(c => c.CompanyID == companyId && 
                            c.ClientID == clientId)
                .ToList();
        }

        public ClientPlanRateGridCategory GetClientPlanRateGridCategory(int companyId, string clientId, int year, string planName, int category)
        {
            return _dbContext.ClientPlanRateGridCategories
                    .FirstOrDefault(c => c.CompanyID == companyId &&
                                         c.ClientID == clientId &&
                                         c.YEAR1 == year &&
                                         c.PlanName == planName &&
                                         c.PlanCategoriesNumber == category);
        }

        public string GetClientPlanRateGridCategoryDescription(int companyId, string clientId, int year, string planName, int category)
        {
            return _dbContext.ClientPlanRateGridCategories
                    .FirstOrDefault(c => c.CompanyID == companyId &&
                                         c.ClientID == clientId &&
                                         c.YEAR1 == year &&
                                         c.PlanName == planName &&
                                         c.PlanCategoriesNumber == category)?
                     .Description;
        }

        #endregion
    }
}