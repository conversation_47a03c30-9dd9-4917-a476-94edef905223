using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Interfaces.Providers.OpenEnrollment;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Providers.OpenEnrollment
{
    public class ClientPlanHDRProvider : DbContextBaseProvider, IClientPlanHDRProvider
    {
        #region Constructors

        public ClientPlanHDRProvider()
            : base()
        {

        }

        public ClientPlanHDRProvider(DnetEntities dbContext)
            : base(dbContext)
        {

        }

        #endregion

        #region Public Functions

        public ClientPlanHDR GetPlanHDR(EmployeeEnrollmentEligiblePlan thisPlan) // modified: 10/23/2019 DS TFS # 5446
        {
            return _dbContext.ClientPlanHDRs
                .FirstOrDefault(cphs => cphs.CompanyID == thisPlan.CompanyID &&
                                        cphs.ClientID == thisPlan.ClientID &&
                                        cphs.YEAR1 == thisPlan.YEAR &&
                                        cphs.PlanName == thisPlan.PlanName);
        }

        public short GetCurrentPlanYear(int companyId, string oeClientId, string enrollmentType, bool[] levents = null)
        {
            var date = DateTime.Now.Date;
            var allPlans = new List<ClientPlanHDR>();

            if (GlobalVariables.AdminEvent != "AdminEvent")
            {
                allPlans = (enrollmentType == Constants.EmployeeEnrollmentStatusEnrollmentTypes.NewHire || enrollmentType == Constants.EmployeeEnrollmentStatusEnrollmentTypes.LifeEvent)
                ? _dbContext.ClientPlanHDRs
                    .Where(cph =>
                        cph.CompanyID == companyId &&
                        cph.ClientID == oeClientId &&
                        cph.ACTIVE &&
                        cph.StartDate <= date &&
                        cph.EndDate >= date &&
                        cph.OpenEnrollment)
                    .ToList()
                : _dbContext.ClientPlanHDRs
                    .Where(cph =>
                        cph.CompanyID == companyId &&
                        cph.ClientID == oeClientId &&
                        cph.ACTIVE &&
                        cph.BeginEnrollment <= date &&
                        cph.EndEnrollment >= date &&
                        cph.OpenEnrollment)
                    .ToList();

                if (enrollmentType == Constants.EmployeeEnrollmentStatusEnrollmentTypes.LifeEvent && levents != null)
                    allPlans = AvailablePlansForLifeEvent(allPlans, levents).ToList();
            }
            else
            {
                allPlans = _dbContext.ClientPlanHDRs
                                .Where(cph =>
                                    cph.CompanyID == GlobalVariables.CompanyID &&
                                    cph.ClientID == GlobalVariables.OEClientID &&
                                    cph.ACTIVE && cph.OpenEnrollment)
                                .ToList();
            }

            short planYear = 0;
            if (allPlans.Any())
            {
                planYear = allPlans.OrderByDescending(ap => ap.YEAR1).First().YEAR1;
            }
            return planYear;
        }

        public ClientPlanHDR GetGrandfatheredPlan(int companyId, string clientId, int year, string planName)
        {
            return _dbContext.ClientPlanHDRs
                    .FirstOrDefault(c => c.CompanyID == companyId &&
                                         c.ClientID == clientId &&
                                         c.YEAR1 == (year - 1) &&
                                         c.PlanName == planName);
        }

        public List<ClientPlanHDR> GetOpenEnrollmentPlansByClient(int companyId, string clientId)
        {
            using (DnetEntities db = new DnetEntities())
            {
                return db.ClientPlanHDRs
                .Where(c => c.CompanyID == companyId &&
                            c.ClientID == clientId &&
                            c.ACTIVE &&
                            c.OpenEnrollment)
                .ToList();
            }
        }
        public List<ClientPlanHDR> GetOpenEnrollmentPlansByClientAndYear(int companyId, string clientId, short year)
        {
            var result = _dbContext.ClientPlanHDRs.Where(cph => cph.CompanyID == companyId && cph.ClientID == clientId && cph.ACTIVE && cph.OpenEnrollment && cph.YEAR1 == year).ToList();
            return result;

        }

        public List<ClientPlanHDR> GetOpenEnrollmentPlans()
        {
            return _dbContext.ClientPlanHDRs
                .Where(c => c.ACTIVE &&
                            c.OpenEnrollment)
                .ToList();
        }

        public short? GetNewHirePlanYear(int companyId, string clientId, short index, out short datetype)
        {
            datetype = 0;
            short? result = null;
            ClientPlanHDR plan = null;
            var plans = _dbContext.ClientPlanHDRs
                .OrderByDescending(p => p.YEAR1)
                .Where(p => p.CompanyID == companyId &&
                            p.ClientID == clientId &&
                            p.ACTIVE &&
                            p.OpenEnrollment &&
                            p.StartDate <= DateTime.Today &&
                            p.EndDate >= DateTime.Today).ToList();
            if (plans.Any())
            {
                switch(index)
                {
                    case 1:
                        result = plans.OrderByDescending(p => p.YEAR1).FirstOrDefault(p => p.PlanDatesHireDateStartDate != 0)?.YEAR1;
                        datetype = 1;
                        break;
                    case 2:
                        result = plans.OrderByDescending(p => p.YEAR1).FirstOrDefault(p => p.PlanDatesHireDateStartDate == 0)?.YEAR1;
                        datetype = 2;
                        break;
                    case 3:
                        plan = plans.OrderByDescending(p => p.YEAR1).FirstOrDefault();
                        if (plan != null)
                        {
                            result = plan.YEAR1;
                            datetype = plan.PlanDatesHireDateStartDate != 0 ? (short)1 : (short)2;
                        }
                        break;
                    case 4:
                        result = plans.OrderByDescending(p => p.YEAR1).FirstOrDefault(p => p.PlanDatesBasedDateOfHire)?.YEAR1;
                        datetype = 3;
                        break;
                    case 5:
                        plan = plans.OrderByDescending(p => p.YEAR1).FirstOrDefault(p => p.PlanDatesHireDateStartDate != 0 || p.PlanDatesBasedDateOfHire);
                        if (plan != null)
                        {
                            result = plan.YEAR1;
                            datetype = plan.PlanDatesHireDateStartDate != 0 ? (short)1 : (short)3;
                        }
                        break;
                    case 6:
                        plan = plans.OrderByDescending(p => p.YEAR1).FirstOrDefault(p => p.PlanDatesHireDateStartDate == 0);
                        if (plan != null)
                        {
                            result = plan.YEAR1;
                            datetype = plan.PlanDatesBasedDateOfHire ? (short)3 : (short)2;
                        }
                        break;
                    case 7:
                        plan = plans.OrderByDescending(p => p.YEAR1).FirstOrDefault();
                        if (plan != null)
                        {
                            result = plan.YEAR1;
                            datetype = plan.PlanDatesHireDateStartDate != 0 ? (short)1 : plan.PlanDatesBasedDateOfHire ? (short)3 : (short)2;
                        }
                        break;
                }
                if (result == null) datetype = 0;
            }
            return result;
        }

        public bool OpenEnrollmentAvailable(int companyid, string clientId)
        {
            var date = DateTime.Today;

            return _dbContext.ClientPlanHDRs
                .Any(cph => cph.CompanyID == companyid &&
                            cph.ClientID == clientId &&
                            cph.ACTIVE &&
                            cph.BeginEnrollment <= date &&
                            cph.EndEnrollment >= date &&
                            cph.OpenEnrollment);
        }

        public bool LifeEventPlansAvailable(int companyId, string clientId, bool[] levents)
        {
            var date = DateTime.Today;

            IEnumerable<ClientPlanHDR> result = _dbContext.ClientPlanHDRs
                .Where(c => c.CompanyID == companyId &&
                          c.ClientID == clientId &&
                          c.ACTIVE &&
                          c.StartDate.Value <= date &&
                          c.EndDate.Value >= date &&
                          c.OpenEnrollment);
            return AvailablePlansForLifeEvent(result, levents).Any();
        }

        public DateTime? OEEndDate(int companyId, string clientId)
        {
            var date = DateTime.Today;
            return _dbContext.ClientPlanHDRs
                .Where(c => c.CompanyID == companyId &&
                            c.ClientID == clientId &&
                            c.ACTIVE &&
                            c.BeginEnrollment <= date &&
                            c.EndEnrollment >= date &&
                            c.OpenEnrollment)
                .OrderByDescending(c => c.EndEnrollment)
                .FirstOrDefault()?
                .EndEnrollment ?? null;
        }

        public DateTime? OEStartDate(int companyId, string clientId)
        {
            var date = DateTime.Today;
            return _dbContext.ClientPlanHDRs
                .Where(c => c.CompanyID == companyId &&
                            c.ClientID == clientId &&
                            c.ACTIVE &&
                            c.BeginEnrollment <= date &&
                            c.EndEnrollment >= date &&
                            c.OpenEnrollment)
                .OrderBy(c => c.BeginEnrollment)
                .FirstOrDefault()?
                .BeginEnrollment ?? null;
        }

        public void OEDates(int companyId, string clientId, short currentPlanYear, out DateTime? oeStartDate, out DateTime? oeEndDate)
        {
            var date = DateTime.Today;
            var plans = new List<ClientPlanHDR>();
            if (GlobalVariables.AdminEvent == "AdminEvent")
            {
                plans = _dbContext.ClientPlanHDRs
                    .Where(c => c.CompanyID == companyId &&
                                c.ClientID == clientId &&
                                c.ACTIVE &&
                                c.YEAR1 == currentPlanYear &&
                                c.OpenEnrollment)
                    .ToList();
            }
            else
            {
                plans = _dbContext.ClientPlanHDRs
                .Where(c => c.CompanyID == companyId &&
                            c.ClientID == clientId &&
                            c.ACTIVE &&
                            c.BeginEnrollment <= date &&
                            c.EndEnrollment >= date &&
                            c.OpenEnrollment)
                .ToList();
            }
            oeStartDate = plans.OrderBy(c => c.BeginEnrollment).FirstOrDefault()?.BeginEnrollment ?? null;
            oeEndDate = plans.OrderByDescending(c => c.EndEnrollment).FirstOrDefault()?.EndEnrollment ?? null;
        }

        public IEnumerable<ClientPlanHDR> GetAllPlans(int companyId, string oeClientId, string enrollmentType, bool[] levents = null)
        {
            var date = DateTime.Today;
            if (enrollmentType == Constants.EmployeeEnrollmentStatusEnrollmentTypes.Open)
            {
                return _dbContext.ClientPlanHDRs
                    .Where(x => x.CompanyID == companyId &&
                                x.ClientID == oeClientId &&
                                (DateTime)x.BeginEnrollment <= DateTime.Today &&
                                (DateTime)x.EndEnrollment >= DateTime.Today &&
                                x.ACTIVE &&
                                x.OpenEnrollment);
            }
            else
            {
                IEnumerable<ClientPlanHDR> result = _dbContext.ClientPlanHDRs
                     .Where(x => x.CompanyID == companyId &&
                                x.ClientID == oeClientId &&
                                (DateTime)x.StartDate <= DateTime.Today &&
                                (DateTime)x.EndDate >= DateTime.Today &&
                                x.ACTIVE &&
                                x.OpenEnrollment);
                if (enrollmentType == Constants.EmployeeEnrollmentStatusEnrollmentTypes.LifeEvent && levents != null) result = AvailablePlansForLifeEvent(result, levents);
                return result;
            }
        }
        public int GetMayYearFromClient(string clientID)
        {
            return _dbContext.ClientPlanHDRs.Where(client => client.ClientID == clientID).OrderByDescending(year => year.YEAR1).Select(year => year.YEAR1).FirstOrDefault();
        }
        #endregion
        public IEnumerable<ClientPlanHDR> AvailablePlansForLifeEvent(IEnumerable<ClientPlanHDR> plans, bool[] levents)
        {
            IEnumerable<ClientPlanHDR> result = plans;
            if (levents != null)
            {
                if (levents[LifeEventIdx.Adoption]) result = result = result.Where(x => x.LifeEventAdoption);
                if (levents[LifeEventIdx.ChildBirth]) result = result = result.Where(x => x.LifeEventChildBirth);
                if (levents[LifeEventIdx.ChildDeath]) result = result = result.Where(x => x.LifeEventChildDeath);
                if (levents[LifeEventIdx.CoverageLoss]) result = result = result.Where(x => x.LifeEventCoverageLoss);
                if (levents[LifeEventIdx.Death]) result = result = result.Where(x => x.LifeEventEEDeath);
                if (levents[LifeEventIdx.Divorce]) result = result = result.Where(x => x.LifeEventDivorce);
                if (levents[LifeEventIdx.Marriage]) result = result = result.Where(x => x.LifeEventMarriage);
                if (levents[LifeEventIdx.SpouseStatusChange]) result = result.Where(x => x.LifeEventSPStatusChg);
                if (levents[LifeEventIdx.StatusChange]) result = result = result.Where(x => x.LifeEventEEStatusChg);
            }
            return result;
        }
        public int GetPlanYearByEffectiveDate(string client, DateTime employeeEffectiveDate)
        {
            var year = _dbContext.ClientPlanHDRs.Where(a => a.ClientID == client
            && a.OpenEnrollment == true
            && a.ACTIVE == true
            && a.EffectiveStartDate <= employeeEffectiveDate
            && a.EffectiveEndDate >= employeeEffectiveDate)
                .OrderByDescending(a => a.YEAR1)
                .Select(a => a.YEAR1)
                .FirstOrDefault();

            return year;
        }
        public int GetActiveOpenEnrollmentCount(string client, int clientPlanHDRYear)
        {
            return _dbContext.ClientPlanHDRs.Count(a =>
                a.ClientID == client &&
                a.OpenEnrollment &&
                a.ACTIVE &&
                a.BeginEnrollment <= DateTime.Now &&
                a.EndEnrollment >= DateTime.Now &&
                a.YEAR1 == clientPlanHDRYear);
        }
        public List<ClientPlanHDR> GetClientActivQleAllowedPlanTypes(List<string> validPlanTypes, string client, int yearMax,  DateTime benefitEffDate)
        {
            return _dbContext.ClientPlanHDRs
            .Where(cp => cp.ClientID == client
                        && cp.OpenEnrollment
                        && cp.ACTIVE
                        && cp.YEAR1 == yearMax
                        && cp.EffectiveStartDate <= benefitEffDate
                        && cp.EffectiveEndDate >= benefitEffDate
                        && validPlanTypes.Contains(cp.PlanType))
            .ToList();
        }

        public List<ClientPlanHDR> GetActiveOpenEnrollmentClientPlans(string clientID, int year)
        {
            return _dbContext.ClientPlanHDRs
                .Where(cph => cph.ClientID == clientID
                    && cph.OpenEnrollment
                    && cph.ACTIVE
                    && cph.YEAR1 == year)
                .ToList();
        }


    }
}