using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Interfaces.Models;
using DocumentFormat.OpenXml.ExtendedProperties;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Thinkware.Pay360.PayrollProfile
{
    public class GetPayrollProfileNotesPaged : IThinkwareFunc<TableQueryInfo<PayrollProfileNoteModel>>
    {
        private readonly int _companyId;
        private readonly string _clientId;
        private readonly string _clientName;
        private readonly string _profileId;
        private readonly string _noteType;
        private readonly string _payrollNumber;
        private readonly TableFilter _filters;
        private readonly IFilterBuilder<PayrollProfileNoteModel> _filterBuilder;
        private readonly ISortBuilder<PayrollProfileNoteModel> _sortBuilder;
        private readonly DnetEntities _dbContext;

        public GetPayrollProfileNotesPaged(int companyId, string clientId, string clientName, string profileId, string noteType, string payrollNumber,
            TableFilter filters,
            IFilterBuilder<PayrollProfileNoteModel> filterBuilder,
            ISortBuilder<PayrollProfileNoteModel> sortBuilder,
            DnetEntities dbContext)
        {
            _companyId = companyId;
            _clientId = clientId;
            _clientName = clientName;
            _profileId = profileId;
            _noteType = noteType;
            _payrollNumber = payrollNumber;
            _filters = filters ?? throw new ArgumentNullException(nameof(filters));
            _filterBuilder = filterBuilder ?? throw new ArgumentNullException(nameof(filterBuilder));
            _sortBuilder = sortBuilder ?? throw new ArgumentNullException(nameof(sortBuilder));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        public bool CanExecute()
        {
            switch (_noteType)
            {
                case Constants.PayrollProfileNoteTypes.INVOICE:
                case Constants.PayrollProfileNoteTypes.PAYROLL:
                    break;

                default:
                    throw new ApplicationException("Invalid 'Note Type'.");
            }

            return true;
        }

        public TableQueryInfo<PayrollProfileNoteModel> Execute()
        {
            IEnumerable<Employee> employees = null;
            if (_noteType == Constants.PayrollProfileNoteTypes.PAYROLL)
            {
                employees = _dbContext.Employees.Where(x => x.CompanyID == _companyId && x.ClientID == _clientId)
                    .AsEnumerable()
                    .Select(x => new Employee()
                    {
                        EmployeeID = x.EmployeeID,
                        FirstName = x.FirstName,
                        LastName = x.LastName
                    })
                    .ToList();
            }

            List<PayrollProfileNoteModel> nonFilteredList = _dbContext.PayrollProfileNotes
                .Where(x =>
                    x.CompanyID == _companyId &&
                    x.ClientID == _clientId &&
                    x.ProfileID == _profileId &&
                    x.NoteType == _noteType)
                .Select(x => new PayrollProfileNoteModel()
                {
                    CompanyID = x.CompanyID,
                    ClientID = x.ClientID,
                    ProfileID = x.ProfileID,
                    NoteID = x.NoteID,
                    NoteType = x.NoteType,
                    EmployeeID = x.EmployeeID,
                    Subject = x.Subject,
                    FromDate = x.FromDate,
                    ToDate = x.ToDate,
                    Completed = x.Completed,
                    Notes = x.Notes,
                    Name = null, // We set this value below, because unable to set Name using extension function inside LINQ-to-SQL.
                    ClientName = _clientName,
                    PayrollNumber = x.PayrollNumber,
                    CreatedBy = x.CreatedBy,
                    LastUpdated = x.LastUpdated,
                    ShowClient = x.ShowClient,
                })
                .ToList();

            if (GlobalVariables.CurrentUser.ClientLevelEnabled)
            {
                nonFilteredList = nonFilteredList.Where(x => x.ShowClient).ToList();
            }

            if (_payrollNumber.IsNotNullOrEmpty() && nonFilteredList.Count > 0)
            {
                nonFilteredList = nonFilteredList.Where(x => x.PayrollNumber == _payrollNumber).ToList();
            }

            if (_noteType == Constants.PayrollProfileNoteTypes.PAYROLL)
            {
                foreach (var note in nonFilteredList)
                {
                    if (string.IsNullOrWhiteSpace(note.EmployeeID))
                        continue;

                    note.Name = employees.GetFirstLastName(note.EmployeeID);
                }
            }

            Func<PayrollProfileNoteModel, bool> filterClause = _filterBuilder.BuildFilterClause(_filters.Filters).Compile();
            List<PayrollProfileNoteModel> filteredList = nonFilteredList.Where(filterClause).OrderBy(x => x.EmployeeID)
                .Select(x => new PayrollProfileNoteModel()
                {
                    CompanyID = x.CompanyID,
                    ClientID = x.ClientID,
                    ProfileID = x.ProfileID,
                    NoteID = x.NoteID,
                    NoteType = x.NoteType,
                    EmployeeID = x.EmployeeID,
                    Subject = x.Subject,
                    FromDate = x.FromDate,
                    ToDate = x.ToDate,
                    Completed = x.Completed,
                    Notes = x.Notes,
                    Name = x.Name,
                    ClientName = x.ClientName,
                    PayrollNumber = x.PayrollNumber,
                    CreatedBy = x.CreatedBy,
                    LastUpdated = x.LastUpdated
                })
                .ToList();

            return new TableQueryInfo<PayrollProfileNoteModel>
            {
                Query = _sortBuilder.Sort(filteredList.AsQueryable(), _filters.Sort).Skip(_filters.EntriesToSkip).Take(_filters.PerPage).ToList().AsQueryable(),
                TotalEntries = nonFilteredList.Count(),
                FilteredEntries = filteredList.Count()
            };
        }
    }
}