using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Interfaces.Services;
using DarwiNet2._0.Services;
using Microsoft.AspNet.SignalR.Infrastructure;
using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using Thinkware.Cohesion.Handlers;
using Thinkware.Pay360.Messaging;
using Thinkware.Pay360.Payroll;
using Kendo.Mvc.Extensions;
using DataDrivenViewEngine.Models.Core;
using System.Web.Mail;

namespace Thinkware.Cohesion.Payroll
{
    public class SubmitInvoiceApprovalRequestHandler : IHandler<SubmitInvoiceApprovalRequest>
    {

        #region Fields

        private readonly DnetEntities _dbContext;
        private readonly string _dnetUrl = "";
        protected string _connectionString;

        // SMTP Mail Properties
        private readonly ProjectSetupDTO _projectSetup;
        private readonly string _fromAddress;
        private readonly string _fromName;
        private readonly string _smtpUsername;
        private readonly string _smtpPassword;
        private readonly string _host;
        private readonly int _port;

        // SmartTag Properties
        public string Approver { get; private set; }
        public SubmitInvoiceApprovalRequestHandler WithApprover(string approver)
        {
            Approver = approver;

            return this;
        }


        public string RejectionType { get; private set; }
        public string RejectedByUser { get; private set; }
        public string RejectReason { get; private set; }


        public SubmitInvoiceApprovalRequestHandler ForRejection(string rejectionType, string rejectedByUser, string rejectReason)
        {
            RejectionType = rejectionType;
            RejectedByUser = rejectedByUser;
            RejectReason = rejectReason;

            return this;
        }

        #endregion

        #region Constructors

        public SubmitInvoiceApprovalRequestHandler(DnetEntities dbContext,
                                                   string dnetUrl = "")
        {
            _dbContext = dbContext;

            string dbDnetUrl = _dbContext.ProjectSetups.FirstOrDefault().DnetAddress;
            _dnetUrl = string.IsNullOrEmpty(dnetUrl) ? dbDnetUrl : dnetUrl;

            var getDB = new GetDB();
            _connectionString = getDB.GetConnection();

            _projectSetup = _dbContext.ProjectSetups
                .Select(s => new ProjectSetupDTO
                {
                    DnetMailHost = s.DnetMailHost,
                    DnetMailSSL = s.DnetMailSSL,
                    DnetMailFromName = s.DnetMailFromName,
                    DnetMailFromAddress = s.DnetMailFromAddress,
                    DnetMailUser = s.DnetMailUser,
                    DnetMailPassword = s.DnetMailPassword,
                    UseCustomMail = s.UseCustomMail,
                    CustMailHost = s.CustMailHost,
                    CustHostLogin = s.CustHostLogin,
                    CustHostPWD = s.CustHostPWD,
                    CustMailFromName = s.CustMailFromName,
                    CustMailFromAddress = s.CustMailFromAddress,
                    CustMailPort = s.CustMailPort
                })
                .FirstOrDefault();

            if (!_projectSetup.UseCustomMail)
            {
                _fromAddress = _projectSetup.DnetMailFromAddress;
                _fromName = _projectSetup.DnetMailFromName;
                _smtpUsername = _projectSetup.DnetMailUser.Trim();
                _smtpPassword = _projectSetup.DnetMailPassword.Trim();
                _host = _projectSetup.DnetMailHost.Trim();
                _port = 587;
            }
            else
            {
                _fromAddress = _projectSetup.CustMailFromAddress;
                _fromName = _projectSetup.CustMailFromName;
                _smtpUsername = _projectSetup.CustHostLogin.Trim();
                _smtpPassword = _projectSetup.CustHostPWD.Trim();
                _host = _projectSetup.CustMailHost.Trim();
                _port = int.TryParse(_projectSetup.CustMailPort, out int customPort) ? customPort : 587;
            }
        }

        #endregion

        public void Handle(SubmitInvoiceApprovalRequest request)
        {
            var invoicePayroll = _dbContext.InvoicePayrolls
                .FirstOrDefault(x =>
                    x.CompanyID == request.CompanyID &&
                    x.ClientID == request.ClientID &&
                    x.DarwinInvoiceNumber == request.InvoiceNumber);
            PayrollNumber oPayrollNumber = PayrollNumber.Parse(invoicePayroll.PayrollNumber);
            string snapshotId = _dbContext.PayrollSnapshots
                .FirstOrDefault(x => x.PayrollNumber == invoicePayroll.PayrollNumber)?
                .SnapshotID;
            InitiateInvoiceApprovalsNotifications(oPayrollNumber, request.InvoiceNumber, request.ApprovalType, snapshotId);
            new UpdatePayrollStatusHandler(_dbContext)
                .Handle(oPayrollNumber, PayrollStatusDefinition.Parse(PayrollStatus.InvoiceWaitingApproval), request.InvoiceNumber);
        }

        #region Private Methods

        private void InitiateInvoiceApprovalsNotifications(PayrollNumber oPayrollNumber, int invoiceNumber, string approvalType, string snapshotId)
        {
            var companyID = oPayrollNumber.CompanyId;
            var clientID = oPayrollNumber.ClientId;
            var profileID = oPayrollNumber.ProfileId;
            var payrollNumber = oPayrollNumber.ToString();

            ResetInvoicePreviewStatus(companyID, clientID, invoiceNumber);
            var invoiceApprovalSetups = GetInvoiceApprovalSetups(payrollNumber, invoiceNumber, approvalType);
            var invoiceApprovalSetupIds = invoiceApprovalSetups.Select(x => x.InvoiceApprovalSetupID).ToList();
            var nextInvoiceApprovalSetupSequences = GetNextInvoiceApprovalSetupSequences(invoiceApprovalSetupIds, companyID, clientID, invoiceNumber);

            if (nextInvoiceApprovalSetupSequences.Any())
            {
                foreach (var invoiceApprovalSetup in invoiceApprovalSetups)
                {
                    var invoiceApprovalSetupSequences = nextInvoiceApprovalSetupSequences.Where(x => x.InvoiceApprovalSetupID == invoiceApprovalSetup.InvoiceApprovalSetupID && x.InvoiceApprovalRecipient.RecipientID != null);
                    var pendingInvoiceApprovalExists = invoiceApprovalSetupSequences.Where(x => x.InvoiceApprovals.Any(a => a.CompanyID == companyID && a.ClientID == clientID && a.InvoiceNumber == invoiceNumber && !a.Approved && !a.Rejected)).Any();
                    var newApprovalInvoiceApprovalSetupSequenceIds = invoiceApprovalSetupSequences.Select(x => x.InvoiceApprovalSetupSequenceID);

                    if (newApprovalInvoiceApprovalSetupSequenceIds.Any() && !pendingInvoiceApprovalExists)
                    {
                        CreateInvoiceApprovals(companyID, clientID, invoiceNumber, newApprovalInvoiceApprovalSetupSequenceIds);
                        CreateApprovalUserToDoItems(companyID, clientID, invoiceNumber, newApprovalInvoiceApprovalSetupSequenceIds);
                    }
                    CreateInvoiceApprovalEmail(companyID, clientID, invoiceNumber, payrollNumber, snapshotId, invoiceApprovalSetup, invoiceApprovalSetupSequences);
                }
            }
        }

        public List<InvoiceApprovalSetup> GetInvoiceApprovalSetups(string payrollNumber, int invoiceNumber, string approvalType)
        {
            return _dbContext.InvoicePayrolls
                .Join(_dbContext.InvoiceApprovalSetups,
                    ip => new { ip.CompanyID, ip.ClientID, ip.ProfileID },
                    ias => new { ias.CompanyID, ias.ClientID, ias.ProfileID },
                    (ip, ias) => new { ip, ias })
                .Where(x =>
                    x.ip.PayrollNumber == payrollNumber &&
                    x.ip.DarwinInvoiceNumber == invoiceNumber &&
                    x.ias.ApprovalType == approvalType)
                .Select(x => x.ias)
                .ToList();
        }

        private IEnumerable<InvoiceApprovalSetupSequence> GetNextInvoiceApprovalSetupSequences(List<int> invoiceApprovalSetupIds, int companyID, string clientID, int invoiceNumber)
        {
            List<InvoiceApproval> invoiceApprovals = GetInvoiceApprovals(invoiceApprovalSetupIds, companyID, clientID, invoiceNumber);

            List<InvoiceApprovalSetupSequence> nextInvoiceApprovalSetupSequences = new List<InvoiceApprovalSetupSequence>();
            foreach (var invoiceApprovalSetupId in invoiceApprovalSetupIds)
            {
                var setupInvoiceApprovals = invoiceApprovals.Where(a => a.InvoiceApprovalSetupSequence.InvoiceApprovalSetupID == invoiceApprovalSetupId);

                int nextInvoiceApprovalSetupSequenceNumber = GetNextInvoiceApprovalSetupSequenceNumber(invoiceApprovalSetupId, setupInvoiceApprovals);

                var invoiceApprovalSetup = _dbContext.InvoiceApprovalSetups.FirstOrDefault(x => x.InvoiceApprovalSetupID == invoiceApprovalSetupId);

                bool hasParallelApproval = false;
                if (invoiceApprovalSetup.ApprovalModel == InvoiceApprovalModel.Parallel.ToString())
                {
                    hasParallelApproval = invoiceApprovals.Any(x => x.Approved &&
                                                                    x.InvoiceApprovalSetupSequence.InvoiceApprovalSetup.ApprovalModel == InvoiceApprovalModel.Parallel.ToString());
                }

                if (nextInvoiceApprovalSetupSequenceNumber > -1 && !hasParallelApproval)
                {
                    var invoiceApprovalSetupSequences = _dbContext.InvoiceApprovalSetupSequences
                        .Where(s => s.InvoiceApprovalSetupID == invoiceApprovalSetupId &&
                                    !s.InvoiceApprovalRecipient.Inactive &&
                                    s.Sequence == nextInvoiceApprovalSetupSequenceNumber);

                    nextInvoiceApprovalSetupSequences.AddRange(invoiceApprovalSetupSequences);
                }
            }

            return nextInvoiceApprovalSetupSequences;
        }

        private List<InvoiceApproval> GetInvoiceApprovals(List<int> invoiceApprovalSetupIds, int companyID, string clientID, int invoiceNumber)
        {
            var invoiceApprovals = _dbContext.InvoiceApprovals
                .Where(a => invoiceApprovalSetupIds.Contains(a.InvoiceApprovalSetupSequence.InvoiceApprovalSetupID) &&
                            a.CompanyID == companyID &&
                            a.ClientID == clientID &&
                            a.InvoiceNumber == invoiceNumber &&
                            !a.Inactive);

            List<InvoiceApproval> maxIterationApprovals = new List<InvoiceApproval>();
            foreach (var approval in invoiceApprovals)
            {
                int maxIteration = _dbContext.InvoiceApprovals
                    .Where(x => x.CompanyID == approval.CompanyID && 
                                x.ClientID == approval.ClientID && 
                                x.InvoiceNumber == approval.InvoiceNumber)
                    .Max(x => x.Iteration);

                if (approval.Iteration == maxIteration) maxIterationApprovals.Add(approval);
            }

            return maxIterationApprovals;
        }

        private int GetNextInvoiceApprovalSetupSequenceNumber(int invoiceApprovalSetupId, IEnumerable<InvoiceApproval> invoiceApprovals)
        {
            var sequences = _dbContext.InvoiceApprovalSetupSequences
                .Where(x => x.InvoiceApprovalSetupID == invoiceApprovalSetupId &&
                            (
                                (
                                    x.InvoiceApprovalSetup.ApprovalModel == InvoiceApprovalModel.GroupSequential.ToString() &&
                                    x.InvoiceApprovalRecipient.GroupID != null &&
                                    x.InvoiceApprovalRecipient.RecipientID != null
                                ) ||
                                (
                                    x.InvoiceApprovalSetup.ApprovalModel != InvoiceApprovalModel.GroupSequential.ToString() &&
                                    x.InvoiceApprovalRecipient.GroupID == null
                                )
                            ) &&
                            x.InvoiceApprovalRecipient.Inactive == false).ToList();

            if (invoiceApprovals.Any())
            {
                var invoiceApprovalSetupSequenceIds = invoiceApprovals.Select(a => a.InvoiceApprovalSetupSequenceID).ToList();
                var filteredSequences = sequences
                    .Where(x => !invoiceApprovalSetupSequenceIds.Contains(x.InvoiceApprovalSetupSequenceID));
                if (filteredSequences.Any() && !invoiceApprovals.Any(a => !a.Approved && !a.Rejected))
                {
                    return filteredSequences.Min(x => x.Sequence);
                }
                else if (invoiceApprovalSetupSequenceIds.Any())
                {
                    return -1;
                }
            }

            else if (sequences.Any())
            {
                return sequences.Min(x => x.Sequence);
            }

            return -1;
        }

        private void CreateInvoiceApprovals(int companyID, string clientID, int invoiceNumber, IEnumerable<int> invoiceApprovalSetupSequenceIDs)
        {
            List<InvoiceApproval> invoiceApprovals = invoiceApprovalSetupSequenceIDs
                .Select(a =>
                {
                    int iteration = GetNextIterationValue(companyID, clientID, invoiceNumber, a);
                    return new InvoiceApproval
                    {
                        CompanyID = companyID,
                        ClientID = clientID,
                        InvoiceNumber = invoiceNumber,
                        InvoiceApprovalSetupSequenceID = a,
                        Created = DateTime.Now,
                        Updated = DateTime.Now,
                        DateSubmittedForApproval = DateTime.Now,
                        Iteration = iteration
                    };
                }).ToList();
            _dbContext.InvoiceApprovals.AddRange(invoiceApprovals);
            _dbContext.SaveChanges();
        }

        private int GetNextIterationValue(int companyID, string clientID, int invoiceNumber, int invoiceApprovalSetupSequenceID)
        {
            List<InvoiceApproval> invoiceApprovals = _dbContext.InvoiceApprovals.Where(x => x.CompanyID == companyID &&
                                                                                            x.ClientID == clientID &&
                                                                                            x.InvoiceNumber == invoiceNumber).ToList();
            int iteration = 1;
            if (!IsInvoiceApprovalSequenceComplete(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequenceID) && invoiceApprovals.Any())
            {
                iteration = invoiceApprovals.Max(x => x.Iteration);
            }
            else if (invoiceApprovals.Any())
            {
                iteration = invoiceApprovals.Max(x => x.Iteration) + 1;
            }
            return iteration;
        }

        private void CreateInvoiceApprovalEmail(int companyID, string clientID, int invoiceNumber, string payrollNumber, string snapshotId, InvoiceApprovalSetup invoiceApprovalSetup, IEnumerable<InvoiceApprovalSetupSequence> invoiceApprovalSetupSequences)
        {
            string clientName = GetClientName(companyID, clientID);
            if (clientName == null)
            {
                clientName = "";
            }
            else
            {
                clientName = " - " + clientName;
            }
            var profileIdAndCheckDate = GetProfileIdAndCheckDate(companyID, clientID, invoiceNumber);
            var emailSubject = $"Invoice Approval Request: {clientID}{clientName} - {profileIdAndCheckDate.Item1} - {profileIdAndCheckDate.Item2} - Invoice {invoiceNumber}";
            var emailBody = FillSmartTags(invoiceNumber, payrollNumber, invoiceApprovalSetup);
            var approvalExpirationHours = invoiceApprovalSetup.ApprovalExpirationHours ?? 0;
            var emailMessage = emailBody + BuildInvoiceApprovalEmailMessage(approvalExpirationHours);
            List<SMTPMessage> messages = new List<SMTPMessage>();
            foreach (var invoiceApprovalSetupSequence in invoiceApprovalSetupSequences)
            {
                bool hasActiveApprovalEmailToken = HasActiveInvoiceApprovalEmailToken(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequence.InvoiceApprovalSetupSequenceID);
                if (invoiceApprovalSetupSequence.InvoiceApprovalRecipient.RecipientID != null && !string.IsNullOrEmpty(invoiceApprovalSetupSequence.InvoiceApprovalRecipient.User.Email) && !hasActiveApprovalEmailToken)
                {
                    var recipient = new UserModel
                    {
                        UserID = invoiceApprovalSetupSequence.InvoiceApprovalRecipient.RecipientID,
                        Name = invoiceApprovalSetupSequence.InvoiceApprovalRecipient.User.Name,
                        Email = invoiceApprovalSetupSequence.InvoiceApprovalRecipient.User.Email
                    };

                    string uriParameterString = BuildUriParameterString(companyID, clientID, invoiceNumber, invoiceApprovalSetupSequence.InvoiceApprovalSetupSequenceID, approvalExpirationHours);
                    var emailMessageWithApproveLink = AppendInvoiceApprovalUrl(emailMessage, uriParameterString);
                    var message = CreateSMTPMessage(recipient, emailSubject, emailMessageWithApproveLink);
                    var expiraton = (double)invoiceApprovalSetup.ApprovalExpirationHours;
                    message.Expiration = approvalExpirationHours > 0 ? DateTime.Now.AddHours(expiraton) : DateTime.MaxValue;
                    messages.Add(message);
                }
            }
            SendSMTPMail(messages, payrollNumber, snapshotId);
        }

        private string GetClientName(int companyID, string clientID)
        {
            return _dbContext.Clients
                        .FirstOrDefault(c =>
                            c.ClientID == clientID &&
                            c.CompanyID == companyID)
                        ?.ClientName;
        }

        private Tuple<string, string> GetProfileIdAndCheckDate(int companyID, string clientID, int darwinInvoiceNumber)
        {
            var invoicePayroll = _dbContext.InvoicePayrolls
                .FirstOrDefault(p => p.CompanyID == companyID &&
                                     p.ClientID == clientID &&
                                     p.DarwinInvoiceNumber == darwinInvoiceNumber);
            var clientPayrollSchedule = _dbContext.ClientPayrollSchedules
                .FirstOrDefault(s => s.PayrollNumber == invoicePayroll.PayrollNumber);
            DateTime checkDate = (DateTime)clientPayrollSchedule.CheckDate;
            return new Tuple<string, string>(clientPayrollSchedule.ProfileID, checkDate.ToString("d"));
        }

        private string FillSmartTags(int invoiceNumber, string payrollNumber, InvoiceApprovalSetup invoiceApprovalSetup)
        {
            int companyId = invoiceApprovalSetup.CompanyID;
            string clientId = invoiceApprovalSetup.ClientID;
            string profileId = invoiceApprovalSetup.ProfileID;
            string emailMessage = invoiceApprovalSetup.EmailMessage ?? "";

            if (!string.IsNullOrWhiteSpace(emailMessage))
            {
                return FillSmartTags(companyId, clientId, profileId, invoiceNumber, payrollNumber, emailMessage);
            }

            return emailMessage;
        }

        private string FillSmartTags(int companyId, string clientId, string profileId, int invoiceNumber, string payrollNumber, string messageText)
        {
            // get position of all smart tags from emailMessage
            IEnumerable<int> smartTagPositions = Regex.Matches(messageText, Regex.Escape("[")).Cast<Match>().Select(m => m.Index);

            List<string> smartTags = new List<string>();
            foreach (var smartTagPosition in smartTagPositions)
            {
                string smartTag = messageText.Substring(smartTagPosition + 1, Regex.Match(messageText.Substring(smartTagPosition), Regex.Escape("]")).Index - 1);

                smartTags.Add(smartTag);
            }

            var smartTagData = GetSmartTagData(smartTags);
            foreach (SmartTagModel smartTag in smartTagData)
            {
                string value = GetSmartTagValue(smartTag, companyId, clientId, profileId, invoiceNumber, payrollNumber);
                value = FormatSmartTagValue(value, smartTag.DataType);
                messageText = InsertSmartTagValue(messageText, smartTag.Name, value);
            }

            return messageText;
        }

        private string FormatSmartTagValue(string value, string type)
        {
            if (type == "Currency")
            {
                if (Decimal.TryParse(value, out decimal decimalValue))
                {
                    return decimalValue.ToString("C");
                }
            }
            if (type == "Date")
            {
                if (DateTime.TryParse(value, out DateTime dateTimeValue))
                {
                    return dateTimeValue.ToString("d");
                }
            }
            return value;
        }

        private IEnumerable<SmartTagModel> GetSmartTagData(List<string> smartTags)
        {
            return _dbContext.SmartTags
                    .Where(t => smartTags.Contains(t.Name))
                    .Select(t => new SmartTagModel
                    {
                        Name = t.Name,
                        TableName = t.TableName,
                        ColumnName = t.ColumnName,
                        Text = t.Text,
                        QueryFilter = t.QueryFilter,
                        CompanyIDFilter = t.CompanyIDFilter,
                        ClientIDFilter = t.ClientIDFilter,
                        ProfileIDFilter = t.ProfileIDFilter,
                        PayrollNumberFilter = t.PayrollNumberFilter,
                        AddressCodeFilter = t.AddressCodeFilter,
                        DataType = t.DataType,
                    });
        }


        //SmartTagProvider
        private string GetSmartTagValue(SmartTagModel smartTag, int companyId, string clientId, string profileId, int invoiceNumber, string payrollNumber)
        {
            string value = "";

            if (smartTag.Text != null)
            {
                value = smartTag.Text;
            }
            else if (smartTag.TableName != null && smartTag.ColumnName != null)
            {
                value = GetDynamicValueByFilter(smartTag, companyId, clientId, profileId, invoiceNumber, payrollNumber);
            }
            else if (smartTag.Name == "InvoiceNumber")
            {
                value = invoiceNumber.ToString();
            }
            else if (smartTag.Name == "Approver")
            {
                value = Approver;
            }
            else if (smartTag.Name == "RejectionType")
            {
                value = RejectionType;
            }
            else if (smartTag.Name == "RejectedByUser")
            {
                value = RejectedByUser;
            }
            else if (smartTag.Name == "RejectReason")
            {
                value = RejectReason;
            }
            else if (smartTag.Name == "InvoiceReviewLink")
            {
                value = CreateInvoiceReviewLink(payrollNumber, invoiceNumber);
            }

            return value;
        }

        private string InsertSmartTagValue(string messageText, string smartTagName, string value)
        {
            if (smartTagName.IndexOf('[') == 0)
            {
                return messageText.Replace(smartTagName, value);
            }

            return messageText.Replace("[" + smartTagName + "]", value);
        }

        private string BuildInvoiceApprovalEmailMessage(int approvalTimeframeHours)
        {
            string expirationString = "<p>Click the link below to visit the review page for this invoice where you may approve or decline.</p>" +
                $"<p>This link includes a unique key that can only be used once and ";

            if (approvalTimeframeHours > 0)
            {
                expirationString += "expires after " + approvalTimeframeHours + " hour(s)." +
                "<p>If the link has expired, please contact a payroll specialist to request a new link.</p>";
            }
            else
            {
                expirationString += "does not expire.";
            }
            return expirationString;
        }

        private string BuildUriParameterString(int companyID, string clientID, int invoiceNumber, int invoiceApprovalSetupSequenceId, int approvalTimeframeHours)
        {
            var token = CreateInvoiceApprovalEmailToken(invoiceApprovalSetupSequenceId, companyID, clientID, invoiceNumber, approvalTimeframeHours);
            return $"companyID={companyID}&clientID={clientID}&invoiceNumber={invoiceNumber}&invoiceApprovalSetupSequenceId={invoiceApprovalSetupSequenceId}&emailToken={token}";
        }

        private string AppendInvoiceApprovalUrl(string emailMessage, string uriParameterString)
        {
            string host = _dnetUrl;
            if (!host.EndsWith("/"))
            {
                host = host + "/";
            }
            string invoiceApprovalRoute = "ApproveInvoice";
            string invoiceApprovalUri = $"{host}InvoiceApprovalByEmail/{invoiceApprovalRoute}?{uriParameterString}";

            return $"{emailMessage}" +
                "<p>" +
                $"<a href='{invoiceApprovalUri}'>Invoice Review</a>" +
                "</p>";
        }

        private string CreateInvoiceReviewLink(string payrollNumber, int invoiceNumber)
        {
            string host = _dnetUrl;
            if (!host.EndsWith("/"))
            {
                host = host + "/";
            }
            string invoiceReviewUri = $"{host}PayrollInvoicesReview/Review?payrollNumber={payrollNumber}&invoiceNumber={invoiceNumber}";

            return $"<a href='{invoiceReviewUri}'>Invoice Review</a>";
        }

        private string CreateInvoiceApprovalEmailToken(int invoiceApprovalSetupSequenceId, int companyID, string clientID, int invoiceNumber, int approvalTimeframeHours)
        {
            // disable any previous email tokens for this invoiceApprovalSetupSequenceId
            DisableEmailToken(invoiceApprovalSetupSequenceId);

            var token = Guid.NewGuid();

            var invoiceApprovalEmailToken = new InvoiceApprovalEmailToken
            {
                InvoiceApprovalSetupSequenceID = invoiceApprovalSetupSequenceId,
                CompanyID = companyID,
                ClientID = clientID,
                InvoiceNumber = invoiceNumber,
                Token = token.ToString(),
                DateCreated = DateTime.Now,
                Expires = DateTime.Now.AddHours(approvalTimeframeHours)
            };

            _dbContext.InvoiceApprovalEmailTokens.Add(invoiceApprovalEmailToken);
            _dbContext.SaveChanges();

            return token.ToString();
        }

        private void DisableEmailToken(int invoiceApprovalSetupSequenceId)
        {
            var tokensToDisable = _dbContext.InvoiceApprovalEmailTokens
            .Where(t => t.InvoiceApprovalSetupSequenceID == invoiceApprovalSetupSequenceId);

            if (tokensToDisable.Any())
            {
                foreach (var token in tokensToDisable)
                {
                    token.Expires = DateTime.Now;
                }
                _dbContext.SaveChanges();
            }
        }

        private SMTPMessage CreateSMTPMessage(UserModel recipient, string subject, string body)
        {
            body = InsertDnetUrl(body);

            if (RecipientDetails(body))
            {
                body = InsertRecipientDetails(body, recipient.UserID, recipient.Name);
            }

            var message = CreateMailMessage(recipient.Email, subject, body);

            return message;
        }

        private string InsertDnetUrl(string body)
        {
            if (Regex.Match(body, @"\[DNetURL\]").Success)
            {
                body = InsertDnetUrl(body, _dnetUrl);
            }
            return body;
        }

        private bool RecipientDetails(string body)
        {
            return (Regex.Match(body, @"\[RecipientName\]").Success ||
                    Regex.Match(body, @"\[RecipientUserID\]").Success);
        }

        private string InsertDnetUrl(string messageText, string dnetUrl)
        {
            return InsertSmartTagValue(messageText, "DNetURL", dnetUrl);
        }

        private string InsertRecipientDetails(string messageText, string recipientUserId, string recipientName)
        {
            messageText = InsertSmartTagValue(messageText, "RecipientName", recipientName);
            messageText = InsertSmartTagValue(messageText, "RecipientUserID", recipientUserId);

            return messageText;
        }

        private void SendSMTPMail(List<SMTPMessage> messages, string payrollNumber, string snapshotId)
        {
            var connectionString = _dbContext.Database.Connection.ConnectionString;
            new SMTPSendMailHandler().Handle(new SMTPSendMailRequest(messages, _smtpUsername, _smtpPassword, _host, _port, connectionString, payrollNumber, snapshotId));
        }

        private SMTPMessage CreateMailMessage(string toAddress, string subject, string body)
        {
            SMTPMessage message = new SMTPMessage();
            message.FromAddress = _fromAddress;
            message.FromName = _fromName;
            message.Subject = subject;
            message.Body = body;
            message.ToAddress = toAddress;
            message.InitialSendTimestamp = DateTime.Now;

            return message;
        }

        private string GetDynamicValueByFilter(SmartTagModel smartTag, int companyId, string clientId, string profileId, int invoiceNumber, string payrollNumber)
        {
            var parameters = new List<SqlParameter>();
            string sql = GetDynamicValueSqlByFilter(smartTag, companyId, clientId, profileId, invoiceNumber, payrollNumber, out parameters);

            if (parameters.Count == 0)
            {
                parameters = null;
            }
            var result = ExecuteScalar(sql, System.Data.CommandType.Text, parameters != null ? parameters.ToArray() : null);

            string value = "";
            if (result != null && !string.IsNullOrEmpty(result.ToString()))
            {
                Type type = GetTypeByProperty(smartTag.TableName, smartTag.ColumnName);
                value = CastValueByType(type, result);
            }

            return value.ToString();
        }

        private string GetDynamicValueSqlByFilter(SmartTagModel smartTag, int companyId, string clientId, string profileId, int invoiceNumber, string payrollNumber, out List<SqlParameter> parameters)
        {
            parameters = new List<SqlParameter>();

            // build sql statement
            string sql = $"SELECT {smartTag.ColumnName} FROM {smartTag.TableName}";

            if (smartTag.QueryFilter != null || smartTag.CompanyIDFilter || smartTag.ClientIDFilter || smartTag.ProfileIDFilter || smartTag.PayrollNumberFilter || smartTag.AddressCodeFilter)
            {
                sql += " WHERE";
                int filterCount = 0;

                if (!string.IsNullOrEmpty(smartTag.QueryFilter))
                {
                    sql += $" {smartTag.QueryFilter}";
                    filterCount++;
                }

                if (smartTag.CompanyIDFilter)
                {
                    sql += AppendFilter(filterCount, "CompanyID");
                    parameters.Add(new SqlParameter("CompanyID", companyId));
                    filterCount++;
                }

                if (smartTag.ClientIDFilter)
                {
                    sql += AppendFilter(filterCount, "ClientID");
                    parameters.Add(new SqlParameter("ClientID", clientId));
                    filterCount++;
                }

                if (smartTag.ProfileIDFilter)
                {
                    sql += AppendFilter(filterCount, "ProfileID");
                    parameters.Add(new SqlParameter("ProfileID", profileId));
                    filterCount++;
                }

                if (smartTag.InvoiceApprovalSmartTag)
                {
                    sql += AppendFilter(filterCount, "InvoiceNumber");
                    parameters.Add(new SqlParameter("InvoiceNumber", invoiceNumber));
                    filterCount++;
                }

                if (smartTag.PayrollNumberFilter)
                {
                    sql += AppendFilter(filterCount, "PayrollNumber");
                    parameters.Add(new SqlParameter("PayrollNumber", payrollNumber));
                    filterCount++;
                }

                if (smartTag.AddressCodeFilter)
                {
                    string addressCode = _dbContext.Clients.Where(c => c.CompanyID == companyId && c.ClientID == clientId).Select(c => c.AddressCode).FirstOrDefault();

                    sql += AppendFilter(filterCount, "AddressCode");
                    parameters.Add(new SqlParameter("AddressCode", addressCode));
                    //filterCount++;
                }
            }

            return sql;
        }

        private string AppendFilter(int filterCount, string fieldName)
        {
            string filter = "";

            if (filterCount > 0)
            {
                filter += " AND";
            }

            filter += $" {fieldName} = @{fieldName}";

            return filter;
        }

        private Type GetTypeByProperty(string tableName, string columnName)
        {
            if (columnName.StartsWith("COUNT") || columnName.StartsWith("SUM"))
            {
                return typeof(int);
            }

            // this can be done programatically with entity framework
            switch (tableName)
            {
                case "Companies":
                    return GetPropertyType<Company>(columnName);
                case "Clients":
                    return GetPropertyType<Client>(columnName);
                case "ClientPayrollSchedules":
                    return GetPropertyType<ClientPayrollSchedule>(columnName);
                case "ClientAddresses":
                    return GetPropertyType<ClientAddress>(columnName);
                case "PayrollWorkHeaders":
                    return GetPropertyType<PayrollWorkHeader>(columnName);
                case "InvoiceStatuses":
                    return GetPropertyType<InvoiceStatus>(columnName);
                default:
                    return typeof(string);
            }
        }

        private Type GetPropertyType<T>(string property)
        {
            Type Type = typeof(T);
            var propInfo = Type.GetProperty(property);
            return propInfo.PropertyType;
        }

        private string CastValueByType(Type type, dynamic value)
        {
            if (type == typeof(DateTime))
            {
                return (DateTime.Parse(value.ToString())).ToShortDateString();
            }

            return value.ToString();
        }

        private Object ExecuteScalar(string commandText, CommandType commandType, SqlParameter[] parameters)
        {
            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                using (SqlCommand command = new SqlCommand(commandText, connection))
                {
                    command.CommandType = commandType;

                    if (parameters != null && parameters.Length > 0)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    connection.Open();
                    return command.ExecuteScalar();
                }
            }
        }

        private bool IsInvoiceApprovalSequenceComplete(int companyID, string clientID, int invoiceNumber, int invoiceApprovalSetupSequenceId)
        {
            var invoiceApprovalSequence = _dbContext.InvoiceApprovalSetupSequences.FirstOrDefault(x => x.InvoiceApprovalSetupSequenceID == invoiceApprovalSetupSequenceId);

            var invoiceApprovalSetup = _dbContext.InvoiceApprovalSetups?
                .FirstOrDefault(s => s.InvoiceApprovalSetupID == invoiceApprovalSequence.InvoiceApprovalSetupID);
            var approvalsRequired = new List<InvoiceApprovalSetupSequence>();

            var approvalSetupIDs = _dbContext.InvoiceApprovalSetups
                .Where(x => x.CompanyID == invoiceApprovalSetup.CompanyID &&
                            x.ClientID == invoiceApprovalSetup.ClientID &&
                            x.ProfileID == invoiceApprovalSetup.ProfileID &&
                            x.ApprovalType == invoiceApprovalSetup.ApprovalType)
                .Select(x => x.InvoiceApprovalSetupID)
                .ToList();

            approvalsRequired = _dbContext.InvoiceApprovalSetupSequences
                .Where(x => approvalSetupIDs.Contains(x.InvoiceApprovalSetupID) &&
                            !x.InvoiceApprovalRecipient.Inactive)
                .ToList();

            var parallelApprovalsRecieved = _dbContext.InvoiceApprovals
                .Where(x => x.CompanyID == companyID &&
                            x.ClientID == clientID &&
                            x.InvoiceNumber == invoiceNumber &&
                            x.Approved &&
                            x.InvoiceApprovalSetupSequence.InvoiceApprovalSetup.ApprovalModel == InvoiceApprovalModel.Parallel.ToString() &&
                            !x.Inactive)
                .ToList();

            var parallelApprovalSetupIDs = parallelApprovalsRecieved
                .Select(x => x.InvoiceApprovalSetupSequence.InvoiceApprovalSetupID)
                .ToList();
            
            var approvalsRequiredSequenceIds = approvalsRequired.Select(r => r.InvoiceApprovalSetupSequenceID);

            var approvalsReceived = _dbContext.InvoiceApprovals
                .Where(x => x.CompanyID == companyID &&
                            x.ClientID == clientID &&
                            x.InvoiceNumber == invoiceNumber &&
                            x.Approved &&
                            approvalsRequiredSequenceIds.Contains(x.InvoiceApprovalSetupSequenceID) &&
                            !x.Inactive)
                .Select(x => x.InvoiceApprovalSetupSequenceID)
                .ToList();

            if (parallelApprovalsRecieved.Any())
            {
                approvalsRequired = approvalsRequired
                    .Where(x => x.InvoiceApprovalSetup.ApprovalModel != InvoiceApprovalModel.Parallel.ToString() ||
                                !parallelApprovalSetupIDs.Contains(x.InvoiceApprovalSetupID))
                    .ToList();
            }

            if (!approvalsReceived.Any())
            {
                var hasInactiveApprovals = _dbContext.InvoiceApprovals.Any(x => x.CompanyID == companyID &&
                                                                                x.ClientID == clientID &&
                                                                                x.InvoiceNumber == invoiceNumber &&
                                                                                approvalsRequiredSequenceIds.Contains(x.InvoiceApprovalSetupSequenceID) &&
                                                                                x.Inactive);
                var hasActiveApprovals = _dbContext.InvoiceApprovals.Any(x => x.CompanyID == companyID &&
                                                                              x.ClientID == clientID &&
                                                                              x.InvoiceNumber == invoiceNumber &&
                                                                              approvalsRequiredSequenceIds.Contains(x.InvoiceApprovalSetupSequenceID) &&
                                                                              !x.Inactive);
                if (hasActiveApprovals)
                {
                    return false;
                }
                else if (hasInactiveApprovals)
                {
                    return true;
                }
            }

            var approvalsRemaining = approvalsRequired
                .Where(x => !approvalsReceived.Contains(x.InvoiceApprovalSetupSequenceID))
                .Any();

            return !approvalsRemaining;
        }

        private bool HasActiveInvoiceApprovalEmailToken(int companyID, string clientID, int invoiceNumber, int invoiceApprovalSetupSequenceID)
        {
            int maxIteration = _dbContext.InvoiceApprovals
                .Where(a => a.CompanyID == companyID &&
                            a.ClientID == clientID &&
                            a.InvoiceNumber == invoiceNumber)
                .Max(a => a.Iteration);

            var activeInvoiceApprovalEmailToken = _dbContext.InvoiceApprovalEmailTokens
                .Where(t => t.CompanyID == companyID &&
                            t.ClientID == clientID &&
                            t.InvoiceNumber == invoiceNumber &&
                            t.InvoiceApprovalSetupSequenceID == invoiceApprovalSetupSequenceID &&
                            t.DateUsed == null)
                .Join(_dbContext.InvoiceApprovalSetupSequences,
                    t => new { t.InvoiceApprovalSetupSequenceID },
                    s => new { s.InvoiceApprovalSetupSequenceID },
                    (t, s) => new { t, s })
                .Join(_dbContext.InvoiceApprovalSetups,
                    ts => new { ts.s.InvoiceApprovalSetupID },
                    a => new { a.InvoiceApprovalSetupID },
                    (ts, a) => new { ts, a })
                .Join(_dbContext.InvoiceApprovals,
                    tsa => new { tsa.ts.t.CompanyID, tsa.ts.t.ClientID, tsa.ts.t.InvoiceNumber, tsa.ts.s.InvoiceApprovalSetupSequenceID },
                    A => new { A.CompanyID, A.ClientID, A.InvoiceNumber, A.InvoiceApprovalSetupSequenceID },
                    (tsa, A) => new { tsa, A })
                .Where(tsaA => tsaA.tsa.ts.t.CompanyID == companyID &&
                               tsaA.tsa.ts.t.ClientID == clientID &&
                               tsaA.tsa.ts.t.InvoiceNumber == invoiceNumber &&
                               tsaA.tsa.ts.t.InvoiceApprovalSetupSequenceID == invoiceApprovalSetupSequenceID &&
                               tsaA.tsa.ts.t.DateUsed == null &&
                               tsaA.A.Iteration == maxIteration &&
                               !tsaA.A.Inactive);

            return activeInvoiceApprovalEmailToken.Any();
        }

        private void ResetInvoicePreviewStatus(int companyID, string clientID, int darwinInvoiceNumber)
        {
            var invoice = _dbContext.Invoices
                .FirstOrDefault(i => i.CompanyID == companyID &&
                                     i.ClientID == clientID &&
                                     i.DarwinInvoiceNumber == darwinInvoiceNumber);
            if (invoice != null)
            {
                invoice.PreviewStatus = PayrollInvoicePreviewStatuses.Pending;
                _dbContext.SaveChanges();
            }
        }

        private void CreateApprovalUserToDoItems(int companyID, string clientID, int darwinInvoiceNumber, IEnumerable<int> invoiceApprovalSetupSequenceIDs)
        {
            var setupSequences = _dbContext.InvoiceApprovalSetupSequences
                .Where(x => invoiceApprovalSetupSequenceIDs.Contains(x.InvoiceApprovalSetupSequenceID) &&
                            x.InvoiceApprovalRecipient.RecipientID != null);
            List<UserToDoItem> toDoItems = new List<UserToDoItem>();
            foreach (var setupSequence in setupSequences)
            {
                var userID = setupSequence.InvoiceApprovalRecipient.RecipientID;
                UserToDoItem newToDo = new UserToDoItem()
                {
                    CompanyID = companyID,
                    ClientID = clientID,
                    UserID = userID,
                    EmployeeID = String.Empty,
                    OriginMessage = "An invoice approval has been assigned to you.",
                    OriginFunction = "PayrollApproval",
                    ASPFunction = "Approvals|loadTable=invoice",
                    ApprovalInfo = $"invoice={darwinInvoiceNumber}",
                    DateCreated = DateTime.Now
                };
                toDoItems.Add(newToDo);
            };
            _dbContext.UserToDoItems.AddRange(toDoItems);
            _dbContext.SaveChanges();
        }

        #endregion
    }
}