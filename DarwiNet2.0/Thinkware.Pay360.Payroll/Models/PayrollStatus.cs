using System.ComponentModel.DataAnnotations;

namespace Thinkware.Pay360.Payroll
{
    // TODO: CH-635 Switch the out-of-order statuses here and Definition
    public enum PayrollStatus
    {
        /// <summary>
        /// If a payroll is in this state, it is invalid. This value is mainly used as a default value.
        /// </summary>
        [Display(Name = "Undefined")]
        Undefined = 0,

        [Display(Name = "Deleting")]
        Deleting = 9997,

        [Display(Name = "Resetting")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        Resetting = 9998,

        /// <summary>
        /// This will be used if the user removes a payroll and wants to “remove” the entry from the schedule.
        /// We won’t remove it, just mark it with this status.
        /// This will be done in the Finalize step or on the Payroll Cockpit.
        /// </summary>
        [Display(Name = "Removed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        Removed = 9999,

        #region 1001-1999 Pre-Process Codes

        /// <summary>
        /// This is initially set by the Payroll Scheduler when creating the entries.
        /// All entries except the first will have this value when the schedule is created.
        /// Payroll cannot be processed with this status.
        /// </summary>
        [Display(Name = "Pending")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        Pending = 1001,

        /// <summary>
        /// When the schedule is created, the first record is set to Available.
        /// Then, when the payroll entry ahead of it completes, the next one becomes "Available".
        /// Available payrolls are the payrolls that can be processed.
        /// </summary>
        [Display(Name = "Available")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        Available = 1003,

        /// <summary>
        /// The payroll is set to this status when the build process starts.
        /// It may only be this status for a brief time depending on how long the build takes to complete.
        /// </summary>
        [Display(Name = "Validating")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        Validating = 1011,

        /// <summary>
        /// The payroll is set to this status if the build process creates warnings in the processing.
        /// </summary>
        [Display(Name = "Validation Warnings Exist")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        ValidationWarningsExist = 1012,

        /// <summary>
        /// The payroll is set to this status if the build process creates warnings in the processing.
        /// </summary>
        [Display(Name = "Build Warnings Exist")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        BuildWarningsExist = 1013,

        /// <summary>
        /// The payroll is set to this status if the build process creates errors in the processing.
        /// </summary>
        [Display(Name = "Errors Exist")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        ErrorsExist = 1014,

        /// <summary>
        /// "The payroll is set to this status when the build process starts.
        /// It may only be this status for a brief time depending on how long the build takes to complete."
        /// </summary>
        [Display(Name = "Building")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        Building = 1020,

        #endregion

        #region 2001-2999 Payroll Codes

        /// <summary>
        /// The payroll is set to this status if the build process returns with no warnings and/or errors.
        /// </summary>
        [Display(Name = "Payroll Ready")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollReady = 2001,

        /// <summary>
        /// The payroll is set to this status when it is sent to the engine and being calculated.
        /// It will remain in this status until calculating has been completed.
        /// </summary>
        [Display(Name = "Payroll Processing")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollProcessing = 2002,

        /// <summary>
        /// Indicates Payroll has calculated and values have returned from the engine
        /// </summary>
        [Display(Name = "Payroll Calculated")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollCalculated = 2005,

        /// <summary>
        /// Indicates a Void payroll has been created - next option 
        /// </summary>
        [Display(Name = "Void Calculated")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        VoidCalculated = 2006,

        /// <summary>
        /// If approvals are required for a payroll, based on profile setting.
        /// </summary>
        [Display(Name = "Payroll Approvals Required")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollApprovalsRequired = 2007,

        /// <summary>
        /// 
        /// </summary>
        [Display(Name = "Void Completed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        VoidCompleted = 2008,

        /// <summary>
        /// 
        /// </summary>
        [Display(Name = "Payroll C/V Needs Processed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollControlsAndVariancesNeedsProcessed = 2010,

        /// <summary>
        /// The payroll is set to this status when the Payroll Controls/ Variances starts.
        /// It may only be this status for a brief time depending on how long the build takes to complete.
        /// </summary>
        [Display(Name = "Payroll C/V Running")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollControlsAndVariancesRunning = 2011,

        /// <summary>
        /// The payroll is set to this status if the Payroll Controls/ Variances process creates records that need reviewed.
        /// </summary>
        [Display(Name = "Payroll C/V Needs Review")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollControlsAndVariancesNeedReview = 2012,

        /// <summary>
        /// Indicates the Payroll Controls/Variances has processed and no issues were found.
        /// </summary>
        [Display(Name = "Payroll C/V Passed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollControlsAndVariancesPassed = 2013,

        /// <summary>
        /// This status indicates the payroll has been submitted for approval/review but has not yet been approved.
        /// </summary>
        [Display(Name = "Payroll Waiting Approval")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollWaitingApproval = 2022,

        /// <summary>
        /// Payroll has been approved by all parties
        /// </summary>
        [Display(Name = "Payroll Approved")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollApproved = 2024,

        /// <summary>
        /// Payroll has been declined by at least one party
        /// </summary>
        [Display(Name = "Payroll Declined")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollDeclined = 2025,

        /// <summary>
        /// The payroll is set to this status when it has been marked as completed by a user.
        /// </summary>
        [Display(Name = "Payroll Completed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        PayrollCompleted = 2031,

        #endregion

        #region 3001-3999 Invoice Codes

        /// <summary>
        /// The payroll is set to this status when the invoice is sent to the engine and being calculated.
        /// It will remain in this status until invoice calculating has been completed.
        /// </summary>
        [Display(Name = "Invoice Processing")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll)]
        InvoiceProcessing = 3001,

        /// <summary>
        /// The invoice is set to this status when the invoice has returned from the engine along with all the invoice values in the tables.  
        /// </summary>
        [Display(Name = "Invoice Calculated")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceCalculated = 3005,

        /// <summary>
        /// If approvals are required for an invoice, based on profile setting.
        /// </summary>
        [Display(Name = "Invoice Approvals Required")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceApprovalsRequired = 3007,

        /// <summary>
        /// 
        /// </summary>
        [Display(Name = "Invoice C/V Needs Processed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceControlsAndVariancesNeedsProcessed = 3010,

        /// <summary>
        /// The invoice is set to this status when the Invoice Controls/ Variances starts.
        /// It may only be this status for a brief time depending on how long the build takes to complete.
        /// </summary>
        [Display(Name = "Invoice C/V Running")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceControlsAndVariancesRunning = 3011,

        /// <summary>
        /// The invoice is set to this status if the Invoice Controls/ Variances process creates records that need reviewed.
        /// </summary>
        [Display(Name = "Invoice C/V Needs Review")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceControlsAndVariancesNeedReview = 3012,

        /// <summary>
        /// The invoice is set to this status to indicate the Invoice Controls/Variances has processed and no issues were found.
        /// </summary>
        [Display(Name = "Invoice C/V Passed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceControlsAndVariancesPassed = 3013,

        /// <summary>
        /// This status indicates the invoice has been merged to another invoice and this is no longer valid.
        /// </summary>
        [Display(Name = "Invoice Merged")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceMerged = 3020,

        /// <summary>
        /// The invoice is set to this status if invoice approval is necessary.
        /// This status indicates the invoice is waiting approval/review.
        /// </summary>
        [Display(Name = "Invoice Waiting Approval")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceWaitingApproval = 3022,

        /// <summary>
        /// The invoice is set to this status when the invoice has been approved by all parties
        /// </summary>
        [Display(Name = "Invoice Approved")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceApproved = 3024,

        /// <summary>
        /// The invoice is set to this status when the invoice has been declined by at least one party
        /// </summary>
        [Display(Name = "Invoice Declined")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceDeclined = 3025,

        /// <summary>
        /// The invoice is set to this status when it has been marked as completed by a user.
        /// </summary>
        [Display(Name = "Invoice Completed")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        InvoiceCompleted = 3031,

        #endregion

        #region 4001-4999 Finalize Codes

        /// <summary>
        /// The payroll is set to this status when it has been finalized and sent to Darwin.
        /// However, the posting results have not yet been updated to DNet.
        /// Entries with this status will appear in the Completed Payrolls page.
        /// The invoice is set to this status when it has been finalized and sent to Darwin.
        /// However, the posting results have not yet been updated to DNet.
        /// Entries with this status will appear in the Completed Invoices page.
        /// </summary>
        [Display(Name = "Finalized")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        Finalized = 4021,

        #endregion

        #region 5001-5999 Post-Process Codes

        /// <summary>
        /// The payroll is set to this status when it is completely done and all final information has been uploaded to DNet.
        /// Entries with this status will appear in the Completed Payrolls page.
        /// The invoice is set to this status when it is completely done and all final information has been uploaded to DNet.
        /// Entries with this status will appear in the Completed Invoices page.
        /// </summary>
        [Display(Name = "Posted")]
        [UpdatePayrollStatusHandler(PayrollStatusTags.Payroll | PayrollStatusTags.Invoice)]
        Posted = 5001,

        #endregion
    }
}