using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Services.D2;
using DataDrivenViewEngine.Models.Core;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Thinkware.Pay360.Payroll;

namespace Thinkware.Cohesion.Payroll
{
    public class ResetPayrollHandler : IHandler<ResetPayrollRequest>
    {
        private readonly DnetEntities _dbContext;
        private readonly IEditPayrollCalculationService _editCalcService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPayrollService _payrollService;
        private readonly IPayrollApprovalService _payrollApprovalService;
        private readonly IInvoiceMergeReversionService _invoiceMergeReversionService;

        public ResetPayrollHandler(DnetEntities dbContext, IEditPayrollCalculationService editCalcService, IInvoiceService invoiceService, IPayrollService payrollService, IPayrollApprovalService payrollApprovalService, IInvoiceMergeReversionService invoiceMergeReversionService)
        {
            _dbContext = dbContext;
            _editCalcService = editCalcService;
            _invoiceService = invoiceService;
            _payrollService = payrollService;
            _payrollApprovalService = payrollApprovalService;
            _invoiceMergeReversionService = invoiceMergeReversionService;
        }

        public void Handle(ResetPayrollRequest request)
        {
            string payrollNumber = request.PayrollNumber;
            ClientPayrollSchedule schedule = _dbContext.ClientPayrollSchedules.Where(x => x.PayrollNumber == payrollNumber).FirstOrDefault();
            if (schedule == null)
                return;

            //_payrollResetHistoryService.Create(reason, userId, payrollNumber, newPayrollNumber, priorStatus, scheduleId);
            _editCalcService.Reset(payrollNumber);
            _payrollApprovalService.ResetPayrollAndInvoiceApprovalsForPayroll(payrollNumber);
            _invoiceMergeReversionService.ResetMergedPayrolls(payrollNumber, _dbContext);
            _invoiceService.DeleteInvoiceData(payrollNumber);
            _payrollService.DeleteOriginalsFromPayroll(payrollNumber);
            _payrollService.DeletePayrollWorkRecordsFromPayroll(payrollNumber, schedule.ManualCheckType);
            _payrollService.DeleteAuditRecords(payrollNumber);
            _payrollService.ResetPayrollSchedule(payrollNumber);
            _dbContext.Database.ExecuteSqlCommand("DELETE FROM TableSnapshots WHERE PayrollNumber = @payrollNumber;", new SqlParameter("@payrollNumber", payrollNumber));
            _dbContext.Database.ExecuteSqlCommand("DELETE FROM ControlRecordHistory WHERE PayrollNumber = @payrollNumber;", new SqlParameter("@payrollNumber", payrollNumber));
            _dbContext.Database.ExecuteSqlCommand("DELETE FROM VarianceRecordHistory WHERE PayrollNumber = @payrollNumber;", new SqlParameter("@payrollNumber", payrollNumber));
            _dbContext.Database.ExecuteSqlCommand("DELETE FROM ControlRecords WHERE PayrollNumber = @payrollNumber;", new SqlParameter("@payrollNumber", payrollNumber));
            _dbContext.Database.ExecuteSqlCommand("DELETE FROM VarianceRecords WHERE PayrollNumber = @payrollNumber;", new SqlParameter("@payrollNumber", payrollNumber));
            
            if (request.ResetForDeletion) return;
            
            if (schedule.ManualCheckType == (byte)ManualCheckTypes.None)
            {
                new UpdatePayrollStatusHandler(_dbContext)
                        .Handle(PayrollNumber.Parse(payrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.Available));
            }
            else
            {
                new UpdatePayrollStatusHandler(_dbContext)
                        .Handle(PayrollNumber.Parse(payrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.PayrollCalculated));
            }
        }
    }
}