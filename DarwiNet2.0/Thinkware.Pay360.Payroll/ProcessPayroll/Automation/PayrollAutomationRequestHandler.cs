using DarwiNet2._0.Data;
using DarwiNet2._0.Interfaces.Services.D2;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Thinkware.Pay360;
using Thinkware.Pay360.Messaging;
using Thinkware.Pay360.Payroll;
using Microsoft.AspNet.SignalR;
using DarwiNet2._0;
using Autofac;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Extensions;
using Thinkware.Pay360.SignalR;
using System.Diagnostics;

namespace Thinkware.Cohesion.Payroll
{
    public class PayrollAutomationRequestHandler : IHandler<PayrollAutomationRequest>
    {
        private readonly DnetEntities _dbContext;
        private readonly IPayrollTeamsService _payrollTeamsService;
        private readonly IPayrollProfileSettingService _payrollProfileSettingService;
        private readonly IControlAndVarianceProvider _controlAndVarianceProvider;
        private readonly IPayrollWorkMasterProvider _payrollWorkMasterProvider;
        private readonly IPayrollFinalizeService _payrollFinalizeService;
        private readonly IPayrollProfileControlService _payrollProfileControlService;
        private readonly IPayrollProfileVarianceService _payrollProfileVarianceService;

        public PayrollAutomationRequestHandler(
            DnetEntities dbContext,
            IPayrollTeamsService payrollTeamsService,
            IPayrollProfileSettingService payrollProfileSettingService,
            IControlAndVarianceProvider controlAndVarianceProvider,
            IPayrollWorkMasterProvider payrollWorkMasterProvider,
            IPayrollFinalizeService payrollFinalizeService,
            IPayrollProfileControlService payrollProfileControlService,
            IPayrollProfileVarianceService payrollProfileVarianceService)
        {
            _dbContext = dbContext;
            _payrollTeamsService = payrollTeamsService;
            _payrollProfileSettingService = payrollProfileSettingService;
            _controlAndVarianceProvider = controlAndVarianceProvider;
            _payrollWorkMasterProvider = payrollWorkMasterProvider;
            _payrollFinalizeService = payrollFinalizeService;
            _payrollProfileControlService = payrollProfileControlService ?? throw new ArgumentNullException(nameof(payrollProfileControlService));
            _payrollProfileVarianceService = payrollProfileVarianceService ?? throw new ArgumentNullException(nameof(payrollProfileVarianceService));
        }

        public void Handle(PayrollAutomationRequest request)
        {
            ClientPayrollSchedule schedule = _dbContext.ClientPayrollSchedules.FirstOrDefault(x => x.PayrollNumber == request.PayrollNumber);
            if (schedule == null)
                throw new ApplicationException(request.PayrollNumber);

            PayrollProfileSetting payrollProfileSettings = _dbContext.PayrollProfileSettings.FirstOrDefault(c => c.CompanyID == schedule.CompanyID && c.ClientID == schedule.ClientID && c.ProfileID == schedule.ProfileID);
            if (payrollProfileSettings == null)
                throw new ArgumentNullException("Unable to find [dbo].[PayrollProfileSettings] record");

            PayrollProcessOption payrollProcessOptions = _dbContext.PayrollProcessOptions.FirstOrDefault(po => po.PayrollNumber == schedule.PayrollNumber);

            var autoFinalize = payrollProfileSettings.AutoFinalize;

            if (payrollProcessOptions.IsNotNull())
            {
                autoFinalize = payrollProcessOptions.AutoFinalize;
            }

            switch (request.StatusEventType)
            {
                case StatusEventTypes.PayrollCompleted:
                    if (payrollProfileSettings.BypassInvoice)
                    {
                        new UpdatePayrollStatusHandler(_dbContext)
                            .Handle(PayrollNumber.Parse(request.PayrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.InvoiceCompleted));
                    }
                    break;

                case StatusEventTypes.ProcessPayrollResultComplete:
                    if (payrollProfileSettings.NotifyCalcComplete)
                    {
                        HandleNotifyCalcComplete(request, schedule);
                    }
                    if (payrollProcessOptions.PayrollControlsVariances && payrollProfileSettings.AutoProcessControlsAndVariances)
                    {
                        var profileControls = _payrollProfileControlService.List(request.CompanyID, request.ClientID, schedule.ProfileID);
                        var profileVariances = _payrollProfileVarianceService.List(request.CompanyID, request.ClientID, schedule.ProfileID);
                        var hasPayrollControls = profileControls.Exists(pc => pc.ControlName < 3000 && pc.Active == 1);
                        var hasPayrollVariances = profileVariances.Exists(pv => pv.VarianceName < 3000 && pv.Active == 1);
                        
                        if (hasPayrollControls || hasPayrollVariances)
                        {
                            HandlePayrollControlsAndVariances(request, schedule);
                            break;
                        }
                    }
                    if (!payrollProcessOptions.PayrollControlsVariances && payrollProcessOptions.AutoApprovalsPayroll)
                    {
                        HandlePayrollApprovals(request);
                        break;
                    }
                    if (!payrollProcessOptions.PayrollControlsVariances && !payrollProcessOptions.AutoApprovalsPayroll && payrollProcessOptions.AutoInvoiceCalc && !payrollProfileSettings.BypassInvoice)
                    {
                        HandleCreateInvoiceSnapshot(request, schedule);
                        break;
                    }
                    break;

                case StatusEventTypes.PayrollControlsAndVariancesPassed:
                    if (payrollProcessOptions.AutoApprovalsPayroll)
                    {
                        HandlePayrollApprovals(request);
                        break;
                    }
                    if (payrollProcessOptions.AutoInvoiceCalc && !payrollProfileSettings.BypassInvoice)
                    {
                        HandleCreateInvoiceSnapshot(request, schedule);
                        break;
                    }
                    break;

                case StatusEventTypes.PayrollApprovalsRequired:
                    if (payrollProfileSettings.NotifyCalcComplete)
                    {
                        HandleNotifyCalcComplete(request, schedule);
                    }
                    break;
                // TODO: Uncomment when this status event is created (and fired after all payroll approvals are approved/completed)
                //case StatusEventTypes.PayrollApproved:
                //if (payrollProfileSettings.AutoInvoice)
                //{
                //    HandleCreateInvoiceSnapshot(request, schedule);
                //    break;
                //}
                //break;

                case StatusEventTypes.ProcessInvoiceResultComplete:
                case StatusEventTypes.InvoiceControlsAndVariancesNeedsProcessed:
                case StatusEventTypes.InvoiceApprovalsRequired:
                    if (payrollProcessOptions.AutoMissedCodeProcessing)
                    {
                        HandleAutoMissedCodesProcessing(request);
                    }

                    if (payrollProfileSettings.NotifyInvoiceCreated)
                    {
                        HandleNotifyInvoiceCreated(request, schedule);
                    }

                    if (payrollProcessOptions.PayrollControlsVariances && payrollProfileSettings.AutoProcessControlsAndVariances)
                    {
                        var profileControls = _payrollProfileControlService.List(request.CompanyID, request.ClientID, schedule.ProfileID);
                        var profileVariances = _payrollProfileVarianceService.List(request.CompanyID, request.ClientID, schedule.ProfileID);
                        var hasInvoiceControls = profileControls.Exists(pc => pc.ControlName >= 3000 && pc.Active == 1);
                        var hasInvoiceVariances = profileVariances.Exists(pv => pv.VarianceName >= 3000 && pv.Active == 1);

                        if (hasInvoiceControls || hasInvoiceVariances)
                        {
                            HandleInvoiceControlsAndVariances(request);
                            break;
                        }
                    }
                    if (!payrollProcessOptions.PayrollControlsVariances && payrollProcessOptions.AutoApprovalsInvoice)
                    {
                        HandleInvoiceApprovals(request);
                        break;
                    }
                    if (!payrollProcessOptions.PayrollControlsVariances && autoFinalize)
                    {
                        HandleFinalize(request);
                        break;
                    }
                    break;

                case StatusEventTypes.InvoiceControlsAndVariancesNeedsReview:
                    if (payrollProcessOptions.AutoMissedCodeProcessing)
                    {
                        HandleAutoMissedCodesProcessing(request);
                    }
                    break;

                case StatusEventTypes.InvoiceControlsAndVariancesPassed:
                    InvoicePayroll processedInvoicePayroll = _dbContext.InvoicePayrolls.FirstOrDefault(x => x.PayrollNumber == request.PayrollNumber &&
                                                                                                            x.DarwinInvoiceNumber == request.InvoiceNumber &&
                                                                                                            x.Status == (int)PayrollStatus.InvoiceControlsAndVariancesPassed);

                    if (processedInvoicePayroll == null)
                    {
                        // TODO: Log error?
                        break;
                    }
                    if (payrollProcessOptions.AutoMissedCodeProcessing)
                    {
                        HandleAutoMissedCodesProcessing(request);
                    }
                    if (payrollProfileSettings.AutoApprovalsInvoice)
                    {
                        HandleInvoiceApprovals(request);
                        break;
                    }
                    if (autoFinalize)
                    {
                        HandleFinalize(request);
                        break;
                    }
                    break;

                    // TODO: Uncomment when this status event is created (and fired after all invoice approvals are approved/completed)
                    //case StatusEventTypes.InvoiceApproved:
                    //if (payrollProfileSettings.AutoFinalize)
                    //{
                    //    HandleFinalize(request);
                    //    break;
                    //}
                    //break;

                case StatusEventTypes.CreatingInvoiceSnapshot:
                    // If auto invoice is on, we need to set the completed by and completed by date now that the payroll is completed
                    if (payrollProcessOptions.AutoInvoiceCalc)
                    {
                        schedule.PayrollProcessCompletedBy = request.UserId;
                        schedule.PayrollProcessCompletedDate = DateTime.Now;
                        _dbContext.SaveChanges();
                    }
                    break;

            }
        }

        private void HandleNotifyCalcComplete(PayrollAutomationRequest request, ClientPayrollSchedule schedule)
        {
            new SMTPNotificationHandler(_dbContext)
                    .Handle(new SMTPNotificationRequest(request.PayrollNumber, request.CompanyID, request.ClientID, schedule.ProfileID, Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.PAYROLL_CALC_COMPLETED));
        }

        private void HandlePayrollControlsAndVariances(PayrollAutomationRequest request, ClientPayrollSchedule schedule)
        {
            new UpdatePayrollStatusHandler(_dbContext)
                .Handle(PayrollNumber.Parse(request.PayrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.PayrollControlsAndVariancesRunning));

            var snapshotId = _dbContext.PayrollSnapshots.Where(s => s.PayrollNumber == request.PayrollNumber)
                .Select(x => x.SnapshotID)
                .FirstOrDefault();
            var connectionString = _dbContext.Database.Connection.ConnectionString;

            var customerId = PayrollNumber.Parse(request.PayrollNumber).CustomerId;

            List<string> employeeIdsPerPayroll = _dbContext.PayrollWorkHeaders.Where(x => x.PayrollNumber == request.PayrollNumber).Select(x => x.EmployeeID).ToList();
            foreach (string employeeId in employeeIdsPerPayroll)
            {
                RabbitMQManager.Instance.PublishTask(new ControlsPerEmployeeTask(request.PayrollNumber, employeeId, snapshotId, customerId, request.UserId)
                {
                    ConnectionString = connectionString
                });
                RabbitMQManager.Instance.PublishTask(new VariancesPerEmployeeTask(request.PayrollNumber, employeeId, snapshotId, customerId, request.UserId)
                {
                    ConnectionString = connectionString
                });
            }

            RabbitMQManager.Instance.PublishTask(new ControlsPerPayrollTask(request.PayrollNumber, snapshotId, customerId, request.UserId)
            {
                ConnectionString = connectionString
            });
            RabbitMQManager.Instance.PublishTask(new VariancesPerPayrollTask(request.PayrollNumber, snapshotId, customerId, request.UserId)
            {
                ConnectionString = connectionString
            });
        }

        private void HandlePayrollApprovals(PayrollAutomationRequest request)
        {
            new SubmitPayrollApprovalRequestHandler(_dbContext)
                .Handle(new SubmitPayrollApprovalRequest(request.PayrollNumber));
        }

        private void HandleCreateInvoiceSnapshot(PayrollAutomationRequest request, ClientPayrollSchedule schedule)
        {
            schedule.PayrollProcessCompletedBy = request.UserId;
            schedule.PayrollProcessCompletedDate = DateTime.Now;
            _dbContext.SaveChanges();

            var payrollWorkMaster = _dbContext.PayrollWorkMasters.FirstOrDefault(x => x.PayrollNumber == request.PayrollNumber);

            new UpdatePayrollStatusHandler(_dbContext)
                .Handle(PayrollNumber.Parse(request.PayrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.PayrollCompleted));

            ProcessPayrollResult result = new ProcessPayrollHandler(_dbContext, _payrollTeamsService, _payrollProfileSettingService, _controlAndVarianceProvider, _payrollWorkMasterProvider, _payrollFinalizeService, _payrollProfileControlService, _payrollProfileVarianceService)
                .Handle(new ProcessPayrollRequest(request.PayrollNumber, request.UserId));
        }

        private void HandleNotifyInvoiceCreated(PayrollAutomationRequest request, ClientPayrollSchedule schedule)
        {
            bool hasNotifiedInvoiceCreated = _dbContext.InvoiceApprovals.Where(x => x.CompanyID == request.CompanyID &&
                                                                                                x.ClientID == request.ClientID &&
                                                                                                x.Approved).Any();
            if (!hasNotifiedInvoiceCreated)
            {
                new SMTPNotificationHandler(_dbContext)
                    .Handle(new SMTPNotificationRequest(request.PayrollNumber, request.CompanyID, request.ClientID, schedule.ProfileID, request.InvoiceNumber, Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.INVOICE_CREATED));
            }
        }

        private void HandleInvoiceControlsAndVariances(PayrollAutomationRequest request)
        {
            var snapshotId = _dbContext.PayrollSnapshots.Where(s => s.PayrollNumber == request.PayrollNumber)
                .Select(x => x.SnapshotID)
                .FirstOrDefault();
            var customerId = PayrollNumber.Parse(request.PayrollNumber).CustomerId;
            var connectionString = _dbContext.Database.Connection.ConnectionString;

            List<InvoicePayroll> invoicePayrolls = _dbContext.InvoicePayrolls.Where(x => x.PayrollNumber == request.PayrollNumber).ToList();
            bool approvalPending = invoicePayrolls.Any(x => x.Status == (int)PayrollStatus.InvoiceWaitingApproval);
            if (!approvalPending)
            {
                InvoicePayroll invoicePayroll = invoicePayrolls.FirstOrDefault(x => x.Status == (int)PayrollStatus.InvoiceCalculated);
                if (invoicePayroll != null)
                {
                    new UpdatePayrollStatusHandler(_dbContext)
                        .Handle(PayrollNumber.Parse(request.PayrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.InvoiceControlsAndVariancesRunning), invoicePayroll.DarwinInvoiceNumber);

                    RabbitMQManager.Instance.PublishTask(new ControlsPerInvoiceTask(invoicePayroll.CompanyID, invoicePayroll.ClientID, invoicePayroll.DivisionID, invoicePayroll.DarwinInvoiceNumber, invoicePayroll.PayrollNumber, snapshotId, customerId, request.UserId)
                    {
                        ConnectionString = connectionString
                    });
                    RabbitMQManager.Instance.PublishTask(new VariancesPerInvoiceTask(invoicePayroll.CompanyID, invoicePayroll.ClientID, invoicePayroll.DivisionID, invoicePayroll.DarwinInvoiceNumber, invoicePayroll.PayrollNumber, snapshotId, customerId, request.UserId)
                    {
                        ConnectionString = connectionString
                    });
                }
            }
        }

        private void HandleInvoiceApprovals(PayrollAutomationRequest request)
        {
            var oPayrollNumber = PayrollNumber.Parse(request.PayrollNumber);

            new SubmitInvoiceApprovalRequestHandler(_dbContext)
                .Handle(new SubmitInvoiceApprovalRequest(oPayrollNumber.CompanyId, oPayrollNumber.ClientId, request.InvoiceNumber));
        }

        private void HandleFinalize(PayrollAutomationRequest request)
        {
            List<InvoicePayroll> invoicePayrolls = _dbContext.InvoicePayrolls.Where(x => x.PayrollNumber == request.PayrollNumber).ToList();
            foreach (var invoicePayroll in invoicePayrolls)
            {
                invoicePayroll.InvoiceProcessCompletedBy = request.UserId;
                invoicePayroll.InvoiceProcessCompletedDate = DateTime.Now;
            }
            _dbContext.SaveChanges();

            foreach (var invoicePayroll in invoicePayrolls)
            {
                new UpdatePayrollStatusHandler(_dbContext)
                    .Handle(PayrollNumber.Parse(request.PayrollNumber), PayrollStatusDefinition.Parse(PayrollStatus.InvoiceCompleted), invoicePayroll.DarwinInvoiceNumber);
            }
            try
            {
                _payrollFinalizeService.FinalizePayroll(request.PayrollNumber, request.UserId);
            } catch (Exception ex)
            {
                GlobalHost.ConnectionManager.GetHubContext<PayrollCockpitHub>()
                    .Clients.All.notifyFinalizeFailed(request.PayrollNumber, request.UserId);
                throw;
            }
        }

        private void HandleAutoMissedCodesProcessing(PayrollAutomationRequest request)
        {
            new InvoiceAutoRecaptureHandler(_dbContext).Handle(new InvoiceAutoRecaptureRequest(request.PayrollNumber, request.InvoiceNumber));

            using (var lifetime = IoC.BeginLifetimeScope())
            {
                var invoiceRecaptureService = lifetime.Resolve<IInvoiceRecaptureService>();
                invoiceRecaptureService.ProcessMissedCodesByPayrollNumberAndDarwinInvoiceNumber(request.PayrollNumber, request.InvoiceNumber);
            }

            GlobalHost.ConnectionManager.GetHubContext<PayrollCockpitHub>().Clients.All.autoMissedCodeComplete(new AutoMissedCodeCompleteMessage
            {
                PayrollNumber = request.PayrollNumber,
                UserId = request.UserId,
            });
        }
    }
}