using DarwiNet2._0.Controllers;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using DarwiNet2._0.ViewModels;
using DarwiNet2._0.ViewModels.D2.TimeSheet;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Thinkware.Utilities;

namespace Thinkware.Pay360.Timesheets
{
    public static class TimesheetUtility
    {
        public static readonly List<short?> CodeColumnTypes = new List<short?>()
        {
            TimesheetColumnType.BenefitCode,
            TimesheetColumnType.DeductionCode,
            TimesheetColumnType.PayCode,
            TimesheetColumnType.OTPayCode,
            TimesheetColumnType.RegPayCode,
            TimesheetColumnType.VarPayCode
        };

        public static readonly List<byte> PTOPaycodeTypes = new List<byte>()
        {
            PaycodeType.Vacation,
            PaycodeType.Sick,
            PaycodeType.PTO1,
            PaycodeType.PTO2,
            PaycodeType.PTO3,
            PaycodeType.PTO4,
            PaycodeType.PTO5,
            PaycodeType.PTO6,
            PaycodeType.PTO7,
            PaycodeType.PTO8,
            PaycodeType.PTO9,
            PaycodeType.PTO10
        };

        public static List<string> GetBusinessExpenseCodes(IEnumerable<ITimeSheetColumn> timeSheetColumns) =>
            timeSheetColumns
                .Where(c =>
                    c.Type == TimesheetColumnType.PayCode &&
                    c.CodeType == PaycodeType.BusinessExp)
                .Select(c => c.Code)
                .ToList();

        public static void SortTimeSheetRows(short? sortBy, List<ITimeSheetRow> timeSheetRows, IEnumerable<ITimeSheetColumn> timeSheetColumns)
        {
            foreach (var row in timeSheetRows)
            {
                row.SortValue = GetRowSortValue(sortBy, row.Details, timeSheetColumns);
            }

            timeSheetRows = timeSheetRows.OrderBy(r => r.SortValue).ToList();

            var rowId = 1;
            foreach (var row in timeSheetRows)
            {
                row.RowID = rowId;
                rowId++;
            }
        }

        public static string GetRowSortValue(short? sortBy, string[] data, IEnumerable<ITimeSheetColumn> timeSheetColumns)
        {
            var result = string.Empty;
            var compareFields = GetTimeSheetSortCompareFields(sortBy);
            foreach (var type in compareFields)
            {
                var col = timeSheetColumns.GetColumnNumberByType(type);
                var tempValue = string.Empty;
                if (col > 0)
                {
                    tempValue = data[col - 1].Trim();
                    if (type == TimesheetColumnType.EmployeeID)
                        tempValue = Normalizer.NormalizeEmployeeID(tempValue);
                }
                result += string.IsNullOrWhiteSpace(result) ? tempValue : $"|{tempValue}";
            }
            return result;
        }

        public static string GetDayStringDate(string name, DateTime? startDate, out string dayOfWeek)
        {
            // TODO: Figure out what the logic with dayIndex is doing.
            string dayIndex = name.Replace("Day", "");
            DateTime d = startDate ?? DateTime.Today;

            if (!int.TryParse(dayIndex, out int idx))
                idx = 1;

            if (idx > 1)
                d = d.AddDays(idx - 1);

            dayOfWeek = string.Empty;
            switch (d.DayOfWeek)
            {
                case DayOfWeek.Sunday:
                    dayOfWeek = "Sun";
                    break;
                case DayOfWeek.Monday:
                    dayOfWeek = "Mon";
                    break;
                case DayOfWeek.Tuesday:
                    dayOfWeek = "Tue";
                    break;
                case DayOfWeek.Wednesday:
                    dayOfWeek = "Wed";
                    break;
                case DayOfWeek.Thursday:
                    dayOfWeek = "Thu";
                    break;
                case DayOfWeek.Friday:
                    dayOfWeek = "Fri";
                    break;
                case DayOfWeek.Saturday:
                    dayOfWeek = "Sat";
                    break;
            }

            // TODO: Move format string to Constants.cs
            return d.ToString("MM/dd/yyyy");
        }

        public static List<TimeSheetColumn> MapToTimesheetColumns(int timesheetId, ClientTimeSheetProfile profile, IEnumerable<ClientTimeSheetProfileColumn> profileColumns, DateTime? dateFrom, bool isCertifiedTS)
        {
            var columns = new List<TimeSheetColumn>();

            if (profileColumns.IsNullOrEmpty())
                return columns;

            var colNum = 1;
            foreach (var profileColumn in profileColumns)
            {
                var status = profileColumn.Status ?? TimesheetColumnStatus.Hidden;
                var lb = profileColumn.Label;

                switch (profileColumn.ColType)
                {
                    case TimesheetColumnType.CodeHours:
                        if (isCertifiedTS)
                            lb = (profileColumn.CodeType == PaycodeType.Overtime) ? "OT" : "Reg";
                        break;

                    case TimesheetColumnType.DateDay:
                        if (isCertifiedTS)
                        {
                            var buf = string.Empty;
                            lb = TimesheetUtility.GetDayStringDate(profileColumn.Name, dateFrom, out buf);
                            if (!string.IsNullOrEmpty(buf))
                                lb += " - " + buf;
                        }
                        break;

                    case TimesheetColumnType.Days:
                        if (profile.HideDays)
                            status = TimesheetColumnStatus.Hidden;
                        break;

                    case TimesheetColumnType.Weeks:
                        if (profile.HideWeeks)
                            status = TimesheetColumnStatus.Hidden;
                        break;

                    case TimesheetColumnType.CheckNbr:
                        if (profile.HideCheckNumber)
                            status = TimesheetColumnStatus.Hidden;
                        break;

                    case TimesheetColumnType.OfferedHours:
                        if (profile.HideOfferedHours)
                            status = TimesheetColumnStatus.Hidden;
                        break;
                }

                columns.Add(new TimeSheetColumn()
                {
                    TimeSheetID = timesheetId,
                    ColNbr = colNum,
                    Name = profileColumn.Name,
                    Label = lb,
                    Status = status,
                    ColType = profileColumn.ColType,
                    ValidationType = profileColumn.ValidationType,
                    Code = profileColumn.Code,
                    CodeType = profileColumn.CodeType,
                    BasedOn = profileColumn.BasedOn,
                    BaseCode = profileColumn.BaseCode,
                    BaseCodeType = profileColumn.BaseCodeType
                });
                colNum++;
            }

            return columns.OrderBy(x => x.SortOrder).ToList();
        }

        public static HashSet<string> GetTimeSheetEmployeeIdsAsHashSet(IEnumerable<ITimeSheetRow> rows) =>
            rows.Select(s => s.EmployeeID).ToHashSet<string>();

        public static bool IsProfileTypeCertified(ClientTimeSheetProfile profile) =>
            profile.ProfileType == TimesheetType.CertifiedByEE || profile.ProfileType == TimesheetType.CertifiedByJob;

        public static bool IsTimeSheetTypeCertified(DarwiNet2._0.Data.TimeSheet timeSheet) => IsTimeSheetTypeCertified(timeSheet.TimeSheetType);

        public static bool IsTimeSheetTypeCertified(short? timeSheetType)
        {
            if (!timeSheetType.HasValue)
                return false;

            return timeSheetType == TimesheetType.CertifiedByEE || timeSheetType == TimesheetType.CertifiedByJob;
        }

        public static int GetColumnNumberByType(this IEnumerable<ITimeSheetColumn> timeSheetColumns, short type)
        {
            try { return timeSheetColumns.First(c => c.ColType == type).ColNbr; }
            catch { return 0; }
        }

        public static List<int> GetColumnNumbersByTypes(int timeSheetId, IEnumerable<short?> types, IEnumerable<ITimeSheetColumn> columns)
        {
            return columns.Where(x => x.TimeSheetID == timeSheetId && types.Contains(x.ColType)).Select(x => x.ColNbr).ToList();
        }

        public static short GetLDColumnStatus(string name, ClientTimeSheetProfile profile, short? timeSheetType)
        {
            short result = TimesheetColumnStatus.Hidden;
            bool isEETE = timeSheetType == TimesheetType.EETimeEntry;
            switch (name)
            {
                case LaborDistribution.Label1:
                    result = (isEETE) ? profile.EELaborDistribution1Status : profile.LaborDistribution1Status;
                    break;
                case LaborDistribution.Label2:
                    result = (isEETE) ? profile.EELaborDistribution2Status : profile.LaborDistribution2Status;
                    break;
                case LaborDistribution.Label3:
                    result = (isEETE) ? profile.EELaborDistribution3Status : profile.LaborDistribution3Status;
                    break;
                case LaborDistribution.Label4:
                    result = (isEETE) ? profile.EELaborDistribution4Status : profile.LaborDistribution4Status;
                    break;
                case LaborDistribution.Label5:
                    result = (isEETE) ? profile.EELaborDistribution5Status : profile.LaborDistribution5Status;
                    break;
                case LaborDistribution.Label6:
                    result = (isEETE) ? profile.EELaborDistribution6Status : profile.LaborDistribution6Status;
                    break;
                case LaborDistribution.Label7:
                    result = (isEETE) ? profile.EELaborDistribution7Status : profile.LaborDistribution7Status;
                    break;
                case LaborDistribution.Label8:
                    result = (isEETE) ? profile.EELaborDistribution8Status : profile.LaborDistribution8Status;
                    break;
            }
            return result;
        }

        public static string GetTypeDisplayText(short? type)
        {
            switch (type)
            {
                case TimesheetType.CertifiedByEE:
                    return "Certified By EE";

                case TimesheetType.CertifiedByJob:
                    return "Certified By Job";

                case TimesheetType.EETimeEntry:
                    return "EE Time Entry";

                case TimesheetType.JobCostByEE:
                    return "Job Cost By EE";

                case TimesheetType.JobCostByJob:
                    return "Job Cost By Job";

                case TimesheetType.Regular:
                    return "Regular";

                case TimesheetType.Unknown:
                    return "Unknown";

                default:
                    return string.Empty;
            }
        }

        public static string GetStatusDisplayText(short? status)
        {
            switch (status)
            {
                case TimesheetStatus.Active:
                    return "Active";

                case TimesheetStatus.ApprovePEO:
                    return "Approve PEO";

                case TimesheetStatus.ApproveProcess:
                    return "Approve Process";

                case TimesheetStatus.Deleted:
                    return "Deleted";

                case TimesheetStatus.Edit:
                    return "Edit";

                case TimesheetStatus.Empty:
                    return "Empty";

                case TimesheetStatus.Historical:
                    return "Historical";

                case TimesheetStatus.InPayroll:
                    return "In Payroll";

                case TimesheetStatus.New:
                    return "New";

                case TimesheetStatus.Released:
                    return "Released";

                case TimesheetStatus.Submitted:
                    return "Submitted";

                case TimesheetStatus.Transferred:
                    return "Transferred";

                default:
                    return string.Empty;
            }
        }

        public static bool IsTimeSheetTypeRegular(DarwiNet2._0.Data.TimeSheet timeSheet) =>
            timeSheet.TimeSheetType == TimesheetType.Regular;

        public static bool IsTimeSheetTypeJobCost(DarwiNet2._0.Data.TimeSheet timeSheet) =>
            timeSheet.TimeSheetType == TimesheetType.JobCostByEE || timeSheet.TimeSheetType == TimesheetType.JobCostByJob;

        public static bool IsColumnTypePayCode(short columnType) =>
            columnType == TimesheetColumnType.PayCode;

        public static int GetCheckNumberColumnId(IEnumerable<ITimeSheetColumn> columns) =>
            columns.FirstOrDefault(x => x.Type == TimesheetColumnType.CheckNbr)?.ColID ?? 0;

        public static List<int> FindCodeColumns(IEnumerable<ITimeSheetColumn> columns, IEnumerable<short?> codes) =>
            columns.Where(c => codes.Contains(c.Type))
                   .OrderBy(c => c.ColID)
                   .Select(c => c.ColID)
                   .ToList();

        public static IDictionary<ITimeSheetColumn, short> GetPayCodeColumnBaseCodeTypes(IEnumerable<ITimeSheetColumn> columns)
        {
            if (columns.IsNullOrEmpty())
                return new Dictionary<ITimeSheetColumn, short>();

            return columns
                .Where(x => x.ColType == TimesheetColumnType.PayCode)
                .ToDictionary(
                    keySelector: column => column,
                    elementSelector: column => column.BaseCodeType ?? PaycodeType.Unknown);
        }

        public static List<string> GetExceptionEmployeeIds(int companyId, int timeSheetId, short coltype, string code, short? codetype, string basecode, short? bcodetype, IEnumerable<string> availEEs, IEnumerable<EmployeePaycode> companyPaycodes, short base_ee_cdtype)
        {
            var result = new List<string>();
            var ees = new List<string>();
            var codes = new List<string>();
            var ee_code = string.Empty;
            var ee_cdtype = 0;

            if (coltype == TimesheetColumnType.CodeHours || coltype == TimesheetColumnType.CodeAmount || coltype == TimesheetColumnType.JustAmount)
            {
                if (!(string.IsNullOrEmpty(code) && string.IsNullOrEmpty(basecode)))
                {
                    if (!string.IsNullOrEmpty(basecode))
                    {
                        ee_code = basecode;
                        if (bcodetype == PaycodeType.Hourly) ee_cdtype = PaycodeType.Salary;
                        if (bcodetype == PaycodeType.Salary) ee_cdtype = PaycodeType.Hourly;
                        if (ee_cdtype != 0)
                        {
                            try
                            {
                                var employeePaycodes = companyPaycodes.Where(p => p.PayRecord == code);

                                codes = employeePaycodes
                                    .Where(p => !string.IsNullOrEmpty(p.BasePayRecord) &&
                                                p.BasePayRecord != basecode)
                                    .OrderBy(p => p.EmployeeID)
                                    .Select(p => "|" + p.BasePayRecord + "|")
                                    .ToList();

                                ees = employeePaycodes
                                    .Where(p => p.BasePayRecord == basecode)
                                    .OrderBy(p => p.EmployeeID)
                                    .Select(p => p.EmployeeID)
                                    .ToList();

                                result = companyPaycodes
                                    .Where(p => codes.Contains("|" + p.PayRecord + "|") &&
                                                p.PayType == ee_cdtype)
                                    .Select(p => p.EmployeeID)
                                    .ToList();

                                result = result.Where(e => availEEs.Any(a => a == e) && !ees.Any(a => a == e)).ToList();
                            }
                            catch { result = new List<string>(); }
                        }

                    }
                    else
                    {
                        ee_code = code;
                        if (codetype == PaycodeType.Hourly)
                            ee_cdtype = PaycodeType.Salary;

                        if (codetype == PaycodeType.Salary)
                            ee_cdtype = PaycodeType.Hourly;

                        if (ee_cdtype == 0 && codetype != 0)
                        {
                            ee_cdtype = (base_ee_cdtype == PaycodeType.Hourly) ? PaycodeType.Salary : PaycodeType.Hourly;
                            try
                            {
                                codes = companyPaycodes
                                    .Where(p => p.PayRecord == code &&
                                                !string.IsNullOrEmpty(p.BasePayRecord))
                                    .OrderBy(p => p.EmployeeID)
                                    .Select(p => "|" + p.BasePayRecord + "|")
                                    .Distinct()
                                    .ToList();

                                result = companyPaycodes
                                    .Where(p => codes.Contains("|" + p.PayRecord + "|") &&
                                                p.PayType == ee_cdtype)
                                    .OrderBy(p => p.EmployeeID)
                                    .Select(p => p.EmployeeID)
                                    .ToList();

                                result = result.Where(e => availEEs.Any(a => a == e)).ToList();
                            }
                            catch { result = new List<string>(); }

                        }
                    }
                }
                if (ee_cdtype != 0 && !result.Any())
                {
                    try
                    {
                        result = companyPaycodes.Where(p => p.PayRecord == ee_code && p.PayType == ee_cdtype).OrderBy(p => p.EmployeeID).Select(p => p.EmployeeID).ToList();
                        result = result.Where(e => availEEs.Any(a => a == e)).ToList();
                    }
                    catch { result = new List<string>(); }
                }
            }
            return result;
        }

        public static short GetTimesheetColumnStatus(short type, string name, short stat) =>
            (type != TimesheetType.JobCostByEE) ? stat : (name == "EmployeeID" || name == "EmployeeName") ? TimesheetColumnStatus.Hidden : stat;

        public static List<string> GetActiveEmployeeIds(IEnumerable<TimeSheetEmployeeRowDetails> rows)
        {
            if (rows.IsNullOrEmpty())
                return new List<string>();

            return rows.Where(e => !e.Inactive)
                       .OrderBy(e => e.EmployeeID)
                       .Select(e => e.EmployeeID)
                       .ToList();
        }

        // TODO: this is not related to TimeSheets directly, should be moved to ClientDivisionDetails provider (clientDivisionDetails is not used for anything else in the calling method)
        public static List<string> GetAvailableDepartments(List<ClientDivisionDetail> clientDivisionDetails, List<string> departments, int userSecurityType, List<string> userDivisions)
        {
            switch (userSecurityType)
            {
                case 0:
                    return userDivisions;
                case 1:
                    return clientDivisionDetails.Where(d => userDivisions.Contains(d.DivisionID))
                                                .OrderBy(s => s.Department)
                                                .Select(s => s.Department)
                                                .ToList();
                default:
                    return departments;
            }
        }

        public static List<string> GetTimeSheetEmployees(ITimeSheetInfo timeSheetInfo, string dnetLevel, bool useJustVisible, bool useAllAvailable)
        {
            if (useAllAvailable)
            {
                var availEE = timeSheetInfo.AvailEmployees;
                if (availEE.Any())
                    return availEE.Select(e => e.Code).ToList();
                else
                    return new List<string>();
            }
            else
            {
                if (timeSheetInfo.TsData.Any())
                {
                    return (useJustVisible && dnetLevel != DNetAccessLevel.System)
                        ? timeSheetInfo.TsData.Where(d => d.IsVisible).OrderBy(d => d.EmployeeID).Select(d => d.EmployeeID).Distinct().ToList()
                        : timeSheetInfo.TsData.OrderBy(d => d.EmployeeID).Select(d => d.EmployeeID).Distinct().ToList();
                }
                else
                {
                    return new List<string>();
                }
            }
        }

        public static string[] GetDepartments(ITimeSheetInfo timesheetInfo, List<Code_Description> departments)
        {
            try
            {
                var clientId = timesheetInfo.ClientId;
                if (timesheetInfo.TsSetup.UseDpDescr)
                {
                    return departments
                        .OrderBy(d => d.Code)
                        .Select(d => d.Code + "|" + d.Code + " (" + FieldTranslation.GetCodeDescription(CodeDescriptionTypes.Department, clientId, d.Code, d.Description, false) + ")")
                        .ToArray();
                }
                return departments.Select(d => d.Code).ToArray();
            }
            catch (Exception ex)
            {
                return new string[] { };
            }
        }

        public static string[] GetPositions(ITimeSheetInfo timesheetInfo, List<Code_Description> positions)
        {
            try
            {
                var clientId = timesheetInfo.ClientId;
                if (timesheetInfo.TsSetup.UsePsDescr)
                {
                    return positions
                        .OrderBy(p => p.Code)
                        .Select(p => p.Code + "|" + p.Code + " (" + FieldTranslation.GetCodeDescription(CodeDescriptionTypes.Position, clientId, p.Code, p.Description, true) + ")")
                        .ToArray();
                }
                return positions.Select(p => p.Code).ToArray();
            }
            catch
            {
                return new string[] { };
            }
        }

        public static List<ITimeSheetJob> GetTimeSheetJobs(IEnumerable<ITimeSheetRow> timeSheetRows)
        {
            var result = new List<ITimeSheetJob>();
            if (timeSheetRows.IsNullOrEmpty())
                return result;

            foreach (var job in timeSheetRows.OrderBy(d => d.Job).Select(d => d.Job).Distinct())
            {
                var values = job.Split('|');
                result.Add(new TimeSheetJob()
                {
                    JobName = values[0],
                    JobLevel2 = values[1],
                    JobLevel3 = values[2],
                    JobLevel4 = values[3],
                    JobLevel5 = values[4],
                    JobLevel6 = values[5]
                });
            }

            return result;
        }

        public static List<Code_Description> GetTimeSheetJobsByLevel(List<ITimeSheetRow> timeSheetInfoTsData, short level)
        {
            var result = new List<Code_Description>();
            var allJobs = GetTimeSheetJobs(timeSheetInfoTsData);
            List<string> foundJobs;

            switch (level)
            {
                case 1:
                    foundJobs = allJobs.OrderBy(j => j.JobName).Select(j => j.JobName).Distinct().ToList();
                    break;
                case 2:
                    foundJobs = allJobs.OrderBy(j => j.JobName).ThenBy(j => j.JobLevel2).Select(j => j.JobName + "|" + j.JobLevel2).Distinct().ToList();
                    break;
                case 3:
                    foundJobs = allJobs.OrderBy(j => j.JobName).ThenBy(j => j.JobLevel2).ThenBy(j => j.JobLevel3).Select(j => j.JobName + "|" + j.JobLevel2 + "|" + j.JobLevel3).Distinct().ToList();
                    break;
                case 4:
                    foundJobs = allJobs.OrderBy(j => j.JobName).ThenBy(j => j.JobLevel2).ThenBy(j => j.JobLevel3).ThenBy(j => j.JobLevel4).Select(j => j.JobName + "|" + j.JobLevel2 + "|" + j.JobLevel3 + "|" + j.JobLevel4).Distinct().ToList();
                    break;
                case 5:
                    foundJobs = allJobs.OrderBy(j => j.JobName).ThenBy(j => j.JobLevel2).ThenBy(j => j.JobLevel3).ThenBy(j => j.JobLevel4).ThenBy(j => j.JobLevel5).Select(j => j.JobName + "|" + j.JobLevel2 + "|" + j.JobLevel3 + "|" + j.JobLevel4 + "|" + j.JobLevel5).Distinct().ToList();
                    break;
                default:
                    foundJobs = allJobs.OrderBy(j => j.JobName).ThenBy(j => j.JobLevel2).ThenBy(j => j.JobLevel3).ThenBy(j => j.JobLevel4).ThenBy(j => j.JobLevel5).ThenBy(j => j.JobLevel6).Select(j => j.JobName + "|" + j.JobLevel2 + "|" + j.JobLevel3 + "|" + j.JobLevel4 + "|" + j.JobLevel5 + "|" + j.JobLevel6).Distinct().ToList();
                    break;
            }

            foreach (var job in foundJobs)
                result.Add(new Code_Description
                {
                    Code = job,
                    Description = job.Replace("|", ".")
                });

            return result;
        }

        public static List<string> GetTimeSheetCodes(short type, IEnumerable<ITimeSheetColumn> columns)
        {
            switch (type)
            {
                case TimesheetCodeType.Benefit:
                    return columns.Where(c => c.Type == TimesheetColumnType.BenefitCode).OrderBy(c => c.Code).Select(c => c.Code).ToList();
                case TimesheetCodeType.Deduction:
                    return columns.Where(c => c.Type == TimesheetColumnType.DeductionCode).OrderBy(c => c.Code).Select(c => c.Code).ToList();
                default:
                    return columns.Where(c => c.Type == TimesheetColumnType.PayCode).OrderBy(c => c.Code).Select(c => c.Code).ToList();
            }
        }

        public static int GetCheckNumberColumnId(List<ITimeSheetColumn> columns) =>
            columns.FirstOrDefault(x => x.ColType == TimesheetColumnType.CheckNbr)?.ColNbr ?? 0;

        public static List<CodeDetails> FindCodeColumns(List<ITimeSheetColumn> columns, IEnumerable<short?> codeColumnTypes = null)
        {
            if (codeColumnTypes == null)
                codeColumnTypes = CodeColumnTypes;
            var codeDetails = new List<CodeDetails>();
            var codeNames = columns.Where(c => codeColumnTypes.Contains(c.ColType))
                                                              .Select(c => c.Name)
                                                              .ToList();

            foreach (var codeName in codeNames)
            {
                var code = columns.FirstOrDefault(c => c.Name.Equals(codeName));
                var basedOnCodeColumns = columns.Where(c => c.BasedOn.Equals(codeName));
                var codeNumber = code.ColNbr;
                var hoursNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.CodeHours)
                                                                      || c.ColType.Equals(TimesheetColumnType.CodeAmount)
                                                                      || c.ColType.Equals(TimesheetColumnType.JustAmount)).ColNbr;
                var rateNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.Rate)).ColNbr;
                var shiftNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.ShiftCode)).ColNbr;
                var premiumNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.Premium)).ColNbr;
                var reductionNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.Reduction)).ColNbr;
                codeDetails.Add(new CodeDetails
                {
                    CodeColumnNumber = code.ColNbr,
                    HoursColumnNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.CodeHours) || c.ColType.Equals(TimesheetColumnType.CodeAmount) || c.ColType.Equals(TimesheetColumnType.JustAmount)).ColNbr,
                    RateColumnNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.Rate)).ColNbr,
                    ShiftColumnNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.ShiftCode)).ColNbr,
                    PremiumColumnNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.Premium)).ColNbr,
                    ReductionColumnNumber = basedOnCodeColumns.FirstOrDefault(c => c.ColType.Equals(TimesheetColumnType.Reduction)).ColNbr,
                });
            }

            return codeDetails;
        }

        public static TimeSheetRowActionDTO MapTimesheetRowActionViewModelToTimesheetRowActionDTO(TimeSheetRowActionViewModel timeSheetRowActionViewModel)
        {
            return new TimeSheetRowActionDTO()
            {
                TimeSheetRow = MapTimesheetRowViewModelToTimesheetRowDTO(timeSheetRowActionViewModel.TimeSheetRow),
                ActionDTO = MapTimesheetActionViewModelToTimesheetActionDTO(timeSheetRowActionViewModel.ActionViewModel)
            };
        }

        public static TimeSheetRowDTO MapTimesheetRowViewModelToTimesheetRowDTO(TimeSheetRowViewModel timeSheetRowViewModel)
        {
            return new TimeSheetRowDTO()
            {
                RowID = timeSheetRowViewModel.RowID,
                EmployeeID = timeSheetRowViewModel.EmployeeID,
                Job = timeSheetRowViewModel.Job,
                Details = timeSheetRowViewModel.Details,
                IsVisible = timeSheetRowViewModel.IsVisible,
                IsConsolidated = timeSheetRowViewModel.IsConsolidated,
                OriginRow = timeSheetRowViewModel.OriginRow,
                SortValue = timeSheetRowViewModel.SortValue,
                TotalHours = timeSheetRowViewModel.TotalHours,
                TotalGross = timeSheetRowViewModel.TotalGross,
            };
        }

        public static void MapTimesheetRowViewModelToTimesheetRowDTO(TimeSheetRowViewModel source, TimeSheetRowDTO destination)
        {
            destination.RowID = source.RowID;
            destination.EmployeeID = source.EmployeeID;
            destination.Job = source.Job;
            destination.Details = source.Details;
            destination.IsVisible = source.IsVisible;
            destination.IsConsolidated = source.IsConsolidated;
            destination.OriginRow = source.OriginRow;
            destination.SortValue = source.SortValue;
            destination.TotalHours = source.TotalHours;
            destination.TotalGross = source.TotalGross;
        }

        public static TimeSheetActionDTO MapTimesheetActionViewModelToTimesheetActionDTO(TimeSheetActionViewModel timeSheetActionViewModel)
        {
            return new TimeSheetActionDTO()
            {
                Action = timeSheetActionViewModel.Action,
                UpdatedColumns = timeSheetActionViewModel.UpdatedColumns
            };
        }

        public static ITimeSheetColumn MapTimesheetColumnToTimesheetColumnModel(TimeSheetColumn column)
        {
            return new TimeSheetColumnModel()
            {
                BaseCode = column.BaseCode,
                BaseCodeType = column.BaseCodeType,
                BasedOn = column.BasedOn,
                Code = column.Code,
                CodeType = column.CodeType,
                ColID = column.ColNbr,
                ColNbr = column.ColNbr,
                ColType = column.ColType,
                ExceptionEEs = null,
                Label = column.Label,
                Name = column.Name,
                OriginCodeNbr = column.OriginCodeNbr,
                SortOrder = column.SortOrder,
                Status = column.Status ?? 0,
                TimeSheetID = column.TimeSheetID,
                TotalAmount = column.TotalAmount,
                Type = column.ColType,
                ValidationType = column.ValidationType
            };
        }

        public static ITimeSheetSetup MapTimesheetToTimesheetSetup(DarwiNet2._0.Data.TimeSheet timeSheet)
        {
            return new TimeSheetSetup()
            {
                CompanyID = timeSheet.CompanyID,
                ClientID = timeSheet.ClientID,
                TsID = timeSheet.TimeSheetID,
                DNetUrl = timeSheet.DNetUrl,
                User = timeSheet.User,
                Type = timeSheet.TimeSheetType ?? TimeSheetTypes.Regular,
                Name = timeSheet.TimeSheetName,
                RangeType = timeSheet.SelectionType ?? TimeSheetSelectionTypes.None,
                Profile = timeSheet.ProfileID,
                DateFrom = timeSheet.DateFrom,
                DateTo = timeSheet.DateTo,
                CheckDate = timeSheet.CheckDate,
                Status = timeSheet.Status ?? 0,
                PageSize = timeSheet.PageSize,
                SortBy = timeSheet.SortBy,
                DefDays = Convert.ToDouble(timeSheet.DefDays ?? 0),
                DefWeeks = Convert.ToDouble(timeSheet.DefWeeks ?? 0),
                DefHours = Convert.ToDouble(timeSheet.DefHours ?? 0),
                MaxRate = Convert.ToDouble(timeSheet.MaxRate ?? 0),
                MaxDayHours = Convert.ToDouble(timeSheet.MaxDayHours ?? 0),
                MaxEEHours = Convert.ToDouble(timeSheet.MaxEEHours ?? 0),
                MaxGrossAmount = Convert.ToDouble(timeSheet.MaxEEGrossAmount ?? 0),
                AllowAddEE = timeSheet.AllowAddEE,
                AllowAddCode = timeSheet.AllowAddCode,
                AllowModifiedRate = timeSheet.AllowModifiedRate,
                AllowModifiedRatePermanent = timeSheet.AllowModifiedRatePermanent,
                PDecimals = timeSheet.PCDecimals ?? 2,
                BDecimals = timeSheet.BenDecimals ?? 2,
                DDecimals = timeSheet.DedDecimals ?? 2,
                PSource = timeSheet.PCSource ?? 0,
                BSource = timeSheet.BenSource ?? 0,
                DSource = timeSheet.DedSource ?? 0,
                MaskSSN = timeSheet.MaskSSN,
                ShowSSN = timeSheet.PrintSocSecOnBlankTS,
                IgnoreWarning = timeSheet.IgnoreWaiting,
                AllowEETime = timeSheet.AllowTimeEntry,
                ShiftStatus = timeSheet.ShiftStatus ?? 0,
                EEDeptStatus = timeSheet.EEDeptStatus ?? 0,
                EEPositionStatus = timeSheet.EEPositionStatus ?? 0,
                EELD1Status = timeSheet.EELaborDistribution1Status,
                EELD2Status = timeSheet.EELaborDistribution2Status,
                EELD3Status = timeSheet.EELaborDistribution3Status,
                EELD4Status = timeSheet.EELaborDistribution4Status,
                EELD5Status = timeSheet.EELaborDistribution5Status,
                EELD6Status = timeSheet.EELaborDistribution6Status,
                EELD7Status = timeSheet.EELaborDistribution7Status,
                EELD8Status = timeSheet.EELaborDistribution8Status,
                UseJBS = timeSheet.UseJBS,
                PrintRate = timeSheet.PrintRateOnBlankTS,
                ApprovalType = timeSheet.ApprovalType,
                ApprovalStatus = timeSheet.ApprovalStatus,
                PEOApproveRequired = timeSheet.PEOApprovalRequired,
                Comment = timeSheet.Comment,
                UseDpDescr = timeSheet.UseDepDescr,
                UsePsDescr = timeSheet.UsePosDescr,
                AutoSave = timeSheet.AutoSaveTime,
                PayrollProfile = timeSheet.PayrollProfileID,
                PayrollNumber = timeSheet.PayrollNumber,
                PayPeriod = timeSheet.PayPeriod ?? 0,
                TRXEmployees = timeSheet.TRXEmployees ?? 0
            };
        }

        public static List<TimeSheetRowActionDTO> ConvertToDTO(IEnumerable<TimeSheetRowActionViewModel> timeSheetRowData)
        {
            var list = new List<TimeSheetRowActionDTO>();

            if (timeSheetRowData.IsNullOrEmpty())
                return list;

            list.AddRange(timeSheetRowData.Select(timeSheetRow => MapTimesheetRowActionViewModelToTimesheetRowActionDTO(timeSheetRow)));

            return list;
        }

        public static List<short> GetTimeSheetSortCompareFields(short? sortBy)
        {
            var compareFields = new List<short>();
            switch (sortBy)
            {
                case TimeSheetSortTypes.Name:
                    compareFields.Add(TimesheetColumnType.EEName);
                    break;
                case TimeSheetSortTypes.EmployeeID:
                    compareFields.Add(TimesheetColumnType.EmployeeID);
                    break;
                case TimeSheetSortTypes.Department:
                    compareFields.Add(TimesheetColumnType.Department);
                    break;
                case TimeSheetSortTypes.Position:
                    compareFields.Add(TimesheetColumnType.Position);
                    break;
                case TimeSheetSortTypes.DeptPlusName:
                    compareFields.Add(TimesheetColumnType.Department);
                    compareFields.Add(TimesheetColumnType.EEName);
                    break;
                case TimeSheetSortTypes.DeptPlusEEID:
                    compareFields.Add(TimesheetColumnType.Department);
                    compareFields.Add(TimesheetColumnType.EmployeeID);
                    break;
                case TimeSheetSortTypes.PositionPlusName:
                    compareFields.Add(TimesheetColumnType.Position);
                    compareFields.Add(TimesheetColumnType.EEName);
                    break;
                case TimeSheetSortTypes.PositionPlusEEID:
                    compareFields.Add(TimesheetColumnType.Position);
                    compareFields.Add(TimesheetColumnType.EmployeeID);
                    break;
                default:
                    compareFields.Add(TimesheetColumnType.EEName);
                    break;
            }
            return compareFields;
        }

        public static short GetColumnStatus(short type, ClientTimeSheetProfile profile)
        {
            short result = TimesheetColumnStatus.ReadOnly;
            short pr_type = profile.ProfileType ?? TimesheetType.Regular;
            bool isCert = pr_type == TimesheetType.CertifiedByEE || pr_type == TimesheetType.CertifiedByJob;
            bool isJobCost = pr_type == TimesheetType.JobCostByEE || pr_type == TimesheetType.JobCostByJob;
            switch (type)
            {
                case TimesheetColumnType.BenefitCode:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.CheckNbr:
                    result = (profile.HideCheckNumber) ? TimesheetColumnStatus.Hidden : TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.CodeAmount:
                    result = TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.BusinessExpAmount:
                    result = TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.JustAmount:
                    result = TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.CodeHours:
                    result = TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.Days:
                    result = (profile.HideDays) ? TimesheetColumnStatus.Hidden : TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.DateDay:
                    if (isCert) result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.DeductionCode:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.Department:
                    result = profile.DepartmentStatus ?? TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.EmployeeID:
                    result = (profile.TSSort == TimesheetSortType.EmployeeID || profile.TSSort == TimesheetSortType.DeptPlusEEID || profile.TSSort == TimesheetSortType.PositionPlusEEID) ? TimesheetColumnStatus.ReadOnly : TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.EENotes:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.EESSN:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.LocalTax:
                    result = profile.LocalTaxStatus ?? TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.OfferedHours:
                    result = (profile.HideOfferedHours) ? TimesheetColumnStatus.Hidden : TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.OTPayCode:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.PayCode:
                    result = (!isJobCost) ? TimesheetColumnStatus.Hidden : TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.Position:
                    result = profile.PositionStatus ?? TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.Premium:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.Rate:
                    result = ((bool)profile.PrintRateOnBlankTS) ? TimesheetColumnStatus.HideEditable : TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.Reduction:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.RegPayCode:
                    result = (!isJobCost) ? TimesheetColumnStatus.Hidden : TimesheetColumnStatus.Editable; ;
                    break;
                case TimesheetColumnType.SalaryLine:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.ShiftCode:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.StateTax:
                    result = profile.StateTaxStatus ?? TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.SutaState:
                    result = profile.SutaStateStatus ?? TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.TimeSheetID:
                    result = TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.Weeks:
                    result = (profile.HideWeeks) ? TimesheetColumnStatus.Hidden : TimesheetColumnStatus.Editable;
                    break;
                case TimesheetColumnType.WorkersComp:
                    result = profile.WCStatus ?? TimesheetColumnStatus.Hidden;
                    break;
                case TimesheetColumnType.VarPayCode:
                    result = TimesheetColumnStatus.Editable;
                    break;
            }
            return result;
        }
    }
}