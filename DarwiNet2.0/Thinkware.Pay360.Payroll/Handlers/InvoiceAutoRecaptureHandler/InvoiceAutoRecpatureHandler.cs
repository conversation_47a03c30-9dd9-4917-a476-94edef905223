using Autofac;
using DarwiNet2._0;
using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Enumerations;
using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using Thinkware.Cohesion.Payroll;
using Thinkware.Cohesion.Payroll.Processor;
using Thinkware.Pay360.SignalR;
using static ComponentSpace.SAML2.SAMLIdentifiers;

namespace Thinkware.Pay360.Payroll
{
    public class InvoiceAutoRecaptureHandler
    {
        private readonly DnetEntities _dbContext;
        private DnetEntities _rawDbContext;

        public InvoiceAutoRecaptureHandler(
            DnetEntities dbContext)
        {
            _dbContext = dbContext;
        }

        public void Handle(InvoiceAutoRecaptureRequest request)
        {
            PayrollNumber oPayrollNumber = PayrollNumber.Parse(request.PayrollNumber);
            PayrollWorkMaster payrollWorkMaster = _dbContext.PayrollWorkMasters.FirstOrDefault(p => p.PayrollNumber == request.PayrollNumber);
            PayrollWorkHeader payrollWorkHeader = _dbContext.PayrollWorkHeaders.FirstOrDefault(x => x.PayrollNumber == request.PayrollNumber);
            Invoice invoice = _dbContext.Invoices.FirstOrDefault(i => i.CompanyID == oPayrollNumber.CompanyId && i.ClientID == oPayrollNumber.ClientId && i.DarwinInvoiceNumber == request.DarwinInvoiceNumber); ;
            Client client = _dbContext.Clients.FirstOrDefault(c => c.CompanyID == oPayrollNumber.CompanyId && c.ClientID == oPayrollNumber.ClientId);
            PayrollProcessOption payrollProcessOptions = _dbContext.PayrollProcessOptions.FirstOrDefault(x => x.PayrollNumber == request.PayrollNumber);

            if (!payrollProcessOptions.IgnoreMissedCodes && !payrollWorkMaster.IgnoreBlendedRates && (payrollWorkMaster.PayRunIncludesBenefits > 0 || payrollWorkMaster.PayRunIncludesDeductions > 0))
            {
                var profileEmployees = _dbContext.PayrollProfileEmployees
                .Join(_dbContext.Employees,
                    pe => new { pe.EmployeeID, pe.CompanyID },
                    ee => new { ee.EmployeeID, ee.CompanyID },
                    (pe, ee) => new { pe, ee })
                .Where(pe => pe.pe.CompanyID == oPayrollNumber.CompanyId &&
                             pe.pe.ClientID == oPayrollNumber.ClientId &&
                             pe.pe.ProfileID == oPayrollNumber.ProfileId &&
                             !pe.ee.Inactive)
                .Select(pe => pe.ee.EmployeeID)
                .ToList();

                var paidEmployees = _dbContext.PayrollWorkHeaders
                    .Where(h => h.PayrollNumber == request.PayrollNumber &&
                                !h.IsRemoved)
                    .Select(h => h.EmployeeID)
                    .ToList();

                var unpaidProfileEmployees = profileEmployees
                    .Where(e => !paidEmployees.Contains(e))
                    .ToList();

                using (var scope = IoC.BeginLifetimeScope())
                {
                    _rawDbContext = scope.Resolve<DnetEntities>();

                    if (unpaidProfileEmployees.Any())
                    {
                        foreach (var employeeID in unpaidProfileEmployees)
                        {
                            var employeeLOAMaster = _dbContext.EmployeeLOAMasters
                                .FirstOrDefault(m => m.CompanyID == oPayrollNumber.CompanyId &&
                                                     m.EmployeeID == employeeID);
                            if (employeeLOAMaster == null ||
                                payrollWorkMaster.PayRunStartDate > employeeLOAMaster.EndDate ||
                                payrollWorkMaster.PayRunEndDate < employeeLOAMaster.StartDate ||
                                employeeLOAMaster.Allow_Payroll == true)
                            {
                                var employee = _dbContext.Employees.FirstOrDefault(e => e.CompanyID == oPayrollNumber.CompanyId && e.EmployeeID == employeeID);
                                if (employee.StartDate <= payrollWorkMaster.PayRunEndDate || employee.OriginalHireDate <= payrollWorkMaster.PayRunEndDate)
                                {
                                    var employeeDeductions = _dbContext.EmployeeDeductions
                                        .Where(d => d.CompanyID == oPayrollNumber.CompanyId &&
                                                    d.EmployeeID == employeeID &&
                                                    !d.Inactive &&
                                                    d.VariableDeduction == false &&
                                                    d.AutomaticRecapture == true &&
                                                    d.DeductionMethod == DeductionMethod.FixedAmount &&
                                                    d.DeductionFormula == 0 &&
                                                    d.DeductionBegDate <= payrollWorkMaster.PayRunEndDate &&
                                                    (d.DeductionEndDate >= payrollWorkMaster.PayRunStartDate ||
                                                    d.DeductionEndDate == new DateTime(1900, 1, 1, 0, 0, 0, 0)));

                                    var payrollProfileDetailsDeductions = _dbContext.PayrollProfileSettingDetails
                                        .Where(p => p.CompanyID == oPayrollNumber.CompanyId &&
                                                    p.ClientID == oPayrollNumber.ClientId &&
                                                    p.ProfileID == oPayrollNumber.ProfileId &&
                                                    p.PayrollRecordType == (short)PayrollRecordType.Deduction)
                                        .Select(p => p.PayrollCode);
                                    foreach (var deduction in employeeDeductions)
                                    {
                                        if (payrollProfileDetailsDeductions.Contains(deduction.Deduction))
                                        {
                                            PayrollWorkMissedCode missedCode = new PayrollWorkMissedCode();
                                            decimal recoverAmount = deduction.DeductionAmount1 ?? 0;
                                            var employeeTransactionHistories = _dbContext.EmployeeTransactionHistories
                                                .Where(h => h.CompanyID == oPayrollNumber.CompanyId &&
                                                            h.EmployeeID == employeeID &&
                                                            h.PayrollRecordType == (short)PayrollRecordType.Deduction &&
                                                            h.PayrollCode == deduction.Deduction);

                                            if (deduction.DeductionLifetimeMax != 0 && (deduction.DeductionLifetimeMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)deduction.DeductionLifetimeMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }
                                            ;
                                            employeeTransactionHistories = employeeTransactionHistories.Where(h => h.CheckDate.Value.Year.Equals(DateTime.Now.Year));
                                            if (deduction.DeductionYearMax != 0 && (deduction.DeductionYearMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)deduction.DeductionYearMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }
                                            employeeTransactionHistories = employeeTransactionHistories.Where(h => (h.CheckDate.Value.Month >= 1 && h.CheckDate.Value.Month <= 3 && new[] { 1, 2, 3 }.Contains(h.CheckDate.Value.Month)) ||
                                                                                                                   (h.CheckDate.Value.Month >= 4 && h.CheckDate.Value.Month <= 6 && new[] { 4, 5, 6 }.Contains(h.CheckDate.Value.Month)) ||
                                                                                                                   (h.CheckDate.Value.Month >= 7 && h.CheckDate.Value.Month <= 9 && new[] { 7, 8, 9 }.Contains(h.CheckDate.Value.Month)) ||
                                                                                                                   (h.CheckDate.Value.Month >= 10 && h.CheckDate.Value.Month <= 12 && new[] { 10, 11, 12 }.Contains(h.CheckDate.Value.Month)));
                                            if (deduction.DeductionQtrMax != 0 && (deduction.DeductionQtrMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)deduction.DeductionQtrMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }
                                            employeeTransactionHistories = employeeTransactionHistories.Where(h => h.CheckDate.Value.Month.Equals(DateTime.Now.Month));
                                            if (deduction.DeductionMonthMax != 0 && (deduction.DeductionMonthMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)deduction.DeductionMonthMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }

                                            missedCode.RecoverAmount = recoverAmount;

                                            if (deduction.AmountsArray1 != 0 && deduction.DeductionAmount1 != 0)
                                            {
                                                missedCode.AgencyRecoverAmount = (recoverAmount / deduction.DeductionAmount1) * deduction.AmountsArray1;
                                            }

                                            missedCode.PayrollNumber = request.PayrollNumber;
                                            missedCode.EmployeeID = employeeID;
                                            missedCode.PayrollRecordType = (int)PayrollRecordType.Deduction;
                                            missedCode.PayrollCode = deduction.Deduction;
                                            missedCode.PayRunType = 0;
                                            missedCode.BenefitMethod = 4;
                                            missedCode.TimeRemaining = deduction.TimeRemaining.HasValue ? (short)deduction.TimeRemaining.Value : (short)0;
                                            missedCode.SnapshotID = payrollWorkHeader != null ? payrollWorkHeader.SnapshotID : "";
                                            missedCode.CompanyID = oPayrollNumber.CompanyId;
                                            missedCode.ClientID = oPayrollNumber.ClientId;
                                            missedCode.ProfileID = oPayrollNumber.ProfileId;
                                            missedCode.UserID = GlobalVariables.DNETOwnerID;
                                            missedCode.DarwinInvoiceNumber = (int)request.DarwinInvoiceNumber;
                                            //SDJ CH-788: set recapture option if using client default
                                            if (deduction.RecaptureOptions == 3) //use client default
                                            {
                                                switch (client.RecaptureOptions)
                                                {
                                                    case 1: //from client
                                                        missedCode.CodeRecaptureOption = 2;
                                                        break;
                                                    case 2: //from employee
                                                        missedCode.CodeRecaptureOption = 1;
                                                        break;
                                                    case 3: //from client then employee
                                                        missedCode.CodeRecaptureOption = 4;
                                                        break;
                                                    default:
                                                        missedCode.CodeRecaptureOption = 2;
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                missedCode.CodeRecaptureOption = deduction.RecaptureOptions;
                                            }

                                            missedCode.IsLatest = true;

                                            if (deduction.AgencyType != 0)
                                            {
                                                InvoiceAgencyCreditMissedCodesHistory missedCodeHistory = new InvoiceAgencyCreditMissedCodesHistory()
                                                {
                                                    CompanyID = oPayrollNumber.CompanyId,
                                                    ClientID = oPayrollNumber.ClientId,
                                                    DarwinInvoiceNumber = (int)request.DarwinInvoiceNumber,
                                                    EmployeeID = employeeID,
                                                    AuditControlCode = payrollWorkMaster.AuditControlCode,
                                                    PayrollCode = deduction.Deduction,
                                                    DBFlag = "D",
                                                    InvoiceNumber = invoice?.InvoiceNumber,
                                                    Department = employee.Department,
                                                    Position = employee.Position,
                                                    ClientVendor = deduction.AgencyType == 1 ? "V" : "C",
                                                    PayrollNumber = request.PayrollNumber,
                                                    Total = 0,
                                                    Tax = 0,
                                                };

                                                var salesTaxScheduleAssignments = _dbContext.SalesTaxScheduleAssignments
                                                    .Where(a => a.CompanyID == oPayrollNumber.CompanyId &&
                                                                a.TaxScheduleID == deduction.TaxScheduleID);
                                                foreach (var salesTaxScheduleAssignment in salesTaxScheduleAssignments)
                                                {
                                                    decimal taxAmount = 0;
                                                    var salesTaxSetup = _dbContext.SalesTaxSetups.FirstOrDefault(s => s.CompanyID == oPayrollNumber.CompanyId && s.TaxDetailID == salesTaxScheduleAssignment.TaxDetailID);
                                                    if (salesTaxSetup.TaxDetailBase == 3)
                                                    {
                                                        if (salesTaxSetup.TaxDetailPercent > 0)
                                                        {
                                                            var percent = (decimal)salesTaxSetup.TaxDetailPercent / 100;
                                                            if (salesTaxSetup.TaxDetailTaxableMaximum > missedCode.RecoverAmount)
                                                            {
                                                                taxAmount = (decimal)salesTaxSetup.TaxDetailTaxableMaximum * percent;
                                                            }
                                                            else if (salesTaxSetup.TaxDetailTaxableMinimum > missedCode.RecoverAmount)
                                                            {
                                                                taxAmount = 0;
                                                            }
                                                            else
                                                            {
                                                                taxAmount = (decimal)missedCode.RecoverAmount * percent;
                                                            }
                                                            if (salesTaxSetup.TaxDetailTaxableMaximum < taxAmount)
                                                            {
                                                                taxAmount = (decimal)salesTaxSetup.TaxDetailTaxableMaximum;
                                                            }
                                                            else if (salesTaxSetup.TaxDetailTaxableMinimum > taxAmount)
                                                            {
                                                                taxAmount = (decimal)salesTaxSetup.TaxDetailTaxableMinimum;
                                                            }
                                                            if (taxAmount < 0)
                                                            {
                                                                taxAmount = 0;
                                                            }
                                                        }
                                                    }
                                                    missedCodeHistory.Tax += taxAmount;
                                                    missedCodeHistory.Total += missedCode.RecoverAmount;
                                                }
                                                SaveInvoiceAgencyCreditMissedCodeHistory(missedCodeHistory);
                                            }
                                            SaveMissedCode(missedCode);
                                        }
                                    }

                                    var employeeBenefits = _dbContext.EmployeeBenefits
                                            .Where(b =>
                                                b.CompanyID == oPayrollNumber.CompanyId &&
                                                b.EmployeeID == employeeID &&
                                                !b.Inactive && 
                                                b.VariableBenefit == false &&
                                                b.AutomaticRecapture == true &&
                                                b.BenefitMethod == BenefitMethod.FixedAmount &&
                                                b.BenefitFormula == 0 &&
                                                b.BenefitBeginDate <= payrollWorkMaster.PayRunEndDate &&
                                                (b.BenefitEndDate >= payrollWorkMaster.PayRunStartDate ||
                                                b.BenefitEndDate == new DateTime(1900, 1, 1, 0, 0, 0, 0)));

                                    var payrollProfileDetailsBenefits = _dbContext.PayrollProfileSettingDetails
                                        .Where(p => p.CompanyID == oPayrollNumber.CompanyId &&
                                                    p.ClientID == oPayrollNumber.ClientId &&
                                                    p.ProfileID == oPayrollNumber.ProfileId &&
                                                    p.PayrollRecordType == (short)PayrollRecordType.Benefit)
                                        .Select(p => p.PayrollCode);
                                    foreach (var benefit in employeeBenefits)
                                    {
                                        if (payrollProfileDetailsBenefits.Contains(benefit.Benefit))
                                        {
                                            PayrollWorkMissedCode missedCode = new PayrollWorkMissedCode();
                                            decimal recoverAmount = benefit.BenefitAmount1 ?? 0;
                                            var employeeTransactionHistories = _dbContext.EmployeeTransactionHistories
                                                .Where(h => h.CompanyID == oPayrollNumber.CompanyId &&
                                                            h.EmployeeID == employeeID &&
                                                            h.PayrollRecordType == (short)PayrollRecordType.Benefit &&
                                                            h.PayrollCode == benefit.Benefit);

                                            if (benefit.BenefitLifetimeMax != 0 && (benefit.BenefitLifetimeMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)benefit.BenefitLifetimeMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }
                                            ;
                                            employeeTransactionHistories = employeeTransactionHistories.Where(h => h.CheckDate.Value.Year.Equals(DateTime.Now.Year));
                                            if (benefit.BenefitYearMax != 0 && (benefit.BenefitYearMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)benefit.BenefitYearMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }
                                            employeeTransactionHistories = employeeTransactionHistories.Where(h => (h.CheckDate.Value.Month >= 1 && h.CheckDate.Value.Month <= 3 && new[] { 1, 2, 3 }.Contains(h.CheckDate.Value.Month)) ||
                                                                                                                   (h.CheckDate.Value.Month >= 4 && h.CheckDate.Value.Month <= 6 && new[] { 4, 5, 6 }.Contains(h.CheckDate.Value.Month)) ||
                                                                                                                   (h.CheckDate.Value.Month >= 7 && h.CheckDate.Value.Month <= 9 && new[] { 7, 8, 9 }.Contains(h.CheckDate.Value.Month)) ||
                                                                                                                   (h.CheckDate.Value.Month >= 10 && h.CheckDate.Value.Month <= 12 && new[] { 10, 11, 12 }.Contains(h.CheckDate.Value.Month)));
                                            if (benefit.BenefitQtrMax != 0 && (benefit.BenefitQtrMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)benefit.BenefitQtrMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }
                                            employeeTransactionHistories = employeeTransactionHistories.Where(h => h.CheckDate.Value.Month.Equals(DateTime.Now.Month));
                                            if (benefit.BenefitMonthMax != 0 && (benefit.BenefitMonthMax - employeeTransactionHistories.Sum(h => h.TRXAmount)) < recoverAmount)
                                            {
                                                recoverAmount = (decimal)benefit.BenefitMonthMax - (decimal)employeeTransactionHistories.Sum(h => h.TRXAmount);
                                            }

                                            missedCode.RecoverAmount = recoverAmount;

                                            if (benefit.AmountsArray1 != 0 && benefit.BenefitAmount1 != 0)
                                            {
                                                missedCode.AgencyRecoverAmount = (recoverAmount / benefit.BenefitAmount1) * benefit.AmountsArray1;
                                            }

                                            missedCode.PayrollNumber = request.PayrollNumber;
                                            missedCode.EmployeeID = employeeID;
                                            missedCode.PayrollRecordType = (int)PayrollRecordType.Benefit;
                                            missedCode.PayrollCode = benefit.Benefit;
                                            missedCode.PayRunType = 0;
                                            missedCode.BenefitMethod = 4;
                                            missedCode.TimeRemaining = (short)benefit.TimeRemaining;
                                            missedCode.SnapshotID = payrollWorkHeader != null ? payrollWorkHeader.SnapshotID : "";
                                            missedCode.CompanyID = oPayrollNumber.CompanyId;
                                            missedCode.ClientID = oPayrollNumber.ClientId;
                                            missedCode.ProfileID = oPayrollNumber.ProfileId;
                                            missedCode.UserID = GlobalVariables.DNETOwnerID;
                                            missedCode.DarwinInvoiceNumber = (int)request.DarwinInvoiceNumber;
                                            //SDJ CH-788: set recapture option if using client default
                                            if (benefit.RecaptureOptions == 3) //use client default
                                            {
                                                switch (client.RecaptureOptions)
                                                {
                                                    case 1: //from client
                                                        missedCode.CodeRecaptureOption = 2;
                                                        break;
                                                    case 2: //from employee
                                                        missedCode.CodeRecaptureOption = 1;
                                                        break;
                                                    case 3: //from client then employee
                                                        missedCode.CodeRecaptureOption = 4;
                                                        break;
                                                    default:
                                                        missedCode.CodeRecaptureOption = 2;
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                missedCode.CodeRecaptureOption = benefit.RecaptureOptions;
                                            }
                                            missedCode.IsLatest = true;

                                            if (benefit.AgencyType != 0)
                                            {
                                                InvoiceAgencyCreditMissedCodesHistory missedCodeHistory = new InvoiceAgencyCreditMissedCodesHistory()
                                                {
                                                    CompanyID = oPayrollNumber.CompanyId,
                                                    ClientID = oPayrollNumber.ClientId,
                                                    DarwinInvoiceNumber = (int)request.DarwinInvoiceNumber,
                                                    EmployeeID = employeeID,
                                                    AuditControlCode = payrollWorkMaster.AuditControlCode,
                                                    PayrollCode = benefit.Benefit,
                                                    DBFlag = "B",
                                                    InvoiceNumber = invoice?.InvoiceNumber,
                                                    Department = employee.Department,
                                                    Position = employee.Position,
                                                    ClientVendor = benefit.AgencyType == 1 ? "V" : "C",
                                                    PayrollNumber = request.PayrollNumber,
                                                    Total = 0,
                                                    Tax = 0,
                                                };

                                                var salesTaxScheduleAssignments = _dbContext.SalesTaxScheduleAssignments
                                                    .Where(a => a.CompanyID == oPayrollNumber.CompanyId &&
                                                                a.TaxScheduleID == benefit.TaxScheduleID);
                                                foreach (var salesTaxScheduleAssignment in salesTaxScheduleAssignments)
                                                {
                                                    decimal taxAmount = 0;
                                                    var salesTaxSetup = _dbContext.SalesTaxSetups.FirstOrDefault(s => s.CompanyID == oPayrollNumber.CompanyId && s.TaxDetailID == salesTaxScheduleAssignment.TaxDetailID);
                                                    if (salesTaxSetup.TaxDetailBase == 3)
                                                    {
                                                        if (salesTaxSetup.TaxDetailPercent > 0)
                                                        {
                                                            var percent = (decimal)salesTaxSetup.TaxDetailPercent / 100;
                                                            if (salesTaxSetup.TaxDetailTaxableMaximum > missedCode.RecoverAmount)
                                                            {
                                                                taxAmount = (decimal)salesTaxSetup.TaxDetailTaxableMaximum * percent;
                                                            }
                                                            else if (salesTaxSetup.TaxDetailTaxableMinimum > missedCode.RecoverAmount)
                                                            {
                                                                taxAmount = 0;
                                                            }
                                                            else
                                                            {
                                                                taxAmount = (decimal)missedCode.RecoverAmount * percent;
                                                            }
                                                            if (salesTaxSetup.TaxDetailTaxableMaximum < taxAmount)
                                                            {
                                                                taxAmount = (decimal)salesTaxSetup.TaxDetailTaxableMaximum;
                                                            }
                                                            else if (salesTaxSetup.TaxDetailTaxableMinimum > taxAmount)
                                                            {
                                                                taxAmount = (decimal)salesTaxSetup.TaxDetailTaxableMinimum;
                                                            }
                                                            if (taxAmount < 0)
                                                            {
                                                                taxAmount = 0;
                                                            }
                                                        }
                                                    }
                                                    missedCodeHistory.Tax += taxAmount;
                                                    missedCodeHistory.Total += missedCode.RecoverAmount;
                                                }
                                                SaveInvoiceAgencyCreditMissedCodeHistory(missedCodeHistory);
                                            }
                                            SaveMissedCode(missedCode);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        private List<int> GetQuarterMonths(int month)
        {
            int quarter = (int)Math.Ceiling(month / 3.0);
            switch (quarter)
            {
                case 1:
                    return new List<int> { 1, 2, 3 };
                case 2:
                    return new List<int> { 4, 5, 6 };
                case 3:
                    return new List<int> { 7, 8, 9 };
                case 4:
                    return new List<int> { 10, 11, 12 };
                default:
                    return null;
            }
        }

        private void SaveMissedCode(PayrollWorkMissedCode missedCode)
        {
            if (missedCode != null)
            {
                string sSQL = "IF NOT EXISTS (SELECT 1 FROM dbo.PayrollWorkMissedCodes " +
                              "WHERE CompanyID = @companyId " +
                              "AND EmployeeID = @employeeId " +
                              "AND PayRunType = @payRunType " +
                              "AND PayrollRecordType = @payrollRecordType " +
                              "AND PayrollCode = @payrollCode " +
                              "AND PayrollNumber = @payrollNumber " +
                              "AND SnapshotID = @snapshotId) " +
                              "INSERT INTO dbo.PayrollWorkMissedCodes " +
                              "([CompanyID], [UserID], [PayRunType], [ProfileID], [EmployeeID], [PayrollRecordType], [PayrollCode], [Amount], [RecoverAmount], [AgencyRecoverAmount], " +
                              "[RecaptureAmount], [AgencyRecaptureAmount], [ManualCheckPrinted], [RecapturedFromClient], [ClientBenefitAmount], [IncludeInYearLimit], [AttemptedAmount], " +
                              "[PayrollNumber], [SnapshotID], [IsLatest], [AuditControlCode], [ClientID], [BenefitMethod], [TimeRemaining], [CodeRecaptureOption], [Print], [Updated], [DarwinINvoiceNumber]) " +
                              "VALUES " +
                              "(@companyID, @userID, @payRunType, @profileID, @employeeID, @payrollRecordType, @payrollCode, @amount, @recoverAmount, @agencyRecoverAmount, " +
                              "@recaptureAmount, @agencyRecaptureAmount, @manualCheckPrinted, @recapturedFromClient, @clientBenefitAmount, @includeInYearLimit, @attemptedAmount, " +
                              "@payrollNumber, @snapshotID, @isLatest, @auditControlCode, @clientID, @benefitMethod, @timeRemaining, @codeRecaptureOption, @print, @updated, @darwinInvoiceNumber)";

                _rawDbContext.Database.ExecuteSqlCommand(
                    sSQL,
                    new SqlParameter("@companyID", missedCode.CompanyID),
                    new SqlParameter("@userID", missedCode.UserID ?? ""),
                    new SqlParameter("@payRunType", missedCode.PayRunType),
                    new SqlParameter("@profileID", missedCode.ProfileID),
                    new SqlParameter("@employeeID", missedCode.EmployeeID),
                    new SqlParameter("@payrollRecordType", missedCode.PayrollRecordType),
                    new SqlParameter("@payrollCode", missedCode.PayrollCode),
                    new SqlParameter("@amount", missedCode.Amount ?? 0),
                    new SqlParameter("@recoverAmount", missedCode.RecoverAmount ?? 0),
                    new SqlParameter("@agencyRecoverAmount", missedCode.AgencyRecoverAmount ?? 0),
                    new SqlParameter("@recaptureAmount", missedCode.RecaptureAmount ?? 0),
                    new SqlParameter("@agencyRecaptureAmount", missedCode.AgencyRecaptureAmount ?? 0),
                    new SqlParameter("@manualCheckPrinted", missedCode.ManualCheckPrinted),
                    new SqlParameter("@recapturedFromClient", missedCode.RecapturedFromClient),
                    new SqlParameter("@clientBenefitAmount", missedCode.ClientBenefitAmount ?? 0),
                    new SqlParameter("@includeInYearLimit", missedCode.IncludeInYearLimit),
                    new SqlParameter("@attemptedAmount", missedCode.AttemptedAmount ?? 0),
                    new SqlParameter("@payrollNumber", missedCode.PayrollNumber),
                    new SqlParameter("@snapshotID", missedCode.SnapshotID),
                    new SqlParameter("@isLatest", missedCode.IsLatest),
                    new SqlParameter("@auditControlCode", missedCode.AuditControlCode ?? ""),
                    new SqlParameter("@clientID", missedCode.ClientID),
                    new SqlParameter("@benefitMethod", missedCode.BenefitMethod),
                    new SqlParameter("@timeRemaining", missedCode.TimeRemaining),
                    new SqlParameter("@codeRecaptureOption", missedCode.CodeRecaptureOption),
                    new SqlParameter("@print", missedCode.Print),
                    new SqlParameter("@updated", missedCode.Updated),
                    new SqlParameter("@darwinInvoiceNumber", missedCode.DarwinInvoiceNumber ?? 0)
                );
            }
        }

        private void SaveInvoiceAgencyCreditMissedCodeHistory(InvoiceAgencyCreditMissedCodesHistory missedCodeHistory)
        {
            if (missedCodeHistory != null)
            {
                string sSQL = "IF NOT EXISTS (SELECT 1 FROM dbo.InvoiceAgencyCreditMissedCodesHistory " +
                              "WHERE CompanyID = @companyId " +
                              "AND ClientID = @clientId " +
                              "AND DarwinInvoiceNumber = @darwinInvoiceNumber " +
                              "AND EmployeeID = @employeeId " +
                              "AND AuditControlCode = @auditControlCode " +
                              "AND PayrollCode = @payrollCode " +
                              "AND DBFlag = @dbFlag) " +
                              "INSERT INTO dbo.InvoiceAgencyCreditMissedCodesHistory " +
                              "([CompanyID], [ClientID], [DarwinInvoiceNumber], [EmployeeID], [AuditControlCode], [PayrollCode], [DBFlag], [Updated], [InvoiceNumber], " +
                              "[ClientVendor], [Total], [Department], [Position], [Tax], [PayrollNumber], [MergedInvoiceNumber]) " +
                              "VALUES " +
                              "(@companyID, @clientId, @darwinInvoiceNumber, @employeeId, @auditControlCode, @payrollCode, @dbFlag, @updated, @invoiceNumber, " +
                              "@clientVendor, @total, @department, @position, @tax, @payrollNumber, @mergedInvoiceNumber)";

                _rawDbContext.Database.ExecuteSqlCommand(
                    sSQL,
                    new SqlParameter("@companyID", missedCodeHistory.CompanyID),
                    new SqlParameter("@clientId", missedCodeHistory.ClientID),
                    new SqlParameter("@darwinInvoiceNumber", missedCodeHistory.DarwinInvoiceNumber),
                    new SqlParameter("@employeeId", missedCodeHistory.EmployeeID ?? ""),
                    new SqlParameter("@auditControlCode", missedCodeHistory.AuditControlCode ?? ""),
                    new SqlParameter("@payrollCode", missedCodeHistory.PayrollCode ?? ""),
                    new SqlParameter("@dbFlag", missedCodeHistory.DBFlag ?? ""),
                    new SqlParameter("@updated", missedCodeHistory.Updated),
                    new SqlParameter("@invoiceNumber", missedCodeHistory.InvoiceNumber ?? ""),
                    new SqlParameter("@clientVendor", missedCodeHistory.ClientVendor ?? ""),
                    new SqlParameter("@total", missedCodeHistory.Total ?? 0),
                    new SqlParameter("@department", missedCodeHistory.Department ?? ""),
                    new SqlParameter("@position", missedCodeHistory.Position ?? ""),
                    new SqlParameter("@tax", missedCodeHistory.Tax ?? 0),
                    new SqlParameter("@payrollNumber", missedCodeHistory.PayrollNumber ?? ""),
                    new SqlParameter("@mergedInvoiceNumber", missedCodeHistory.MergedInvoiceNumber)
                );
            }
        }
    }
}