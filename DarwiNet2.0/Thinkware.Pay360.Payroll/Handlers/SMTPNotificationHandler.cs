using DarwiNet2._0.Controllers;
using DarwiNet2._0.Core;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Services;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using Thinkware.Cohesion.Handlers;
using Thinkware.Pay360.Caching;
using Thinkware.Pay360.Payroll;

namespace Thinkware.Cohesion.Payroll
{
    public class SMTPNotificationHandler : IHandler<SMTPNotificationRequest>
    {
        private readonly DnetEntities _dbContext;
        private readonly List<string> payrollNotificationTypes;
        private readonly List<string> invoiceNotificationTypes;
        protected string _connectionString;

        private readonly string  _dnetUrl = "";

        // SMTP Mail Properties
        private readonly ProjectSetupDTO _projectSetup;
        private readonly string _fromAddress;
        private readonly string _fromName;
        private readonly string _smtpUsername;
        private readonly string _smtpPassword;
        private readonly string _host;
        private readonly int _port;

        public SMTPNotificationHandler(DnetEntities dbContext,
                                       string dnetUrl = "") 
        {
            _dbContext = dbContext;

            string dbDnetUrl = _dbContext.ProjectSetups.FirstOrDefault().DnetAddress;
            _dnetUrl = string.IsNullOrEmpty(dnetUrl) ? dbDnetUrl : dnetUrl;

            var getDB = new GetDB();
            _connectionString = getDB.GetConnection();

            _projectSetup = _dbContext.ProjectSetups
            .Select(s => new ProjectSetupDTO
            {
                DnetMailHost = s.DnetMailHost,
                DnetMailSSL = s.DnetMailSSL,
                DnetMailFromName = s.DnetMailFromName,
                DnetMailFromAddress = s.DnetMailFromAddress,
                DnetMailUser = s.DnetMailUser,
                DnetMailPassword = s.DnetMailPassword,
                UseCustomMail = s.UseCustomMail,
                CustMailHost = s.CustMailHost,
                CustHostLogin = s.CustHostLogin,
                CustHostPWD = s.CustHostPWD,
                CustMailFromName = s.CustMailFromName,
                CustMailFromAddress = s.CustMailFromAddress,
                CustMailPort = s.CustMailPort
            })
            .FirstOrDefault();

            if (!_projectSetup.UseCustomMail)
            {
                _fromAddress = _projectSetup.DnetMailFromAddress;
                _fromName = _projectSetup.DnetMailFromName;
                _smtpUsername = _projectSetup.DnetMailUser.Trim();
                _smtpPassword = _projectSetup.DnetMailPassword.Trim();
                _host = _projectSetup.DnetMailHost.Trim();
                _port = 587;
            }
            else
            {
                _fromAddress = _projectSetup.CustMailFromAddress;
                _fromName = _projectSetup.CustMailFromName;
                _smtpUsername = _projectSetup.CustHostLogin.Trim();
                _smtpPassword = _projectSetup.CustHostPWD.Trim();
                _host = _projectSetup.CustMailHost.Trim();
                _port = int.TryParse(_projectSetup.CustMailPort, out int customPort) ? customPort : 587;
            }

            payrollNotificationTypes = new List<string>()
            {
                Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.PAYROLL_CALC_COMPLETED,
                Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.PAYROLL_DECLINED,
                Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.PAYROLL_FINALIZE_COMPLETED
            };
            invoiceNotificationTypes = new List<string>()
            {
                Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.INVOICE_CREATED,
                Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.INVOICE_DECLINED,
                Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.INVOICE_FINALIZE_COMPLETED,
            };
        }

        public void Handle(SMTPNotificationRequest request)
        {
            var snapshotId = _dbContext.PayrollSnapshots
                .FirstOrDefault(s => s.PayrollNumber == request.PayrollNumber)
                .SnapshotID;
            if (payrollNotificationTypes.Contains(request.NotificationType))
            {
                bool notifyPayrollNotifyOnPayrollNotificationType = _dbContext.PayrollApprovalSetups
                                                                        .Join(_dbContext.ClientPayrollSchedules,
                                                                            s => new { s.CompanyID, s.ClientID, s.ProfileID },
                                                                            c => new { c.CompanyID, c.ClientID, c.ProfileID },
                                                                            (s, c) => new { s, c })
                                                                        .Where(sc => sc.c.PayrollNumber == request.PayrollNumber &&
                                                                                sc.s.ApprovalType == request.NotificationType)
                                                                        .Any();
                if (notifyPayrollNotifyOnPayrollNotificationType)
                {
                    var schedule = _dbContext.ClientPayrollSchedules.FirstOrDefault(x => x.PayrollNumber == request.PayrollNumber);
                    var payrollApprovalSetup = _dbContext.PayrollApprovalSetups
                                                    .FirstOrDefault(s => s.CompanyID == schedule.CompanyID &&
                                                                        s.ClientID == schedule.ClientID &&
                                                                        s.ProfileID == schedule.ProfileID &&
                                                                        s.ApprovalType == request.NotificationType);
                    string subject = GetEmailSubject(request);
                    string emailMessage = FillSmartTags(request.PayrollNumber, schedule.CompanyID, schedule.ClientID, schedule.ProfileID, request.InvoiceNumber, request.RejectMessage, payrollApprovalSetup.EmailMessage);
                    List<SMTPMessage> messages = new List<SMTPMessage>();
                    foreach (PayrollApprovalRecipient payrollApprovalRecipient in payrollApprovalSetup.PayrollApprovalRecipients)
                    {
                        if (!payrollApprovalRecipient.Inactive)
                        {
                            UserModel recipient =  new UserModel
                            {
                                UserID = payrollApprovalRecipient.RecipientID,
                                Name = payrollApprovalRecipient.User.Name,
                                Email = payrollApprovalRecipient.User.Email
                            };
                            var message = CreateSMTPMessage(recipient, subject, emailMessage);
                            var approvalExpirationHours = payrollApprovalSetup.ApprovalExpirationHours ?? 0;
                            message.Expiration = approvalExpirationHours > 0 ? DateTime.Now.AddHours(approvalExpirationHours) : DateTime.MaxValue;
                            messages.Add(message);
                        }
                    }
                    SendSMTPMail(messages, request.PayrollNumber, snapshotId);
                }
            }
            else if (invoiceNotificationTypes.Contains(request.NotificationType))
            {
                bool notifyInvoiceNotifyOnPayrollNotificationType = _dbContext.InvoiceApprovalSetups
                                                                        .Join(_dbContext.ClientPayrollSchedules,
                                                                            s => new { s.CompanyID, s.ClientID, s.ProfileID },
                                                                            c => new { c.CompanyID, c.ClientID, c.ProfileID },
                                                                            (s, c) => new { s, c })
                                                                        .Where(sc => sc.c.PayrollNumber == request.PayrollNumber &&
                                                                                        sc.s.ApprovalType == request.NotificationType)
                                                                        .Any();
                if (notifyInvoiceNotifyOnPayrollNotificationType)
                {
                    var schedule = _dbContext.ClientPayrollSchedules.FirstOrDefault(x => x.PayrollNumber == request.PayrollNumber);
                    List<InvoicePayroll> invoicePayrolls = _dbContext.InvoicePayrolls.Where(x => x.PayrollNumber == request.PayrollNumber).ToList();
                    List<SMTPMessage> messages = new List<SMTPMessage>();
                    foreach (InvoicePayroll invoicePayroll in invoicePayrolls)
                    {
                        var invoiceApprovalSetup = _dbContext.InvoiceApprovalSetups
                                                                .FirstOrDefault(s => s.CompanyID == schedule.CompanyID &&
                                                                                        s.ClientID == schedule.ClientID &&
                                                                                        s.ProfileID == schedule.ProfileID &&
                                                                                        s.ApprovalType == request.NotificationType);
                        string subject = GetEmailSubject(request, invoicePayroll.DarwinInvoiceNumber);
                        string emailMessage = FillSmartTags(request.PayrollNumber, schedule.CompanyID, schedule.ClientID, schedule.ProfileID, invoicePayroll.DarwinInvoiceNumber, request.RejectMessage, invoiceApprovalSetup.EmailMessage);
                        foreach (InvoiceApprovalRecipient invoiceApprovalRecipient in invoiceApprovalSetup.InvoiceApprovalRecipients)
                        {
                            if (!invoiceApprovalRecipient.Inactive)
                            {
                                UserModel recipient = new UserModel
                                {
                                    UserID = invoiceApprovalRecipient.RecipientID,
                                    Name = invoiceApprovalRecipient.User.Name,
                                    Email = invoiceApprovalRecipient.User.Email
                                };
                                var message = CreateSMTPMessage(recipient, subject, emailMessage);
                                var approvalExpirationHours = invoiceApprovalSetup.ApprovalExpirationHours ?? 0;
                                message.Expiration = approvalExpirationHours > 0 ? DateTime.Now.AddHours(approvalExpirationHours) : DateTime.MaxValue;
                                messages.Add(message);
                            }
                        }
                    }
                    SendSMTPMail(messages, request.PayrollNumber, snapshotId);
                }
            }
        }

        #region Private Methods

        private string GetEmailSubject(SMTPNotificationRequest request, int invoiceNumber = 0)
        {
            PayrollNumber oPayrollNumber = PayrollNumber.Parse(request.PayrollNumber);
            var clientName = GetClientName(oPayrollNumber.CompanyId, oPayrollNumber.ClientId);
            if (clientName == null)
            {
                clientName = "";
            }
            else
            {
                clientName = " - " + clientName;
            }
            var profileIdAndCheckDate = GetProfileIdAndCheckDate(request.PayrollNumber);
            switch (request.NotificationType)
            {
                case Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.PAYROLL_CALC_COMPLETED:
                    return $"Payroll Calculation Complete: {oPayrollNumber.ClientId}{clientName} - {profileIdAndCheckDate.Item1} - {profileIdAndCheckDate.Item2}";
                case Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.PAYROLL_FINALIZE_COMPLETED:
                    return $"Payroll Finalized: {oPayrollNumber.ClientId}{clientName} - {profileIdAndCheckDate.Item1} - {profileIdAndCheckDate.Item2}";
                case Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.PAYROLL_DECLINED:
                    return $"Payroll Approval Declined: {oPayrollNumber.ClientId}{clientName} - {profileIdAndCheckDate.Item1} - {profileIdAndCheckDate.Item2}";
                case Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.INVOICE_CREATED:
                    return $"Invoice {invoiceNumber} Created: {oPayrollNumber.ClientId}{clientName} - {profileIdAndCheckDate.Item1} - {profileIdAndCheckDate.Item2}";
                case Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.INVOICE_FINALIZE_COMPLETED:
                    return $"Invoice {invoiceNumber} Finalized: {oPayrollNumber.ClientId}{clientName} - {profileIdAndCheckDate.Item1} - {profileIdAndCheckDate.Item2}";
                case Constants.PayrollProfileApprovalTypes.PayrollProfileApprovalNotificationTypes.INVOICE_DECLINED:
                    return $"Invoice {invoiceNumber} Declined: {oPayrollNumber.ClientId}{clientName} - {profileIdAndCheckDate.Item1} - {profileIdAndCheckDate.Item2}";
                default:
                    return "";
            }
        }
        private string GetClientName(int companyID, string clientID)
        {
            return _dbContext.Clients
                        .FirstOrDefault(c =>
                            c.ClientID == clientID &&
                            c.CompanyID == companyID)
                        ?.ClientName;
        }

        private Tuple<string, string> GetProfileIdAndCheckDate(string payrollNumber)
        {
            var clientPayrollSchedule = _dbContext.ClientPayrollSchedules
                .FirstOrDefault(s => s.PayrollNumber == payrollNumber);
            DateTime checkDate = (DateTime)clientPayrollSchedule.CheckDate;
            return new Tuple<string, string>(clientPayrollSchedule.ProfileID, checkDate.ToString("d"));
        }

        private string FillSmartTags(string payrollNumber, int companyID, string clientID, string profileID, int invoiceNumber, string rejectMessage, string emailMessage)
        {
            if (emailMessage == null) emailMessage = "";

            if (!string.IsNullOrWhiteSpace(emailMessage))
            {
                return FillSmartTags(companyID, clientID, profileID, payrollNumber, invoiceNumber, rejectMessage, emailMessage);
            }

            return emailMessage;
        }

        private string FillSmartTags(int companyId, string clientId, string profileId, string payrollNumber, int invoiceNumber, string rejectMessage, string messageText)
        {
            // get position of all smart tags from emailMessage
            IEnumerable<int> smartTagPositions = Regex.Matches(messageText, Regex.Escape("[")).Cast<Match>().Select(m => m.Index);

            List<string> smartTags = new List<string>();
            foreach (var smartTagPosition in smartTagPositions)
            {
                string smartTag = messageText.Substring(smartTagPosition + 1, Regex.Match(messageText.Substring(smartTagPosition), Regex.Escape("]")).Index - 1);

                smartTags.Add(smartTag);
            }

            var smartTagData = GetSmartTagData(smartTags);
            foreach (SmartTagModel smartTag in smartTagData)
            {
                string value = GetSmartTagValue(smartTag, companyId, clientId, profileId, payrollNumber, invoiceNumber, rejectMessage);
                value = FormatSmartTagValue(value, smartTag.Name);
                messageText = InsertSmartTagValue(messageText, smartTag.Name, value);
            }

            return messageText;
        }

        private string FormatSmartTagValue(string value, string name)
        {
            if (name == "CheckTotal" || name == "EETotal")
            {
                return value;
            }
            if (Decimal.TryParse(value, out decimal decimalValue))
            {
                return decimalValue.ToString("C");
            }
            if (DateTime.TryParse(value, out DateTime dateTimeValue))
            {
                return dateTimeValue.ToString("d");
            }
            return value;
        }

        private IEnumerable<SmartTagModel> GetSmartTagData(List<string> smartTags)
        {
            return _dbContext.SmartTags
                    .Where(t => smartTags.Contains(t.Name))
                    .Select(t => new SmartTagModel
                    {
                        Name = t.Name,
                        TableName = t.TableName,
                        ColumnName = t.ColumnName,
                        Text = t.Text,
                        QueryFilter = t.QueryFilter,
                        CompanyIDFilter = t.CompanyIDFilter,
                        ClientIDFilter = t.ClientIDFilter,
                        ProfileIDFilter = t.ProfileIDFilter,
                        PayrollNumberFilter = t.PayrollNumberFilter,
                        AddressCodeFilter = t.AddressCodeFilter
                    });
        }

        private string GetSmartTagValue(SmartTagModel smartTag, int companyId, string clientId, string profileId, string payrollNumber, int invoiceNumber, string rejectMessage)
        {
            string value = "";

            if (smartTag.Text != null)
            {
                value = smartTag.Text;
            }
            else if (smartTag.TableName != null && smartTag.ColumnName != null)
            {
                value = GetDynamicValueByFilter(smartTag, companyId, clientId, profileId, payrollNumber);
            }
            else if (smartTag.Name == "PayrollNumber")
            {
                value = payrollNumber;
            }
            else if (smartTag.Name == "CompanyID")
            {
                value = companyId.ToString();
            }
            else if (smartTag.Name == "ClientID")
            {
                value = clientId;
            }
            else if (smartTag.Name == "CompanyID")
            {
                value = companyId.ToString();
            }
            else if (smartTag.Name == "InvoiceNumber")
            {
                value = invoiceNumber.ToString();
            }
            else if (smartTag.Name == "RejectReason")
            {
                value = rejectMessage.ToString();
            }
            else if (smartTag.Name == "InvoiceReviewLink")
            {
                value = CreateInvoiceReviewLink(payrollNumber, invoiceNumber);
            }
            return value;
        }

        private string GetDynamicValueByFilter(SmartTagModel smartTag, int companyId, string clientId, string profileId, string payrollNumber)
        {
            var parameters = new List<SqlParameter>();
            string sql = GetDynamicValueSqlByFilter(smartTag, companyId, clientId, profileId, payrollNumber, out parameters);

            if (parameters.Count == 0)
            {
                parameters = null;
            }
            var result = ExecuteScalar(sql, System.Data.CommandType.Text, parameters.ToArray());

            string value = "";
            if (result != null && !string.IsNullOrEmpty(result.ToString()))
            {
                Type type = GetTypeByProperty(smartTag.TableName, smartTag.ColumnName);
                value = CastValueByType(type, result);
            }

            return value.ToString();
        }

        private string GetDynamicValueSqlByFilter(SmartTagModel smartTag, int companyId, string clientId, string profileId, string payrollNumber, out List<SqlParameter> parameters)
        {
            parameters = new List<SqlParameter>();

            // build sql statement
            string sql = $"SELECT {smartTag.ColumnName} FROM {smartTag.TableName}";

            if (smartTag.QueryFilter != null || smartTag.CompanyIDFilter || smartTag.ClientIDFilter || smartTag.ProfileIDFilter || smartTag.PayrollNumberFilter || smartTag.AddressCodeFilter)
            {
                sql += " WHERE";
                int filterCount = 0;

                if (!string.IsNullOrEmpty(smartTag.QueryFilter))
                {
                    sql += $" {smartTag.QueryFilter}";
                    filterCount++;
                }

                if (smartTag.CompanyIDFilter)
                {
                    sql += AppendFilter(filterCount, "CompanyID");
                    parameters.Add(new SqlParameter("CompanyID", companyId));
                    filterCount++;
                }

                if (smartTag.ClientIDFilter)
                {
                    sql += AppendFilter(filterCount, "ClientID");
                    parameters.Add(new SqlParameter("ClientID", clientId));
                    filterCount++;
                }

                if (smartTag.ProfileIDFilter)
                {
                    sql += AppendFilter(filterCount, "ProfileID");
                    parameters.Add(new SqlParameter("ProfileID", profileId));
                    filterCount++;
                }

                if (smartTag.PayrollNumberFilter)
                {
                    sql += AppendFilter(filterCount, "PayrollNumber");
                    parameters.Add(new SqlParameter("PayrollNumber", payrollNumber));
                    filterCount++;
                }

                if (smartTag.AddressCodeFilter)
                {
                    string addressCode = _dbContext.Clients.Where(c => c.CompanyID == companyId && c.ClientID == clientId).Select(c => c.AddressCode).FirstOrDefault();

                    sql += AppendFilter(filterCount, "AddressCode");
                    parameters.Add(new SqlParameter("AddressCode", addressCode));
                    //filterCount++;
                }
            }

            return sql;
        }

        private string CreateInvoiceReviewLink(string payrollNumber, int invoiceNumber)
        {
            string host = _dnetUrl;
            if (!host.EndsWith("/"))
            {
                host = host + "/";
            }
            string invoiceReviewUri = $"{host}PayrollInvoicesReview/Review?payrollNumber={payrollNumber}&invoiceNumber={invoiceNumber}";

            return $"<a href='{invoiceReviewUri}'>Invoice Review</a>";
        }

        private string AppendFilter(int filterCount, string fieldName)
        {
            string filter = "";

            if (filterCount > 0)
            {
                filter += " AND";
            }

            filter += $" {fieldName} = @{fieldName}";

            return filter;
        }

        private Type GetTypeByProperty(string tableName, string columnName)
        {
            if (columnName.StartsWith("COUNT") || columnName.StartsWith("SUM"))
            {
                return typeof(int);
            }

            // this can be done programatically with entity framework
            switch (tableName)
            {
                case "Companies":
                    return GetPropertyType<Company>(columnName);
                case "Clients":
                    return GetPropertyType<Client>(columnName);
                case "ClientPayrollSchedules":
                    return GetPropertyType<ClientPayrollSchedule>(columnName);
                case "ClientAddresses":
                    return GetPropertyType<ClientAddress>(columnName);
                case "PayrollWorkHeaders":
                    return GetPropertyType<PayrollWorkHeader>(columnName);
                default:
                    return typeof(string);
            }
        }

        private Type GetPropertyType<T>(string property)
        {
            Type Type = typeof(T);
            var propInfo = Type.GetProperty(property);
            return propInfo.PropertyType;
        }

        private string CastValueByType(Type type, dynamic value)
        {
            if (type == typeof(DateTime))
            {
                return (DateTime.Parse(value.ToString())).ToShortDateString();
            }

            return value.ToString();
        }

        private Object ExecuteScalar(string commandText, CommandType commandType, SqlParameter[] parameters)
        {
            using (SqlConnection connection = new SqlConnection(_connectionString))
            {
                using (SqlCommand command = new SqlCommand(commandText, connection))
                {
                    command.CommandType = commandType;

                    if (parameters != null && parameters.Length > 0)
                    {
                        command.Parameters.AddRange(parameters);
                    }

                    connection.Open();
                    return command.ExecuteScalar();
                }
            }
        }

        private string InsertSmartTagValue(string messageText, string smartTagName, string value)
        {
            if (smartTagName.IndexOf('[') == 0)
            {
                return messageText.Replace(smartTagName, value);
            }

            return messageText.Replace("[" + smartTagName + "]", value);
        }

        private SMTPMessage CreateSMTPMessage(UserModel recipient, string subject, string body)
        {
            if (RecipientDetails(body))
            {
                body = InsertRecipientDetails(body, recipient.UserID, recipient.Name);
            }

            var message = CreateMailMessage(recipient.Email, subject, body);

            return message;
        }

        private bool RecipientDetails(string body)
        {
            return (Regex.Match(body, @"\[RecipientName\]").Success ||
                    Regex.Match(body, @"\[RecipientUserID\]").Success);
        }

        private string InsertRecipientDetails(string messageText, string recipientUserId, string recipientName)
        {
            messageText = InsertSmartTagValue(messageText, "RecipientName", recipientName);
            messageText = InsertSmartTagValue(messageText, "RecipientUserID", recipientUserId);

            return messageText;
        }

        private void SendSMTPMail(List<SMTPMessage> messages, string payrollNumber, string snapshotId)
        {
            var connectionString = _dbContext.Database.Connection.ConnectionString;
            new SMTPSendMailHandler().Handle(new SMTPSendMailRequest(messages, _smtpUsername, _smtpPassword, _host, _port, connectionString, payrollNumber, snapshotId));
        }

        private SMTPMessage CreateMailMessage(string toAddress, string subject, string body)
        {
            SMTPMessage message = new SMTPMessage();
            message.FromAddress = _fromAddress;
            message.FromName = _fromName;
            message.Subject = subject;
            message.Body = body;
            message.ToAddress = toAddress;
            message.InitialSendTimestamp = DateTime.Now;

            return message;
        }

        #endregion

    }
}