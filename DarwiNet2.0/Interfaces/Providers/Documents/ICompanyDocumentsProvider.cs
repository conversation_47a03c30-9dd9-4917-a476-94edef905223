using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Providers.Documents
{
    interface ICompanyDocumentsProvider
    {
        CompanyDocument GetDocumentById(int companyid, int id);
        List<CompanyDocument> DocumentsList(int companyid, string clientid = "");
        IEnumerable<CompanyDocument> GetCompanyDocuments(int companyid, string clientid = "");
        int AddDocument(CompanyDocument doc);
        void SaveDocument();
        void CheckSavedDocument(int companyid, int id, int maprec, bool mapped);
        void DeleteDocumentHeader(int companyid, int id);
        string GetDocumentClient(int companyid, int id);
        List<int> RemovableDocuments(int companyid, string clientid = "");
    }
}
