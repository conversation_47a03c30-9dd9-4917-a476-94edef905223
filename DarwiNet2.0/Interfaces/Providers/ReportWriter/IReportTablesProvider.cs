using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Providers.ReportWriter
{
    public interface IReportTablesProvider
    {
        List<Code_Description> AvailableReportObjects(int access);
        List<string> GetMissedParentTables(int access, List<string> tables);
        List<Id_Code_Description> GetObjectsWithRelations(int access, List<string> tables);
        List<ReportDataObject> GetReportTables();
        List<ReportDataObject> GetReportTablesByLevel(int level);
    }
}
