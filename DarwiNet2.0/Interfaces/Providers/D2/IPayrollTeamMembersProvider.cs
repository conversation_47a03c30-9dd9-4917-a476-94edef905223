using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollTeamMembersProvider
    {
        List<PayrollTeamMemberViewModel> GetPayrollTeamMembers(int id);
        List<Code_Description> AvailableTeamMembers(int id);
        void AddTeamMembers(int id, List<string> users);
        void DeleteTeamMembers(int id, List<string> users);
        void SetActiveTeamMember(int id, string userId, bool actflag);
        int GetPayrollTeamIdForUserId(string userId);
        List<int> GetPayrollTeamsForUserId(string userId, bool forApproval = false);

    }
}
