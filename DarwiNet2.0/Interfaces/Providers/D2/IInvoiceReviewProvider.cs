using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceReviewProvider
    {
        List<InvoiceSummaryInfo> GetInvoicesForPayrollNumber(string payrollNumber);
        InvoiceSummaryInfo GetPreviousInvoice(InvoiceSummaryInfo invoice);
        InvoiceSummaryInfo GetInvoiceByInvoiceNumber(int companyId, string clientId, int invoiceNumber);
        List<InvoiceCodes> GetInvoiceWageOverview(InvoiceSummaryInfo invoice, InvoiceSummaryInfo previousInvoice);
        List<InvoiceCodes> GetInvoiceWageUnitOfPayOverview(InvoiceSummaryInfo invoice, InvoiceSummaryInfo previousInvoice);
        List<InvoiceCodes> GetInvoiceBenefitOverview(InvoiceSummaryInfo invoice, InvoiceSummaryInfo previousInvoice);
        List<InvoiceReviewByCode> GetInvoiceBenefitCodes(int companyId, string clientId, int invoiceNumber);
        List<InvoiceReviewEmployeeInfo> GetBenefitEmployeeByCode(int companyId, string clientId, int invoiceNumber, string code);
        List<InvoiceCodes> GetAdminFeeOverview(InvoiceSummaryInfo invoice, InvoiceSummaryInfo previousInvoice);
        List<InvoiceCodes> GetOtherFeeOverview(InvoiceSummaryInfo invoice, InvoiceSummaryInfo previousInvoice);
        List<InvoiceCodes> GetCreditFeeOverview(InvoiceSummaryInfo invoice, InvoiceSummaryInfo previousInvoice);
        List<InvoiceReviewByCode> GetAdminFeeCodes(int companyId, string clientId, int invoiceNumber);
        List<InvoiceReviewEmployeeInfo> GetAdminEmployeeFeeByCode(int companyId, string clientId, int invoiceNumber, string code);
        List<InvoiceReviewByCode> GetOtherFeeCodes(int companyId, string clientId, int invoiceNumber);
        List<InvoiceReviewEmployeeInfo> GetOtherEmployeeFeeByCode(int companyId, string clientId, int invoiceNumber, string code);
        List<InvoiceReviewByCode> GetCreditFeeCodes(int companyId, string clientId, int invoiceNumber);
        List<InvoiceReviewEmployeeInfo> GetCreditEmployeeFeeByCode(int companyId, string clientId, int invoiceNumber, string code);
        TableQueryInfo<InvoiceReviewEmployeeWages> GetInvoiceWageDetails(int companyId, string clientId, int invoiceNumber, Expression<Func<InvoiceReviewEmployeeWages, bool>> filterClause);
        List<InvoiceReviewEmployeeWageCodes> GetInvoiceWageChargeTotalForEmployeeDetailsCodes(int companyId, string clientId, int invoiceNumber, string employeeId);
        decimal? GetUnitOfPayForEmployeeAndPayCode(int companyId, string payrollNumber, string employeeId, string payCode);
        List<InvoiceReviewEmployeeChecks> GetEmployeeCheckDetails(int companyId, string clientId, int invoiceNumber);
        decimal? GetWageTotal(int companyId, string clientId, int invoiceNumber);
        decimal? GetBenefitTotal(int companyId, string clientId, int invoiceNumber);
        decimal? GetFeeTotal(int companyId, string clientId, int invoiceNumber);
        List<InvoiceReviewEmployeeTaxInfo> GetEmployeeTaxDetails(int companyId, string clientId, int invoiceNumber);
        List<AvailableFilter> GetInvoiceEmployees(Expression<Func<InvoiceEmployee, bool>> preFilterClause);
        List<string> GetInvoiceEmployeeIds(Expression<Func<InvoiceEmployee, bool>> filterClause);
    }
}