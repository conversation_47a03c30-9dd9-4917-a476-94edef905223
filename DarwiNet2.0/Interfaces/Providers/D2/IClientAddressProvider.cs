using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IClientAddressProvider
    {
        List<ClientAddress> List(int companyId, string clientId);
        List<Code_Description> ListCodes(int companyId, string clientId);
        List<Code_Description_Selected> GetClientAddresses(int companyId, string clientId, List<string> selectedClientAddressCodes);
        ClientAddress FindByAddressCode(int companyId, string clientId, string addressCode);
        void Update();
        string GetClientPhone(int companyId, string clientId);
    }
}
