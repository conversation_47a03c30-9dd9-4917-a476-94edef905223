using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IUserSupervisorSecurityProvider
    {
        List<UserSupervisorSecurity> List(int companyId, string clientId);
        List<string> ListUserSupervisorSecuritiesUserIds(int companyId, string clientId);
        List<SupervisorSecurity> GetSupervisorSecurities(int companyId, string clientId, string userId);
        List<string> GetSupervisorSecuritiesEmployeeIds(int companyId, string clientId, string userId);
    }
}
