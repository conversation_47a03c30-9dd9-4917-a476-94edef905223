using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollApprovalEmailTokenProvider
    {
        bool IsTokenValid(string payrollNumber, int payrollApprovalSetupSequenceId, string token);
        void SetDateUsed(string payrollNumber, int payrollApprovalSetupSequenceId, string token);
        void DisableEmailToken(int payrollApprovalSetupSequenceId);
        string CreatePayrollApprovalEmailToken(int payrollApprovalSetupSequenceId, string payrollNumber, int approvalTimeframeHours);
        bool HasActivePayrollApprovalEmailToken(string payrollNumber, int payrollApprovalSetupSequenceID);
        void RemoveUnusedEmailToken(string payrollNumber, int payrollApprovalSetupSequenceID);
    }
}
