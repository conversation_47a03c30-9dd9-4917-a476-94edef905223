using DarwiNet2._0.Data;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IDarwinetSetupProvider
    {
        IQueryable<DarwinetSetup> GetByCompanyId(int companyId);
        DarwinetSetup GetDarwinetSetup(int companyId, string clientId);
        bool AllowLoginAs(int companyId, string clientId);
        bool CheckDisabled(int companyId, string clientId);
        int? OENotificationOffset(int companyId, string clientId);
        string OEVerificationAgreement(int companyId, string oeClientId);
        bool? HideEmployerContribution(int companyId, string oeClientId);
        bool? DoNotAllowWaiveForClientOnlyPlans(int companyId, string oeClientId);
        bool? AllowEmployeeEnterAnnualWages(int companyId, string oeClientId);
        string DefaultNewEmployeeRole(int companyId, string clientId);
        bool DisableNotifications(int companyId, string clientId);
    }
}
