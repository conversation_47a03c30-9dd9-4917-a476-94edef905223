using DarwiNet2._0.Data;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollProcessOptionProvider
    {
        PayrollProcessOption GetPayrollProcessOption(string payrollNumber);
        PayrollProcessOption FetchPayrollProcessOptionAndCreateIfNeeded(string payrollNumber);
        bool ProcessOptionIsAutoApprovalsPayroll(string payrollNumber);
        bool ProcessOptionIsAutoApprovalsInvoice(string payrollNumber);
    }
}
