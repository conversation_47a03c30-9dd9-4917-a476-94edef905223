using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceASOSetupAccountHistoryProvider
    {
        void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers);
        List<InvoiceASOAccountSetupHistory> ListInvoiceASOAccountSetupHistoriesByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceASOAccountSetupHistory> ListInvoiceASOAccountSetupHistoriesByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceASOAccountSetupHistory> ListInvoiceASOAccountSetupHistoriesByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void UpdateMergedInvoiceNumberRaw(InvoiceASOAccountSetupHistory invoiceASOAccountSetupHistory, int mergedInvoiceNumber, DnetEntities dbContext = null);
        void SaveInvoiceASOAccountSetupHistory(InvoiceASOAccountSetupHistory invoiceASOAccountSetupHistoryToSave, DnetEntities dbContext = null);
        void DeleteListInvoiceASOAccountSetupHistories(List<InvoiceASOAccountSetupHistory> invoiceASOAccountSetupHistories, DnetEntities dbContext = null);
    }
}