using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IBenefitProvider
    {
        List<Benefit> List(int companyId);
        List<Code_Description_Selected> GetBenefits(int companyId, List<string> selectedBenefitCodes);
        IEnumerable<Code_Description> GetBenefitCodes(int companyId, string clientId);
        List<Benefit> GetBenefitsByCodes(List<string> codes);
        Benefit GetBenefit(int companyID, string benefit);
        List<string> ListUniqueBenefitCodes(int companyID);
    }
}
