using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Providers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IEmployeeAssignedPlanProvider : IDbContextProvider
    {
        List<EmployeeAssignedPlan> FindByEmployee(int companyId, string employeeId, int year);
        List<string> GetActiveEmployeeBenefits(List<EmployeeAssignedPlan> existingPlans);
        List<string> GetActiveEmployeeDeductions(List<EmployeeAssignedPlan> existingPlans);
        void RemoveEmployeeAssignedPlans(int companyId, string employeeId, int year, List<EmployeeAssignedPlan> existingPlans);
        EmployeeAssignedPlan GetGrandfatheredPlan(int companyId, string clientId, string employeeId, string planName, int category, int year);
        bool IsPlanGrandfathered(int companyId, string clientId, string employeeId, string name, int cat, int year);
        EmployeeAssignedPlan CreateNewEmployeeAssignedPlan(EmployeeEnrollmentEligiblePlan plan, ClientPlanHDR cph, EmployeeEnrollmentEmployeeModel currentEmployee, int numberDependents, string planCategories, bool? isLife, EmployeeEnrollmentPlanRateGrid thisEERG, EeAmountErAmountMonthAmount theAmounts, int employeeAge, bool grandfathered, EmployeeAssignedPlan grPlan = null);
        bool IsEmployeePlansActive(int companyId, string employeeId);
        List<EmployeeAssignedPlan> FindAssignedPlansByEmployee(int companyId, string employeeId);
        List<ReactivatedEmployeeAssignedPlansDTO> FindAssignedPlansByEmployeeForFinalPay(int companyId, string employeeId);

        List<EmployeeAssignedPlan> GetEmployeePlanNamesByYear(string employeeID, int yearMax, List<string> plans);
    }
}
