using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceAgencyCreditMissedCodesHistoryProvider
    {
        InvoiceAgencyCreditMissedCodesHistory GetByPK(int companyID, string clientID, int darwinInvoiceNumber, string employeeID, string auditControlCode, string payrollCode, string dbFlag, DnetEntities dbContext = null);
        InvoiceAgencyCreditMissedCodesHistory GetByPK(InvoiceAgencyCreditMissedCodesHistory invoiceAgencyCreditMissedCodesHistory, DnetEntities dbContext = null);
        List<InvoiceAgencyCreditMissedCodesHistory> ListInvoiceAgencyCreditMissedCodesHistoriesByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceAgencyCreditMissedCodesHistory> ListInvoiceAgencyCreditMissedCodesHistoriesByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        InvoiceAgencyCreditMissedCodesHistory Add(InvoiceAgencyCreditMissedCodesHistory invoiceAgencyCreditMissedCodesHistory, DnetEntities dbContext = null);
        void SaveListInvoiceAgencyCreditMissedCodesHistories(List<InvoiceAgencyCreditMissedCodesHistory> invoiceAgencyCreditMissedCodesHistoriesToSave, DnetEntities dbContext = null);
        void UpdateMergedInvoiceNumberRaw(InvoiceAgencyCreditMissedCodesHistory invoiceAgencyCreditMissedCodesHistory, int mergedInvoiceNumber, DnetEntities dbContext = null);
        void DeleteListInvoiceAgencyCreditMissedCodesHistories(List<InvoiceAgencyCreditMissedCodesHistory> invoiceAgencyCreditMissedCodesHistories, DnetEntities dbContext = null);
        void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers);
    }
}