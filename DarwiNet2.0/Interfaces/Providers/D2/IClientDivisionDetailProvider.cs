using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IClientDivisionDetailProvider
    {
        List<string> ListDepartmentCodes(int companyId, string clientId);
        List<string> GetClientDivisionDepartments(int companyId, string clientId, string[] divisions);
        List<DivisionShort> GetDivisionsShortByCompanyAndClient(int companyId, string clientId);
        List<ClientDivisionDetail> GetAllClientDivisionDetails(int companyId, string clientId);
        List<string> GetTimeSheetDepartmentDivisions(int companyId, string clientId, List<string> depts);
        string GetDivisionIdByClientIdDepartment(int companyId, string clientId, string department);
    }
}
