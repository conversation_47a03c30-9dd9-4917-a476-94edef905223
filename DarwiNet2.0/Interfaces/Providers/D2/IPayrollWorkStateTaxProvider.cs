using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Models.D2.EditPayrollCalculation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using static DarwiNet2._0.DTOs.PayrollReviewTaxesDTO;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollWorkStateTaxProvider
    {
        List<StateWithholdingDetail> GetStateTaxes(string employeeId, string payrollNumber);
        List<PayrollWorkStateTaxModel> GetStateTaxes(string payrollNumber, HashSet<string> employeeIds = null);
        void Update(string employeeId, string name, decimal amount);
        void Add(string employeeId, string stateCode, decimal amount);
        void Delete(string payrollNumber, string snapshotId, string employeeId, string stateTaxCode);
        List<PayrollWorkStateTax> GetStateTaxTotals(string payrollNumber);
        List<string> GetPayrollPreviewStateTaxes(string payrollNumber);
        List<string> GetPayrollPreviewStateTaxes(string payrollNumber, string employeeId);
        List<PayrollWorkStateTax> ListStateTaxes(string payrollNumber, string employeeID, DnetEntities context = null);
        PayrollWorkStateTax GetStateTaxes(int companyID, string payrollNumber, string employeeID, string code, DnetEntities context = null);
        void SaveVoided(List<PayrollWorkStateTax> voidedPayrollWorkStateTaxes, DnetEntities context);
        int GetPreviousVoidCount(string payrollNumber, string employeeID);
        void DeleteFromPayroll(string payrollNumber);
        void Delete(string payrollNumber, string employeeID);
        PayrollWorkStateTaxTotals GetStateTaxTotalsByEmployee(string payrollNumber, string employeeID);
        decimal GetTotalTaxableWages(string payrollNumber, string employeeID);
        int GetNextTRXNumber(string payrollNumber, string employeeID);
        void SavePayrollWorkStateTaxes(List<PayrollWorkStateTax> payrollWorkStateTaxes);
        void RemovePayrollWorkStateTaxes(List<PayrollWorkStateTax> stateTaxes, DnetEntities context = null);
    }
}