using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IDeductionProvider
    {
        IEnumerable<Code_Description> GetDeductions(int companyId, string clientId);
        List<Deduction> FindDeductionsByCodes(int companyId, List<string> deductionCodes);
        List<Deduction> List(int companyId);
        Deduction GetDeduction(int companyID, string deduction);
        List<string> ListUniqueDeductionCodes(int companyID);
    }
}
