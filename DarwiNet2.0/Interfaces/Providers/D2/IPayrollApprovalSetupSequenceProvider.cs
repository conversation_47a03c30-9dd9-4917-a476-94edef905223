using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollApprovalSetupSequenceProvider
    {
        IEnumerable<PayrollApprovalSetupSequence> GetPayrollApprovalSetupSequences(IEnumerable<int> payrollApprovalSetupSequenceIds);
        PayrollApprovalSetupSequence GetPayrollApprovalSetupSequence(int payrollApprovalSetupSequenceId);
    }
}
