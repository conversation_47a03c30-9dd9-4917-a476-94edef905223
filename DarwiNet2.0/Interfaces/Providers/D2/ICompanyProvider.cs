using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface ICompanyProvider
    {
        Company FindCompanyById(int companyId);
        string FindCompanyNameById(int companyId);
        Dictionary<int, string> FindCompanyNamesByIds(IEnumerable<int> companyIds);
        List<Company> GetCompanies();
        List<CompanyShort> GetCompaniesShort();
        List<Code_Description> GetCompaniesAvailableToUser(string userID);
        Dictionary<int, string> GetCompanyNames();
        Dictionary<int, Code_Description> GetCompanyNamesWithIds();
        string GetDNetClientId(int companyId);
        string GetFullDNetCompany(int companyId);
        string GetDNetUrl();
        Code_Description GetCompanyIntercompanyIdWithNameById(int companyId);
        CompanyContactDetailsModel GetCompanyContactDetails(int companyId);
        bool HasSwipeClockCredentials(int companyId);
        string GetReprintPassword(int companyId);
        bool CheckElectronicConsent(int companyId);
        string FindInterCompanyById(int companyId);
        List<Company> FindCompanyUserAssignedToWithSmsPolicy(string userName);
    }
}
