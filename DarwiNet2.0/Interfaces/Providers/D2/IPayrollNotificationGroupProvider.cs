using DarwiNet2._0.DTOs;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using DarwiNet2._0.ViewModels.D2;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollNotificationGroupProvider
    {
        IQueryable<PayrollNotificationGroup> ListGroups();
        List<PayrollNotificationGroupModel> ListPayrollNotificationGroups();
        List<PayrollNotificationGroupModel> ListInvoiceNotificationGroups();
        PayrollNotificationGroup GetById(int payrollNotificationGroupId);
        int Create(PayrollNotificationGroup payrollNotificationGroup);
        void Update(PayrollNotificationGroup payrollNotificationGroup, string groupName, bool shared);
        void Delete(PayrollNotificationGroup payrollNotificationGroup);
        TableQueryInfo<ExternallApprovalGroupViewModel> GetExternalApprovalGroups(TableFilter filters);
        TableQueryInfo<ExternallApprovalGroupViewModel> GetExternalInvoiceApprovalGroups(TableFilter filters);
        void AddGroupWithUsers(string groupName, List<MultiselectOptionsDTO> groupUsers);
        void AddInvoiceGroupWithUsers(string groupName, List<MultiselectOptionsDTO> groupUsers);
    }
}
