using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceClientMinimumBillingHistoryProvider
    {
        void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers);
        List<InvoiceClientMinimumBillingHistory> ListInvoiceClientMinimumBillingHistoriesByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceClientMinimumBillingHistory> ListInvoiceClientMinimumBillingHistoriesByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceClientMinimumBillingHistory> ListInvoiceClientMinimumBillingHistoriesByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void UpdateMergedInvoiceNumberRaw(InvoiceClientMinimumBillingHistory invoiceClientMinimumBillingHistory, int mergedInvoiceNumber, DnetEntities dbContext = null);
        void SaveInvoiceClientMinimumBillingHistory(InvoiceClientMinimumBillingHistory invoiceClientMinimumBillingHistoryToSave, DnetEntities dbContext = null);
        void DeleteListInvoiceClientMinimumBillingHistories(List<InvoiceClientMinimumBillingHistory> invoiceClientMinimumBillingHistories, DnetEntities dbContext = null);
    }
}