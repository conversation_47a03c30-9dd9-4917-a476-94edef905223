using DarwiNet2._0.Data;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollProfileVarianceProvider
    {
        List<PayrollProfileVariance> List(int companyId, string clientId, string profileId);
        void Update(int companyId, string clientId, string profileId, List<PayrollProfileVariance> variances);
        bool HasProfileVariances(int companyID, string clientID, string profileID, string varianceType);
    }
}
