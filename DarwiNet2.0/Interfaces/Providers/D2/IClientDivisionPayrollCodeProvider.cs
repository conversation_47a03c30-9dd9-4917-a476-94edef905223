using DarwiNet2._0.Controllers;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using Kendo.Mvc.UI;
using DarwiNet2._0.DTOs;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IClientDivisionPayrollCodeProvider
    {
        List<IGrouping<string, ClientDivisionPayrollCode>> List(int companyId, string clientId);
        List<ClientDivisionPayrollCode> List(int companyId, string clientId, string divisionId, PayrollCodeType payrollCodeType);
        List<Code_Description_Selected> GetDeductions(int companyId, string clientId, string divisionId, List<string> selectedDeductionCodes);
        List<ClientDivisionPayrollCode> GetAllClientDivisionPayrollCodes(int companyId, string clientId);
        List<ClientDivisionPayrollCode> GetAllClientDivisionPayrollCodesByType(int companyId, string clientId, PayrollCodeType payrollCodeType);
        List<string> GetAllPayrollCodes(int companyId, string clientId);
        List<MultiselectOptionsDTO> GetWorkersCompPayCodesByCompanyClient(int companyId, string clientId);
        List<MultiselectOptionsDTO> GetPayCodesByCompanyClient(int companyId, string clientId, int payType);
        List<MultiselectOptionsDTO> GetOvertimePayCodesByCompanyClient(int companyId, string clientId, int payType);
        List<MultiselectOptionsDTO> GetWorkersCompPayCodesByUserCompany();
        List<Code_Description_Selected> GetPaycodes(int companyId, string clientId, List<string> selectedPaycodes);
    }
}
