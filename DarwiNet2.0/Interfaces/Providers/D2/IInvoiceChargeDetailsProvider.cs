using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceChargeDetailsProvider
    {
        decimal? GetWagesTotalForInvoice(int companyId, string clientId, int invoiceNumber);
        decimal? GetWagesTotalForInvoiceNonGross(int companyId, string clientId, int invoiceNumber);
        decimal? GetBenefitsTotalForInvoice(int companyId, string clientId, int invoiceNumber);
        List<InvoiceChargeDetail> GetTaxRecordsForInvoice(int companyId, string clientId, int invoiceNumber);
        void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers);
        List<InvoiceChargeDetail> ListInvoiceChargeDetalsByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceChargeDetail> ListInvoiceChargeDetalsByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceChargeDetail> ListInvoiceChargeDetailsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void UpdateMergedInvoiceNumberRaw(InvoiceChargeDetail invoiceChargeDetail, int mergedInvoiceNumber, DnetEntities dbContext = null);
        void SaveInvoiceChargeDetail(InvoiceChargeDetail invoiceChargeDetailToSave, DnetEntities dbContext = null);
        void DeleteListInvoiceChargeDetails(List<InvoiceChargeDetail> invoiceChargeDetails, DnetEntities dbContext = null);
        InvoiceChargeDetail GetByPK(string chargeType, string clientID, int companyID, string componentCode, int darwinInvoiceNumber, int mergedInvoiceNumber, DnetEntities dbContext = null);
        InvoiceChargeDetail GetByPK(InvoiceChargeDetail invoiceChargeDetail, DnetEntities dbContext = null);
        List<InvoiceChargeDetail> ListByChargeType(string chargeType, string clientID, int companyID, int darwinInvoiceNumber);
        InvoiceChargeDetail Add(InvoiceChargeDetail icd, DnetEntities dbContext = null);
    }
}
