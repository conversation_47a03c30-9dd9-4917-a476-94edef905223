using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollWorkFutaSutaWCOriginalProvider
    {
        void SaveOriginals(List<PayrollWorkFutaSutaWCOriginal> payrollWorkFutaSutaWCOriginals, DnetEntities context);
        List<PayrollWorkFutaSutaWCOriginal> ListFutaSutaWCOriginals(string payrollNumber, string employeeID, DnetEntities context = null);
        void DeleteFromPayroll(string payrollNumber);
        void DeleteFromPayroll(string payrollNumber, string employeeId);
    }
}