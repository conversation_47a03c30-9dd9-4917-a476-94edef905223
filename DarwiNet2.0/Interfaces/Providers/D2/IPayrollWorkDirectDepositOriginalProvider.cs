using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

//SDJ CH-1232
namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollWorkDirectDepositOriginalProvider
    {
        void SaveOriginals(List<PayrollWorkDirectDepositsOriginal> payrollWorkDirectDepositsOriginals, DnetEntities context);
        List<PayrollWorkDirectDepositsOriginal> ListDirectDepositOriginals(string payrollNumber, string employeeID, DnetEntities context = null);
        void DeleteFromPayroll(string payrollNumber);
        void DeleteFromPayroll(string payrollNumber, string employeeId);
    }
}