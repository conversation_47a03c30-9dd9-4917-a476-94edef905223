using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceAgencyCreditHistoryOriginalsProvider
    {
        string SaveOriginals(List<InvoiceAgencyCreditHistoryOriginal> invoiceAgencyCreditHistoryOriginalsToSave, DnetEntities dbContext = null);
        List<InvoiceAgencyCreditHistoryOriginal> ListInvoiceAgencyCreditHistoryOriginalsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void DeleteOriginalsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
    }
}