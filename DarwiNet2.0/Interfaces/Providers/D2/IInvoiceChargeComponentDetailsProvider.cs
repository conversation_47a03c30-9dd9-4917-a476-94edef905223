using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceChargeComponentDetailsProvider
    {
        void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers);
        List<InvoiceChargeComponentDetail> ListInvoiceChargeComponentDetailsByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceChargeComponentDetail> ListInvoiceChargeComponentDetailsByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceChargeComponentDetail> ListInvoiceChargeComponentDetailsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void UpdateMergedInvoiceNumberRaw(InvoiceChargeComponentDetail invoiceChargeComponentDetail, int mergedInvoiceNumber, DnetEntities dbContext = null);
        void SaveInvoiceChargeComponentDetail(InvoiceChargeComponentDetail invoiceChargeComponentDetailToSave, DnetEntities dbContext = null);
        void DeleteListInvoiceChargeComponentDetails(List<InvoiceChargeComponentDetail> invoiceChargeComponentDetails, DnetEntities dbContext = null);
        InvoiceChargeComponentDetail GetByPK(int companyID, string clientID, int darwinInvoiceNumber, string componentCode, string componentType, string chargeType, int mergedInvoiceNumber, string additionalData, DnetEntities dbContext = null);
        InvoiceChargeComponentDetail GetByPK(InvoiceChargeComponentDetail invoiceChargeComponentDetail, DnetEntities dbContext = null);
        InvoiceChargeComponentDetail Add(InvoiceChargeComponentDetail iccd, DnetEntities dbContext = null);
    }
}