using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.ViewModels.NIL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IDealsProvider
    {
        Deal AddDeal(Deal deal);
        Deal UpdateDeal(Deal deal);
        void DeleteDeal(Deal deal);
        Deal GetDeal(int companyID, string clientID, long dealID);
        long GenerateDealID(int companyID, string clientID);
        TableQueryInfo<DealCockpitTableRowDTO> GetDealCockpitTableData(int companyID, string clientID, TableFilter filters);
        List<Code_Description> GetDealTypes(int companyID);
        List<Code_Description> GetDealActivityTypes(int companyID);
        List<Employee> GetEmployees(int companyID, string clientID);
        List<Code_Description> GetPositions(int companyID);
    }
}