using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceAgencyCreditHistoryProvider
    {
        void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers);
        List<InvoiceAgencyCreditHistoryWithEENameDTO> GetInvoiceAgencyCreditHistories(int companyId, string clientId, int invoiceNumber);
        List<InvoiceAgencyCreditHistory> ListInvoiceAgencyCreditHistoriesByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceAgencyCreditHistory> ListInvoiceAgencyCreditHistoriesByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceAgencyCreditHistory> ListInvoiceAgencyCreditHistoriesByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void UpdateMergedInvoiceNumberRaw(InvoiceAgencyCreditHistory invoiceAgencyCreditHistory, int mergedInvoiceNumber, DnetEntities dbContext = null);
        void SaveInvoiceAgencyCreditHistory(InvoiceAgencyCreditHistory invoiceAgencyCreditHistoryToSave, DnetEntities dbContext = null);
        void SaveListInvoiceAgencyCreditHistories(List<InvoiceAgencyCreditHistory> invoiceAgencyCreditHistoriesToSave, DnetEntities dbContext = null);
        void DeleteListInvoiceAgencyCreditHistories(List<InvoiceAgencyCreditHistory> invoiceAgencyCreditHistories, DnetEntities dbContext = null);
    }
}