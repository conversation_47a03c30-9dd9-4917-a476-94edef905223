using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IInvoiceCertifiedPayrollsProvider
    {
        void DeleteFromInvoices(int companyId, string clientId, List<int> invoiceNumbers);
        List<InvoiceCertifiedPayroll> ListInvoiceCertifiedPayrollsByDarwinInvoiceNumber(int companyID, string clientID, int darwinInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceCertifiedPayroll> ListInvoiceCertifiedPayrollsByMergedInvoiceNumber(int companyID, string clientID, int mergedInvoiceNumber, DnetEntities dbContext = null);
        List<InvoiceCertifiedPayroll> ListInvoiceCertifiedPayrollsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void UpdateMergedInvoiceNumberRaw(InvoiceCertifiedPayroll invoiceCertifiedPayroll, int mergedInvoiceNumber, DnetEntities dbContext = null);
        void SaveInvoiceCertifiedPayroll(InvoiceCertifiedPayroll invoiceCertifiedPayrollToSave, DnetEntities dbContext = null);
        void SaveListInvoiceCertifiedPayrolls(List<InvoiceCertifiedPayroll> invoiceCertifiedPayrollsToSave, DnetEntities dbContext = null);
        void DeleteListInvoiceCertifiedPayrolls(List<InvoiceCertifiedPayroll> invoiceCertifiedPayrolls, DnetEntities dbContext = null);
    }
}