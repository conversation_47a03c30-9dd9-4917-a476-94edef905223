using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IPayrollWorkFutaSutaWCProvider
    {
        List<PayrollWorkFutaSutaWCModel> GetTaxTotalsByPayrollRecordType(string payrollNumber, HashSet<string> employeeIds, byte? payrollRecordType = null);
        void SaveVoided(List<PayrollWorkFutaSutaWC> voidedPayrollWorkFutaSutaWCs, DnetEntities context);
        List<PayrollWorkFutaSutaWC> ListFutaSutaWCs(string payrollNumber, string employeeID, DnetEntities context = null);
        int GetPreviousVoidCount(string payrollNumber, string employeeID);
        void DeleteFromPayroll(string payrollNumber);
        void Delete(string payrollNumber, string employeeID);
        void Delete(PayrollWorkFutaSutaWC futaSutaWC);
    }
}
