using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Providers.D2
{
    public interface IEmployeeInvoiceCheckHsitoryOriginalsProvider
    {
        string SaveOriginals(List<EmployeeInvoiceCheckHistoryOriginal> employeeInvoiceCheckHistoryOriginals, DnetEntities dbContext = null);
        List<EmployeeInvoiceCheckHistoryOriginal> ListEmployeeInvoiceCheckHistoryOriginalsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
        void DeleteOriginalsByListDarwinInvoiceNumbers(int companyID, string clientID, List<int> darwinInvoiceNumbers, DnetEntities dbContext = null);
    }
}