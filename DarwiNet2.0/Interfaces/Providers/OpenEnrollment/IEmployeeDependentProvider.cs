using DarwiNet2._0.Data;
using DarwiNet2._0.Models;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Providers.OpenEnrollment
{
    public interface IEmployeeDependentProvider
    {
        List<EEEligiblePlanDependents> GetEmployeeEligiblePlanDependents(int companyId, string oeEmployeeId);
        List<EmployeeDependent> FindByEmployeeId(int companyId, string employeeId);
        int GetCountEmployeeDependentsByRelationship(int companyID, string employeeID, int relation);
        List<EmployeeDependent> GetEmployeeChildrenDep(string employeeID, int companyID);
        int GetCountEmployeeDependents(int companyId, string employeeID);
    }
}
