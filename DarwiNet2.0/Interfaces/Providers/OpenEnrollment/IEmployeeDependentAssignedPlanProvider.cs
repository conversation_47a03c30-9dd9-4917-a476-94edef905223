using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Providers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Providers.OpenEnrollment
{
    public interface IEmployeeDependentAssignedPlanProvider : IDbContextProvider 
    {
        void CreateNewEmployeeDependentAssignedPlan(List<EmployeeEnrollmentPlanDependent> eeDeps, ClientPlanHDR cph, string planCategories, EmployeeAssignedPlan newEEAP, EmployeeEnrollmentPlanRateGrid thisEERG);
        List<EmployeeDependentAssignedPlan> GetEmployeeDependentAssignedPlansByEmployeePerYear(string employeeID, int year);
        List<EmployeeDependentAssignedPlan> GetEmployeeDependentAssignedPlansByEmployee(string employeeID);
        List<ReactivatedEmployeeDependentAssignedPlanDto> GetReactivatedEmployeeDependentAssignedPlansByEmployee(string employeeID);

    }
}
