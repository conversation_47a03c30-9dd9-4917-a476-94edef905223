using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IInvoiceMergeReversionService
    {
        string ProcessUnmergeInvoices(InvoiceMergeRevertDTO invoiceMergeRevertDTO, DnetEntities dbContext);
        string DeleteInvoiceOriginals(InvoiceMergeRevertDTO invoiceMergeRevertDTO, List<int> darwinInvoiceNumbers, DnetEntities dbContext);
        void ResetMergedPayrolls(string payrollNumber, DnetEntities dbContext);
    }
}