using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IPayrollVoidService
    {
        TableQueryInfo<PayrollVoidTableRowDTO> GetListVoidableChecks(Expression<Func<PayrollVoidTableRowDTO, bool>> filterClause);
        TableQueryInfo<PayrollVoidTableRowDTO> GetListVoidableChecksTableFilterData(TableFilter filters);
    }
}