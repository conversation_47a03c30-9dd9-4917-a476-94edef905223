using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Models.D2.EditPayrollCalculation;
using System;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using System.Collections.Generic;
using DarwiNet2._0.Models.D2;
using DarwiNet2._0.ViewModels;
using System.Web.Mvc;
using System.Web;
using DarwiNet2._0.Helpers;
using DarwiNet2._0.DTOs;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IEmployeeService
    {
        List<Employee> GetEmployees(int companyId, string clientId);
        List<Employee> GetEmployeesByCompanyId(int companyId);
        List<Code_Description> ListCodes(int companyId, string clientId);
        List<Code_Description_Selected> GetPayrollProfileEmployees(int companyId, string clientId, string profileId);
        List<Code_Description_Selected> GetPayrollProfileEmployeesSelectedAndAvailable(int companyId, string clientId, string profileId);
        List<Employee> GetEmployeesByEmployeeIds(int companyId, string clientId, IEnumerable<string> employeeIds);
        EmployeeDetail GetEmployeeDetail(int companyId, string employeeId);
        List<Employee> GetEmployees(int companyId, HashSet<string> employeeIds);
        List<Code_Description> GetEmployeeDepartments(int companyId, string clientId);
        List<string> GetSupervisorSecurityEmployeeIds(int companyId, string clientId, string userId);
        List<string> ListEmployeeIds(int companyId, string clientId);
        List<string> GetSecurityEmployees(int companyId, string clientId, string userId, string dnetOwnerId, string dnetLevel, string dnetRoleId, string emplId);
        List<string> GetActiveEmployees(int companyId, string clientId);
        List<string> GetActiveDepartmentEmployees(int companyId, string clientId, List<string> departments);
        List<Code_Description> FindEmployeeNamesByEmployeeIds(int companyId, string clientId, List<string> employeeIds);
        Code_Description FindEmployeeNameByEmployeeId(int companyId, string employeeId);
        List<Id_Code_Description> GetEmployeeNamesSocialSecurityNumbers(int companyId);
        IEnumerable<Id_Code_Description> GetEmployeeNamesSocialSecurityNumbers(int companyId, string clientId);
        DateTime? GetEmployeeStartDate(int companyId, string clientId, string employeeId);
        EmployeeEnrollmentEmployeeModel GetEmployeeEnrollmentEmployee(int companyId, string oeEmployeeId);
        Employee FindEmployeeByEmployeeId(int companyId, string employeeId);
        bool IsValidEmployee(int companyId, string employeeId);
        List<Employee> GetActiveEmployees();
        List<Employee> GetActiveEmployeesByClient(int companyId, string clientId);
        List<string> GetDocumentSecurityTypes(int companyId, string clientId, string userId);
        List<Employee> GetEmployees(int companyId, List<ITimeSheetRow> timeSheetRows);
        EmployeeDefaultsModel GetEmployeeDefaults(int companyId, string employeeId);
        TableQueryInfo<PayrollNoteEmployeeDTO> GetPayrollNoteEmployeeData(TableFilter filters);
        List<MultiselectOptionsDTO> MultiSelectEmployeesForClient(int companyID, string clientID);
        List<MultiselectOptionsDTO> MultiSelectEmployeesForClient(List<int> companyIDs, List<string> clientIDs);
        List<MultiselectOptionsDTO> EmployeesByClientNotInEmployeeJobCostAssignments(int companyID, string clientID, string jbsid);
        List<MultiselectOptionsDTO> MultiSelectEmployeesForPayrollEdits(string payrollNumber);
        void SetEEAnnualWage(int companyId, string employeeId, decimal wage);
        bool CheckClientLevel(string userid);
        bool CheckPeoUserLevel(string userid);
        List<Code_Description> GetLocations(string clientId);
        ResponseObject<object> UploadTerminationDocument(Employee employee, EmployeeTerminationDTO employeeTerminationDTO, HttpPostedFileBase file, HttpRequestBase request, string loggedInUser);
        ResponseObject<object> PopulateAndInactivateTermEmployee(Employee employee, EmployeeTerminationDTO employeeTerminationDTO, string loggedInUser);
        ResponseObject<object> UpdateEndDatesOnEAP(EmployeeTerminationDTO employeeTerminationDTO, Employee employee);
        void SendEmployeeTerminationNotification(Employee employee);
        List<EmployeeChangeStatus> EditEmploymentInfo(EmployeesInfoVM vm, Employee origEe, bool updWC, EmployeePaycode empPaycode, string _selPayRecord, string payTypeChange, bool supervisor, bool exempt, byte? payUnitPeriod, DateTime? effectiveDate = null);
        List<EmployeeChangeStatus> EditEmploymentInfoCOS(EmployeesInfoVM employeesInfo, DateTime effectiveDate, string reason, string additionalNotes, bool compreturnValue, bool empstspreturnValue, bool posreturnValue, string ePayTypeChange, bool exempt);
        void EmployeeCOSUpdates();



        Employee GetEmployeeByEmployeeId(string employeeId);
    }
}
