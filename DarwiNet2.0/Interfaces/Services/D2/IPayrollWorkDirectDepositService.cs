using DarwiNet2._0.DTOs;
using DarwiNet2._0.Models.D2.EditPayrollCalculation;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2.PayrollPreview;
using DarwiNet2._0.ViewModels.D2.PayrollReview;
using System.Collections.Generic;
using System.Data.Entity;

//SDJ CH-1232
namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IPayrollWorkDirectDepositService
    {
        List<PayrollWorkDirectDeposit> ListDirectDeposits(string payrollNumber, string employeeID);
    }
}
