using DarwiNet2._0.DTOs;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2.TimeSheet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Services.D2.TimeSheet
{
    public interface ITimeSheetUpdateService
    {
        List<TimeSheetDetail> UpdateTimeSheetRowData(int companyId, int timeSheetId, List<ITimeSheetColumn> columns, List<TimeSheetRowActionDTO> timeSheetRowData);
    }
}
