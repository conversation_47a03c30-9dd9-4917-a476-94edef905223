using DarwiNet2._0.Controllers;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Extensions;
using DarwiNet2._0.Interfaces.Providers.D2;
using DarwiNet2._0.Interfaces.Services.D2;
using DarwiNet2._0.Models.TimeSheetServiceModels;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2.TimeSheet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Interfaces.Services.D2.TimeSheet
{
    public interface ITimeSheetDeleteService
    {
        List<TimeSheetRowKeyDTO> DeleteTimeSheetRowData(int companyId, int timeSheetId, List<TimeSheetRowActionDTO> timeSheetRowData);
    }
}