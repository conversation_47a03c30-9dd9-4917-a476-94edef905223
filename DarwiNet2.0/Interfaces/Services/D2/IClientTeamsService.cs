using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.ViewModels.D2;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IClientTeamsService
    {
        IEnumerable<ClientTeam> GetClientTeams(int companyId, string clientId);
        List<ClientTeamViewModel> ClientTeamsList(int companyId, string clientId);
        List<Code_Description> GetAvailableClientTeams(int companyId, string clientId);
        ClientTeam GetClientTeamById(int companyId, string clientId, string id);
        bool CreateClientTeam(int companyId, string clientId, string id, string name, string type = "");
        void DeleteTeam(int companyId, string clientId, string id);
        string GetNameByTeamID(int companyId, string clientId, string id);
        List<ClientTeamMemberViewModel> GetClientTeamMembers(int companyId, string clientId, string id);
        List<Code_Description> AvailableTeamMembers(int companyId, string clientId, string id);
        void AddTeamMembers(int companyId, string clientId, string id, List<string> users);
        void DeleteTeamMembers(int companyId, string clientId, string id, List<string> users);
        void DeleteTeamMember(int companyId, string clientId, string id, string uid);
        void UpdateClientTeam(int companyId, string clientId, string id, string name, string type = "");
        bool IsTeamTypeTaken(int companyId, string clientId, string type);
        //bool DoesUserBelongToValidTeam(string payrollNumber, string currentUserId);
        IEnumerable<ClientTeamType> GetTeamTypes(int companyId, string clientId);
        List<ClientTeamTypeViewModel> TeamTypesList(int companyId, string clientId);
        ClientTeamType GetTeamType(int companyId, string clientId, string type);
        void DeleteTeamType(int companyId, string clientId, string type);
        bool UpdateTeamType(int companyId, string clientId, string type, string descr);
        bool CreateTeamType(int companyId, string clientId, string type, string descr);
        //IEnumerable<ClientTeam> GetClientTeams(List<int> companyIds, List<string> clientIds, List<string> profileIds);
    }
}
