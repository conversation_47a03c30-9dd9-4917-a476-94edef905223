using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IEmployeeDeductionService
    {
        byte? GetDeductionFrequency(int companyId, string employeeId, string employeeDeductionCode);
        List<Code_Description> GetEmployeeDeductions(int companyId, string employeeId);
        EmployeeDeduction GetEmployeeDeduction(int companyID, string employeeID, string code);
    }
}
