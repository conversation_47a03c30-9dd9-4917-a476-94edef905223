using DarwiNet2._0.Enumerations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Thinkware.Pay360.Payroll;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IPayrollResetHistoryService
    {
        void Create(string reason, string userId, string payrollNumber, string newPayrollNumber, PayrollStatus status, string scheduleId);
    }
}
