using DarwiNet2._0.Controllers;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface ISelectArrayService
    {
        void Update<T>(List<T> itemList, List<T> newList);
        void RemoveAll<T>(List<T> itemList);
        void AddItems<T>(List<T> itemList);
        List<Code_Description_Selected> GetSelectedCodes(List<Code_Description> allCodes, List<string> selectedCodes);
    }
}
