using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using DarwiNet2._0.Models.D2.EditPayrollCalculation;
using DarwiNet2._0.ViewModels.D2.PayrollPreview;
using DarwiNet2._0.ViewModels.D2.PayrollReview;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static DarwiNet2._0.DTOs.PayrollReviewTaxesDTO;
using static DarwiNet2._0.Services.D2.PayrollWorkStateTaxService;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IPayrollWorkStateTaxService
    {
        List<PayrollReviewStateTaxesDTO> GetStateTaxes(HashSet<string> employeeIds, string payrollNumber);
        List<PayrollWorkStateTaxModel> GetStateTaxes(string payrollNumber, HashSet<string> employeeIds);
        Dictionary<EditCalcUpdateAction, List<UpdateError>> HandleStateWithholdingChanges(string employeeId, int companyId, string payrollNumber, string snapshotId, string profileId, List<RecordUpdate> updates);
        PayrollReviewStateTaxTotals GetStateTaxTotals(string payrollNumber);
        List<TaxesHeaderViewModel> EmployeeStateTaxes(string payrollNumber, string employeeId);
        List<TaxesHeaderViewModel> EmployeeStateTaxes(string employeeId, List<PayrollWorkStateTaxModel> stateTaxes);
        //List<PayrollPreviewTaxesViewModel> GetPayrollPreviewStateTaxes(string payrollNumber);
        List<PayrollPreviewTaxesViewModel> GetPayrollPreviewStateTaxes(string payrollNumber, string employeeId = null);
        void Delete(string payrollNumber, string snapshotId, string employeeId, string stateTaxCode);
        void CreateNewEmployeeStateTaxes(string payrollNumber, string snapshotID, List<PayrollWorkHeader> workHeaders);
        void CombineProratedStateTaxes(int companyID, string payrollNumber, string employeeID);

    }
}
