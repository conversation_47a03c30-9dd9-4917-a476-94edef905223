using DarwiNet2._0.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IDeductionService
    {
        IEnumerable<Code_Description_Selected> GetDeductions(int companyId, string clientId, string profileId);

        IEnumerable<Code_Description> GetNonPlanDeductions(int companyId, string clientId, string profileId);
    }
}
