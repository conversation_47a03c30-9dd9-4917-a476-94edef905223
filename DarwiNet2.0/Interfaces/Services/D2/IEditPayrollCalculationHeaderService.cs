using DarwiNet2._0.Models.D2.EditPayrollCalculation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Services.D2
{
    public interface IEditPayrollCalculationHeaderService
    {
        HashSet<string> UnprocessedAdds(string payrollNumber, string snapshotId, PayrollWorkTable table, string partialKey);
        void DeleteUnprocessedAdd(string headerId, string payrollNumber, string snapshotId, string employeeId, PayrollWorkTable table, string partialKey);
    }
}
