using System;
using System.Collections.Generic;
using DarwiNet2._0.ViewModels.CheckPrinter;
using DarwiNet2._0.Models.CheckPrinter;
using DarwiNet2._0.DTOs.CheckPrinter;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Interfaces.Services.CheckPrinter
{
    public interface ICheckInfoService
    {
        string GetCodeDescription(int compantId, string clientid, int type);
        string GetPlanDescription(int compantId, string employeeId, bool isDeduction, string code);
        string GetDefaultPlanDescription(int compantId, string employeeId, string code);
        string GetLatestPlanDescription(int compantId, string employeeId, string plan);
        string GetAuditCode(int companyId, string employeeId, DateTime checkDate, string checkNumber);
        bool DoNotPrintDirectDeposit(int companyId, string employeeId);
        bool UseSummarizeYTDCalc(int companyId, string employeeId);
        EmployeeStateTax GetEmployeeStTax(int companyId, string employeeId, string code);
        List<EmployeePTOType> GetEmployeePTO(int companyId, string employeeId);
        List<EmployeePTOType> GetEmployeePTOCodes(int companyId, string employeeId);
        decimal GetPTOTakenAdjustment(int companyid, string employeeid, short ptotype, DateTime checkDate, DateTime start);
        DateTime? GetEmployeeHireDate(int companyid, string employeeid);
        List<JobTransactionModel> GetEmployeeJobTransactions(int companyid, string employeeId, string auditcode, string code, string checkNbr, DateTime checkDate);
        string GetEmployeeTransactionDepartment(int companyid, string employeeId, int payid, DateTime checkDate);
        CheckNotesModel GetEmployeeNotes(int companyId, string employeeId, DateTime checkDate);
        string GetAuditCodeByCheck(int companyid, string employeeId, string checkNbr, DateTime checkDate);
        List<EmployeeDirectDepositHistory> GetCheckACH(int companyId, string employeeId, int paymentId);
        List<WorkCheckModel> GetPayrollChecks(int companyid, string clientid, string auditcode, bool reprint, string userid);
        List<EmployeeCheckModel> GetPayrollCheckEmployees(int companyid, string clientid, string auditcode, string profileid, bool reprint, string userid);
        List<EmployeeCheckModel> GetPayrollPrintedChecks(int companyid, string clientid, string auditcode, string profileid);
        List<WorkCheckModel> GetInvoiceChecks(int companyid, string clientid, int invoice, bool reprint, string userid);
        List<EmployeeCheckModel> GetInvoiceCheckEmployees(int companyid, string clientid, int invoice, bool reprint, string userid);
        List<WorkCheckCodeModel> GetCheckDetails(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        List<WorkCheckCodeModel> GetCheckDetailsGroupByDepartment(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        List<WorkCheckCodeModel> GetCheckDetailsGroupByPosition(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        List<WorkCheckCodeModel> GetCheckDetailsGroupByRate(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        List<WorkCheckCodeModel> GetCheckDetailsGroupByDepartmentPosition(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        List<WorkCheckCodeModel> GetCheckDetailsGroupByDepartmentRate(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        List<WorkCheckCodeModel> GetCheckDetailsGroupByPositionRate(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        List<WorkCheckCodeModel> GetCheckDetailsGroupByDepartmentPositionRate(int companyid, string employeeid, int paymentid, DateTime checkDate, int year, bool useSSN, bool useYTD);
        CheckPTOHoursModel GetPTOTakenAndAccrued(int companyid, string employeeid, short ptotype, int paymentid, DateTime checkDate, DateTime startDate);
        List<EmployeeDirectDepositHistory> GetACHRecords(int companyid, string employeeid, int payid, DateTime checkDate);
        string DepartmentDescription(int companyid, string department);
        decimal MaxLTDDeduction(int companyid, string employeeid, string deduction);
        string GetCheckInvoices(int companyid, string employeeid, int paymentid, DateTime checkDate, int maxlength);
        Wages_Fed_AmountsModel RecalculateCheckTotals(int companyid, List<string> employees, int year, DateTime checkDate);
        void SetEECheckPrinted(int companyid, string employeeid, int payid, DateTime checkDate, bool mailed);
        void SetChecksPrinted(List<EmployeeCheckModel> list);
        void SetEECheckEmailed(int companyid, string employeeid, int payid, DateTime checkDate);
        void SetChecksEmailed(List<EmployeeCheckModel> list);
        void SaveCheckImage(EmployeeCheckModel ee, byte[] checkimage);
        ClientPayrollSchedule GetPayrollRecord(string payroll);
        List<byte[]> GetPrintedCheckImages(List<EmployeeCheckModel> list, PrintStatus status);
        List<EmployeeCheckModel> GetPayrollSelectedPrintedChecks(int companyid, string clientid, string auditcode,  string profileid, List<int> payments);
        bool IsRecentEECheck(int companyId, WorkCheckModel workcheck);
        List<WorkCheckModel> GetPayrollChecks(int companyid, string clientid, string auditcode, string userid);
        List<WorkCheckModel> GetPayrollChecks(int companyid, string clientid, string emplid, string auditcode, string userid);
        List<WorkCheckModel> GetPayrollChecks(int companyid, string clientid, int year, string userid);

    }
}
