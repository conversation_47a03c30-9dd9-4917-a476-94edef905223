using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DarwiNet2._0.ViewModels.CheckPrinter;
using DarwiNet2._0.DTOs.CheckPrinter;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Interfaces.Services.CheckPrinter
{
    public interface IClientInfoService
    {
        Bank GetBankRecord(int companyId, string bankId);
        Checkbook GetCheckbookRecord(int companyId, string cbId);
        Company GetCompanyRecord(int companyId);
        Employee GetEmployeeRecord(int companyid, string employeeid);
        Client GetClientRecord(int companyId, string clientId);
        CheckNotesModel GetClientNotes(int companyId, string clientId, DateTime checkDate);
        CheckNotesModel GetCompanyNotes(int companyId, DateTime checkDate);
        CompanyACHSetupModel CompanyACHSetup(int companyId);
        ClientDivision DivisionInfo(int companyid, string clientid, string department);
        ClientAddress ClientAddress(int companyid, string clientid, string code);
        List<string> GetAvailableEmployeeIDs(int companyid, string employeeid, bool summarize);
    }
}