using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static DarwiNet2._0.Services.FinalPayService;

namespace DarwiNet2._0.Interfaces.Services
{
    public interface IFinalPayService
    {
        Employee ReactivateEmployeeForFinalPay(string employeeId, string eeClient, int eeCompany, DateTime finalPaycheckDate, List<string> selectedPayCodes, string ownerId);
        FinalPayProcessingResult ScanAndInactivateFinalPayEmployees();
        bool RemoveAndAddDirectDepositHistory(List<EmployeeDirectDeposit> directDeposits, Employee employee);
       List<string> Getter<PERSON><PERSON>ay<PERSON>H<PERSON>ory(Employee employee);
        bool RemoveAndAddEmployeePaycodetHistory(List<EmployeePaycode> paycodes, Employee employee);


    }
}
