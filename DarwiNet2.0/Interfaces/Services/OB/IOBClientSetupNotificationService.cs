using DarwiNet2._0.Data;

namespace DarwiNet2._0.Interfaces.Services.OB
{
    public interface IOBClientSetupNotificationService
    {
        OBClientSetupNotification GetNotificationRecord(int companyId, string clientId, int setupId, short type, short assigned);
        string GetNotificationInfo(int companyId, string clientId, string employeeId, int id, short section, short assigned, string peoMail, string peoName, string peoPhone, string dnetLevel, string dnetOwnerId, string dnetUrl, out string subject, out string addr, out string name, out string cc, out string bcc);
        ProjectSetup GetProjectSetupMailSettings();
        bool IsNotificationEnabled(int companyId, int setupId, short type, short assigned);
        bool IsEmployeeRehired(int companyId, string employeeId);
    }
}
