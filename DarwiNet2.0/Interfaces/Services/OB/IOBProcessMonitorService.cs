using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DTOs;
using System;
using System.Collections.Generic;

namespace DarwiNet2._0.Interfaces.Services.OB
{
    public interface IOBProcessMonitorService
    {
        IEnumerable<OBProcessMonitorVM> GetEmployees(int companyId, string clientId, bool completed, string dnetLevel);
        IEnumerable<OBProcessMonitorVM> GetEESingleInfo(int companyId, string employeeId, bool completed);
        int? GetSetupId(int companyId, string employeeId);
        EmployeeClassVM GetEmployeeClassVM(int companyId, string classId);
        bool GetMaskSSNInSign(int companyId, string employeeId);
        KeyValuePair<string, bool> GetEmployeeMaskSSNInSign(int companyId, string employeeId);
        IEnumerable<OBProcessTask> GetEmployeeTasks(int companyId, string dnetLevel, string employeeId);
        bool IsProcessCompleted(int companyId, string employeeId);
        IEnumerable<OBProcessMonitor> GetPendingEmployees(int companyId);
        OBProcessMonitor GetOBProcessMonitorEmployeeDetails(int companyId, string employeeId);
        IEnumerable<Code_Description> GetEmployeeClassesByDepartmentAndPosition(int companyId, string department, string position);
        bool IsEmployeeClassRequired(int companyId, int? id);
        List<Code_Description> GetEmployeeClasses(int companyId, string clientId);
        void SetTaskStamp(string dnetLevel, string employeeId, int taskId, short taskStatus);
        void CreateProcessPersonal(int companyId, string clientId, string employeeId, int taskId, string payPeriod, string eeDept, string eePosition, string eeClass, string eeStatus, string ghApplicantID, string BEClass);
        void CreateProcessFederal(int companyId, string employeeId, int taskId, string employeeState);
        void CreateProcessState(int companyId, string employeeId, int taskId, string employeeState);
        void CreateProcessPayCodes(int companyId, string employeeId, int taskId, string payPeriod, List<string> codes);
        void CreateProcessBenefitCodes(int companyId, string employeeId, int taskId, string payPeriod, List<string> codes);
        void CreateProcessDeductionCodes(int companyId, string employeeId, int taskId, string payPeriod, List<string> codes);
        void CreateProcessAdditionalInfo(int companyId, string employeeId, int taskId, string eeClass);
        void CreateProcessKERS(int companyId, string employeeId, int taskId);
        void CreateProcessI9Verification(int companyId, string employeeId, int taskId);
        bool IsTaskAvailable(string clientId, OBProcessTask task);
        OBClientSetup GetOBClientSetup(int setupId, int companyId);
        OBUser GetOBUserByEmployeeID(int companyId, string employeeId);
        void CreateNewOBProcessMonitor(int companyId, string clientId, string dnetOwnerId, string emplId, OBClientSetup setup, ClientDivision div, bool useClientCodes, int startTask, DateTime dDate, DateTime wDate, string empDivision, string codeDivision, string eeClass, string payPeriod, string workState, string dueDate, string RoleID);
        bool CreateNewOBUser(int companyId, string company, string clientId, string customer, string loginCode, string emplId, string eeFirstName, string eeLastName, string eeMail, string eeSSN, string eeBirthDate);
        List<Code_Description> GetBEClasses(int companyId, string clientId);
        bool IsBEClassRequired(int? id, int companyId);
        int GetTaskByType(int setupId, short type);
        bool IsEmployeeRehired(int companyId, string employeeId);
        EmployeeIdCreationDTO CreateEmployeeID(string division, string department, string position, string EEClass, string employmentStatus, string payPeriod, string workState,
          string roleID, string BEClass, string firstName, string lastName, string email, string ssn, string birthDate, string dueDate, int profileSetup, string employeeId, string dnetLevel, int companyId);
        string TaskInstruction(int taskId, int setupID, string dnetLevel, int companyId, out string docFile, out string taskTip, out string tipImage);
        string GetLoginCode();

        IEnumerable<OBProcessMonitorVM> GetOBClientFinalizeEEs(int companyId, string client);
    }
}
