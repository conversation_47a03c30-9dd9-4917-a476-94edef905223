using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.Interfaces.Services.Documents
{
    interface ICompanyDocumentAssignmentsService
    {
        List<CompanyDocumentAssignment> AssignedDocuments(int compid, string clientid = "");
        List<CompanyDocumentAssignment> GetEEAssignedDocuments(int compid, string emplid);
        CompanyDocumentAssignment GetEEDocumentByID(int companyid, string employeeid, int id, string userid = null);
        bool CanDeleteDocument(int companyid, int id, out string error);
        int GetEEUnverifiedDocumentID(int companyid, string employeeid, List<short> types);
        int GetCCUnverifiedDocumentID(int companyid, string userid, List<string> types, List<string> ees, out string ee, string clientid = "");
        List<CompanyDocumentAssignment> ReplicateDocumentToUsers(CompanyDocumentAssignment eedoc, bool allusers);
        void RemoveDocumentERUsers(int comp, int id, short oldtype, short newtype);
        void RemoveDocumentAssignments(int companyid, int id, bool clientusers);
        void RemoveDocumentAssignment(int companyid, int docid, string clientid, string employeeid, string userid);
        short GetReturnAction(CompanyDocument doc);
        List<string> DocumentAssignedClientUsers(int companyid, string clientid, int id);
    }
}
