using System;
using System.Collections.Generic;
using DarwiNet2._0.Core;
using System.Data;
using DarwiNet2._0.Controllers;
using DarwiNet2._0.ViewModels;
using DarwiNet2._0.ViewModels.Reports;
using DarwiNet2._0.Interfaces.Providers.ReportWriter;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Interfaces.Services.ReportWriter
{
    public interface IReportWriterService
    {
        List<ReportTableVM> ReportSources(int access);
        DataTable GetReportDataTable(string sql, List<CustomReportFiltersVM> fparam, out string err);
        DataTable GetReportDataTable(string sql, List<CustomReportFiltersVM> fparam, out string err, out int tot_recs);
        void ReportParameters(CustomReportVM report, out CustomReportDetailsVM pivot_fld, out CustomReportDetailsVM calc_fld, out CustomReportDetailsVM sec_fld, out string pivot_columns);
        List<CustomReportFiltersVM> ReportFilterParameters(CustomReportVM report);
        DataTable GetReportDataTable(CustomReportVM report, CustomReportDetailsVM pivot, CustomReportDetailsVM pivot_calc, CustomReportDetailsVM sel_fld, string pivot_columns, out string err, out int tot_recs, byte tot, bool forPreview = false);
        DataTable GetReportDataTable(int id, out CustomReportVM report, out string err, byte tot = 0);
        string PreviewReportQuery(CustomReportVM report);
        ReportResultVM DataTableToHtml(string err, string prefix = "");
        ReportResultVM DataTableToHtml(List<CustomReportDetailsVM> fields, List<CustomReportFiltersVM> fparam, DataTable data, DataTable totals, string sections, int ssnDType = 0, int totrecs = -1);
        string ReportQuery(ref CustomReportVM report, CustomReportDetailsVM pivot, CustomReportDetailsVM pivot_calc, CustomReportDetailsVM sel_fld, string pivot_columns, byte tot, bool forPreview = false);
        string ReportQuery(int id, byte tot = 0);
        List<CustomReportDetailsVM> ReportFields(int id);
        List<CustomReportFiltersVM> ReportFilters(int id);
        List<CustomReportSortsVM> ReportSorts(int id);
        List<CustomReportAssignmentsVM> ReportAssignments(int id);
        CustomReport GetReportById(int id);
        bool SaveReport(CustomReportVM report, string user, short status, out int id);
        ReportResultVM ExecuteReport(CustomReportVM report, int ssnType, bool forPreview = false);
        bool DeleteReport(int id);
        List<CustomReportVM> ReportsList(int companyid, string clientid, string user, string level);
        CustomReportVM GetCustomReport(int id);
        List<string> AvailableFunctions(int type);
        List<string> AvailableOperators(int type);
        List<string> AvailableSmartValues(int type);
        bool ReportCopy(int id, out string msg);
        bool AssignReportToClient(int companyid, string clientid, int id, out string msg);
        bool ExportReport(int id, out string msg);
        string ExportReportFile(int id, out string msg);
        List<Code_Description> CompanyClients(int companyId);
        List<CustomReportUsersVM> GetAvailableUsers(string creator, string level, short type, int companyid = 0, string client = Constants.ANY);
        List<Code_Description> GetAvailableCompanies(string creator);
        string GetUserSecurity(string user, short type);
    }
}
