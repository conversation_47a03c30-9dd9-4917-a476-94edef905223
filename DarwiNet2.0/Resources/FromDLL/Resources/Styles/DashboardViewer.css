/*!
 * jQuery UI CSS Framework 1.8.21
 *
 * Copyright 2012, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 */

/* Layout helpers
----------------------------------*/
.db-ui-helper-hidden { display: none; }
.db-ui-helper-hidden-accessible { position: absolute !important; clip: rect(1px 1px 1px 1px); clip: rect(1px,1px,1px,1px); }
.db-ui-helper-reset { margin: 0; padding: 0; border: 0; outline: 0; line-height: 1.3; text-decoration: none; font-size: 100%; list-style: none; }
.db-ui-helper-clearfix:before, .db-ui-helper-clearfix:after { content: ""; display: table; }
.db-ui-helper-clearfix:after { clear: both; }
.db-ui-helper-clearfix { zoom: 1; }
.db-ui-helper-zfix { width: 100%; height: 100%; top: 0; left: 0; position: absolute; opacity: 0; filter:Alpha(Opacity=0); }


/* Interaction Cues
----------------------------------*/
.db-ui-state-disabled { cursor: default !important; }


/* Icons
----------------------------------*/

/* states and images */
.db-ui-icon { display: block; text-indent: -99999px; overflow: hidden; background-repeat: no-repeat; }


/* Misc visuals
----------------------------------*/

/* Overlays */
.db-ui-widget-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }


/*!
 * jQuery UI CSS Framework 1.8.21
 *
 * Copyright 2012, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 *
 * To view and modify this theme, visit http://jqueryui.com/themeroller/?ctl=themeroller
 */


/* Component containers
----------------------------------*/
.db-ui-widget { font-family: Verdana,Arial,sans-serif; font-size: 0.5em; }
.db-ui-widget .db-ui-widget { font-size: 1em; }
.db-ui-widget input, .db-ui-widget select, .db-ui-widget textarea, .db-ui-widget button { font-family: Verdana,Arial,sans-serif; font-size: 1em; }
.db-ui-widget-content { border: 1px solid #aaaaaa; background: #ffffff url(###RS###image=jQuery.iz-ui-bg_glass_75_ffffff_1x400.png) 50% 50% repeat-x; color: #222222; }
.db-ui-widget-content a { color: #222222; }
.db-ui-widget-header { background: #dddddd; color: #222222; font-weight: bold; }
.db-ui-widget-header a { color: #222222; }

/* Interaction states
----------------------------------*/
.db-ui-state-default, .db-ui-widget-content .db-ui-state-default, .db-ui-widget-header .db-ui-state-default { border: 1px solid #d3d3d3; background: #e6e6e6 url(###RS###image=jQuery.iz-ui-bg_glass_75_e6e6e6_1x400.png) 50% 50% repeat-x; font-weight: normal; color: #555555; }
.db-ui-state-default a, .db-ui-state-default a:link, .db-ui-state-default a:visited { color: #555555; }
.db-ui-state-hover, .db-ui-widget-content .db-ui-state-hover, .db-ui-widget-header .db-ui-state-hover, .db-ui-state-focus, .db-ui-widget-content .db-ui-state-focus, .db-ui-widget-header .db-ui-state-focus { border: 1px solid #999999; background: #dadada url(###RS###image=jQuery.iz-ui-bg_glass_75_dadada_1x400.png) 50% 50% repeat-x; font-weight: normal; color: #212121; }
.db-ui-state-hover a, .db-ui-state-hover a:hover { color: #212121; text-decoration: none; }
.db-ui-state-active, .db-ui-widget-content .db-ui-state-active, .db-ui-widget-header .db-ui-state-active { border: 1px solid #aaaaaa; background: #ffffff url(###RS###image=jQuery.iz-ui-bg_glass_65_ffffff_1x400.png) 50% 50% repeat-x; font-weight: normal; color: #212121; }
.db-ui-state-active a, .db-ui-state-active a:link, .db-ui-state-active a:visited { color: #212121; }
.db-ui-widget :active { outline: none; }

/* Interaction Cues
----------------------------------*/
.db-ui-state-highlight, .db-ui-widget-content .db-ui-state-highlight, .db-ui-widget-header .db-ui-state-highlight  {border: 1px solid #fcefa1; background: #fbf9ee url(###RS###image=jQuery.iz-ui-bg_glass_55_fbf9ee_1x400.png) 50% 50% repeat-x; color: #363636; }
.db-ui-state-highlight a, .db-ui-widget-content .db-ui-state-highlight a,.db-ui-widget-header .db-ui-state-highlight a { color: #363636; }
.db-ui-state-error, .db-ui-widget-content .db-ui-state-error, .db-ui-widget-header .db-ui-state-error {border: 1px solid #cd0a0a; background: #fef1ec url(###RS###image=jQuery.iz-ui-bg_inset-soft_95_fef1ec_1x100.png) 50% bottom repeat-x; color: #cd0a0a; }
.db-ui-state-error a, .db-ui-widget-content .db-ui-state-error a, .db-ui-widget-header .db-ui-state-error a { color: #cd0a0a; }
.db-ui-state-error-text, .db-ui-widget-content .db-ui-state-error-text, .db-ui-widget-header .db-ui-state-error-text { color: #cd0a0a; }
.db-ui-priority-primary, .db-ui-widget-content .db-ui-priority-primary, .db-ui-widget-header .db-ui-priority-primary { font-weight: bold; }
.db-ui-priority-secondary, .db-ui-widget-content .db-ui-priority-secondary,  .db-ui-widget-header .db-ui-priority-secondary { opacity: .7; filter:Alpha(Opacity=70); font-weight: normal; }
.db-ui-state-disabled, .db-ui-widget-content .db-ui-state-disabled, .db-ui-widget-header .db-ui-state-disabled { opacity: .35; filter:Alpha(Opacity=35); background-image: none; }

/* Icons
----------------------------------*/

/* states and images */
.db-ui-icon { width: 16px; height: 16px; background-image: url(###RS###image=jQuery.iz-ui-icons_222222_256x240.png); }
.db-ui-widget-content .db-ui-icon {background-image: url(###RS###image=jQuery.iz-ui-icons_222222_256x240.png); }
.db-ui-widget-header .db-ui-icon {background-image: url(###RS###image=jQuery.iz-ui-icons_222222_256x240.png); }
.db-ui-state-default .db-ui-icon { background-image: url(###RS###image=jQuery.iz-ui-icons_888888_256x240.png); }
.db-ui-state-hover .db-ui-icon, .db-ui-state-focus .db-ui-icon {background-image: url(###RS###image=jQuery.iz-ui-icons_454545_256x240.png); }
.db-ui-state-active .db-ui-icon {background-image: url(###RS###image=jQuery.iz-ui-icons_454545_256x240.png); }
.db-ui-state-highlight .db-ui-icon {background-image: url(###RS###image=jQuery.iz-ui-icons_2e83ff_256x240.png); }
.db-ui-state-error .db-ui-icon, .db-ui-state-error-text .db-ui-icon {background-image: url(###RS###image=jQuery.iz-ui-icons_cd0a0a_256x240.png); }

/* positioning */
.db-ui-icon-carat-1-n { background-position: 0 0; }
.db-ui-icon-carat-1-ne { background-position: -16px 0; }
.db-ui-icon-carat-1-e { background-position: -32px 0; }
.db-ui-icon-carat-1-se { background-position: -48px 0; }
.db-ui-icon-carat-1-s { background-position: -64px 0; }
.db-ui-icon-carat-1-sw { background-position: -80px 0; }
.db-ui-icon-carat-1-w { background-position: -96px 0; }
.db-ui-icon-carat-1-nw { background-position: -112px 0; }
.db-ui-icon-carat-2-n-s { background-position: -128px 0; }
.db-ui-icon-carat-2-e-w { background-position: -144px 0; }
.db-ui-icon-triangle-1-n { background-position: 0 -16px; }
.db-ui-icon-triangle-1-ne { background-position: -16px -16px; }
.db-ui-icon-triangle-1-e { background-position: -32px -16px; }
.db-ui-icon-triangle-1-se { background-position: -48px -16px; }
.db-ui-icon-triangle-1-s { background-position: -64px -16px; }
.db-ui-icon-triangle-1-sw { background-position: -80px -16px; }
.db-ui-icon-triangle-1-w { background-position: -96px -16px; }
.db-ui-icon-triangle-1-nw { background-position: -112px -16px; }
.db-ui-icon-triangle-2-n-s { background-position: -128px -16px; }
.db-ui-icon-triangle-2-e-w { background-position: -144px -16px; }
.db-ui-icon-arrow-1-n { background-position: 0 -32px; }
.db-ui-icon-arrow-1-ne { background-position: -16px -32px; }
.db-ui-icon-arrow-1-e { background-position: -32px -32px; }
.db-ui-icon-arrow-1-se { background-position: -48px -32px; }
.db-ui-icon-arrow-1-s { background-position: -64px -32px; }
.db-ui-icon-arrow-1-sw { background-position: -80px -32px; }
.db-ui-icon-arrow-1-w { background-position: -96px -32px; }
.db-ui-icon-arrow-1-nw { background-position: -112px -32px; }
.db-ui-icon-arrow-2-n-s { background-position: -128px -32px; }
.db-ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }
.db-ui-icon-arrow-2-e-w { background-position: -160px -32px; }
.db-ui-icon-arrow-2-se-nw { background-position: -176px -32px; }
.db-ui-icon-arrowstop-1-n { background-position: -192px -32px; }
.db-ui-icon-arrowstop-1-e { background-position: -208px -32px; }
.db-ui-icon-arrowstop-1-s { background-position: -224px -32px; }
.db-ui-icon-arrowstop-1-w { background-position: -240px -32px; }
.db-ui-icon-arrowthick-1-n { background-position: 0 -48px; }
.db-ui-icon-arrowthick-1-ne { background-position: -16px -48px; }
.db-ui-icon-arrowthick-1-e { background-position: -32px -48px; }
.db-ui-icon-arrowthick-1-se { background-position: -48px -48px; }
.db-ui-icon-arrowthick-1-s { background-position: -64px -48px; }
.db-ui-icon-arrowthick-1-sw { background-position: -80px -48px; }
.db-ui-icon-arrowthick-1-w { background-position: -96px -48px; }
.db-ui-icon-arrowthick-1-nw { background-position: -112px -48px; }
.db-ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }
.db-ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }
.db-ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }
.db-ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }
.db-ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }
.db-ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }
.db-ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }
.db-ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }
.db-ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }
.db-ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }
.db-ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }
.db-ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }
.db-ui-icon-arrowreturn-1-w { background-position: -64px -64px; }
.db-ui-icon-arrowreturn-1-n { background-position: -80px -64px; }
.db-ui-icon-arrowreturn-1-e { background-position: -96px -64px; }
.db-ui-icon-arrowreturn-1-s { background-position: -112px -64px; }
.db-ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }
.db-ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }
.db-ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }
.db-ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }
.db-ui-icon-arrow-4 { background-position: 0 -80px; }
.db-ui-icon-arrow-4-diag { background-position: -16px -80px; }
.db-ui-icon-extlink { background-position: -32px -80px; }
.db-ui-icon-newwin { background-position: -48px -80px; }
.db-ui-icon-refresh { background-position: -64px -80px; }
.db-ui-icon-shuffle { background-position: -80px -80px; }
.db-ui-icon-transfer-e-w { background-position: -96px -80px; }
.db-ui-icon-transferthick-e-w { background-position: -112px -80px; }
.db-ui-icon-folder-collapsed { background-position: 0 -96px; }
.db-ui-icon-folder-open { background-position: -16px -96px; }
.db-ui-icon-document { background-position: -32px -96px; }
.db-ui-icon-document-b { background-position: -48px -96px; }
.db-ui-icon-note { background-position: -64px -96px; }
.db-ui-icon-mail-closed { background-position: -80px -96px; }
.db-ui-icon-mail-open { background-position: -96px -96px; }
.db-ui-icon-suitcase { background-position: -112px -96px; }
.db-ui-icon-comment { background-position: -128px -96px; }
.db-ui-icon-person { background-position: -144px -96px; }
.db-ui-icon-print { background-position: -160px -96px; }
.db-ui-icon-trash { background-position: -176px -96px; }
.db-ui-icon-locked { background-position: -192px -96px; }
.db-ui-icon-unlocked { background-position: -208px -96px; }
.db-ui-icon-bookmark { background-position: -224px -96px; }
.db-ui-icon-tag { background-position: -240px -96px; }
.db-ui-icon-home { background-position: 0 -112px; }
.db-ui-icon-flag { background-position: -16px -112px; }
.db-ui-icon-calendar { background-position: -32px -112px; }
.db-ui-icon-cart { background-position: -48px -112px; }
.db-ui-icon-pencil { background-position: -64px -112px; }
.db-ui-icon-clock { background-position: -80px -112px; }
.db-ui-icon-disk { background-position: -96px -112px; }
.db-ui-icon-calculator { background-position: -112px -112px; }
.db-ui-icon-zoomin { background-position: -128px -112px; }
.db-ui-icon-zoomout { background-position: -144px -112px; }
.db-ui-icon-search { background-position: -160px -112px; }
.db-ui-icon-wrench { background-position: -176px -112px; }
.db-ui-icon-gear { background-position: -193px -113px; }
.db-ui-icon-heart { background-position: -208px -112px; }
.db-ui-icon-star { background-position: -224px -112px; }
.db-ui-icon-link { background-position: -240px -112px; }
.db-ui-icon-cancel { background-position: 0 -128px; }
.db-ui-icon-plus { background-position: -16px -128px; }
.db-ui-icon-plusthick { background-position: -32px -128px; }
.db-ui-icon-minus { background-position: -48px -128px; }
.db-ui-icon-minusthick { background-position: -64px -128px; }
.db-ui-icon-close { background-position: -80px -128px; }
.db-ui-icon-closethick { background-position: -96px -128px; }
.db-ui-icon-key { background-position: -112px -128px; }
.db-ui-icon-lightbulb { background-position: -128px -128px; }
.db-ui-icon-scissors { background-position: -144px -128px; }
.db-ui-icon-clipboard { background-position: -160px -128px; }
.db-ui-icon-copy { background-position: -176px -128px; }
.db-ui-icon-contact { background-position: -192px -128px; }
.db-ui-icon-image { background-position: -208px -128px; }
.db-ui-icon-video { background-position: -224px -128px; }
.db-ui-icon-script { background-position: -240px -128px; }
.db-ui-icon-alert { background-position: 0 -144px; }
.db-ui-icon-info { background-position: -16px -144px; }
.db-ui-icon-notice { background-position: -32px -144px; }
.db-ui-icon-help { background-position: -48px -144px; }
.db-ui-icon-check { background-position: -64px -144px; }
.db-ui-icon-bullet { background-position: -80px -144px; }
.db-ui-icon-radio-off { background-position: -96px -144px; }
.db-ui-icon-radio-on { background-position: -112px -144px; }
.db-ui-icon-pin-w { background-position: -128px -144px; }
.db-ui-icon-pin-s { background-position: -144px -144px; }
.db-ui-icon-play { background-position: 0 -160px; }
.db-ui-icon-pause { background-position: -16px -160px; }
.db-ui-icon-seek-next { background-position: -32px -160px; }
.db-ui-icon-seek-prev { background-position: -48px -160px; }
.db-ui-icon-seek-end { background-position: -64px -160px; }
.db-ui-icon-seek-start { background-position: -80px -160px; }
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.db-ui-icon-seek-first { background-position: -80px -160px; }
.db-ui-icon-stop { background-position: -96px -160px; }
.db-ui-icon-eject { background-position: -112px -160px; }
.db-ui-icon-volume-off { background-position: -128px -160px; }
.db-ui-icon-volume-on { background-position: -144px -160px; }
.db-ui-icon-power { background-position: 0 -176px; }
.db-ui-icon-signal-diag { background-position: -16px -176px; }
.db-ui-icon-signal { background-position: -32px -176px; }
.db-ui-icon-battery-0 { background-position: -48px -176px; }
.db-ui-icon-battery-1 { background-position: -64px -176px; }
.db-ui-icon-battery-2 { background-position: -80px -176px; }
.db-ui-icon-battery-3 { background-position: -96px -176px; }
.db-ui-icon-circle-plus { background-position: 0 -192px; }
.db-ui-icon-circle-minus { background-position: -16px -192px; }
.db-ui-icon-circle-close { background-position: -32px -192px; }
.db-ui-icon-circle-triangle-e { background-position: -48px -192px; }
.db-ui-icon-circle-triangle-s { background-position: -64px -192px; }
.db-ui-icon-circle-triangle-w { background-position: -80px -192px; }
.db-ui-icon-circle-triangle-n { background-position: -96px -192px; }
.db-ui-icon-circle-arrow-e { background-position: -112px -192px; }
.db-ui-icon-circle-arrow-s { background-position: -128px -192px; }
.db-ui-icon-circle-arrow-w { background-position: -144px -192px; }
.db-ui-icon-circle-arrow-n { background-position: -160px -192px; }
.db-ui-icon-circle-zoomin { background-position: -176px -192px; }
.db-ui-icon-circle-zoomout { background-position: -192px -192px; }
.db-ui-icon-circle-check { background-position: -208px -192px; }
.db-ui-icon-circlesmall-plus { background-position: 0 -208px; }
.db-ui-icon-circlesmall-minus { background-position: -16px -208px; }
.db-ui-icon-circlesmall-close { background-position: -32px -208px; }
.db-ui-icon-squaresmall-plus { background-position: -48px -208px; }
.db-ui-icon-squaresmall-minus { background-position: -64px -208px; }
.db-ui-icon-squaresmall-close { background-position: -80px -208px; }
.db-ui-icon-grip-dotted-vertical { background-position: 0 -224px; }
.db-ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }
.db-ui-icon-grip-solid-vertical { background-position: -32px -224px; }
.db-ui-icon-grip-solid-horizontal { background-position: -48px -224px; }
.db-ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }
.db-ui-icon-grip-diagonal-se { background-position: -80px -224px; }


/* Misc visuals
----------------------------------*/

/* Corner radius */
.db-ui-corner-all, .db-ui-corner-top, .db-ui-corner-left, .db-ui-corner-tl { -moz-border-radius-topleft: 4px; -webkit-border-top-left-radius: 4px; -khtml-border-top-left-radius: 4px; border-top-left-radius: 4px; }
.db-ui-corner-all, .db-ui-corner-top, .db-ui-corner-right, .db-ui-corner-tr { -moz-border-radius-topright: 4px; -webkit-border-top-right-radius: 4px; -khtml-border-top-right-radius: 4px; border-top-right-radius: 4px; }
.db-ui-corner-all, .db-ui-corner-bottom, .db-ui-corner-left, .db-ui-corner-bl { -moz-border-radius-bottomleft: 4px; -webkit-border-bottom-left-radius: 4px; -khtml-border-bottom-left-radius: 4px; border-bottom-left-radius: 4px; }
.db-ui-corner-all, .db-ui-corner-bottom, .db-ui-corner-right, .db-ui-corner-br { -moz-border-radius-bottomright: 4px; -webkit-border-bottom-right-radius: 4px; -khtml-border-bottom-right-radius: 4px; border-bottom-right-radius: 4px; }

/* Overlays */
.db-ui-widget-overlay { background: #aaaaaa url(###RS###image=jQuery.iz-ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x; opacity: .30;filter:Alpha(Opacity=30); }
.db-ui-widget-shadow { margin: -8px 0 0 -8px; padding: 8px; background: #aaaaaa url(###RS###image=jQuery.iz-ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x; opacity: .30;filter:Alpha(Opacity=30); -moz-border-radius: 8px; -khtml-border-radius: 8px; -webkit-border-radius: 8px; border-radius: 8px; }
/*!
 * jQuery UI Button 1.8.21
 *
 * Copyright 2012, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Button#theming
 */
.db-ui-button { display: inline-block; position: relative; padding: 0; margin-right: .1em; text-decoration: none !important; cursor: pointer; text-align: center; zoom: 1; overflow: visible; } /* the overflow property removes extra width in IE */
.db-ui-button-icon-only { width: 16px; } /* to make room for the icon, a width needs to be set here */
button.db-ui-button-icon-only { width: 2.4em; } /* button elements seem to need a little more width */
.db-ui-button-icons-only { width: 3.4em; } 
button.db-ui-button-icons-only { width: 3.7em; } 

/*button text element */
.db-ui-button .db-ui-button-text { display: block; line-height: 1.4;  }
.db-ui-button-text-only .db-ui-button-text { padding: .4em 1em; }
.db-ui-button-icon-only .db-ui-button-text, .db-ui-button-icons-only .db-ui-button-text { padding: .4em; text-indent: -9999999px; }
.db-ui-button-text-icon-primary .db-ui-button-text, .db-ui-button-text-icons .db-ui-button-text { padding: .4em 1em .4em 2.1em; }
.db-ui-button-text-icon-secondary .db-ui-button-text, .db-ui-button-text-icons .db-ui-button-text { padding: .4em 2.1em .4em 1em; }
.db-ui-button-text-icons .db-ui-button-text { padding-left: 2.1em; padding-right: 2.1em; }
/* no icon support for input elements, provide padding by default */
input.db-ui-button { padding: .4em 1em; }

/*button icon element(s) */
.db-ui-button-icon-only .db-ui-icon, .db-ui-button-text-icon-primary .db-ui-icon, .db-ui-button-text-icon-secondary .db-ui-icon, .db-ui-button-text-icons .db-ui-icon, .db-ui-button-icons-only .db-ui-icon { position: absolute; top: 50%; margin-top: -8px; }
.db-ui-button-icon-only .db-ui-icon { left: 50%; margin-left: -8px; }
.db-ui-button-text-icon-primary .db-ui-button-icon-primary, .db-ui-button-text-icons .db-ui-button-icon-primary, .db-ui-button-icons-only .db-ui-button-icon-primary { left: .5em; }
.db-ui-button-text-icon-secondary .db-ui-button-icon-secondary, .db-ui-button-text-icons .db-ui-button-icon-secondary, .db-ui-button-icons-only .db-ui-button-icon-secondary { right: .5em; }
.db-ui-button-text-icons .db-ui-button-icon-secondary, .db-ui-button-icons-only .db-ui-button-icon-secondary { right: .5em; }

/*button sets*/
.db-ui-buttonset { margin-right: 7px; }
.db-ui-buttonset .db-ui-button { margin-left: 0; margin-right: -.3em; }

/* workarounds */
button.db-ui-button::-moz-focus-inner { border: 0; padding: 0; } /* reset extra padding in Firefox */





.db-backgroung-image {
	position:fixed;
	top:0;
	left:0; 
	min-width:100%;
	min-height:100%;
	z-index: 0;
}

#db-loadingScreen{
	font-weight: bold;
	font-size: 3em;
	text-align: center;
	color: #1D5987;
}

#db-cdTabs{
	min-width: 940px;
	background: none;
}

#db-cdTabs.db-ui-widget-content {
	border: none;
}

#db-cdTabs > ul > li{
	float: right;
}

.db-cbControlTab{
	padding: 0.5em;
}

.db-cbControlTab > span{
	font-size: 1.2em;
}

.db-dashboard-tab{
	padding: 2px 0px !important;
}

table.db-dashboard-part-table 
{
	border: 0; 
	border-spacing: 10px;
	width: 100%;
}

table.db-dashboard-part-table tbody tr td{
	vertical-align: top;
}

.db-dashboard-part
{
	border: 2px solid #AAAAAA;
	display: inline-block;
	vertical-align: top;
	/*-moz-box-shadow: 5px 5px 10px rgba(0,0,0,0.5);
	-webkit-box-shadow: 5px 5px 10px rgba(0,0,0,0.5);
	box-shadow: 5px 5px 10px rgba(0,0,0,0.5);*/
	margin: 2px;
}

.db-dashboard-part-title{
	text-align: center;
	font-size: 1em;
	height: 2.5em;
	vertical-align: middle;
	line-height: 1.1em;
}

.db-dashboard-hide-button{
	float: right;
	margin-right: 10px;
	height: 16px;
}

.db-dashboard-view-button, .db-dashboard-design-button{
	float: left;
	margin-left: 10px;
	height: 16px;
}

.db-dashboard-report-content{
	overflow: auto;
	padding: 0px;
	background-color: White;
	position: relative;
}

.db-report-part-container {
	padding: 5px;
}

.db-dashboard-report-content-watermark 
{
	display: none;
	position: absolute;
	width: 100%;
	height: 100%;
	overflow: hidden;
	opacity:.4;
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMTAwJSIgeDI9IjEwMCUiIHkyPSIwJSI+CiAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjZmZmZmZmIiBzdG9wLW9wYWNpdHk9IjAiLz4KICAgIDxzdG9wIG9mZnNldD0iNTclIiBzdG9wLWNvbG9yPSIjZmZmZmZmIiBzdG9wLW9wYWNpdHk9IjAuNTciLz4KICAgIDxzdG9wIG9mZnNldD0iNjglIiBzdG9wLWNvbG9yPSIjMjE4Y2ZmIiBzdG9wLW9wYWNpdHk9IjAuNjgiLz4KICAgIDxzdG9wIG9mZnNldD0iNzglIiBzdG9wLWNvbG9yPSIjNTRiM2YyIiBzdG9wLW9wYWNpdHk9IjAuNzgiLz4KICAgIDxzdG9wIG9mZnNldD0iODclIiBzdG9wLWNvbG9yPSIjYzFlMGZmIiBzdG9wLW9wYWNpdHk9IjAuODciLz4KICAgIDxzdG9wIG9mZnNldD0iOTclIiBzdG9wLWNvbG9yPSIjMWMyZmQ4IiBzdG9wLW9wYWNpdHk9IjAuOTciLz4KICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iIzFjMmZkOCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgPC9saW5lYXJHcmFkaWVudD4KICA8cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2dyYWQtdWNnZy1nZW5lcmF0ZWQpIiAvPgo8L3N2Zz4=);
	background: -moz-linear-gradient(45deg,  rgba(255,255,255,0) 0%, rgba(255,255,255,0.57) 57%, rgba(33,140,255,0.68) 68%, rgba(84,179,242,0.78) 78%, rgba(193,224,255,0.87) 87%, rgba(28,47,216,0.97) 97%, rgba(28,47,216,1) 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left bottom, right top, color-stop(0%,rgba(255,255,255,0)), color-stop(57%,rgba(255,255,255,0.57)), color-stop(68%,rgba(33,140,255,0.68)), color-stop(78%,rgba(84,179,242,0.78)), color-stop(87%,rgba(193,224,255,0.87)), color-stop(97%,rgba(28,47,216,0.97)), color-stop(100%,rgba(28,47,216,1))); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(45deg,  rgba(255,255,255,0) 0%,rgba(255,255,255,0.57) 57%,rgba(33,140,255,0.68) 68%,rgba(84,179,242,0.78) 78%,rgba(193,224,255,0.87) 87%,rgba(28,47,216,0.97) 97%,rgba(28,47,216,1) 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(45deg,  rgba(255,255,255,0) 0%,rgba(255,255,255,0.57) 57%,rgba(33,140,255,0.68) 68%,rgba(84,179,242,0.78) 78%,rgba(193,224,255,0.87) 87%,rgba(28,47,216,0.97) 97%,rgba(28,47,216,1) 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(45deg,  rgba(255,255,255,0) 0%,rgba(255,255,255,0.57) 57%,rgba(33,140,255,0.68) 68%,rgba(84,179,242,0.78) 78%,rgba(193,224,255,0.87) 87%,rgba(28,47,216,0.97) 97%,rgba(28,47,216,1) 100%); /* IE10+ */
	background: linear-gradient(45deg,  rgba(255,255,255,0) 0%,rgba(255,255,255,0.57) 57%,rgba(33,140,255,0.68) 68%,rgba(84,179,242,0.78) 78%,rgba(193,224,255,0.87) 87%,rgba(28,47,216,0.97) 97%,rgba(28,47,216,1) 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ffffff', endColorstr='#1c2fd8',GradientType=1 ); /* IE6-8 fallback on horizontal gradient */
}

.db-dashboard-report-icon {
	height: 30px;
	vertical-align: top;
}

.db-tab-title{
	font-size: 36px; 
	font-weight: bold; 
	text-shadow: #2E6E9E 2px 2px 2px;
	color: white;
	margin-left: 15px;
	vertical-align: middle;
}

.db-tab-subtitle{
	color: white;
	font-size: 20px;
	text-shadow: #2E6E9E 2px 2px 2px;
	margin: 0px 10px 0px 10px;
	vertical-align: middle;
}

.db-tab-icon{
	height: 40px;
	vertical-align: middle;
	margin-left: 25px;
}

.db-refresh-button{
	float: right;
}