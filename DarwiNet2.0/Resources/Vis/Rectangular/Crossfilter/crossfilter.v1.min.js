!function(r){function n(r){return r}function t(r,n){for(var t=0,e=n.length,u=Array(e);e>t;++t)u[t]=r[n[t]];return u}function e(r){function n(n,t,e,u){for(;u>e;){var f=e+u>>>1;r(n[f])<t?e=f+1:u=f}return e}function t(n,t,e,u){for(;u>e;){var f=e+u>>>1;t<r(n[f])?u=f:e=f+1}return e}return t.right=t,t.left=n,t}function u(r){function n(r,n,t){for(var u=t-n,f=(u>>>1)+1;--f>0;)e(r,f,u,n);return r}function t(r,n,t){for(var u,f=t-n;--f>0;)u=r[n],r[n]=r[n+f],r[n+f]=u,e(r,1,f,n);return r}function e(n,t,e,u){for(var f,o=n[--u+t],i=r(o);(f=t<<1)<=e&&(e>f&&r(n[u+f])>r(n[u+f+1])&&f++,!(i<=r(n[u+f])));)n[u+t]=n[u+f],t=f;n[u+t]=o}return n.sort=t,n}function f(r){function n(n,e,u,f){var o,i,a,c,l=Array(f=Math.min(u-e,f));for(i=0;f>i;++i)l[i]=n[e++];if(t(l,0,f),u>e){o=r(l[0]);do(a=r(c=n[e])>o)&&(l[0]=c,o=r(t(l,0,f)[0]));while(++e<u)}return l}var t=u(r);return n}function o(r){function n(n,t,e){for(var u=t+1;e>u;++u){for(var f=u,o=n[u],i=r(o);f>t&&r(n[f-1])>i;--f)n[f]=n[f-1];n[f]=o}return n}return n}function i(r){function n(r,n,u){return(U>u-n?e:t)(r,n,u)}function t(t,e,u){var f,o=0|(u-e)/6,i=e+o,a=u-1-o,c=e+u-1>>1,l=c-o,v=c+o,s=t[i],h=r(s),p=t[l],d=r(p),g=t[c],y=r(g),m=t[v],b=r(m),x=t[a],A=r(x);h>d&&(f=s,s=p,p=f,f=h,h=d,d=f),b>A&&(f=m,m=x,x=f,f=b,b=A,A=f),h>y&&(f=s,s=g,g=f,f=h,h=y,y=f),d>y&&(f=p,p=g,g=f,f=d,d=y,y=f),h>b&&(f=s,s=m,m=f,f=h,h=b,b=f),y>b&&(f=g,g=m,m=f,f=y,y=b,b=f),d>A&&(f=p,p=x,x=f,f=d,d=A,A=f),d>y&&(f=p,p=g,g=f,f=d,d=y,y=f),b>A&&(f=m,m=x,x=f,f=b,b=A,A=f);var k=p,O=d,E=m,w=b;t[i]=s,t[l]=t[e],t[c]=g,t[v]=t[u-1],t[a]=x;var M=e+1,U=u-2,z=w>=O&&O>=w;if(z)for(var N=M;U>=N;++N){var C=t[N],S=r(C);if(O>S)N!==M&&(t[N]=t[M],t[M]=C),++M;else if(S>O)for(;;){var q=r(t[U]);{if(!(q>O)){if(O>q){t[N]=t[M],t[M++]=t[U],t[U--]=C;break}t[N]=t[U],t[U--]=C;break}U--}}}else for(var N=M;U>=N;N++){var C=t[N],S=r(C);if(O>S)N!==M&&(t[N]=t[M],t[M]=C),++M;else if(S>w)for(;;){var q=r(t[U]);{if(!(q>w)){O>q?(t[N]=t[M],t[M++]=t[U],t[U--]=C):(t[N]=t[U],t[U--]=C);break}if(U--,N>U)break}}}if(t[e]=t[M-1],t[M-1]=k,t[u-1]=t[U+1],t[U+1]=E,n(t,e,M-1),n(t,U+2,u),z)return t;if(i>M&&U>a){for(var F,q;(F=r(t[M]))<=O&&F>=O;)++M;for(;(q=r(t[U]))<=w&&q>=w;)--U;for(var N=M;U>=N;N++){var C=t[N],S=r(C);if(O>=S&&S>=O)N!==M&&(t[N]=t[M],t[M]=C),M++;else if(w>=S&&S>=w)for(;;){var q=r(t[U]);{if(!(w>=q&&q>=w)){O>q?(t[N]=t[M],t[M++]=t[U],t[U--]=C):(t[N]=t[U],t[U--]=C);break}if(U--,N>U)break}}}}return n(t,M,U+1)}var e=o(r);return n}function a(r){return Array(r)}function c(r,n){return function(t){var e=t.length;return[r.left(t,n,0,e),r.right(t,n,0,e)]}}function l(r,n){var t=n[0],e=n[1];return function(n){var u=n.length;return[r.left(n,t,0,u),r.left(n,e,0,u)]}}function v(r){return[0,r.length]}function s(){return null}function h(){return 0}function p(r){return r+1}function d(r){return r-1}function g(r){return function(n,t){return n+ +r(t)}}function y(r){return function(n,t){return n-r(t)}}function m(){function r(r){var n=w,t=r.length;return t&&(E=E.concat(r),N=S(N,w+=t),F.forEach(function(e){e(r,n,t)})),O}function e(){for(var r=b(w,w),n=[],t=0,e=0;w>t;++t)N[t]?r[t]=e++:n.push(t);C.forEach(function(r){r(0,[],n)}),R.forEach(function(n){n(r)});for(var u,t=0,e=0;w>t;++t)(u=N[t])&&(t!==e&&(N[e]=u,E[e]=E[t]),++e);for(E.length=e;w>e;)N[--w]=0}function o(r){function e(n,e,u){T=n.map(r),V=$(x(u),0,u),T=t(T,V);var f,o=_(T),i=o[0],a=o[1];if(W)for(f=0;u>f;++f)W(T[f],f)||(N[V[f]+e]|=Y);else{for(f=0;i>f;++f)N[V[f]+e]|=Y;for(f=a;u>f;++f)N[V[f]+e]|=Y}if(!e)return P=T,Q=V,tn=i,en=a,void 0;var c=P,l=Q,v=0,s=0;for(P=Array(w),Q=b(w,w),f=0;e>v&&u>s;++f)c[v]<T[s]?(P[f]=c[v],Q[f]=l[v++]):(P[f]=T[s],Q[f]=V[s++]+e);for(;e>v;++v,++f)P[f]=c[v],Q[f]=l[v];for(;u>s;++s,++f)P[f]=T[s],Q[f]=V[s]+e;o=_(P),tn=o[0],en=o[1]}function o(r,n,t){rn.forEach(function(r){r(T,V,n,t)}),T=V=null}function a(r){for(var n,t=0,e=0;w>t;++t)N[n=Q[t]]&&(t!==e&&(P[e]=P[t]),Q[e]=r[n],++e);for(P.length=e;w>e;)Q[e++]=0;var u=_(P);tn=u[0],en=u[1]}function m(r){var n=r[0],t=r[1];if(W)return W=null,G(function(r,e){return e>=n&&t>e}),tn=n,en=t,X;var e,u,f,o=[],i=[];if(tn>n)for(e=n,u=Math.min(tn,t);u>e;++e)N[f=Q[e]]^=Y,o.push(f);else if(n>tn)for(e=tn,u=Math.min(n,en);u>e;++e)N[f=Q[e]]^=Y,i.push(f);if(t>en)for(e=Math.max(n,en),u=t;u>e;++e)N[f=Q[e]]^=Y,o.push(f);else if(en>t)for(e=Math.max(tn,t),u=en;u>e;++e)N[f=Q[e]]^=Y,i.push(f);return tn=n,en=t,C.forEach(function(r){r(Y,o,i)}),X}function O(r){return null==r?B():Array.isArray(r)?j(r):"function"==typeof r?D(r):z(r)}function z(r){return m((_=c(k,r))(P))}function j(r){return m((_=l(k,r))(P))}function B(){return m((_=v)(P))}function D(r){return _=v,G(W=r),tn=0,en=w,X}function G(r){var n,t,e,u=[],f=[];for(n=0;w>n;++n)!(N[t=Q[n]]&Y)^(e=r(P[n],n))&&(e?(N[t]&=Z,u.push(t)):(N[t]|=Y,f.push(t)));C.forEach(function(r){r(Y,u,f)})}function H(r){for(var n,t=[],e=en;--e>=tn&&r>0;)N[n=Q[e]]||(t.push(E[n]),--r);return t}function I(r){for(var n,t=[],e=tn;en>e&&r>0;)N[n=Q[e]]||(t.push(E[n]),--r),e++;return t}function J(r){function t(n,t,e,u){function f(){++T===L&&(m=q(m,K<<=1),B=q(B,K),L=A(K))}var l,v,h,p,d,g,y=j,m=b(T,L),x=H,k=J,O=T,M=0,U=0;for(X&&(x=k=s),j=Array(T),T=0,B=O>1?S(B,w):b(w,L),O&&(h=(v=y[0]).key);u>U&&!((p=r(n[U]))>=p);)++U;for(;u>U;){for(v&&p>=h?(d=v,g=h,m[M]=T,(v=y[++M])&&(h=v.key)):(d={key:p,value:k()},g=p),j[T]=d;!(p>g||(B[l=t[U]+e]=T,N[l]&Z||(d.value=x(d.value,E[l])),++U>=u));)p=r(n[U]);f()}for(;O>M;)j[m[M]=T]=y[M++],f();if(T>M)for(M=0;e>M;++M)B[M]=m[B[M]];l=C.indexOf(V),T>1?(V=o,W=a):(1===T?(V=i,W=c):(V=s,W=s),B=null),C[l]=V}function e(){if(T>1){for(var r=T,n=j,t=b(r,r),e=0,u=0;w>e;++e)N[e]&&(t[B[u]=B[e]]=1,++u);for(j=[],T=0,e=0;r>e;++e)t[e]&&(t[e]=T++,j.push(n[e]));if(T>1)for(var e=0;u>e;++e)B[e]=t[B[e]];else B=null;C[C.indexOf(V)]=T>1?(W=a,V=o):1===T?(W=c,V=i):W=V=s}else if(1===T){for(var e=0;w>e;++e)if(N[e])return;j=[],T=0,C[C.indexOf(V)]=V=W=s}}function o(r,n,t){if(r!==Y&&!X){var e,u,f,o;for(e=0,f=n.length;f>e;++e)N[u=n[e]]&Z||(o=j[B[u]],o.value=H(o.value,E[u]));for(e=0,f=t.length;f>e;++e)(N[u=t[e]]&Z)===r&&(o=j[B[u]],o.value=I(o.value,E[u]))}}function i(r,n,t){if(r!==Y&&!X){var e,u,f,o=j[0];for(e=0,f=n.length;f>e;++e)N[u=n[e]]&Z||(o.value=H(o.value,E[u]));for(e=0,f=t.length;f>e;++e)(N[u=t[e]]&Z)===r&&(o.value=I(o.value,E[u]))}}function a(){var r,n;for(r=0;T>r;++r)j[r].value=J();for(r=0;w>r;++r)N[r]&Z||(n=j[B[r]],n.value=H(n.value,E[r]))}function c(){var r,n=j[0];for(n.value=J(),r=0;w>r;++r)N[r]&Z||(n.value=H(n.value,E[r]))}function l(){return X&&(W(),X=!1),j}function v(r){var n=D(l(),0,j.length,r);return G.sort(n,0,n.length)}function m(r,n,t){return H=r,I=n,J=t,X=!0,F}function x(){return m(p,d,h)}function k(r){return m(g(r),y(r),h)}function O(r){function n(n){return r(n.value)}return D=f(n),G=u(n),F}function M(){return O(n)}function U(){return T}function z(){var r=C.indexOf(V);return r>=0&&C.splice(r,1),r=rn.indexOf(t),r>=0&&rn.splice(r,1),r=R.indexOf(e),r>=0&&R.splice(r,1),F}var F={top:v,all:l,reduce:m,reduceCount:x,reduceSum:k,order:O,orderNatural:M,size:U,dispose:z,remove:z};nn.push(F);var j,B,D,G,H,I,J,K=8,L=A(K),T=0,V=s,W=s,X=!0;return arguments.length<1&&(r=n),C.push(V),rn.push(t),R.push(e),t(P,Q,0,w),x().orderNatural()}function K(){var r=J(s),n=r.all;return delete r.all,delete r.top,delete r.order,delete r.orderNatural,delete r.size,r.value=function(){return n()[0].value},r}function L(){nn.forEach(function(r){r.dispose()});var r=F.indexOf(e);for(r>=0&&F.splice(r,1),r=F.indexOf(o),r>=0&&F.splice(r,1),r=R.indexOf(a),r>=0&&R.splice(r,1),r=0;w>r;++r)N[r]&=Z;return M&=Z,X}var P,Q,T,V,W,X={filter:O,filterExact:z,filterRange:j,filterFunction:D,filterAll:B,top:H,bottom:I,group:J,groupAll:K,dispose:L,remove:L},Y=~M&-~M,Z=~Y,$=i(function(r){return T[r]}),_=v,rn=[],nn=[],tn=0,en=0;return F.unshift(e),F.push(o),R.push(a),M|=Y,(U>=32?!Y:M&(1<<U)-1)&&(N=q(N,U<<=1)),e(E,0,w),o(E,0,w),X}function a(){function r(r,n){var t;if(!m)for(t=n;w>t;++t)N[t]||(a=c(a,E[t]))}function n(r,n,t){var e,u,f;if(!m){for(e=0,f=n.length;f>e;++e)N[u=n[e]]||(a=c(a,E[u]));for(e=0,f=t.length;f>e;++e)N[u=t[e]]===r&&(a=l(a,E[u]))}}function t(){var r;for(a=v(),r=0;w>r;++r)N[r]||(a=c(a,E[r]))}function e(r,n,t){return c=r,l=n,v=t,m=!0,s}function u(){return e(p,d,h)}function f(r){return e(g(r),y(r),h)}function o(){return m&&(t(),m=!1),a}function i(){var t=C.indexOf(n);return t>=0&&C.splice(t),t=F.indexOf(r),t>=0&&F.splice(t),s}var a,c,l,v,s={reduce:e,reduceCount:u,reduceSum:f,value:o,dispose:i,remove:i},m=!0;return C.push(n),F.push(r),r(E,0,w),u()}function m(){return w}var O={add:r,remove:e,dimension:o,groupAll:a,size:m},E=[],w=0,M=0,U=8,N=z(0),C=[],F=[],R=[];return arguments.length?r(arguments[0]):O}function b(r,n){return(257>n?z:65537>n?N:C)(r)}function x(r){for(var n=b(r,r),t=-1;++t<r;)n[t]=t;return n}function A(r){return 8===r?256:16===r?65536:4294967296}m.version="1.3.5",m.permute=t;var k=m.bisect=e(n);k.by=e;var O=m.heap=u(n);O.by=u;var E=m.heapselect=f(n);E.by=f;var w=m.insertionsort=o(n);w.by=o;var M=m.quicksort=i(n);M.by=i;var U=32,z=a,N=a,C=a,S=n,q=n;"undefined"!=typeof Uint8Array&&(z=function(r){return new Uint8Array(r)},N=function(r){return new Uint16Array(r)},C=function(r){return new Uint32Array(r)},S=function(r,n){if(r.length>=n)return r;var t=new r.constructor(n);return t.set(r),t},q=function(r,n){var t;switch(n){case 16:t=N(r.length);break;case 32:t=C(r.length);break;default:throw Error("invalid array width!")}return t.set(r),t}),r.crossfilter=m}(this);