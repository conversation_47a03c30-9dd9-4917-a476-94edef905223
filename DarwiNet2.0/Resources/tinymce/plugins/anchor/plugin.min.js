tinymce.PluginManager.add("anchor",function(a){function b(){var b=a.selection.getNode(),c="";"A"==b.tagName&&(c=b.name||b.id||""),a.windowManager.open({title:"Anchor",body:{type:"textbox",name:"name",size:40,label:"Name",value:c},onsubmit:function(b){a.execCommand("mceInsertContent",!1,a.dom.createHTML("a",{id:b.data.name}))}})}a.addButton("anchor",{icon:"anchor",tooltip:"Anchor",onclick:b,stateSelector:"a:not([href])"}),a.addMenuItem("anchor",{icon:"anchor",text:"Anchor",context:"insert",onclick:b})});