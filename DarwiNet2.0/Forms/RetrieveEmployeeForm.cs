using DarwiNet2._0.Models.API;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Web;
using System.Web.Http.ModelBinding;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Forms
{
    public class RetrieveEmployeeForm : AbstractRegistrationForm
    {
        private EmployeeRegistrationDTO EmployeeInfo { get; }

        public RetrieveEmployeeForm(DnetEntities dbContext, EmployeeRegistrationDTO employeeInfo)
        {
            DbContext = dbContext;
            EmployeeInfo = employeeInfo;
        }

        public override bool Valid(out ModelStateDictionary errors)
        {
            errors = new ModelStateDictionary();

            ValidateRequiredFields(errors);
            if (!errors.IsValid)
                return false;

            ValidateEmployeeExist(errors);
            if (!errors.IsValid)
                return false;

            ValidateEmployeeIsActive(errors);
            if (!errors.IsValid)
                return false;

            ValidateEmployerSetupInCheck(errors);
            return errors.IsValid;
        }

        protected override void ValidateRequiredFields(ModelStateDictionary errors)
        {
            if (string.IsNullOrEmpty(EmployeeInfo.EmployeeId))
                errors.AddModelError("employeeId", "Is a required field");

            if (EmployeeInfo.BirthDate == null)
                errors.AddModelError("birthDate", "Is a required field");

            if (string.IsNullOrEmpty(EmployeeInfo.Ssn))
                errors.AddModelError("ssn", "Is a required field");
        }

        private Employee _employee;
        public override Employee Employee
        {
            get
            {
                if (_employee == null)
                {
                    using (var dbContext = new DnetEntities())
                    {
                        _employee =
                            dbContext.Employees.
                                FirstOrDefault(emp => emp.EmployeeID == EmployeeInfo.EmployeeId &&
                                                        emp.SSN == EmployeeInfo.Ssn &&
                                                        emp.BirthDate == EmployeeInfo.BirthDate);
                    }
                }

                return _employee;
            }
        }
    }
}