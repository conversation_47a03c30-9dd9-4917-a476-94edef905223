using DarwiNet2._0.Models.API;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http.ModelBinding;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Forms
{
    public class SecurityQuestionForm
    {
        private DnetEntities _dbContext;
        private SecurityAnswersDTO _securityAnswers;

        public SecurityQuestionForm(DnetEntities dbContext, SecurityAnswersDTO securityAnswers)
        {
            _dbContext = dbContext;
            _securityAnswers = securityAnswers;
        }

        public bool Valid(out ModelStateDictionary errors)
        {
            errors = new ModelStateDictionary();

            ValidateRequiredFields(errors);
            ValidateUserExists(errors);

            return errors.IsValid;
        }

        private void ValidateRequiredFields(ModelStateDictionary errors)
        {
            if (string.IsNullOrEmpty(_securityAnswers.Answer1))
                errors.AddModelError("answer1", "Is a required field.");

            if (string.IsNullOrEmpty(_securityAnswers.Answer2))
                errors.AddModelError("answer2", "Is a required field.");

            if (string.IsNullOrEmpty(_securityAnswers.Answer3))
                errors.AddModelError("answer3", "Is a required field.");
        }

        private void ValidateUserExists(ModelStateDictionary errors)
        {
            if (User == null)
                errors.AddModelError("global", "This User does not exist");
        }

        public void CreateSecurityAnswers()
        {
            SecurityAnswer answer1 = new SecurityAnswer();
            answer1.UserID = User.UserID;
            answer1.QuestionID = _securityAnswers.QuestionId1;
            answer1.AnswerText = _securityAnswers.EncryptAnswer1;
            _dbContext.SecurityAnswers.Add(answer1);

            SecurityAnswer answer2 = new SecurityAnswer();
            answer2.UserID = User.UserID;
            answer2.QuestionID = _securityAnswers.QuestionId2;
            answer2.AnswerText = _securityAnswers.EncryptAnswer2;
            _dbContext.SecurityAnswers.Add(answer2);

            SecurityAnswer answer3 = new SecurityAnswer();
            answer3.UserID = User.UserID;
            answer3.QuestionID = _securityAnswers.QuestionId3;
            answer3.AnswerText = _securityAnswers.EncryptAnswer3;
            _dbContext.SecurityAnswers.Add(answer3);

            User.MobileRegistrationToken = null;

            _dbContext.SaveChanges();
        }

        private User _user;
        private User User
        {
            get
            {
                if (_user == null)
                {
                    _user =
                        _dbContext.Users.
                            FirstOrDefault(u => u.UserID == _securityAnswers.Username && u.MobileRegistrationToken == _securityAnswers.TmpToken);
                }

                return _user;
            }
        }
    }
}