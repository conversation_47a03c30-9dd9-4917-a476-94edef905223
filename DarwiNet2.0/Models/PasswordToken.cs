using DarwiNet2._0.Controllers;
using DarwiNet2._0.DNetSynch;
using System;
using System.Linq;
using DarwiNet2._0.Data;

namespace DarwiNet2._0.Models
{
    public class PasswordToken
    {
        private const string RESET = "RESET";
        private const string SPLIT_CHAR = "_";
        private const string DATETIME_FORMAT = "yyyyMMddHHmm";

        public PasswordToken() { }

        public PasswordToken(string userID)
        {
            Random rnd = new Random();

            UserID = userID;
            Code = rnd.Next(0, 999999).ToString("D6");
            ValidUntil = DateTime.Now.AddMinutes(30);
            Token = string.Concat(userID, RESET, Code, SPLIT_CHAR, ValidUntil.ToString(DATETIME_FORMAT));
            EncryptedToken = ConCryptor.Encrypt(Token);
        }

        public PasswordToken DecryptPasswordToken(string encryptedToken)
        {
            PasswordToken passwordToken = this;

            try
            {
                Token = ConCryptor.Decrypt(encryptedToken);

                int index = Token.IndexOf(RESET);
                int split = Token.IndexOf(SPLIT_CHAR);

                UserID = Token.Substring(0, index);
                Code = Token.Substring(index + 5, split - (index + 5));
                ValidUntil = DateTime.ParseExact(Token.Substring(split + 1), DATETIME_FORMAT, System.Globalization.CultureInfo.InvariantCulture);
                EncryptedToken = encryptedToken;
            }
            catch (Exception)
            {
                return new PasswordToken();
            }

            return passwordToken;
        }

        public bool SetToken(DnetEntities dbContext)
        {
            OBUser obUser = null;
            User user = dbContext.Users.Where(x => x.UserID.ToUpper() == UserID.ToUpper() && x.Enabled && x.ResetCode == Code).FirstOrDefault();
            if (user != null)
            {
                user.ResetCode = Code;
            }
            else
            {
                obUser = dbContext.OBUsers.Where(x => x.UserID.ToUpper() == UserID.ToUpper() && x.ResetCode == Code).FirstOrDefault();
                if (obUser != null)
                {
                    obUser.ResetCode = Code;
                }
            }
            dbContext.SaveChanges();

            return (user != null || obUser != null);
        }

        public bool ClearToken(DnetEntities dbContext)
        {
            OBUser obUser = null;
            User user = dbContext.Users.Where(x => x.UserID.ToUpper() == UserID.ToUpper() && x.Enabled && x.ResetCode == Code).FirstOrDefault();
            if (user != null)
            {
                user.ResetCode = null;
            }
            else
            {
                obUser = dbContext.OBUsers.Where(x => x.UserID.ToUpper() == UserID.ToUpper() && x.ResetCode == Code).FirstOrDefault();
                if (obUser != null)
                {
                    obUser.ResetCode = null;
                }
            }
            dbContext.SaveChanges();

            return (user != null || obUser != null);
        }

        public bool ValidateToken(DnetEntities dbContext)
        {
            bool isValid = false;
            if (ValidUntil >= DateTime.Now)
            {
                User user = dbContext.Users.FirstOrDefault(x => x.UserID.ToUpper() == UserID.ToUpper() && x.Enabled && x.ResetCode == Code);
                var access = dbContext.UserRoleClientEmployeeAssignments.Where(x => x.UserID.ToUpper() == UserID.ToUpper()).ToList();
                if (user != null)
                {
                    isValid = true;
                    if (!string.IsNullOrEmpty(user.DefaultAccess.ToString()))
                    {
                        GlobalVariables.Client = access.FirstOrDefault(c => c.id == user.DefaultAccess).ClientID;
                    }
                    else
                    {
                        GlobalVariables.Client = access.FirstOrDefault().ClientID;
                    }
                }
                else
                {
                    OBUser obUser = dbContext.OBUsers.FirstOrDefault(x => x.UserID.ToUpper() == UserID.ToUpper() && x.ResetCode == Code);
                    if (obUser != null)
                    {
                        isValid = true;
                    }
                }
            }

            return isValid;
        }


        public string UserID { get; set; }
        public string Code { get; set; }
        public string Token { get; set; }
        public DateTime ValidUntil { get; set; }
        public string EncryptedToken { get; set; }

    }
}