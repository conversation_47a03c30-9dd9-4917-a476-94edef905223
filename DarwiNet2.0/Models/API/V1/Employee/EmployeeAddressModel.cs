namespace DarwiNet2._0.Models.API.V1.Employee
{
    public class EmployeeAddressModel
    {
        public static EmployeeAddressModel CopyEmployeeAddress(Data.EmployeeAddress employeeAddress)
        {
            EmployeeAddressModel employeeAddressModel = new EmployeeAddressModel();

            employeeAddressModel.ID = employeeAddress.ID;
            employeeAddressModel.CompanyID = employeeAddress.CompanyID;
            employeeAddressModel.EmployeeID = employeeAddress.EmployeeID;
            employeeAddressModel.AddressCode = employeeAddress.AddressCode;
            employeeAddressModel.Address1 = employeeAddress.Address1;
            employeeAddressModel.Address2 = employeeAddress.Address2;
            employeeAddressModel.Address3 = employeeAddress.Address3;
            employeeAddressModel.City = employeeAddress.City;
            employeeAddressModel.State = employeeAddress.State;
            employeeAddressModel.Zip = employeeAddress.Zip;
            employeeAddressModel.County = employeeAddress.County;
            employeeAddressModel.Country = employeeAddress.Country;
            employeeAddressModel.Phone1 = employeeAddress.Phone1;
            employeeAddressModel.Phone2 = employeeAddress.Phone2;
            employeeAddressModel.Phone3 = employeeAddress.Phone3;
            employeeAddressModel.Fax = employeeAddress.Fax;
            employeeAddressModel.ForeignAddress = employeeAddress.ForeignAddress;
            employeeAddressModel.ForeignStateProvince = employeeAddress.ForeignStateProvince;
            employeeAddressModel.ForeignPostalCode = employeeAddress.ForeignPostalCode;
            employeeAddressModel.CountryCode = employeeAddress.CountryCode;

            return employeeAddressModel;
        }

        public int ID { get; set; }
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string AddressCode { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string Address3 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string County { get; set; }
        public string Country { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Phone3 { get; set; }
        public string Fax { get; set; }
        public byte? ForeignAddress { get; set; }
        public string ForeignStateProvince { get; set; }
        public string ForeignPostalCode { get; set; }
        public string CountryCode { get; set; }
    }
}