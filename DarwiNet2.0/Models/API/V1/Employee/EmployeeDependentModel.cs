using System;

namespace DarwiNet2._0.Models.API.V1.Employee
{
    public class EmployeeDependentModel
    {
        public static EmployeeDependentModel CopyEmployeeDependent(Data.EmployeeDependent employeeDependent)
        {
            EmployeeDependentModel employeeDependentModel = new EmployeeDependentModel();

            employeeDependentModel.ID = employeeDependent.ID;
            employeeDependentModel.CompanyID = employeeDependent.CompanyID;
            employeeDependentModel.EmployeeID = employeeDependent.EmployeeID;
            employeeDependentModel.SSN = employeeDependent.SSN;
            employeeDependentModel.FirstName = employeeDependent.FirstName;
            employeeDependentModel.MiddleName = employeeDependent.MiddleName;
            employeeDependentModel.LastName = employeeDependent.LastName;
            employeeDependentModel.BirthDate = employeeDependent.BirthDate;
            employeeDependentModel.Gender = employeeDependent.Gender;
            employeeDependentModel.EmployeeRelationship = employeeDependent.EmployeeRelationship;
            employeeDependentModel.LivesWithEmployee = employeeDependent.LivesWithEmployee;
            employeeDependentModel.OverageStudent = employeeDependent.OverageStudent;
            employeeDependentModel.Disabled = employeeDependent.Disabled;
            employeeDependentModel.Comments = employeeDependent.Comments;
            employeeDependentModel.Smoker = employeeDependent.Smoker;
            employeeDependentModel.Wellness = employeeDependent.Wellness;
            employeeDependentModel.Other1 = employeeDependent.Other1;
            employeeDependentModel.Other2 = employeeDependent.Other2;
            employeeDependentModel.Other3 = employeeDependent.Other3;
            employeeDependentModel.Other4 = employeeDependent.Other4;
            employeeDependentModel.Address1 = employeeDependent.Address1;
            employeeDependentModel.Address2 = employeeDependent.Address2;
            employeeDependentModel.City = employeeDependent.City;
            employeeDependentModel.State = employeeDependent.State;
            employeeDependentModel.Zip = employeeDependent.Zip;
            employeeDependentModel.Phone1 = employeeDependent.Phone1;
            employeeDependentModel.Phone2 = employeeDependent.Phone2;
            employeeDependentModel.Email = employeeDependent.Email;
            employeeDependentModel.AdditionalComment = employeeDependent.AdditionalComment;

            return employeeDependentModel;
        }

        public int ID { get; set; }
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string SSN { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public DateTime? BirthDate { get; set; }
        public byte? Gender { get; set; }
        public byte? EmployeeRelationship { get; set; }
        public bool LivesWithEmployee { get; set; }
        public bool OverageStudent { get; set; }
        public bool Disabled { get; set; }
        public string Comments { get; set; }
        public bool Smoker { get; set; }
        public bool Wellness { get; set; }
        public bool Other1 { get; set; }
        public bool Other2 { get; set; }
        public bool Other3 { get; set; }
        public bool Other4 { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Email { get; set; }
        public string AdditionalComment { get; set; }
    }
}