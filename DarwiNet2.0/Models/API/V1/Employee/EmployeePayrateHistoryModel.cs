using System;

namespace DarwiNet2._0.Models.API.V1.Employee
{
    public class EmployeePayrateHistoryModel
    {
        public static EmployeePayrateHistoryModel CopyEmployeePayrateHistory(Data.EmployeePayrateHistory employeePayrateHistory)
        {
            EmployeePayrateHistoryModel employeePayrateHistoryModel = new EmployeePayrateHistoryModel();

            employeePayrateHistoryModel.CompanyID = employeePayrateHistory.CompanyID;
            employeePayrateHistoryModel.EmployeeID = employeePayrateHistory.EmployeeID;
            employeePayrateHistoryModel.PayRecord = employeePayrateHistory.PayRecord;
            employeePayrateHistoryModel.EffectiveDate = employeePayrateHistory.EffectiveDate;
            employeePayrateHistoryModel.PayRateAmountOld = employeePayrateHistory.PayRateAmountOld;
            employeePayrateHistoryModel.PayRateChangeType = employeePayrateHistory.PayRateChangeType;
            employeePayrateHistoryModel.PayRateAmount = employeePayrateHistory.PayRateAmount;
            employeePayrateHistoryModel.PayRateChangeRate = employeePayrateHistory.PayRateChangeRate;
            employeePayrateHistoryModel.ChangeReason = employeePayrateHistory.ChangeReason;
            employeePayrateHistoryModel.UserID = employeePayrateHistory.UserID;
            employeePayrateHistoryModel.Date = employeePayrateHistory.Date;

            return employeePayrateHistoryModel;
        }

        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string PayRecord { get; set; }
        public System.DateTime EffectiveDate { get; set; }
        public decimal? PayRateAmountOld { get; set; }
        public byte? PayRateChangeType { get; set; }
        public decimal? PayRateAmount { get; set; }
        public decimal? PayRateChangeRate { get; set; }
        public string ChangeReason { get; set; }
        public string UserID { get; set; }
        public DateTime? Date { get; set; }
    }
}