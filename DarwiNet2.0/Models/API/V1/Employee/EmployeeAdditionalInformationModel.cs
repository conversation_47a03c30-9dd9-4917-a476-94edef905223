using System;

namespace DarwiNet2._0.Models.API.V1.Employee
{
    public class EmployeeAdditionalInformationModel
    {
        public static EmployeeAdditionalInformationModel CopyEmployeeAdditionalInformation(Data.Employee employee)
        {
            EmployeeAdditionalInformationModel employeeAdditionalInformationModel = new EmployeeAdditionalInformationModel();

            employeeAdditionalInformationModel.CompanyID = employee.CompanyID;
            employeeAdditionalInformationModel.EmployeeID = employee.EmployeeID;
            employeeAdditionalInformationModel.MaritalStatus = employee.MaritalStatus;
            employeeAdditionalInformationModel.Spouse = employee.Spouse;
            employeeAdditionalInformationModel.SpouseSSN = employee.SpouseSSN;
            employeeAdditionalInformationModel.NickName = employee.NickName;
            employeeAdditionalInformationModel.AlternateName = employee.AlternateName;
            employeeAdditionalInformationModel.DateOfLastReview = employee.DateOfLastReview;
            employeeAdditionalInformationModel.DateOfNextReview = employee.DateOfNextReview;
            employeeAdditionalInformationModel.Handicapped = employee.Handicapped;
            employeeAdditionalInformationModel.Veteran = employee.Veteran;
            employeeAdditionalInformationModel.VietnamVeteran = employee.VietnamVeteran;
            employeeAdditionalInformationModel.DisabledVeteran = employee.DisabledVeteran;
            employeeAdditionalInformationModel.UnionEmployee = employee.UnionEmployee;
            employeeAdditionalInformationModel.UnionCode = employee.UnionCode;
            employeeAdditionalInformationModel.Smoker = employee.Smoker;
            employeeAdditionalInformationModel.Citizen = employee.Citizen;
            employeeAdditionalInformationModel.Verified = employee.Verified;
            employeeAdditionalInformationModel.I9renew = employee.I9renew;
            employeeAdditionalInformationModel.I9Verified = employee.I9Verified;
            employeeAdditionalInformationModel.I9ReverificationDue = employee.I9ReverificationDue;
            employeeAdditionalInformationModel.VisaType = employee.VisaType;
            employeeAdditionalInformationModel.VisaExpirationDate = employee.VisaExpirationDate;
            employeeAdditionalInformationModel.EmergencyContact = employee.EmergencyContact;
            employeeAdditionalInformationModel.EmergencyPhone = employee.EmergencyPhone;
            employeeAdditionalInformationModel.DriversLicense = employee.DriversLicense;
            employeeAdditionalInformationModel.LicenseState = employee.LicenseState;
            employeeAdditionalInformationModel.C401KHireDate = employee.C401KHireDate;
            employeeAdditionalInformationModel.HighCompensation = employee.HighCompensation;
            employeeAdditionalInformationModel.Citizenship = employee.Citizenship;
            employeeAdditionalInformationModel.JobCategory = employee.JobCategory;
            employeeAdditionalInformationModel.SeniorityDate = employee.SeniorityDate;
            employeeAdditionalInformationModel.IndependentContractor = employee.IndependentContractor;
            employeeAdditionalInformationModel.RehireDate = employee.RehireDate;
            employeeAdditionalInformationModel.DeathDate = employee.DeathDate;
            employeeAdditionalInformationModel.RetirementDate = employee.RetirementDate;
            employeeAdditionalInformationModel.ACAEmployeeStatus = employee.ACAEmployeeStatus;
            employeeAdditionalInformationModel.ACACoverageOffered = employee.ACACoverageOffered;
            employeeAdditionalInformationModel.ACASeasonalEmployee = employee.ACASeasonalEmployee;
            employeeAdditionalInformationModel.ACAVariableEmployee = employee.ACAVariableEmployee;
            employeeAdditionalInformationModel.ACAAdjustmentHours = employee.ACAAdjustmentHours;
            employeeAdditionalInformationModel.ACAUseAdjustmentHoursOnly = employee.ACAUseAdjustmentHoursOnly;
            employeeAdditionalInformationModel.ACAOffsetMethod = employee.ACAOffsetMethod;
            employeeAdditionalInformationModel.TerminationDate = employee.TerminationDate;

            return employeeAdditionalInformationModel;
        }

        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public byte? MaritalStatus { get; set; }
        public string Spouse { get; set; }
        public string SpouseSSN { get; set; }
        public string NickName { get; set; }
        public string AlternateName { get; set; }
        public DateTime? DateOfLastReview { get; set; }
        public DateTime? DateOfNextReview { get; set; }
        public bool Handicapped { get; set; }
        public bool Veteran { get; set; }
        public bool VietnamVeteran { get; set; }
        public bool DisabledVeteran { get; set; }
        public bool UnionEmployee { get; set; }
        public string UnionCode { get; set; }
        public bool Smoker { get; set; }
        public bool Citizen { get; set; }
        public bool Verified { get; set; }
        public DateTime? I9renew { get; set; }
        public bool I9Verified { get; set; }
        public DateTime? I9ReverificationDue { get; set; }
        public string VisaType { get; set; }
        public DateTime? VisaExpirationDate { get; set; }
        public string EmergencyContact { get; set; }
        public string EmergencyPhone { get; set; }
        public string DriversLicense { get; set; }
        public string LicenseState { get; set; }
        public DateTime? C401KHireDate { get; set; }
        public bool HighCompensation { get; set; }
        public string Citizenship { get; set; }
        public byte? JobCategory { get; set; }
        public DateTime? SeniorityDate { get; set; }
        public bool IndependentContractor { get; set; }
        public DateTime? RehireDate { get; set; }
        public DateTime? DeathDate { get; set; }
        public DateTime? RetirementDate { get; set; }
        public byte? ACAEmployeeStatus { get; set; }
        public bool ACACoverageOffered { get; set; }
        public bool ACASeasonalEmployee { get; set; }
        public bool ACAVariableEmployee { get; set; }
        public decimal? ACAAdjustmentHours { get; set; }
        public bool ACAUseAdjustmentHoursOnly { get; set; }
        public byte? ACAOffsetMethod { get; set; }
        public DateTime? TerminationDate { get; set; }
    }
}