namespace DarwiNet2._0.Models.API.V1.Client
{
    public class ClientWorkersCompPlanExperienceModifierModel
    {
        public static ClientWorkersCompPlanExperienceModifierModel CopyClientWorkersCompPlanExperienceModifier(Data.ClientWorkersCompPlanExperienceModifier clientWorkersCompPlanExperienceModifier)
        {
            ClientWorkersCompPlanExperienceModifierModel clientWorkersCompPlanExperienceModifierModel = new ClientWorkersCompPlanExperienceModifierModel();

            clientWorkersCompPlanExperienceModifierModel.CompanyID = clientWorkersCompPlanExperienceModifier.CompanyID;
            clientWorkersCompPlanExperienceModifierModel.WorkersCompPlanID = clientWorkersCompPlanExperienceModifier.WorkersCompPlanID;
            clientWorkersCompPlanExperienceModifierModel.ClientID = clientWorkersCompPlanExperienceModifier.ClientID;
            clientWorkersCompPlanExperienceModifierModel.SequenceNumber = clientWorkersCompPlanExperienceModifier.SequenceNumber;
            clientWorkersCompPlanExperienceModifierModel.ApplyToLiability = clientWorkersCompPlanExperienceModifier.ApplyToLiability;
            clientWorkersCompPlanExperienceModifierModel.ApplyToBilling = clientWorkersCompPlanExperienceModifier.ApplyToBilling;
            clientWorkersCompPlanExperienceModifierModel.ExperienceModifier = clientWorkersCompPlanExperienceModifier.ExperienceModifier;
            clientWorkersCompPlanExperienceModifierModel.Description = clientWorkersCompPlanExperienceModifier.Description;
            clientWorkersCompPlanExperienceModifierModel.Index1 = clientWorkersCompPlanExperienceModifier.Index1;
            clientWorkersCompPlanExperienceModifierModel.State = clientWorkersCompPlanExperienceModifier.State;

            return clientWorkersCompPlanExperienceModifierModel;
        }

        public int CompanyID { get; set; }
        public string WorkersCompPlanID { get; set; }
        public string ClientID { get; set; }
        public int SequenceNumber { get; set; }
        public bool? ApplyToLiability { get; set; }
        public bool? ApplyToBilling { get; set; }
        public int? ExperienceModifier { get; set; }
        public string Description { get; set; }
        public int? Index1 { get; set; }
        public string State { get; set; }
    }
}