namespace DarwiNet2._0.Models.API.V1.OBProcess
{
    public class OBProcessRecordModel
    {
        public static OBProcessRecordModel CopyOBProcessRecord(Data.OBProcessRecord oBProcessRecord)
        {
            OBProcessRecordModel oBProcessRecordModel = new OBProcessRecordModel();

            oBProcessRecordModel.CompanyID = oBProcessRecord.CompanyID;
            oBProcessRecordModel.EmployeeID = oBProcessRecord.EmployeeID;
            oBProcessRecordModel.TaskID = oBProcessRecord.TaskID;
            oBProcessRecordModel.RecordID = oBProcessRecord.RecordID;
            oBProcessRecordModel.ImportStatus = oBProcessRecord.ImportStatus;
            oBProcessRecordModel.EECompleted = oBProcessRecord.EECompleted;
            oBProcessRecordModel.CCCompleted = oBProcessRecord.CCCompleted;

            return oBProcessRecordModel;
        }

        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public int TaskID { get; set; }
        public int RecordID { get; set; }
        public int ImportStatus { get; set; }
        public bool EECompleted { get; set; }
        public bool CCCompleted { get; set; }
    }
}