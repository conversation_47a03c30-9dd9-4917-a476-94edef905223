using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models.API
{
    public static class Constants
    {
        public enum CODETYPE
        {
            PAYCODE = 1,
            DEDUCTION = 2,
            BENEFITS = 3,
            STATETAX = 4,
            LOCALTAX = 5
        }

        public static string ForgetPasswordEmailBody =>
            "<html><body>{0},<br /><br />" +
            "A password change request was recently processed for the account associated with this email address. <br /><br /><br />" +
            "To change your password click the link below: <br /><br />" +
            "{1}Home/ResetPassword/{2} <br /><br /><br />" +
            "If this request was not made by you, please contact your system administrator for assistance.  If this change was made by you, please disregard this message.<br /><br />" +
            "Thank you.<br />" +
            "</body></html>";

        public static string ForgotUserIdBody => 
            "<html><body>{0}" +
            "<br />Your current User ID is '<b>{1}</b>'." +
            "<br /><br />If you did not make this request, please contact your system administrator for assistance.<br /><br />Thanks<br /></body></html>";

    }
}