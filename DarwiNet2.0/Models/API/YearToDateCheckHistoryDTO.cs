using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models.API
{
    public class YearToDateCheckHistoryDTO
    {
        public int Year { get; set; }
        public decimal? GrossPay { get; set; }
        public decimal? NetPay { get; set; }
        public decimal? Taxes { get; set; }
        public decimal? Deductions { get; set; }
        public decimal? Benefits { get; set; }
        public List<CheckDTO> Checks { get; set; }

        public static YearToDateCheckHistoryDTO PopulateYearToDateCheckHistoryDTO(List<CheckDTO> checks, int year, List<YearToDateCheckHistoryTotalsDTO> totals)
        {
            var ytdCheckHistory = new YearToDateCheckHistoryDTO();
            ytdCheckHistory.Year = year;
            ytdCheckHistory.NetPay = CalculateNetPay(checks);
            ytdCheckHistory.Taxes = CalculateTaxes(checks);
            ytdCheckHistory.Checks = checks.OrderByDescending(check => check.Date).ToList();

            foreach (YearToDateCheckHistoryTotalsDTO code in totals)
            {
                switch (code.PayrollRecordType)
                {
                    case 1:
                        ytdCheckHistory.GrossPay = code.Total;
                        break;
                    case 2:
                        ytdCheckHistory.Deductions = code.Total;
                        break;
                    case 3:
                        ytdCheckHistory.Benefits = code.Total;
                        break;
                    default:
                        break;
                }
            }

            return ytdCheckHistory;
        }

        private static decimal? CalculateGrossPay(List<CheckDTO> checks)
        {
            return checks.Sum(check => check.GrossPay);
        }

        private static decimal? CalculateNetPay(List<CheckDTO> checks)
        {
            return checks.Sum(check => check.NetPay);
        }

        private static decimal? CalculateTaxes(List<CheckDTO> checks)
        {
            return checks.Sum(check => check.Taxes);
        }

        private static decimal? CalculateDeductions(List<CheckDTO> checks)
        {
            return checks.Sum(check => check.Deductions);
        }

        private static decimal? CalculateBenefits(List<CheckDTO> checks)
        {
            return checks.Sum(check => check.Benefits);
        }
    }
}