using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models.API
{
    public class TwoFactorAuthenticationDTO
    {
        private string _twoFactorCode;

        public string Username { get; set; }

        public string TwoFactorAuthCode { set { _twoFactorCode = value; } }

        public bool UsePhone { get; set; }
        public bool UseEmail { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string TmpToken { get; set; }
        public bool RememberMe { get; set; }

        [JsonIgnore]
        public string TwoFactorCode => _twoFactorCode;
    }
}