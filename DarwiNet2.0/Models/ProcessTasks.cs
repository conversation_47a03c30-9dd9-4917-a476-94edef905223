using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models
{
    public class OBEEPaycodes
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string Paycode { get; set; }

        public string Description { get; set; }

        public string PayType { get; set; }

        public decimal Payrate { get; set; }

        public string PayUnit { get; set; }

        public short PayPeriod { get; set; }

        public string BasedUpon { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEEBenefits
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string BenefitCode { get; set; }

        public string Description { get; set; }

        public DateTime? StartDate { get; set; }

        public string BenefitMethod { get; set; }

        public decimal Amount { get; set; }

        public double PercentRate { get; set; }

        public double Rate { get; set; }

        public short PayPeriod { get; set; }

        public string PlanName { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEEDeductions
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string DeductionCode { get; set; }

        public string Description { get; set; }

        public DateTime? StartDate { get; set; }

        public string DeductionMethod { get; set; }

        public decimal Amount { get; set; }

        public double PercentRate { get; set; }

        public double Rate { get; set; }

        public short PayPeriod { get; set; }

        public string PlanName { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEEDependents
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string FirstName { get; set; }

        public string MiddleName { get; set; }

        public string LastName { get; set; }

        public string SocialSecNumber { get; set; }

        public DateTime? BirthDate { get; set; }

        public string Gender { get; set; }

        public string Relationship { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEEDeposits
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string BankID { get; set; }

        public string BankName { get; set; }

        public string BankTransit { get; set; }

        public string BankAccount { get; set; }

        public string AccountType { get; set; }

        public string DepositOption { get; set; }

        public decimal Amount { get; set; }

        public double PercentRate { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEELicenses
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string LicenseID { get; set; }


        public string Description { get; set; }

        public string LicenseNumber { get; set; }

        public DateTime? EffectiveDate { get; set; }

        public DateTime? ExpirationDate { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEELocalTax
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }


        public string LocalID { get; set; }

        public bool Automatic { get; set; }

        public bool PrimaryCode { get; set; }

        public bool ReciprocalCode { get; set; }

        public string MaritalStatus { get; set; }

        public int Exemptions { get; set; }

        public bool DefaultLocal { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEEPensionPlans
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string PlanName { get; set; }

        public string DeductionCode { get; set; }

        public string Description { get; set; }

        public string DeductionMethod { get; set; }


        public double Contribution { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEEPTO
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string PTOType { get; set; }

        public string Description { get; set; }

        public bool Completed { get; set; }
    }

    public class OBEEStateTax
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string State { get; set; }


        public string MaritalStatus { get; set; }

        public int Dependents { get; set; }

        public int Status { get; set; }

        public int Additional { get; set; }

        public decimal AdditionalAmount { get; set; }

        public decimal EstimatedAmount { get; set; }

        public bool DefaultState { get; set; }

        public bool Completed { get; set; }

        private string _docId;
        public string DocId { get; set; }
        //TFS - DG - Bug 6801 - 09/04/2020 - START
        public bool CanWeDel {get;set;}
        //TFS -DG- Bug 6801 - 09/04/2020 - END
    }

    public class OBEETrainings
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string Type { get; set; }

        public string Description { get; set; }

        public string Status { get; set; }

        public DateTime? RenewalDate { get; set; }


        public bool Completed { get; set; }
    }

    public class TipImages
    {
        public string ImageName { get; set; }

        public string ImageURL { get; set; }
    }

    public class EEEligiblePlanTypes
    {
        public string PlanType { get; set; }

        public int? Status { get; set; }

        public bool Waived { get; set; }

        public decimal? Cafe { get; set; }

        public int? WaiveReason { get; set; }
    }

    public class EEEligiblePlanCategories
    {
        public string PlanName { get; set; }

        public string CoverageLevel { get; set; }

        public DateTime EffectiveDate { get; set; }

        public decimal YourMonthlyCost { get; set; }

        public decimal CostPerPayPeriod { get; set; }

        public decimal EmployerMonthlyContribution { get; set; }

        public Boolean Selected { get; set; }
    }

    public class EEEligiblePlanDependents
    {
        public string PlanName { get; set; }

        public string DependentName { get; set; }

        public int MemberAge { get; set; }

        public DateTime BirthDate { get; set; }

        public DateTime EffectiveDate { get; set; }

        public decimal YourMonthlyCost { get; set; }

        public decimal CostPerPayPeriod { get; set; }

        public decimal EmployerMonthlyContribution { get; set; }

        public Boolean Covered { get; set; }

        public string SSN { get; set; }

        public string State { get; set; }

        public bool Smoker { get; set; }

        public bool Wellness { get; set; }

        public bool Other1 { get; set; }

        public bool Other2 { get; set; }

        public bool Other3 { get; set; }

        public bool Other4 { get; set; }

        public byte Relationship { get; set; }

        public bool Student { get; set; }

        public bool? IsDeleted { get; set; }
    }

    public class EEEligiblePlans
    {
        public string PlanName { get; set; }

        public string Method { get; set; }

        public string Label1 { get; set; }

        public string Value1 { get; set; }

        public string Label2 { get; set; }

        public string Value2 { get; set; }

        public string Label3 { get; set; }

        public string Value3 { get; set; }

        public string Label4 { get; set; }

        public string Value4 { get; set; }
        public string PlanNameYear { get; set; }
    }

    public class OBEEEligibility
    {
        public int RecordID { get; set; }

        public string EmployeeID { get; set; }

        public string OtherName { get; set; }

        public short ResidentStatus { get; set; }

        public string AlienNbr { get; set; }

        public DateTime? WorkExpDate { get; set; }

        public string I94Nbr { get; set; }

        public string ForeignPassportNbr { get; set; }

        public string PassportCountry { get; set; }

        public bool UsePreparer { get; set; }

        public string PrepLastName { get; set; }

        public string PrepFirstName { get; set; }

        public string PrepMiddle { get; set; }
        public string PrepAddress { get; set; }

        public string PrepCity { get; set; }

        public string PrepState { get; set; }

        public string PrepZIP { get; set; }

        public string PrepLastName2 { get; set; }

        public string PrepFirstName2 { get; set; }

        public string PrepMiddle2 { get; set; }
        public string PrepAddress2 { get; set; }

        public string PrepCity2 { get; set; }

        public string PrepState2 { get; set; }

        public string PrepZIP2 { get; set; }

        public string PrepLastName3 { get; set; }

        public string PrepFirstName3 { get; set; }

        public string PrepMiddle3 { get; set; }
        public string PrepAddress3 { get; set; }

        public string PrepCity3 { get; set; }

        public string PrepState3 { get; set; }

        public string PrepZIP3 { get; set; }
        
        public string PrepLastName4 { get; set; }

        public string PrepFirstName4 { get; set; }

        public string PrepMiddle4 { get; set; }
        public string PrepAddress4 { get; set; }

        public string PrepCity4 { get; set; }

        public string PrepState4 { get; set; }

        public string PrepZIP4 { get; set; }

        public string CCTitle { get; set; }

        public string CCLastName { get; set; }

        public string CCFirstName { get; set; }

        public short DocA1Type { get; set; }

        public string DocA1Title { get; set; }

        public string DocA1Authority { get; set; }

        public string DocA1Number { get; set; }

        public DateTime? DocA1ExpDate { get; set; }

        public short DocA2Type { get; set; }

        public string DocA2Title { get; set; }

        public string DocA2Authority { get; set; }

        public string DocA2Number { get; set; }

        public DateTime? DocA2ExpDate { get; set; }

        public short DocA3Type { get; set; }

        public string DocA3Title { get; set; }

        public string DocA3Authority { get; set; }

        public string DocA3Number { get; set; }

        public DateTime? DocA3ExpDate { get; set; }

        public short DocBType { get; set; }

        public string DocBTitle { get; set; }

        public string DocBAuthority { get; set; }

        public string DocBNumber { get; set; }

        public DateTime? DocBExpDate { get; set; }

        public short DocCType { get; set; }

        public string DocCTitle { get; set; }

        public string DocCAuthority { get; set; }

        public string DocCNumber { get; set; }

        public DateTime? DocCExpDate { get; set; }

        public DateTime? CCCertDate { get; set; }

        public string CCCertIP { get; set; }

        public DateTime? EECompleteDate { get; set; }

        public string Comment { get; set; }
        public bool AltAuthorize { get; set; }
        public bool Completed { get; set; }
    }

    public class OBEEPaycode_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string Paycode { get; set; }
        public string Description { get; set; }
        public string PayType { get; set; }
        public string Payrate { get; set; }
        public string PayUnit { get; set; }
        public string PayUnitPeriod { get; set; }
        public string TipType { get; set; }
        public string PayPeriod { get; set; }
        public string BasedUpon { get; set; }
        public string FlatFedTaxRate { get; set; }
        public string FlatStateTaxRate { get; set; }
        public string MaxPeriod { get; set; }
        public string MaxMonth { get; set; }
        public string MaxYear { get; set; }
        public string MaxLife { get; set; }
    }

    public class OBEEDeduction_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string DeductionCode { get; set; }
        public string Description { get; set; }
        public string StartDate { get; set; }
        public string DeductionMethod { get; set; }
        public string Amount { get; set; }
        public string PercentRate { get; set; }
        public string PayPeriod { get; set; }
    }

    public class OBEEBenefit_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string BenefitCode { get; set; }
        public string Description { get; set; }
        public string StartDate { get; set; }
        public string BenefitMethod { get; set; }
        public string Amount { get; set; }
        public string PercentRate { get; set; }
        public string PayPeriod { get; set; }
        public string PlanName { get; set; }
        public string MaxPeriod { get; set; }
        public string MaxMonth { get; set; }
        public string MaxQtr { get; set; }
        public string MaxYear { get; set; }
        public string MaxLife { get; set; }
    }


    public class OBEEState_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string State { get; set; }
        public string MaritalStatus { get; set; }
        public string Dependents { get; set; }
        public string Additional { get; set; }
        public string AdditionalAmount { get; set; }
        public string EstimatedAmount { get; set; }
        public string DefaultState { get; set; }
        public string TaxExempt { get; set; }
        public string ExemptionForBlind { get; set; }
        public string ExemptionForBlindSpouse { get; set; }
        public string ExemptionForOver65 { get; set; }
        public string ExemptionForSelf { get; set; }
        public string ExemptionForSpecialAllowance { get; set; }
        public string ExemptionForSpouse { get; set; }
        public string ExemptionForSpouseOver65 { get; set; }
        public string PersonalExemptions { get; set; }
        public string EstimatedDeductionAllowances { get; set; }
        public string ExemptionAmount { get; set; }
    }

    public class OBEELocal_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string LocalID { get; set; }
        public string Automatic { get; set; }
        public string PrimaryCode { get; set; }
        public string ReciprocalCode { get; set; }
        public string MaritalStatus { get; set; }
        public string Exemptions { get; set; }
        public string DefaultLocal { get; set; }
    }

    public class OBEEDependent_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public string SocialSecNumber { get; set; }
        public string Birthdate { get; set; }
        public string Gender { get; set; }
        public string Relationship { get; set; }
        public string LivesWithEE { get; set; }
        public string OverageStudent { get; set; }
        public string Disabled { get; set; }
    }

    public class OBEEPension_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string PlanName { get; set; }
        public string DeductionCode { get; set; }
        public string Description { get; set; }
        public string DeductionMethod { get; set; }
        public string Contribution { get; set; }
    }

    public class OBEEDeposit_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string BankID { get; set; }
        public string BankName { get; set; }
        public string BankTransit { get; set; }
        public string BankAccount { get; set; }
        public string AccountType { get; set; }
        public string DepositOption { get; set; }
        public string BasedOn { get; set; }
        public string PayRecord { get; set; }
        public string Amount { get; set; }
        public string PercentRate { get; set; }
    }

    public class OBEEPTOPlan_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string PTOType { get; set; }
        public string Description { get; set; }
    }

    public class OBEELicense_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string LicenseID { get; set; }
        public string LicenseDescription { get; set; }
        public string LicenseNumber { get; set; }
        public string EffectiveDate { get; set; }
        public string ExpirationDate { get; set; }
    }

    public class OBEETraining_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string Type { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string RenewalDate { get; set; }
    }

    public class OBEEEligibility_Read
    {
        public string RecordID { get; set; }
        public string EmployeeID { get; set; }
        public string OtherName { get; set; }
        public string ResidentStatus { get; set; }
        public string AlienNbr { get; set; }
        public string WorkExpDate { get; set; }
        public string I94Nbr { get; set; }
        public string ForeignPassportNbr { get; set; }
        public string PassportCountry { get; set; }
        public string UsePreparer { get; set; }
        public string PrepLastName { get; set; }
        public string PrepFirstName { get; set; }
        public string PrepMiddle { get; set; }
        public string PrepAddress { get; set; }

        public string PrepCity { get; set; }

        public string PrepState { get; set; }

        public string PrepZIP { get; set; }

        public string PrepLastName2 { get; set; }

        public string PrepFirstName2 { get; set; }

        public string PrepMiddle2 { get; set; }
        public string PrepAddress2 { get; set; }

        public string PrepCity2 { get; set; }

        public string PrepState2 { get; set; }

        public string PrepZIP2 { get; set; }

        public string PrepLastName3 { get; set; }

        public string PrepFirstName3 { get; set; }

        public string PrepMiddle3 { get; set; }
        public string PrepAddress3 { get; set; }

        public string PrepCity3 { get; set; }

        public string PrepState3 { get; set; }

        public string PrepZIP3 { get; set; }

        public string PrepLastName4 { get; set; }

        public string PrepFirstName4 { get; set; }

        public string PrepMiddle4 { get; set; }
        public string PrepAddress4 { get; set; }

        public string PrepCity4 { get; set; }

        public string PrepState4 { get; set; }

        public string PrepZIP4 { get; set; }

        public string CCTitle { get; set; }
        public string CCLastName { get; set; }
        public string CCFirstName { get; set; }
        public string DocA1Type { get; set; }
        public string DocA1Title { get; set; }
        public string DocA1Authority { get; set; }
        public string DocA1Number { get; set; }
        public string DocA1ExpDate { get; set; }
        public string DocA2Type { get; set; }
        public string DocA2Title { get; set; }
        public string DocA2Authority { get; set; }
        public string DocA2Number { get; set; }
        public string DocA2ExpDate { get; set; }
        public string DocA3Type { get; set; }
        public string DocA3Title { get; set; }
        public string DocA3Authority { get; set; }
        public string DocA3Number { get; set; }
        public string DocA3ExpDate { get; set; }
        public string DocBType { get; set; }
        public string DocBTitle { get; set; }
        public string DocBAuthority { get; set; }
        public string DocBNumber { get; set; }
        public string DocBExpDate { get; set; }
        public string DocCType { get; set; }
        public string DocCTitle { get; set; }
        public string DocCAuthority { get; set; }
        public string DocCNumber { get; set; }
        public string DocCExpDate { get; set; }
        public string Comment { get; set; }
        public string AltAuthorize { get; set; }
    }

}