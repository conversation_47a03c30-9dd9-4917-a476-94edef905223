using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;

namespace DarwiNet2._0.Models.D2.EditPayrollCalculation
{
    public class PayCode
    {
        private decimal? _rateOrAmount;
        public string Name { get; set; }
        public string CodeDescription { get; set; }
        public decimal? Hours { get; set; }
        public decimal? OriginalHours { get; set; }
        public decimal? RateOrAmount 
        {
            get => Precision == null || _rateOrAmount == null ?
                _rateOrAmount : Precision.Apply((decimal)_rateOrAmount);

            set => _rateOrAmount = value; 
        }
        public decimal? RawRateOrAmount
        {
            get => _rateOrAmount;
        }
        public decimal? OriginalRateOrAmount { get; set; }
        public string Department { get; set; }
        public string OriginalDepartment { get; set; }
        public string Position { get; set; }
        public string OriginalPosition { get; set; }
        public DateTime? TRXBeginningDate { get; set; }
        public DateTime? TRXEndingDate { get; set; }
        public string StateTaxCode { get; set; }
        public string LocalTaxCode { get; set; }
        public string SUTAState { get; set; }
        public string WorkersComp { get; set; }
        public bool IsIncluded { get; set; }
        public int? Scale { get; set; }
        public Precision Precision => new Precision(Scale);
        private decimal? _grossWages;
        public decimal GrossWages
        {
            get 
            {
                //This list needs to be kept in sync with the list of amountPayTypes on /Views/EditPayrollCalculation/Components/pay-codes-list.vue.cshtml
                //                                                                      /Services/D2/EditCalcPendingChangesService.cs
                List<int> amountPayTypes = new List<int> { 4, 5, 11, 12, 13, 14, 15, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36 };

                if (amountPayTypes.Contains(Convert.ToInt32(PayType)))
                {
                    if (Convert.ToInt32(PayType) >= PayTypes.Misc1 && Convert.ToInt32(PayType) <= PayTypes.Misc10)
                    {
                        if (amountPayTypes.Contains(Convert.ToInt32(BasePayType)))
                        {
                            return RawRateOrAmount == null ? 0 : RawRateOrAmount.GetValueOrDefault();
                        }
                        else
                        {
                            return Hours.GetValueOrDefault() * RawRateOrAmount.GetValueOrDefault();
                        }
                    }
                    else if (Convert.ToInt32(PayType) == PayTypes.Other)
                    {
                        return TotalPay ?? 0m;
                    }
                    else
                    {
                        return RawRateOrAmount == null ? 0 : RawRateOrAmount.GetValueOrDefault();
                    }
                }
                else if (Convert.ToInt32(PayType) == PayTypes.Salary || Convert.ToInt32(BasePayType) == PayTypes.Salary)
                {
                    return (decimal)_grossWages;
                }
                else if (Convert.ToInt32(PayType) == PayTypes.MinWageBalance)
                {
                    return (decimal)TotalPay;
                }
                else
                {
                    return Hours.GetValueOrDefault() * RawRateOrAmount.GetValueOrDefault();
                }
            }
            set => _grossWages = value;
            
        }
        public decimal OriginalGrossWages { get; set; }
        public Nullable<byte> PayType { get; set; }
        public Nullable<byte> BasePayType { get; set; }
        public Nullable<decimal> BasedOnRate { get; set; }
        public decimal? TotalPay { get; set; }
        public string LDLabel1 { get; set; }
        public string LDLabel2 { get; set; }
        public string LDLabel3 { get; set; }
        public string LDLabel4 { get; set; }
        public string LDLabel5 { get; set; }
        public string LDLabel6 { get; set; }
        public string LDLabel7 { get; set; }
        public string LDLabel8 { get; set; }
        public string LDValue1 { get; set; }
        public string LDValue2 { get; set; }
        public string LDValue3 { get; set; }
        public string LDValue4 { get; set; }
        public string LDValue5 { get; set; }
        public string LDValue6 { get; set; }
        public string LDValue7 { get; set; }
        public string LDValue8 { get; set; }
        public int IndexLong { get; set; }
        public bool SubjectToStateTax { get; set; }
        public bool SubjectToLocalTax { get; set; }
    }
}