using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models.D2.EditPayrollCalculation
{
    public class PayrollWorkHeaderPTO
    {
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string PayrollNumber { get; set; }
        public string UserID { get; set; }
        public string ProfileID { get; set; }
        public int PTOType { get; set; }
        public string Description { get; set; }
        public decimal? AmountAccrued { get; set; }
        public int? AmountTaken { get; set; }
        public int? AvailableBeforeHours { get; set; }
        public int? AvailableAfterHours { get; set; }
        public int? YTDHoursAccrued { get; set; }
        public int? LastCarryoverAmount { get; set; }
        public DateTime? ProcessDate { get; set; }
        public byte? RecordProcessed { get; set; }
        public int? HoursPerYear { get; set; }
        public int? MaxAccrualYTD { get; set; }
        public int? ForfeitedHours { get; set; }
        public string SnapshotID { get; set; }
        public bool IsLatest { get; set; }
        public DateTime? LastAnniversaryDate { get; set; }
    }
}