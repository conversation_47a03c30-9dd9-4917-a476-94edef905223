using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models.D2.EditPayrollCalculation
{
    public class PTOCode
    {
        public int Name { get; set; }
        public string Description { get; set; }
        public int? Accrued { get; set; }
        public int? Taken { get; set; }
        public int? AvailableBefore { get; set; } // => AvailableAfter.GetValueOrDefault() + Taken.GetValueOrDefault() - Accrued.GetValueOrDefault();
        public int? AvailableAfter { get; set; }
    }
}