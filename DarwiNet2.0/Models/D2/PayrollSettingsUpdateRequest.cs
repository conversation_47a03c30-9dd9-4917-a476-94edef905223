using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models.D2.PayrollSettingsUpdateRequest
{
    public class PayrollSettingsUpdateRequest
    {
        public Dictionary<string, bool> Options { get; set; } = new Dictionary<string, bool>();
        public string Checkbook { get; set; } = string.Empty;
        public string DepositCheckbook { get; set; } = string.Empty;
        public Dictionary<string, bool> Deductions { get; set; } = new Dictionary<string, bool>();
        public Dictionary<string, bool> Benefits { get; set; } = new Dictionary<string, bool>();
        public Dictionary<string, PayrollSettingsUpdateRequestAutoPay> AutoPay { get; set; } = new Dictionary<string, PayrollSettingsUpdateRequestAutoPay>();
    }

    public class PayrollSettingsUpdateRequestAutoPay
    {
        public string Code { get; set; }
        public bool Exclude { get; set; }
    }
   
}