using DarwiNet2._0.Data;
using DarwiNet2._0.Enumerations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.Models.TimeSheetServiceModels
{
    public class TimeSheetColumnModel : ITimeSheetColumn
    {
        public int TimeSheetID { get; set; }
        public int ColID { get; set; }
        public int ColNbr { get; set; }
        public short? ColType { get; set; }
        public short? Type { get; set; }
        public string Name { get; set; }
        public string Label { get; set; }
        public short? Status { get; set; }
        public short? ValidationType { get; set; }
        public string Code { get; set; }
        public short? CodeType { get; set; }
        public string BasedOn { get; set; }
        public string BaseCode { get; set; }
        public short? BaseCodeType { get; set; }
        public double TotalAmount { get; set; }
        public int OriginCodeNbr { get; set; }
        public int SortOrder { get; set; }
        public List<string> ExceptionEEs { get; set; }
    }
}