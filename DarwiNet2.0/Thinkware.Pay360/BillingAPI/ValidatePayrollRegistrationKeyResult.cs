using DarwiNet2._0.Models;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;

namespace Thinkware.Pay360.BillingAPI
{
    public class ValidatePayrollRegistrationKeyResult
    {
        private string _errorMessage;

        //public int NumOfCurrentUsers { get; set; }
        //public int NumOfMaxUsers { get; set; }
        public string ErrorMessage => _errorMessage;
        //public int NumOfAvailableUsers => NumOfMaxUsers - NumOfCurrentUsers;
        public bool IsValid => string.IsNullOrWhiteSpace(_errorMessage); // && NumOfCurrentUsers < NumOfMaxUsers;

        public void ValidateApiResponse(HttpResponseMessage apiResponse)
        {
            if (!apiResponse.IsSuccessStatusCode)
            {
                SetError($"Invalid HTTP status code {apiResponse.StatusCode}");
                return;
            }

            var json = $"[{apiResponse.Content.ReadAsStringAsync().Result}]";
            var dnetRegistrationKey = JsonConvert.DeserializeObject<HashSet<DnetRegistrationKey>>(json).FirstOrDefault();
            if (dnetRegistrationKey == null)
            {
                SetError("Payroll Registration Key not found in Billing API.");
                return;
            }
            if (dnetRegistrationKey.Status != "Success")
            {
                SetError(dnetRegistrationKey.Message);
                return;
            }
            /*if (!int.TryParse(dnetRegistrationKey.Message.Replace(" users.", string.Empty), out int numOfMaxUsers))
            {
                SetError($"Invalid message. {dnetRegistrationKey.Message}");
                return;
            }

            // Parse api response.
            NumOfMaxUsers = numOfMaxUsers;

            if (!IsValid)
            {
                SetError($"Current count of {NumOfCurrentUsers} can not exceed max number of {NumOfMaxUsers} payroll users.");
                return;
            }*/
        }

        public void SetError(string error)
        {
            _errorMessage = error;
        }
    }
}