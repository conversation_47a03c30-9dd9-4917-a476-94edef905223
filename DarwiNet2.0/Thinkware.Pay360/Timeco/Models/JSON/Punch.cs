using DarwiNet2._0.Extensions;
using System;

namespace Thinkware.Cohesion.Timeco
{
    public class Punch
    {
        public Punch()
        {
        }

        public double Amount { get; set; }
        public int Count { get; set; }
        public DepartmentIdentifier Department { get; set; }

        public string Hours { get; set; }
        internal TimeSpan HoursTimeSpan
        {
            get { return Hours.GetTimeSpanFromString(); }
        }

        public bool IsTransaction { get; set; }
        public string PayrollTransferCode { get; set; }

        public string PunchIn { get; set; }
        internal DateTime? PunchInDateTime
        {
            get
            {
                if (PunchIn == null)
                {
                    return null;
                }
                return PunchIn.GetDateTimeFromJSONDate();
            }
            set
            {
                if (value == null)
                {
                    PunchIn = null;
                    return;
                }
                PunchIn = value.GetValueOrDefault().GetJSONDateFromDateTime();
            }
        }

        public string PunchOut { get; set; }
        internal DateTime? PunchOutDateTime
        {
            get
            {
                if (PunchOut == null)
                {
                    return null;
                }
                return PunchOut.GetDateTimeFromJSONDate();
            }
            set
            {
                if (value == null)
                {
                    PunchOut = null;
                    return;
                }
                PunchOut = value.GetValueOrDefault().GetJSONDateFromDateTime();
            }
        }
    }
}