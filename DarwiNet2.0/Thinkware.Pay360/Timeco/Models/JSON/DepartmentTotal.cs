using DarwiNet2._0.Extensions;
using System;
using System.Collections.Generic;

namespace Thinkware.Cohesion.Timeco
{
    public class DepartmentTotal
    {
        public DepartmentTotal()
        {
        }

        public double Amount { get; set; }
        public double? BaseWageRate { get; set; }
        public List<DepartmentIdentifier> Department { get; set; }

        public string Hours { get; set; }
        internal TimeSpan HoursTimeSpan
        {
            get { return Hours.GetTimeSpanFromString(); }
        }

        public int Count { get; set; }
        public double WageRate { get; set; }
    }
}