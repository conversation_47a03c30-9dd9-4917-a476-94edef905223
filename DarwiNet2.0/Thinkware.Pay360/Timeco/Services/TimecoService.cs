using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Thinkware.Cohesion.Timeco
{
    public class TimecoService
    {
        private readonly TimecoRestClient _client;
        private readonly DnetEntities _dbContext;

        public TimecoService(
            TimecoRestClient client,
            DnetEntities dbContext)
        {
            _client = client;
            _dbContext = dbContext;
        }

        public Result<string> GetSSOUrl(string employeeId)
        {
            return _client.GetSSOUrl(employeeId);
        }

        public Result<TimecoData> FetchData(int companyId, string clientId, string employeeId, string userId)
        {
            Result<TimecoData> Failure(string error) => Result.Failure<TimecoData>(error);

            var employeeResult = GetEmployee(companyId, clientId, employeeId, userId);
            if (employeeResult.IsFailure)
                return Failure(employeeResult.ErrorMessage);

            var departmentsResult = GetDepartments(companyId, clientId);
            if (departmentsResult.IsFailure)
                return Failure(departmentsResult.ErrorMessage);

            var payRulesResult = GetPayRules();
            if (payRulesResult.IsFailure)
                return Failure(payRulesResult.ErrorMessage);

            var timeZonesRules = GetTimeZones();
            if (timeZonesRules.IsFailure)
                return Failure(timeZonesRules.ErrorMessage);

            var statusesResult = GetStatuses();
            if (statusesResult.IsFailure)
                return Failure(statusesResult.ErrorMessage);

            var rolesResult = GetRoles();
            if (rolesResult.IsFailure)
                return Failure(rolesResult.ErrorMessage);

            return Result.Success(new TimecoData
            {
                Employee = employeeResult.Value,
                Departments = departmentsResult.Value,
                PayRules = payRulesResult.Value,
                TimeZones = timeZonesRules.Value,
                Statuses = statusesResult.Value,
                Roles = rolesResult.Value
            });
        }

        public Result SyncPTO(int companyID, List<string> clientIDs, out string errorMessage)
        {
            errorMessage = string.Empty;
            foreach (string clientID in clientIDs)
            {
                Result result = SyncPTO(companyID, clientID, out string innerError);
                if (result.IsFailure)
                {
                    errorMessage += innerError + "\n";
                }
            }

            return Result.Success();
        }

        public Result SyncPTO(int companyID, string clientID, out string errorMessage)
        {
            List<Employee> employees = _client.GetEmployees(out errorMessage);

            if (employees.Count == 0)
            {
                return Result.Failure(errorMessage);
            }

            List<DarwiNet2._0.Data.Employee> dNetEmployees = new List<DarwiNet2._0.Data.Employee>();

            foreach (Employee employee in employees)
            {
                EmployeeTimePunchesSetup employeeTimePunchesSetup = _dbContext.EmployeeTimePunchesSetups.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID && x.TimecoEECode == employee.EmployeeNumber);
                if (employeeTimePunchesSetup == null)
                {
                    continue;
                }

                DarwiNet2._0.Data.Employee dNetEmployee = _dbContext.Employees.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID && x.EmployeeID == employeeTimePunchesSetup.EmployeeID);
                if (dNetEmployee == null)
                {
                    continue;
                }

                dNetEmployees.Add(dNetEmployee);
            }

            if (dNetEmployees.Count == 0)
            {
                errorMessage = "No employees with Employee Timepunch Setup and matching Timeco Employee ID.";
                return Result.Failure(errorMessage);
            }

            foreach (DarwiNet2._0.Data.Employee dNetEmployee in dNetEmployees)
            {
                List<EmployeePTOType> employeePTOTypes = _dbContext.EmployeePTOTypes.Where(x => x.CompanyID == companyID && x.EmployeeID == dNetEmployee.EmployeeID).ToList();

                if (employeePTOTypes.Count == 0)
                {
                    continue;
                }

                EmployeeTimePunchesSetup employeeTimePunchesSetup = _dbContext.EmployeeTimePunchesSetups.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID && x.EmployeeID == dNetEmployee.EmployeeID);
                if (employeeTimePunchesSetup == null)
                {
                    continue;
                }

                foreach (EmployeePTOType employeePTOType in employeePTOTypes)
                {
                    if (!_client.UpdateBenefitBalance(employeeTimePunchesSetup.TimecoEECode, employeePTOType.AvailableHours.GetValueOrDefault(), employeePTOType.Description, out string error))
                    {
                        errorMessage += error + "\n";
                    }
                }
            }

            return Result.Success();
        }

        public Result CreateAndSaveEmployeeTimePunches(int companyID, string clientID, DateTime startDate, DateTime endDate)
        {
            int timeEntryID = 0;
            int timeSheetID = 0;
            HashSet<string> validatedDepartments = new HashSet<string>();

            try
            {
                RemoveExistingTimecoPunches(companyID, clientID, startDate, endDate);
            }
            catch (Exception ex)
            {
                return Result.Failure(ex.Message);
            }

            List<DailyHours> dailyHours = _client.GetCompanyHours(startDate, endDate, out string errorMessage);
            if (dailyHours == null)
            {
                return Result.Failure(errorMessage);
            }

            ClientWorkSchedule clientWorkSchedule = _dbContext.ClientWorkSchedules.FirstOrDefault(w => w.CompanyID == companyID && w.ClientID == clientID);
            ClientTimePunchesSetup clientTimePunchesSetup = TimePunchesController.ClientTPSetup(_dbContext, companyID, clientID, string.Empty, out _);
            foreach (DailyHours dailyHour in dailyHours)
            {
                EmployeeTimePunchesSetup employeeTimePunchesSetup = _dbContext.EmployeeTimePunchesSetups.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID && x.TimecoEECode == dailyHour.EmployeeNumber);
                if (employeeTimePunchesSetup == null)
                {
                    continue;
                }
                DarwiNet2._0.Data.Employee employee = _dbContext.Employees.FirstOrDefault(x => x.CompanyID == companyID && x.ClientID == clientID && x.EmployeeID == employeeTimePunchesSetup.EmployeeID);
                if (employee == null)
                {
                    continue;
                }
                string employeeID = employee.EmployeeID;
                string employeeName = employee.LastName + ", " + employee.FirstName;
                string position = employee.Position;

                foreach (DailyTotal dailyTotal in dailyHour.DailyTotals)
                {
                    DateTime punchDay = dailyTotal.WorkDay.GetDateTimeFromJSONDate();

                    bool isWorkDay = getDayDefaults(punchDay, clientWorkSchedule, clientTimePunchesSetup, employeeTimePunchesSetup, employeeID, out byte dayType, out byte shift);

                    foreach (Punch punch in dailyTotal.Punches)
                    {
                        string departmentName = punch.Department.Name;
                        if (!validatedDepartments.Contains(departmentName))
                        {
                            Department department = _dbContext.Departments.FirstOrDefault(x => x.Department1 == departmentName && x.CompanyID == companyID);
                            if (department == null)
                            {
                                continue;
                            }
                            validatedDepartments.Add(departmentName);
                        }

                        DateTime? punchIn = punch.PunchInDateTime;
                        DateTime? punchOut = punch.PunchOutDateTime;
                        float workHours = (float)punch.Hours.GetTimeSpanFromString().TotalHours;
                        decimal holidayHours = 0;
                        decimal pTOHours = 0;
                        short punchType = ConsolidateStatus.TimePunch;
                        string pTOCode = string.Empty;

                        if (punchIn == null || punchOut == null)
                        {
                            if (dayType == 2) // holiday
                            {
                                holidayHours = Convert.ToDecimal(workHours);
                            }
                            else // PTO
                            {
                                EmployeePaycode employeePaycode = _dbContext.EmployeePaycodes.Where(x => x.PayRecord == punch.PayrollTransferCode && x.EmployeeID == employee.EmployeeID && x.Inactive == false).FirstOrDefault();
                                if (employeePaycode == null)
                                {
                                    continue;
                                }
                                pTOHours = Convert.ToDecimal(workHours);
                                punchType = ConsolidateStatus.PTO;
                                dayType = 4;
                                pTOCode = punch.PayrollTransferCode;
                            }
                        }

                        _dbContext.EmployeeTimePunches.Add(new EmployeeTimePunch
                        {
                            Source = (short)EmployeeTimePunchSource.Timeco,
                            CompanyID = companyID,
                            ClientID = clientID,
                            EmployeeID = employee.EmployeeID,
                            EmployeeName = employeeName,
                            PunchDay = punchDay,
                            PunchType = punchType,
                            DayType = dayType,
                            WorkDay = isWorkDay,
                            PunchIn = punchIn,
                            PunchOut = punchOut,
                            WorkHours = workHours,
                            Inactive = false,
                            Shift = shift,
                            ShiftCode = string.Empty,
                            Department = departmentName,
                            Position = position,
                            TimeEntryID = timeEntryID,
                            TimeSheetID = timeSheetID,
                            RegHours = 0,
                            OTHours = 0,
                            DblHours = 0,
                            HolHours = holidayHours,
                            PTOHours = pTOHours,
                            RegCode = string.Empty,
                            OTCode = string.Empty,
                            DblCode = string.Empty,
                            HolCode = string.Empty,
                            PTOCode = pTOCode,
                            RegRate = 0,
                            OTRate = 0,
                            DblRate = 0,
                            HolRate = 0,
                            PTORate = 0,
                            Exeption = false,
                            ExDescription = string.Empty,
                            PunchInFromIP = string.Empty,
                            PunchOutFromIP = string.Empty,
                            // Created? Datetime Could be tagged at time of import
                            Validated = false,
                            AdjustTime = 0,
                            AdjustDescr = string.Empty,
                            ImportFile = string.Empty,
                            PartOfTimeEntry = false,
                            OfflinePunchIn = false,
                            OfflinePunchOut = false
                        });

                        try
                        {
                            _dbContext.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            return Result.Failure(ex.Message);
                        }
                    }
                }
            }

            return Result.Success();
        }

        private bool getDayDefaults(DateTime punchDay, ClientWorkSchedule clientWorkSchedule, ClientTimePunchesSetup clientTimePunchesSetup, EmployeeTimePunchesSetup employeeTimePunchesSetup, string employeeID, out byte dayType, out byte shift)
        {
            bool isWorkDay = true;
            dayType = CalendarDayType.WorkDay;
            shift = (byte)(employeeTimePunchesSetup.Shift ?? 1);
            if (TimePunchesController.IsHoliday(_dbContext, punchDay, clientWorkSchedule) && TimePunchesController.IsHolidayEmployee(_dbContext, clientTimePunchesSetup.CompanyID, GlobalVariables.Client, employeeID))
            {
                if (clientWorkSchedule != null)
                {
                    shift = 1;
                }
                dayType = CalendarDayType.Holiday;
                isWorkDay = false;
            }
            else
            {
                if (clientWorkSchedule != null)
                {
                    isWorkDay = TimePunchesController.IsWorkDay(punchDay, clientWorkSchedule, out dayType);
                }
                else
                {
                    shift = 1;
                }
            }

            return isWorkDay;
        }

        private void RemoveExistingTimecoPunches(int companyID, string clientID, DateTime startDate, DateTime endDate)
        {
            List<EmployeeTimePunch> employeeTimePunches = _dbContext.EmployeeTimePunches.Where(x => x.Source == (short)EmployeeTimePunchSource.Timeco && x.CompanyID == companyID && x.ClientID == clientID && x.PunchDay >= startDate && x.PunchDay <= endDate).ToList();
            foreach (EmployeeTimePunch employeeTimePunch in employeeTimePunches)
            {
                _dbContext.EmployeeTimePunches.Remove(employeeTimePunch);
            }

            _dbContext.SaveChanges();
        }

        public Result<List<EmployeeExistsIdentifier>> DoesEmployeesExist(List<string> employeeIDs)
        {
            return _client.DoEmployeesExist(employeeIDs);
        }

        public Result<bool> DoesEmployeeExist(string employeeID)
        {
            return _client.DoesEmployeeExist(employeeID);
        }

        public Result CreateEmployee(string userID, string password, string employeeID, string firstName, string lastName, string department, DateTime hireDate, DateTime eligibilityDate, string payRule, string timeZone, string status, string role, out string errorMessage)
        {
            return _client.CreateEmployee(userID, password, employeeID, firstName, lastName, department, hireDate, eligibilityDate, payRule, timeZone, status, role, out errorMessage);
        }

        // Use DNet departments until we know if we can map DNet departments and Timeco departments.
        public Result<List<DepartmentIdentifier>> GetDepartments(int companyId, string clientId)
        {
            var departments = _dbContext.ClientDivisionDetails
                .Join(_dbContext.Departments,
                    cdd => new { cdd.CompanyID, Department1 = cdd.Department },
                    d => new { d.CompanyID, d.Department1 },
                    (cdd, d) => new { cdd, d })
                .Where(x => x.cdd.CompanyID == companyId && x.cdd.ClientID == clientId)
                .OrderBy(x => x.cdd.Department)
                .Select(x => new DepartmentIdentifier
                {
                    Identifier = x.cdd.Department,
                    Name = x.d.Description
                })
                .ToList();
            return Result.Success(departments);
        }

        public Result<List<DepartmentIdentifier>> GetDepartments(out string errorMessage)
        {
            return _client.GetDepartments(out errorMessage);
        }

        public bool IsEnabled(int companyId, string clientId, out string errorMessage)
        {
            errorMessage = null;

            var useTimeco = _dbContext.ClientTimePunchesSetups
                .Where(x => x.CompanyID == companyId && x.ClientID == clientId)
                .FirstOrDefault()
                ?.UseTimeco ?? false;
            if (!useTimeco)
            {
                errorMessage = "Timeco is not enabled for the client time punch setup.";
                return false;
            }

            return true;
        }

        public Result<List<string>> GetPayRules()
        {
            return _client.GetPayRules();
        }

        public Result<List<BenefitBalance>> GetBenefitBalances(string employeeID)
        {
            return _client.GetBenefitBalance(employeeID);
        }

        public Result<List<string>> GetTimeZones()
        {
            return _client.GetTimeZones();
        }

        public Result<List<string>> GetStatuses()
        {
            var statuses = Enum.GetNames(typeof(TimecoStatus)).ToList();
            return Result.Success(statuses);
        }

        public Result<List<string>> GetRoles()
        {
            return _client.GetRoles();
        }

        public Result<Employee> GetEmployee(int companyId, string clientId, string employeeId, string userId)
        {
            var dnetEmployee = _dbContext.Employees
                .Where(x => x.CompanyID == companyId && x.ClientID == clientId && x.EmployeeID == employeeId)
                .Select(x => new
                {
                    x.FirstName,
                    x.LastName,
                    x.Department,
                    x.OriginalHireDate,
                    x.PartTime
                })
                .FirstOrDefault();

            if (dnetEmployee is null)
                return Result.Failure<Employee>("Failed to find employee.");

            // TODO: TW-MF: Populate any remaining fields.
            return Result.Success(new Employee(
                userID: userId,
                password: "",
                employeeID: employeeId,
                firstName: dnetEmployee.FirstName,
                lastName: dnetEmployee.LastName,
                department: dnetEmployee.Department,
                hireDate: dnetEmployee.OriginalHireDate.GetValueOrDefault(),
                eligibilityDate: dnetEmployee.OriginalHireDate.GetValueOrDefault(),
                payRule: "",
                timeZone: "",
                status: dnetEmployee.PartTime?.ToString() ?? string.Empty,
                role: ""));
        }
    }

    public enum TimecoStatus
    {
        Hourly,
        Salary,
        Historical
    }
}