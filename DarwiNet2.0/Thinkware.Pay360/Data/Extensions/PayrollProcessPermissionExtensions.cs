using DarwiNet2._0.Data;
using System.Collections.Generic;

namespace Thinkware.Pay360.Data
{
    public static class PayrollProcessPermissionExtensions
    {
        public static Dictionary<string, int> GetPermissions(this PayrollProcessPermission @this) =>
            new Dictionary<string, int>()
            {
                { PayrollProcessPermission.PayrollProcessKey, @this.Payroll },
                { PayrollProcessPermission.InvoiceProcessKey, @this.Invoice },
                { PayrollProcessPermission.FinalizeProcessKey, @this.Finalize }
            };

        public static PayrollProcessPermission SetPermission(this PayrollProcessPermission @this, string name, int value)
        {
            switch (name)
            {
                case PayrollProcessPermission.PayrollProcessKey:
                    @this.Payroll = value;
                    break;

                case PayrollProcessPermission.InvoiceProcessKey:
                    @this.Invoice = value;
                    break;

                case PayrollProcessPermission.FinalizeProcessKey:
                    @this.Finalize = value;
                    break;

                default:
                    break;
            }
            return @this;
        }
    }
}