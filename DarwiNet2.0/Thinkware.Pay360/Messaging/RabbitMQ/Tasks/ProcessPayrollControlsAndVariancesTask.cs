namespace Thinkware.Pay360.Messaging
{
    public class ProcessPayrollControlsAndVariancesTask : CohesionTask
    {
        public override string TaskDestination { get; } = TaskDestinations.DataService;
        public override string TaskType { get; } = TaskTypes.ProcessPayrollControlsAndVariances;

        public ProcessPayrollControlsAndVariancesTask(string payrollNumber, string snapshotId, string customerId, string userId, CohesionProcessOptions cohesionProcessOptions = null)
            : base(customerId, payrollNumber, snapshotId, userId, cohesionProcessOptions)
        {
        }
    }
}
