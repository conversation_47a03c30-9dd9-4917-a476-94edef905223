namespace Thinkware.Pay360.Messaging
{
    /// <summary>
    /// Routing Key => <see cref="TaskDestinations.ControlsAndVariancesService"/>.<see cref="TaskTypes.VariancesPerPayroll"/>
    /// </summary>
    public class VariancesPerPayrollTask : CohesionTask
    {
        public override string TaskDestination { get; } = TaskDestinations.ControlsAndVariancesService;
        public override string TaskType { get; } = TaskTypes.VariancesPerPayroll;

        public VariancesPerPayrollTask(string payrollNumber, string snapshotId, string customerId, string userId, CohesionProcessOptions cohesionProcessOptions = null)
            : base(customerId, payrollNumber, snapshotId, userId, cohesionProcessOptions)
        {
        }
    }
}
