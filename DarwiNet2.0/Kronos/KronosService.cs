using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using ComponentSpace.SAML2;
using DarwiNet2._0.Core;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Logging;
using Thinkware;

namespace DarwiNet2._0.Kronos
{
    public sealed class KronosService
    {
        private readonly KronosHttpClient _kronosHttpClient;
        private readonly KronosOptions _kronosOptions;
        private readonly ILogger _logger;
        private KronosService(KronosHttpClient kronosHttpClient, KronosOptions kronosOptions, ILogger logger)
        {
            _kronosOptions = kronosOptions;
            _kronosHttpClient = kronosHttpClient;
            _logger = logger;
        }

        /// <summary>
        /// Safely construct instance of KronosService only if the service is being setup to use for the company
        /// </summary>
        /// <returns>Return either the KronosService instance or the error indicating Kronos is not setup</returns>
        public static Result<KronosService> Create(KronosOptions kronosOptions, ILogger logger)
        {
            // Safely construct Kronos http client and service only when Kronos is correctly configured
            Result<KronosHttpClient> kronosHttpClient = KronosHttpClient.Create(kronosOptions, logger);

            // Kronos configuration not setup
            if (kronosHttpClient.IsFailure)
            {
                return Result.Failure<KronosService>(kronosHttpClient.ErrorMessage);
            }

            return Result.Success(new KronosService(kronosHttpClient.Value, kronosOptions, logger));
        }

        /// <summary>
        /// Initiate SSO to route user to Kronos website and login
        /// </summary>
        /// <param name="httpResponse">The http response</param>
        /// <param name="clientShortNameInKronos">The client short name being used in Kronos</param>
        /// <param name="employeeId">The id of the employee</param>
        /// <param name="relayState"></param>
        public async Task<Result> InitiateSSO(HttpResponseBase httpResponse, string clientShortNameInKronos, string employeeId, string relayState)
        {
            var response = await _kronosHttpClient.GetEmployeeInfosAsync(clientShortNameInKronos);
            if (response.IsFailure)
            {
                return Result.Failure(response.ErrorMessage);
            }

            // Current employee record isn't found in Kronos, failure
            KronosEmployeeResponse currentEmployee = response.Value
                .FirstOrDefault(e => e.EmployeeId == employeeId);
            if (currentEmployee == null)
            {
                _logger.LogError(
                    new ObjectNotFoundException(string.Format("Employee id {0} is missing for client {1} in Kronos", employeeId, clientShortNameInKronos)));
                return Result.Failure("You are currently not yet setup in Kronos. Please contact admin for support");
            }

            // Once found, use the employee's Kronos username to SSO
            string assertionConsumerServiceUrl = string.Format("{0}{1}.login-saml", _kronosOptions.SingleSignOnUri, clientShortNameInKronos);
            string partnerSP = string.Format("{0}{1}", _kronosOptions.SingleSignOnUri, clientShortNameInKronos);

            // Resolve configuration for Kronos (only applicable if UseSAMLConfigurationResolver is true; otherwise, read config from saml.config)
            GlobalVariables.SamlNameConfiguration = Constants.SamlConfigurationResolverName.Kronos;
            SAMLIdentityProvider.InitiateSSO(
                httpResponse: httpResponse,
                userName: currentEmployee.Username,
                attributes: new Dictionary<string, string>(),
                authnContext: null,
                relayState: relayState,
                partnerSP: partnerSP,
                assertionConsumerServiceUrl: assertionConsumerServiceUrl
            );

            return Result.Success();
        }
    }
}
