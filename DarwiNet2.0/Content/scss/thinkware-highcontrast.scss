@import "_variables.scss";

//Body
html {
    position: relative;
    min-height: 100%;
}


.body-content {
    padding-top: 25px;
}

.theme-background{
    background: $TW-Theme-HighContrast;
}

.theme-color{
    color: $TW-Theme-HighContrast;
}

//Wrapper
#wrapper {
    padding-left: 0; 
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}
 
#wrapper.toggled {
    padding-left: 220px;
}
 
#sidebar-wrapper {
    z-index: 1000;
    left: 220px;
    width: 0;
    height: 100%;
    margin-left: -220px;
    overflow-y: auto;
    overflow-x: hidden;
    background: #1a1a1a;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

#sidebar-wrapper::-webkit-scrollbar {
    display: none;
}

#wrapper.toggled #sidebar-wrapper {
    width: 220px;
}

#page-content-wrapper {
    width: 100%;
}

#wrapper.toggled #page-content-wrapper {
    position: absolute;
    margin-right: -220px;
}

//Top Navigation
.top-navigation {
    height: 100px;
    border-bottom: solid;
    border-bottom-width: 1px;
    border-bottom-color: $TW-Light-Gray;
}

.top-nav-padding {
    padding: 20px 10px 10px 20px;
}

//Sidebar Navigation
.nav-side-menu {
    overflow: auto;
    font-family: verdana;
    font-size: 12px; 
    font-weight: 200;
    background-color: $TW-Theme-HighContrast;
    position: fixed;
    top: auto;
    width: 300px;
    height: 100%;
    color: #e1ffff;
}

.nav-side-menu .brand {
    background-color: $TW-White;
    line-height: 50px;
    display: block;
}

.nav-side-menu .toggle-btn {
    display: none;
}

.nav-side-menu ul,
.nav-side-menu li {
    list-style: none;
    padding: 0px;
    margin: 0px;
    line-height: 35px;
    cursor: pointer;
    /*    
    .collapsed{
       .arrow:before{
                 font-family: FontAwesome;
                 content: "\f053";
                 display: inline-block;
                 padding-left:10px;
                 padding-right: 10px;
                 vertical-align: middle;
                 float:right;
            }
     }
*/
}

.nav-side-menu ul :not(collapsed) .arrow:before,
.nav-side-menu li :not(collapsed) .arrow:before {
    font-family: FontAwesome;
    content: "\f078";
    display: inline-block;
    padding-left: 10px;
    padding-right: 10px;
    vertical-align: middle;
    float: right;
}

.nav-side-menu ul .active,
.nav-side-menu li .active {
    border-left: 3px solid $TW-Red;
    background-color: #4f5b69;
}

.nav-side-menu ul .sub-menu li.active,
.nav-side-menu li .sub-menu li.active {
    color: $TW-Red;
}

.nav-side-menu ul .sub-menu li.active a,
.nav-side-menu li .sub-menu li.active a {
    color: $TW-White;
}

.nav-side-menu ul .sub-menu li,
.nav-side-menu li .sub-menu li {
    background-color: #181c20;
    border: none;
    line-height: 28px;
    border-bottom: 1px solid #23282e;
    margin-left: 0px;
}

.nav-side-menu ul .sub-menu li:hover,
.nav-side-menu li .sub-menu li:hover {
    background-color: #020203;
}

.nav-side-menu ul .sub-menu li:before,
.nav-side-menu li .sub-menu li:before {
    font-family: FontAwesome;
    content: "\f105";
    display: inline-block;
    padding-left: 10px;
    padding-right: 10px;
    vertical-align: middle;
}

.nav-side-menu li {
    padding-left: 0;
    border-left: 3px solid #2e353d;
    border-bottom: 1px solid $TW-White;
}

.nav-side-menu li a {
    text-decoration: none;
    color: #e1ffff;
}

.nav-side-menu li a i {
    padding-left: 10px;
    width: 20px;
    padding-right: 20px;
}

.nav-side-menu li:hover {
    border-left: 3px solid $TW-Red;
    background-color: #4f5b69;
    -webkit-transition: all 1s ease;
    -moz-transition: all 1s ease;
    -o-transition: all 1s ease;
    -ms-transition: all 1s ease;
    transition: all 1s ease;
}
//Sidebar Navigation Responsive
@media (max-width: 767px) {
    .nav-side-menu {
        position: relative;
        width: 100%;
        margin-bottom: 10px;
    }

    .nav-side-menu .toggle-btn {
        display: block;
        cursor: pointer;
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 10 !important;
        padding: 3px;
        background-color: #ffffff;
        color: #000;
        width: 40px;
        text-align: center;
    }

    .brand {
        text-align: left !important;
        font-size: 22px;
        padding-left: 20px;
        line-height: 50px !important;
        height: 50px;
    }

    .mobile-nav {
        text-align: left !important;
        font-size: 22px;        
        line-height: 50px !important;
        height: 50px;
        border-bottom: solid 1px #23282e;
    }
}

@media (min-width: 767px) {
    .nav-side-menu .menu-list .menu-content {
        display: block;
    }
}
.navbar-default {
  background-color: $TW-Theme-HighContrast;
  border-color: $TW-Theme-HighContrast;
  
}
.navbar-default .navbar-nav>li>a {
  color: white;
}
.navbar-default .navbar-nav>li>a:hover {
  color: white;
}
.navbar-default .navbar-nav>li>a:active {
  color: white;
}
.navbar-default .navbar-nav>li>a:focus {
  color: white;
}
/*Panels*/
.panel-thinkware>.panel-heading {
  color: #fff;
  background-color: $TW-Theme-HighContrast;
  border-color: #ddd;
}
.panel-thinkware {
  border-color: #ddd;
}
/*Buttons*/
.btn-thinkware {
  color: #fff;
  background-color: $TW-Theme-HighContrast;
  border-color: $TW-Theme-HighContrast; }

.btn-thinkware:hover { 
  background-color: $TW-Hover-HighContrast; }

.btn-thinkware:active { 
  background-color: $TW-Hover-HighContrast; }

.btn-thinkware:focus { 
  background-color: $TW-Hover-HighContrast; }

.btn-thinkware-close {
  color: #fff;
  background-color: #333;
  border-color: #333; }

.btn-thinkware-close:hover {
  background-color: #333; }

.btn-t {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px; }

//Table Overrides
.dynamic-process .k-button.k-button-icontext.k-grid-save-changes {
    color: #fff;
    border-color: #4cae4c;
    background-color: #5cb85c
}

.dynamic-process .k-button.k-button-icontext.k-grid-save-changes:hover{
    color: #ffffff;
  background-color: #47a447;
  border-color: #398439;
}
.dynamic-process .k-button.k-button-icontext.k-grid-cancel-changes {
    color: #fff;
    border-color: #ac2925;
    background-color: #d2322d
}

.dynamic-process .k-button.k-button-icontext.k-grid-cancel-changes:hover{
    color: #ffffff;
  background-color: #d9534f;
  border-color: #d43f3a;
}
/*Buttons*/
.btn-outlined {
    border-radius: 4px;
    -webkit-transition: all 0.3s;
       -moz-transition: all 0.3s;
            transition: all 0.3s;
}

.btn-outlined.btn-theme {
    background: none;
    color: #6f5499;
	border-color: #6f5499;
}

.btn-outlined.btn-theme:hover,
.btn-outlined.btn-theme:active {
    color: #FFF;
    background: #6f5499;
    border-color: #6f5499;
}

.btn-outlined.btn-gray {
    background: #eee;
    color: #000000;
	border-color: #ccc;
}

.btn-outlined.btn-gray:hover,
.btn-outlined.btn-gray:active {
    color: #FFF;
    background: $TW-Theme-HighContrast;
    border-color: #ccc;
}

.btn-outlined.btn-thinkware:hover,
.btn-outlined.btn-thinkware:active {
    color: #FFF;
    background: $TW-Theme-HighContrast;
    border-color: #ccc;
}


.btn-outlined.btn-white {
    background: none;
    color: #FFFFFF;
	border-color: #FFFFFF;
}

.btn-outlined.btn-white:hover,
.btn-outlined.btn-white:active {
    color: #6f5499;
    background: #FFFFFF;
    border-color: #FFFFFF;
}
.plain-editor .k-editor-toolbar .k-autocomplete.k-state-focused,
.plain-editor .k-editor-toolbar .k-picker-wrap.k-state-focused,
.plain-editor .k-editor-toolbar .k-numeric-wrap.k-state-focused,
.plain-editor .k-editor-toolbar .k-dropdown-wrap.k-state-focused {
    background-color: #fff;
    background-image: none;
    background-position: 50% 50%;
    border-color: #ccc
}

.add-codes-arrow{
    display:inline-block;
    border-bottom:5px solid;
    padding-bottom:2px;
}

#tip-bar .handle {
  background-color: $TW-Theme-HighContrast;
  left: -40px;
  padding: 10px;
  position: absolute;
  top: 0;
  height: 400px;
  width: 45px;
  cursor: pointer;
  margin-top: 200px;
  color: white;
}

#tip-bar .handle .fa{
    margin-top: 20px;
}
.skin-thinkware .main-header .navbar {
    background-color: $TW-Theme-HighContrast !important;
}

.skin-thinkware .main-header .logo {
    background-color: $TW-Theme-HighContrast !important;
    color: #ffffff;
    border-bottom: 0px solid transparent;
}

.skin-thinkware .sidebar-menu > li:hover > a, .skin-thinkware .sidebar-menu > li.active > a {
    color: #ffffff;
    background: #1e282c;
    border-left-color: $TW-Theme-HighContrast;
}

.skin-thinkware .main-header .navbar .sidebar-toggle:hover {
  background-color: $TW-Hover-HighContrast !important;
}

.colored-line-left {
  height: 1px;
  width: 70px;
  margin-top: 10px;
  margin-bottom: 10px;
  background: $TW-Theme-HighContrast; }

.legend-text h3{    
    text-align: center;
}

h3.legend {
    position: relative;
    z-index: 1;
    
    &:before {
        border-top: 2px solid $TW-Theme-HighContrast;
        content:"";
        margin: 0 auto; /* this centers the line to the full width specified */
        position: absolute; /* positioning must be absolute here, and relative positioning must be applied to the parent */
        top: 50%; left: 0; right: 0; bottom: 0;
        width: 95%;
        z-index: -1;
    }span { 
        /* to hide the lines from behind the text, you have to set the background color the same as the container */ 
        background: #fff; 
        padding: 0 15px; 
    }
}

.legend-icons{
    padding-top: 10px;
}

.list-icon-mail{
    color: white !important; 
}

.widget-heading {
        padding: 10px 15px;
        border-bottom: 1px solid transparent;
        background-color: #eee;
        color: #000;
        //text-align: center;
        cursor: move;        
        font-weight: 600;
        font-size: 22px;
    }

.filter-text h3{    
    text-align: center;
}

h3.filter {
    position: relative;
    z-index: 1;
    
    &:before {
        border-top: 2px solid $TW-Theme-HighContrast;
        content:"";
        margin: 0 auto; /* this centers the line to the full width specified */
        position: absolute; /* positioning must be absolute here, and relative positioning must be applied to the parent */
        top: 50%; left: 0; right: 0; bottom: 0;
        width: 95%;
        z-index: -1;
    }span { 
        /* to hide the lines from behind the text, you have to set the background color the same as the container */ 
        background: #fff; 
        padding: 0 15px;  
    } 
}
#peo-div .subsectionheader    {
    background-color:$TW-Theme-HighContrast;
    margin-top:30px;
    padding:4px;
    text-align:center;
    color:#ffffff;
    font-size:1em;
    font-weight:bold;

}
.ui-rangeSlider-bar {
    background: $TW-Theme-HighContrast !important;
    height: 29px;
    margin: 1px 0;
    -moz-border-radius: 4px;
    border-radius: 4px;
    cursor: move;
    cursor: grab;
    cursor: -moz-grab;
    -webkit-box-shadow: inset 0 2px 6px RGBA(0,0,0,.5);
    -moz-box-shadow: inset 0 2px 6px RGBA(0,0,0,.5);
    box-shadow: inset 0 2px 6px RGBA(0,0,0,.5);
}

.borderradio .borderradio-thinkware input[type="radio"]:checked ~ label:before,
.borderradio .borderradio-thinkware input[type="checkbox"]:checked ~ label:before {
  color: #fff;
  background-color: $TW-Theme-HighContrast;
}

.time-clock-background{
    background-color: $TW-Theme-HighContrast;
    color: white;    
}
/*Dashboard*/
.dashboard-invoices-footer{
    background-color: $TW-Theme-HighContrast;
    height: 50px;
    color: white;
    font-size: 22px;
}
.dashboard-header{
    background-color: $TW-Theme-HighContrast;
    text-align: center;
   
}
.card-top{
    border-top: 5px solid $TW-Theme-HighContrast;
}
/*checkstub*/
.stub-background-header{
    background-color: $TW-Theme-HighContrast;
    color: white;
}

.employee_details .table-striped>tbody>tr:nth-of-type(odd) {
    background-color: #1b141a !important;
}

.table-rows-recolor .table-striped>tbody>tr:nth-of-type(odd) {
    background-color: #1b141a !important;
}
  
.sub-table-background {
    border: 1px solid #ccc;
    background-color: #1b141a !important;
}
//snapshot
.active-emp-snapshot{
    border-right: 5px solid $TW-Hover-HighContrast  !important;
    border-bottom: 1px solid $TW-Hover-HighContrast  !important;
    background-color: $TW-Hover-HighContrast  !important;
    color: white;
}
#emplNavSearch .k-dropdown-wrap.k-state-default {
    background-image: none;
    background-position: 50% 50%;
    background-color: #fff;
    border-color: #ccc;
}

#emplNavSearch .k-dropdown .k-input {
    color: #000;
}


/*The HighContrast theme's treeview font and arrows are white on a white background, so we change 
    the font color and use the Bootstrap theme's arrows instead*/
.k-treeview .k-in {
    color: black;
}

.k-treeview .k-state-selected {
    color: white;
}

#treeview-left .k-group .k-item .k-top .k-icon,
#treeview-left .k-group .k-item .k-mid .k-icon,
#treeview-left .k-group .k-item .k-bot .k-icon {
    background-image: url('../themes/Bootstrap/sprite.png') !important;
}

/*Vue-Multiselect*/
.multiselect__tag {
    background: $TW-Theme-HighContrast !important;
    color: #fff !important;
}

.multiselect__tag-icon::after {
    color: #fff !important;
}