@charset "UTF-8";
/*Imports*/
@import url(https://fonts.googleapis.com/css?family=Roboto:400,300,700);
/*End Imports*/
/*Main Styling*/
@import url("//netdna.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css");
body,
html {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  /*font-family: 'Roboto', sans-serif;*/
  color: #000;
  background: #FFFFFF;
}

.error {
  COLOR: RED;
  font-weight: normal;
}

.inactive-true {
  color: red !important;
}

.panel-group .panel + .panel {
  margin-top: 0;
}

.col-centered {
  float: none;
  margin: 0 auto;
}

.login-box {
  box-shadow: 10px 10px 5px rgba(0, 0, 0, 0.5);
  background-color: #fff;
  min-height: 380px;
}

.login-box .img-responsive {
  margin: 0 auto;
}

.home-background {
  background-color: #f3f3f3;
  height: 100%;
}

html {
  -ms-overflow-style: scrollbar;
}

.talk-to-the-hand {
  cursor: pointer;
}

.makemebig {
  font-size: 36px;
}

.slider-dates {
  margin-top: 15px;
}

#empNavSub li > a {
  font-size: 16px;
}

.regWorkHours input {
  margin-bottom: 5px;
}

.required-error {
  padding-top: 5px;
  color: #e50000;
}

.main-content-pad {
  padding-right: 40px;
}

.message-pad-top {
  padding-top: 10px;
}

/*End Main Styling*/
/*Buttons*/
.button-center {
  text-align: center;
}

.button-nav {
  border-bottom: solid 1px #ccc;
  margin-bottom: 15px;
}

.btn-t, .btn-sm {
  padding: 5px 10px 5px 10px;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 6px;
  margin-bottom: 3px;
}

.btn-xlg,
.btn-group-xlg > .btn {
  padding: 20px 56px;
  font-size: 32px;
  line-height: 1.3333333;
  border-radius: 0;
}

.btn-md,
.btn-group-md > .btn {
  padding: 6px 12px;
  font-size: 16px;
  line-height: 1.3333333;
  border-radius: 0;
}

@media (max-width: 768px) {
  .btn-responsive {
    padding: 2px 4px;
    font-size: 80%;
    line-height: 1;
    border-radius: 3px;
  }
}
@media (min-width: 769px) and (max-width: 992px) {
  .btn-responsive {
    padding: 8px 13px;
    font-size: 90%;
    line-height: 1.2;
  }
}
.tw-button a {
  text-decoration: none;
  color: #fff;
}

tw-button a:link {
  text-decoration: none;
  color: #fff;
}

.tw-button a:visited {
  text-decoration: none;
  color: #fff;
}

.tw-button a:hover {
  text-decoration: none;
  color: #fff;
}

.tw-button a:active {
  text-decoration: none;
  color: #fff;
}

.setup-buttons {
  padding-bottom: 10px;
}

.setup-buttons-inner {
  padding-bottom: 15px;
}

.table-icons button {
  border: none;
  padding: 5px 10px;
}

.table-icons button:link {
  border: none;
}

.table-icons button:visited {
  border: none;
}

.table-icons button:hover {
  border: none;
}

.table-icons button:focus {
  outline: 0;
}

.table-icons a {
  border: none;
}

.table-icons a:link {
  border: none;
}

.table-icons a:visited {
  border: none;
}

.table-icons a:hover {
  border: none;
}

.table-icons a:active {
  border: none;
}

.table-icons a:focus {
  outline: 0;
}

/*End Buttons*/
/*Icons*/
.icon-white {
  color: white !important;
}

.icon-red {
  color: #d34f4f !important;
}

.icon-dark-gray {
  color: #6C7A89 !important;
}

.icon-green {
  color: #00b16a !important;
}

.icon-dark-green {
  color: #26A65B !important;
}

.icon-replicate {
  color: #1E824C !important;
}

.icon-edit {
  color: #2574a9 !important;
}

.icon-purple {
  color: #674172 !important;
}

.icon-black {
  color: #000 !important;
}

.icon-blue {
  color: #1E8BC3 !important;
}

.icon-light-yellow {
  color: #f8b722 !important;
}

.icon-california {
  color: #f89406 !important;
}

.icon-wistful {
  color: #aea8d3 !important;
}

.icon-snuff {
  color: #DCC6E0 !important;
}

.icon-gossip {
  color: #87d37c !important;
}

icon-niagara {
  color: #2ABB9B !important;
}

.icon-cinnabar {
  color: #e74c3c !important;
}

.icon-jaffa {
  color: #F4B350 !important;
}

.icon-lynch {
  color: #6c7a89 !important;
}

.icon-thinkware-red {
  color: #880024 !important;
}

.icon-edward {
  color: #abb7b7 !important;
}

.icon-transparent {
  color: transparent !important;
}

.icon-center {
  text-align: center;
}

/*End Icons*/
/*Icon Layout Mods*/
.chevron-select {
  padding-top: 50%;
}

.chevron-add-bottom {
  padding-bottom: 45px;
}

/*End Icon Layout Mods*/
/*Grid Mods*/
.create-pad {
  padding-right: 25px;
}

.create-pad-left {
  padding-left: 16px;
}

.create-pad-sm {
  padding-right: 15px;
}

.k-grid-toolbar {
  border-top: hidden;
}

.grid-center {
  text-align: center;
}

.div-44 {
  padding-top: 44px;
}

.div-10 {
  padding-top: 10px;
}

.div-6 {
  padding-top: 6px;
}

/*End Grid Mods*/
/*Navigation Mods*/
.navbar-default .navbar-brand {
  color: white;
}

.navbar {
  border-radius: 1px;
}

/*End Navigation Mods*/
/*Tables*/
.table-bottom {
  padding-bottom: 15px;
}

.table-body {
  padding: 15px;
}

table {
  table-layout: fixed;
}

#report-writer table {
  table-layout: inherit;
}

td {
  word-wrap: break-word;
}

.k-grid td, th {
  border-width: 0;
}

.subTable .k-grid-header {
  display: none;
}

.subTable .k-pager-wrap.k-grid-pager.k-widget {
  display: none;
}

.k-grid-header th.k-header {
  vertical-align: top;
}

/*End Tables*/
/*Forms*/
.form-control-preview {
  display: block;
  width: 100%;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.control-display {
  padding-top: 7px;
}

.static-field {
  display: block;
  width: 100%;
  height: 34px;
  padding-top: 8px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
}

/*End Forms*/
/*Modals*/
.modal-footer-custom {
  padding: 19px 20px 20px;
  margin-top: 15px;
  text-align: right;
}

.modal-footer-custom:before,
.modal-footer-custom:after {
  display: table;
  content: " ";
}

.modal-footer-custom:after {
  clear: both;
}

.modal-footer-custom:before,
.modal-footer-custom:after {
  display: table;
  content: " ";
}

.modal-footer-custom:after {
  clear: both;
}

.modal-footer-custom .btn + .btn {
  margin-bottom: 0;
  margin-left: 5px;
}

.modal-footer-custom .btn-group .btn + .btn {
  margin-left: -1px;
}

.modal-footer-custom .btn-block + .btn-block {
  margin-left: 0;
}

/*Form Required*/
/*required form fields - Removed to support IE Compatibility mode. Please do not remove*/
/*:required {
    border-color: #e50000 !important;
}

:required:focus {
    border-color: #e50000 !important;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgb(229, 0, 0);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgb(229, 0, 0);
}*/
.field-required-wrap .k-picker-wrap {
  border-color: #e50000 !important;
}

.field-required-wrap .k-picker-wrap.k-state-hover {
  border-color: #e50000 !important;
}

.field-required.form-control {
  border-color: #e50000 !important;
}

.field-required.form-control:focus {
  border-color: #e50000 !important;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgb(229, 0, 0);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgb(229, 0, 0);
}

/*End Required*/
/*End Forms*/
/*Layout*/
.client-setup-assign-text {
  padding-top: 7px;
}

.assignment-top {
  padding-top: 25px;
}

.assign-search {
  padding-bottom: 45px;
}

.onboarding select {
  display: block;
  width: 75%;
  margin-right: 2px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.428571429;
  color: #555555;
  vertical-align: middle;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.client-setup-codes-select {
  padding-bottom: 25px;
}

.profile-name {
  font-size: 15px;
}

.setup-code-edit {
  padding-bottom: 10px;
  padding-left: 15px;
}

.setup-code-edit-np {
  padding-bottom: 2px;
  padding-left: 15px;
}

.instructions-box-dynamic {
  padding-top: 33px;
}

.dynamic-box {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #ccc;
}

.dynamic-box-createprocess {
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.instructions-box {
  font-style: italic;
}

/*End Layout*/
/*DatePicker*/
#datetimepicker_timeview li {
  color: #333;
  border-color: #363940;
}

.k-widget.k-datepicker.k-header {
  width: 100%;
}

.k-widget.k-datetimepicker.k-header {
  width: 100%;
}

/*End DatePicker*/
/*Dyanmic Sidebar*/
.nav-sidebar {
  width: 100%;
  border-right: 1px solid #ddd;
}

.nav-sidebar a {
  color: #333;
  -webkit-transition: all 0.08s linear;
  -moz-transition: all 0.08s linear;
  -o-transition: all 0.08s linear;
  transition: all 0.08s linear;
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}

.nav-sidebar .active a {
  cursor: default;
  background-color: #ddd;
  color: #333;
}

.nav-sidebar .active a:hover {
  background-color: #ccc;
}

.nav-sidebar .text-overflow a,
.nav-sidebar .text-overflow .media-body {
  white-space: nowrap;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
}

/*End Dynamic Sidebar*/
/* Right-aligned sidebar */
.nav-sidebar.pull-right {
  border-right: 0;
  border-left: 1px solid #ddd;
}

.nav-sidebar.pull-right a {
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}

/*End Right-aligned sidebar*/
/*Wizard*/
.stepwizard-step p {
  margin-top: 10px;
}

.stepwizard-row {
  display: table-row;
}

.stepwizard {
  display: table;
  width: 100%;
  position: relative;
  padding-bottom: 15px;
}

.stepwizard-step button[disabled] {
  opacity: 1 !important;
  filter: alpha(opacity=100) !important;
}

.stepwizard-row:before {
  top: 14px;
  bottom: 0;
  position: absolute;
  content: " ";
  width: 100%;
  height: 1px;
  background-color: #ccc;
  z-order: 0;
}

.stepwizard-step {
  display: table-cell;
  text-align: center;
  position: relative;
}

.btn-circle {
  width: 50px;
  height: 50px;
  text-align: center;
  padding: 8px 0;
  font-size: 20px;
  line-height: 1.428571429;
  border-radius: 35px;
}

.finalize-text .btn-circle {
  width: 50px;
  height: 50px;
  text-align: center;
  padding: 8px 0;
  font-size: 20px;
  line-height: 1.428571429;
  border-radius: 35px;
}

.obprocessfinalize .btn-circle {
  width: 50px;
  height: 50px;
  text-align: center;
  padding: 8px 0;
  font-size: 20px;
  line-height: 1.428571429;
  border-radius: 35px;
}

.btn-circle .focus.btn, .btn:focus, .btn:hover {
  color: #fff;
  text-decoration: none;
}

/*End Wizard*/
/*Labels*/
.label-thinkware {
  background-color: #244061;
}

.nav-sidebar .label-tw {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 80%;
  font-weight: 500;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
}

/*End Labels*/
/*Instructions Size*/
.instructions-subtext {
  font-size: 90%;
  font-weight: 700;
  padding-left: 25px;
}

/*End Instructions Size*/
/* Text Colors */
.text-color-white {
  color: #FFFFFF;
}

.text-color-black {
  color: #000000;
}

.text-color-theme {
  color: #6f5499;
}

/*End Text Colors*/
/* Background colors */
.theme-bg {
  background: #6f5499;
}

.bg-black {
  background: #000000;
}

.secondary-bg {
  background: #f0ecf6;
}

/*End Background Colors*/
/* Margin */
.margin-bottom-0 {
  margin-bottom: 0 !important;
}

.margin-bottom-5 {
  margin-bottom: 5px !important;
}

.margin-bottom-10 {
  margin-bottom: 10px !important;
}

.margin-bottom-15 {
  margin-bottom: 15px !important;
}

.margin-bottom-20 {
  margin-bottom: 20px !important;
}

.margin-bottom-30 {
  margin-bottom: 30px !important;
}

.margin-bottom-40 {
  margin-bottom: 40px !important;
}

.margin-bottom-50 {
  margin-bottom: 50px !important;
}

.margin-bottom-60 {
  margin-bottom: 60px !important;
}

.margin-bottom-70 {
  margin-bottom: 70px !important;
}

.margin-bottom-80 {
  margin-bottom: 80px !important;
}

.margin-bottom-90 {
  margin-bottom: 90px !important;
}

.margin-bottom-100 {
  margin-bottom: 100px !important;
}

.margin-bottom-120 {
  margin-bottom: 120px !important;
}

.margin-right-0 {
  margin-right: 0 !important;
}

.margin-right-5 {
  margin-right: 5px !important;
}

.margin-right-10 {
  margin-right: 10px !important;
}

/*End Margin*/
/* Padding surround */
.padding-0 {
  padding: 0px !important;
}

.padding-30 {
  padding: 30px !important;
}

.padding-50 {
  padding: 40px !important;
}

/*End Padding*/
/* Buttons */
.btn-xs {
  font-size: 11px;
  line-height: 14px;
  border: 1px solid;
  padding: 5px 10px;
}

.btn-sm {
  font-size: 12px;
  line-height: 16px;
  border: 2px solid;
  padding: 8px 15px;
}

.btn-lg {
  font-size: 18px;
  line-height: 22px;
  margin: 4px;
  border-radius: 6px;
  padding: 13px 40px;
}

@media only screen and (min-width: 320px) and (max-width: 480px) {
  .btn-lg {
    font-size: 13px;
    line-height: 16px;
    margin: 4px;
    border-radius: 6px;
    padding: 13px 20px;
  }
}
/*Old Tips Bar bar*/
#menu-bar {
  background-color: #fff;
  color: #fff;
  z-index: 1002;
  border-left: 3px solid #244061;
  /*margin-top: 60px;*/
  height: 100% !important;
}

#menu-bar .handle {
  background-color: #244061;
  left: -40px;
  padding: 10px;
  position: absolute;
  top: 0;
  width: 45px;
  cursor: pointer;
  margin-top: 200px;
}

#menu-bar .inner {
  color: #333;
  padding: 10px 10px 10px 10px;
}

#menu-bar .text {
  padding-bottom: 15px;
}

/*Old Tips Bar*/
.hidden-filter .k-grid-filter {
  visibility: hidden;
}

.completion-date {
  border-bottom: 1px solid #ccc;
  width: 80%;
  margin-bottom: 5px;
}

.completion-date p {
  padding-top: 5px;
}

.k-editor .k-editable-area {
  width: 100%;
  height: 100%;
  border-style: solid;
  border-width: 1px;
  outline: 0;
  padding: 5px 5px 5px 5px;
}

.k-editor .k-resize-handle {
  position: absolute;
  padding: 0;
  right: 0;
  bottom: 0;
}

.k-colorpalette .k-palette {
  border-collapse: collapse;
  position: relative;
  width: 75px;
  height: 100%;
}

.tip-image-upload {
  padding: 5px 0 15px 0;
}

.tip-modal-image-top {
  padding-top: 10px;
}

.order-header {
  padding-bottom: 10px;
}

/*OBProcessFinalize*/
.obprocessfinalize h2.background {
  position: relative;
  z-index: 1;
}
.obprocessfinalize h2.background:before {
  border-top: 2px solid #dfdfdf;
  content: "";
  margin: 0 auto; /* this centers the line to the full width specified */
  position: absolute; /* positioning must be absolute here, and relative positioning must be applied to the parent */
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 95%;
  z-index: -1;
}
.obprocessfinalize h2.background span {
  /* to hide the lines from behind the text, you have to set the background color the same as the container */
  background: #fff;
  padding: 0 15px;
}

.obprocessfinalize h2.double:before {
  /* this is just to undo the :before styling from above */
  border-top: none;
}

.obprocessfinalize h2.double:after {
  border-bottom: 1px solid #ccc;
  content: "";
  margin: 0 auto; /* this centers the line to the full width specified */
  position: absolute;
  top: 45%;
  left: 0;
  right: 0;
  width: 95%;
  z-index: -1;
}

.finalize-text {
  padding-bottom: 10px;
}

.doc-border {
  border: 1px solid #ccc;
}

.doc-bottom {
  padding-bottom: 15px;
}

.doc-top {
  margin-top: 15px;
}

.finalize-submit {
  padding-top: 10px;
}

.finalize-signature {
  padding: 10px 0 10px 15px;
}

.finalize-check {
  padding: 10px 0 10px 15px;
}

.routing-result {
  padding-bottom: 25px;
}

/*End Finalize*/
/*Dynamic Styling*/
.dynamic-dropdown .k-dropdown {
  background-image: none;
  width: 75%;
}

.dynamic-dropdown .k-input {
  background-color: #fff;
  color: #000;
}

.dynamic-dropdown .k-dropdown-wrap.k-state-default.k-state-hover.k-state-hover:hover .k-input {
  color: #000 !important;
}

.dynamic-dropdown .k-dropdown-wrap.k-state-default.k-state-focused .k-input {
  color: #000 !important;
}

.employer-i9 {
  padding-top: 15px;
}

.check-down {
  padding-top: 5px;
}

.dynamic-check-top {
  margin-top: 13px !important;
}

.waive-top {
  padding-top: 15px;
}

.complete-bottom .btn-t .btn-thinkware {
  padding-bottom: 15px;
}

.complete-bottom {
  padding-bottom: 15px;
}

/*End Dynamic Styling*/
/* Right sidebar.*/
.tip-sidebar-bg {
  position: fixed;
  z-index: 1000;
  bottom: 0;
}

.tip-sidebar-bg,
.tip-sidebar {
  top: 0;
  right: -230px;
  width: 230px;
  -webkit-transition: right 0.3s ease-in-out;
  -o-transition: right 0.3s ease-in-out;
  transition: right 0.3s ease-in-out;
}

.tip-sidebar {
  position: absolute;
  padding-top: 50px;
  z-index: 1010;
}

@media (max-width: 768px) {
  .tip-sidebar {
    padding-top: 100px;
  }
}
.tip-sidebar > .tab-content {
  padding: 10px 15px;
}

.tip-sidebar.tip-sidebar-open,
.tip-sidebar.tip-sidebar-open + .tip-sidebar-bg {
  right: 0;
}

.tip-sidebar-open .tip-sidebar-bg,
.tip-sidebar-open .tip-sidebar {
  right: 0;
}

@media (min-width: 768px) {
  .tip-sidebar-open .content-wrapper,
  .tip-sidebar-open .right-side,
  .tip-sidebar-open .main-footer {
    margin-right: 230px;
  }
}
.nav-tabs.tip-sidebar-tabs > li:first-of-type > a,
.nav-tabs.tip-sidebar-tabs > li:first-of-type > a:hover,
.nav-tabs.tip-sidebar-tabs > li:first-of-type > a:focus {
  border-left-width: 0;
}

.nav-tabs.tip-sidebar-tabs > li > a {
  border-radius: 0;
}

.nav-tabs.tip-sidebar-tabs > li > a,
.nav-tabs.tip-sidebar-tabs > li > a:hover {
  border-top: none;
  border-right: none;
  border-left: 1px solid transparent;
  border-bottom: 1px solid transparent;
}

.nav-tabs.tip-sidebar-tabs > li > a .icon {
  font-size: 16px;
}

.nav-tabs.tip-sidebar-tabs > li.active > a,
.nav-tabs.tip-sidebar-tabs > li.active > a:hover,
.nav-tabs.tip-sidebar-tabs > li.active > a:focus,
.nav-tabs.tip-sidebar-tabs > li.active > a:active {
  border-top: none;
  border-right: none;
  border-bottom: none;
}

@media (max-width: 768px) {
  .nav-tabs.tip-sidebar-tabs {
    display: table;
  }
  .nav-tabs.tip-sidebar-tabs > li {
    display: table-cell;
  }
}
.tip-sidebar-heading {
  font-weight: 400;
  font-size: 16px;
  padding: 10px 0;
  margin-bottom: 10px;
}

.tip-sidebar-subheading {
  display: block;
  font-weight: 400;
  font-size: 14px;
}

.tip-sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0 -15px;
}

.tip-sidebar-menu > li > a {
  display: block;
  padding: 10px 15px;
}

.tip-sidebar-menu > li > a:before,
.tip-sidebar-menu > li > a:after {
  content: " ";
  display: table;
}

.tip-sidebar-menu > li > a:after {
  clear: both;
}

.tip-sidebar-menu > li > a > .tip-sidebar-subheading {
  margin-top: 0;
}

.tip-sidebar-menu .menu-icon {
  float: left;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  text-align: center;
  line-height: 35px;
}

.tip-sidebar-menu .menu-info {
  margin-left: 45px;
  margin-top: 3px;
}

.tip-sidebar-menu .menu-info > .tip-sidebar-subheading {
  margin: 0;
}

.tip-sidebar-menu .menu-info > p {
  margin: 0;
  font-size: 11px;
}

.tip-sidebar-menu .progress {
  margin: 0;
}

/*.tip-sidebar-dark {
  color: #b8c7ce;
}*/
.tip-sidebar-dark,
.tip-sidebar-dark + .tip-sidebar-bg {
  background: #eee;
}

.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs {
  border-bottom: #1c2529;
}

.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a {
  background: #181f23;
  color: #b8c7ce;
}

.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a,
.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a:hover,
.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a:focus {
  border-left-color: #141a1d;
  border-bottom-color: #141a1d;
}

.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a:hover,
.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a:focus,
.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a:active {
  background: #1c2529;
}

.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li > a:hover {
  color: #fff;
}

.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li.active > a,
.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li.active > a:hover,
.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li.active > a:focus,
.tip-sidebar-dark .nav-tabs.tip-sidebar-tabs > li.active > a:active {
  background: #222d32;
  color: #fff;
}

.tip-sidebar-dark .tip-sidebar-heading,
.tip-sidebar-dark .tip-sidebar-subheading {
  color: #fff;
}

.tip-sidebar-dark .tip-sidebar-menu > li > a:hover {
  background: #1e282c;
}

.tip-sidebar-dark .tip-sidebar-menu > li > a .menu-info > p {
  color: #b8c7ce;
}

.tip-sidebar-light {
  color: #5e5e5e;
}

.tip-sidebar-light,
.tip-sidebar-light + .tip-sidebar-bg {
  background: #f9fafc;
  border-left: 1px solid #d2d6de;
}

.tip-sidebar-light .nav-tabs.tip-sidebar-tabs {
  border-bottom: #d2d6de;
}

.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li > a {
  background: #e8ecf4;
  color: #444444;
}

.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li > a,
.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li > a:hover,
.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li > a:focus {
  border-left-color: #d2d6de;
  border-bottom-color: #d2d6de;
}

.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li > a:hover,
.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li > a:focus,
.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li > a:active {
  background: #eff1f7;
}

.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li.active > a,
.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li.active > a:hover,
.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li.active > a:focus,
.tip-sidebar-light .nav-tabs.tip-sidebar-tabs > li.active > a:active {
  background: #f9fafc;
  color: #111;
}

.tip-sidebar-light .tip-sidebar-heading,
.tip-sidebar-light .tip-sidebar-subheading {
  color: #111;
}

.tip-sidebar-light .tip-sidebar-menu {
  margin-left: -14px;
}

.tip-sidebar-light .tip-sidebar-menu > li > a:hover {
  background: #f4f4f5;
}

.tip-sidebar-light .tip-sidebar-menu > li > a .menu-info > p {
  color: #5e5e5e;
}

/*End Tip Sidebar*/
.k-insertHtml {
  width: 250px;
}

/*message center*/
.note-box {
  border: 1px solid #ccc;
  height: 200px;
  overflow: auto;
}

.note-box .inner {
  padding: 5px;
}

/*end message center*/
/*paycodes multiselect*/
.base-multiselect {
  height: 15em;
  padding: 5px 5px 5px 5px;
  overflow: auto;
}

.base-multiselect label {
  display: block;
  margin-bottom: 0;
}

.base-multiselect-on {
  color: #333;
  background-color: #ccc;
}

/*end paycodes multiselect*/
.header-center {
  text-align: center !important;
}

/*Import Mapping Upload*/
.import-mapping-select {
  padding-bottom: 10px;
}

.import-warning {
  padding: 0 50px 0 50px;
}

/*Company Info*/
.company-info-box {
  min-height: 100px;
  border: 1px solid #ccc;
}

.company-info-box .inner {
  padding: 15px 15px 15px 15px;
}

.company-info-box .company-name {
  font-size: 24px;
}

.company-info-box .client-info {
  font-size: 18px;
}

.company-info-box .image-logo {
  padding: 5px 0 15px 0;
}

.company-info {
  padding-top: 5px;
}

.company-info-header {
  padding: 0 10px 10px 30px;
  font-size: 28px;
}

.company-info-data {
  padding-top: 25px;
}

.company-info-form {
  padding: 0 30px 0 30px;
}

.company-info-form .top {
  margin-bottom: 10px;
}

.short-comp-text {
  padding-left: 10px;
  font-size: 24px;
}

/*Employee Info*/
.employee-info-box {
  min-height: 100px;
  border: 1px solid #ccc;
}

.employee-info-box .inner {
  padding: 15px 15px 15px 15px;
}

.employee-info-box .company-name {
  font-size: 28px;
}

.employee-info-box .client-info {
  font-size: 22px;
  padding-top: 15px;
}

.employee-info-box .image-logo {
  padding-bottom: 15px;
}

.employee-info {
  padding-bottom: 15px;
}

.employee-info-header {
  padding: 0 10px 10px 30px;
  font-size: 28px;
}

.employee-info-data {
  padding-top: 25px;
  max-width: 792px;
}

.employee-info-form {
  padding: 0 30px 0 30px;
}

.employee-info-form .top {
  border-bottom: 1px solid #ccc;
  margin-bottom: 10px;
}

/*Contacts - Company*/
.contacts-toolbar {
  padding-top: 25px;
}

/*HR - Company*/
.company-setup-hr-select {
  padding: 10px 10px 0 30px;
}

/*Invoices*/
.current-invoice-content {
  padding-bottom: 10px;
}

.current-invoice-list li {
  margin: 6px 5px;
}

.invoice-current-comments {
  height: 200px;
  overflow: auto;
}

#invoices .height {
  min-height: 160px;
}

#invoices .height-scroll {
  height: 160px;
}

.height-scroll-inner {
  overflow-y: auto;
  height: 135px;
}

#invoices .panel-body .invoice-sub-type {
  font-weight: 600;
  padding: 0 3px 0 3px;
}

#balances .panel-body .invoice-sub-type {
  font-weight: 600;
  padding: 0 3px 0 3px;
}

#invoices .panel-footer span {
  font-weight: 600;
  display: inline-block;
}

#invoices .select-center {
  margin: auto;
  max-width: 300px;
}

#invoices .invoice-blocks {
  margin: auto;
}

@media (max-width: 1050px) {
  .invoice-side {
    padding: 0 25px 0 25px;
  }
}
#balances .height {
  min-height: 210px;
}

/*#balances .panel-body span {
    display: inline-block;
    max-width: 200px;
    min-width: 150px;
    background-color: #ccc;
    margin-bottom: 3px;
    padding: 0 3px 0 3px;
}*/
#balances .select-center {
  margin: auto;
  max-width: 300px;
}

#balances .invoice-blocks {
  margin: auto;
}

.gridster li {
  list-style: none;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.45);
}

/*.widget-heading:hover {
        background-color: #b26200;
    }*/
.rotate {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
}

#gridBarChart {
  -webkit-animation-duration: 0.5s;
}

.acabar {
  position: relative;
  display: block;
  margin-bottom: 15px;
  width: 100%;
  height: 35px;
  border-radius: 3px;
  background-color: #fff;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  -webkit-transition: 0.4s ease;
  -moz-transition: 0.4s ease;
  -ms-transition: 0.4s ease;
  -o-transition: 0.4s ease;
  transition: 0.4s ease;
  -webkit-transition-property: width, background-color;
  -moz-transition-property: width, background-color;
  -ms-transition-property: width, background-color;
  -o-transition-property: width, background-color;
  transition-property: width, background-color;
}

.acabar-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  font-weight: bold;
  font-size: 16px;
  color: #fff;
  margin-left: auto;
  -webkit-border-top-left-radius: 3px;
  -webkit-border-bottom-left-radius: 4px;
  -moz-border-radius-topleft: 3px;
  -moz-border-radius-bottomleft: 3px;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.acabar-title span {
  display: block;
  background: rgba(0, 0, 0, 0.1);
  padding: 0 10px;
  height: 35px;
  line-height: 35px;
  -webkit-border-top-left-radius: 3px;
  -webkit-border-bottom-left-radius: 3px;
  -moz-border-radius-topleft: 3px;
  -moz-border-radius-bottomleft: 3px;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.acabar-bar {
  height: 35px;
  width: 0px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
}

.aca-bar-percent {
  position: absolute;
  right: 10px;
  top: 0;
  font-size: 11px;
  font-weight: 500;
  height: 35px;
  line-height: 35px;
  color: #000;
  /*color:rgba(0, 0, 0, 0.4);*/
}

.aca-label {
  padding-top: 5px;
}

.bar-label {
  text-align: right;
}

.col-width {
  width: 125px;
}

.underline {
  text-decoration: underline;
}

.grid-stack-item-content {
  background: white;
  text-align: center;
}

.CodeSummaryPadding {
  padding: 5px;
}

/*Employee Slider*/
#emp-slider-list .glyphicon-lg {
  font-size: 4em;
}

#emp-slider-list .info-block {
  border-right: 5px solid #E6E6E6;
  border-bottom: 1px solid #E6E6E6;
  background-color: #eee;
}

#emp-slider-list .info-block .square-box {
  width: 100px;
  min-height: 110px;
  margin-right: 22px;
  text-align: center !important;
  background-color: #676767;
  padding: 20px 0;
}

#emp-slider-list .info-block.block-info {
  border-color: #ddd;
}

#emp-slider-list .info-block.block-info .square-box {
  background-color: #eee;
  color: #FFF;
}

#emp-slider-list .info-block.block-info .square-box img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border-color: #244061;
}

#emp-slider-list .search {
  padding-bottom: 15px;
}

#emp-slider-list .user-list {
  width: 325px;
}

#emp-slider-list .scroller {
  overflow-y: auto;
  height: 1200px;
}

.empIndexSlider .scroller {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 290px;
}

.empIndexSlider .user-list {
  width: 100%;
  padding-bottom: 10px;
}

.empIndexSlider .empindex-inner {
  padding: 10px 25px 10px 10px;
  border-bottom: 1px solid #ccc;
}

/*radios*/
.borderradio label {
  width: 100%;
  border-radius: 3px;
  border: 1px solid #D1D3D4;
  font-weight: normal;
}

.borderradio input[type=radio]:empty,
.borderradio input[type=checkbox]:empty {
  display: none;
}

.borderradio input[type=radio]:empty ~ label,
.borderradio input[type=checkbox]:empty ~ label {
  position: relative;
  line-height: 2.5em;
  text-indent: 3.25em;
  margin-top: 2em;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.borderradio input[type=radio]:empty ~ label:before,
.borderradio input[type=checkbox]:empty ~ label:before {
  position: absolute;
  display: block;
  top: 0;
  bottom: 0;
  left: 0;
  content: "";
  width: 2.5em;
  background: #D1D3D4;
  border-radius: 3px 0 0 3px;
}

.borderradio input[type=radio]:hover:not(:checked) ~ label,
.borderradio input[type=checkbox]:hover:not(:checked) ~ label {
  color: #888;
}

.borderradio input[type=radio]:hover:not(:checked) ~ label:before,
.borderradio input[type=checkbox]:hover:not(:checked) ~ label:before {
  content: "✔";
  text-indent: 0.9em;
  color: #C2C2C2;
}

.borderradio input[type=radio]:checked ~ label,
.borderradio input[type=checkbox]:checked ~ label {
  color: #777;
}

.borderradio input[type=radio]:checked ~ label:before,
.borderradio input[type=checkbox]:checked ~ label:before {
  content: "✔";
  text-indent: 0.9em;
  color: #333;
  background-color: #ccc;
}

.borderradio input[type=radio]:focus ~ label:before,
.borderradio input[type=checkbox]:focus ~ label:before {
  box-shadow: 0 0 0 3px #999;
}

.borderradio-default input[type=radio]:checked ~ label:before,
.borderradio-default input[type=checkbox]:checked ~ label:before {
  color: #333;
  background-color: #ccc;
}

.borderradio-primary input[type=radio]:checked ~ label:before,
.borderradio-primary input[type=checkbox]:checked ~ label:before {
  color: #fff;
  background-color: #337ab7;
}

.borderradio-success input[type=radio]:checked ~ label:before,
.borderradio-success input[type=checkbox]:checked ~ label:before {
  color: #fff;
  background-color: #5cb85c;
}

.borderradio-danger input[type=radio]:checked ~ label:before,
.borderradio-danger input[type=checkbox]:checked ~ label:before {
  color: #fff;
  background-color: #d9534f;
}

.borderradio-warning input[type=radio]:checked ~ label:before,
.borderradio-warning input[type=checkbox]:checked ~ label:before {
  color: #fff;
  background-color: #f0ad4e;
}

.borderradio-info input[type=radio]:checked ~ label:before,
.borderradio-info input[type=checkbox]:checked ~ label:before {
  color: #fff;
  background-color: #5bc0de;
}

.col-md-sm {
  position: relative;
  min-height: 1px;
  padding-right: 5px;
  padding-left: 15px;
}

@media (min-width: 992px) {
  .col-md-sm {
    float: left;
  }
}
/*Employee Search*/
.dropdown-header {
  font-size: 1.2em;
}

.dropdown-header > span {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-align: left;
  display: inline-block;
  border-style: solid;
  border-width: 0 0 1px 1px;
  padding: 0.3em 0.6em;
  width: 312px;
}

.dropdown-header > span:first-child {
  width: 82px;
  border-left-width: 0;
}

.demo-section {
  width: 400px;
  margin: 30px auto 50px;
  padding: 30px;
}

.demo-section h2 {
  text-transform: uppercase;
  font-size: 1.2em;
  margin-bottom: 10px;
}

.tag-image {
  width: auto;
  height: 18px;
  margin-right: 5px;
  vertical-align: top;
}

#employeesrch-list .k-item > span {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  border-style: solid;
  border-width: 0 0 1px 1px;
  vertical-align: top;
  min-height: 77px;
  width: 79%;
  padding: 0.6em 0 0 0.6em;
}

#employeesrch-list .k-item > span:first-child {
  width: 77px;
  border-left-width: 0;
  padding: 0.6em 0 0 10px;
}

#employeesrch-list img {
  -moz-box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid #880024;
}

#employeesrch-list h3 {
  font-size: 1.6em;
  margin: 0;
  padding: 0;
}

#employeesrch-list p {
  margin: 0;
  padding: 0;
}

#employeesrch-list li {
  min-height: 75px !important;
  cursor: pointer;
}

#employeesrch-list {
  background-color: #fff;
  Color: #333;
}

#employeesrch-list .k-state-focused, .k-list > .k-state-focused {
  -webkit-box-shadow: inset 0 0 0 1px #fff;
  box-shadow: inset 0 0 0 1px #fff;
}

#employeesrch-list .k-state-focused {
  border-color: #fff;
}

/*Employee Search Snapshot*/
#employeesearch-list .k-item > span {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  border-style: solid;
  border-width: 0 0 1px 1px;
  vertical-align: top;
  min-height: 95px;
  width: 62%;
  padding: 0.6em 0 0 0.6em;
}

#employeesearch-list .k-item > span:first-child {
  width: 77px;
  border-left-width: 0;
  padding: 0.6em 0 0 0;
}

#employeesearch-list img {
  -moz-box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 1px solid #880024;
}

#employeesearch-list h3 {
  font-size: 1.6em;
  margin: 0;
  padding: 0;
}

#employeesearch-list p {
  margin: 0;
  padding: 0;
}

.snapshot-links {
  text-decoration: underline;
}

.snapshot-links:hover {
  color: #333;
  text-decoration: underline;
}

#quicklist-font-size .btn {
  display: inline-block;
  padding: 2px 4px;
  margin-bottom: 0;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}

/*Cards*/
.card {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  background-color: white;
}

.top-card {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  background-color: white;
}

.card-footer {
  text-align: center;
  font-size: 22px;
}

.card {
  margin-top: 10px;
  box-sizing: border-box;
  border-radius: 2px;
  background-clip: padding-box;
  left: 10px;
  right: 10px;
  position: absolute;
  width: auto;
  height: 420px;
}

.card i {
  padding: 5px 5px 5px 5px;
}

.top-card {
  margin-top: 10px;
  box-sizing: border-box;
  border-radius: 2px;
  background-clip: padding-box;
  width: auto;
}

.top-card .card-content {
  padding: 5px 10px 5px 10px;
  border-radius: 0 0 2px 2px;
  background-clip: padding-box;
  box-sizing: border-box;
}

.top-card .card-title {
  font-size: 20px;
  font-weight: 500;
  text-transform: uppercase;
  border-bottom: 1px solid #ccc;
}

.top-card .card-inner {
  padding-top: 15px;
  font-size: 26px;
  font-weight: 400;
}

.card-info {
  padding: 5px 15px 5px 15px;
}

.card-info span {
  display: inline-block;
  vertical-align: top;
  width: 50%;
}

.quick-dash li {
  padding: 7px 0 0 15px;
  font-size: 18px;
}

.card span.card-title {
  padding-top: 5px;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
}

.card .card-image {
  position: relative;
  overflow: hidden;
}

.card .card-image img {
  border-radius: 2px 2px 0 0;
  background-clip: padding-box;
  position: relative;
  z-index: -1;
}

.card .card-image span.card-title {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 16px;
}

.card .card-content {
  padding: 16px 16px 0 16px;
  border-radius: 0 0 2px 2px;
  background-clip: padding-box;
  box-sizing: border-box;
}

.card .card-content p {
  margin: 0;
  color: inherit;
}

.card .card-content li {
  padding: 0 0 10px 0;
  color: inherit;
}

.card .card-content span.card-title {
  line-height: 48px;
}

.card .card-action {
  text-align: center;
  border-top: 1px solid rgba(160, 160, 160, 0.2);
  padding: 16px;
}

.card .card-action a {
  margin-right: 16px;
  transition: color 0.3s ease;
  text-transform: uppercase;
}

.card .card-action a:hover {
  text-decoration: none;
}

.sub-table-background {
  border: 1px solid #ccc;
  background-color: #fff;
  color: #000;
}

.eo-title {
  font-size: 32px;
  padding-top: 15px;
}

.eo-stat {
  font-size: 48px;
  padding-right: 10px;
}

.eo-item {
  margin: 10px 5px 10px 5px;
}

/*Employee Cards*/
.emp-card {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}

.emp-card {
  margin-top: 10px;
  box-sizing: border-box;
  border-radius: 2px;
  background-clip: padding-box;
  margin-left: 10px;
  margin-right: 10px;
  width: auto;
}

.emp-card span.card-title {
  font-size: 20px;
  font-weight: 500;
  text-transform: uppercase;
  border-bottom: 1px solid #ccc;
}

.emp-card .card-image {
  position: relative;
  overflow: hidden;
}

.emp-card .card-image img {
  border-radius: 2px 2px 0 0;
  background-clip: padding-box;
  position: relative;
  z-index: -1;
}

.emp-card .card-image span.card-title {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 16px;
}

.emp-card .card-content {
  padding: 16px;
  border-radius: 0 0 2px 2px;
  background-clip: padding-box;
  box-sizing: border-box;
}

.emp-card .card-info {
  padding: 5px 0 5px 15px;
}

.emp-card .card-info span {
  display: inline-block;
  vertical-align: top;
  width: 100%;
}

.emp-card .card-content p {
  margin: 0;
  color: inherit;
}

.emp-card .card-content span.card-title {
  line-height: 48px;
}

.emp-card .card-action {
  border-top: 1px solid rgba(160, 160, 160, 0.2);
  padding: 16px;
}

.emp-card .card-action a {
  color: #ffab40;
  margin-right: 16px;
  transition: color 0.3s ease;
  text-transform: uppercase;
}

.emp-card .card-action a:hover {
  color: #ffd8a6;
  text-decoration: none;
}

.emp-todo-list li {
  border-bottom: 1px solid #ccc;
  margin: 10px 0 25px 0;
}

.emp-todo-list .emp-todo-item {
  padding: 25px;
}

.emp-todo-list .emp-todo-icon {
  padding-right: 25px;
}

.emp-dash-time-button-div {
  margin-bottom: 15px;
}

.emp-dash-time-button {
  width: 175px;
}

.last-punch {
  padding-left: 10px;
}

.to-section {
  border-bottom: 1px solid #ccc;
  margin: 5px 0 10px 0;
}

.to-item {
  border: 1px solid #ccc;
}

.to-inner {
  padding-bottom: 15px;
}

.to-number {
  font-size: 28px;
  font-weight: 600;
}

.to-info {
  font-size: 24px;
  font-weight: 500;
}

#clock {
  text-align: center;
  height: 100px;
  font-size: 48px;
  padding-top: 15px;
}

#emp-dash-clock {
  text-align: center;
  margin-bottom: 15px;
  font-size: 48px;
}

#emp-dash-date {
  text-align: center;
  font-size: 32px;
}

.time-off-header {
  font-size: 32px;
  font-weight: 600;
  color: #bbb;
}

.time-off-info {
  padding-right: 20px;
}

.time-off-sub-info {
  padding-right: 10px;
  font-size: 32px;
  font-weight: 600;
}

.emp-dash-hrs {
  font-size: 18px;
}

/*Dashboard*/
.dashboard-content {
  padding: 10px;
}

.invoices-footer-content {
  padding-top: 15px;
  color: white;
  font-size: 16px;
}

.invoices-footer-content a {
  color: white;
  text-decoration: underline;
}

.payroll-select {
  padding-bottom: 10px;
}

.exec-select {
  padding-bottom: 10px;
}

.remove-widget {
  cursor: pointer;
}

.gwCharts {
  padding: 10px 0 10px 0;
}

.time-off-dashboard {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
}

.time-off-inner {
  padding: 0 10px 0 10px;
}

.news-dashboard {
  max-height: 450px;
  overflow-y: auto;
  overflow-x: hidden;
}

.news-inner {
  padding: 0 10px 0 10px;
}

#form1 label {
  font-weight: 500 !important;
}

/*izenda*/
.layout .top-nav, ul#topnav ul {
  background-color: #244061;
}

#mainContentDiv input[type=text], #mainContentDiv select {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  margin-left: 5px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

#mainContentDiv input[type=checkbox] {
  margin-left: 5px;
}

#stub-table strong {
  color: #333;
}

#stub-table td {
  padding: 0 5px 0 5px;
}

.stub-payment-info {
  border-top: solid 1px #ccc;
  padding-top: 100px;
}

@media (max-width: 1380px) {
  #griddiv g text {
    font-size: 9px !important;
    padding-left: 3px;
  }
}
.strong-stub td {
  font-weight: 700;
  color: #333;
}

/*Time Sheets*/
.payroll-timesheet-edit .form-control {
  display: block;
  width: auto;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}

.timesheet-highlight {
  border: 3px solid black !important;
}

/*Library*/
.tree-pad {
  padding: 10px 10px 10px 10px;
}

.tree-menu {
  padding: 5px 0 15px 5px;
}

.tree-under {
  border-bottom: 1px solid #ccc;
  width: 95%;
}

.tree-full-border {
  margin-bottom: -1px;
  padding-bottom: 1000px;
  overflow: hidden;
  border-right: 1px solid #ccc;
}

/*Client Reporting*/
.chart-title-italic {
  font-style: italic;
}

.summary-table-amount {
  font-size: 54px;
  font-weight: 700;
}

.summary-table-type {
  font-size: 24px;
}

#EmployeeTableDiv td {
  font-size: 18px;
}

.ts-dates {
  font-size: 18px;
}

/*Image Overlay*/
/*.image-container {
    position: relative;
    cursor: pointer;

}
.image-container .after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    text-align: center;    
    color: #FFF;
}
.image-container:hover .after {
    display: block;
    background: rgba(0, 0, 0, .6);
    border-radius: 50%;
}
.emp-img-edit{
    padding-top: 40px;
}*/
.change-image {
  font-size: 12px;
}

.image-container {
  text-align: center;
}

.company-info-header .label {
  position: absolute;
  top: 9px;
  text-align: center;
  font-size: 12px;
  padding: 3px 4px;
  line-height: 0.9;
}

.calendar-select {
  padding-left: 5px;
}

#team-schedule {
  padding-top: 8px;
}

.treeview span {
  height: 100%;
}

#filteredon-title {
  font-size: 24px;
}

.next-review {
  padding-right: 10px;
}

/*Open Enrollment Wizard*/
.wizard {
  margin: 20px auto;
  background: #fff;
}

.wizard .active {
  color: green !important;
}

.connecting-line {
  height: 2px;
  background: #e0e0e0;
  position: absolute;
  width: 80%;
  margin: 0 auto;
  left: 0;
  right: 0;
  top: 50%;
  z-index: 1;
}

.wizard .nav-tabby {
  position: relative;
  margin: 40px auto;
  margin-bottom: 0;
  border-bottom-color: #e0e0e0;
}

.wizard > div.wizard-inner {
  position: relative;
}

.wizard .nav-tabby > li {
  width: 15%;
}

.wizard .nav-tabby > li:hover {
  width: 15%;
}

.round-step {
  z-index: 2;
  background: #fff;
}

.icon-wizard {
  color: #5bc0de !important;
}

/*open enrollment*/
.radioBtn .notActive {
  color: #3276b1;
  background-color: #fff;
}

.plan-details {
  min-height: 210px;
}

.oe-start {
  border-bottom: 1px solid #ccc;
  margin-bottom: 25px;
}

.detail-size {
  font-size: 16px;
}

.member-hide {
  display: none;
}

.member-show {
  display: block;
}

@media (min-width: 1300px) and (max-width: 1600px) {
  .col-md-3-custom {
    width: 33.33333333%;
  }
}
.wizard-card {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}

.wizard-card {
  margin-top: 10px;
  box-sizing: border-box;
  border-radius: 2px;
  background-clip: padding-box;
  width: auto;
}

.wizard-card span.card-title {
  font-size: 20px;
  font-weight: 500;
  text-transform: uppercase;
  border-bottom: 1px solid #ccc;
}

.wizard-card .card-image {
  position: relative;
  overflow: hidden;
}

.wizard-card .card-image img {
  border-radius: 2px 2px 0 0;
  background-clip: padding-box;
  position: relative;
  z-index: -1;
}

.wizard-card .card-image span.card-title {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 16px;
}

.wizard-card .card-content {
  padding: 16px;
  border-radius: 0 0 2px 2px;
  background-clip: padding-box;
  box-sizing: border-box;
}

.wizard-card .card-info {
  padding: 5px 0 5px 15px;
}

.wizard-card .card-info span {
  display: inline-block;
  vertical-align: top;
  width: 100%;
}

.wizard-card .card-content p {
  margin: 0;
  color: inherit;
}

.wizard-card .card-content span.card-title {
  line-height: 48px;
}

.wizard-card .card-action {
  border-top: 1px solid rgba(160, 160, 160, 0.2);
  padding: 16px;
}

.wizard-card .card-action a {
  color: #ffab40;
  margin-right: 16px;
  transition: color 0.3s ease;
  text-transform: uppercase;
}

.wizard-card .card-action a:hover {
  color: #ffd8a6;
  text-decoration: none;
}

.plan-master {
  overflow-x: auto;
  white-space: nowrap;
}

.plan-nav-block {
  width: 250px;
  float: left;
  margin-left: 10px;
  margin-right: 10px;
}

.plan-block {
  width: 400px;
  display: inline-block;
  padding: 0 5px 0 5px;
}

.plan-name {
  font-size: 22px;
}

.plan-year {
  font-size: 18px;
}

.plan-amount {
  font-size: 36px;
  padding-top: 10px;
}

#PTODisabled label {
  display: block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: 700;
}

.tip-help {
  -ms-writing-mode: tb-rl;
  writing-mode: tb-rl;
  padding-top: 130px;
  padding-right: 15px;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
}

#lifeEventsForm label {
  padding-left: 10px;
}

.accordion-toggle:after {
  font-family: "FontAwesome";
  content: "\f078";
  float: right;
}

.accordion-opened .accordion-toggle:after {
  content: "\f054";
}

.news-title-box {
  border-bottom: 1px solid #ccc;
}

.news-body-box {
  margin-top: 25px;
}

.subject-bold {
  font-weight: 700;
}

.subject-link {
  color: #000;
  text-decoration: none;
}

.subject-link:hover, subject-link:active {
  color: #000;
  text-decoration: none;
}

.input-group.input-group-unstyled input.form-control {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}

.input-group-unstyled .input-group-addon {
  border-radius: 4px;
  border: 0px;
  background-color: transparent;
}

.my-group .form-control {
  width: 50%;
}

.sec-question {
  margin-top: 15px;
  padding-bottom: 25px;
}

.sec-question-answer {
  padding-top: 30px;
}

.real-time-search-container {
  position: relative;
  width: 400px;
}

.real-time-search-inner {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 10;
  max-height: 500px;
  overflow-y: auto;
}

.real-time-search-box {
  margin-bottom: 0;
  background-color: #fff;
}

#realTimeSearchResults li {
  border: 1px solid #ccc;
}

.real-time-search-container .k-item span {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  border-style: solid;
  border-width: 0 0 1px 1px;
  vertical-align: top;
  min-height: 75px;
  width: 62%;
  padding: 0.6em 0 0 0.6em;
  color: #000;
}

.real-time-search-container .k-item span:first-child {
  width: 77px;
  border-left-width: 0;
  padding: 0.6em 0 0 10px;
}

.real-time-search-container img {
  -moz-box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.4);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid #880024;
}

.real-time-search-container h3 {
  font-size: 1.6em;
  margin: 0;
  padding: 0;
}

.real-time-search-container p {
  margin: 0;
  padding: 0;
}

.no-emp-found {
  font-weight: 700;
  padding: 10px;
}

#requestPTO .k-header {
  background-color: #fff;
}

.hide-login-as {
  display: none;
  width: 27.42px;
}

.navbar-nav > .notifications-menu > .dropdown-menu > li.footer-clear > a, .navbar-nav > .messages-menu > .dropdown-menu > li.footer-clear > a, .navbar-nav > .favorites-menu > .dropdown-menu > li.footer-clear > a, .navbar-nav > .tasks-menu > .dropdown-menu > li.footer-clear > a {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  font-size: 12px;
  background-color: #d9534f;
  padding: 7px 10px;
  border-bottom: 1px solid #eeeeee;
  color: #fff !important;
  text-align: center;
}
