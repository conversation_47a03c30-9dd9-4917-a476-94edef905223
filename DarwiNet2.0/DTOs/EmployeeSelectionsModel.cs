using DarwiNet2._0.Controllers;
using System;
using System.Collections.Generic;

namespace DarwiNet2._0.DTOs
{
    public class EmployeeSelectionsModel
    {
        public string EmployeeWorkStatus { get; set; }
        //public int PayrollTeamID { get; set; }
        public bool RefreshAutoListPayRun { get; set; }
        public bool IgnoreManuallyAddedEmployeesOnRefresh { get; set; }
        public IEnumerable<Code_Description_Selected> Worksites { get; set; }
        public IEnumerable<Code_Description_Selected> Departments { get; set; }
        public IEnumerable<Code_Description_Selected> Locations { get; set; }
        public IEnumerable<Code_Description_Selected> EEClasses { get; set; }
        public IEnumerable<Code_Description_Selected> Positions { get; set; }
        public IEnumerable<Code_Description_Selected> PayGroups { get; set; }
        public IEnumerable<Code_Description_Selected> PayCodes { get; set; }
        public IEnumerable<string> SelectedPayFrequencies { get; set; }
    }
}