using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.DTOs
{
    public class SalespersonTableRow
    {
        public int CompanyID { get; set; }
        public string SalespersonID { get; set; }
        public bool Inactive { get; set; }
        public string ScaleID { get; set; }
        public Nullable<int> CommissionMethod { get; set; }
        public Nullable<decimal> CommissionPercent { get; set; }
        public string EmployeeID { get; set; }
        public string VendorID { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string Address3 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Country { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Phone3 { get; set; }
        public string Fax { get; set; }
        public string SalesTerritory { get; set; }
        public string CommissionCode { get; set; }
        public Nullable<decimal> StandardCommissionPercent { get; set; }
        public Nullable<int> CommissionAppliedTo { get; set; }
        public Nullable<decimal> CostToDate { get; set; }
        public Nullable<decimal> CostLastYear { get; set; }
        public Nullable<decimal> TotalCommissionsToDate { get; set; }
        public Nullable<decimal> TotalCommissionsLastYear { get; set; }
        public Nullable<decimal> CommissionedSalesToDate { get; set; }
        public Nullable<decimal> CommissionedSalesLastYear { get; set; }
        public Nullable<decimal> NonCommissionedSalesToDate { get; set; }
        public Nullable<decimal> NonCommissionedSalesLastYear { get; set; }
        public bool KeepCalendarHistory { get; set; }
        public bool KeepPeriodHistory { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<int> CommissionDestination { get; set; }
        public Nullable<int> HoursWorkedDefault { get; set; }
        public bool AssignedToClient { get; set; }
        public bool HasCommissionHistory { get; set; }
    }
}