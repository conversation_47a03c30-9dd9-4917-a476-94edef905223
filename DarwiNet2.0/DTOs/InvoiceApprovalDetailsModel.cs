using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.DTOs
{
    public class InvoiceApprovalDetailsModel
    {
        public string PayrollNumber { get; set; }
        public int InvoiceNumber { get; set; }
        public int InvoiceApprovalSetupSequenceID { get; set; }
        public int? Sequence { get; set; }
        public string ApproverID { get; set; }
        public string ApproverName { get; set; }
        public string ApproverEmail { get; set; }
        public bool Approved { get; set; }
        public bool Rejected { get; set; }
        public string RejectReason { get; set; }
        public DateTime DateSubmittedForApproval { get; set; }
        public DateTime Created { get; set; }
        public DateTime? Updated { get; set; }
        public string Status => Approved ? "Approved" : Rejected ? "Rejected" : "Pending";
        public DateTime? TimeApprovedRejected { get; set; }
        public bool IsInternal { get; set; }
    }
}