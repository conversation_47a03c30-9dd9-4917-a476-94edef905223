using System;

namespace DarwiNet2._0.DTOs
{
    public class SMTPMessage
    {
        public string ToAddress { get; set; }
        public string FromAddress { get; set; }
        public string FromName { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public DateTime InitialSendTimestamp { get; set; }
        public int MaxRetryInterval { get; set; } = 2880;
        public DateTime Expiration { get; set; }
        public SMTPMessage() { }
        public SMTPMessage(string toAddress, string fromAddress, string fromName, string subject, string body, int maxRetryInterval, DateTime expiration)
        {
            ToAddress = toAddress;
            FromAddress = fromAddress;
            FromName = fromName;
            Subject = subject;
            Body = body;
            MaxRetryInterval = maxRetryInterval;
            Expiration = expiration;
            InitialSendTimestamp = DateTime.Now;
        }
    }
}