using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.EnterpriseServices.Internal;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DarwiNet2._0.DTOs
{
    public class ApprovalInformationDTO
    {
        public bool Approved { get; set; }
        public bool Rejected { get; set; }
        public string RejectedReason { get; set; }
        public string Status {  get; set; }
        public string ApprovalUser { get; set; }
        public string Created { get; set; }
        public string DateSubmittedForApproval { get; set; }
        public string ApprovalActionTime { get; set; }
    }
}
