using DarwiNet2._0.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.DTOs
{
    public class CommissionsCalculationTableRowModel
    {
        public int CompanyID { get; set; }
        public string CompanyName { get; set; }
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public int InvoiceNumber { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public string Salesperson { get; set; }
        public decimal? Commissionable { get; set; }
        public decimal? Commission { get; set; }
        public int CommissionMethod { get; set; }
        public string CommissionScale { get; set; }
        public decimal? CommissionPercent { get; set; }
        public bool Employee { get; set; }
        public string EmployeeID { get; set; }
        public int? CommissionDestination { get; set; }
        public List<ClientCommissionTier> CommissionTiers { get; set; }
        public int? HoursWorkedDefault { get; set; }
    }
}