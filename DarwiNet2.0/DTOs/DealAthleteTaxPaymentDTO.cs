using System;

namespace DarwiNet2._0.DTOs
{
    public class DealAthleteTaxPaymentDTO
    {
        public int? CompanyID { get; set; }
        public string ClientID { get; set; }
        public string EmployeeID { get; set; }
        public DealEmployeeDTO Employee { get; set; }
        public long? TaxPaymentID { get; set; }
        public decimal? PaymentAmount { get; set; }
        public DateTime? PaymentDate { get; set; }
        public short? TaxType { get; set; }
        public string TaxEntity { get; set; }
        public string TaxYear { get; set; }
        public short? TaxPeriod { get; set; }
    }
}