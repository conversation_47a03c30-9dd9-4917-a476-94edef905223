using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.DTOs
{
    public class InvoiceCommentDTO
    {
        [MaxLength(50)]
        public string CommentArray1 { get; set; }
        [MaxLength(50)]
        public string CommentArray2 { get; set; }
        [MaxLength(50)]
        public string CommentArray3 { get; set; }
        [MaxLength(50)]
        public string CommentArray4 { get; set; }
        [MaxLength(30)]
        public string Comment1 { get; set; }
        [MaxLength(30)]
        public string Comment2 { get; set; }
    }
}