using DarwiNet2._0.Controllers;
using DarwiNet2._0.Data;


namespace DarwiNet2._0.DTOs
{
    public class EmployeeIdCreationDTO
    {
        public OBClientSetupTask OBClientSetupTask { get; set; }
        public OBNewEE NewEmployee { get; set; }
        public int Profile { get; set; }
        public string Instructions { get; set; }
        public string InstructionDoc { get; set; }
        public string TipText { get; set; }
        public string TipImage { get; set; }
        public bool IsUserAlreadyExists { get; set; }
        public string LoginCode { get; set; }


    }
}