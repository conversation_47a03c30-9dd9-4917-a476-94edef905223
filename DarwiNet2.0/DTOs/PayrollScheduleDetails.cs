using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Permissions;
using System.Web;

namespace DarwiNet2._0.DTOs
{
    public class PayrollScheduleDetails
    {
        public PayrollScheduleOrigin ScheduleOrigin =>
            HasAdjustedDate ? PayrollScheduleOrigin.Conflict : PayrollScheduleOrigin.AutoSchedule;

        public DateTime InitialProcessDate { get; set; }
        public DateTime? AdjustedProcessDate { get; set; }
        public DateTime ProcessDate => AdjustedProcessDate ?? InitialProcessDate;
        //public DateTime? ProcessTime { get; set; }

        public DateTime InitialCheckDate { get; set; }
        public DateTime? AdjustedCheckDate { get; set; }
        public DateTime CheckDate => AdjustedCheckDate ?? InitialCheckDate;

        public DateTime InitialPayPeriodBeginDate { get; set; }
        public DateTime? AdjustedPayPeriodBeginDate { get; set; }
        public DateTime PayPeriodBeginDate => AdjustedPayPeriodBeginDate ?? InitialPayPeriodBeginDate;

        public DateTime InitialPayPeriodEndDate { get; set; }
        public DateTime? AdjustedPayPeriodEndDate { get; set; }
        public DateTime PayPeriodEndDate => AdjustedPayPeriodEndDate ?? InitialPayPeriodEndDate;

        public DateTime? InitialSendTimesheetDate { get; set; }
        public DateTime? AdjustedSendTimesheetDate { get; set; }
        public DateTime? SendTimesheetDate => AdjustedSendTimesheetDate ?? InitialSendTimesheetDate;
        //public DateTime? SendTimeSheetTime { get; set; }

        public DateTime? InitialReceiveTimesheetDate { get; set; }
        public DateTime? AdjustedReceiveTimesheetDate { get; set; }
        public DateTime? ReceiveTimesheetDate => AdjustedReceiveTimesheetDate ?? InitialReceiveTimesheetDate;
        //public DateTime? ReceiveTimeSheetTime { get; set; }

        public DateTime InitialShipDate { get; set; }
        public DateTime? AdjustedShipDate { get; set; }
        public DateTime ShipDate => AdjustedShipDate ?? InitialShipDate;
        //public DateTime? ShipTime { get; set; }
        public bool Inactive { get; set; }

        private bool HasAdjustedDate =>
            AdjustedProcessDate != null ||
                AdjustedCheckDate != null ||
                AdjustedPayPeriodBeginDate != null ||
                AdjustedPayPeriodEndDate != null ||
                AdjustedSendTimesheetDate != null ||
                AdjustedReceiveTimesheetDate != null ||
                AdjustedShipDate != null;
    }
}