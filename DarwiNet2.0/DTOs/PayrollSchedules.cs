using DarwiNet2._0.Controllers;
using DarwiNet2._0.Enumerations;
using DarwiNet2._0.Factories;
using DarwiNet2._0.Data;
using DataDrivenViewEngine.Models.Core;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using Thinkware.Pay360.Payroll;
using DarwiNet2._0.Core;
using Thinkware.UI.Vue;

namespace DarwiNet2._0.DTOs
{
    public class PayrollSchedule
    {
        public PayrollSchedule(ClientPayrollSchedule schedule, PayrollProfileSetting settings)
        {
            ClientId = schedule.ClientID;
            ClientName = schedule.ClientName;
            CompanyId = schedule.CompanyID;
            CompanyName = schedule.CompanyName;
            DivisionId = schedule.Division_ID;
            PayrollNumber = schedule.PayrollNumber;
            Status = (PayrollStatus)schedule.Schedule_Status;
            ProfileId = schedule.ProfileID;
            PayPeriodBeginDate = schedule.PayPeriod_BeginDate;
            PayPeriodEndDate = schedule.PayPeriod_EndDate;
            CheckDate = schedule.CheckDate;
            ScheduleId = schedule.Schedule_ID;
            AutoPayOptions = new Dictionary<string, bool>()
            {
                { Constants.AutoPayOptions.AUTO_PROCESS_INVOICE, settings.AutoInvoiceCalc },
                { Constants.AutoPayOptions.AUTO_PROCESS_FINALIZE, settings.AutoFinalize },
                { Constants.AutoPayOptions.AUTO_PROCESS_PAYROLL, settings.AutoPayrollCalc },
                { Constants.AutoPayOptions.BYPASS_INVOICE, settings.BypassInvoice },
                //{ Constants.AutoPayOptions.FINALIZE_APPROVAL_REQUIRED, false },
                { Constants.AutoPayOptions.INVOICE_APPROVAL_REQUIRED, settings.ApproveInvoice },
                { Constants.AutoPayOptions.PAYROLL_APPROVAL_REQUIRED, settings.ApprovePayroll },
                { Constants.AutoPayOptions.STOP_ON_WARNINGS, settings.StopOnWarnings },
                { Constants.AutoPayOptions.STOP_ON_BUILD, settings.StopOnBuild },
            };
        }

        public IDictionary<string, bool> AutoPayOptions { get; }
        public string ClientId { get; }
        public string ClientName { get; }
        public int CompanyId { get; }
        public string CompanyName { get; }
        public string DivisionId { get; }
        public string PayrollNumber { get; }
        public string SnapshotId { get; }
        public string ScheduleId { get; }
        public PayrollStatus Status { get; }
        public string ProfileId { get; }
        public DateTime? PayPeriodBeginDate { get; }
        public DateTime? PayPeriodEndDate { get; }
        public DateTime? CheckDate { get; }
    }

    public class PayrollScheduleJsonModel
    {
        public int CompanyId { get; set; }
        public string ClientId { get; set; }
        public string ProfileId { get; set; }
        public string ScheduleId { get; set; }
        public ScheduleIdsJsonModel ScheduleIds { get; set; }
        public class ScheduleIdsJsonModel
        {
            public VueSelectList<string> Items { get; set; }
            public string Value { get; set; }
        }
        public int ScheduleKey { get; set; }
        public string PayrollNumber { get; set; }
        public PayFrequencyJsonModel PayFrequency { get; set; } = new PayFrequencyJsonModel();
        public class PayFrequencyJsonModel
        {
            public VueSelectList<int> Items { get; set; }
            public int Value { get; set; }
        }
        public ResponsibleJsonModel Responsible { get; set; } = new ResponsibleJsonModel();
        public class ResponsibleJsonModel
        {
            public VueSelectList<string> Items { get; set; }
            public string PayrollUserId { get; set; }
            public string InvoiceUserId { get; set; }
            public string FinalizeUserId { get; set; }
        }
        public NumberOfYearsJsonModel NumberOfYears { get; set; } = new NumberOfYearsJsonModel();
        public class NumberOfYearsJsonModel
        {
            public VueSelectList<int> Items { get; set; }
            public int Value { get; set; }
        }
        public ShippingMethodJsonModel ShippingMethod { get; set; } = new ShippingMethodJsonModel();
        public class ShippingMethodJsonModel
        {
            public VueSelectList<string> Items { get; set; }
            public string Value { get; set; }
        }
        public string Description { get; set; }
        public string Comment { get; set; }
        public string HolidayRule { get; set; }
        public string WeekendRule { get; set; }
        public bool Inactive { get; set; }
        public DateDetailsTemplate DetailsTemplate { get; set; } = new DateDetailsTemplate();
        public DateDetailsTemplate SecondPayPeriodDetailsTemplate { get; set; } = new DateDetailsTemplate();
        public class DateDetailsTemplate
        {
            public string InitialCheckDate { get; set; }
            public string InitialPayPeriodBeginDate { get; set; }
            public string InitialPayPeriodEndDate { get; set; }
            public string InitialProcessDate { get; set; }
            public string InitialReceiveTimesheetDate { get; set; }
            public string InitialSendTimesheetDate { get; set; }
            public string InitialShipDate { get; set; }
        }

        public PayrollScheduleJsonModel()
        {

        }
        public PayrollScheduleJsonModel(ClientPayrollSchedule schedule, VueSelectList<string> scheduleIdsItems = null, VueSelectList<int> payFrequencyItems = null, VueSelectList<string> responsibleItems = null, VueSelectList<int> numberOfYearsItems = null, VueSelectList<string> shippingMethodItems = null)
        {
            CompanyId = schedule.CompanyID;
            ClientId = schedule.ClientID;
            ProfileId = schedule.ProfileID;
            ScheduleId = schedule.Schedule_ID;
            ScheduleKey = schedule.Schedule_Key;
            ScheduleIds = new ScheduleIdsJsonModel()
            {
                Items = scheduleIdsItems,
                Value = schedule.Schedule_ID ?? string.Empty
            };
            PayFrequency = new PayFrequencyJsonModel()
            {
                Items = payFrequencyItems,
                Value = schedule.Frequency ?? (int)PaySchedulePayPeriods.Weekly
            };
            Responsible = new ResponsibleJsonModel()
            {
                Items = responsibleItems,
                PayrollUserId = schedule.Responsible ?? string.Empty,
                InvoiceUserId = schedule.InvoiceProcessAssigned ?? schedule.Responsible ?? string.Empty,
                FinalizeUserId = schedule.FinalizeAssigned ?? schedule.Responsible ?? string.Empty
            };
            NumberOfYears = new NumberOfYearsJsonModel()
            {
                Items = numberOfYearsItems,
                Value = numberOfYearsItems?[0].value ?? 1
            };
            ShippingMethod = new ShippingMethodJsonModel()
            {
                Items = shippingMethodItems,
                Value = schedule.ShippingMethod ?? string.Empty
            };
            Description = schedule.Description ?? string.Empty;
            Comment = schedule.Comment ?? string.Empty;
            HolidayRule = string.Empty;
            WeekendRule = string.Empty;
            Inactive = schedule.Inactive;
            DetailsTemplate = new DateDetailsTemplate()
            {
                InitialCheckDate = schedule.CheckDate.ToVCalendarDateFormat(),
                InitialPayPeriodBeginDate = schedule.PayPeriod_BeginDate.ToVCalendarDateFormat(),
                InitialPayPeriodEndDate = schedule.PayPeriod_EndDate.ToVCalendarDateFormat(),
                InitialProcessDate = schedule.Process_Date.ToVCalendarDateFormat(),
                InitialReceiveTimesheetDate = schedule.Receive_TS_Date.ToVCalendarDateFormat(),
                InitialSendTimesheetDate = schedule.Send_TS_Date.ToVCalendarDateFormat(),
                InitialShipDate = schedule.Ship_Date.ToVCalendarDateFormat()
            };
            SecondPayPeriodDetailsTemplate = new DateDetailsTemplate()
            {
                InitialCheckDate = schedule.CheckDate.ToVCalendarDateFormat(),
                InitialPayPeriodBeginDate = schedule.PayPeriod_BeginDate.ToVCalendarDateFormat(),
                InitialPayPeriodEndDate = schedule.PayPeriod_EndDate.ToVCalendarDateFormat(),
                InitialProcessDate = schedule.Process_Date.ToVCalendarDateFormat(),
                InitialReceiveTimesheetDate = schedule.Receive_TS_Date.ToVCalendarDateFormat(),
                InitialSendTimesheetDate = schedule.Send_TS_Date.ToVCalendarDateFormat(),
                InitialShipDate = schedule.Ship_Date.ToVCalendarDateFormat()
            };
        }
    }

    public class PayrollSchedules
    {
        public string ScheduleId { get; set; }
        public int CompanyId { get; set; }
        public string ClientId { get; set; }
        public string DivisionId { get; set; }
        public string ProfileId { get; set; }
        public byte Frequency { get; set; }
        public int NumberOfYears { get; set; }
        public string Responsible { get; set; }
        public string Comment { get; set; }
        public string ShippingMethod { get; set; }
        public string Description { get; set; }
        public DateTime ScheduleStartDate { get; set; } = DateTime.Now;
        public DateTime ScheduleEndDate => ScheduleStartDate.AddYears(NumberOfYears).AddMonths(1);
        public string HolidayRule { get; set; }
        public PayrollScheduleRule HolidayRuleValue => Rule(HolidayRule);
        public string WeekendRule { get; set; }
        public PayrollScheduleRule WeekendRuleValue => Rule(WeekendRule);
        public List<DateTime> Holidays { get; set; }
        public List<DayOfWeek> WorkDays { get; set; }
        public PayrollScheduleDetails DetailsTemplate { get; set; }
        public PayrollScheduleDetails SecondPayPeriodDetailsTemplate { get; set; }
        public List<PayrollScheduleDetails> Details { get; set; }
        public string InvoiceAssignedUserId { get; set; }
        public string FinalizeAssignedUserId { get; set; }

        #region Public Functions
        /// <summary>
        /// Generates all future schedules based off the number of years and frequency
        /// </summary>
        /// <history>
        ///     [dkocher]   07/15/20    Task-6347: Add logic to create new payroll schedule and future schedules
        /// </history>
        public void GenerateSchedules()
        {
            var schedules =
                PayrollScheduleGeneratorFactory.
                    Generate(
                        frequency: Frequency,
                        numberOfYears: NumberOfYears,
                        templateScheduleDetails: DetailsTemplate,
                        holidays: Holidays,
                        workSchedule: WorkDays,
                        holidayRule: HolidayRuleValue,
                        weekendRule: WeekendRuleValue,
                        secondPayrollTemplateScheduleDetails: SecondPayPeriodDetailsTemplate
                    );

            Details = schedules;
        }
        #endregion

        public static PayrollScheduleRule Rule(string rule)
        {
            switch (rule)
            {
                case "FirstDayBefore":
                    return PayrollScheduleRule.FirstDayBefore;
                case "FirstDayAfter":
                    return PayrollScheduleRule.FirstDayAfter;
                default:
                    return PayrollScheduleRule.None;
            }
        }
    }
}