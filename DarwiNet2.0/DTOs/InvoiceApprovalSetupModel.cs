using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DarwiNet2._0.DTOs
{
    public class InvoiceApprovalSetupModel
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string ProfileID { get; set; }
        public string ApprovalType { get; set; }        // ApproveInvoice, etc
        public int InvoiceApprovalSetupID { get; set; }
        public bool InternalApproval { get; set; }
        public string ApprovalModel { get; set; }       // Parallel, Sequential, Group Sequential
        public string EmailMessage { get; set; }
        public string SMSMessage { get; set; }
        public bool NotifyOnRejection { get; set; }
        public bool ApprovalExpiration { get; set; }
        public int ApprovalExpirationHours { get; set; }
        public string AutoApprovalAction { get; set; }
        public bool CreateAlert { get; set; }
        public bool CreateInvoiceExpiredAlert { get; set; }
        public IList<InvoiceApprovalRecipientModel> InvoiceApprovalRecipients { get; set; }
        public IList<InvoiceApprovalRecipientModel> InvoiceApprovalRecipientGroups { get; set; }
        public IList<InvoiceApprovalSetupSequenceModel> InvoiceApprovalSetupSequences { get; set; }
        public List<MultiselectOptionsDTO> PayrollApprovalTeams { get; set; }
    }
}