//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class vw_Employees
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string EmployeeID { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public string SSN { get; set; }
        public Nullable<System.DateTime> BirthDate { get; set; }
        public string HomeDepartment { get; set; }
        public string Position { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public string SUTAState { get; set; }
        public string WorkersComp { get; set; }
        public string UDF1 { get; set; }
        public string UDF2 { get; set; }
        public string EmployeeClass { get; set; }
        public Nullable<System.DateTime> DateEmployeeInactivated { get; set; }
        public string ReasonEmployeeInactivated { get; set; }
        public Nullable<System.DateTime> OriginalHireDate { get; set; }
        public Nullable<int> WorkHoursPerYear { get; set; }
        public string EthnicOrigin { get; set; }
        public string Gender { get; set; }
        public string WorkStatus { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string Address3 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string County { get; set; }
        public string Country { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string DepartmentDescription { get; set; }
        public string PositionDescription { get; set; }
        public string InActive { get; set; }
        public string SupervisorID { get; set; }
        public string SupervisorFirstName { get; set; }
        public string SupervisorMiddleName { get; set; }
        public string SupervisorLastName { get; set; }
    }
}
