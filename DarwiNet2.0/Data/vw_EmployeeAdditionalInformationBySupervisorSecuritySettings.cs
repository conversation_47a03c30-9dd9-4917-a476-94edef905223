//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class vw_EmployeeAdditionalInformationBySupervisorSecuritySettings
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeEmail { get; set; }
        public string NickName { get; set; }
        public string AlternateName { get; set; }
        public string EmergencyContact { get; set; }
        public string EmergencyPhone { get; set; }
        public string MaritalStatus { get; set; }
        public string JobCategory { get; set; }
        public string Citizenship { get; set; }
        public string LicenseState { get; set; }
        public string DriversLicense { get; set; }
        public string VisaType { get; set; }
        public Nullable<System.DateTime> VisaExpirationDate { get; set; }
        public bool I9Verified { get; set; }
        public Nullable<System.DateTime> I9ReverificationDue { get; set; }
        public bool HighCompensation { get; set; }
        public string HighCompensationDescription { get; set; }
        public Nullable<System.DateTime> DateOfNextReview { get; set; }
        public Nullable<System.DateTime> SeniorityDate { get; set; }
        public Nullable<System.DateTime> C401KHireDate { get; set; }
        public Nullable<System.DateTime> TerminationDate { get; set; }
        public Nullable<System.DateTime> RehireDate { get; set; }
        public Nullable<System.DateTime> RetirementDate { get; set; }
        public Nullable<System.DateTime> DeathDate { get; set; }
        public string MilitaryServiceCode { get; set; }
        public string HandicappedCode { get; set; }
        public bool DisabledVeteran { get; set; }
        public bool Veteran { get; set; }
        public bool Smoker { get; set; }
        public bool IndependentContractor { get; set; }
        public bool CorporateOfficer { get; set; }
        public string HomeEmail { get; set; }
        public string AdditionalInfoEmail { get; set; }
        public string DefaultEmailAddress { get; set; }
        public string DarwinetEmail { get; set; }
    }
}
