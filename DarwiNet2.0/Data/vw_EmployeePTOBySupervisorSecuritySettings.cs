//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class vw_EmployeePTOBySupervisorSecuritySettings
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string EmployeeID { get; set; }
        public string PTOType { get; set; }
        public string PTOName { get; set; }
        public Nullable<decimal> YTDAccrued { get; set; }
        public Nullable<decimal> Available { get; set; }
        public Nullable<decimal> Taken { get; set; }
        public string InActive { get; set; }
        public string AnniversaryBasedOn { get; set; }
        public Nullable<System.DateTime> LastAnniversaryDate { get; set; }
        public string AccrualMethod { get; set; }
        public Nullable<decimal> PTOHoursPerYear { get; set; }
        public Nullable<byte> MaxAccrualBasedOn { get; set; }
        public Nullable<decimal> MaxAccrualHrsYTD { get; set; }
        public Nullable<decimal> MaxAccrualHrsLTD { get; set; }
        public Nullable<int> WaitingPeriodToBeginAccrual { get; set; }
        public Nullable<System.DateTime> DateToBeginAccrual { get; set; }
        public Nullable<int> WaitingPeriodToBeginUse { get; set; }
        public Nullable<System.DateTime> DateToBeginUse { get; set; }
        public bool CarryOver { get; set; }
        public Nullable<decimal> MaxHoursToCarryOver { get; set; }
        public Nullable<decimal> LastAnniversaryCarryOverAmount { get; set; }
    }
}
