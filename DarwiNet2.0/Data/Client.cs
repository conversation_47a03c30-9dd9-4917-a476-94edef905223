//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class Client
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Client()
        {
            this.CheckPrinterDatas = new HashSet<CheckPrinterData>();
            this.Client401kPlans = new HashSet<Client401kPlans>();
            this.ClientACACodeSelections = new HashSet<ClientACACodeSelection>();
            this.ClientACAPlanLevels = new HashSet<ClientACAPlanLevel>();
            this.ClientACAShareLevels = new HashSet<ClientACAShareLevel>();
            this.ClientAddresses = new HashSet<ClientAddress>();
            this.ClientAssignments = new HashSet<ClientAssignment>();
            this.ClientBusinessExpenses = new HashSet<ClientBusinessExpens>();
            this.ClientCharges = new HashSet<ClientCharge>();
            this.ClientCodeDescriptions = new HashSet<ClientCodeDescription>();
            this.ClientCompositeBillingSetups = new HashSet<ClientCompositeBillingSetup>();
            this.ClientContacts = new HashSet<ClientContact>();
            this.ClientContactTypes = new HashSet<ClientContactType>();
            this.ClientDivisions = new HashSet<ClientDivision>();
            this.ClientImportCodeMappings = new HashSet<ClientImportCodeMapping>();
            this.ClientInsurancePlans = new HashSet<ClientInsurancePlan>();
            this.ClientInvoiceCertifiedPayrollDetails = new HashSet<ClientInvoiceCertifiedPayrollDetail>();
            this.ClientInvoiceDefaults = new HashSet<ClientInvoiceDefault>();
            this.ClientJBSCodes = new HashSet<ClientJBSCode>();
            this.ClientJobCostAssignments = new HashSet<ClientJobCostAssignment>();
            this.ClientManagers = new HashSet<ClientManager>();
            this.ClientPayrollSchedules = new HashSet<ClientPayrollSchedule>();
            this.ClientPostings = new HashSet<ClientPosting>();
            this.ClientPTOTypes = new HashSet<ClientPTOType>();
            this.ClientReviewActions = new HashSet<ClientReviewAction>();
            this.ClientReviewRatings = new HashSet<ClientReviewRating>();
            this.ClientReviewTypes = new HashSet<ClientReviewType>();
            this.ClientSicktimeTiers = new HashSet<ClientSicktimeTier>();
            this.ClientTimePunchSecurities = new HashSet<ClientTimePunchSecurity>();
            this.ClientTrainingRatings = new HashSet<ClientTrainingRating>();
            this.ClientTrainingStatuses = new HashSet<ClientTrainingStatus>();
            this.ClientTrainingTypes = new HashSet<ClientTrainingType>();
            this.ClientUnemploymentSetups = new HashSet<ClientUnemploymentSetup>();
            this.ClientVacationTiers = new HashSet<ClientVacationTier>();
            this.ClientWorkersCompPlans = new HashSet<ClientWorkersCompPlan>();
            this.InvoiceASOAccountSetupHistories = new HashSet<InvoiceASOAccountSetupHistory>();
            this.InvoiceASOAccountSetupHistoryOriginals = new HashSet<InvoiceASOAccountSetupHistoryOriginal>();
            this.InvoiceASOClientPostHistories = new HashSet<InvoiceASOClientPostHistory>();
            this.InvoiceASOClientPostHistoryOriginals = new HashSet<InvoiceASOClientPostHistoryOriginal>();
            this.Invoices = new HashSet<Invoice>();
            this.InvoicesOriginals = new HashSet<InvoicesOriginal>();
            this.NotificationClients = new HashSet<NotificationClient>();
            this.ClientACHHeaders = new HashSet<ClientACHHeader>();
        }
    
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public string ClientClass { get; set; }
        public string CorporateClientID { get; set; }
        public string ContactPerson { get; set; }
        public string StatementName { get; set; }
        public string ShortName { get; set; }
        public string AddressCode { get; set; }
        public string PrimaryBilltoAddressCode { get; set; }
        public string PrimaryShiptoAddressCode { get; set; }
        public string StatementAddressCode { get; set; }
        public string SalesPersonID { get; set; }
        public string CheckbookID { get; set; }
        public string PaymentTermsID { get; set; }
        public Nullable<byte> CreditLimitType { get; set; }
        public Nullable<decimal> CreditLimitAmount { get; set; }
        public Nullable<byte> CreditLimitPeriod { get; set; }
        public Nullable<decimal> CreditLimitPeriodAmount { get; set; }
        public string CurrencyID { get; set; }
        public string RateTypeID { get; set; }
        public Nullable<int> ClientDiscount { get; set; }
        public string PriceLevel { get; set; }
        public Nullable<byte> MinimumPaymentType { get; set; }
        public Nullable<decimal> MinimumPaymentDollar { get; set; }
        public Nullable<int> MinimumPaymentPercent { get; set; }
        public Nullable<byte> FinanceChargeAmtType { get; set; }
        public Nullable<int> FinanceChargePercent { get; set; }
        public Nullable<decimal> FinanceChargeDollar { get; set; }
        public Nullable<byte> MaxWriteoffType { get; set; }
        public Nullable<decimal> MaxWriteoffAmount { get; set; }
        public string Comment1 { get; set; }
        public string Comment2 { get; set; }
        public string UserDefined1 { get; set; }
        public string UserDefined2 { get; set; }
        public string TaxExempt1 { get; set; }
        public string TaxExempt2 { get; set; }
        public string TaxRegistrationNumber { get; set; }
        public Nullable<byte> BalanceType { get; set; }
        public Nullable<byte> StatementCycle { get; set; }
        public string BankName { get; set; }
        public string BankBranch { get; set; }
        public string SalesTerritory { get; set; }
        public Nullable<byte> DefaultCashAccountType { get; set; }
        public Nullable<int> CashAccountIndex { get; set; }
        public Nullable<int> ARAccountIndex { get; set; }
        public Nullable<int> SalesAccountIndex { get; set; }
        public Nullable<int> IVAccountIndex { get; set; }
        public Nullable<int> CostOfSalesAccountIndex { get; set; }
        public Nullable<int> DiscountsTakenAccountIndex { get; set; }
        public Nullable<int> DiscountsAvailAccountIndex { get; set; }
        public Nullable<int> FinanceChargeAccountIndex { get; set; }
        public Nullable<int> WriteoffAccountIndex { get; set; }
        public Nullable<int> SalesOrderReturnsAccountIndex { get; set; }
        public Nullable<System.DateTime> FirstInvoiceDate { get; set; }
        public bool Inactive { get; set; }
        public bool Hold { get; set; }
        public string CreditCardID { get; set; }
        public string CreditCardNumber { get; set; }
        public Nullable<System.DateTime> CreditCardExpDate { get; set; }
        public bool KeepDistributionHistory { get; set; }
        public bool KeepCalendarHistory { get; set; }
        public bool KeepPeriodHistory { get; set; }
        public bool KeepTrxHistory { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<System.DateTime> ModifiedDate { get; set; }
        public Nullable<byte> RevalueClient { get; set; }
        public Nullable<byte> PostResultsTo { get; set; }
        public string FinanceChargeID { get; set; }
        public string GovernmentalCorporateID { get; set; }
        public string GovernmentalIndividualID { get; set; }
        public Nullable<int> DiscountGracePeriod { get; set; }
        public Nullable<int> DueDateGracePeriod { get; set; }
        public string DocumentFormatID { get; set; }
        public bool SendEmailStatements { get; set; }
        public Nullable<int> UserLanguageID { get; set; }
        public string GPSFOIntegrationID { get; set; }
        public Nullable<int> IntegrationSource { get; set; }
        public string IntegrationID { get; set; }
        public Nullable<byte> OrderShortageDefault { get; set; }
        public Nullable<byte> ClientPriority { get; set; }
        public string CountryCode { get; set; }
        public string DeclarantID { get; set; }
        public Nullable<int> OverpayWriteoffAccntIndex { get; set; }
        public bool ShipCompleteDocument { get; set; }
        public bool CashBasedVAT { get; set; }
        public bool IncludeInDemandPlanning { get; set; }
        public string RegionID { get; set; }
        public bool AutomaticRecapture { get; set; }
        public Nullable<byte> RecaptureOptions { get; set; }
        public Nullable<int> VacationAccrualCeiling { get; set; }
        public Nullable<int> SickTimeAccrualCeiling { get; set; }
        public Nullable<byte> VacationAnniversaryMethod { get; set; }
        public Nullable<byte> SickTimeAnniversaryMethod { get; set; }
        public Nullable<int> YTDAccrualHoursSickTime { get; set; }
        public Nullable<int> YTDAccrualHoursVacation { get; set; }
        public string RegionDescription { get; set; }
        public bool AutoAccrueVacation { get; set; }
        public bool AutoAccrueSickTime { get; set; }
        public Nullable<int> VacationAccrualAmount { get; set; }
        public Nullable<byte> VacationAccrualMethod { get; set; }
        public Nullable<int> VacationAvailable { get; set; }
        public Nullable<int> VacationHoursPerYear { get; set; }
        public Nullable<int> SickTimeAccrualAmount { get; set; }
        public Nullable<byte> SickTimeAccrualMethod { get; set; }
        public Nullable<int> SickTimeAvailable { get; set; }
        public Nullable<int> SickTimeHoursPerYear { get; set; }
        public bool VacationYearEndCarryOver { get; set; }
        public bool SickTimeYearEndCarryOver { get; set; }
        public bool UseSickTimeTiers { get; set; }
        public bool UseVacationTiers { get; set; }
        public bool Section125 { get; set; }
        public string AdminCode1 { get; set; }
        public string AdminCode2 { get; set; }
        public string AdminCode3 { get; set; }
        public Nullable<byte> AgencyRealTime { get; set; }
        public string Deduction { get; set; }
        public bool NoInvoice { get; set; }
        public bool GenerateCertified { get; set; }
        public bool PassFICA { get; set; }
        public bool PassFUTA { get; set; }
        public bool PassSUTA { get; set; }
        public bool PassWC { get; set; }
        public Nullable<int> ExperienceModifier { get; set; }
        public Nullable<int> SUTAFactor { get; set; }
        public Nullable<decimal> ReinsuranceFee { get; set; }
        public string GrossAdjustmentCode { get; set; }
        public string TipsCode { get; set; }
        public Nullable<byte> GrossMinimum { get; set; }
        public Nullable<int> OTFactor { get; set; }
        public string EmployerStateIDNumber { get; set; }
        public Nullable<byte> ClientWC { get; set; }
        public Nullable<byte> PayrollCheckSortOption { get; set; }
        public bool PayrollUseClientCheckPEOName { get; set; }
        public string PayrollCheckPEOName { get; set; }
        public bool PayrollUseClientCheckFile { get; set; }
        public string PayrollCheckFile { get; set; }
        public bool PayrollACHEnabled { get; set; }
        public bool PayrollPrintDepositReport { get; set; }
        public Nullable<int> PayrollOffsetDays { get; set; }
        public bool PayrollUseClientOffsetDays { get; set; }
        public Nullable<byte> ClentContributionsType { get; set; }
        public Nullable<decimal> Amount { get; set; }
        public Nullable<int> PCT { get; set; }
        public Nullable<int> SICCode { get; set; }
        public Nullable<System.DateTime> ExportDate { get; set; }
        public Nullable<System.DateTime> ExportTime { get; set; }
        public Nullable<System.DateTime> ImportDate { get; set; }
        public Nullable<System.DateTime> ImportTime { get; set; }
        public string OutstandingBalanceCode { get; set; }
        public bool UseJobCosting { get; set; }
        public Nullable<byte> ClientFrequency { get; set; }
        public bool UseCompanySetupInfo { get; set; }
        public bool UseClientCheckbookInfo { get; set; }
        public string ClientCheckbookID { get; set; }
        public string DepositCheckbook { get; set; }
        public Nullable<System.DateTime> CheckDate { get; set; }
        public Nullable<System.DateTime> PayRunStartDate { get; set; }
        public Nullable<System.DateTime> PayRunEndDate { get; set; }
        public bool AutomateAgencyChecks { get; set; }
        public Nullable<byte> PayablesCheckType { get; set; }
        public string PayablesCheckbookID { get; set; }
        public bool Prepare941ScheduleB { get; set; }
        public bool PayCodeSplit { get; set; }
        public bool UseDedCredits { get; set; }
        public bool UseBenCredits { get; set; }
        public Nullable<int> NewHireEnrollmentPeriod { get; set; }
        public Nullable<byte> TransporterInterface { get; set; }
        public string ControlCode { get; set; }
        public string ControlCodeName { get; set; }
        public bool Terminated { get; set; }
        public bool SuppressInvoice { get; set; }
        public bool EditPTOTaken { get; set; }
        public string WaitingPeriod401kDays { get; set; }
        public Nullable<byte> WaitingPeriod401kRule { get; set; }
        public bool ShowInactiveCodes { get; set; }
        public bool IncludeInBlendedRate { get; set; }
        public Nullable<byte> UseStraightRateForMultiComp { get; set; }
        public bool UseArray1 { get; set; }
        public bool UseArray2 { get; set; }
        public bool UseArray3 { get; set; }
        public bool UseArray4 { get; set; }
        public bool UseArray5 { get; set; }
        public bool UseArray6 { get; set; }
        public string JobLevel2 { get; set; }
        public string JobLevel3 { get; set; }
        public string JobLevel4 { get; set; }
        public string JobLevel5 { get; set; }
        public string JobLevel6 { get; set; }
        public Nullable<byte> JobCostingMode { get; set; }
        public bool PreviewInvoice { get; set; }
        public bool ProrateBenefitDivisionInvoice { get; set; }
        public bool ProrateAgencyCreditDivInvoice { get; set; }
        public Nullable<System.DateTime> ClientStartDate { get; set; }
        public Nullable<System.DateTime> ClientTerminationDate { get; set; }
        public bool SuppressBuildWarning { get; set; }
        public bool SendInvoicePreviewToDarwinet { get; set; }
        public Nullable<byte> DarwinetPreviewOptions { get; set; }
        public Nullable<byte> OverrideBenDedDateSelect { get; set; }
        public Nullable<byte> BenefitDeductionDateType { get; set; }
        public bool AutoCalculate { get; set; }
        public bool NeverCalculate { get; set; }
        public bool BypassARPostWindow { get; set; }
        public bool UseInvoiceComponentBilling { get; set; }
        public bool PrintShippingInvoice { get; set; }
        public Nullable<byte> AdditionalFUTABillingOption { get; set; }
        public bool AutoPostInvoice { get; set; }
        public string InvPrevEmailMsg1 { get; set; }
        public string InvPrevEmailMsg2 { get; set; }
        public bool PrintCurrentPTOTotals { get; set; }
        public Nullable<byte> DnetUploadMethod { get; set; }
        public Nullable<byte> DnetFileMethod { get; set; }
        public Nullable<byte> DnetRefreshData { get; set; }
        public Nullable<int> UploadFreqType { get; set; }
        public Nullable<int> UploadFreqInterval { get; set; }
        public Nullable<int> UploadFreqSubtype { get; set; }
        public Nullable<int> UploadFreqSubInterval { get; set; }
        public Nullable<int> UploadFreqRelInterval { get; set; }
        public Nullable<int> UploadFreqRecurInterval { get; set; }
        public Nullable<System.DateTime> UploadStartDate { get; set; }
        public Nullable<System.DateTime> UploadStartTime { get; set; }
        public Nullable<System.DateTime> UploadEndTime { get; set; }
        public Nullable<int> SyncFreqType { get; set; }
        public Nullable<int> SyncFreqInterval { get; set; }
        public Nullable<int> SyncFreqSubtype { get; set; }
        public Nullable<int> SyncFreqSubInterval { get; set; }
        public Nullable<int> SyncFreqRelInterval { get; set; }
        public Nullable<int> SyncFreqRecurInterval { get; set; }
        public Nullable<System.DateTime> SyncStartDate { get; set; }
        public Nullable<System.DateTime> SyncStartTime { get; set; }
        public Nullable<System.DateTime> SyncEndTime { get; set; }
        public string SAICCOCode { get; set; }
        public bool EnableSchedUpload { get; set; }
        public bool AutoMasterPost { get; set; }
        public Nullable<byte> CheckDateMode { get; set; }
        public bool MaskEmployeeSSN { get; set; }
        public Nullable<decimal> ActiveEmployeeFTChargeAmt1 { get; set; }
        public Nullable<decimal> ActiveEmployeeFTChargeAmt2 { get; set; }
        public Nullable<decimal> ActiveEmployeeFTChargeAmt3 { get; set; }
        public Nullable<decimal> ActiveEmployeeFTChargeAmt4 { get; set; }
        public Nullable<decimal> ActiveEmployeePTChargeAmt1 { get; set; }
        public Nullable<decimal> ActiveEmployeePTChargeAmt2 { get; set; }
        public Nullable<decimal> ActiveEmployeePTChargeAmt3 { get; set; }
        public Nullable<decimal> ActiveEmployeePTChargeAmt4 { get; set; }
        public bool ProrateChargeByDivision { get; set; }
        public string LegacyClientID { get; set; }
        public string GuardCustID { get; set; }
        public string BusinessType { get; set; }
        public Nullable<byte> ClientStatus { get; set; }
        public Nullable<byte> ClientType { get; set; }
        public Nullable<decimal> PTONoteIndex { get; set; }
        public bool PTOInactive { get; set; }
        public string County { get; set; }
        public Nullable<decimal> TotalAmountOfNSFChecksLife { get; set; }
        public Nullable<int> NumberOfNSFChecksLife { get; set; }
        public Nullable<decimal> CustomerBalance { get; set; }
        public Nullable<decimal> AgingPeriodAmounts1 { get; set; }
        public Nullable<decimal> AgingPeriodAmounts2 { get; set; }
        public Nullable<decimal> AgingPeriodAmounts3 { get; set; }
        public Nullable<decimal> AgingPeriodAmounts4 { get; set; }
        public Nullable<decimal> AgingPeriodAmounts5 { get; set; }
        public Nullable<decimal> AgingPeriodAmounts6 { get; set; }
        public Nullable<decimal> AgingPeriodAmounts7 { get; set; }
        public Nullable<System.DateTime> LastAged { get; set; }
        public Nullable<System.DateTime> LastNSFCheckDate { get; set; }
        public Nullable<decimal> LastPaymentAmount { get; set; }
        public Nullable<System.DateTime> LastPaymentDate { get; set; }
        public Nullable<System.DateTime> LastTransactionDate { get; set; }
        public Nullable<decimal> LastTransactionAmount { get; set; }
        public Nullable<decimal> LastFinanceChargeAmount { get; set; }
        public Nullable<decimal> UnpaidFinanceChargesYTD { get; set; }
        public Nullable<int> AverageDaysToPayLYR { get; set; }
        public Nullable<int> AverageDaysToPayLife { get; set; }
        public Nullable<int> AverageDaysToPayYear { get; set; }
        public Nullable<int> NumberADTPDocumentsLife { get; set; }
        public Nullable<int> NumberADTPDocumentsYear { get; set; }
        public Nullable<int> NumberADTPDocumentsLYR { get; set; }
        public Nullable<decimal> TotalDiscountsTakenYTD { get; set; }
        public Nullable<decimal> TotalDiscountsTakenLYR { get; set; }
        public Nullable<decimal> TotalDiscountsTakenLTD { get; set; }
        public Nullable<decimal> TotalDiscountsAvailableYTD { get; set; }
        public Nullable<decimal> Retainage { get; set; }
        public Nullable<decimal> TotalAmountOfNSFChecksYTD { get; set; }
        public Nullable<int> NumberOfNSFChecksYTD { get; set; }
        public Nullable<decimal> UnpostedSalesAmount { get; set; }
        public Nullable<decimal> UnpostedCashAmount { get; set; }
        public Nullable<decimal> UnpostedOtherSalesAmount { get; set; }
        public Nullable<decimal> UnpostedOtherCashAmount { get; set; }
        public Nullable<decimal> NonCurrentScheduledPayments { get; set; }
        public Nullable<decimal> TotalSalesYTD { get; set; }
        public Nullable<decimal> TotalSalesLTD { get; set; }
        public Nullable<decimal> TotalSalesLYR { get; set; }
        public Nullable<decimal> TotalCostYTD { get; set; }
        public Nullable<decimal> TotalCostLTD { get; set; }
        public Nullable<decimal> TotalCostLYR { get; set; }
        public Nullable<decimal> TotalCashReceivedYTD { get; set; }
        public Nullable<decimal> TotalCashRecievedLTD { get; set; }
        public Nullable<decimal> TotalCashReceivedLYR { get; set; }
        public Nullable<decimal> TotalFinanceChargesYTD { get; set; }
        public Nullable<decimal> TotalFinanceChargesLTD { get; set; }
        public Nullable<decimal> TotalFinanceChargesLYR { get; set; }
        public Nullable<decimal> FinanceChargesCYTD { get; set; }
        public Nullable<decimal> FinanceChargesLYRCalendar { get; set; }
        public Nullable<decimal> TotalBadDebtYTD { get; set; }
        public Nullable<decimal> TotalBadDebtLYR { get; set; }
        public Nullable<decimal> TotalBadDebtLTD { get; set; }
        public Nullable<decimal> TotalWaivedFCYTD { get; set; }
        public Nullable<decimal> TotalWaivedFCLTD { get; set; }
        public Nullable<decimal> TotalWaivedFCLYR { get; set; }
        public Nullable<decimal> TotalWriteoffsYTD { get; set; }
        public Nullable<decimal> TotalWriteoffsLTD { get; set; }
        public Nullable<decimal> TotalWriteoffsLYR { get; set; }
        public Nullable<int> TotalInvoicesYTD { get; set; }
        public Nullable<int> TotalInvoicesLTD { get; set; }
        public Nullable<int> TotalInvoicesLYR { get; set; }
        public Nullable<int> TotalFCYTD { get; set; }
        public Nullable<int> TotalFCLTD { get; set; }
        public Nullable<int> TotalFCLYR { get; set; }
        public Nullable<decimal> WriteOffsLife { get; set; }
        public Nullable<decimal> WriteOffsLYR { get; set; }
        public Nullable<decimal> WriteOffsYTD { get; set; }
        public Nullable<decimal> HighBalanceLYR { get; set; }
        public Nullable<decimal> HighBalanceYTD { get; set; }
        public Nullable<decimal> HighBalanceLTD { get; set; }
        public Nullable<System.DateTime> LastStatementDate { get; set; }
        public Nullable<decimal> LastStatementAmount { get; set; }
        public Nullable<decimal> DepositsReveived { get; set; }
        public Nullable<decimal> OnOrderAmount { get; set; }
        public Nullable<decimal> TotalReturnsYTD { get; set; }
        public Nullable<decimal> TotalReturnsLTD { get; set; }
        public Nullable<decimal> TotalReturnsLYR { get; set; }
        public Nullable<System.DateTime> CheckNotEndDate { get; set; }
        public string CheckString1 { get; set; }
        public string CheckString2 { get; set; }
        public string CheckString3 { get; set; }
        public Nullable<decimal> UserDefinedCurrency1 { get; set; }
        public Nullable<decimal> UserDefinedCurrency2 { get; set; }
        public Nullable<decimal> UserDefinedCurrency3 { get; set; }
        public Nullable<decimal> UserDefinedCurrency4 { get; set; }
        public Nullable<decimal> UserDefinedCurrency5 { get; set; }
        public Nullable<System.DateTime> UserDefinedDate1 { get; set; }
        public Nullable<System.DateTime> UserDefinedDate2 { get; set; }
        public Nullable<System.DateTime> UserDefinedDate3 { get; set; }
        public Nullable<System.DateTime> UserDefinedDate4 { get; set; }
        public Nullable<System.DateTime> UserDefinedDate5 { get; set; }
        public string UserDefinedText1 { get; set; }
        public string UserDefinedText2 { get; set; }
        public string UserDefinedText3 { get; set; }
        public string UserDefinedText4 { get; set; }
        public string UserDefinedText5 { get; set; }
        public Nullable<int> UserDefinedNumerical1 { get; set; }
        public Nullable<int> UserDefinedNumerical2 { get; set; }
        public Nullable<int> UserDefinedNumerical3 { get; set; }
        public Nullable<int> UserDefinedNumerical4 { get; set; }
        public Nullable<int> UserDefinedNumerical5 { get; set; }
        public Nullable<byte> EmployeeIDMethod { get; set; }
        public string EmployeeIDPrefix { get; set; }
        public string NextEmployeeID { get; set; }
        public Nullable<byte> LogonIDMethod { get; set; }
        public string LogonIDPrefix { get; set; }
        public string AllowedEmployeeClasses { get; set; }
        public bool AllowedNewEmployeeSetup { get; set; }
        public bool OnUploadNotifyClient { get; set; }
        public bool OnUploadNotifyEmployee { get; set; }
        public bool OnUploadNotifyNewEmployee { get; set; }
        public string RequiredTables { get; set; }
        public bool DnetClientBeginner { get; set; }
        public bool DnetEmployeeBeginner { get; set; }
        public string AllowedEmployeeClasses2 { get; set; }
        public string InvoiceItemName1 { get; set; }
        public string InvoiceItemName2 { get; set; }
        public string InvoiceItemName3 { get; set; }
        public Nullable<int> Logo { get; set; }
        public Nullable<int> ACALookbackPeriodMethod { get; set; }
        public Nullable<System.DateTime> ACALookbackPeriodStartDate { get; set; }
        public Nullable<System.DateTime> ACALookbackPeriodEndDate { get; set; }
        public Nullable<int> ACALookbackPeriodStartMonth { get; set; }
        public Nullable<int> ACALookbackPeriodEndMonth { get; set; }
        public Nullable<int> ACALookbackHoursMethod { get; set; }
        public Nullable<int> ACALookbackHoursAmount { get; set; }
        public Nullable<int> ACAAdminPeriod { get; set; }
        public Nullable<int> ACALOAMethod { get; set; }
        public Nullable<decimal> ACALOACreditAmount { get; set; }
        public Nullable<int> ACABreakInServiceMethod { get; set; }
        public Nullable<int> ACAAffordabilityMethod { get; set; }
        public Nullable<decimal> ACAWhatIfGrossWagePercent { get; set; }
        public Nullable<decimal> ACAWhatIfHealthPremiumPercent { get; set; }
        public Nullable<System.DateTime> ACAAffordabilityStartDate { get; set; }
        public Nullable<System.DateTime> ACAAffordabilityEndDate { get; set; }
        public Nullable<decimal> ACAPlanAmount { get; set; }
        public Nullable<short> ACAPlanType { get; set; }
        public bool ACAAgeBandedPlan { get; set; }
        public Nullable<System.DateTime> ACAStabilityStartDate { get; set; }
        public Nullable<System.DateTime> ACAStablilityEndDate { get; set; }
        public Nullable<System.DateTime> ACAAdminStartDate { get; set; }
        public Nullable<System.DateTime> ACAAdminEndDate { get; set; }
        public Nullable<int> ACALookbackLength { get; set; }
        public string KERS_ERCode { get; set; }
        public Nullable<short> KERS_RptType { get; set; }
        public Nullable<decimal> KERS_IRSLimit { get; set; }
        public bool IsSynced { get; set; }
        public bool USeNEw { get; set; }
        public string AID { get; set; }
        public Nullable<System.DateTime> CheckNoteValidDate { get; set; }
        public string CheckNote1 { get; set; }
        public string CheckNote2 { get; set; }
        public string CheckNote3 { get; set; }
        public Nullable<short> ContributionPriorityMethod { get; set; }
        public bool LaborDistributionEnabled1 { get; set; }
        public string LaborDistributionLabel1 { get; set; }
        public string LaborDistributionDescription1 { get; set; }
        public string LaborDistributionValue1 { get; set; }
        public bool LaborDistributionEnabled2 { get; set; }
        public string LaborDistributionLabel2 { get; set; }
        public string LaborDistributionDescription2 { get; set; }
        public string LaborDistributionValue2 { get; set; }
        public bool LaborDistributionEnabled3 { get; set; }
        public string LaborDistributionLabel3 { get; set; }
        public string LaborDistributionDescription3 { get; set; }
        public string LaborDistributionValue3 { get; set; }
        public bool LaborDistributionEnabled4 { get; set; }
        public string LaborDistributionLabel4 { get; set; }
        public string LaborDistributionDescription4 { get; set; }
        public string LaborDistributionValue4 { get; set; }
        public bool LaborDistributionEnabled5 { get; set; }
        public string LaborDistributionLabel5 { get; set; }
        public string LaborDistributionDescription5 { get; set; }
        public string LaborDistributionValue5 { get; set; }
        public bool LaborDistributionEnabled6 { get; set; }
        public string LaborDistributionLabel6 { get; set; }
        public string LaborDistributionDescription6 { get; set; }
        public string LaborDistributionValue6 { get; set; }
        public bool LaborDistributionEnabled7 { get; set; }
        public string LaborDistributionLabel7 { get; set; }
        public string LaborDistributionDescription7 { get; set; }
        public string LaborDistributionValue7 { get; set; }
        public bool LaborDistributionEnabled8 { get; set; }
        public string LaborDistributionLabel8 { get; set; }
        public string LaborDistributionDescription8 { get; set; }
        public string LaborDistributionValue8 { get; set; }
        public bool UseDnetPayroll { get; set; }
        public bool EmergencyLeaveCredit { get; set; }
        public bool EmergencyDeferralCredit { get; set; }
        public bool EmergencyRetentionCredit { get; set; }
        public bool PrintPlanDescOnCheck { get; set; }
        public string InvoiceItemName4 { get; set; }
        public string InvoiceItemName5 { get; set; }
        public string InvoiceItemName6 { get; set; }
        public string InvoiceItemName7 { get; set; }
        public string InvoiceItemName8 { get; set; }
        public string InvoiceItemName9 { get; set; }
        public string InvoiceItemName10 { get; set; }
        public string ACHCompanyID { get; set; }
        public string ACHCompanyName { get; set; }
        public string OriginationID { get; set; }
        public string OffsetRecordAccountHolder { get; set; }
        public string PayrollProfileID { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<CheckPrinterData> CheckPrinterDatas { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<Client401kPlans> Client401kPlans { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientACACodeSelection> ClientACACodeSelections { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientACAPlanLevel> ClientACAPlanLevels { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientACAShareLevel> ClientACAShareLevels { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientAddress> ClientAddresses { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientAssignment> ClientAssignments { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientBusinessExpens> ClientBusinessExpenses { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientCharge> ClientCharges { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientCodeDescription> ClientCodeDescriptions { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientCompositeBillingSetup> ClientCompositeBillingSetups { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientContact> ClientContacts { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientContactType> ClientContactTypes { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientDivision> ClientDivisions { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientImportCodeMapping> ClientImportCodeMappings { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientInsurancePlan> ClientInsurancePlans { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientInvoiceCertifiedPayrollDetail> ClientInvoiceCertifiedPayrollDetails { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientInvoiceDefault> ClientInvoiceDefaults { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientJBSCode> ClientJBSCodes { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientJobCostAssignment> ClientJobCostAssignments { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientManager> ClientManagers { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientPayrollSchedule> ClientPayrollSchedules { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientPosting> ClientPostings { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientPTOType> ClientPTOTypes { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientReviewAction> ClientReviewActions { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientReviewRating> ClientReviewRatings { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientReviewType> ClientReviewTypes { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual Company Company { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientSicktimeTier> ClientSicktimeTiers { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual ClientTimeClockImportSetup ClientTimeClockImportSetup { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientTimePunchSecurity> ClientTimePunchSecurities { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientTrainingRating> ClientTrainingRatings { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientTrainingStatus> ClientTrainingStatuses { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientTrainingType> ClientTrainingTypes { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientUnemploymentSetup> ClientUnemploymentSetups { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientVacationTier> ClientVacationTiers { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientWorkersCompPlan> ClientWorkersCompPlans { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual DarwinetSetup DarwinetSetup { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<InvoiceASOAccountSetupHistory> InvoiceASOAccountSetupHistories { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<InvoiceASOAccountSetupHistoryOriginal> InvoiceASOAccountSetupHistoryOriginals { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<InvoiceASOClientPostHistory> InvoiceASOClientPostHistories { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<InvoiceASOClientPostHistoryOriginal> InvoiceASOClientPostHistoryOriginals { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<Invoice> Invoices { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<InvoicesOriginal> InvoicesOriginals { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<NotificationClient> NotificationClients { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<ClientACHHeader> ClientACHHeaders { get; set; }
    }
}
