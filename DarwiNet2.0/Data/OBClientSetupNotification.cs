//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class OBClientSetupNotification
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public OBClientSetupNotification()
        {
            this.OBClientSetupNotificationAttachments = new HashSet<OBClientSetupNotificationAttachment>();
        }
    
        public int CompanyID { get; set; }
        public int SetupID { get; set; }
        public short Type { get; set; }
        public short Assigned { get; set; }
        public bool Enabled { get; set; }
        public bool UseDefault { get; set; }
        public string CCList { get; set; }
        public string BCCList { get; set; }
        public string EmailSubject { get; set; }
        public string EmailBody { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual OBClientSetup OBClientSetup { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<OBClientSetupNotificationAttachment> OBClientSetupNotificationAttachments { get; set; }
    }
}
