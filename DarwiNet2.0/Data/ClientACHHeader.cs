//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class ClientACHHeader
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public Nullable<short> ACHType { get; set; }
        public string TransactionNumber { get; set; }
        public string CheckBookID { get; set; }
        public Nullable<int> CheckCount { get; set; }
        public Nullable<int> DirectDepositCount { get; set; }
        public string PostedUserID { get; set; }
        public Nullable<System.DateTime> PostedDate { get; set; }
        public Nullable<System.DateTime> ACHExportDate { get; set; }
        public string ACHExportUser { get; set; }
        public Nullable<int> ACHExportBatch { get; set; }
        public string ACHExportID { get; set; }
        public Nullable<int> ACHReverseBatch { get; set; }
        public Nullable<System.DateTime> ACHReverseDate { get; set; }
        public string ACHReverseUser { get; set; }
        public string ACHReverseNumber { get; set; }
        public string ACHPayrollProfileID { get; set; }
        public string ACHReceivablesProfileID { get; set; }
        public int DarwinInvoiceNumber { get; set; }
        public string PayrollNumber { get; set; }
        public Nullable<decimal> TransactionAmount { get; set; }
        public Nullable<decimal> TransactionBalance { get; set; }
        public bool CMUpdated { get; set; }
        public string TransactionType { get; set; }
        public Nullable<System.DateTime> LastDateEdited { get; set; }
        public string LastEditedByUser { get; set; }
        public bool SelectedToPrint { get; set; }
        public string FileContent { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual Client Client { get; set; }
    }
}
