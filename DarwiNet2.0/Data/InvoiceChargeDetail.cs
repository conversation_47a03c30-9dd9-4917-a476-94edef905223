//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class InvoiceChargeDetail
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public int DarwinInvoiceNumber { get; set; }
        public string ChargeType { get; set; }
        public string ComponentCode { get; set; }
        public Nullable<int> SequenceNumber { get; set; }
        public Nullable<decimal> Total { get; set; }
        public bool SelectForPrint { get; set; }
        public Nullable<decimal> FUTAWageLimit { get; set; }
        public Nullable<decimal> SUTAWageLimit { get; set; }
        public Nullable<decimal> FICASSWageLimit { get; set; }
        public Nullable<decimal> WorkersCompWageLimit { get; set; }
        public int MergedInvoiceNumber { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual InvoiceCharge InvoiceCharge { get; set; }
    }
}
