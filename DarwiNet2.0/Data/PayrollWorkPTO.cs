//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class PayrollWorkPTO
    {
        public int CompanyID { get; set; }
        public string UserID { get; set; }
        public string ProfileID { get; set; }
        public string EmployeeID { get; set; }
        public int PTOType { get; set; }
        public Nullable<int> AmountTaken { get; set; }
        public Nullable<int> AvailableHours { get; set; }
        public Nullable<int> YTDHoursAccrued { get; set; }
        public Nullable<int> LastCarryoverAmount { get; set; }
        public Nullable<System.DateTime> ProcessDate { get; set; }
        public bool RecordProcessed { get; set; }
        public Nullable<int> HoursPerYear { get; set; }
        public Nullable<int> AccrualAmount { get; set; }
        public Nullable<int> MaxAccrualYTD { get; set; }
        public Nullable<int> ForfeitedHours { get; set; }
        public string PayrollNumber { get; set; }
        public string SnapshotID { get; set; }
        public bool IsLatest { get; set; }
        public Nullable<System.DateTime> LastAnniversaryDate { get; set; }
    }
}
