//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class ClientJobCostAssignmentDetail
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string JobCostingName { get; set; }
        public string JobLevel2 { get; set; }
        public string JobLevel3 { get; set; }
        public string JobLevel4 { get; set; }
        public string JobLevel5 { get; set; }
        public string JobLevel6 { get; set; }
        public string JBSID { get; set; }
        public Nullable<int> SequenceNumber { get; set; }
        public string Description2 { get; set; }
        public string Description3 { get; set; }
        public string Description4 { get; set; }
        public string Description5 { get; set; }
        public string Description6 { get; set; }
        public Nullable<System.DateTime> EstimatedStartDate { get; set; }
        public Nullable<System.DateTime> EstimatedCompletion { get; set; }
        public Nullable<int> EstimatedHours { get; set; }
        public Nullable<decimal> Cost { get; set; }
        public string ProjectManager { get; set; }
        public string Phone1 { get; set; }
        public string Email { get; set; }
        public string WorkersComp { get; set; }
        public string Department { get; set; }
        public string DefaultHourlyCode { get; set; }
        public string DefaultOTCode { get; set; }
        public string DefaultSalaryCode { get; set; }
        public string DefaultSalOTCode { get; set; }
        public string StateCode { get; set; }
        public string LocalTax { get; set; }
        public Nullable<decimal> RegularRate { get; set; }
        public Nullable<decimal> OTRate { get; set; }
        public Nullable<decimal> DefaultSalaryRate { get; set; }
        public Nullable<decimal> DefaultSalaryOTRate { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual ClientJobCostAssignment ClientJobCostAssignment { get; set; }
    }
}
