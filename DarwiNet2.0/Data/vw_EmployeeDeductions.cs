//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class vw_EmployeeDeductions
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string EmployeeID { get; set; }
        public string Deduction { get; set; }
        public string Description { get; set; }
        public string InActive { get; set; }
        public string BasedOnPayCode { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public Nullable<System.DateTime> EffectiveDate { get; set; }
        public string DeductionMethod { get; set; }
        public Nullable<decimal> DeductionAmount1 { get; set; }
        public Nullable<decimal> DeductionAmount2 { get; set; }
        public Nullable<decimal> DeductionAmount3 { get; set; }
        public Nullable<decimal> DeductionAmount4 { get; set; }
        public Nullable<decimal> DeductionAmount5 { get; set; }
        public Nullable<decimal> DeductionPercent1 { get; set; }
        public Nullable<decimal> DeductionPercent2 { get; set; }
        public Nullable<decimal> DeductionPercent3 { get; set; }
        public Nullable<decimal> DeductionPercent4 { get; set; }
        public Nullable<decimal> DeductionPercent5 { get; set; }
        public Nullable<decimal> PayPeriodMax { get; set; }
        public Nullable<decimal> DeductionMonthMax { get; set; }
        public Nullable<decimal> DeductionQuarterMax { get; set; }
        public Nullable<decimal> DeductionFiscalYearMax { get; set; }
        public Nullable<byte> DeductionType { get; set; }
        public Nullable<byte> GarnishmentCategory { get; set; }
        public Nullable<decimal> LifetimeMax { get; set; }
    }
}
