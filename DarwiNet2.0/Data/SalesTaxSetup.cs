//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class SalesTaxSetup
    {
        public int CompanyID { get; set; }
        public string TaxDetailID { get; set; }
        public string Description { get; set; }
        public Nullable<short> Type { get; set; }
        public Nullable<int> AccountIndex { get; set; }
        public string TaxIDNumber { get; set; }
        public Nullable<short> TaxDetailBase { get; set; }
        public Nullable<decimal> TaxDetailPercent { get; set; }
        public Nullable<decimal> TaxDetailAmount { get; set; }
        public Nullable<short> TaxDetailRounding { get; set; }
        public string TaxDetailBasedOnDetailID { get; set; }
        public Nullable<decimal> TaxDetailTaxableMinimum { get; set; }
        public Nullable<decimal> TaxDetailTaxableMaximum { get; set; }
        public Nullable<decimal> TaxDetailTaxMinimum { get; set; }
        public Nullable<decimal> TaxDetailTaxMaximum { get; set; }
        public Nullable<short> TaxDetailRangeType { get; set; }
        public Nullable<short> TaxDetailBaseQualifiers { get; set; }
        public Nullable<bool> TaxDetailPrintOnDocuments { get; set; }
        public string TaxDetailPrintCharacter { get; set; }
        public Nullable<bool> TaxDetailExcludeDiscounts { get; set; }
        public string CompanyTaxID { get; set; }
        public string ContactPerson { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string Address3 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string ZipCode { get; set; }
        public string Country { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Phone3 { get; set; }
        public string Fax { get; set; }
        public string TaxUDF1 { get; set; }
        public string TaxUDF2 { get; set; }
        public Nullable<bool> VATRegisterTax { get; set; }
        public Nullable<bool> TaxInvoiceRequired { get; set; }
        public Nullable<short> TaxPostToAccount { get; set; }
        public byte[] TaxBoxes { get; set; }
        public Nullable<bool> IgnoreGrossAmount { get; set; }
        public Nullable<decimal> TaxDetailTaxablePercent { get; set; }
    }
}
