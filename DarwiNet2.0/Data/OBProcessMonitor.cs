//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class OBProcessMonitor
    {
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string ClientID { get; set; }
        public Nullable<int> SetupID { get; set; }
        public string DivisionID { get; set; }
        public string CodeDivision { get; set; }
        public Nullable<short> CodeSetup { get; set; }
        public string WorkState { get; set; }
        public string EmployeeClass { get; set; }
        public string PlanName { get; set; }
        public bool EEFinalize { get; set; }
        public Nullable<short> Signature { get; set; }
        public bool MaskSSNInSign { get; set; }
        public Nullable<short> BankPresentation { get; set; }
        public Nullable<short> SortOption { get; set; }
        public Nullable<System.DateTime> EEStartDate { get; set; }
        public Nullable<int> EECurrentTask { get; set; }
        public Nullable<short> EEStatus { get; set; }
        public Nullable<System.DateTime> EESubmitDate { get; set; }
        public Nullable<System.DateTime> CCStartDate { get; set; }
        public string CCStartBy { get; set; }
        public Nullable<int> CCCurrentTask { get; set; }
        public Nullable<short> CCStatus { get; set; }
        public Nullable<System.DateTime> CCSubmitDate { get; set; }
        public string CCSubmitBy { get; set; }
        public Nullable<System.DateTime> DueDate { get; set; }
        public bool IsCompleted { get; set; }
        public string PayPeriod { get; set; }
        public bool UseBenefits { get; set; }
        public bool UseLicenses { get; set; }
        public bool UseDeductions { get; set; }
        public bool UseDepartments { get; set; }
        public bool UsePaycodes { get; set; }
        public bool UsePositions { get; set; }
        public bool UsePTO { get; set; }
        public bool UseSUTAState { get; set; }
        public bool UseTraining { get; set; }
        public bool UseWorkComp { get; set; }
        public Nullable<System.DateTime> WarningDate { get; set; }
        public bool WaivedPension { get; set; }
        public bool WaivedDirDep { get; set; }
        public string RoleID { get; set; }
        public bool AlertCleared { get; set; }
        public bool ODAlertCleared { get; set; }
        public bool CreateUserAccount { get; set; }
        public Nullable<System.DateTime> RehireDate { get; set; }
        public string RehireEmployeeID { get; set; }
    }
}
