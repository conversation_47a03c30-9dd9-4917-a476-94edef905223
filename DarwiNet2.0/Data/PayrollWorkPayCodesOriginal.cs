//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class PayrollWorkPayCodesOriginal
    {
        public int CompanyID { get; set; }
        public string UserID { get; set; }
        public byte PayRunType { get; set; }
        public string EmployeeID { get; set; }
        public Nullable<int> TRXNumber { get; set; }
        public string PayRecord { get; set; }
        public Nullable<int> ProcessSequence { get; set; }
        public Nullable<byte> PayType { get; set; }
        public string BasePayRecord { get; set; }
        public Nullable<byte> BasePayType { get; set; }
        public Nullable<decimal> BasedOnRate { get; set; }
        public Nullable<decimal> PayRateAmount { get; set; }
        public string PayUnit { get; set; }
        public Nullable<byte> PayUnitPeriod { get; set; }
        public Nullable<byte> PayPeriod { get; set; }
        public Nullable<decimal> PayPerPeriod { get; set; }
        public Nullable<decimal> PayAdvance { get; set; }
        public bool AccrueVacation { get; set; }
        public bool AccrueSickTime { get; set; }
        public bool ReportAsWages { get; set; }
        public Nullable<int> DaysWorked { get; set; }
        public Nullable<int> WeeksWorked { get; set; }
        public Nullable<decimal> Receipts { get; set; }
        public Nullable<byte> TipType { get; set; }
        public string Department { get; set; }
        public string JobTitle { get; set; }
        public string StateCode { get; set; }
        public string LocalTax { get; set; }
        public string SutaState { get; set; }
        public string WorkersComp { get; set; }
        public Nullable<decimal> UnitsToPay { get; set; }
        public Nullable<decimal> AdjustedUnits { get; set; }
        public Nullable<decimal> TotalPay { get; set; }
        public string BatchSource { get; set; }
        public Nullable<int> PayrollTRXSource { get; set; }
        public string LastUser { get; set; }
        public Nullable<System.DateTime> LastDateEdited { get; set; }
        public Nullable<System.DateTime> TRXBeginningDate { get; set; }
        public Nullable<System.DateTime> TRXEndingDate { get; set; }
        public bool BuildCheckWarnings { get; set; }
        public bool BuildCheckErrors { get; set; }
        public string ShiftCode { get; set; }
        public Nullable<decimal> ShiftPremium { get; set; }
        public Nullable<decimal> TotalUnitsToPeriod { get; set; }
        public bool ZeroBySalaryAdjustment { get; set; }
        public Nullable<decimal> OriginalPayPerPeriod { get; set; }
        public Nullable<decimal> NetWcBillingAmount { get; set; }
        public Nullable<decimal> NetWcLiabilityAmount { get; set; }
        public Nullable<decimal> GrossWcBillingAmount { get; set; }
        public bool AccruePTO1 { get; set; }
        public bool AccruePTO2 { get; set; }
        public bool AccruePTO3 { get; set; }
        public bool AccruePTO4 { get; set; }
        public bool AccruePTO5 { get; set; }
        public bool AccruePTO6 { get; set; }
        public bool AccruePTO7 { get; set; }
        public bool AccruePTO8 { get; set; }
        public bool AccruePTO9 { get; set; }
        public bool AccruePTO10 { get; set; }
        public bool IncludedInBlendedRate { get; set; }
        public bool HoursNotBlended { get; set; }
        public string JBSID { get; set; }
        public int IndexLong { get; set; }
        public Nullable<byte> SalaryTRXType { get; set; }
        public Nullable<decimal> OrigCalcAmount { get; set; }
        public Nullable<decimal> OrigUnits { get; set; }
        public Nullable<decimal> InitialAdjustAmount { get; set; }
        public Nullable<decimal> WageRatio { get; set; }
        public string ProfileID { get; set; }
        public string TRXSource { get; set; }
        public string LDLabel1 { get; set; }
        public string LDLabel2 { get; set; }
        public string LDLabel3 { get; set; }
        public string LDLabel4 { get; set; }
        public string LDLabel5 { get; set; }
        public string LDLabel6 { get; set; }
        public string LDLabel7 { get; set; }
        public string LDLabel8 { get; set; }
        public string LDValue1 { get; set; }
        public string LDValue2 { get; set; }
        public string LDValue3 { get; set; }
        public string LDValue4 { get; set; }
        public string LDValue5 { get; set; }
        public string LDValue6 { get; set; }
        public string LDValue7 { get; set; }
        public string LDValue8 { get; set; }
        public Nullable<decimal> FringeWage { get; set; }
        public Nullable<decimal> YTD { get; set; }
        public string Worksite { get; set; }
        public string Location { get; set; }
        public string Area { get; set; }
        public string PayGroup { get; set; }
        public string BuildCheckWarningReasons { get; set; }
        public string BuildCheckErrorReasons { get; set; }
        public string JobCostingName { get; set; }
        public string JobLevel2 { get; set; }
        public string JobLevel3 { get; set; }
        public string JobLevel4 { get; set; }
        public string JobLevel5 { get; set; }
        public string JobLevel6 { get; set; }
        public string PayrollNumber { get; set; }
        public string SnapshotID { get; set; }
        public bool IsLatest { get; set; }
        public byte[] BuildCheckWarningsAdditional { get; set; }
        public byte[] BuildCheckErrorsAdditional { get; set; }
    }
}
