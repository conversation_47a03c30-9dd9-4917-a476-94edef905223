//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class EmployeeStateTax
    {
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string StateCode { get; set; }
        public string TaxFilingStatus { get; set; }
        public bool ExemptionForBlind { get; set; }
        public bool ExemptionForBlindSpouse { get; set; }
        public bool ExemptionForOver65 { get; set; }
        public bool ExemptionForSelf { get; set; }
        public bool ExemptionForSpecialAllowance { get; set; }
        public bool ExemptionForSpouse { get; set; }
        public bool ExemptionForSpouseOver65 { get; set; }
        public Nullable<int> PersonalExemptions { get; set; }
        public Nullable<int> Dependents { get; set; }
        public Nullable<int> AdditionalAllowances { get; set; }
        public Nullable<int> EstimatedDeductionAllowances { get; set; }
        public decimal ExemptionAmount { get; set; }
        public decimal AdditionalStateWithholding { get; set; }
        public decimal EstimatedStateWithholding { get; set; }
        public bool Inactive { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual Employee Employee { get; set; }
    }
}
