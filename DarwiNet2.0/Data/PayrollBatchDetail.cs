//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class PayrollBatchDetail
    {
        public int CompanyID { get; set; }
        public int ComputerTRXNumber { get; set; }
        public string BatchNumber { get; set; }
        public string EmployeeID { get; set; }
        public string UPRTRXCode { get; set; }
        public string J<PERSON><PERSON> { get; set; }
        public Nullable<byte> SalaryTRXType { get; set; }
        public Nullable<byte> JobCostingMode { get; set; }
        public string JobCostingName { get; set; }
        public string JobLevel2 { get; set; }
        public string JobLevel3 { get; set; }
        public string JobLevel4 { get; set; }
        public string JobLevel5 { get; set; }
        public string JobLevel6 { get; set; }
        public string PayrollCode { get; set; }
        public Nullable<int> HoursWorked { get; set; }
        public string LDLabel1 { get; set; }
        public string LDLabel2 { get; set; }
        public string LDLabel3 { get; set; }
        public string LDLabel4 { get; set; }
        public string LDLabel5 { get; set; }
        public string LDLabel6 { get; set; }
        public string LDLabel7 { get; set; }
        public string LDLabel8 { get; set; }
        public string LDValue1 { get; set; }
        public string LDValue2 { get; set; }
        public string LDValue3 { get; set; }
        public string LDValue4 { get; set; }
        public string LDValue5 { get; set; }
        public string LDValue6 { get; set; }
        public string LDValue7 { get; set; }
        public string LDValue8 { get; set; }
        public Nullable<decimal> FringeWage { get; set; }
        public Nullable<byte> ComputerTRXType { get; set; }
        public Nullable<byte> SalaryChange { get; set; }
        public Nullable<System.DateTime> TRXBeginningDate { get; set; }
        public Nullable<System.DateTime> TRXEndingDate { get; set; }
        public Nullable<int> TRXHoursUnits { get; set; }
        public Nullable<decimal> HourlyPayRate { get; set; }
        public Nullable<decimal> PayRateAmount { get; set; }
        public Nullable<decimal> VariableDedBenAmount { get; set; }
        public Nullable<int> VariableDedBenPercent { get; set; }
        public Nullable<int> DaysWorked { get; set; }
        public Nullable<int> WeeksWorked { get; set; }
        public string Department { get; set; }
        public string JobTitle { get; set; }
        public string StateCode { get; set; }
        public string LocalTax { get; set; }
        public string SutaState { get; set; }
        public string WorkersComp { get; set; }
        public string LastUser { get; set; }
        public Nullable<System.DateTime> LastDateEdited { get; set; }
        public string TRXSource { get; set; }
        public Nullable<short> DocumentType { get; set; }
        public bool Voided { get; set; }
        public bool InAdditionToSalary { get; set; }
        public string ShiftCode { get; set; }
        public Nullable<decimal> ShiftPremium { get; set; }
        public string JobNumber { get; set; }
        public string UnionCode { get; set; }
        public Nullable<decimal> Receipts { get; set; }
    }
}
