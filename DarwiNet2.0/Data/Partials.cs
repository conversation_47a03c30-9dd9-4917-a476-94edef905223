using DarwiNet2._0.Controllers.ACH;
using System;
using System.Collections.Generic;
using System.Linq;
using Thinkware.Pay360.Timesheets;

namespace DarwiNet2._0.Data
{
    public partial class OBProcessTask
    {
        public string eeTaskName { get; set; }
        public string eeTaskStatus { get; set; }
        public string ccTaskname { get; set; }
        public string ccTaskStatus { get; set; }
        //public DateTime? ECompleteDate { get; set; }
        //public DateTime? CCompleteDate { get; set; }
    }
    public class ETaxStatement : Employee
    {
        public int CompanyId { get; set; }
        public string ClientId { get; set; }
        public short Year { get; set; }
        public string Selected { get; set; }
        public string EmailSent { get; set; }
        public string CreateDate { get; set; }
        public string ModifiedDate { get; set; }
        public string CreatedBy { get; set; }
    }
    public partial class EmployeeDirectDepositHistory : ITransferInfo, IEntryDetail
    {
        public DateTime CheckDate { get; set; }
    }

    public partial class InvoiceDirectDeposit : ITransferInfo, IEntryDetail { }

    public class AddPayrollNameAndNumber
    {
        public string RecordNumber { get; set; }
        public string RecordName { get; set; }
    }

    public class EmployeeClassVM
    {
        public string EmployeeClass { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string SUTAState { get; set; }
        public string WorkersComp { get; set; }
        public int WorkHoursPerYear { get; set; }
        public short EmploymentStatus { get; set; }
    }


    public class InvoiceReportsVM
    {
        public int ID { get; set; }
        public int DarwinInvoiceNumber { get; set; }
        public DateTime? InvoiceDate { get; set; }
        public string ReportDisplayName { get; set; }
    }

    public class OBProcessMonitorVM
    {
        public string eeTaskName { get; set; }
        public string eeTaskStatus { get; set; }
        public string ccTaskname { get; set; }
        public string ccTaskStatus { get; set; }
        public string EmailAddress { get; set; }
        public string LoginCode { get; set; }
        public DateTime? CreationDate { get; set; }
        public DateTime? BirthDate { get; set; }
        public int? SetupID { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeIDDisplay { get; set; }
        public DateTime? EEStartDate { get; set; }
        public DateTime? CCStartDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? CCSubmitDate { get; set; }
        public string EmployeeName { get; set; }
        public string WorkState { get; set; }
        public string EmployeeClass { get; set; }
        public string PayPeriod { get; set; }
        public DateTime? EECompleteDate { get; set; }
        public DateTime? CCCompleteDate { get; set; }
        public string ClientID { get; set; }
        public string Division { get; set; }
        public string CCSubmitBy { get; set; }
        public string ProfileName { get; set; }
        public string UserID { get; set; }
        public string Password { get; set; }
        public string OnBoardingType { get; set; }
        public string Comments { get; set; }
    }

    [Serializable]
    public partial class Employee
    {
        public string DepartmentName { get; set; } //Task#2056
        public string EmployeeName { get; set; }

        public virtual string StrGender { get; set; }

        public virtual Boolean BlnInactive { get; set; }

        public virtual Client Client { get; set; }
        public bool FamilyStatusNew { get; set; }
    }

    public partial class EmployeeDirectDeposit
    {
        public Employee Employee { get; set; }
    }

    public class SupervisorEmployeeVM
    {
        public string UserID { get; set; }
        public string UserName { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string Position { get; set; }
        public string DepartmentName { get; set; }
    }


    //Task#2056
    public class SupervisorSecurity : DepartmentQL
    {
        public string UID { get; set; } //id of Users
        //public string UserID { get; set; }
        public string UserName { get; set; }
    }
    public class DocTypeSecurity : UserDocumentSecurity
    {
        public string UID { get; set; } //id of Users
        public string Description { get; set; }
    }

    [Serializable]
    public class EmployeeSrch
    {
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string SSN { get; set; }
        public string Picture { get; set; }
        public string SearchString { get; set; }
        public string dispEEID { get; set; }
        public string phone { get; set; }
        public string dispDept { get; set; }
        public string dispClient { get; set; }
        public string dispRegion { get; set; }
        public string dispPos { get; set; }
        public string dispDivision { get; set; }
        public string EmployeeStatus { get; set; }
        public bool Inactive { get; set; }
    }

    public class EmployeeSrchTemp
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string EmployeeID { get; set; }
        public string TempSSN { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string AddressCode { get; set; }
        public bool Inactive { get; set; }
    }

    public class JsonError
    {
        public string status { get; set; }
        public string message { get; set; }
    }

    public partial class EmployeePTOType
    {
        public string dispType { get; set; }
        public string dispMethod { get; set; }
        public double AvailableHrs { get; set; }
        public double AmountTaken { get; set; }
        public double Accrued { get; set; }
    }
    public partial class EmployeeTraining
    {
        public string Description { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string Rating { get; set; }
        public string Comments { get; set; }

    }
    public partial class EmployeeReview
    {
        public DateTime? NextReviewDate { get; set; }
    }

    #region HR
    public class PTOGroup
    {
        public string PTOPlanID { get; set; }
        public string Description { get; set; }
        public string AccrualMethod { get; set; }
        public string AccrualAmount { get; set; }
        public string AnniversaryMethod { get; set; }
        public bool Inactive { get; set; }
        public byte PTOID { get; set; }
    }

    public class PTOHR : Employee
    {
        public string Available { get; set; }
        public string Accrued { get; set; }
        public string Used { get; set; }
        public string PTOType { get; set; }
        public int PTOTypeInt { get; set; }
        public string Pending { get; set; }
        public string Approved { get; set; }
    }

    public class TrainingGroup
    {
        public string TrainingID { get; set; }
        public string Description { get; set; }
        public string EncryptedID { get; set; }
    }

    public class Training : Employee
    {
        public string Rate { get; set; }
        public string Status { get; set; }
        public string CompletedDate { get; set; }
        public string RenewalDate { get; set; }
        public string TrainingType { get; set; }
        public new string EmployeeID { get; set; }
    }

    public class ReviewGroup
    {
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string NextReview { get; set; }
        public string LastReview { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string Manager { get; set; }
        public bool Inactive { get; set; }
        public int SeqNumber { get; set; }
    }

    public class ReviewHR : Employee
    {
        public string ReviewDate { get; set; }
        public string Manager { get; set; }
        public string Type { get; set; }
        public string Rating { get; set; }
        public string Action { get; set; }
        public int SequenceNumber { get; set; }
    }

    public class LicenseGroup
    {
        public string EmployeeID { get; set; }
        public string LicenseID { get; set; }
        public string Description { get; set; }
        public string EncryptedID { get; set; }
    }

    public class LicenseHR : Employee
    {
        public string LicenseID { get; set; }
        public string LicenseNumber { get; set; }
        public string EffectiveDate { get; set; }
        public string ExpirationDate { get; set; }
        //public bool Inactive { get; set; }
    }

    public class I9Group
    {
        public string EmployeeName { get; set; }
        public string SSN { get; set; }
        public string EmployeeID { get; set; }
        public string ResidentStatus { get; set; }
        public bool Inactive { get; set; }
    }

    public class EmployeeEligibilityVerificationVM
    {
        public string dispSSN { get; set; }
        public string dispResStatus { get; set; }
        public string dispName { get; set; }
        public virtual int CompanyID { get; set; }
        public virtual string EmployeeID { get; set; }
        public virtual string OtherName { get; set; }

        public virtual string AlienNbr { get; set; }
        public virtual string dispWorkExpDate { get; set; }
        public virtual string I94Nbr { get; set; }
        public virtual string ForeignPassportNbr { get; set; }
        public virtual string PassportCountry { get; set; }
        public virtual string dispDocA1Type { get; set; }
        public virtual string DocA1Title { get; set; }
        public virtual string DocA1Authority { get; set; }
        public virtual string DocA1Number { get; set; }
        public virtual string dispDocA1ExpDate { get; set; }
        public virtual string dispDocA2Type { get; set; }
        public virtual string DocA2Title { get; set; }
        public virtual string DocA2Authority { get; set; }
        public virtual string DocA2Number { get; set; }
        public virtual string dispDocA2Expdate { get; set; }
        public virtual string dispDocA3Type { get; set; }
        public virtual string DocA3Title { get; set; }
        public virtual string DocA3Authority { get; set; }
        public virtual string DocA3Number { get; set; }
        public virtual string dispDocA3ExpDate { get; set; }
        public virtual string dispDocBType { get; set; }
        public virtual string DocBTitle { get; set; }
        public virtual string DocBAuthority { get; set; }
        public virtual string DocBNumber { get; set; }
        public virtual string dispDocBExpDate { get; set; }
        public virtual string dispDocCType { get; set; }
        public virtual string DocCTitle { get; set; }
        public virtual string DocCAuthority { get; set; }
        public virtual string DocCNumber { get; set; }
        public virtual string dispDocCExpDate { get; set; }
        public virtual string dispEECompleteDate { get; set; }
        public virtual bool UsePreparer { get; set; }
        public virtual string PrepLastName { get; set; }
        public virtual string PrepFirstName { get; set; }
        public virtual string PrepMiddle { get; set; }
        public virtual string PrepAddress { get; set; }
        public virtual string PrepCity { get; set; }
        public virtual string PrepState { get; set; }
        public virtual string PrepZip { get; set; }
        public virtual string PrepLastName2 { get; set; }
        public virtual string PrepFirstName2 { get; set; }
        public virtual string PrepMiddle2 { get; set; }
        public virtual string PrepAddress2 { get; set; }
        public virtual string PrepCity2 { get; set; }
        public virtual string PrepState2 { get; set; }
        public virtual string PrepZip2 { get; set; }
        public virtual string PrepLastName3 { get; set; }
        public virtual string PrepFirstName3 { get; set; }
        public virtual string PrepMiddle3 { get; set; }
        public virtual string PrepAddress3 { get; set; }
        public virtual string PrepCity3 { get; set; }
        public virtual string PrepState3 { get; set; }
        public virtual string PrepZip3 { get; set; }
        public virtual string PrepLastName4 { get; set; }
        public virtual string PrepFirstName4 { get; set; }
        public virtual string PrepMiddle4 { get; set; }
        public virtual string PrepAddress4 { get; set; }
        public virtual string PrepCity4 { get; set; }
        public virtual string PrepState4 { get; set; }
        public virtual string PrepZip4 { get; set; }
        public virtual string CCTitle { get; set; }
        public virtual string CCLastName { get; set; }
        public virtual string CCFirstName { get; set; }
        public virtual string dispCCCertdate { get; set; }
        public virtual string CCCertIPAddr { get; set; }
        public virtual string RHLastName { get; set; }
        public virtual string RHFirstName { get; set; }
        public virtual string RHMiddle { get; set; }
        public virtual string dispRHDate { get; set; }
        public virtual string dispRHDocType { get; set; }
        public virtual string RHDocTitle { get; set; }
        public virtual string RHDocNumber { get; set; }
        public virtual string dispRHDocExpDate { get; set; }
        public virtual string RHERName { get; set; }
        public virtual string RHComment { get; set; }
        public virtual bool RHAltAuthorize { get; set; }
        public virtual string RHLastName2 { get; set; }
        public virtual string RHFirstName2 { get; set; }
        public virtual string RHMiddle2 { get; set; }
        public virtual string dispRHDate2 { get; set; }
        public virtual string dispRHDocType2 { get; set; }
        public virtual string RHDocTitle2 { get; set; }
        public virtual string RHDocNumber2 { get; set; }
        public virtual string dispRHDocExpDate2 { get; set; }
        public virtual string RHERName2 { get; set; }
        public virtual string RHComment2 { get; set; }
        public virtual bool RHAltAuthorize2 { get; set; }
        public virtual string RHLastName3 { get; set; }
        public virtual string RHFirstName3 { get; set; }
        public virtual string RHMiddle3 { get; set; }
        public virtual string dispRHDate3 { get; set; }
        public virtual string dispRHDocType3 { get; set; }
        public virtual string RHDocTitle3 { get; set; }
        public virtual string RHDocNumber3 { get; set; }
        public virtual string dispRHDocExpDate3 { get; set; }
        public virtual string RHERName3 { get; set; }
        public virtual string RHComment3 { get; set; }
        public virtual bool RHAltAuthorize3 { get; set; }
        public virtual string Comment { get; set; }
        public virtual bool AltAuthorize { get; set; }
        public string ClientID { get; set; }
        public virtual short? ResidentStatus { get; set; }
        public Nullable<System.DateTime> WorkExpDate { get; set; }
        public Nullable<short> DocA1Type { get; set; }
        public Nullable<System.DateTime> DocA1ExpDate { get; set; }
        public Nullable<short> DocA2Type { get; set; }
        public Nullable<System.DateTime> DocA2ExpDate { get; set; }
        public Nullable<short> DocA3Type { get; set; }
        public Nullable<System.DateTime> DocA3ExpDate { get; set; }
        public Nullable<short> DocBType { get; set; }
        public Nullable<System.DateTime> DocBExpDate { get; set; }
        public Nullable<short> DocCType { get; set; }
        public Nullable<System.DateTime> DocCExpDate { get; set; }
        public Nullable<System.DateTime> RHDate { get; set; }
        public Nullable<System.DateTime> RHDocExpDate { get; set; }
        public Nullable<System.DateTime> CCCertdate { get; set; }
        public Nullable<System.DateTime> CCVerifiedDate { get; set; }
        public Nullable<System.DateTime> EECompleteDate { get; set; }
        public Nullable<System.DateTime> EEVerifiedDate { get; set; }
        public string SSN { get; set; }
    }
    public class FMLALOAGroup
    {
        public string EmployeeName { get; set; }
        public string EmployeeID { get; set; }
        public string Reason { get; set; }
        public string BeginDate { get; set; }
        public string EndDate { get; set; }
        public string Dept { get; set; }
        public string Position { get; set; }
        public bool FMLA { get; set; }
        public bool Approved { get; set; }
        public int Index { get; set; }
    }

    #endregion HR

    #region Quick Lists
    public class EEByCodes : Employee
    {
        //public string Department { get; set; }
        //public string PayRate { get; set; }
        public string AmountPercent { get; set; }
        public string Status { get; set; }
        public string Renewal { get; set; }
        public string License { get; set; }
        public string Effective { get; set; }
        public string Expiration { get; set; }
        public string ContributionAmount { get; set; }
        public string AvailableAmt { get; set; }
        public string AccruedAmt { get; set; }
        //public string Email { get; set; }
    }

    public class EEByDate : Employee
    {
        public string HireDate { get; set; }
        public string NextReview { get; set; }
        public string Birthdate { get; set; }
        public string Anniversary { get; set; }
        public string Term { get; set; }
        public string Email { get; set; }
    }
    public class DepartmentQL : Employee
    {
    }

    public class PaycodeQL : Employee
    {
        public string PayRate1 { get; set; }
        public string PayRecord { get; set; }
    }

    public class HomeStateQL : Employee
    {
    }

    public class DivisionQL : Employee
    {
        public string DivisionID { get; set; }
    }

    public class DeductionQL : Employee
    {
        public string AmountPercent { get; set; }
        public string Deduction { get; set; }
    }

    public class SUTAStateQL : Employee
    {
    }

    public class PositionQL : Employee
    {
    }

    public class BenefitQL : Employee
    {
        public string AmountPercent { get; set; }
        public string Benefit { get; set; }
        public string Year { get; set; }
    }

    public class WorkersCompQL : Employee
    {
    }

    public class BirthDateQL : Employee
    {
        public string BirthDate1 { get; set; }
        public string month { get; set; }
    }

    public class AnniversaryQL : Employee
    {
        public string Anniversary { get; set; }
        public string month { get; set; }
    }

    public class TermQL : Employee
    {
        public string TerminationDate1 { get; set; }
        public string month { get; set; }
    }

    public class OriginalHireDateQL : Employee
    {
        public string OriginalHireDate1 { get; set; }
        public string month { get; set; }
    }

    public class DateOfNextReviewQL : Employee
    {
        public string DateOfNextReview1 { get; set; }
        public string month { get; set; }
    }

    public class TrainingQL : Employee
    {
        public string RenewalDate1 { get; set; }
        public string TrainingStatus { get; set; }
        public string TrainingType { get; set; }
    }

    public class PTOQL : Employee
    {
        public string AvailableHours { get; set; }
        public string PTOAccrualAmount { get; set; }
        public string PTOAccruedAmount { get; set; }
        public string PTOType { get; set; }
        public int PTOTypeInt { get; set; }
    }


    public class LicenseQL : Employee
    {
        public string License { get; set; }
        public string Effective { get; set; }
        public string Expiration { get; set; }
        public string LicenseCertificationID { get; set; }
    }

    public class EE401kQL : Employee
    {
        public string ContributionAmount { get; set; }
        public string PlanName { get; set; }
        public string Deduction { get; set; }
    }

    public class EEInfoQL
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string EmployeeName { get; set; }
        public string SSN { get; set; }
        public string EmployeeID { get; set; }
        public string Position { get; set; }
        public bool Inactive { get; set; }
        public string Birthdate { get; set; }
        public string Gender { get; set; }
        public string EthnicOrigin { get; set; }
        public string Department { get; set; }
        public string StartDate { get; set; }
        public string DateEmployeeInactivated { get; set; }
        public string ReasonEmployeeInactivated { get; set; }
        public string SUTAState { get; set; }
        public string WorkersComp { get; set; }
        public bool Handicapped { get; set; }
        public bool Veteran { get; set; }
        public bool VietnamVeteran { get; set; }
        public bool DisabledVeteran { get; set; }
        public bool UnionEmployee { get; set; }
        public bool Smoker { get; set; }
        public bool Citizen { get; set; }
        public bool Verified { get; set; }
        public string I9renew { get; set; }
        public string UnionCode { get; set; }
        public string RateClass { get; set; }
        public string FederalClassificationCode { get; set; }
        public bool OtherVeterans { get; set; }
        public string MilitaryDischargeDate { get; set; }
        public string StatusCode { get; set; }
        public bool ArmedForcesServiceMedalVeteran { get; set; }
        public bool RecentlySeparatedVeteran { get; set; }
        public string EmergencyContact { get; set; }
        public string EmergencyPhone { get; set; }
        public string DriversLicense { get; set; }
        public string LicenseState { get; set; }
        public string _401kHireDate { get; set; }
        public string OriginalHireDate { get; set; }
        public string Email { get; set; }
        public string Citizenship { get; set; }
        public bool I9Verified { get; set; }
        public string I9ReverificationDue { get; set; }
        public string VisaType { get; set; }
        public string VisaExpirationDate { get; set; }
        public string MilitaryServiceCode { get; set; }
        public string HandicappedCode { get; set; }
        public string HighCompensationDescription { get; set; }
        public string SeniorityDate { get; set; }
        public string NextReviewDate { get; set; }
        public bool IndependentContractor { get; set; }
        public string EmploymentType { get; set; }
        public string WorksiteID { get; set; }
        public bool SeasonalEmployee { get; set; }
        public bool CorporateOfficer { get; set; }
        public string CorporateOfficerEarnings { get; set; }
        public string WorksiteLocation { get; set; }
        public string RehireDate { get; set; }
        public string ResidentState { get; set; }
        public bool UseEmployeeWorkAddress { get; set; }
        public string ACAEmployeeStatus { get; set; }
        public bool ACACoverageOffered { get; set; }
        public bool ACASeasonalEmployee { get; set; }
        public bool ACAVariableEmployee { get; set; }
        public string ACAAdjustmentHours { get; set; }
        public bool ACAUseAdjustmentHoursOnly { get; set; }
        public string ACAOffsetMethod { get; set; }
        public string TerminationDate { get; set; }
        public string DepartmentDescription { get; set; }
        public string PositionDescription { get; set; }
    }

    public class OrgChartQL : Employee
    {
        public string SupervisorName { get; set; }
        public string Division { get; set; }
        public string Group { get; set; }
    }

    public class QuicklistGroup
    {
        public string GroupName { get; set; }
        public int Month { get; set; }
        public string GroupDisplayName { get; set; }
        public string GroupDescription { get; set; }
    }

    public class QuicklistGroupUsers
    {
        public string UID { get; set; }
        public string GroupName { get; set; }
        public string GroupDescription { get; set; }
    }
    #endregion


    public partial class OBDocument
    {
        public bool HasMappedFields { get; set; }
    }

    public class i9Employees
    {
        public string EmployeeID { get; set; }
    }
    public class w4Employees
    {
        public string EmployeeID { get; set; }
    }

    [Serializable]
    public partial class EmployeePaycode
    {
        public virtual string PaycodeDescription { get; set; }
    }

    [Serializable]
    public partial class EmployeeLocalTax
    {

    }

    [Serializable]
    public partial class EmployeeStateTax
    {

    }

    [Serializable]
    public partial class EmployeeBenefit
    {
        public virtual string Description { get; set; }
    }

    [Serializable]
    public partial class EmployeeDeduction
    {
        public virtual string Description { get; set; }

        public virtual EmployeeDeduction Deductions { get; set; }
    }

    public partial class EmployeeBusinessExpens
    {
        public string EmployeeName { get; set; }
    }
    #region EmployeeGrid
    public class EmployeeMasterView
    {
        public virtual int CompanyID { get; set; }

        public virtual string EmployeeID { get; set; }
        public virtual string dispEmployeeID { get; set; }

        public virtual bool? Inactive { get; set; }

        public virtual string StrGender { get; set; }

        public virtual Boolean BlnInactive { get; set; }

        public virtual string LastName { get; set; }

        public virtual string FirstName { get; set; }

        public virtual string MiddleName { get; set; }

        public virtual string SSN { get; set; }

        public virtual DateTime? BirthDate { get; set; }

        public virtual byte? Gender { get; set; }

        public virtual byte? EthnicOrigin { get; set; }

        public virtual string Department { get; set; }

        public virtual string Position { get; set; }

        public virtual DateTime? StartDate { get; set; }

        public virtual string DnetUserID { get; set; }

        public virtual string EmergencyPhone { get; set; }

        public virtual DateTime? OriginalHireDate { get; set; }

        public virtual string Email { get; set; }

        public virtual bool I9Versified { get; set; }


        public virtual string EmployeePicture { get; set; }

        public string dispName { get; set; }
        public string dispDept { get; set; }
        public string dispPos { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string EmployeementStatus { get; set; }
        public string dispBirthDate { get; set; }
        public string dispStartDate { get; set; }
        public string dispOriginalHireDate { get; set; }
        public string dispDOH { get; set; }
        public string phone1 { get; set; }
        public string phone2 { get; set; }
        public string phone3 { get; set; }
        public string DateEmpInactivated { get; set; }
        public string InactivatedReason { get; set; }
        public string JobCategory { get; set; }
        public string WorkSiteID { get; set; }
        public string WorkSiteLocation { get; set; }
        public string EmployeeWorkAddress { get; set; }
        public string TerminationDate { get; set; }
        public string dispEthnicity { get; set; }
        public string WorkStatus { get; set; }
    }

    public class EmployeeMasterSnapView
    {
        public int CompanyID { get; set; }

        public virtual string EmployeeID { get; set; }
        public virtual string Department { get; set; }

        public virtual string Position { get; set; }

        public virtual string LastName { get; set; }

        public virtual string FirstName { get; set; }


        public virtual string SSN { get; set; }

        public virtual DateTime? BirthDate { get; set; }

        public virtual DateTime? OriginalHireDate { get; set; }

        public string Address1 { get; set; }
        public string Address2 { get; set; }


        public virtual string EmployeePicture { get; set; }

        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string EmployeementStatus { get; set; }
    }



    public class EmployeeBenefitsView
    {
        public virtual string EmployeeID { get; set; }

        public virtual string Benefit { get; set; }

        public virtual bool Inactive { get; set; }

        public virtual string Description { get; set; }

        public virtual byte? BenefitMethod { get; set; }


        public virtual decimal? BenefitAmount1 { get; set; }
        public virtual string Amount { get; set; }

        public string TypeDesc { get; set; }


    }
    public class EmployeePaycodesView
    {
        public virtual string EmployeeID { get; set; }

        public double Hours { get; set; }
        public decimal Amount { get; set; }

        public virtual string PaycodeDescription { get; set; }

        public virtual string PayRecord { get; set; }

        public virtual byte? PayType { get; set; }

        public virtual decimal? PayRateAmount { get; set; }

        public virtual string PayUnit { get; set; }

        public string PayTypeDesc { get; set; }
        public decimal YTD { get; set; }
    }
    public class EmployeeDeductionsView
    {
        public virtual string Description { get; set; }

        public virtual string EmployeeID { get; set; }

        public virtual string Deduction { get; set; }

        public virtual bool Inactive { get; set; }

        public virtual byte? DeductionMethod { get; set; }

        public virtual decimal? DeductionAmount1 { get; set; }

        public string TypeDesc { get; set; }

        public virtual string Amount { get; set; }

    }
    public class EmployeeTimePunchesView
    {
        public virtual int ID { get; set; }

        public virtual string ClientID { get; set; }

        public virtual string EmployeeID { get; set; }

        public virtual string EmployeeName { get; set; }

        public virtual DateTime? PunchDay { get; set; }

        public virtual string DayType { get; set; }

        public virtual string PunchIn { get; set; }

        public virtual string PunchOut { get; set; }

        public virtual decimal? WorkHours { get; set; }

        public virtual decimal? AdjustTime { get; set; }

        public virtual string AdjustDescr { get; set; }

        public virtual decimal? AdjustHours { get; set; }

        public virtual string Department { get; set; }
        public virtual string GeoPunchIn { get; set; }
        public virtual string GeoPunchOut { get; set; }
        public virtual short Source { get; set; }
    }

    public class EmployeeCheckHistoryView
    {
        public virtual DateTime? CheckDate { get; set; }

        public virtual string EmployeeID { get; set; }

        public virtual string Department { get; set; }

        public virtual decimal? GrossWagesPayRun { get; set; }

        public virtual decimal? TotalDeductions { get; set; }

        public virtual decimal? TotalBenefits { get; set; }

        public virtual decimal? NetWagesPayRun { get; set; }

        public virtual decimal? TotalTaxes { get; set; }

        public virtual int? Year { get; set; }
        public string AuditControlCode { get; set; }

        public string CheckNumber { get; set; }
        public int PaymentAdjustmentNumber { get; set; }
        public string EmployeeName { get; set; }
        public string SSN { get; set; }
        public DateTime? PayPeriodStart { get; set; }
        public DateTime? PayPeriodEnd { get; set; }
        public decimal NonGross { get; set; }
        public decimal TotalState { get; set; }
        public decimal TotalWC { get; set; }
        public decimal FicaSS { get; set; }
        public decimal FicaM { get; set; }
        public decimal Futa { get; set; }
        public bool Voided { get; set; }

    }
    #endregion

    public class EmployeeCodes
    {
        public string EmployeeID { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public decimal? PayRate { get; set; }
        public int Frequency { get; set; }
        public string FrequencyName { get; set; }
        public double YTD { get; set; }
        public double Exemptions { get; set; }
        public decimal? Additional { get; set; }
        public decimal Percent { get; set; }
        public decimal Amount { get; set; }
        public string AccountType { get; set; }
        public string Option { get; set; }
        public string payRecord { get; set; }
        public string record_id { get; set; }
        public string FilingStatus { get; set; }
        public bool Inactive { get; set; }
        public string AccountNumber { get; set; }
        public string Routing { get; set; }
        public string BasedOn { get; set; }
        public bool? CanDelete { get; set; }
    }

    public class EmployeeCodeHist
    {
        public DateTime? CheckDate { get; set; }
        public string CheckNumber { get; set; }
        public decimal PayRate { get; set; }
        public decimal Hours { get; set; }
        public decimal AmountPaid { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }

    }

    public class EmployeePlanHistory
    {
        public DateTime? CheckDate { get; set; }
        public string CheckNumber { get; set; }
        public decimal Deduction { get; set; }
        public decimal Benefit { get; set; }
    }
    public class EmployeePTOHist
    {
        public string EmployeeName { get; set; }
        public string EmployeeID { get; set; }
        public string dispEmployeeID { get; set; }
        public string dispPTOTYpe { get; set; }
        public DateTime? CheckDate { get; set; }
        public string CheckNumber { get; set; }
        public string Department { get; set; }
        public string dispDepartment { get; set; }
        public decimal Hours { get; set; }
    }

    #region Dashboard
    public class DBrdLineGraph
    {
        public string Group { get; set; }
        public List<string> SeriesName { get; set; }
        public List<decimal> Values { get; set; }
    }
    public class DBrdEmployeeType
    {
        public int FullTime { get; set; }
        public int PartTime { get; set; }
        public int TempTime { get; set; }
        public int OtherTime { get; set; }

    }
    public class DBrdBarGraph
    {
        public List<string> SeriesName { get; set; }
        public List<decimal> Value { get; set; }
    }
    public class DBrdExecOverview
    {
        public string Title { get; set; }
        public string Last { get; set; }
        public string MTD { get; set; }
        public string QTD { get; set; }
        public string YTD { get; set; }
    }

    public class DbrdEEPTo
    {
        public decimal Accrued { get; set; }
        public string Plan { get; set; }
        public byte Type { get; set; }
        public decimal Available { get; set; }
        public decimal Taken { get; set; }
        public decimal Pending { get; set; }
        public decimal Approved { get; set; }
    }
    public class DBrdEmployeeList
    {
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string SSN { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string EmployeePicture { get; set; }
        public string DepartmentDesc { get; set; }
        public string PositionDesc { get; set; }
    }
    public class DBrdLastInvoice
    {
        public Int32 InvoiceNumber { get; set; }
        public string InvDate { get; set; }
        public string Amount { get; set; }
    }
    public class DBrdLastCheck
    {
        public string CheckNumber { get; set; }
        public string CheckDate { get; set; }
        public string Net { get; set; }
        public string PaymentNumber { get; set; }
    }
    public class DBrdInvoiceList
    {
        public Int32 InvoiceID { get; set; }
        public Int32 InvoiceNumber { get; set; }
        public string InvDate { get; set; }
        public string Amount { get; set; }
        //public string TotalChecks { get; set; }
        public string TotalEEsPaid { get; set; }

    }
    public class DBrdPayrollSchedule
    {
        public string ProcessDate { get; set; }
        public string CheckDate { get; set; }
        public string PayPeriodBegin { get; set; }
        public string PayPeriodEnd { get; set; }
    }
    public class DbrdEECheckList
    {
        public string EmployeeID { get; set; }
        public int PaymentAdjustmentNumber { get; set; }
        public string CheckNumber { get; set; }
        public string CheckDate { get; set; }
        public string GrossWages { get; set; }
        public string NetWages { get; set; }
        public string TotalDeductions { get; set; }
        public string TotalTaxes { get; set; }
        public string TotalBenefits { get; set; }
        public string AuditControlCode { get; set; }
        public DateTime realCheckDate { get; set; }
        public bool Voided { get; set; }
    }

    public class DbrdGWTrend
    {
        public int CheckDate { get; set; }
        public decimal? WageAmount { get; set; }

    }
    #endregion

    #region ACA
    public class ACADashboard
    {
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string SSN { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public string Status { get; set; }
        public string Affordable { get; set; }
        public string CoverageOffered { get; set; }
        public double PeriodPlanAmount { get; set; }
        public double PeriodEEWages { get; set; }
        public double ACAPercOfWages { get; set; }

    }
    public class ACAHoursDetail
    {
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public decimal HoursAmount1 { get; set; }
        public decimal HoursAmount2 { get; set; }
        public decimal HoursAmount3 { get; set; }
        public decimal HoursAmount4 { get; set; }
        public decimal HoursAmount5 { get; set; }
        public decimal HoursAmount6 { get; set; }
        public decimal HoursAmount7 { get; set; }
        public decimal HoursAmount8 { get; set; }
        public decimal HoursAmount9 { get; set; }
        public decimal HoursAmount10 { get; set; }
        public decimal HoursAmount11 { get; set; }
        public decimal HoursAmount12 { get; set; }
    }

    public class ACALookback
    {
        public string EmployeeName { get; set; }
        public string EmployeeID { get; set; }
        public double ReportedHours { get; set; }
        public double AverageHours { get; set; }
        public string Status { get; set; }
        public string CoverageOffered { get; set; }
    }
    public class ACAHoursAnalysis
    {
        public string EmployeeName { get; set; }
        public string EmployeeID { get; set; }
        public string SSN { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }
        public double Hours { get; set; }
        public string Status { get; set; }
        public string Approaching { get; set; }
        public string Over { get; set; }
    }
    #endregion

    #region ClientList
    public class ClientList
    {
        public string Logo { get; set; }
        public bool Active { get; set; }
        public string ClientID { get; set; }
        public string Name { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Company { get; set; }
        public int? UserAccessCount { get; set; }
        public bool IsACHEnabled { get; set; }
        public bool AdminOnly { get; set; }
    }

    public partial class ClientUser
    {
        public string ClientID { get; set; }
        public string UserID { get; set; }
        public string Name { get; set; }
        public string RoleID { get; set; }
        public DateTime? LastLogin { get; set; }
        public string EmployeeID { get; set; }
        public string Email { get; set; }

    }

    public class ClientEditVM
    {
        public string ClientID { get; set; }
        public string CompanyID { get; set; }
        public string MainContact { get; set; }
        public string Title { get; set; }
        public string CTPhone1 { get; set; }
        public string CTPhone2 { get; set; }
        public string CTEmail1 { get; set; }
        public string CTEmail2 { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public DateTime? StartDate { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Fax { get; set; }
        public string Website { get; set; }
        public string Email { get; set; }

    }
    #endregion

    #region InvoiceList
    public class InvoiceDetailsVM //1/15/2018 DS TFS # 2981
    {
        public virtual DateTime? StartDate { get; set; }
        public virtual DateTime? EndDate { get; set; }
        public decimal? Tax { get; set; }
        public int Employees { get; set; }
        public int Checks { get; set; }
        public decimal TotalFicaM { get; set; }
        public decimal TotalFicaSS { get; set; }
        public decimal TotalFUTA { get; set; }
        public decimal TotalSUTA { get; set; }
        public decimal TotalWC { get; set; }
        public decimal TotalBenefits { get; set; }
        public decimal TotalCredits { get; set; }
        public decimal TotalFees { get; set; }
        public decimal TotalNonGross { get; set; }
        public decimal TotalGross { get; set; }
        public decimal TotalNet { get; set; }
        public decimal AmountRemaining { get; set; }
        public string StartDateDisplay { get; set; }
        public string EndDateDisplay { get; set; }
        private List<InvoiceDetailsVM> _invoiceDetails = new List<InvoiceDetailsVM>();
        public virtual List<InvoiceDetailsVM> invoiceDetails
        {
            get
            {
                return _invoiceDetails;
            }
        }
    }
    public class InvoiceVM
    {
        public virtual int CompanyID { get; set; }

        public virtual string ClientID { get; set; }

        public virtual int DarwinInvoiceNumber { get; set; }

        public virtual byte? Posted { get; set; }

        public virtual string UserID { get; set; }

        public virtual DateTime? Time { get; set; }

        public virtual DateTime? Date { get; set; }

        public virtual decimal? Total { get; set; }

        public virtual decimal? Tax { get; set; }

        public virtual decimal? GrandTotal { get; set; }

        public virtual string InvoiceNumber { get; set; }

        public virtual DateTime? CheckDate { get; set; }

        public virtual DateTime? StartDate { get; set; }

        public virtual DateTime? EndDate { get; set; }

        public virtual int? Employees { get; set; }

        public virtual decimal? NetWagesPayRun { get; set; }

        public virtual decimal? FederalWagesPayRun { get; set; }

        public virtual decimal? TotalStateTax { get; set; }

        public virtual decimal? TotalLocalTax { get; set; }

        public virtual decimal? TotalDeductions { get; set; }

        public virtual string CommentArray1 { get; set; }

        public virtual string CommentArray2 { get; set; }

        public virtual string CommentArray3 { get; set; }

        public virtual string CommentArray4 { get; set; }

        public virtual string Comment1 { get; set; }

        public virtual string Comment2 { get; set; }

        public virtual byte? UseBenCredits { get; set; }

        public virtual byte? UseDedCredits { get; set; }

        public virtual byte? ChecksASOPays { get; set; }

        public virtual byte? DepositsASOPays { get; set; }

        public virtual byte? PassFICA { get; set; }

        public virtual byte? PassFUTA { get; set; }

        public virtual byte? PassSUTA { get; set; }

        public virtual decimal? ClientBalance { get; set; }

        public virtual string DivisionID { get; set; }

        public virtual byte? PreviewInvoice { get; set; }

        public virtual DateTime? DebitDate { get; set; }

        public virtual byte? UseDirectDebitDateForAll { get; set; }

        public virtual string CustomerPONumber { get; set; }

        public virtual int InvoiceID { get; set; }

        public decimal AmountPaid { get; set; }
        public decimal AmountRemaining { get; set; }
        public string DatePaidOff { get; set; }
        public decimal TotalFicaM { get; set; }
        public decimal TotalFicaSS { get; set; }
        public decimal TotalFUTA { get; set; }
        public decimal TotalSUTA { get; set; }
        public decimal TotalWC { get; set; }
        public decimal TotalBenefits { get; set; }
        public decimal TotalCredits { get; set; }
        public decimal TotalFees { get; set; }
        public decimal TotalNonGross { get; set; }
        public decimal TotalGross { get; set; }
        public string DateDisplay { get; set; }  //09/26/2017 DS TFS # 2793
        public string CheckDateDisplay { get; set; } //09/26/2017 DS TFS # 2793
        public string StartDateDisplay { get; set; } //09/26/2017 DS TFS # 2793
        public string EndDateDisplay { get; set; }   //09/26/2017 DS TFS # 2793
        public bool Voided { get; set; }

        public virtual Client Clients { get; set; }

        //private IList<ClientInvoicePayrolls> _clientInvoicePayrolls = new List<ClientInvoicePayrolls>();
        //public virtual IList<ClientInvoicePayrolls> ClientInvoicePayrolls
        //{
        //    get
        //    {
        //        return this._clientInvoicePayrolls;
        //    }
        //}

        private IList<InvoicePayroll> _invoicePayrolls = new List<InvoicePayroll>();
        public virtual IList<InvoicePayroll> InvoicePayrolls
        {
            get
            {
                return _invoicePayrolls;
            }
        }

    }

    public class InvoicePayCodesVM
    {
        public string payrollCode { get; set; }
        public string description { get; set; }
        public decimal payrollCodeTotal { get; set; }
    }

    [Serializable]
    public partial class Invoice
    {

    }

    [Serializable]
    public partial class InvoicePayroll
    {

    }
    #endregion

    #region ClientContacts
    public class ClientContactsVM
    {
        public string dispDepartment { get; set; }
        public string photoURL { get; set; }
        public int Id { get; set; }
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string ContactType { get; set; }
        public string ContactName { get; set; }
        public string EmployeeID { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Email { get; set; }
        public bool NotAnEmployee { get; set; }
        public string Department { get; set; }
        public string imgFolder { get; set; }
        public string website { get; set; }
    }
    public class ClientContactTypesVM
    {
        public string ContactType { get; set; }
        public int id { get; set; }

    }

    public class ClientPTOTypesVM
    {
        public string PTOPlan { get; set; }
        public string Description { get; set; }
        public bool Accrue { get; set; }
        public bool AllowCarryOver { get; set; }
        public string AccrualMethod { get; set; }
        public int MaxCarry { get; set; }
        public int AccrualAmount { get; set; }
        public int AccrualHoursPerYear { get; set; }
        public int MaxAccrualYTD { get; set; }
        public string AnniversaryMethod { get; set; }
    }
    #endregion

    #region ACH

    public partial class ClientACHHeader : ITransferInfo
    {
        public bool ManualVoided { get => false; set { throw new NotImplementedException(); } }
    }

    public partial class ACHSetup : IBatchHeaderInvoice
    {
        public string ACHCompanyName { get => CompanyName; set => CompanyName = value; }
        public string TW_Recv_Key { get => ReceivablesKey; set => ReceivablesKey = value; }
    }

    #endregion

    public partial class UserRoleMenuAccess
    {
        public bool FullAccess { get; set; }
        public bool NoAccess { get; set; }
        public bool DisplayOnly { get; set; }
        public string menuItemName { get; set; }
        public int ParentID { get; set; }
    }

    public class DarwinetSetupVM
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public bool UseDivision { get; set; }
        public bool UseDepartment { get; set; }
        public bool UseSupervisor { get; set; } //Task#2056
        public bool UseDocuments { get; set; }
    }

    public class CompanySecurityVM
    {
        public int ID { get; set; }
        public int CompanyID { get; set; }
        public string CompanyName { get; set; }
        public string InterCompID { get; set; }
        public string RoleID { get; set; }
        public bool OverrideMenu { get; set; }
    }
    public partial class UserRoleMenuAccessOverride
    {
        public bool FullAccess { get; set; }
        public bool NoAccess { get; set; }
        public bool DisplayOnly { get; set; }
        public string menuItemName { get; set; }
        public int ParentID { get; set; }
        public int mid { get; set; }
    }
    public class ClientSecurityVM
    {
        public int ID { get; set; }
        public int CompanyID { get; set; }
        public string ClientName { get; set; }
        public string InterCompID { get; set; }
        public string ClientID { get; set; }
        public string RoleID { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public bool OverrideMenu { get; set; }
        public int SecurityModel { get; set; }
        public bool IsAdmin { get; set; }
        public bool IsPayroll { get; set; }
    }
    public class EEW2View
    {
        public string EmployeeID { get; set; }
        public int ReportingYear { get; set; }
        public decimal WagesTipsOthers { get; set; }
        public decimal FederalTax { get; set; }
        public decimal SocSecWages { get; set; }
        public decimal SocSecTax { get; set; }
        public decimal MedicareWages { get; set; }
        public decimal MedicareTax { get; set; }
        public string Form1095C { get; set; }
        public string Form1095B { get; set; }
    }

    [Serializable]
    public partial class MenuItem
    {
        public bool HasChildren { get; set; }
        public List<MenuItem> Children { get; set; }
    }

    public partial class UserDeptDivSecurity
    {
        public string DisplayName { get; set; }
    }

    //public partial class UserSupervisorSecurityVM
    //{
    //    public int CompanyID { get; set; }
    //    public string ClientID { get; set; }
    //    public string UserID { get; set; }
    //    public string UserID { get; set; }
    //    public string EmployeeID { get; set; }
    //    public string EmployeeName { get; set; }
    //    public string Department { get; set; }
    //    public string Position { get; set; }
    //}

    public partial class OBClientSetup
    {
        public int eeOnBoarding { get; set; }
    }
    public partial class OBProfileTaskDocumentMapping
    {
        public string DisplayField { get; set; }
        public string DisplayTable { get; set; }

    }
    public partial class OBClientSetupDocumentMapping
    {
        public string DisplayField { get; set; }
        public string DisplayTable { get; set; }

    }

    public class Paystub_Earnings
    {
        public string Code;
        public List<Paystub_EarningData> Details;
        public int? Count;
    }

    public class Paystub_EarningData
    {
        public decimal? Rate;
        public string Department;
        public string Position;
    }

    public class Paystub_EarningGroupBy
    {
        public const byte Nothing = 0;
        public const byte Rate = 1;
        public const byte Department = 2;
        public const byte RateDepartment = 3;
        public const byte Position = 4;
        public const byte RatePosition = 5;
        public const byte DepartmentPosition = 6;
        public const byte All = 7;
    }

    public class CheckStubTransactionRecord
    {
        public int seq { get; set; }
        public string code { get; set; }
        public string Description { get; set; }
        public short recType { get; set; }
        public decimal hours { get; set; }
        public decimal amount { get; set; }
        public decimal rate { get; set; }
        public decimal ytd { get; set; }
        public string department { get; set; }
        public string position { get; set; }
    }

    public class CheckStubPTORecord  // 06/30/2018 DS TFS # 3282
    {
        public int seq { get; set; }
        public string code { get; set; }
        public string Description { get; set; }
        public short recType { get; set; }
        public decimal accrued { get; set; }
        public decimal available { get; set; }
        public decimal taken { get; set; }
        public decimal accruedYTD { get; set; }
        public decimal takenYTD { get; set; }
    }

    public class Code_Desc_Amount
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
    }
    public class Link_Code_Desc_Amount
    {
        public string Link { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
    }
    [Serializable]
    public partial class EmployeeTransactionHistory
    {
        public decimal totalRate { get; set; }
        public string ClientID { get; set; }
    }
    public class ClientTaxes
    {
        public int DarwinInvoiceNumber { get; set; }
        public int PayrollRecordType { get; set; }
        public decimal Amount { get; set; }
    }
    public class ClientPayrollScheduleVM
    {
        public string ScheduleID { get; set; }
        // 06/20/2017 DS TFS# 2603: changed DateTime fields to string for the ViewModel
        //public DateTime CheckDate { get; set; }
        //public DateTime PPBegin { get; set; }
        //public DateTime PPEnd { get; set; }
        //public DateTime TS_Due { get; set; }
        public string CheckDate { get; set; }
        public string PPBegin { get; set; }
        public string PPEnd { get; set; }
        public string TS_Due { get; set; }
        public int Status { get; set; }
        public string Responsible { get; set; }
        public string DivisionID { get; set; }
        public int DarwinInvoiceNumber { get; set; }
        public decimal InvoiceTotal { get; set; }
        public decimal GrossWages { get; set; }
        public int TotalChecks { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal TotalTaxes { get; set; }
        public decimal TotalBenefits { get; set; }
        public decimal TotalFees { get; set; }
        public string dispStatus { get; set; }
        public string BuildID { get; set; }
        public string ProfileID { get; set; }
        public string PayrollNumber { get; set; }
    }

    public class EESummaryCode
    {

        public string code { get; set; }
        public string RateAmount { get; set; }
        public string Total { get; set; }
        public string MTD { get; set; }
        public string QTD { get; set; }
        public string YTD { get; set; }
        public string Description { get; set; }

        public string Hours { get; set; }
    }
    public class PayrollHoursByType
    {
        public string Department { get; set; }
        public string DepartmentName { get; set; }
        public string Position { get; set; }
        public decimal GrossWages { get; set; }

    }
    public class PayrollHoursByTypeA
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public decimal Value { get; set; }
    }
    public class PayrollWagesComp
    {
        public string Series { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public decimal Value { get; set; }
    }
    public class PayrollHoursByTypeComp
    {
        public string Series { get; set; }
        public string Group { get; set; }
        public string Code { get; set; }
        public decimal Value { get; set; }
    }
    public class ClientReportingLineChart
    {
        public DateTime thedate { get; set; }
        //public decimal GrossWages { get; set; }
        public decimal Value { get; set; }
    }
    public class PEOReportingLineChart
    {
        public DateTime thedate { get; set; }
        //public decimal GrossWages { get; set; }
        public decimal value { get; set; }
    }
    public class ClientReportingLineChartB
    {
        public string thedate { get; set; }
        //public decimal GrossWages { get; set; }
        public decimal Value { get; set; }
    }
    public class ClientReportingMultiLine
    {
        public DateTime thedate { get; set; }
        public string Code { get; set; }
        public string Description { get; set; }
        public decimal Value { get; set; }

    }
    public class PayrollHoursByTypeB
    {
        public string Code { get; set; }
        public string Group { get; set; }
        public decimal Value { get; set; }
    }
    public class PayrollHoursByTypeC
    {
        public string Group { get; set; }
        public decimal Regular { get; set; }
        public decimal Overtime { get; set; }
        public decimal PTO { get; set; }
        public decimal Other { get; set; }
    }
    public partial class DarwinetNote
    {
        public string AttachmentName { get; set; }
        public string FileName { get; set; }
        public string DocumentType { get; set; }
        public string IconType { get; set; }
        public bool canChange { get; set; }
    }
    public partial class LibraryDocument
    {
        public string IconType { get; set; }
        public string AllowEEToSee { get; set; }
    }
    public class News
    {
        public const int All = 0;
        public const int Clients = 1;
        public const int Users = 3;
        public const int Employees = 2;
    }
    public class NewsLevels
    {
        public const byte Client = 1;
        public const byte Employee = 2;
        public const int Users = 3;

    }


    public partial class NewsItem
    {
        public int NewsItemID { get; set; }
        public string Active { get; set; }
        public bool CanDelete { get; set; }
    }
    public class PEOFeesModel
    {
        //public int CompanyID { get; set; }
        //public string CompanyName { get; set; }
        //public string ClientID { get; set; }
        //public string ClientName { get; set; }
        //public decimal Total { get; set; }
        public string Series { get; set; }
        public string Code { get; set; }
        public decimal Value { get; set; }
    }
    public class PEOChecksModel
    {
        public string Series { get; set; }
        public string Detail { get; set; }
        public decimal Value { get; set; }
    }
    public class PEOPayrollModel
    {
        public string Series { get; set; }
        public string Code { get; set; }
        public decimal Value { get; set; }
    }
    public class PEOPayrollPayroll
    {
        public string Series { get; set; }
        public string Code { get; set; }
        public int Value { get; set; }
        public int Voided { get; set; }
        public int Manual { get; set; }
    }

    public class EEDocLibraryView
    {
        public string DocEmployee { get; set; }
        public List<EEDocView> Documents { get; set; }
        public List<EEDocGroup> DocGroups { get; set; }
        public List<EEDocGroupType> DocTypeGroups { get; set; }
        public List<EEDocGroupEmps> DocEmplGroups { get; set; }
        public List<EEDocGroupClients> DocClientGroups { get; set; }
        public List<EEDocSources> DocSourses { get; set; }

    }

    public class DocSearchParameters
    {
        public string Document { get; set; }
        public string DocumentType { get; set; }
        public string Client { get; set; }
        public string Employee { get; set; }
        public string Folder { get; set; }
    }

    [Serializable]
    public class EEDocView
    {
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public string EmployeeID { get; set; }
        public string DocName { get; set; }
        public string Foldername { get; set; }
        public string Related { get; set; }
        public string Subject { get; set; }
        public bool AllowEEToSee { get; set; }
        public string SavedDocName { get; set; }
        public int AttachmentID { get; set; }
        public string NoteText { get; set; }
        public DateTime NoteDate { get; set; }
        public int Id { get; set; }
        public string IconType { get; set; }
        public string DocType { get; set; }
        public string EmployeeName { get; set; }
        public string OwnedDoc { get; set; }
        public string Source { get; set; }
        public string dispNoteDate { get; set; }
        public bool Selected { get; set; }
        public bool Used { get; set; }
    }

    public class EEDocGroup
    {
        public string DocName { get; set; }
        public string DocUploadName { get; set; }
        public int DocId { get; set; }
        public string DocType { get; set; }
    }

    public class EEDocSources
    {
        public int FolderID { get; set; }
        public string FolderName { get; set; }
        public string Source { get; set; }
    }

    public class EEDocGroupEmps
    {
        public string EmployeeName { get; set; }
        public string EmployeeID { get; set; }
    }

    //08/30/2017 DS TFS # 2759
    public class EEDocGroupClients
    {
        public string ClientName { get; set; }
        public string ClientID { get; set; }
    }

    public class EEDocGroupType
    {
        public string Type { get; set; }
        public int ID { get; set; }
    }

    public class EEDocGroupEmpsItems
    {
        public string AttachmentName { get; set; }
        public int AttachmentID { get; set; }
    }

    public class DbrdLineGraph
    {
        public string SeriesName { get; set; }
        //public decimal GrossWages { get; set; }
        public decimal Value { get; set; }
    }
    public class PEOReportingBarChart
    {
        public string Series { get; set; }
        public string Code { get; set; }
        public decimal Value { get; set; }
    }

    public partial class DarwinetSetup
    {
        public bool chkPREnabled { get; set; }
        public bool chkPRDepEnabled { get; set; }
        public int chkPRProfileID { get; set; }
    }

    [Serializable]
    public partial class User
    {
        public int UserDefId { get; set; }
        public string ClientID { get; set; }
        public bool Level { get; set; }
        public bool LoginAsEnabled { get; set; }
        public bool MenuAccess { get; set; }
        public bool UserLoginAs { get; set; }
    }

    public class CurrentUser
    {
        public string userID { get; set; }
        public string userIP { get; set; }
        public string name { get; set; }
        public string email { get; set; }
        public string phone { get; set; }
        public int? CompanyID { get; set; }
        public string company { get; set; }
        public string client { get; set; }
    }

    public partial class ActiveUser
    {
        public string userID { get; set; }
        public string name { get; set; }
        public string clientID { get; set; }
        public DateTime? LastLogin { get; set; }
        public string employeeID { get; set; }
        public string email { get; set; }

    }

    public partial class PTORequest
    {
        public string dispStatus { get; set; }
        public string dispType { get; set; }
    }


    public class ChangeRequestsVM
    {
        public virtual int CompanyID { get; set; }

        public virtual int RequestID { get; set; }

        public virtual System.Nullable<System.Char> RequestOrigin { get; set; }

        public virtual bool RequestProcess { get; set; }

        public virtual bool RequestExport { get; set; }

        public virtual System.Nullable<System.Char> RequestType { get; set; }
        public string dispRequestType { get; set; }
        public string dispOrigin { get; set; }
        public string dispStatus { get; set; }
        public virtual DateTime? RequestDate { get; set; }

        public virtual DateTime? RequestTime { get; set; }

        public virtual string ClientID { get; set; }
        public virtual string EmployeeID { get; set; }

        public virtual byte? AssignmentType { get; set; }

        public virtual string TableName { get; set; }

        public virtual string Key1 { get; set; }

        public virtual string Key2 { get; set; }

        public virtual string LogonID { get; set; }

        public virtual string TransferUserID { get; set; }

        public virtual DateTime? TransferDate { get; set; }

        public virtual DateTime? TransferTime { get; set; }

        public virtual byte? RequestStatus { get; set; }

        public virtual string ProcessUserID { get; set; }

        public virtual DateTime? ProcessDate { get; set; }

        public virtual DateTime? ProcessTime { get; set; }

        public virtual string RequestNote { get; set; }

        public virtual string ResponseNote { get; set; }

        //private IList<ChangeRequestDetails> _changeRequestDetails = new List<ChangeRequestDetails>();
        //public virtual IList<ChangeRequestDetails> ChangeRequestDetails
        //{
        //    get
        //    {
        //        return this._changeRequestDetails;
        //    }
        //}
    }


    public class ChangeRequestDetailsVM
    {
        public virtual int CompanyID { get; set; }

        public virtual int RequestID { get; set; }

        public virtual int SeqNbr { get; set; }

        public virtual string FieldName { get; set; }

        public virtual string OldValue { get; set; }

        public virtual string NewValue { get; set; }

        //public virtual ChangeRequestsHDR ChangeRequests { get; set; }
    }

    public class CalendarEntries
    {
        public int TaskID { get; set; }
        public int OwnerID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public int StartTimeZone { get; set; }
        public DateTime Start { get; set; }
        public DateTime End { get; set; }
        public int EndTimeZone { get; set; }
        public string RecurrenceRule { get; set; }
        public string RecurrenceID { get; set; }
        public string RecurrenceException { get; set; }
        public bool IsAllDay { get; set; }

    }

    public class EETotalComp
    {
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public int year { get; set; }
        public decimal totalTaxes { get; set; }
        public decimal totalBenefits { get; set; }
        public decimal totalPTO { get; set; }
        public decimal totalHoliday { get; set; }
        public decimal totalAnnualWages { get; set; }
        public decimal totalCompensation { get; set; }
        public decimal totalFicaSS { get; set; }
        public decimal totalFicaM { get; set; }
        public decimal totalUnEmployment { get; set; }
        public decimal totalWc { get; set; }
        public decimal totalFees { get; set; }
        public decimal totalBurden { get; set; }
        public decimal totalReim { get; set; }
        public List<EmployeeCodes> allBenefits { get; set; }
        public List<EmployeeCodes> allPay { get; set; }
    }

    public class EETotalBurden
    {
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public decimal Benefits { get; set; }
        public decimal AnnualWages { get; set; }
        public decimal FicaSS { get; set; }
        public decimal FicaM { get; set; }
        public decimal SUTA { get; set; }
        public decimal FUTA { get; set; }
        public decimal WC { get; set; }
        public decimal AdminFee { get; set; }
    }

    public class PTORequestsVM : PTORequest
    {
        public string Department { get; set; }
        public string EmployeeName { get; set; }
        public string dispAllDay { get; set; }
        public string dispSDate { get; set; }
        public string dispEDate { get; set; }
        public string dispStatus { get; set; }
        public string AllowDelete { get; set; }
        public string AvailableHrs { get; set; }
        public string AmountTaken { get; set; }
        public string Pending { get; set; }
        public string Approved { get; set; }
    }

    public class EEPayStub
    {
        public List<CheckStubTransactionRecord> thePay { get; set; }
        public List<CheckStubTransactionRecord> theDed { get; set; }
        public List<CheckStubTransactionRecord> theBen { get; set; }
        public List<CheckStubTransactionRecord> theState { get; set; }
        public List<CheckStubTransactionRecord> theLocal { get; set; }
        public List<CheckStubTransactionRecord> theTax { get; set; }
        public List<CheckStubTransactionRecord> theDep { get; set; }
        public List<CheckStubPTORecord> thePTO { get; set; }
        public List<string> theEENotes { get; set; }
        public List<string> theCCNotes { get; set; }
        public decimal totalHours { get; set; }
        public decimal totalGross { get; set; }
        public decimal grossPayRun { get; set; } // 08/28/2019 DS TFS # 5346
        public decimal grossPayRunYTD { get; set; } // 08/28/2019 DS TFS # 5346
        public decimal totalYTD { get; set; }
        public decimal totalBenYTD { get; set; }
        public decimal totalDedYTD { get; set; }
        public decimal totalTaxYTD { get; set; }
        public decimal netWagesYTD { get; set; }
        public decimal netDeposit { get; set; }
        public decimal totalTax { get; set; }
        public decimal totalBens { get; set; }
        public decimal totalDed { get; set; }
        public decimal offeredHours { get; set; }
        public EmployeeAddress eeAddress { get; set; }
        public string employeeName { get; set; }
        public string clientName { get; set; }
        public string companyName { get; set; }
        public string clientAddress { get; set; }
        public string companyAddress { get; set; }
        public string worksiteAddress { get; set; }
        public string corporateAddress { get; set; } // 10/22/2019 HP TFS # 5139
        public EmployeeInvoiceCheckHistory theWorkCheck { get; set; }
        public string ssn { get; set; }
        public PEOAddress peoAddress { get; set; }
        public EmployerAddress employerAddress { get; set; }
        public EmployerAddress worksiteErAddress { get; set; } // 03/25/2019 DS TFS # 4958
        public string homeDepartment { get; set; }
        public string homePosition { get; set; }

    }

    public class PEOAddress
    {
        public string CompanyName { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string Address3 { get; set; }
        public string City { get; set; }
        public string County { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Phone3 { get; set; }
        public string Fax { get; set; }
    }

    public class EmployerAddress
    {
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string Address3 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Zip { get; set; }
        public string Phone1 { get; set; }
        public string Phone2 { get; set; }
        public string Phone3 { get; set; }
        public string Fax { get; set; }
    }

    public class CodeDescriptionTypes
    {
        public const int Paycode = 1;
        public const int Deduction = 2;
        public const int Benefit = 3;
        public const int StateTax = 4;
        public const int LocalTax = 5;
        public const int WC = 6;
        public const int Position = 7;
        public const int Department = 8;
    }

    public class ACAFTETrend
    {
        public string label { get; set; }
        public decimal limit { get; set; }
        public decimal actual { get; set; }
    }

    public partial class UserToDoItem
    {
        public string action { get; set; }
        public string qstring { get; set; }
        public string EmployeeName { get; set; }
        public int TimeSheetID { get; set; }
        public string TimeSheetName { get; set; }
    }

    [Serializable]
    public partial class TimeSheet
    {
        public string TimeSheetTypeConverted { get; set; }
        public string StatusConverted { get; set; }
        public virtual ClientTimeSheetProfile Profile { get; set; }
        public virtual ICollection<TimeSheetColumn> Columns { get; set; }
        public string DNetUrl { get; set; }
        public bool IsCertified => this.Profile != null
            ? (this.Profile.ProfileType == TimesheetType.CertifiedByEE || this.Profile.ProfileType == TimesheetType.CertifiedByJob)
            : false;

        public static TimeSheet Create(ClientTimeSheetProfile profile) =>
            new TimeSheet()
            {
                Profile = profile,
                CompanyID = profile.CompanyID,
                ClientID = profile.ClientID,
                TimeSheetType = profile.ProfileType ?? TimesheetType.Regular,
                TimeSheetName = profile.ProfileName,
                ProfileID = profile.ProfileID,
                SelectionType = profile.SelectionType,
                DateFrom = profile.PayPeriodFrom,
                DateTo = profile.PayPeriodEnd,
                Status = TimesheetStatus.New,
                PageSize = 100,
                RowsPerEE = profile.RowsPerEE,
                SortBy = TimesheetSortType.Name,
                DefDays = profile.DefDays,
                DefWeeks = profile.DefWeeks,
                DefHours = profile.DefHours,
                MaxRate = profile.MaxRate,
                MaxDayHours = profile.MaxDayHours,
                MaxEEHours = profile.MaxEEHours ?? 0,
                MaxEEGrossAmount = (float)(profile.MaxEEGrossAmount ?? 0),
                ShiftStatus = profile.ShiftStatus,
                AllowAddEE = profile.AllowAddEE,
                AllowAddCode = profile.AllowAddCode,
                AllowModifiedRate = profile.AllowModifiedRate,
                AllowModifiedRatePermanent = profile.AllowModifiedRatePermanent,
                PCDecimals = profile.PaycodeDecimals ?? 2,
                BenDecimals = profile.BenefitDecimals ?? 2,
                DedDecimals = profile.DeductionDecimals ?? 2,
                PCSource = profile.PayCodesSource ?? 0,
                BenSource = profile.BenefitsSource ?? 0,
                DedSource = profile.DeductionsSource ?? 0,
                AutoSaveTime = profile.AutoSave,
                MaskSSN = profile.MaskSSN,
                PrintSocSecOnBlankTS = profile.PrintSocSecOnBlankTS,
                PrintSocSecOnReports = profile.PrintSocSecOnReports,
                PrintRateOnBlankTS = profile.PrintRateOnBlankTS,
                PrintRateOnReports = profile.PrintRateOnReports,
                PrintYTDOnBlankTS = profile.PrintYTDOnBlankTS,
                PrintYTDOnReports = profile.PrintYTDOnReports,
                AllowTimeEntry = profile.AllowTimeEntry,

                EEDeptStatus = (profile.DepartmentStatus == TimesheetColumnStatus.Hidden)
                    ? TimesheetColumnStatus.Hidden
                    : (profile.AllowEEDeptChg)
                        ? TimesheetColumnStatus.Editable
                        : TimesheetColumnStatus.ReadOnly,

                EEPositionStatus = (profile.PositionStatus == TimesheetColumnStatus.Hidden)
                    ? TimesheetColumnStatus.Hidden
                    : (profile.AllowEEPositionChg)
                        ? TimesheetColumnStatus.Editable
                        : TimesheetColumnStatus.ReadOnly,

                EELaborDistribution1Status = profile.EELaborDistribution1Status,
                EELaborDistribution2Status = profile.EELaborDistribution2Status,
                EELaborDistribution3Status = profile.EELaborDistribution3Status,
                EELaborDistribution4Status = profile.EELaborDistribution4Status,
                EELaborDistribution5Status = profile.EELaborDistribution5Status,
                EELaborDistribution6Status = profile.EELaborDistribution6Status,
                EELaborDistribution7Status = profile.EELaborDistribution7Status,
                EELaborDistribution8Status = profile.EELaborDistribution8Status,
                PayrollProfileID = profile.PayrollProfileID,
                PayPeriod = profile.PayPeriod,
                ApprovalType = profile.ApprovalType,
                HideOfferedHours = profile.HideOfferedHours,
                HideCheckNumber = profile.HideCheckNumber,
                HideWeeks = profile.HideWeeks,
                HideDays = profile.HideDays,
                DateCreated = DateTime.Now
            };
    }

    public partial class TimeSheetColumn : ITimeSheetColumn
    {
        public int ColID { get; set; }
        public short? Type { get; set; }
        public List<string> ExceptionEEs { get; set; }
        public double TotalAmount { get; set; }
        public int OriginCodeNbr { get; set; }

        public static TimeSheetColumn Create(int timesheetId, string name, int colNum, short? type, string label, short? status, short? validationType, string code, short? codeType, string basedOn, string baseCode, short? baseCodeType) =>
            new TimeSheetColumn
            {
                TimeSheetID = timesheetId,
                Name = name,
                ColNbr = colNum,
                ColType = type,
                Label = label,
                Status = status,
                ValidationType = validationType,
                Code = code,
                CodeType = codeType,
                BasedOn = basedOn,
                BaseCode = baseCode,
                BaseCodeType = baseCodeType,
                Type = type
            };
    }

    public class ClientEmployeesMetrics
    {
        public string CompanyName { get; set; }
        public decimal AvgEmployees { get; set; }
        public decimal AvgAnnualWage { get; set; }
        public int Checks { get; set; }
        public int EmployeesPaid { get; set; }
        public int ClientsNew { get; set; }
        public int ClientsTerm { get; set; }
        public int EmployeesNew { get; set; }
        public int EmployeesTerm { get; set; }
    }

    [Serializable]
    public partial class EmployeeTimePunch
    {
        public bool HasPendingRequest { get; set; }
    }

    public partial class EmployeeEnrollmentEligiblePlan
    {
        public List<EmployeeEnrollmentPlanRateGrid> categories { get; set; }
        public List<ClientPlanSpec> specs { get; set; }
        public decimal PerCheck { get; set; }
        public decimal Monthly { get; set; }
        public decimal ErMonthly { get; set; }
        public decimal EeMonthly { get; set; }
        public string AdminEeCode { get; set; }     // 12/29/2017 DS TFS # 2927
        public decimal AdminEeAmount { get; set; }  // 12/29/2017 DS TFS # 2927
        public string AdminErCode { get; set; }     // 12/28/2017 DS TFS # 2927
        public decimal AdminErAmount { get; set; }  // 12/28/2017 DS TFS # 2927
        public int defSelected { get; set; }
        public string effectiveDate { get; set; }
        public List<EmployeeEnrollmentPlanDependent> Dependents { get; set; }
        public string Description { get; set; }
        public List<ClientPlanDocument> theDocs { get; set; }
        public string Link { get; set; }
        public List<EmployeeEnrollmentPlanDependent> CategoryDependents { get; set; }
        public short? EmployerPaysPlan { get; set; }
        public bool DoNotAllowWaiveForClientOnlyPlans { get; set; }
        public bool CafeEligible { get; set; }
        public bool DoNotAllowWaiveForClientOnlyPlan { get; set; }
        public bool AllowEmployeeEnterAnnualWages { get; set; }
        public decimal AnnualWage { get; set; }
    }

    public partial class EmployeeEnrollmentPlanDependent
    {
        public string Relationship { get; set; }
    }

    public partial class EmployeeEnrollmentPlanType
    {
        public bool DoNotAllowWaiveForClientOnlyPlans { get; set; }
        public bool AllowEmployeeEnterAnnualWages { get; set; }
        public short? EmployerPaysPlan { get; set; }
    }

    public partial class MemRatedAmounts
    {
        public decimal PerCheck { get; set; }
        public decimal Monthly { get; set; }
        public decimal ErMonthly { get; set; }
    }

    public class EmployeeBeneficiariesList
    {
        public string Name { get; set; }
        public string SSN { get; set; }
        public string DorB { get; set; }
    }

    public partial class EmployeeAssignedPlan
    {
        public string BenefitType { get; set; }
        public string BenefitDescription { get; set; }
        public string PlanType { get; set; }
        public string Carrier { get; set; }
        public decimal? TotalPremiumPerCheck { get; set; }
        public decimal? TotalPremiumPerMonth { get; set; }
        public decimal? TotalPremiumPerYear { get; set; }
        public decimal? TotalPremiumYTD { get; set; }
        public decimal? EmployeeAmountPerCheck { get; set; }
        public decimal? EmployeeAmountPerMonth { get; set; }
        public decimal? EmployeeAmountPerYear { get; set; }
        public decimal? EmployeeAmountYTD { get; set; }
        public decimal? EmployerAmountPerCheck { get; set; }
        public decimal? EmployerAmountPerMonth { get; set; }
        public decimal? EmployerAmountPerYear { get; set; }
        public decimal? EmployerAmountYTD { get; set; }
        public string SSN { get; set; }
        public string Name { get; set; }
        public bool Active { get; set; }
    }
    public class EmployeeAssignedPlansHist : EmployeeAssignedPlan
    {
        public DateTime? CheckDate { get; set; }
        public string CheckNumber { get; set; }
        public decimal PayRate { get; set; }
        public decimal Hours { get; set; }
        public decimal AmountPaid { get; set; }
        public string Department { get; set; }
        public string Position { get; set; }

    }
    public partial class EmployeeEnrollmentStatusVM : EmployeeEnrollmentStatu
    {
        public string FormattedEmployeeID { get; set; }
        public string FormattedSocialSecNumber { get; set; }
        public string CompletedDate { get; set; }
        public string EnrollEndDate { get; set; }
        public string Department { get; set; }
        public string LastSubmittedDate { get; set; }
        public string Location { get; set; }
        public int SelectedYear { get; set; }
        public string EmploymentStatus { get; set; }
    }

    public partial class InvoiceCharge
    {
        public string ChargeDescription { get; set; }
    }

    [Serializable]
    public partial class UserRoleClientEmployeeAssignment
    {
        public string EmployeeName { get; set; }
        public string Email { get; set; }
        public virtual HRPerformanceProSetup HRPerformanceProSetup { get; set; }
        public virtual HRPerformanceProAssignment HRPerformanceProAssignment { get; set; }
        public string TimecoUsername { get; set; }
    }

    public partial class OpenEnrollmentDocument
    {
        public bool hasFields { get; set; }
    }

    public partial class ClientPlanDocument
    {
        public bool hasFields { get; set; }
    }

    public class TimeSheetList
    {
        public int TimeSheetID { get; set; }
        public string ProfileID { get; set; }
        public string TimeSheetName { get; set; }
        public string TimeSheetType { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public string Status { get; set; }
        public string Creator { get; set; }
        public int TRXNumber { get; set; }
        public int EENumber { get; set; }
        public string PayPeriod { get; set; }
    }

    public class ClientJobCostAssignmentDetailsView
    {
        public virtual string ClientID { get; set; }
        public virtual string JobCostingName { get; set; }
        public string JobLevel { get; set; }
        public string EstimatedStartDate { get; set; }
        public string EstimatedCompletionDate { get; set; }
        public string EstimatedHours { get; set; }
        public string Cost { get; set; }
    }

    public class ClientJobCostAssignmentsView
    {
        public virtual string ClientID { get; set; }
        public virtual string JobCostingName { get; set; }
        public virtual string LongDescription { get; set; }
        public string EstimatedStartDate { get; set; }
        public string EstimatedCompletionDate { get; set; }
        public string EstimatedHours { get; set; }
        public string Cost { get; set; }
        public virtual bool Inactive { get; set; }
        public virtual bool Complete { get; set; }
        public string JobCostingID { get; set; }
    }

    public class EmployeeJobCostAssignmentsView
    {
        public virtual string EmployeeID { get; set; }
        public virtual string EmployeeName { get; set; }
        public virtual string JobCostingName { get; set; }
        public string JobLevel { get; set; }
        public string EstimatedStartDate { get; set; }
        public string EstimatedCompletionDate { get; set; }
        public string EstimatedHours { get; set; }
        public string Cost { get; set; }
        public virtual bool Complete { get; set; }
    }

    public class AvailableJobCostAssignmentsView
    {
        public virtual string JobCostingName { get; set; }
        public string JobLevel2 { get; set; }
        public string JobLevel3 { get; set; }
        public string JobLevel4 { get; set; }
        public string JobLevel5 { get; set; }
        public string JobLevel6 { get; set; }
        public List<string> EmployeeID { get; set; }
    }

    public class AvailableEEJobCostAssignmentsView
    {
        public string EmployeeID { get; set; }
        public string JobCostingName { get; set; }
        public string JobLevel2 { get; set; }
        public string JobLevel3 { get; set; }
        public string JobLevel4 { get; set; }
        public string JobLevel5 { get; set; }
        public string JobLevel6 { get; set; }
        public bool NoAssignment { get; set; }
        public string JobCode { get; set; }
        public string JobAssignment { get; set; }
    }

    /// <summary>
    ///     For some reason, OpenAccess decided not to auto-generate the Clients collection on the ClientTimeSheetProfiles
    ///     table, so we have to get the clients ourselves and populate a view with all the combined data.
    /// </summary>
    public class ClientTimeSheetProfilesView
    {
        public virtual ClientTimeSheetProfile ClientTimeSheetProfile { get; set; }
        public virtual Client Client { get; set; }

    }

    public class SupervisorsVM // 11/05/2019 DS TFS # 5473
    {
        public string SupervisorCode { get; set; }
        public string Description { get; set; }
        public string EmployeeID { get; set; }
        public string EmployeeName { get; set; }
    }

    public class CompanyEmployee
    {
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string Name { get; set; }
    }

    [Serializable]
    public partial class Client : IBatchHeaderInvoice
    {
        public virtual ICollection<Employee> Employees { get; set; }
        public string TW_Recv_Key { get => ""; set => throw new NotImplementedException(); }
        public string PayrollKey { get => ""; set => throw new NotImplementedException(); }
    }

    [Serializable]
    public partial class ClientDivisionPayrollCode
    {

    }

    [Serializable]
    public partial class ClientDivisionDetail
    {

    }

    [Serializable]
    public partial class Paycode
    {

    }

    [Serializable]
    public partial class Benefit
    {

    }

    [Serializable]
    public partial class Deduction
    {

    }

    [Serializable]
    public partial class Position
    {

    }

    [Serializable]
    public partial class Department
    {

    }

    [Serializable]
    public partial class LocalTax
    {

    }

    [Serializable]
    public partial class SUTAState
    {

    }

    [Serializable]
    public partial class WorkersCompCode
    {

    }

    [Serializable]
    public partial class ClientPayrollSchedule
    {
    }

    [Serializable]
    public partial class PayrollSnapshot
    {
    }

    [Serializable]
    public partial class PayrollProcessOption
    {
    }

    [Serializable]
    public partial class PayrollProfileSetting
    {
    }

    [Serializable]
    public partial class Company
    {
    }

    [Serializable]
    public partial class HRPerformanceProSetup
    {

    }

    [Serializable]
    public partial class HRPerformanceProAssignment
    {

    }

    [Serializable]
    public partial class ClientCodeDescription
    {

    }

    [Serializable]
    public partial class UserRole
    {

    }

    public partial class PayrollBuildNotes
    {
        public string Name { get; set; }
    }

    public partial class PayrollBuildSettings
    {
        public bool Locked { get; set; }
    }
    public partial class PayrollBatch
    {
        public List<PayrollBatchDetail> Details { get; set; }
        public TimeSheet TimeSheet { get; set; }
    }

    [Serializable]
    public partial class ClientTimeSheetProfile
    {
        public List<ClientTimeSheetProfileColumn> Columns { get; set; }
    }

    public partial class EmployeeTimeEntryColumnSetup
    {
        public static EmployeeTimeEntryColumnSetup Create(int timesheetId, int colNum, short status, TimeSheetColumnDefault columnDefault) =>
            new EmployeeTimeEntryColumnSetup
            {
                TimeSheetID = timesheetId,
                TimeSheetColID = columnDefault.ColNbr,
                Name = columnDefault.Name,
                ColNbr = colNum,
                ColType = columnDefault.ColType,
                Label = columnDefault.Label,
                Status = status,
                ValidationType = columnDefault.ValidationType,
                Code = string.Empty,
                CodeType = columnDefault.CodeType,
                BasedOn = columnDefault.BasedOn,
                BaseCode = string.Empty,
                BaseCodeType = 0
            };

        public static EmployeeTimeEntryColumnSetup Create(int timesheetId, string name, int colNum, short? columnType, string label, short? status, short? validationType, string code, short? codeType, string basedOn, string baseCode, short? baseCodeType) =>
            new EmployeeTimeEntryColumnSetup
            {
                TimeSheetID = timesheetId,
                TimeSheetColID = null,
                Name = name,
                ColNbr = colNum,
                ColType = columnType,
                Label = label,
                Status = status,
                ValidationType = validationType,
                Code = code,
                CodeType = codeType,
                BasedOn = basedOn,
                BaseCode = baseCode,
                BaseCodeType = baseCodeType
            };
    }

    public partial class EmployeeTimeEntry
    {
        public virtual ICollection<EmployeeTimeEntryDetail> Details { get; set; } = new HashSet<EmployeeTimeEntryDetail>();
        public virtual ICollection<TimeEntryColumn> Columns { get; set; } = new HashSet<TimeEntryColumn>();
        public virtual ICollection<TimeEntryRow> Rows { get; set; } = new HashSet<TimeEntryRow>();

        public void Consolidate()
        {
            foreach (var column in Columns)
            {
                var columnId = column.ColumnId - 1; // - 1 because ColumnId is not zero-based index.
                var rows = Rows.Where(x => x.Details[columnId] != TimesheetParam.GreyCellString);
                foreach (var row in rows)
                {
                    if (!double.TryParse(row.Details[columnId], out double amount))
                        amount = 0;

                    row.TotalHours = amount;
                }
                column.TotalAmount = rows.Sum(x => x.TotalHours);
            }

            Details = new HashSet<EmployeeTimeEntryDetail>();
            foreach (var row in Rows)
            {
                DateTime entryDate;

                if (!DateTime.TryParse(row.SortValue, out entryDate))
                    entryDate = DateTime.Today.Date;

                for (int i = 0; i < row.Details.Length; i++)
                {
                    var colId = i + 1;
                    var timeSheetColumnId = Columns.GetTimeSheetColumnId(colId);
                    Details.Add(new EmployeeTimeEntryDetail()
                    {
                        TimeEntryID = row.TimeEntryId,
                        RowID = row.RowId,
                        ColID = colId,
                        EntryDate = entryDate,
                        CellValue = row.Details[i].Trim(),
                        TimeSheetColID = timeSheetColumnId,
                        OriginRowID = row.RowId
                    });
                }
            }

            Status = Thinkware.Pay360.Timesheets.TimeEntryStatus.Consolidated;
        }
        public void CreateTimeEntryRows()
        {
            var entryDateColumnId = Columns.GetColumnIdByColumnType(TimesheetColumnType.DateDay) - 1; // - 1 because ColumnId = ColNbr (not zero-index based).
            var departmentColumnId = Columns.GetColumnIdByColumnType(TimesheetColumnType.Department) - 1;
            var positionColumnId = Columns.GetColumnIdByColumnType(TimesheetColumnType.Position) - 1;

            Rows = new List<TimeEntryRow>();
            var rowCount = Details.Select(x => x.RowID).Distinct().Count();
            for (int rowId = 1; rowId <= rowCount; rowId++)
            {
                var detail = Details.First(x => x.RowID == rowId);
                var rowDetails = Details.Where(x => x.RowID == rowId).OrderBy(x => x.ColID).Select(x => x.CellValue).ToArray();
                Rows.Add(new TimeEntryRow()
                {
                    TimeEntryId = detail.TimeEntryID,
                    RowId = detail.RowID,
                    Department = (departmentColumnId > 0) ? rowDetails[departmentColumnId] : string.Empty,
                    Position = (positionColumnId > 0) ? rowDetails[positionColumnId] : string.Empty,
                    OriginRow = detail.OriginRowID ?? detail.RowID,
                    Details = rowDetails,
                    SortValue = rowDetails[entryDateColumnId]
                });
            }
        }
        public void SetColumns(ICollection<TimeEntryColumn> columns) => Columns = columns;
    }

    public partial class PayrollProcessPermission
    {
        public const int DefaultPermissionValue = AllPermissionValue;

        public const string AllPermissionKey = "All";
        public const string AllPermissionText = "All";
        public const int AllPermissionValue = 1;

        public const string TeamPermissionKey = "Team";
        public const string TeamPermissionText = "Team";
        public const int TeamPermissionValue = 2;

        public const string ResponsibleOnlyPermissionKey = "ResponsibleOnly";
        public const string ResponsibleOnlyPermissionText = "Responsible Only";
        public const int ResponsibleOnlyPermissionValue = 3;

        public const string PayrollProcessKey = "Payroll";
        public const string InvoiceProcessKey = "Invoice";
        public const string FinalizeProcessKey = "Finalize";

        public PayrollProcessPermission()
        {
            this.Payroll = DefaultPermissionValue;
            this.Invoice = DefaultPermissionValue;
            this.Finalize = DefaultPermissionValue;
        }

        public static PayrollProcessPermission Create(int companyId, string clientId, string profileId, int payrollPermission = DefaultPermissionValue, int invoicePermission = DefaultPermissionValue, int finalizePermission = DefaultPermissionValue) =>
            new PayrollProcessPermission()
            {
                CompanyId = companyId,
                ClientId = clientId,
                ProfileId = profileId,
                Payroll = GetPermissionOrDefaultValue(payrollPermission),
                Invoice = GetPermissionOrDefaultValue(invoicePermission),
                Finalize = GetPermissionOrDefaultValue(finalizePermission)
            };

        public Dictionary<string, int> GetPermissions() =>
            GetPermissions(this.Payroll, this.Invoice, this.Finalize);

        public int GetPermission(string name)
        {
            switch (name)
            {
                case PayrollProcessKey: return this.Payroll;
                case InvoiceProcessKey: return this.Invoice;
                case FinalizeProcessKey: return this.Finalize;
                default: return DefaultPermissionValue;
            }
        }

        public void SetPermission(string name, int value)
        {
            switch (name)
            {
                case PayrollProcessKey:
                    this.Payroll = value;
                    break;
                case InvoiceProcessKey:
                    this.Invoice = value;
                    break;
                case FinalizeProcessKey:
                    this.Finalize = value;
                    break;
                default:
                    break;
            }
        }

        public void SetPermissions(IDictionary<string, int> permissions)
        {
            foreach (var item in permissions)
                SetPermission(item.Key, item.Value);
        }

        public static Dictionary<string, int> GetPermissions(int payrollPermission, int invoicePermission, int finalizePermission) =>
            new Dictionary<string, int>()
            {
                { PayrollProcessKey, GetPermissionOrDefaultValue(payrollPermission) },
                { InvoiceProcessKey, GetPermissionOrDefaultValue(invoicePermission) },
                { FinalizeProcessKey, GetPermissionOrDefaultValue(finalizePermission) }
            };

        public static Dictionary<string, int> GetDefaultPermissions() =>
            new Dictionary<string, int>()
            {
                { PayrollProcessKey, DefaultPermissionValue },
                { InvoiceProcessKey, DefaultPermissionValue },
                { FinalizeProcessKey, DefaultPermissionValue }
            };

        public static int GetPermissionOrDefaultValue(int permission)
        {
            if (permission >= AllPermissionValue && permission <= ResponsibleOnlyPermissionValue)
                return permission;
            else
                return DefaultPermissionValue;
        }

        public static string GetPermissionText(int value)
        {
            switch (value)
            {
                case AllPermissionValue: return AllPermissionText;
                case TeamPermissionValue: return TeamPermissionText;
                case ResponsibleOnlyPermissionValue: return ResponsibleOnlyPermissionText;
                default: return string.Empty;
            }
        }
    }

    public partial class PayrollBenefitsDeduction
    {
        public string Description { get; set; }
    }

    public partial class EmployeeEnrollmentEligiblePlansStage
    {
        public decimal PerCheck { get; set; }
        public decimal Monthly { get; set; }
        public decimal ErMonthly { get; set; }
        //public decimal EeMonthly { get; set; }
        public string AdminEeCode { get; set; }     // 12/29/2017 DS TFS # 2927
        public decimal AdminEeAmount { get; set; }  // 12/29/2017 DS TFS # 2927
        public string AdminErCode { get; set; }     // 12/28/2017 DS TFS # 2927
        public decimal AdminErAmount { get; set; }  // 12/28/2017 DS TFS # 2927
        public int defSelected { get; set; }
        public string effectiveDate { get; set; }
        public string Description { get; set; }
        public string Link { get; set; }
        public short? EmployerPaysPlan { get; set; }
        public bool DoNotAllowWaiveForClientOnlyPlans { get; set; }
        public bool CafeEligible { get; set; }

        public string PlanDescription { get; set; } //Added by Tecnics on 07JUN2021 for tkt:1043

        public string MappedBenefitType { get; set; } //Added by Tecnics for tkt:1209
    }

    //Added by tecnics on 16AUG2022 for tkt:1381
    public partial class EmployeeEnrollmentPlanTypesStage
    {
        public bool DoNotAllowWaiveForClientOnlyPlans { get; set; }
        public short? EmployerPaysPlan { get; set; }

        public string MappedBenefitType { get; set; }
        public string FullyERPaid { get; set; }

        public bool FSAAdminShow { get; set; }
        public bool LFSAAdminShow { get; set; }
        public bool DFSAAdminShow { get; set; }
    }

    public partial class EmployeeAssignedPlansStage
    {
        public decimal NewPlanMonthlyCost { get; set; }
        public short PlanCategoryNumber { get; set; }
        public string PlanType { get; set; }

    }

    //Added by Tecnics on 17JUL2022 for tkt:1381
    public partial class BeneficiarySummary
    {
        public string PlanDesc { get; set; }
        public short BeneficiaryType { get; set; }
        public short Percent { get; set; }
        public string SSN { get; set; }
    }
}
