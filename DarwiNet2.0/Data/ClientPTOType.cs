//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class ClientPTOType
    {
        public int ID { get; set; }
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public byte PTOType { get; set; }
        public string Description { get; set; }
        public bool Accrue { get; set; }
        public Nullable<short> AccrualMethod { get; set; }
        public Nullable<int> AvailableHours { get; set; }
        public Nullable<int> AccrualAmount { get; set; }
        public Nullable<int> HoursPerYear { get; set; }
        public Nullable<int> MaxAccrualYTD { get; set; }
        public Nullable<int> MaxAccrualLTD { get; set; }
        public Nullable<short> Type { get; set; }
        public bool CarryOver { get; set; }
        public Nullable<int> MaxCarryOver { get; set; }
        public bool SuppressPrintTilAvailable { get; set; }
        public Nullable<System.DateTime> ProcessDate { get; set; }
        public Nullable<short> SchedulePeriod1 { get; set; }
        public Nullable<short> SchedulePeriod2 { get; set; }
        public bool FirstOfNextMonth1 { get; set; }
        public bool FirstOfNextMonth2 { get; set; }
        public Nullable<int> WaitPeriodAccrualBeginLimit { get; set; }
        public Nullable<int> WaitPeriodUsageBeginLimit { get; set; }
        public bool UseTiers { get; set; }
        public Nullable<short> AnniversaryMethod { get; set; }
        public bool WarnAvailableBelowZero { get; set; }
        public bool Inactive { get; set; }
        public Nullable<short> TiersBasedOn { get; set; }
        public Nullable<short> MaxAccrualBasedOn { get; set; }
        public bool Vested { get; set; }
        public Nullable<int> VestedDestination { get; set; }
        public Nullable<int> VestedSource { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual Client Client { get; set; }
    }
}
