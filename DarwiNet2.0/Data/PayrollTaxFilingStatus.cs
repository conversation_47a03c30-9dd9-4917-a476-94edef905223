//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class PayrollTaxFilingStatus
    {
        public string TaxCode { get; set; }
        public string TaxFilingStatus { get; set; }
        public string StatusDescription { get; set; }
        public decimal LowIncomeLimit { get; set; }
        public decimal PersonalExemptionAmount { get; set; }
        public bool IncludePersonalExemptions { get; set; }
        public bool IncludeAdditionalAllowances { get; set; }
        public bool IncludeDependents { get; set; }
        public int FederalTaxPercent { get; set; }
        public decimal FederalTaxMaximum { get; set; }
        public int FICATaxPercent { get; set; }
        public decimal FICATaxMaximum { get; set; }
        public int FlatTaxRate { get; set; }
        public short StandardDeductionMethod { get; set; }
        public int StandardDeductionPercent { get; set; }
        public decimal StandardDeductionAmount { get; set; }
        public decimal StandardDeductionMinimum { get; set; }
        public decimal StandardDeductionMaximum { get; set; }
        public decimal SpecialExemptionAmount { get; set; }
        public decimal SpecialStandardDeduction { get; set; }
        public int SpecialTaxRate { get; set; }
    }
}
