//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class Employee401kPlans
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Employee401kPlans()
        {
            this.Employee401kPlanDetails = new HashSet<Employee401kPlanDetails>();
        }
    
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string PlanName { get; set; }
        public bool Active { get; set; }
        public string Benefit { get; set; }
        public bool ApplyAnnual401kMatchLimit { get; set; }
        public Nullable<int> C401kAnnualWagePctLimit { get; set; }
        public Nullable<decimal> AnnualWages { get; set; }
        public bool OverrideWaitingPeriod { get; set; }
        public bool WaitingPeriodSatisfied { get; set; }
        public bool NotifyUser { get; set; }
        public Nullable<decimal> DeductionPayPeriodMax { get; set; }
        public Nullable<decimal> DeductionMonthMax { get; set; }
        public Nullable<decimal> DeductionQtrMax { get; set; }
        public Nullable<decimal> DeductionYearMax { get; set; }
        public Nullable<decimal> DeductionLifetimeMax { get; set; }
        public Nullable<decimal> C401kMatchingDeficit { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<Employee401kPlanDetails> Employee401kPlanDetails { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual Employee Employee { get; set; }
    }
}
