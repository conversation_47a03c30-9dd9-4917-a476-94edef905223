//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class InvoiceApprovalSetupSequence
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public InvoiceApprovalSetupSequence()
        {
            this.InvoiceApprovalEmailTokens = new HashSet<InvoiceApprovalEmailToken>();
            this.InvoiceApprovals = new HashSet<InvoiceApproval>();
        }
    
        public int InvoiceApprovalSetupSequenceID { get; set; }
        public int InvoiceApprovalSetupID { get; set; }
        public int InvoiceApprovalRecipientID { get; set; }
        public int Sequence { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<InvoiceApprovalEmailToken> InvoiceApprovalEmailTokens { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual InvoiceApprovalRecipient InvoiceApprovalRecipient { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<InvoiceApproval> InvoiceApprovals { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual InvoiceApprovalSetup InvoiceApprovalSetup { get; set; }
    }
}
