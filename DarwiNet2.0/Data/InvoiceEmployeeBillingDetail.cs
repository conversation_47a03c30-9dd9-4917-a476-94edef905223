//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class InvoiceEmployeeBillingDetail
    {
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public int DarwinInvoiceNumber { get; set; }
        public string EmployeeID { get; set; }
        public string PayRecord { get; set; }
        public decimal BillRate { get; set; }
        public string AuditControlCode { get; set; }
        public System.DateTime TRXEndingDate { get; set; }
        public Nullable<int> RegularHours { get; set; }
        public Nullable<decimal> RegularEarnings { get; set; }
        public Nullable<int> OTHours { get; set; }
        public Nullable<decimal> OTEarnings { get; set; }
        public Nullable<decimal> OTBillingRate { get; set; }
        public Nullable<int> DTHours { get; set; }
        public Nullable<decimal> DTEarnings { get; set; }
        public Nullable<decimal> DTBillingRate { get; set; }
        public Nullable<int> BillingFactor { get; set; }
        public Nullable<decimal> TotalBilling { get; set; }
        public int MergedInvoiceNumber { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual Invoice Invoice { get; set; }
    }
}
