//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class NotificationQueue
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public NotificationQueue()
        {
            this.NotificationQueueDetails = new HashSet<NotificationQueueDetail>();
        }
    
        public int NotificationQueueID { get; set; }
        public int NotificationTypeID { get; set; }
        public string MailSettings { get; set; }
        public System.DateTime Created { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual NotificationType NotificationType { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<NotificationQueueDetail> NotificationQueueDetails { get; set; }
    }
}
