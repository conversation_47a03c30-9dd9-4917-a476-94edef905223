//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class OBClientSetupTaskField
    {
        public int CompanyID { get; set; }
        public int SetupID { get; set; }
        public int TaskID { get; set; }
        public string FName { get; set; }
        public Nullable<short> SeqNbr { get; set; }
        public string FLabel { get; set; }
        public Nullable<short> FType { get; set; }
        public Nullable<short> FSize { get; set; }
        public Nullable<short> DNType { get; set; }
        public string FValueOptions { get; set; }
        public string PermanentTBL { get; set; }
        public string PermanentFLD { get; set; }
        public Nullable<int> RelatedTask { get; set; }
        public string RelatedFLD { get; set; }
        public Nullable<short> FRequired { get; set; }
        public Nullable<short> FLocked { get; set; }
        public Nullable<short> CCAccess { get; set; }
        public Nullable<short> EEAccess { get; set; }
        public string FTip { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual OBClientSetupTask OBClientSetupTask { get; set; }
    }
}
