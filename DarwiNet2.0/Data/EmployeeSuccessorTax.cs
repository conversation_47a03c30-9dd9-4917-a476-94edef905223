//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class EmployeeSuccessorTax
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public EmployeeSuccessorTax()
        {
            this.EmployeeSuccessorSUTATaxes = new HashSet<EmployeeSuccessorSUTATax>();
        }
    
        public int CompanyID { get; set; }
        public int Year { get; set; }
        public string EmployeeID { get; set; }
        public System.DateTime Date { get; set; }
        public Nullable<decimal> FICASSWithholding { get; set; }
        public Nullable<decimal> FICAMWithholding { get; set; }
        public Nullable<decimal> FUTAAmount { get; set; }
        public Nullable<decimal> FICAMWageAmount { get; set; }
        public Nullable<decimal> FICASSWageAmount { get; set; }
        public string LastUser { get; set; }
        public Nullable<System.DateTime> LastDateEdited { get; set; }
        public bool UseDuringProcessing { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual Employee Employee { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<EmployeeSuccessorSUTATax> EmployeeSuccessorSUTATaxes { get; set; }
    }
}
