//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class DealAthletePayment
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public DealAthletePayment()
        {
            this.DealAthleteDeliverables = new HashSet<DealAthleteDeliverable>();
        }
    
        public int CompanyID { get; set; }
        public string ClientID { get; set; }
        public long DealID { get; set; }
        public string EmployeeID { get; set; }
        public Nullable<short> Status { get; set; }
        public Nullable<decimal> OfferAmount { get; set; }
        public Nullable<decimal> DealAmount { get; set; }
        public Nullable<System.DateTime> CreatedDate { get; set; }
        public Nullable<System.DateTime> DueDate { get; set; }
        public Nullable<System.DateTime> CompletedDate { get; set; }
        public Nullable<System.DateTime> DisclosedDate { get; set; }
        public Nullable<decimal> PaymentAmount { get; set; }
        public Nullable<System.DateTime> PaymentDate { get; set; }
        public string PaymentMethod { get; set; }
        public Nullable<int> PaymentAdjustmentNumber { get; set; }
        public string PayrollNumber { get; set; }
        public string CheckbookID { get; set; }
        public string CheckNumber { get; set; }
        public string AuditControlCode { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual Deal Deal { get; set; }
        [Newtonsoft.Json.JsonIgnore]
        public virtual Employee Employee { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        [Newtonsoft.Json.JsonIgnore]
        public virtual ICollection<DealAthleteDeliverable> DealAthleteDeliverables { get; set; }
    }
}
