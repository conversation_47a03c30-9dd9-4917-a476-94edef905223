//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class OBDocumentMapping
    {
        public int CompanyID { get; set; }
        public int DocumentID { get; set; }
        public string FormName { get; set; }
        public Nullable<short> FormFieldType { get; set; }
        public string DB_Table { get; set; }
        public string DB_Field { get; set; }
        public int DB_RecNum { get; set; }
        public string DB_Value { get; set; }
        public string FieldPresentation { get; set; }
        public Nullable<short> FieldStatus { get; set; }
        public bool UseInWF { get; set; }
        public Nullable<int> WF_SeqNbr { get; set; }
        public string WF_FieldLabel { get; set; }
        public Nullable<short> WF_FieldType { get; set; }
        public string WF_FieldOptions { get; set; }
    
        [Newtonsoft.Json.JsonIgnore]
        public virtual OBDocument OBDocument { get; set; }
    }
}
