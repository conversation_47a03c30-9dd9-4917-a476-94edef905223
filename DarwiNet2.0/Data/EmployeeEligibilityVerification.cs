//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class EmployeeEligibilityVerification
    {
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public string OtherName { get; set; }
        public Nullable<short> ResidentStatus { get; set; }
        public string AlienNbr { get; set; }
        public Nullable<System.DateTime> WorkExpDate { get; set; }
        public string I94Nbr { get; set; }
        public string ForeignPassportNbr { get; set; }
        public string PassportCountry { get; set; }
        public Nullable<short> DocA1Type { get; set; }
        public string DocA1Title { get; set; }
        public string DocA1Authority { get; set; }
        public string DocA1Number { get; set; }
        public Nullable<System.DateTime> DocA1ExpDate { get; set; }
        public Nullable<short> DocA2Type { get; set; }
        public string DocA2Title { get; set; }
        public string DocA2Authority { get; set; }
        public string DocA2Number { get; set; }
        public Nullable<System.DateTime> DocA2ExpDate { get; set; }
        public Nullable<short> DocA3Type { get; set; }
        public string DocA3Title { get; set; }
        public string DocA3Authority { get; set; }
        public string DocA3Number { get; set; }
        public Nullable<System.DateTime> DocA3ExpDate { get; set; }
        public Nullable<short> DocBType { get; set; }
        public string DocBTitle { get; set; }
        public string DocBAuthority { get; set; }
        public string DocBNumber { get; set; }
        public Nullable<System.DateTime> DocBExpDate { get; set; }
        public Nullable<short> DocCType { get; set; }
        public string DocCTitle { get; set; }
        public string DocCAuthority { get; set; }
        public string DocCNumber { get; set; }
        public Nullable<System.DateTime> DocCExpDate { get; set; }
        public Nullable<System.DateTime> EECompleteDate { get; set; }
        public bool UsePreparer { get; set; }
        public string PrepLastName { get; set; }
        public string PrepFirstName { get; set; }
        public string PrepAddress { get; set; }
        public string PrepCity { get; set; }
        public string PrepState { get; set; }
        public string PrepZIP { get; set; }
        public string CCTitle { get; set; }
        public string CCLastName { get; set; }
        public string CCFirstName { get; set; }
        public Nullable<System.DateTime> CCCertdate { get; set; }
        public string CCCertIPAddr { get; set; }
        public string RHLastName { get; set; }
        public string RHFirstName { get; set; }
        public string RHMiddle { get; set; }
        public Nullable<System.DateTime> RHDate { get; set; }
        public Nullable<short> RHDocType { get; set; }
        public string RHDocTitle { get; set; }
        public string RHDocNumber { get; set; }
        public Nullable<System.DateTime> RHDocExpDate { get; set; }
        public string RHERName { get; set; }
        public string EESignature { get; set; }
        public string ERSignature { get; set; }
        public string Comment { get; set; }
        public string RHSignature { get; set; }
        public Nullable<System.DateTime> RHSignDate { get; set; }
        public string RHComment { get; set; }
        public bool RHAltAuthorize { get; set; }
        public bool AltAuthorize { get; set; }
        public string PrepMiddle { get; set; }
        public string PrepSignature { get; set; }
        public Nullable<System.DateTime> PrepSignDate { get; set; }
        public string PrepLastName2 { get; set; }
        public string PrepFirstName2 { get; set; }
        public string PrepMiddle2 { get; set; }
        public string PrepAddress2 { get; set; }
        public string PrepCity2 { get; set; }
        public string PrepState2 { get; set; }
        public string PrepZIP2 { get; set; }
        public string PrepSignature2 { get; set; }
        public Nullable<System.DateTime> PrepSignDate2 { get; set; }
        public string PrepLastName3 { get; set; }
        public string PrepFirstName3 { get; set; }
        public string PrepMiddle3 { get; set; }
        public string PrepAddress3 { get; set; }
        public string PrepCity3 { get; set; }
        public string PrepState3 { get; set; }
        public string PrepZIP3 { get; set; }
        public string PrepSignature3 { get; set; }
        public Nullable<System.DateTime> PrepSignDate3 { get; set; }
        public string PrepLastName4 { get; set; }
        public string PrepFirstName4 { get; set; }
        public string PrepMiddle4 { get; set; }
        public string PrepAddress4 { get; set; }
        public string PrepCity4 { get; set; }
        public string PrepState4 { get; set; }
        public string PrepZIP4 { get; set; }
        public string PrepSignature4 { get; set; }
        public Nullable<System.DateTime> PrepSignDate4 { get; set; }
        public string RHLastName2 { get; set; }
        public string RHFirstName2 { get; set; }
        public string RHMiddle2 { get; set; }
        public Nullable<System.DateTime> RHDate2 { get; set; }
        public Nullable<short> RHDocType2 { get; set; }
        public string RHDocTitle2 { get; set; }
        public string RHDocNumber2 { get; set; }
        public Nullable<System.DateTime> RHDocExpDate2 { get; set; }
        public string RHERName2 { get; set; }
        public string RHComment2 { get; set; }
        public bool RHAltAuthorize2 { get; set; }
        public string RHSignature2 { get; set; }
        public Nullable<System.DateTime> RHSignDate2 { get; set; }
        public string RHLastName3 { get; set; }
        public string RHFirstName3 { get; set; }
        public string RHMiddle3 { get; set; }
        public Nullable<System.DateTime> RHDate3 { get; set; }
        public Nullable<short> RHDocType3 { get; set; }
        public string RHDocTitle3 { get; set; }
        public string RHDocNumber3 { get; set; }
        public Nullable<System.DateTime> RHDocExpDate3 { get; set; }
        public string RHERName3 { get; set; }
        public string RHComment3 { get; set; }
        public bool RHAltAuthorize3 { get; set; }
        public string RHSignature3 { get; set; }
        public Nullable<System.DateTime> RHSignDate3 { get; set; }

        public virtual Employee Employee { get; set; }
    }
}
