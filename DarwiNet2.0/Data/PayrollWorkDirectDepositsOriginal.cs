//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DarwiNet2._0.Data
{
    using System;
    using System.Collections.Generic;
    
    public partial class PayrollWorkDirectDepositsOriginal
    {
        public int CompanyID { get; set; }
        public string EmployeeID { get; set; }
        public int PaymentAdjustmentNumber { get; set; }
        public string AuditControlCode { get; set; }
        public int SequenceNumber { get; set; }
        public byte DirectDepositOptions { get; set; }
        public string PayRecord { get; set; }
        public byte DirectDepositBasedOnType { get; set; }
        public string ClientID { get; set; }
        public string BankID { get; set; }
        public string AccountNumber { get; set; }
        public string AccountDescription { get; set; }
        public Nullable<decimal> DirectDepositAmount { get; set; }
        public Nullable<int> Percentages { get; set; }
        public bool InsufficientFunds { get; set; }
        public bool Precheck { get; set; }
        public Nullable<decimal> PayCodeEarnings { get; set; }
        public Nullable<decimal> PayCodeNet { get; set; }
        public Nullable<decimal> CalculatedDeposit { get; set; }
        public Nullable<decimal> ActualDeposit { get; set; }
        public string CheckbookID { get; set; }
        public string PayrollNumber { get; set; }
        public string SnapshotID { get; set; }
        public bool IsLatest { get; set; }
    }
}
