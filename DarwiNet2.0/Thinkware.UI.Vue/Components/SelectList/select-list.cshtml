@model Thinkware.UI.Vue.VueSelectListHtmlElement

@if (!string.IsNullOrWhiteSpace(Model.LabelText))
{
    <label for="@Model.ElementId">@Model.LabelText</label>
    if (Model.IsRequired) { <span class="text-danger">*</span>}
}
<select 
    v-model="@Model.VModel" 
    id="@Model.ElementId" 
    class="@(Model.Classes ?? "form-control")" 
    v-on:change="@Model.OnChange"
    @if (!string.IsNullOrWhiteSpace(Model.Disabled))
    {
        @: :disabled="@Model.Disabled"
    }
    >
    <option
        v-for="(item, index) in @Model.Items"
        :value="<EMAIL>">
        {{@($"item.{Model.ItemText}")}}
    </option>
</select>