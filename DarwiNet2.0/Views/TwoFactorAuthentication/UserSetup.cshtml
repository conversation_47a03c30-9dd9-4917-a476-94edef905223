@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core

@model DarwiNet2._0.ViewModels.TwoFactorAuthentication.UserSetupVM

<style>
    .modal {
        min-width: 500px;
    }
    
    .container-fluid.master {
        margin: 0px 0px 0px 20px;
        max-width: 1400px;
    }

    .container-fluid.container-two-factor {
        margin: 0px 0px 0px 20px;
        max-width: 1400px;
    }

    .authentication-type,
    .buttons {
        min-width: 450px;
        padding: 30px 0px 0px 0px;
        width: 65%;
    }
    
        .buttons .col-xs-12 {
            padding: 0px 0px 0px 0px !important;
        }

    .setting-prompt {
        font-size: 18px;
        padding: 0px 0px 10px 10px;
    }

    .setting-control {
        text-align: right;
    }

    .setting-label > label {
        font-weight: bold;
    }

    .setting-description {
        font-size: 14px;
        padding: 0px 0px 10px 0px;
    }

    .input-control-text {
        display: none;
        margin: 0px 5px 0px 0px;
        width: 260px;
    }

    .verify-button {
        display: none;
        margin: -3px 0px 0px 0px;
    }

    .fa-check-circle {
        color: #008000;
        display: none;
        font-size: 28px;
        padding: 0px 0px 0px 15px;
        vertical-align: middle;
    }

    .disabled-text {
        color: gray;
    }
</style>

@{
    ViewBag.Title = "User Authentication Settings";

    var isTwoFactorPhoneNumberVerified = @Model.IsTwoFactorPhoneNumberVerified;
    var isTwoFactorEmailVerified = @Model.IsTwoFactorEmailVerified;
}

@*Security*@
@Html.AntiForgeryToken()

@*Success Message*@
@if (!string.IsNullOrWhiteSpace(Model.SuccessMessage))
{
    <div id="successMessage" name="successMessage" class="alert alert-success alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <p>@Model.SuccessMessage</p>
    </div>
}

@*Info Message*@
@if (!string.IsNullOrWhiteSpace(Model.InfoMessage))
{
    <div class="alert alert-info alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <p>@Model.InfoMessage</p>
    </div>
}

@*Error Message*@
@if (!string.IsNullOrWhiteSpace(Model.ErrorMessage))
{
    <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <p>@Model.ErrorMessage</p>
    </div>
}

@*Page Title*@
<div class="company-info">
    <div class="row">
        <div class="col-md-12">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
    </div>
</div>
    
@*Verify Phone Number Modal*@
<div class="modal fade" id="verifyPhoneNumberModal" name="verifyPhoneNumberModal" tabindex="-1" role="dialog" aria-labelledby="verifyPhoneNumberModal">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="verifyPhoneNumberModal">Verify Phone Number</h4>
            </div>
            <div class="modal-body">
                Loading...
            </div>
        </div>
    </div>
</div>
    
@*Verify Email Modal*@
<div class="modal fade" id="verifyEmailModal" name="verifyEmailModal" tabindex="-1" role="dialog" aria-labelledby="verifyEmailModal">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="verifyEmailModal">Verify Email Address</h4>
            </div>
            <div class="modal-body">
                Loading...
            </div>
        </div>
    </div>
</div>

@using (Html.BeginForm("UserSetup", "TwoFactorAuthentication", FormMethod.Post, new { id = "frmUserSetup", name = "frmUserSetup" }))
{
    @* *** We only need hidden fields for values that are not editable (i.e. disabled controls, or viewmodel data that does not get edited by the user) *** *@

    if (!Model.IsTwoFactorSmsEnabledByCompany)
    {
        @Html.HiddenFor(x => x.EnableTwoFactorSms)
    }

    if (!Model.IsTwoFactorEmailEnabledByCompany)
    {
        @Html.HiddenFor(x => x.EnableTwoFactorEmail)
    }
    
    @Html.HiddenFor(x => x.TwoFactorPhoneNumberOriginal)
    @Html.HiddenFor(x => x.TwoFactorEmailOriginal)

    @Html.HiddenFor(x => x.IsTwoFactorSmsEnabledByCompany)
    @Html.HiddenFor(x => x.IsTwoFactorEmailEnabledByCompany)

    @Html.HiddenFor(x => x.IsTwoFactorPhoneNumberVerified)
    @Html.HiddenFor(x => x.IsTwoFactorEmailVerified)

    @Html.HiddenFor(x => x.SmsNeedsVerification)
    @Html.HiddenFor(x => x.EmailNeedsVerification)

    @Html.HiddenFor(x => x.OpenVerifyPhoneNumberModal)
    @Html.HiddenFor(x => x.OpenVerifyEmailModal)

    @Html.HiddenFor(x => x.IsMfaMandatoryForUser)
    @Html.HiddenFor(x => x.UpdatePhoneNumber)

    <div class="container-fluid master">
        <div class="row authentication-type">

            @*Two Factor Authentication Settings*@
            <fieldset>
                <legend>Two Factor Authentication</legend>

                <div class="container-fluid container-two-factor">

                    @*Two Factor Prompt*@
                    <div class="row setting-prompt">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            @if (Model.IsMfaMandatoryForUser)
                            {
                                <p><strong>Two-factor authentication is required by your company policy.</strong></p>
                                <p>You can update your phone number but cannot disable two-factor authentication.</p>
                            }
                            else
                            {
                                <p>Select which methods you would like to use to receive two factor authentication codes:</p>
                            }
                        </div>
                    </div>

                    @*Text/SMS*@
                    <div class="row">
                        <div class="col-xs-1 setting-control">
                            @if (Model.IsMfaMandatoryForUser)
                            {
                                @Html.CheckBoxFor(x => x.EnableTwoFactorSms, new { id = "smsCheckBox", name = "smsCheckBox", @class = "", disabled = "disabled", @checked = "checked"})
                            }
                            else
                            {
                                @Html.CheckBoxFor(x => x.EnableTwoFactorSms, new { id = "smsCheckBox", name = "smsCheckBox", @class = ""})
                            }
                        </div>
                        <div id="divSmsLabel" name="divSmsLabel" class="col-xs-10 setting-label">
                            @Html.LabelFor(x => x.EnableTwoFactorSms, null, new { @class = "" })
                            @if (Model.IsMfaMandatoryForUser)
                            {
                                <span class="text-muted">(Required by company policy)</span>
                            }
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-10 col-xs-offset-1">
                            @Html.TextBoxFor(x => x.TwoFactorPhoneNumber, new { id = "phoneNumber", name = "phoneNumber", @class = "form-control input-control-text", placeholder = "Phone Number", @readonly = true })
                            
                            @if (Model.IsMfaMandatoryForUser && Model.EnableTwoFactorSms && Model.IsTwoFactorPhoneNumberVerified)
                            {
                                @Html.HiddenFor(m => m.UpdatePhoneNumber)
                                @* <input id="btnUpdatePhoneNumber" name="btnUpdatePhoneNumber" type="button" value="Update Phone Number" class="btn btn-thinkware" style="margin-right: 10px;"/> *@
                                <button class="btn btn-thinkware" style="margin-right: 10px;" onclick="() => SubmitForUpdatePhoneNumber()">
                                    Update Phone Number
                                </button>
                            }
                        </div>
                    </div>

                    <div id="divSmsDescription" name="divSmsDescription" class="row setting-description">
                        <div class="col-xs-1">
                        </div>
                        <div class="col-xs-10">
                            @if (!Model.IsTwoFactorSmsEnabledByCompany)
                            {
                                <p>Your company has not enabled Text/SMS for receiving two factor authentication codes.</p>
                            }
                            else
                            {
                                <p>
                                    Enabling Text/SMS authentication allows you to set up a phone number for receiving two factor authentication codes on login. 
                                    When both Text/SMS and email authentication are enabled, Text/SMS will take priority.
                                </p>
                            }
                        </div>
                    </div>

                    @*Email*@
                    <div class="row">
                        <div class="col-xs-1 setting-control">
                            @Html.CheckBoxFor(x => x.EnableTwoFactorEmail, new { id = "emailCheckBox", name = "emailCheckBox" })
                        </div>
                        <div id="divEmailLabel" name="divEmailLabel" class="col-xs-10 setting-label">
                            @Html.LabelFor(x => x.EnableTwoFactorEmail, null, new { @class = "" })
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-10 col-xs-offset-1">
                            @Html.TextBoxFor(x => x.TwoFactorEmail, new { id = "email", name = "email", @class = "form-control input-control-text", placeholder = "Email Address" })
                            
                            <button id="btnVerifyEmail" name="btnVerifyEmail" title="Verify Email" class="btn btn-thinkware verify-button">Verify</button>
                            <a href="@Url.Action("VerifyEmail", "TwoFactorAuthentication", new {
                                    a = Model.EnableTwoFactorSms,
                                    b = Model.TwoFactorPhoneNumber,
                                    c = Model.IsTwoFactorPhoneNumberVerified,
                                    d = Model.EnableTwoFactorEmail,
                                    f = Model.TwoFactorEmail,
                                    g = Model.IsTwoFactorEmailVerified
                                })" id="lnkOpenVerifyEmailModal" name="lnkOpenVerifyEmailModal" title="Verify Email" class="btn btn-thinkware hidden"
                                data-toggle="modal" data-target="#verifyEmailModal" data-remote="false" data-backdrop="static">Verify
                            </a>

                            <span id="emailVerifiedIcon" name="emailVerifiedIcon" class="fa fa-check-circle" title="Verified"></span>
                        </div>
                        <div class="col-xs-10 col-xs-offset-1">
                            @Html.ValidationMessageFor(x => x.TwoFactorEmail, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div id="divEmailDescription" name="divEmailDescription" class="row setting-description">
                        <div class="col-xs-1">
                        </div>
                        <div class="col-xs-10">
                            @if (!Model.IsTwoFactorEmailEnabledByCompany)
                            {
                                <p>Your company has not enabled email for receiving two factor authentication codes.</p>
                            }
                            else
                            {
                                <p>
                                    Enabling email authentication allows you to set up an email address for receiving two factor authentication codes on login.
                                </p>
                            }
                        </div>
                    </div>
                </div>
            </fieldset>
        </div>

        @*Save*@
        <div class="row buttons">
            <div class="col-xs-12">
                <div class="pull-right">
                    <input id="btnSave" name="btnSave" type="button" value="Save" class="btn btn-thinkware"/>
                </div>
            </div>
        </div>
    </div>
}

<script src="~/Scripts/ThinkWareScripts/DataUtilities.js"></script>

@*Disable Text/SMS checkbox*@
@if (!Model.IsTwoFactorSmsEnabledByCompany)
{
    <script>
        $('document').ready(function () {
            $('#smsCheckBox').attr('disabled', 'disabled');
            $('#divSmsLabel').addClass('disabled-text');
            $('#divSmsDescription').addClass('disabled-text');
        });
    </script>
}

@*Disable Email checkbox*@
@if (!Model.IsTwoFactorEmailEnabledByCompany)
{
    <script>
        $('document').ready(function () {
            $('#emailCheckBox').attr('disabled', 'disabled');
            $('#divEmailLabel').addClass('disabled-text');
            $('#divEmailDescription').addClass('disabled-text');
        });
    </script>
}

<script>
    $('document').ready(function () {
        @*Hide success message after a short time*@
        $("#successMessage").delay('10000').fadeOut(5000, function () { });

        @*Show information input fields for enabled authentication methods*@
        TogglePhoneNumberDisplay();
        ToggleEmailDisplay();

        $('#smsCheckBox').on('change', function () {
            TogglePhoneNumberDisplay();
        });

        $('#emailCheckBox').on('change', function () {
            ToggleEmailDisplay();
        });

        $('#phoneNumber').on('keyup', function () {
            TogglePhoneNumberVerifyButtonAndIconDisplay();
        });

        $('#email').on('keyup', function () {
            ToggleEmailVerifyButtonAndIconDisplay();
        });

        $('#btnVerifyEmail').on('click', function () {
            SubmitForVerifyEmailModal();
        });

        // $("#verifyPhoneNumberModal").on("show.bs.modal", function (e) {
        //     var link = $(e.relatedTarget);
        //     $(this).find(".modal-body").load(link.attr("href"));
        // });

        @*Show the verify email address modal*@
        $("#verifyEmailModal").on("show.bs.modal", function (e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-body").load(link.attr("href"));
        });

        $('#btnSave').on('click', function () {
            SubmitUserSetup();
        });

        @*Prevent form submission on Enter*@
        $(window).keydown(function(event){
            if (event.keyCode == 13) {
                event.preventDefault();
                return false;
            }
        });

        @*Show verify modal if we are reloading due to a user clicking verify*@
        OpenVerifyModal();
    });



    @* ------------------------------------------------------------
        Shows/Hides the phone number textbox when Text/SMS is
        enabled/disabled
    ------------------------------------------------------------ *@
    function TogglePhoneNumberDisplay() {
        var value = $('#smsCheckBox').is(':checked');

        if (value == true) {
            @*Show phone number info*@
            $('#phoneNumber').attr('disabled', false);
            $('#phoneNumber').show();

            TogglePhoneNumberVerifyButtonAndIconDisplay();

            @*Show the validation message if it was previously hidden*@
            $('span[data-valmsg-for="TwoFactorPhoneNumber"].field-validation-error').show();
        }
        else {
            @*Hide phone number info*@
            $('#phoneNumber').attr('disabled', true); @*Sends null back to the server on submit*@
            $('#phoneNumber').hide();
            $('#btnVerifyPhoneNumber').hide();
            $('#phoneNumberVerifiedIcon').hide();

            @*Remove the validation message if it is shown*@
            $('span[data-valmsg-for="TwoFactorPhoneNumber"].field-validation-error').hide();
        }
    }



    @* ------------------------------------------------------------
        Shows/Hides the email textbox when email is
        enabled/disabled
    ------------------------------------------------------------ *@
    function ToggleEmailDisplay() {
        var value = $('#emailCheckBox').is(':checked');

        if (value == true) {
            @*Show email info*@
            $('#email').attr('disabled', false);
            $('#email').show();

            ToggleEmailVerifyButtonAndIconDisplay();

            @*Show the validation message if it was previously hidden*@
            $('span[data-valmsg-for="TwoFactorEmail"].field-validation-error').show();
        }
        else {
            @*Hide email info*@
            $('#email').attr('disabled', true); @*Sends null back to the server on submit*@
            $('#email').hide();
            $('#btnVerifyEmail').hide();
            $('#emailVerifiedIcon').hide();

            @*Remove the validation message if it is shown*@
            $('span[data-valmsg-for="TwoFactorEmail"].field-validation-error').hide();
        }
    }



    @* ------------------------------------------------------------
        Shows the Verify button when the phone number is unverified and
        shows the Verified icon when the phone number is verified.
    ------------------------------------------------------------ *@
    function TogglePhoneNumberVerifyButtonAndIconDisplay() {
        var phoneNumber = $('#phoneNumber').val();

        @*If the phone number has been changed, it is no longer valid*@
        if ('@Model.IsTwoFactorPhoneNumberVerified' == 'True' && FormatPhoneNumber('@Model.TwoFactorPhoneNumberOriginal') == FormatPhoneNumber(phoneNumber)) {
            @*Valid*@
            $('#btnVerifyPhoneNumber').hide();
            $('#phoneNumberVerifiedIcon').show();

            @*Remove the validation message if it is shown*@
            $('span[data-valmsg-for="TwoFactorEmail"].field-validation-error').hide();
            $('#email').css('border', '1px solid #ccc');
        }
        else {
            @*Invalid*@
            $('#btnVerifyPhoneNumber').show();
            $('#phoneNumberVerifiedIcon').hide();
        }
    }



    @* ------------------------------------------------------------
        Shows the Verify button when the email is unverified and
        shows the Verified icon when the email is verified.
    ------------------------------------------------------------ *@
    function ToggleEmailVerifyButtonAndIconDisplay() {
        var email = $('#email').val();

        @*If the email has been changed, it is no longer valid*@
        if ('@Model.IsTwoFactorEmailVerified' == 'True' && '@Model.TwoFactorEmailOriginal' == email) {
            @*Valid*@
            $('#btnVerifyEmail').hide();
            $('#emailVerifiedIcon').show();

            @*Remove the validation message if it is shown*@
            $('span[data-valmsg-for="TwoFactorEmail"].field-validation-error').hide();
            $('#email').css('border', '1px solid #ccc');
        }
        else {
            @*Invalid*@
            $('#btnVerifyEmail').show();
            $('#emailVerifiedIcon').hide();
        }
    }



    @* ------------------------------------------------------------
        Posts the page in order to populate the viewmodel with the
        user's entered phone number and then returns to the page,
        where the verify phone number modal will open.
    ------------------------------------------------------------ *@
    function SubmitForVerifyPhoneNumberModal() {
        $('#OpenVerifyPhoneNumberModal').val(true);

        $('#frmUserSetup').submit();
    }



    @* ------------------------------------------------------------
        Posts the page in order to populate the viewmodel with the
        user's entered email and then returns to the page,
        where the verify email modal will open.
    ------------------------------------------------------------ *@
    function SubmitForVerifyEmailModal() {
        $('#OpenVerifyEmailModal').val(true);

        $('#frmUserSetup').submit();
    }



    @* ------------------------------------------------------------
        Opens a verify modal for the authentication method the
        user selected as wanting to verify.
    ------------------------------------------------------------ *@
    function OpenVerifyModal() {
        if ('@Model.OpenVerifyPhoneNumberModal' == 'True') {
            $('#lnkOpenVerifyPhoneNumberModal').click();
        }

        if ('@Model.OpenVerifyEmailModal' == 'True') {
            $('#lnkOpenVerifyEmailModal').click();
        }

        @*Reset flags*@
        $('#OpenVerifyPhoneNumberModal').val(false);
        $('#OpenVerifyEmailModal').val(false);
    }



    @* ------------------------------------------------------------
        Sets flags to indicate whether the selected authentication
        methods have been validated and then submits the form.
    ------------------------------------------------------------ *@
    function SubmitUserSetup() {
        var smsEnabled = $('#smsCheckBox').is(':checked');
        var emailEnabled = $('#emailCheckBox').is(':checked');

        var phoneNumber = $('#phoneNumber').val();
        var email = $('#email').val();

        if (smsEnabled && ('@isTwoFactorPhoneNumberVerified' == 'False' || FormatPhoneNumber('@Model.TwoFactorPhoneNumberOriginal') != FormatPhoneNumber(phoneNumber))) {
            $('#SmsNeedsVerification').val(true);
        }
        else {
            $('#SmsNeedsVerification').val(false);
        }

        if (emailEnabled && ('@isTwoFactorEmailVerified' == 'False' || '@Model.TwoFactorEmailOriginal' != email)) {
            $('#EmailNeedsVerification').val(true);
        }
        else {
            $('#EmailNeedsVerification').val(false);
        }

        $('#frmUserSetup').submit();
    }

    @* ------------------------------------------------------------
        Posts the page to trigger the phone number update flow
    ------------------------------------------------------------ *@

    function SubmitForUpdatePhoneNumber() {
        $('#UpdatePhoneNumber').val(true);
        $('#frmUserSetup').submit();
    }
    
</script>