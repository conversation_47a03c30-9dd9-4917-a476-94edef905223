@{
    Layout = "~/Views/Shared/_LayoutNoWrap.cshtml";
}

<link rel="stylesheet" href="~/Scripts/dist/style.css" />
<div id="mfa-setup-app"></div>

@* Inject app context so client side vuejs can read *@
<script type="application/json" id="app-context">
  @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewBag.AppContext))
</script>

<script type="module" src="~/Scripts/dist/mfa-setup.js"></script>
