@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model DarwiNet2._0.Data.OBClientSetupTaskField
@using OBFieldAccess = DataDrivenViewEngine.Models.Core.enOBFieldAccess
@using FieldType = DataDrivenViewEngine.Models.Core.enFieldType
@using OBFieldLocked = DataDrivenViewEngine.Models.Core.enOBFieldLock
@using OBFieldReq = DataDrivenViewEngine.Models.Core.enOBFieldRequirement

@{
    ViewBag.Title = "Create Tasks - " + @GlobalVariables.ProfileName;
    var mySetupID = Model.SetupID;
    //Do not change
    ViewBag.Page = "OBClientSetupTaskFieldsCreate";
    //
}
@Html.Partial("~/Views/Navigation/_ClientPartialTabs.cshtml", mySetupID)

@using (Html.BeginForm("Create", "OBClientSetupTaskFields", new { id = Model.SetupID, tid = Model.TaskID }, FormMethod.Post, new { @id = "obclientsetuptaskfieldscreate", @name = "obclientsetuptaskfieldscreate" }))
{
    @*@Html.AntiForgeryToken()*@
    <div class="form-horizontal">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.SetupID, new { @class = "form-control", @required = "required", @readonly = "readonly" })
        @Html.HiddenFor(model => model.TaskID, new { @class = "form-control", @required = "required", @readonly = "readonly" })

        <div class="form-group">

            @Html.LabelFor(model => model.FLabel, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FLabel", GlobalVariables.LanguageID))
            <div class="col-md-4">

                @Html.TextBoxFor(model => model.FLabel, new { @class = "field-required form-control", @maxlength = "500", @required = "required" })
                @Html.ValidationMessageFor(model => model.FLabel, "", new { @class = "text-danger" })

            </div>
            @Html.LabelFor(model => model.FType, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FType", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("FType", EnumHelper.GetSelectList(typeof(FieldType)), new { id = "FType", name = "FType", @class = "form-control" })
                @Html.ValidationMessageFor(model => model.FType, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.FSize, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FSize", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.TextBoxFor(model => model.FSize, new { @class = "form-control", data_toggle = "tooltip", data_placement = "top", title = "The maximum amount of text allowed in the input field." })
                @Html.ValidationMessageFor(model => model.FSize, "", new { @class = "text-danger" })
            </div>
            <div id="valueoptions">
                @Html.LabelFor(model => model.FValueOptions, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FValueOptions", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.FValueOptions, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.FValueOptions, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.FRequired, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FRequired", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("FRequired", EnumHelper.GetSelectList(typeof(OBFieldReq)), new { id = "FRequired", name = "FRequired", @class = "form-control", data_toggle = "tooltip", data_placement = "top", title = "Select if this task field is required and who is required to use it." })
                @Html.ValidationMessageFor(model => model.FRequired, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.FLocked, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FLocked", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("FLocked", EnumHelper.GetSelectList(typeof(OBFieldLocked)), new { id = "FLocked", name = "FLocked", @class = "form-control", data_toggle = "tooltip", data_placement = "top", title = "Set permissions for who can modify this field." })
                @Html.ValidationMessageFor(model => model.FLocked, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.CCAccess, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("CCAccess", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("CCAccess", EnumHelper.GetSelectList(typeof(OBFieldAccess)), new { id = "CCAccess", name = "CCAccess", @class = "form-control" })
                @Html.ValidationMessageFor(model => model.CCAccess, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.EEAccess, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EEAccess", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("EEAccess", EnumHelper.GetSelectList(typeof(OBFieldAccess)), new { id = "EEAccess", name = "EEAccess", @class = "form-control" })
                @Html.ValidationMessageFor(model => model.EEAccess, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.FTip, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FTip", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.TextBoxFor(model => model.FTip, new { @class = "form-control", data_toggle = "tooltip", data_placement = "top", title = "Enter a text tip to display to the user." })
                @Html.ValidationMessageFor(model => model.FTip, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <div class="pull-right">
                    @Html.ActionLink("Cancel", "Index", new { @id = @Model.SetupID, tid = @Model.TaskID }, new { @class = "btn btn-thinkware" }) <input type="submit" value="Create" class="btn btn-thinkware" />
                </div>
            </div>
        </div>

    </div>
}

@section scripts{
    <script>
        $("#obclientsetuptaskfieldscreate").validate();
    </script>
    <script>
        $(document).ready(function () {
            $("#FType").on("change", function () {
                if ($(this).val() == 203) {
                    $('#valueoptions').show();
                } else {
                    $('#valueoptions').hide(); /* If you want to be hidden if it's not */
                }
            }).trigger("change");

        })
    </script>
}