@model IEnumerable<DarwiNet2._0.Data.OBProcessMonitorVM>
@using OBProcessTaskStatus = DataDrivenViewEngine.Models.Core.enOBProcessTaskStatus
@using PayPeriod = DataDrivenViewEngine.Models.Core.enPayPeriods
@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@{
    ViewBag.Title = "On-Boarding Monitor";
    var myLevel = @GlobalVariables.DNETLevel;
}
@section styles{
    <link href="~/Content/sweetalert.css" rel="stylesheet" />
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    if (ViewBag.IsSampleCompany)
    {
        <div class="alert alert-danger alert-dismissible" role="alert" id="globalFail">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <p>This screen is unavailable in this company. Please log into a company that has been synced from Darwin</p>
        </div>
    }
    else
    {
        <div class="company-info">
            <div class="row">
                <div class="col-md-6 col-sm-6">
                    <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                    <div class="colored-line-left"></div>
                </div>

            </div>
        </div>
        <script type="text/javascript">
            $(function () {
                $('#resend').click(function () {
                });
            });
        </script>
        <div class="toolbar" style="padding-bottom: 10px;">
            <div class="row">
                <div class="col-md-3 pull-left">
                    <div class="input-group">
                        <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                        <input type="text" class="form-control" id='FieldFilter' placeholder="Search Employee Name">
                    </div>
                </div>
                <p class="create-pad pull-right">

                    @{
                        if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
                        {

                            if (ViewBag.PEOSecurityRole)
                            {

                                <a href="@Url.Action("ClientFinalize", "OBProcessFinalize")" class="btn btn-thinkware">Client Finalize</a>
                            }
                            <a href="@Url.Action("Create","OBprocessMonitor")" class="btn btn-thinkware"><i class="fa fa-plus fa-lg fa-fw"></i>Add New Employee</a>
                        }
                    }

                </p>
            </div>
            
            <div class="row">
                <p class="create-pad pull-right">
                    @{
                        if (ViewBag.IsThirdPartyATSIntegrationSetup != null && 
                            ViewBag.IsThirdPartyATSIntegrationSetup == true && 
                            (GlobalVariables.DNETLevel == DNetAccessLevel.Client || GlobalVariables.DNETLevel == DNetAccessLevel.System))
                        {
                            <a href="#"
                                class="btn btn-thinkware"
                                onclick="showHireologyEmployees()"
                                title="Status Of Hireology Candidates">
                                <i class="fa fa-users fa-lg"></i> Hireology Candidates 
                            </a>
                        }
                    }
                </p>
            </div>
        </div>
        <div class="table-bottom">
            @(Html.Kendo().Grid<DarwiNet2._0.Data.OBProcessMonitorVM>()
        .Name("grid")
        .Columns(columns =>
        {
        if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
        {
            columns.Bound(e => e.ClientID).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Client", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Client", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Client", GlobalVariables.LanguageID) }).Width(90);
        }
            columns.Bound(e => e.UserID).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("UserID", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("UserID", GlobalVariables.LanguageID)).Width(150).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("UserID", GlobalVariables.LanguageID) });
            columns.Bound(e => e.LoginCode).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Login Code", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Login Code", GlobalVariables.LanguageID)).Width(150).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Login Code", GlobalVariables.LanguageID) });
        if (GlobalVariables.DNETLevel == DNetAccessLevel.System || GlobalVariables.DNETLevel == DNetAccessLevel.Client)
        {
            columns.Bound(e => e.ProfileName).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Profile", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Profile", GlobalVariables.LanguageID)).Width(150).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Profile", GlobalVariables.LanguageID) });
        }
        columns.Bound(e => e.OnBoardingType).Width(180).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("OnBoarding Type", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("OnBoarding Type", GlobalVariables.LanguageID));
        columns.Bound(e => e.EmployeeIDDisplay).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Employee ID", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Employee ID", GlobalVariables.LanguageID)).Width(150).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID) });
        columns.Bound(e => e.EmployeeName).Width(300).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID));
        columns.Bound(e => e.eeTaskName).Width(150).Title(FieldTranslation.GetLabel("EmployeeTask", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeTask", GlobalVariables.LanguageID) });
        columns.Bound(e => e.eeTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) }).Width(150);
        columns.Bound(e => e.ccTaskname).Title(FieldTranslation.GetLabel("ClientTask", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("ClientTask", GlobalVariables.LanguageID) }).Width(150);
        columns.Bound(e => e.ccTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) }).Width(150);
        columns.Bound(e => e.DueDate).Format("{0:MM/dd/yyyy}").Width(115).Title(FieldTranslation.GetLabel("DueDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("DueDate", GlobalVariables.LanguageID) });
        columns.Bound(e => e.Division).Hidden(true).Title(FieldTranslation.GetLabel("Division", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Division", GlobalVariables.LanguageID) });
        columns.Bound(e => e.CCSubmitBy).Hidden(true).Title(FieldTranslation.GetLabel("Submitted By", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Submitted By", GlobalVariables.LanguageID) });
        //columns.Bound(e => e.Profile).Hidden(true).Title(FieldTranslation.GetLabel("Submitted By", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Submitted By", GlobalVariables.LanguageID) });
        if (ViewBag.PEOSecurityRole)
        {
            columns.Template(@<text>

                </text>).Title("Action").ClientTemplate(@"#
                 if (UserID != null && Password != null) { #<div class='icon-center'><a href='" + Url.Action("OBEEInfo", "OBProcessMonitor", new { employeeId = "#= EmployeeID#" }) + "' title='More Information' id='more_info' data-desc='" + "#=EmployeeName#" + "' data-toggle='modal' data-target='\\#infoModal' data-id='#=EmployeeID#' data-remote='false'><i class='icon-blue fa fa-info fa-fw fa-lg'></i></a>" +
                  "<a href='\\#' title='Delete' id='delete' data-desc='" + "#=EmployeeName#" + "' data-id='" + "#=EmployeeID#'><i class='icon-red fa fa-times fa-fw fa-lg'></i></a>" +
                  "<a href='" + Url.Action("LoginAsFromClient", "Home", new { UserId = "#= UserID#" }) + "' title='Login As'><i class='icon-california fa fa-external-link fa-fw fa-lg'></i></a></div># }" +
                  "else {#<div class='icon-center'><a href='" + Url.Action("OBEEInfo", "OBProcessMonitor", new { employeeId = "#= EmployeeID#" }) + "' title='More Information' id='more_info' data-desc='" + "#=EmployeeName#" + "' data-toggle='modal' data-target='\\#infoModal' data-id='#=EmployeeID#' data-remote='false'><i class='icon-blue fa fa-info fa-fw fa-lg'></i></a>" +
                  "<a href='\\#' title='Delete' id='delete' data-desc='" + "#=EmployeeName#" + "' data-id='" + "#=EmployeeID#'><i class='icon-red fa fa-times fa-fw fa-lg'></i></a>#} #").Width(150).HeaderHtmlAttributes(new { @class = "header-center" });

            }
            else
            {
            columns.Template(@<text>
            </text>)
                      .Title("Action")
                      .ClientTemplate("<div class='icon-center'>" +
                      "<a href='" + Url.Action("OBEEInfo", "OBProcessMonitor", new { employeeId = "#= EmployeeID#" }) + "' title='More Information' id='more_info' data-desc='" + "#=EmployeeName#" + "' data-toggle='modal' data-target='\\#infoModal' data-id='#=EmployeeID#' data-remote='false'><i class='icon-blue fa fa-info fa-fw fa-lg'></i></a>" +
                      "<a href='" + Url.Action("OBEEDocuments", "OBProcessMonitor", new { employeeId = "#= EmployeeID#" }) + "' title='Download All Documents' id='download' data-desc='" + "#=EmployeeName#" + "' data-toggle='modal' data-target='\\#docModal' data-id='#=EmployeeID#' data-remote='false'><i class='icon-blue fa fa-download fa-fw fa-lg'></i></a>" +
                      "<a href='\\#' title='Delete' id='delete' data-desc='" + "#=EmployeeName#" + "' data-id='" + "#=EmployeeID#'><i class='icon-red fa fa-times fa-fw fa-lg'></i></a>" + "</div>"
                         ).Width(100).HeaderHtmlAttributes(new { @class = "header-center" });
            }
        })
                                .ColumnMenu()
                                .Sortable()
                                .Pageable()
                                .Groupable()
                                .Filterable()
                                .Scrollable()
                                .ClientDetailTemplateId("template")
                                .HtmlAttributes(new { style = "height: 647px" })
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .PageSize(15)
                                        .Read(read => read.Action("GetEEs_Read", "OBProcessMonitor"))

                                                )
                                .Events(events => events.DataBound("dataBound"))
            )
            @if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
            {
                <script id="template" type="text/kendo-tmpl">
                    @(Html.Kendo().Grid<DarwiNet2._0.Data.OBProcessTask>()
            .Name("grid_#=EmployeeID#") // template expression, to be evaluated in the master context
            .Columns(columns =>
            {
                columns.Template(@<text>
                </text>)
                      .ClientTemplate(
                      "\\# if(CAccess != -1 && TaskType != 1 ){\\#" +
                      "<div class='icon-center table-icons'><a href='" + @Url.Action("Index", "OBProcess", new { taskId = "\\#=TaskID\\#" }, null) + "'><i class='icon-blue fa fa-eye fa-lg fa-fw'></i></a></div>"
                          + "\\#} \\#"
                         ).Width(80);
                columns.Bound(o => o.TaskName).Title(FieldTranslation.GetLabel("TaskName", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskName", GlobalVariables.LanguageID) });
                columns.Bound(o => o.EStartDate).Format("{0:MM/dd/yyyy}").Title(FieldTranslation.GetLabel("EmployeeStartDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeStartDate", GlobalVariables.LanguageID) });
                columns.Bound(o => o.eeTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) });
                columns.Bound(o => o.CStartDate).Format("{0:MM/dd/yyyy}").Title(FieldTranslation.GetLabel("ClientStartDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("ClientStartDate", GlobalVariables.LanguageID) });
                columns.Bound(o => o.ccTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) });
                columns.Bound(o => o.LastUpdate).Format("{0:MM/dd/yyyy}").Title(FieldTranslation.GetLabel("LastUpdate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("LastUpdate", GlobalVariables.LanguageID) });

            })
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .PageSize(10)
                                                .Read(read => read.Action("GetEETasks_Read", "OBProcessMonitor", new { employeeID = "#=EmployeeID#" }))
                            )
                            .Pageable()
                            .Sortable()
                            .ToClientTemplate()
                    )
                </script>
                <script>
                    function dataBound() {
                        // this.expandRow(this.tbody.find("tr.k-master-row").first());
                    }
                </script>
            }
            else
            {
                <script id="template" type="text/kendo-tmpl">
                    @(Html.Kendo().Grid<DarwiNet2._0.Data.OBProcessTask>()
            .Name("grid_#=EmployeeID#") // template expression, to be evaluated in the master context
            .Columns(columns =>
            {
                columns.Template(@<text>
                </text>)
                      .ClientTemplate("").Width(50);
                columns.Bound(o => o.TaskName).Title(FieldTranslation.GetLabel("TaskName", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskName", GlobalVariables.LanguageID) });
                columns.Bound(o => o.EStartDate).Format("{0:MM/dd/yyyy}").Title(FieldTranslation.GetLabel("EmployeeStartDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeStartDate", GlobalVariables.LanguageID) });
                columns.Bound(o => o.eeTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) });
                columns.Bound(o => o.CStartDate).Format("{0:MM/dd/yyyy}").Title(FieldTranslation.GetLabel("ClientStartDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("ClientStartDate", GlobalVariables.LanguageID) });
                columns.Bound(o => o.ccTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) });
                columns.Bound(o => o.LastUpdate).Format("{0:MM/dd/yyyy}").Title(FieldTranslation.GetLabel("LastUpdate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("LastUpdate", GlobalVariables.LanguageID) });

            })
                            .DataSource(dataSource => dataSource
                                .Ajax()
                                .PageSize(10)
                                                .Read(read => read.Action("GetEETasks_Read", "OBProcessMonitor", new { employeeID = "#=EmployeeID#" }))
                            )
                            .Pageable()
                            .Sortable()
                            .ToClientTemplate()
                    )
                </script>
            }
            <script>
                function dataBound() {
                }
            </script>
        </div>
        <div class="modal fade" id="hireologyEmpListModal" tabindex="-1" role="dialog" aria-labelledby="hireologyEmpListModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="hireologyEmpListModalLabel"><b>Hireology Candidates</b></h4>
                    </div>
                    <div class="modal-body">
                        @Html.Partial("_HireologyStatusGrid")
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="infoModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="infoModalLabel">Employee Information</h4>
                    </div>
                    <div class="modal-body">
                        Generating...
                    </div>@*
                        <div class="modal-footer">
                            <button type="button" class="btn btn-thinkware" data-dismiss="modal">Close</button>
                            $1$<button type="button" class="btn btn-primary">Save changes</button>#1#
                        </div>*@
                </div>
            </div>
        </div>
        <div class="modal fade" id="docModal" tabindex="-1" role="dialog" aria-labelledby="docModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="docModalLabel">Download All Documents</h4>
                    </div>
                    <div class="modal-body">
                        Generating...
                    </div>@*
                        <div class="modal-footer">
                            <button type="button" class="btn btn-thinkware" data-dismiss="modal">Close</button>
                            $1$<button type="button" class="btn btn-primary">Save changes</button>#1#
                        </div>*@
                </div>
            </div>
        </div>
        @section scripts{
            <script src="~/Scripts/bootbox.min.js"></script>
            <script src="~/Scripts/jquery.datetimepicker.js"></script>
            <link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
            <script src="~/Scripts/MaskedInput.js"></script>
            <script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
            @if (GlobalVariables.DNETLevel != DNetAccessLevel.Employee && ViewBag.Access != MenuAccessLevel.ReadOnly)
            {
                <script>
                    $("#infoModal").on("show.bs.modal", function (e) {
                        var link = $(e.relatedTarget);
                        var url = encodeURI(link.attr("href"));
                        $(this).find(".modal-body").load(url, function () {
                            $('#BirthDate').kendoMaskedDatePicker();
                        });
                    });
                </script>
            }
            else
            {
                <script>
                    $("#infoModal").on("show.bs.modal", function (e) {
                        var link = $(e.relatedTarget);
                        var url = encodeURI(link.attr("href"));
                        $(this).find(".modal-body").load(url, function () {
                            $('#BirthDate').addClass('form-control');
                        });
                    });
                </script>
            }
            <script>
                $("#docModal").on("show.bs.modal", function (e) {
                    var link = $(e.relatedTarget);
                    $(this).find(".modal-body").load(link.attr("href"));
                });
            </script>

            <script>
                $("#resentWelcome").validate({
                    rules: {
                        BirthDate: {
                            date: true
                        }
                    },
                    errorPlacement: function (error, element) {
                        if (element.attr("name") == "BirthDate")
                            error.insertAfter(".error-msg");
                        else
                            error.insertAfter(element);
                    }
                });


            </script>
            <script>
                $(document).ready(function () {
                    var grid = $("#NewEmployeeTable").kendoGrid({
                        dataSource: {
                            pageSize: 15
                        },
                        sortable: true,
                        pageable: true,
                        filterable: true,
                        groupable: true,
                        scrollable: false,
                        resizable: true
                    }).data("kendoGrid");
                });
                
                function showHireologyEmployees() {
                    const grid = $("#HireologyEmployeesStatusGrid").data("kendoGrid");
                    $.ajax({
                        type: "GET",
                        url: "/OBProcessMonitor/GetHireologyCandidates",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        success: function (data) {
                            if (grid && data) {
                                grid.dataSource.data(data);
                                if (data.length > 0) {
                                    grid.dataSource.page(1);
                                }
                            }
                        },
                        error: function (error) {
                            console.error("Error loading Hireology data:", error);
                        }
                    });

                    $('#hireologyEmpListModal').modal();
                }
            </script>
            <script>
                $(function () {
                    $(".datepicker").datetimepicker({
                        timepicker: false,
                        format: 'm/d/Y',
                        formatDate: 'm/d/Y'
                    });

                    $('input').tooltip({
                        placement: "top",
                        trigger: "focus"
                    });
                });
            </script>

            <script>
                $(document).ready(function () {
                    $("#FieldFilter").keyup(function () {

                        var value = $("#FieldFilter").val();
                        var grid = $("#grid").data("kendoGrid");

                        if (value) {
                            grid.dataSource.filter({
                                logic: "or",
                                filters: [
                                    { field: "EmployeeName", operator: "contains", value: value }
                                ]
                            })
                        } else {
                            grid.dataSource.filter({});
                        }
                    });
                });
            </script>
            <script>
                $(document).on("click", "td #delete", function (e) {
                    var deletedID = $(this).attr('data-id')
                    var url = "@Url.Action("Delete", "OBProcessMonitor", null)"
                    var prof = $(this).attr('data-desc')
                    bootbox.dialog({
                        message: "Are you sure you want to delete: " + "<strong>" + prof + "</strong>",
                        title: "Delete On Boarding Employee",
                        buttons: {
                            main: {
                                label: "Cancel",
                                className: "btn-primary",
                                callback: function () {
                                    //Example.show("Primary button");
                                }
                            },
                            danger: {
                                label: "Delete",
                                className: "btn-danger",
                                callback: function () {
                                    window.location.href = url + "/?emplId=" + deletedID;
                                }
                            }
                        }
                    });
                });
            </script>
        }
    }

}
