@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch


@using (Html.BeginForm("Create", "OBProcessMonitor", FormMethod.Post, new { id = "obprocessmonitorcreate" }))
{

    @*@Html.AntiForgeryToken()*@
<div class="form-horizontal">
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @{if (ViewBag.Profiles != null)
        {
            if (ViewBag.Profiles.Count > 0)
            {
                <div class="form-group">
                    @Html.Label(FieldTranslation.GetLabel("Profile", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        <select id="SetupID" name="SetupID" class="form-control">
                            @{
                                foreach (var item in ViewBag.Profiles)
                                {
                                    if (ViewBag.DefaultProfile != null)
                                    {
                                        if (item.ID == ViewBag.DefaultProfile)
                                        {
                                            <option value="@item.ID" selected>@item.Name</option>
                                        }
                                        else
                                        {
                                            <option value="@item.ID">@item.Name</option>
                                        }
                                    }
                                    else
                                    {
                                        <option value="@item.ID">@item.Name</option>
                                    }
                                    //}
                                }
                            }
                        </select>
                        @Html.ValidationMessage("SetupID", new { @class = "text-danger" })
                    </div>
                </div>
            }
        }
    }
    @{if (ViewBag.EEClasses != null)
        {
            if (ViewBag.EEClasses.Count > 0)
            {
                <div class="form-group">
                    @Html.Label(FieldTranslation.GetLabel("Employee Class", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        <select id="EmployeeClass" name="EmployeeClass" class="form-control @((ViewBag.IsEEClassRequired == true) ? "required='required'" : string.Empty)">
                            @{
                                foreach (var item in ViewBag.EEClasses)
                                {
                                    @*if (ViewBag.SelectedClass != null)
                                        {
                                            if (item.Code == ViewBag.SelectedClass)
                                            {
                                                <option value="@item.Code" selected>@item.Description</option>
                                            }
                                            else
                                            {
                                                <option value="@item.Code">@item.Description</option>
                                            }
                                        }
                                        else
                                        {*@
                                    {
                                        <option value="@item.Code">@item.Description</option>
                                    }
                                    //}
                                }
                            }
                        </select>
                        @Html.ValidationMessage("EmployeeClass", new { @class = "text-danger" })
                    </div>
                </div>
            }
        }
    }
    <div class="form-group">
        @Html.Label(FieldTranslation.GetLabel("Work State", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            <select id="WorkState" name="WorkState" class="form-control field-required" required="required">
                @{
                    if (ViewBag.WorkStates != null)
                    {
                        foreach (var item in ViewBag.WorkStates)
                        {
                            if (ViewBag.SelectedState != null)
                            {
                                if (ViewBag.SelectedState == item.Code)
                                {
                                    <option value="@item.Code" selected="selected">@item.Description</option>
                                }
                                else
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                            else
                            {
                                if (item.Code == @ViewBag.DefaultWorkState)
                                {
                                    <option value="@item.Code" selected="selected">@item.Description</option>
                                }
                                else
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                        }
                    }
                }
            </select>

            @*@Html.DropDownList("Department", ((SelectList)ViewBag.Departments))
                @Html.DropDownListFor()*@
            @Html.ValidationMessage("WorkState", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.Label(FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            <select id="Department" name="Department" class="field-required form-control" required="required">
                @{
                    if (ViewBag.Departments != null)
                    {
                        foreach (var item in ViewBag.Departments)
                        {
                            if (ViewBag.SelectedDept != null)
                            {
                                if (item.Code == ViewBag.SelectedDept)
                                {
                                    <option value="@item.Code" selected>@item.Description</option>
                                }
                                else
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                            else
                            {
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                        }

                    }
                }
            </select>



            @Html.ValidationMessage("Department", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.Label(FieldTranslation.GetLabel("Position", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            <select id="Position" name="Position" class="form-control @((ViewBag.PositionRequired == true) ? "field-required" : string.Empty)" @((ViewBag.PositionRequired == true) ? "required='required'" : string.Empty)>
                @{
                    if (ViewBag.Positions != null)
                    {
                        foreach (var item in ViewBag.Positions)
                        {
                            if (ViewBag.SelectPos != null)
                            {
                                if (item.Code == ViewBag.SelectPos)
                                {
                                    <option value="@item.Code" selected>@item.Description</option>
                                }
                                else
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                            else
                            {
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                        }
                    }

                }
            </select>

            @Html.ValidationMessage("Position", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.Label(FieldTranslation.GetLabel("Role ID", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            <select id="RoleID" name="RoleID" class="form-control field-required" required="required">
                @{
                    if (ViewBag.AvailableRoles != null)
                    {
                        foreach (var item in ViewBag.AvailableRoles)
                        {
                            if (ViewBag.SelectedRole != null)
                            {
                                if (item.Code == ViewBag.SelectedRole)
                                {
                                    <option value="@item.Code" selected>@item.Description</option>
                                }
                                else
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                            else
                            {
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            }
                        }
                    }
                }
            </select>

            @Html.ValidationMessage("RoleID", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.Label(FieldTranslation.GetLabel("Employment Status", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            @Html.DropDownList("EmploymentStatus", (SelectList)ViewBag.EmplStatuses, new { id = "EmploymentStatus", name = "EmploymentStatus", @class = "form-control" })
            @*@Html.DropDownList("Department", ((SelectList)ViewBag.Departments))
                @Html.DropDownListFor()*@
            @Html.ValidationMessage("EmploymentStatus", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.Label(FieldTranslation.GetLabel("PayPeriod", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
        <div class="col-md-4">
            <select id="PayPeriod" name="PayPeriod" class="form-control">
                @{
                    foreach (var item in ViewBag.PayPeriods)
                    {
                        if (ViewBag.SelectedPeriod != null)
                        {
                            if (item.Text == ViewBag.SelectedPeriod)
                            {
                                <option value="@item.Value" selected>@item.Text</option>
                            }
                            else
                            {
                                <option value="@item.Value">@item.Text</option>
                            }
                        }
                        else
                        {
                            {
                                <option value="@item.Value">@item.Text</option>
                            }
                        }
                    }
                }
            </select>

            @*@Html.DropDownList("Department", ((SelectList)ViewBag.Departments))
                @Html.DropDownListFor()*@
            @Html.ValidationMessage("PayPeriod", new { @class = "text-danger" })
        </div>
    </div>

    @{
        if (ViewBag.BEClasses != null)
        {
            if (ViewBag.BEClasses.Count > 1)
            {
                <div class="form-group">
                    @Html.Label(FieldTranslation.GetLabel("Benefit Class", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        @*<select id="BenefitClass" name="BenefitClass" class="form-control @((ViewBag.IsBEClassRequired == true) ? "field-required" : string.Empty)">*@
                        @*Above line commented on 18OCT2022*@
                        <select id="BenefitClass" name="BenefitClass" class="form-control field-required">
                            @{
                                foreach (var item in ViewBag.BEClasses)
                                {
                                    {
                                        <option value="@item.Code">@item.Description</option>
                                    }
                                }
                            }
                        </select>

                        @Html.ValidationMessage("BenefitClass", new { @class = "text-danger" })
                    </div>
                </div>
            }
        }
    }
    @*End*@


    @if (GlobalVariables.IsReHire)
    {
        <div class="form-group">
            @Html.Label(FieldTranslation.GetLabel("Onboarding Type", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <input type="text" class="form-control" id="txtobtype" value="ReHire" name="txtobtype" disabled="disabled">
            </div>
        </div>
        <div class="form-group">
            @Html.Label(FieldTranslation.GetLabel("ReHire Date", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <input type="text" class="form-control" id="txtRhDate" name="txtRhDate" value="@GlobalVariables.ReHireDate.ToString().Split(' ')[0]" disabled="disabled">
            </div>
        </div>
        <div class="form-group">
            @Html.Label(FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <input type="text" class="form-control" id="txtRhempid" name="txtRhempid" value="@GlobalVariables.ReHireEmployeeID" disabled="disabled">
            </div>
        </div>
    }
    <div class=" pull-right">
        <button type="submit" value="Submit" class="btn btn-lg btn-success submitButtonText">Continue&nbsp;&nbsp;<i class="fa fa-arrow-right fa-fw"></i></button>
    </div>
</div>
}
@section scripts{
    <script>
        $("#obprocessmonitorcreate").validate();
    </script>
}