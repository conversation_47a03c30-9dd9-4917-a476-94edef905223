@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.ViewModels
@model SelectedSecurityQuestionsVM
@{
    ViewBag.Title = "SelectSecurityQuestions";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
@if (GlobalVariables.WelcomeBackground != null)
{
    <style>
        .login-box {
            box-shadow: 10px 10px 5px rgba(0, 0, 0, 0.5);
        }
    </style>
}
<div class="home-background  welcome-background" style="background: URL('@(GlobalVariables.WelcomeBackground != null ? GlobalVariables.WelcomeBackground : "~")') no-repeat center center fixed; -webkit-background-size: cover; -moz-background-size: cover; -o-background-size: cover; background-size: cover; background-color: #efefef">
    <div class="container">
        <div style="margin-top: 50px;" class="mainbox col-md-9 col-centered login-box">
            <div class="panel">
                <div style="padding-top: 30px" class="panel-body">
                    <div class="col-md-12 text-center" style="padding-bottom: 25px;">
                        <a href="@Url.Action("Index", "Home")"><img src="@ViewBag.LogoURL" class="img-responsive" /></a>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="text-center" style="font-size: 1.7rem; text-decoration: underline; padding-bottom: 12px;">
                                Select Security Questions
                            </div>
                            <div class="text-center">
                                <p>
                                    Your profile is missing security questions in the case you have forgotten your password.
                                </p>
                                <p>
                                    Please select from the questions below and submit your answers.
                                </p>
                            </div>
                        </div>
                    </div>
                    @using (Html.BeginForm())
                    {
                        @Html.HiddenFor(x => x.UserID)
                        <div class="sec-question row">
                            <div class="form-group">
                                <div class="">
                                    <div class="col-sm-2">Question #1:</div>
                                    <div class="col-sm-10">
                                        @(Html.Kendo().DropDownList()
                                              .Name("Question1")
                                              .BindTo(Model.SecurityQuestions)
                                              .DataTextField("Question")
                                              .DataValueField("QuestionID")
                                              .HtmlAttributes(new { style = "width: 100%;" })
                                              .OptionLabel("Select Question #1:")
                                              .Events(e => e.Change("Question1Change"))
                                              .Value(Model.SecurityAnswer1.QuestionID.ToString())
                                        )
                                        @Html.HiddenFor(x => x.SecurityAnswer1.QuestionID)
                                    </div>
                                </div>
                            </div>
                            <div class="form-group sec-question-answer">
                                <div class="col-sm-2">Answer 1:</div>
                                <div class="col-sm-10">
                                    @Html.TextBoxFor(x => x.SecurityAnswer1.AnswerText, new { id = "AnswerText1", @class = "form-control", @disabled = "disabled", required = "required" })
                                    @Html.ValidationMessageFor(x => x.SecurityAnswer1.AnswerText)
                                </div>
                            </div>
                        </div>
                        <div class="sec-question row">
                            <div class="form-group">
                                <div class="">
                                    <div class="col-sm-2">Question #2:</div>
                                    <div class="col-sm-10">
                                        @(Html.Kendo().DropDownList()
                                              .Name("Question2")
                            //.BindTo(Model.SecurityQuestions)
                                              .DataTextField("Question")
                                              .DataValueField("QuestionID")
                                              .HtmlAttributes(new { style = "width: 100%;" })
                                              .OptionLabel("Select Question #2:")
                                              .Events(e => e.Change("Question2Change"))
                                              .Enable(false)
                                        )
                                        @Html.HiddenFor(x => x.SecurityAnswer2.QuestionID)
                                    </div>
                                </div>

                            </div>
                            <div class="form-group sec-question-answer">
                                <div class=" col-sm-2">Answer 2:</div>
                                <div class="col-sm-10">
                                    @Html.TextBoxFor(x => x.SecurityAnswer2.AnswerText, new { id = "AnswerText2", @class = "form-control", @disabled = "disabled", required = "required" })
                                    @Html.ValidationMessageFor(x => x.SecurityAnswer2.AnswerText)
                                </div>
                            </div>
                        </div>
                        <div class="sec-question row">
                            <div class="form-group">
                                <div class="">
                                    <div class="col-sm-2">Question #3:</div>
                                    <div class="col-sm-10">
                                        @(Html.Kendo().DropDownList()
                                              .Name("Question3")
                            //.BindTo(Model.SecurityQuestions)
                                              .DataTextField("Question")
                                              .DataValueField("QuestionID")
                                              .HtmlAttributes(new { style = "width: 100%;" })
                                              .OptionLabel("Select Question #3:")
                                              .Events(e => e.Change("Question3Change"))
                                              .Enable(false)
                                        )
                                        @Html.HiddenFor(x => x.SecurityAnswer3.QuestionID)
                                    </div>
                                </div>
                            </div>
                            <div class="form-group sec-question-answer">
                                <div class="col-sm-2">Answer 3:</div>
                                <div class="col-sm-10">
                                    @Html.TextBoxFor(x => x.SecurityAnswer3.AnswerText, new { id = "AnswerText3", @class = "form-control", @disabled = "disabled", required = "required" })
                                    @Html.ValidationMessageFor(x => x.SecurityAnswer3.AnswerText)
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <button id="btnClear" type="button" class="btn btn-primary btn-lg" onclick="ClearData()">Reset Questions</button>
                            </div>
                            <div class="col-sm-6 ">
                                <div class="pull-right create-pad">
                                    <button id="btnSubmit" type="submit" class="btn btn-primary btn-lg" disabled="disabled" onclick="SaveData()">Save</button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>







<script>
    if (self !== top) top.location.replace(self.location.href);

    function Question1Change() {
        var question1 = $("#Question1").data("kendoDropDownList");
        var answer1 = question1.value();

        var securityQuestions = @Html.Raw(Json.Encode(Model.SecurityQuestions));
        var filteredQuestions = $.grep(securityQuestions, function(n,i) { return parseInt(n.QuestionID) !== parseInt(answer1) });

        var question2 = $("#Question2").data("kendoDropDownList");
        question2.dataSource.data(filteredQuestions);
        question2.refresh();

        var answer = $("#AnswerText1");
        if (answer1) {
            answer.removeAttr("disabled");
            question1.enable(false);
            question2.enable(true);
            answer.focus();
        }
        else {
            answer.prop("disabled", true);
            question2.enable(false);
        }
    }

    function Question2Change() {
        var question1 = $("#Question1").data("kendoDropDownList");
        var answer1 = question1.value();
        var question2 = $("#Question2").data("kendoDropDownList");
        var answer2 = question2.value();

        var securityQuestions = @Html.Raw(Json.Encode(Model.SecurityQuestions));
        var filteredQuestions = $.grep(securityQuestions, function(n,i) { return parseInt(n.QuestionID) !== parseInt(answer1) && parseInt(n.QuestionID) !== parseInt(answer2) });

        var question3 = $("#Question3").data("kendoDropDownList");
        question3.dataSource.data(filteredQuestions);
        question3.refresh();

        var answer = $("#AnswerText2");
        if (answer1) {
            answer.removeAttr("disabled");
            question2.enable(false);
            question3.enable(true);
            answer.focus();
        }
        else {
            answer.prop("disabled", true);
            question3.enable(false);
        }
    }

    function Question3Change() {
        var question3 = $("#Question3").data("kendoDropDownList");
        var answer = $("#AnswerText3");
        var button = $("#btnSubmit");
        if ($("#Question3").val()) {
            question3.enable(false);
            answer.removeAttr("disabled");
            button.removeAttr("disabled");
        } else {
            answer.prop("disabled", true);
            button.prop("disabled", true);
        }
    }

    function SaveData() {
        $("#SecurityAnswer1_QuestionID").val($("#Question1").val());
        $("#SecurityAnswer2_QuestionID").val($("#Question2").val());
        $("#SecurityAnswer3_QuestionID").val($("#Question3").val());
    }

    function ClearData() {
        var question1 = $("#Question1").data("kendoDropDownList");
        question1.enable(true);
        question1.value('');

        var answer1 = $("#AnswerText1");
        answer1.val('');
        answer1.prop("disabled", true);

        var question2 = $("#Question2").data("kendoDropDownList");
        question2.enable(false);
        question2.value('');

        var answer2 = $("#AnswerText2");
        answer2.val('');
        answer2.prop("disabled", true);

        var question3 = $("#Question3").data("kendoDropDownList");
        question3.enable(false);
        question3.value('');

        var answer3 = $("#AnswerText3");
        answer3.val('');
        answer3.prop("disabled", true);
    }

</script>


