<script type="text/x-template" id="payroll-to-web-template">
    <div class="panel panel-default">
        <div class="panel-heading">{{viewData.componentTitle}}</div>
        <div class="panel-body">
            <p><input type="radio" value="OnCompletion" v-model="viewData.payrollAvailableOption" /> On Completion</p>
            <p><input type="radio" value="OnCheckDate" v-model="viewData.payrollAvailableOption" /> On Check Date</p>
            Time of Day
            <div class="row">
                <div class="col-md-6 col-sm-12">
                    <div class="input-group my-group" style="width: 100%">
                        <select class="form-control" name="HourInput" v-model="viewData.hourInput">
                            <option value="12:00">12:00</option>
                            <option value="01:00">01:00</option>
                            <option value="02:00">02:00</option>
                            <option value="03:00">03:00</option>
                            <option value="04:00">04:00</option>
                            <option value="05:00">05:00</option>
                            <option value="06:00">06:00</option>
                            <option value="07:00">07:00</option>
                            <option value="08:00">08:00</option>
                            <option value="09:00">09:00</option>
                            <option value="10:00">10:00</option>
                            <option value="11:00">11:00</option>
                        </select>
                        <select id="AMPM" class="selectpicker form-control" data-live-search="true" v-model="viewData.availableTimeAMPM"  name="AMPM">
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                    </div>
                </div>
            </div>
            <p><input type="radio" value="OnNumberOfDaysAfterCompletion" v-model="viewData.payrollAvailableOption" /> On Number of Days After Completion</p>
            <div class="payroll-options-alignment">
                Number of Days
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                       <input type="text" class="form-control" v-mode="viewData.payrollAvailableDays" />
                    </div>
                </div>
                Time of Day
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="input-group my-group" style="width: 100%">
                            <select class="form-control" name="HourInput" v-model="viewData.hourInput">
                                <option value="12:00" >12:00</option>
                                <option value="01:00" >01:00</option>
                                <option value="02:00" >02:00</option>
                                <option value="03:00" >03:00</option>
                                <option value="04:00" >04:00</option>
                                <option value="05:00" >05:00</option>
                                <option value="06:00" >06:00</option>
                                <option value="07:00" >07:00</option>
                                <option value="08:00" >08:00</option>
                                <option value="09:00" >09:00</option>
                                <option value="10:00" >10:00</option>
                                <option value="11:00" >11:00</option>
                            </select>
                            <select id="AMPM" class="selectpicker form-control" data-live-search="true" name="AMPM">
                                <option value="AM">AM</option>
                                <option value="PM">PM</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript" id="payroll-to-web-script">
    var PayrollTowebComponent = VueComponent('payroll-to-web', {
        props: {
            viewData: {
                type: Object,
                default: () => ({})
            }
        },
        data: function () {
            return{

            }
        },
        mounted: function () {
            
        },
        methods: {
            
        }
    });

</script>