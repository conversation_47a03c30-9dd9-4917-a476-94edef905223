<form action="@Url.Action("CreateFolders", "API")" method="POST">
    CompanyID :<input type="text" id="co" name="co"/><br/>
    Customer # :<input type="text" id="cn" name="cn"/><br/>
    Client ID :<input type="text" id="cl" name="cl" /><br />
    <button type="submit" value="submit"></button>
</form>

<form action="@Url.Action("CreateFolders", "API")" method="POST">
    CompanyID :<input type="text" id="co" name="co"/><br/>
    Customer # :<input type="text" id="cn" name="cn"/><br/>
    <button type="submit" value="submit"></button>
</form>

<form action="@Url.Action("SendInvoicePrev", "API")" method="POST">
    CompanyID :<input type="text" id="co" name="co"/><br/>
    Invoice # :<input type="text" id="inv" name="inv"/><br/>
    Client # :<input type="text" id="cl" name="cl"/><br/>
    <button type="submit" value="submit"></button>
</form>
<form action="@Url.Action("SendChecks", "API")" method="POST">

    <button type="submit" value="submit">Send Checks</button>
</form>

<form action="@Url.Action("dec", "API")" method="POST">
    <input type="text" id="fld" name="fld"/>
    <button type="submit" value="submit">dec</button>
</form>