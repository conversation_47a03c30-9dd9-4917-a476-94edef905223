@model IEnumerable<DarwiNet2._0.Data.OBImport>
@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using PayPeriods = DataDrivenViewEngine.Models.Core.enPayPeriods
@{
    ViewBag.Title = "Previous Imports" + " - " + GlobalVariables.ProfileName;
    int mynewvalue = ViewBag.ProfileID;
    ViewBag.Page = "OBImportPrevious";
}
@Html.Partial("~/Views/Navigation/_PartialTabs.cshtml", mynewvalue)
<div class="col-md-12">
    <div class="form-group">

        <div class="row">
            <div class="col-md-3 pull-left">
                <div class="input-group">
                    <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                    <input type="text" class="form-control" id='FieldFilter' placeholder="Search User ID">
                </div>
            </div>
            <div class="pull-right create-pad">
                @Html.ActionLink("Back To Setup", "Index", "OBClientSetupTranslation", new {id = ViewBag.SetupID, section = 1}, new {@class = "btn-t btn-thinkware"})
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <table class="table" id="previousImports">
            <tr>
                <th title="@FieldTranslation.GetLabel("ImportID", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("ImportID", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("ClientID", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("ClientID", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("UserID", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("UserID", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("StartTime", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("StartTime", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Source", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Source", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Completed", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Completed", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("WorkState", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("WorkState", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("WarningDate", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("WarningDate", GlobalVariables.LanguageID)
                </th>
                @*<th>
                    @Html.DisplayNameFor(model => model.Profile)
                </th>*@
                <th title="@FieldTranslation.GetLabel("PayPeriod", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("PayPeriod", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("DueDate", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("DueDate", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Actions", GlobalVariables.LanguageID)" class="hidden-filter">
                    <div class="header-center">
                        Actions
                    </div>
                </th>
            </tr>

            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.ImportID)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.ClientID)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.UserID)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.StartTime)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Source)
                    </td>
                    <td>
                        @(item.Completed ? Html.CheckBoxFor(model => item.Completed, new { @disabled = "disabled", @checked = "checked" }) : Html.CheckBoxFor(model => item.Completed, new { @disabled = "disabled" }))
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.WorkState)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.WarningDate)
                    </td>
                    @*<td>
                        @Html.DisplayFor(modelItem => item.Profile)
                    </td>*@
                    <td>
                        @FieldTranslation.GetLabel(@FieldTranslation.GetEnumDescription(typeof(PayPeriods), Convert.ToInt16(item.PayPeriod)), @GlobalVariables.LanguageID)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.DueDate)
                    </td>
                    <td>
                        <div class="icon-center">
                            <a href="@Url.Action("ImportDetails", "OBImport", new { id = item.ImportID })" title="Details"><i class="icon-blue fa fa-eye fa-lg fa-fw"></i></a>
                            <a href="#" id="delete" data-id="@item.ImportID" data-profile="@item.Profile" title="Delete"><i class="icon-red fa fa-times fa-lg fa-fw"></i></a>
                        </div>
                    </td>
                </tr>
            }

        </table>
    </div>
</div>
    
<script src="~/Scripts/bootbox.min.js"></script>
<script>
    $(document).ready(function() {
        var grid = $("#previousImports").kendoGrid({
            dataSource: {
                pageSize: 15
            },
            sortable: true,
            pageable: true,
            filterable: true,
            groupable: true,
            scrollable: false,
            resizable: true
        }).data("kendoGrid");

    });
</script>
<script>
    $(document).on("click", "td #delete", function (e) {
        var deletedID = $(this).attr('data-id');
        var profileID = $(this).attr('data-profile');
        var url = "@Url.Action("Delete", "OBImport", null)";
        bootbox.dialog({
            message: "Are you sure you want to delete: " + "<strong>" + deletedID + "</strong>",
            title: "Delete Profile",
            buttons: {
                main: {
                    label: "Cancel",
                    className: "btn-primary",
                    callback: function () {
                        //Example.show("Primary button");
                    }
                },
                danger: {
                    label: "Delete",
                    className: "btn-danger",
                    callback: function () {
                        window.location.href = url + "/?id=" + deletedID + "&profileId=" + profileID ;
                    }
                }
            }
        });
    });
</script>
<script>
    $(document).ready(function () {
        $("#FieldFilter").keyup(function () {

            var value = $("#FieldFilter").val();
            var grid = $("#previousImports").data("kendoGrid");

            if (value) {
                grid.dataSource.filter({
                    logic: "or",
                    filters: [
                        { field: "UserID", operator: "contains", value: value }
                    ]
                })
            } else {
                grid.dataSource.filter({});
            }
        });
    });
</script>