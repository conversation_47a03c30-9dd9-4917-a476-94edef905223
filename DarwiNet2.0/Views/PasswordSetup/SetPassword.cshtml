@using DarwiNet2._0.DNetSynch;

@model  DarwiNet2._0.ViewModels.PasswordSetup.SetPasswordVM

@{
    ViewBag.Title = "Set Password";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<style>
    .progress {
        height: 20px;
        margin-bottom: 3px;
        overflow: hidden;
        background-color: #f5f5f5;
        border-radius: 4px;
        -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
        box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
    }
</style>

@if (GlobalVariables.WelcomeBackground != null)
{
    <style>
        .login-box {
            box-shadow: 10px 10px 5px rgba(0, 0, 0, 0.5);
        }
    </style>
}

<div class="home-background welcome-background" style="background: URL('@(GlobalVariables.WelcomeBackground != null ? GlobalVariables.WelcomeBackground : "~")') no-repeat center center fixed; -webkit-background-size: cover; -moz-background-size: cover; -o-background-size: cover; background-size: cover; background-color: #efefef">
    <div class="container">
        <div style="margin-top: 50px;" class="col-md-9 col-centered login-box">
            <div class="panel">
                <div style="padding-top: 30px;" class="panel-body">

                    @*Logo*@
                    <div class="col-md-12 text-center" style="padding-bottom: 25px;">
                        <a href="@Url.Action("Index", "Home")"><img src="@Model.LogoURL" class="img-responsive" /></a>
                    </div>

                    @*Error Message*@
                    @if (Model.ErrorMessage != "" && Model.ErrorMessage != null)
                    {
                        <div class="alert alert-danger alert-dismissible col-md-12" role="alert">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <p>@Model.ErrorMessage</p>
                        </div>
                    }
                    <div class="row">
                        <div class="col-md-6">
                            @using (Html.BeginForm("SetPassword", "PasswordSetup", new { @class = "form-horizontal", role = "form", method = "post", @autocomplete = "off" }))
                            {
                                <div class="form-group">
                                    @Html.ValidationMessageFor(model => model.Password)
                                    @Html.PasswordFor(model => model.Password, new { @class = "form-control", @placeholder = "Password", @maxlength = "15" })
                                </div>
                                <div class="form-group">
                                    @Html.ValidationMessageFor(model => model.PasswordReenter)
                                    @Html.PasswordFor(model => model.PasswordReenter, new { @class = "form-control", @placeholder = "Re-Enter Password", @maxlength = "15" })
                                </div>

                                @*Button(s)*@
                                <div style="margin-top: 10px" class="form-group">
                                    <div class="col-md-12 controls">
                                        <div class="pull-right">
                                            <button type="submit" class="btn btn-lg btn-success btn-responsive">Set Password</button>
                                        </div>
                                    </div>
                                </div>

                                @*Repopulate ViewModel*@
                                @Html.HiddenFor(model => model.LogoURL)
                                @Html.HiddenFor(model => model.ErrorMessage)
                                @Html.HiddenFor(model => model.EmployeeID)
                                @Html.HiddenFor(model => model.UserID)
                            }
                        </div>

                        @*Side Text*@
                        <div class="col-md-6">
                            <div>
                                <p><strong>Enter your desired password.</strong></p>
                                <div style="margin: 10px 0;">
                                    * Username cannot contain your SSN<br />
                                    * Password cannot match Username<br />
                                    * Password must be between 8-15 characters long<br />
                                    * Password must contain at least 1 Uppercase letter<br />
                                    * Password must contain at least 1 Lowercase letter<br />
                                    * Password must contain at least 1 Number<br />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/Scripts/pwstrength-bootstrap.min.js"></script>

<script type="text/javascript">
    $(':password').pwstrength();
</script>
