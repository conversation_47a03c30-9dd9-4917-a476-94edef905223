@model DarwiNet2._0.Data.EmployeeTraining
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;
@using DataDrivenViewEngine.Models.Core;
@{
    Layout = null;
    ViewBag.Title = "Add Training";
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{

    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>

                <div class="colored-line-left"></div>
            </div>
        </div>
    </div>
    using (Html.BeginForm("AddTraining", "Employees", FormMethod.Post, new { id = "trainingadd" }))
    {
        @Html.AntiForgeryToken()

        <div class="form-horizontal">


            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.Partial("~/Views/Employees/_employeeError.cshtml")
            <div class="form-group">
                <input type="hidden" name="CompanyID" value="@ViewBag.CompanyID" />
                <input type="hidden" name="ClientID" value="@ViewBag.Client" />
                <input type="hidden" id="EmployeeID" name="EmployeeID" value="@ViewBag.EmployeeID" />
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.TrainingType, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("TrainingType", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" name="TrainingTypes" id="TrainingTypes">
                        @foreach (Code_Description item in ViewBag.TrainingTypes)
                        {
                            <option value="@item.Code">@item.Description</option>
                        }
                    </select>
                </div>
                @Html.LabelFor(model => model.TrainingStatus, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("TrainingStatus", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" name="TrainingStatus" id="TrainingStatus">
                        @foreach (Code_Description item in ViewBag.TrainingStatuses)
                        {
                            <option value="@item.Code">@item.Description</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                @*@Html.LabelFor(model => model.Description, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
                    </div>*@
                @Html.LabelFor(model => model.Rating, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Rating", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" name="TrainingRatings" id="TrainingRatings">
                        @foreach (Code_Description item in ViewBag.TrainingRatings)
                        {
                            <option value="@item.Code">@item.Description</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.CompletionDate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("CompletionDate", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.EditorFor(model => model.CompletionDate, new { htmlAttributes = new { @class = "form-control" } })
                    <div class="error-msg"></div>
                    @Html.ValidationMessageFor(model => model.CompletionDate, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.RenewalDate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("RenewalDate", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.EditorFor(model => model.RenewalDate, new { htmlAttributes = new { @class = "form-control" } })
                    <div class="error-msg1"></div>
                    @Html.ValidationMessageFor(model => model.RenewalDate, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-2">
                    @FieldTranslation.GetLabel("Comments", GlobalVariables.LanguageID)
                </label>
                <div class="col-md-4">
                    <textarea id="comments" name="comments" class="form-control" rows="5"></textarea>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-offset-2 col-md-10">
                    <div class="pull-right">
                        <button type="button" class="backPayCodes btn btn-thinkware">@FieldTranslation.GetLabel("Cancel", GlobalVariables.LanguageID)</button> <input type="submit" value="@FieldTranslation.GetLabel("Save", GlobalVariables.LanguageID)" class="btn btn-thinkware" />
                    </div>
                </div>
            </div>
        </div>
    }
    <script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
    <script>
        $(document).ready(
            function() {

                $('#CompletionDate, #RenewalDate').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
            });

    </script>
    <script>
        $("#trainingadd").validate({
        rules: {
            /*CompletionDate: {
                required: true,
                date: true
            },
            RenewalDate: {
                required: true,
                date: true
            }*/
        },
        errorPlacement: function(error, element) {
            if (element.attr("name") == "CompletionDate")
                error.insertAfter(".error-msg");
            else if (element.attr("name") == "RenewalDate")
                error.insertAfter(".error-msg1");
            else
                error.insertAfter(element);
        }
    });
    </script>
    <script>
        
        $("#trainingadd").on('submit', function (ev) {
            var frm = $('#trainingadd');
            ev.preventDefault();

            $.ajax({
                type: "POST",
                url: '@Url.Action("AddTraining", "Employees")',
                dataType: "html",
                data: frm.serialize(),
                success: function (data) {
                    if (data.indexOf('There was an error') >= 0) {
                        $('#error').show();
                        $('#errResponse').text(data);
                    } else {
                        //$('#return').append(data);
                        var div = $('#partialDiv');
                        var url2 = '@Url.Action("EmployeeTraining", "Employees")';
                        url2 = url2 + "/?e=@ViewBag.EmployeeID&client=@ViewBag.Client&co=@ViewBag.CompanyID";
                        $.get(url2, function (data) {
                            div.html(data);
                            $('#partialDiv').delay(1000).show(0);
                            $('#listDiv').hide();
                            $('#loading').delay(1000).hide(0);


                        });
                    }
                }
            });
        });
    </script>

}
@if (ViewBag.Access == MenuAccessLevel.ReadOnly)
{
    <script>
        $(document).ready(function () {
            $('#trainingadd input').attr('disabled', true);
            $('#trainingadd select').attr('disabled', true);
        })
    </script>
}
@if (GlobalVariables.DNETLevel == DNetAccessLevel.Employee)
{
    <script>
        $(document).ready(function () {
            $('input.ee-disabled').attr('disabled', true);
            $('select.ee-disabled').attr('disabled', true);
        })
    </script>
}