@model DarwiNet2._0.Data.EETotalComp
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Data;
@using DataDrivenViewEngine.Models.Core;

@{
    Layout = null;
    ViewBag.Title = "Total Compensation";

}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else if ((bool)ViewBag.NoHistory)
{
    <div class="alert alert-danger alert-dismissible" role="alert" id="globalFail">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <p>No compensation history has been found to display.</p>
    </div>
}
else
{
    <script>
        $(function() {
            // bind change event to select
            $('#sel_year').on('change', function(evt) {
                var url = "@Url.Action("EETotalComp", "Employees")";
                url = url + "?e=@ViewBag.EmployeeID";
                url = url + "&y=" + $(this).val();
                url = url + "&client=@ViewBag.Client";
                url = url + "&co=@ViewBag.CompanyID";
                evt.preventDefault();
                evt.stopPropagation();
                var div = $('#partialDiv');
                $.get(url, function(data) {
                    div.html(data);
                    $('#partialDiv').delay(1000).show(0);

                });
            });
        });
    </script>
    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>

                <div class="colored-line-left"></div>
            </div>
            <div class="col-md-6 col-sm-6">
                <div class="text-right">
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <select id="sel_year" name="sel_year" class="form-control">
                @foreach (var y in ViewBag.Years)
                {
                    <option value="@y" @((ViewBag.SelectedYear.ToString() == y.ToString()) ? "selected" : string.Empty)>@y</option>
                }
            </select>
        </div>
    </div>
    <div class="row" style="padding-top: 15px;">
        <div class="col-md-6">
            <div id="chart"></div>
            <script>
    function createChart() {
        $("#chart").kendoChart({
            title: {
                position: "bottom",
                text: "Total Compensation"
            },
            legend: {
                visible: false
            },
            chartArea: {
                background: ""
            },
            seriesDefaults: {
                type: "donut",
                startAngle: 150
            },
            series: [
                {
                    name: "2011",
                    data: [
                        {
                            category: "Total Wages",
                            value: @Model.totalAnnualWages,
                            color: "#59ABE3"
                        }, {
                            category: "Total Benefits",
                            value: @Model.totalBenefits,
                            color: "#D91E18"
                        }, {
                            category: "Total Taxes",
                            value: @Model.totalTaxes,
                            color: "#87D37C"
                        },{
                            category: "Total Reimbursements",
                            value: @Model.totalReim,
                            color: "#F4D03F"
                        },
                        {
                            category: "Total PTO",
                            value: @Model.totalPTO,
                            color: "#eb8d2f"
                        }
                        , {
                            category: "Total Holiday",
                            value: @Model.totalHoliday,
                            color: "#9B59B6"
                        }
                    ]
                }
            ],
            tooltip: {
                visible: true,
                template: "#= category # : #= kendo.toString(value,'c2') #"
            }
        });
    }


            </script>

        </div>
        <div class="col-md-6">
            <div>
                <div id="line"></div>
                <script>
                    var lineData = [];
                </script>
                @foreach (EETotalComp thisComp in ViewBag.theComps)
                {
                    <script>
                        var data = { "year": @thisComp.year, "value": @thisComp.totalCompensation };
                        lineData.push(data);
                    </script>
                }
                <script>
                    function createChart2() {
                        $("#line").kendoChart({
                            dataSource: {
                                data: lineData
                            },
                            title: {
                                text: "Total Comp Trend since Hire"
                            },
                            legend: {
                                visible: false
                            },
                            seriesDefaults: {
                                type: "line",
                                labels: {
                                    visible: true,
                                    background: "transparent",
                                    format: "{0:c}"
                                }
                            },
                            series: [
                                {
                                    field: "value",
                                    name: "Total Comp"
                                }
                            ],
                            valueAxis: {
                                labels: {
                                    format: "{0:c}"
                                },
                                line: {
                                    visible: true
                                }
                            },
                            categoryAxis: {
                                field: "year",
                                majorGridLines: {
                                    visible: true
                                }
                            }
                        });
                    }


                </script>

            </div>
        </div>
    </div>
    <div class="row" style="padding-top: 15px;">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><strong>Total Compensation Breakdown</strong></h3>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped">
                        <tr>
                            <td>
                                Annual Wages
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalAnnualWages)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Reimbursements
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalReim)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Company Paid Benefits
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalBenefits)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Company Paid Taxes
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalTaxes)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Company Paid PTO
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalPTO)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                @FieldTranslation.GetLabel("Company Paid Holiday", GlobalVariables.LanguageID)
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalHoliday)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Total Compensation
                            </td>
                            <td>
                                @string.Format("{0:C}", @Model.totalCompensation)
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><strong>Other Employer Paid Items</strong></h3>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    FICA SS
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalFicaSS)
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    FICA Med
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalFicaM)
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Unemployment
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalUnEmployment)
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Workers Compensation
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalWc)
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><strong>Annual Pay Breakdown</strong></h3>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered">
                        <tr>
                            <th>Pay Code</th>
                            <th>Description</th>
                            <th>YTD Amount</th>
                        </tr>
                        @foreach (EmployeeCodes item in Model.allPay)
                        {
                            <tr>
                                <td>
                                    @item.Code
                                </td>
                                <td>
                                    @item.Description
                                </td>
                                <td>
                                    @String.Format("{0:c}", item.Additional)
                                </td>
                            </tr>
                        }
                    </table>
                </div>
            </div>

        </div>
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><strong>Benefits Breakdown</strong></h3>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Benefit Code</th>
                                <th>Description</th>
                                <th>YTD Amount</th>
                            </tr>
                        </thead>

                        @foreach (EmployeeCodes item in Model.allBenefits)
                        {
                            <tr>
                                <td>
                                    @item.Code
                                </td>
                                <td>
                                    @item.Description
                                </td>
                                <td>
                                    @String.Format("{0:c}", item.Additional)
                                </td>
                            </tr>
                        }
                    </table>
                </div>
            </div>

        </div>
    </div>

    <script>
        $(window).resize(function () {
            var chart = $("#chart").data("kendoChart");
            var line = $("#line").data("kendoChart");
            chart.redraw();
            line.redraw();
        });
        $(document).ready(createChart);
        $(document).bind("kendo:skinChange", createChart);
        $(document).ready(createChart2);
        $(document).bind("kendo:skinChange", createChart2);
        $(document).ready(function() {
            setTimeout(function() {
                var chart = $("#chart").data("kendoChart");
                var line = $("#line").data("kendoChart");
                chart.redraw();
                line.redraw();
            }, 1100);
        });
    </script>
}