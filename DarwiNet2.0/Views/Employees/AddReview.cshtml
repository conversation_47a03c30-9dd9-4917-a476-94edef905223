@model DarwiNet2._0.Data.EmployeeReview
@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DataDrivenViewEngine.Models.Core;
@{
    ViewBag.Title = "Add Review";
    Layout = null;
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{

    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                <div class="colored-line-left"></div>
            </div>
        </div>
    </div>
    using (Html.BeginForm("AddReview", "Employees", FormMethod.Post, new { id = "reviewadd" }))
    {
        @Html.AntiForgeryToken()

        <div class="form-horizontal">
            @Html.HiddenFor(model => model.CompanyID, new { htmlAttributes = new { @class = "form-control" } })
            <input type="hidden" name="clientID" value="@ViewBag.Client" />
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <input type="hidden" id="EmployeeID" name="EmployeeID" value="@ViewBag.EmployeeID" />
            @Html.HiddenFor(model => model.SequenceNumber, new { htmlAttributes = new { @class = "form-control" } })
            @Html.Partial("~/Views/Employees/_employeeError.cshtml")
            <div class="form-group">
                @Html.LabelFor(model => model.Date, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Date", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "form-control" } })
                    <div class="error-msg"></div>
                    @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Time, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Time", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <div class="input-group my-group" style="width: 100%">
                        <input type="text" class="form-control" name="TimeInput" id="TimeInput" ><select id="TimeAMPM" class="selectpicker form-control" data-live-search="true">
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                        <input type="hidden" value="" name="Time" id="Time" />
                    </div>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.HRManager, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Manager", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" name="HRManager" id="HRManager">
                        @foreach (Code_Description item in ViewBag.Managers)
                        {
                            <option value="@item.Code" @((item.Code == Model.HRManager) ? "selected" : string.Empty)>@item.Description</option>
                        }
                    </select>
                </div>
                @Html.LabelFor(model => model.ReviewPerformanceType, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("ReviewType", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" name="ReviewPerformanceType" id="ReviewPerformanceType">
                        @foreach (Code_Description item in ViewBag.ReviewTypes)
                        {
                            <option value="@item.Code" @((item.Code == Model.ReviewPerformanceType) ? "selected" : string.Empty)>@item.Description</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.ReviewPerformanceAction, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("ReviewAction", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" name="ReviewPerformanceAction" id="ReviewPerformanceAction">
                        @foreach (Code_Description item in ViewBag.ReviewActions)
                        {
                            <option value="@item.Code" @((item.Code == Model.ReviewPerformanceAction) ? "selected" : string.Empty)>@item.Description</option>
                        }
                    </select>
                </div>
                @Html.LabelFor(model => model.EmployeeRating, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Rating", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" name="EmployeeRating" id="EmployeeRating">
                        @foreach (Code_Description item in ViewBag.ReviewRatings)
                        {
                            <option value="@item.Code" @((item.Code == Model.EmployeeRating) ? "selected" : string.Empty)>@item.Description</option>
                        }
                    </select>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Comments, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Comments", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Comments, new { @class = "form-control", rows = "5", style = "resize:none" })
                    @Html.ValidationMessageFor(model => model.Comments, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.NextReviewDate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("NextReviewDate", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.EditorFor(model => model.NextReviewDate, new { htmlAttributes = new { @class = "form-control" } })
                    <div class="error-msg1"></div>
                    @Html.ValidationMessageFor(model => model.NextReviewDate, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-12">
                    <div class="pull-right">
                        <button type="button" class="backPayCodes btn btn-thinkware">@FieldTranslation.GetLabel("Cancel", GlobalVariables.LanguageID)</button> <input type="submit" value="@FieldTranslation.GetLabel("Save", GlobalVariables.LanguageID)" class="btn btn-thinkware" />
                    </div>
                </div>
            </div>
        </div>
    }
    <script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
    <script src="~/Scripts/MaskedInput.js"></script>
    <script>
        $(document).ready(
            function() {

                $('#Date, #NextReviewDate').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
            });
        $(document).ready(function() {
            $('#TimeInput').mask('99:99', { placeholder: "__:__" });
        });
        $('#TimeInput').keyup(function () {
            var startTime = $('#TimeInput').val();
            var startTimeTrim = startTime.split(':')[0];
            var startMinTrim = startTime.split(':')[1];
            if (startTimeTrim >= 13 || startMinTrim >= 60) {
                bootbox.dialog({
                    message: "Time must be in the correct 12 hour format",
                    title: "Status",
                    buttons: {
                        main: {
                            label: "Ok",
                            className: "btn-primary",
                            callback: function () {
                                //Example.show("Primary button");
                            }
                        }
                    }
                });
                $('#TimeInput').val('');
            }
        });
    </script>
    <script>
    $("#reviewadd").validate({
        rules: {
            Date: {
                required: true,
                date: true
            /*},
            NextReviewDate: {
                required: true,
                date: true*/
            }
        },
        errorPlacement: function (error, element) {
            if (element.attr("name") == "Date")
                error.insertAfter(".error-msg");
            else if (element.attr("name") == "NextReviewDate")
                error.insertAfter(".error-msg1");
            else
                error.insertAfter(element);
        }
    });
    </script>
    <script>
        $("#reviewadd").on('submit', function (ev) {
            var frm = $('#reviewadd');
            ev.preventDefault();
            var time = $('#TimeInput').val();
            var timeAMPM = $('#TimeAMPM').val();
            $('#Time').val(time + " " + timeAMPM);
            $.ajax({
                type: "POST",
                url: '@Url.Action("AddReview", "Employees")',
                dataType: "html",
                data: frm.serialize(),
                success: function (data) {
                    if (data.indexOf('There was an error') >= 0) {
                        $('#error').show();
                        $('#errResponse').text(data);
                    } else {
                        //$('#return').append(data);
                        var div = $('#partialDiv');
                        var url2 = '@Url.Action("EmployeeReviews", "Employees")';
                        url2 = url2 + "/?e=@ViewBag.EmployeeID&client=@ViewBag.Client&co=@Model.CompanyID";
                        $.get(url2, function (data) {
                            div.html(data);
                            $('#partialDiv').delay(1000).show(0);
                            $('#listDiv').hide();
                            $('#loading').delay(1000).hide(0);
                        });
                    }
                }
            });
        });
    </script>


}
@if (ViewBag.Access == MenuAccessLevel.ReadOnly)
{
    <script>
        $(document).ready(function () {
            $('#reviewadd input').attr('disabled', true);
            $('#reviewadd select').attr('disabled', true);
        })
    </script>
}
@if (GlobalVariables.DNETLevel == DNetAccessLevel.Employee)
{
    <script>
        $(document).ready(function () {
            $('input.ee-disabled').attr('disabled', true);
            $('select.ee-disabled').attr('disabled', true);
        })
    </script>
}