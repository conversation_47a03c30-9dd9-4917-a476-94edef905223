@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@model IEnumerable<DarwiNet2._0.Data.EmployeeBeneficiary>
@{
    Layout = null;
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <link href="~/Scripts/animate.css" rel="stylesheet" />
    <div class="row">
        <div class="col-md-12" id="returnResponse">
            <div id="alertBox" class="alert alert-danger alert-dismissible" role="alert" style="display:none">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div id="response"></div>
            </div>
        </div>
    </div>
    <div class="row" style="padding-bottom: 10px;">
        <div class="pull-right create-pad">
            @if (ViewBag.Access == MenuAccessLevel.FullAccess)
            {
                <a href="javascript: void(0)" id="addEdit" data-url='@Url.Action("BeneficiaryAllocations", "Employees", new { EmployeeID = ViewBag.EmployeeID })' class="ee-hist btn btn-thinkware">@FieldTranslation.GetLabel("Add Allocations", GlobalVariables.LanguageID)</a>
                <a href="javascript: void(0)" id="addEdit" data-url='@Url.Action("AddBeneficiary", "Employees", new { e = ViewBag.EmployeeID })' class="ee-hist btn btn-thinkware">@FieldTranslation.GetLabel("Add Beneficiary", GlobalVariables.LanguageID)</a>
            }
        </div>
    </div>
    <table class="table" id="benTable">
        <thead>
            <tr>
                <th title="@FieldTranslation.GetLabel("First Name", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("First Name", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Last Name", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Last Name", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("SSN", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("SSN", GlobalVariables.LanguageID)
                </th>
                <th width="100px" title="@FieldTranslation.GetLabel("Birth Date", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Birth Date", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Gender", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Gender", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Relationship", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Relationship", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Actions", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Actions", GlobalVariables.LanguageID)
                </th>
            </tr>
        </thead>
        <tbody>

            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        <a href="javascript: void(0)" id="reviewEdit" data-url='@Url.Action("EditBeneficiary", "Employees", new { e = ViewBag.EmployeeID, s = item.SocialSecNumber })' class="ee-hist">@item.FirstName</a>
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.LastName)
                    </td>
                    <td>
                        @if (item.SocialSecNumber.Substring(0, 2) == "EF")
                        {
                            @FieldTranslation.GetMaskSSN("");
                        }
                        else
                        {
                            @FieldTranslation.GetMaskSSN(item.SocialSecNumber);
                        }
                    </td>
                    <td>
                        @FieldTranslation.ToShortDate(item.Birthdate.ToString())
                    </td>
                    <td>
                        @FieldTranslation.GetEnumDescription(typeof(enGetGender), Convert.ToInt32(item.Gender ?? 0))
                    </td>
                    <td>
                        @FieldTranslation.GetEnumDescription(typeof(enRelationship), Convert.ToInt32(item.EmployeeRelationship ?? 0))
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.EMail)
                    </td>
                    <td>
                        <a href="#" data-emp="@item.EmployeeID" data-ssn="@item.SocialSecNumber" id="deleteEmployeeBeneficiary"><i class="icon-red fa fa-times fa-lg fa-fw"></i></a>
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <script>
        $(document).ready(function () {
            var grid = $("#benTable")
                .kendoGrid({
                    dataSource: {
                        pageSize: 15
                    },
                    sortable: true,
                    pageable: true,
                    filterable: true,
                    scrollable: false,
                    groupable: true,
                    resizable: true
                }).data("kendoGrid");
        }
        );

    </script>

    <script>

        //Slide mechanic
        var fadeToggle = function (id) {
            $(id).toggle(function () {
                $(id).removeClass('animated slideInLeft');
                $(id).addClass('animated slideOutRight');
            },
                function () {
                    $(id).removeClass('animated slideOutRight');
                    $(id).addClass('animated slideInLeft');
                });
        };
        $("#payHistory").hide();
        $("#paySummary").hide();
        $("#payCode").hide();

        function detailInit(e) {
            $("#check-sub-list").appendTo(e.detailCell).kendoGrid({
                dataSource: {
                    serverPaging: true,
                    serverSorting: true,
                    serverFiltering: true,
                    pageSize: 10,
                },
                scrollable: true,
                sortable: true,
                pageable: true
            });
        }

        //Swap between pay history and pay code list
        $(document).ready(
            function () {
                $('#changePayCode').click(
                    function () {
                        $("#payCodesList").slideUp();
                        $("#payCode").show("slow");
                    });
                $('#backPayCode').click(
                    function () {
                        $("#payCode").slideUp();
                        $("#payCodesList").show("slow");
                    });
                $(document).on("click", "#payHistoryModal",
                    function () {
                        $("#partialDiv").slideUp();
                        $("#listDiv").show("slow");
                    });

                $(document).on("click", "#paySummaryModal",
                    function () {
                        $("#partialDiv").slideUp();
                        $("#listDiv").show("slow");
                    });

                $(document).on("click", "#addEdit",
                    function () {
                        $("#partialDiv").slideUp();
                        $("#listDiv").show("slow");
                    });
                $(document).on("click", "#reviewEdit",
                    function () {
                        $("#partialDiv").slideUp();
                        $("#listDiv").show("slow");
                    });
                $(document).on("click", "#backPayCodes",
                    function () {
                        $("#listDiv").slideUp();
                        $("#partialDiv").show("slow");
                    });

                $(document).on("click", ".backPayCodes",
                    function () {
                        $("#listDiv").slideUp();
                        $("#partialDiv").show("slow");
                    });

                $('#backPaySummary').click(
                    function () {
                        $("#paySummary").slideUp();
                        $("#payCodesList").show("slow");
                    });

            }
        );

    </script>
}