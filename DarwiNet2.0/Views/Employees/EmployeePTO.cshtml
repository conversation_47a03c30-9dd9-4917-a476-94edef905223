@using DarwiNet2._0.Data;

@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DataDrivenViewEngine.Models.Core;
@{
    Layout = null;
}

@{
    ViewBag.Title = "PTO";

}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{


    <script>
        function dataBound() {
            var dataSource = this.dataSource;
            this.element.find('tr.k-master-row').each(function() {
                var row = $(this);
                var data = dataSource.getByUid(row.data('uid'));

                // this example will work if ReportId is null or 0 (if the row has no details)
                if (!data.get('PTOAmountTaken')) {
                    row.find('.k-hierarchy-cell a').css({ opacity: 0.3, cursor: 'default' }).click(function(e) {
                        e.stopImmediatePropagation();
                        return false;
                    });
                }
            });
        }

    </script>
    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                <div class="colored-line-left"></div>
            </div>
            <div class="col-md-6 col-sm-6">
                <div class="text-right">
                   
                </div>
            </div>
        </div>
    </div>
    <div id="checkhist">




        @(Html.Kendo().Grid<EmployeePTOType>()
              .Name("grid")
              .ToolBar(tools => tools.Excel())
              .Excel(e => e.AllPages(true))
              .Excel(excel => excel
                  .FileName("EmployeePTO.xlsx")
              )
              .Columns(columns =>
              {
                  columns.Bound(e => e.dispType).Title("PTO ID");
                  columns.Bound(e => e.Description).Title("Description");
                  columns.Bound(e => e.dispMethod).Title("Accrual Method");
                  columns.Bound(e => e.AvailableHrs).Title("Available").Format("{0:N2}");
                  columns.Bound(e => e.AmountTaken).Title("Used").Format("{0:N2}");
                  columns.Bound(e => e.Accrued).Title("Accrued").Format("{0:N2}");
                  columns.Bound(e => e.Inactive).Title("Inactive").ClientTemplate("<input type='checkbox' #= Inactive ? checked='checked' : '' # disabled='disabled'></input>");
              })
              .Sortable()
              // .Selectable()
              .Scrollable(scr => scr.Height(380))
              .Groupable()
              // .ColumnMenu()
              .Pageable()
              .Filterable()
              .ClientDetailTemplateId("template")
              //.HtmlAttributes(new { style = "height: 647px" })
              .Reorderable(reorder => reorder.Columns(true))
              .Resizable(resize => resize.Columns(true))
              .DataSource(dataSource => dataSource
                  .Ajax()
                  //  .PageSize(10)
                  .Read(read => read.Action("GetEmployeePTO", "Employees", new {e = ViewBag.EmployeeID, client = ViewBag.Client, co = ViewBag.CompanyID}))

              )
              .Events(events => events.DataBound("dataBound"))
              )


        <script id="template" type="text/kendo-tmpl">
        @(Html.Kendo().Grid<EmployeeCodeHist>()
              .Name("grid_pc#=PTOType#")
              .Columns(columns =>
              {
                  columns.Bound(p => p.CheckDate).Title("Check Date").Format("{0:MM/dd/yyyy}");
                  columns.Bound(p => p.Hours).Title("Amount").Format("{0:N2}");
                  columns.Bound(p => p.CheckNumber).Title("Check Number");
              })
              .DataSource(dataSource => dataSource
                  .Ajax()
                  .Read(read => read.Action("GetEEPTOHistory", "Employees", new {e = "#=EmployeeID#", p = "#=PTOType#", client = ViewBag.Client, co = ViewBag.CompanyID}))
              )
              .ToClientTemplate()
        )


    </script>



    </div>
}