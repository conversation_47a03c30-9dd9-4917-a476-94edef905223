@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using EmploymentStatuses = DataDrivenViewEngine.Models.Core.enEmploymentStatuses
@using DataDrivenViewEngine.Models.Core
@model DarwiNet2._0.ViewModels.EmployeesInfoVM
@using PayPeriods = DataDrivenViewEngine.Models.Core.enPayPeriods
@using PayTypes = DataDrivenViewEngine.Models.Core.enPayTypesCOS

@{
    Layout = null;

}
<style>
    #dvheader {
        background: lightgray;
        width: 100%;
        display: inline-block;
        padding: 15px 0 0 0;
        margin-top: -15px;
    }

    .comp, .empsts, .posdtls {
        margin-top: 5px !important;
    }

    .efectdt span.k-widget.k-datepicker.k-header {
        width: 100% !important;
        height: 33px;
    }

    .modal-body {
        overflow: auto;
    }

    .form-horizontal .control-label {
        text-align: left;
    }

    .k-widget.k-datepicker.k-header {
        border: 0;
    }
</style>
@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "editemploymentinfo" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.Employee.EmployeeID)
    @Html.Hidden("hndEmpName", (string)ViewBag.EmployeeName)
    <div class="form-horizontal">
        <div class="row">
            <div id="dvheader">
                <div class="col-md-12" style="background-color:lightgray;">
                    <div class="form-horizontal">
                        <div class="col-md-6 efectdt">
                            <div class="form-group">

                                @Html.LabelFor(model => Model.Employee.StartDate, htmlAttributes: new { @class = "control-label col-md-4 " }, labelText: FieldTranslation.GetLabel("Effective Date", GlobalVariables.LanguageID))
                                <div class="col-md-6">
                                    <input type="text" value="@FieldTranslation.ToShortDate(DateTime.Now.Date)" id="Effectivedate" name="EffectiveDate" required class="form-control" style="height:27px;" />
                                    <div class="EffectivedateValidation"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <input type="hidden" id="hdnreason" value="" name="hdnreason" />
                                <input type="hidden" id="hdnsupervisor" name="hdnsupervisor" value="" />
                                @Html.Label(FieldTranslation.GetLabel("Reason for Change", GlobalVariables.LanguageID), new { @class = "control-label col-md-4" })
                                <div class="col-md-6">

                                    @Html.DropDownList("ReasonforChange", EnumHelper.GetSelectList(typeof(enChangeReason)), new { id = "ReasonforChange", name = "ReasonforChange", @class = "form-control field-required", required = "required" })
                                    <div id="ReasonValidation" class="text-danger"></div>
                                </div>
                            </div>
                            <div class="form-group" id="OtherReason">
                                @Html.Label("lblothers", " ", new { @class = "control-label col-md-4" })
                                <div class="col-md-6 sbchanged">
                                    @Html.TextArea("txtothers", "", new { @class = "form-control" })
                                </div>
                            </div>
                            <div class="form-group">
                                @Html.Label("lblattach", "Add attachments", new { @class = "control-label col-md-4" })
                                <div>
                                    <span><a href="@Url.Action("Create", "Notes", new {c = GlobalVariables.CompanyID, cc = GlobalVariables.Client, e = Model.Employee.EmployeeID, a = "EmploymentInfo", co = "Employees", k1 = Model.Key1, k2 = Model.Key2, k3 = Model.Key3, k4 = Model.Key4})" data-toggle="modal" data-target="#noteModal" data-remote="false" title="Add attachments, if applicable"><i style="color: #666" class="fa fa-paperclip fa-fw fa-2x"></i></a></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                @Html.Label("lblcat", "Select at least one category:", new { @class = "control-label col-md-12" })
                            </div>
                            @if (ViewBag.PaycodeAccess != 2)
                            {
                                <div class="form-group" style="margin-bottom:0;">
                                    <div class="checkbox">
                                        <label style="font-weight:700">@Html.CheckBox("Compensation", false, new { @class = "comp chkbox" })&nbsp;@Html.DisplayName("Compensation")</label>
                                    </div>
                                </div>
                            }
                            <div class="form-group" style="margin-bottom:0;">
                                <div class="checkbox">
                                    <label style="font-weight:700">@Html.CheckBox("EmploymentStatus", false, new { @class = "empsts chkbox" })&nbsp;@Html.DisplayName("Employment Status")</label>
                                </div>
                            </div>
                            <div class="form-group" style="margin-bottom:0;">
                                <div class="checkbox">
                                    <label style="font-weight:700">@Html.CheckBox("PositionDetails", false, new { @class = "posdtls chkbox" })&nbsp;@Html.DisplayName("Position Details")</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">

                                <div>
                                    @Html.Label("Processing Notes", "Processing Notes", new { @class = "control-label col-md-9" })
                                    @Html.TextArea("txtaddnotes", "", new { @class = "form-control", @rows = 5 })
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <br />
    <div class="row" id="dvcomp">
        <div class="col-md-12">
            <div class="form-horizontal">
                <div class="form-group">
                    <p style="margin-left:10px;"><i>The Change Center allows you to make changes to an employee's base pay information. To add or edit other pay codes, navigate to Payroll -> Pay Codes.</i></p>

                    <p style="margin-left:10px;"><i><b>Note:</b> Pay Rate changes submitted less than 3 days prior to payroll processing may not be applied until the following pay cycle.</i></p>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(model => model.EmpPayCodes.PayType, htmlAttributes: new { @class = "col-md-2 control-label" }, labelText: FieldTranslation.GetLabel("PayType", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        @Html.DropDownListFor(model => model.EmpPayCodes.PayType, EnumHelper.GetSelectList(typeof(enPayTypesCOS)), new { id = "PayType", name = "EmpPayCodes.PayType", @class = "form-control select", @Onchange = "PayTypeChangefunc(this)" })
                        <div class="PayType" />
                    </div>
                    @Html.LabelFor(model => model.EmpPayCodes.PayRateAmount, htmlAttributes: new { @class = "col-md-2 control-label" }, labelText: FieldTranslation.GetLabel("PayRateAmount", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        @Html.TextBoxFor(model => model.EmpPayCodes.PayRateAmount, new { @class = "form-control select prequired" })
                        @Html.ValidationMessageFor(model => model.EmpPayCodes.PayRateAmount, "", new { @class = "text-danger" })
                    </div>

                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.EmpPayCodes.PayUnitPeriod, htmlAttributes: new { @class = "col-md-2 control-label" }, labelText: FieldTranslation.GetLabel("Pay Unit Period", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        @Html.DropDownListFor(model => model.EmpPayCodes.PayUnitPeriod, EnumHelper.GetSelectList(typeof(enPayUnits)), new { id = "PayUnitPeriod", name = "EmpPayCodes.PayUnitPeriod", @class = "form-control select paytypesel" })
                        @Html.ValidationMessageFor(model => model.EmpPayCodes.PayUnit, "", new { @class = "text-danger" })
                        @Html.HiddenFor(model => model.EmpPayCodes.PayUnit, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    @Html.Label(FieldTranslation.GetLabel("Overtime Exemption Status", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        @Html.DropDownListFor(model => model.Employee.SalariedExemptEmployee, EnumHelper.GetSelectList(typeof(enExemptionStatus)), new { id = "SalariedExemptEmployee", name = "Employee.SalariedExemptEmployee", @class = "form-control ee-disabled select exemptsts" })
                        @Html.ValidationMessage("SalariedExemptEmployee", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="dvempsts" style="display:none;">
        <div class="col-md-12">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.Label(FieldTranslation.GetLabel("Employment Status", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        @Html.DropDownListFor(model => model.Employee.PartTime, EnumHelper.GetSelectList(typeof(enEmploymentTypeCOS)), new { id = "PartTime", name = "Employee.PartTime", @class = "form-control select", @Onchange = "callChangefunc(this.value)" })
                        @Html.ValidationMessage("PartTime", new { @class = "text-danger" })
                    </div>
                    @Html.LabelFor(model => Model.Employee.WorkHoursPerYear, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Work Hours PerYear", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        <input type="text" id="txtWorkHoursPerYear" name="Employee.WorkHoursPerYear" value="@Model.Employee.WorkHoursPerYear" readonly="readonly" class="form-control select ee-disabled txtWorkHoursPerYear" />
                        @Html.ValidationMessageFor(model => Model.Employee.WorkHoursPerYear, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => Model.BEClass, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Benefit Class", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        <select id="BenefitClass" name="BEClass" class="form-control select ee-disabled">
                            @{
                                foreach (var item in ViewBag.BEClasses)
                                {
                                    <option value="@item.Code" @((item.Description == @Model.BEClass) ? "selected" : string.Empty)>@item.Description</option>
                                }
                            }
                        </select>

                        @Html.ValidationMessageFor(model => Model.BEClass, "", new { @class = "text-danger" })
                    </div>

                </div>

            </div>
        </div>
    </div>

    <div class="row" id="dvposdtls">
        <div class="col-md-12">
            <div class="form-horizontal">
                <div class="form-group">
                    @Html.LabelFor(model => Model.Employee.Position, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Position", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        <select class="form-control  select" name="Employee.Position" id="Position">
                            @foreach (Code_Description item in Model.Positions)
                            {
                                <option value="@item.Code" @((item.Code == Model.Employee.Position) ? "selected" : string.Empty)>@item.Description</option>
                            }
                        </select>
                        @Html.ValidationMessageFor(model => Model.Employee.Position, "", new { @class = "text-danger" })
                    </div>
                    @Html.LabelFor(model => Model.Employee.Department, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        <select class="form-control  select" name="Employee.Department" id="Department">
                            @foreach (Code_Description item in Model.Departments)
                            {
                                <option value="@item.Code" @((item.Code == Model.Employee.Department) ? "selected" : string.Empty)>@item.Description</option>
                            }
                        </select>
                        @Html.ValidationMessageFor(model => Model.Employee.Department, "", new { @class = "text-danger" })
                    </div>

                </div>
                <div class="form-group">
                    @Html.LabelFor(model => Model.Employee.JobCategory, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Job Category", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        @Html.DropDownListFor(model => model.Employee.JobCategory, EnumHelper.GetSelectList(typeof(enJobCategories)), new { id = "JobCategory", name = "Employee.JobCategory", @class = "form-control select ee-disabled" })
                        @Html.ValidationMessageFor(model => Model.Employee.JobCategory, "", new { @class = "text-danger" })
                    </div>
                    @Html.LabelFor(model => Model.Locations, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Location", GlobalVariables.LanguageID))

                    <div class="col-md-4">
                        <select class="form-control select " name="Employee.UDF1" id="Location">
                            <option></option>
                            @foreach (Code_Description item in Model.Locations)
                            {
                                <option value="@item.Code" @((item.Code == @Model.Employee.UDF1) ? "selected" : string.Empty)>@item.Description</option>
                            }
                        </select>
                        @Html.ValidationMessage("Locations", new { @class = "text-danger" })
                    </div>

                </div>
                <div class="form-group">
                    @Html.LabelFor(model => Model.Employee.WorkersComp, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Workers Comp", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        <select class="form-control select" name="Employee.WorkersComp" id="WorkersComp">
                            @*<option></option>*@
                            @foreach (Code_Description item in Model.WorkersComp)
                            {
                                <option value="@item.Code" @((item.Code == Model.Employee.WorkersComp) ? "selected" : string.Empty)>@item.Description</option>
                            }
                        </select>
                        @Html.ValidationMessageFor(model => Model.Employee.WorkersComp, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div style="display: none;">
        <input type="checkbox" id="ePayTypeChange" name="ePayTypeChange" />
    </div>
    <div class="form-group">
        <div class="col-md-12">
            <div class="pull-right">
                <button type="button" data-toggle="modal" class="btn btn-danger" id="btnsubmit">Submit</button>
            </div>

        </div>
    </div>
    <div class="modal fade" id="COSModal" tabindex="-1" role="dialog" aria-labelledby="COSModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="waiveModalLabel">Changes Confirmation</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 text-center">
                            <i style="color: yellow;" class="fa fa-exclamation-triangle fa-4x"></i>
                        </div>
                        <div class="col-md-12">
                            <div>Are you sure you want to save the changes?</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" onclick="CloseConfirmation();">Cancel</button>
                    <button type="submit" class="btn btn-success">Save</button>
                </div>
            </div>
        </div>
    </div>
}
<script src="~/Scripts/jquery.datetimepicker.js"></script>
<link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
<script src="~/Scripts/MaskedInput.js"></script>
<script src="~/Scripts/jquery.inputmask.bundle.js"></script>
<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>

<script>

        $(document).ready(function () {
            $('#Effectivedate').kendoMaskedDatePicker();
            $('#dvcomp').hide();
            $('#dvempsts').hide();
            $('#dvposdtls').hide();
            $("#OtherReason").hide();

            if ('@Model.Employee.PartTime' == '1') {
                $('#BenefitClass.ee-disabled').attr('disabled', false);
            }
            else {
                $('#BenefitClass.ee-disabled').attr('disabled', true);
            }
            var Salariedexempt = '@Model.Employee.SalariedExemptEmployee';
            if (Salariedexempt == 'True') {
                $('.exemptsts').val(1);
            }
            else {
                $('.exemptsts').val(0);
            }
            $('.paytypesel').attr('disabled', true);

            $('#btnsubmit').click(function () {
                if ($('.chkbox').filter(':checked').length < 1) {
                    alert("Please Check at least one Option");
                    return false;
                } else {
                    if ($("#editemploymentinfo").valid()) {
                        $('#COSModal').modal('toggle');
                    }
                }
            });

            $(document).on('hidden.bs.modal', '#noteModal', function () {
                $('body').addClass('modal-open');
            });

        });
        $(function () {
            $(".comp").change(function () {
                if ($(this).is(':checked')) {
                    $('#dvcomp').show();
                }
                else {
                    $('#dvcomp').hide();
                }
            });
            $(".empsts").change(function () {
                if ($(this).is(':checked')) {
                    $('#dvempsts').show();


                }
                else {
                    $('#dvempsts').hide();
                }
            });
            $(".posdtls").change(function () {
                if ($(this).is(':checked')) {

                    $('#dvposdtls').show();
                    $('.paytypesel').attr('disabled', true);
                }
                else {
                    $('#dvposdtls').hide();
                }
            });
            $("#ReasonforChange").change(function () {
                var value = document.getElementById("ReasonforChange").value;
                var ddtext = $("#ReasonforChange option:selected").text();
                $("#hdnreason").val(ddtext);
                if (value == "6") {
                    $("#OtherReason").show();
                    var othrreason = $("#txtothers").val();
                    $("#hdnreason").val(othrreason);
                }
                else {
                    $("#OtherReason").hide();
                }
            });

        });

        function callChangefunc(selval) {
            if (selval == '1') {
                $('#BenefitClass.ee-disabled').attr('disabled', false);
            }
            else {
                $('#BenefitClass.ee-disabled').attr('disabled', true);
            }

            //To change the WorkHoursPerYear based on EmploymentStatus Selection
            switch (selval) {
                case "0":
                    result = 0;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;

                case "1":
                    result = 2080;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;

                case "2":
                    result = 2080;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;
                case "3":
                    result = 1040;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;

                case "4":
                    result = 1040;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;
                case "5":
                    result = 1040;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;

                case "6":
                    result = 480;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;
                case "7":
                    result = 2080;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;

                case "8":
                    result = 520;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;
                case "9":
                    result = 840;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;

                case "10":
                    result = 840;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;
                case "11":
                    result = 2080;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;

                case "12":
                    result = 2080;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;
                case "13":
                    result = 2080;
                    $(".txtWorkHoursPerYear").removeAttr("readonly");
                    $(".txtWorkHoursPerYear").val(result);
                    $(".txtWorkHoursPerYear").attr("readonly", "readonly");
                    break;
            }
        }
        function PayTypeChangefunc(ctrl) {
            var type = $(ctrl).val();

            var seltype = $(ctrl).find("option:selected").text();
            var existtype = '@Model.EmpPayCodes.PayType';
            var typedesc = (existtype == '1' ? 'REGHRS' : (existtype == '2') ? 'SALARY' : '');
            if (type != existtype) {
                $('.prequired').val('');
                $('.paytypesel').attr('disabled', false);
                $('.prequired').addClass('field-required');
                $('.prequired').prop('required', true);
                bootbox.dialog({
                    message: "Would you like to replace this existing Primary Pay Code (<strong>" + typedesc + "</strong>) with this code? " +
                        "<strong>" +
                        seltype.toUpperCase() +
                        "</strong>" +
                        " for " +
                        "<strong>@ViewBag.EmployeeName</strong>" +
                        "?",
                    title: "PayType Change",
                    buttons: {
                        main: {
                            label: "Cancel",
                            className: "btn-primary",
                            callback: function () {
                                $('#ePayTypeChange').prop('checked', false);
                            }
                        },
                        danger: {
                            label: "Yes",
                            className: "btn-danger",
                            callback: function () {
                                if (type != 0) {
                                    $('#ePayTypeChange').prop('checked', true);


                                }
                            }
                        }
                    }
                });
            }
        }
        function CloseConfirmation() {
            $('#COSModal').modal('toggle');
        }
        function validateform() {


        }
        function isDatenew(txtDate) {
            alert('in')
            var currVal = txtDate;
            if (currVal == '')
                return false;

            var rxDatePattern = /^(\d{1,2})(\/|-)(\d{1,2})(\/|-)(\d{4})$/; //Declare Regex
            var dtArray = currVal.match(rxDatePattern); // is format OK?

            if (dtArray == null)
                return false;

            //Checks for mm/dd/yyyy format.
            dtMonth = dtArray[1];
            dtDay = dtArray[3];
            dtYear = dtArray[5];

            var GivenDate = dtYear + '-' + dtMonth + '-' + dtDay;
            var CurrentDate = new Date();
            GivenDate = new Date(GivenDate);

            if (GivenDate > CurrentDate) {
                return false;
            } else {
                if (dtYear < 1753)
                    return false;
                else if (dtMonth < 1 || dtMonth > 12)
                    return false;
                else if (dtDay < 1 || dtDay > 31)
                    return false;
                else if ((dtMonth == 4 || dtMonth == 6 || dtMonth == 9 || dtMonth == 11) && dtDay == 31)
                    return false;
                else if (dtMonth == 2) {
                    var isleap = (dtYear % 4 == 0 && (dtYear % 100 != 0 || dtYear % 400 == 0));
                    if (dtDay > 29 || (dtDay == 29 && !isleap))
                        return false;
                }
            }
            return true;
        }

        // mini jQuery plugin that formats to two decimal places
        (function ($) {
            $.fn.currencyFormat = function () {
                this.each(function (i) {
                    $(this).change(function (e) {
                        if (isNaN(parseFloat(this.value))) return;
                        this.value = parseFloat(this.value).toFixed(2);
                    });
                });
                return this; //for chaining
            }
        })(jQuery);
        $(function () {
            $('.prequired').currencyFormat("${0:n2}");
        });
        $('.prequired').keypress(function (event) {
            var $this = $(this);
            if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
               ((event.which < 48 || event.which > 57) &&
               (event.which != 0 && event.which != 8))) {
                event.preventDefault();
            }

            var text = $(this).val();
            if ((event.which == 46) && (text.indexOf('.') == -1)) {
                setTimeout(function () {
                    if ($this.val().substring($this.val().indexOf('.')).length > 3) {
                        $this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
                    }
                }, 1);
            }

            if ((text.indexOf('.') != -1) &&
                (text.substring(text.indexOf('.')).length > 2) &&
                (event.which != 0 && event.which != 8) &&
                ($(this)[0].selectionStart >= text.length - 2)) {
                event.preventDefault();
            }
        });
        $('.prequired').blur(function (event) {
            var textprate = $(this).val();
            if (textprate == 0) {
                $('.prequired').val('');
                $('.prequired').addClass('field-required');
                $('.prequired').prop('required', true);
            }
        });
</script>

<script>
    var form = $("#editemploymentinfo");
    $.validator.addMethod("ValidateReason", function (value) {
        if (value != 0) {
            return true;
        }
        return false;
    }, "Please select a valid Reason.");

    $.validator.addMethod("ValidateEffectiveDate", function (value) {
        if (isDatenew(value)) {
            return true;
        }
        return false;
    }, "Please enter a valid Effective Date.");

    $.validator.addMethod("ValidatePayType", function (value) {
        if (value != 0) {
            return true;
        }
        return false;
    }, "Please select a valid Pay Type.");

    form.validate({
        rules: {
            ReasonforChange: {
                ValidateReason: true
            },
            EffectiveDate: {
                date: true,
                required: true
            },
            "EmpPayCodes.PayType": {
                ValidatePayType: true
            }
        },
        errorPlacement: function (error, element) {
            if (element.attr("name") == "ReasonforChange") { error.insertAfter(".ReasonValidation"); }
            if (element.attr("name") == "EffectiveDate") { error.insertAfter(".EffectivedateValidation"); }
            if (element.attr("name") == "EmpPayCodes.PayType") { error.insertAfter(".PayType"); }

            else { error.insertAfter(element); }
        }
    });
</script>