@model DarwiNet2._0.Data.Bank

@{
    if (String.IsNullOrEmpty(ViewBag.Bankname))
    {
        <div class="alert alert-danger alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <span class="col-md-offset-2">Enter your routing number in the field above</span>
        </div>
    }
    else
    {
        <div class="col-md-offset-2">
            <div class="row">
                <div class="col-md-3">
                    <strong>Bank Name:</strong>
                </div>
                <div class="col-md-9">
                    @Model.BankName
                </div>
            </div>
            <input type="hidden" id="BankName" name="BankName" value="@Model.BankName" />
            <input type="hidden" id="BankID" name="BankID" value="@Model.BankID" />
            <div class="row">
                <div class="col-md-3">
                    <strong>Address:</strong>
                </div>
                <div class="col-md-9">
                    <div class="bank-info">
                        <address>
                            @Model.Address1<br />
                            @if (!String.IsNullOrEmpty(Model.Address2))
                            {
                                @Model.Address2<br />
                            }
                            @Model.City, @Model.State @Model.Zip
                        </address>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <strong>Phone:</strong>
                </div>
                <div class="col-md-9">
                    @Model.PhoneNumber1
                </div>
            </div>
        </div>
    }
}
