@model DarwiNet2._0.Data.EmployeeDependent
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;
@using DataDrivenViewEngine.Models.Core;
@{
    Layout = null;
    ViewBag.Title = "Add Dependent";
}

@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{

    <script>
        $("#bcedit").on('submit', function (ev) {
            var frm = $('#bcedit');
            ev.preventDefault();

            $.ajax({
                type: "POST",
                url: '@Url.Action("AddDependent", "Employees")',
                dataType: "html",
                data: frm.serialize(),
                success: function (data) {
                    if (data.indexOf('There was an error') >= 0) {
                        $('#error').show();
                        $('#errResponse').text(data);
                    } else {
                        //$('#return').append(data);
                        var div = $('#partialDiv');
                        var url2 = '@Url.Action("EmployeeDependents", "Employees")';
                        url2 = url2 + "?e=@ViewBag.EmployeeID&client=@ViewBag.Client&co=@ViewBag.CompanyID";
                        $.get(url2, function (data) {
                            div.html(data);
                            $('#partialDiv').delay(1000).show(0);
                            $('#listDiv').hide();
                            $('#loading').delay(1000).hide(0);
                        });
                    }
                }
            });
        });
    </script>
    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                <div class="colored-line-left"></div>
            </div>
        </div>
    </div>

    using (Html.BeginForm(null, null, FormMethod.Post, new { id = "bcedit" }))
    {
        @Html.AntiForgeryToken()

        <div class="form-horizontal">

            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <input type="hidden" id="CompanyID" name="CompanyID" value="@ViewBag.CompanyID" />
            <input type="hidden" id="EmployeeID" name="EmployeeID" value="@ViewBag.EmployeeID" />
            <input type="hidden" name="clientID" value="@ViewBag.Client" />
            @Html.Partial("~/Views/Employees/_employeeError.cshtml")
            <div class="form-group">
                @Html.LabelFor(model => model.FirstName, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("FirstName", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.FirstName, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.MiddleName, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("MiddleName", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.MiddleName, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.LastName, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("LastName", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.LastName, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.SSN, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("SSN", GlobalVariables.LanguageID))
                <div class="col-md-4">                   
                    @Html.TextBoxFor(model => model.SSN, new { @class = "form-control ssn-type-required", @maxlength = "11" })
                    @*}*@
                    @Html.ValidationMessageFor(model => model.SSN, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.BirthDate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("BirthDate", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <input type="text" class="" value="" id="BirthDate" name="BirthDate" />
                    <div class="error-msg"></div>
                    @Html.ValidationMessageFor(model => model.BirthDate, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Gender, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Gender", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.DropDownList("Gender", EnumHelper.GetSelectList(typeof(enGetGender)), new { id = "Gender", name = "Gender", @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Gender, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.EmployeeRelationship, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EmployeeRelationship", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.DropDownList("EmployeeRelationship", EnumHelper.GetSelectList(typeof(enRelationship)), new { id = "EmployeeRelationship", name = "EmployeeRelationship", @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.EmployeeRelationship, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.LivesWithEmployee, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("LivesWithEmployee", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <div class="checkbox">
                        @Html.EditorFor(model => model.LivesWithEmployee)
                        @Html.ValidationMessageFor(model => model.LivesWithEmployee, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.OverageStudent, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("OverageStudent", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <div class="checkbox">
                        @Html.EditorFor(model => model.OverageStudent)
                        @Html.ValidationMessageFor(model => model.OverageStudent, "", new { @class = "text-danger" })
                    </div>
                </div>
                @Html.LabelFor(model => model.Disabled, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Disabled", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <div class="checkbox">
                        @Html.EditorFor(model => model.Disabled)
                        @Html.ValidationMessageFor(model => model.Disabled, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Comments, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Comments", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Comments, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Comments, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-12">
                    <div class="pull-right">
                        <button type="button" class="backPayCodes btn btn-thinkware">@FieldTranslation.GetLabel("Cancel", GlobalVariables.LanguageID)</button> <input type="submit" value="@FieldTranslation.GetLabel("Save",GlobalVariables.LanguageID)" class="btn btn-thinkware" />
                    </div>
                </div>
            </div>
        </div>
    }
    <script src="~/Scripts/MaskedInput.js"></script>
    <script>

        $(document).ready(function () {

            $(".ssn-type-required").mask("***********", { placeholder: " " });

        });
        $('.ssn-type-required').on('blur', function () {
            $('.ssn-type-required').attr({ 'type': 'password' });
        });
        $('.ssn-type-required').on('focus', function () {
            $('.ssn-type-required').attr({ 'type': 'text' });
        });
        $("input[type=submit]").click(function () {
            $("#bcedit").submit(function () {
                $('.ssn-type-required').mask('999999999');
            });
        });
    </script>

}
@if (ViewBag.Access == MenuAccessLevel.ReadOnly)
{
    <script>
        $(document).ready(function () {
            $('#bcedit input').attr('disabled', true);
            $('#bcedit select').attr('disabled', true);
        })
    </script>
    <script>
        $(document).ready(function () {
            $('#BirthDate').addClass('form-control');
        });
    </script>
}
else
{
    <script>
        $(document).ready(function () {
            $('#BirthDate').kendoMaskedDatePicker();
        });

    </script>
}
@if (GlobalVariables.DNETLevel == DNetAccessLevel.Employee)
{
    <script>
        $(document).ready(function () {
            $('input.ee-disabled').attr('disabled', true);
            $('select.ee-disabled').attr('disabled', true);
        })
    </script>
}