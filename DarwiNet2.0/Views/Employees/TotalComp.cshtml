@model DarwiNet2._0.Data.EETotalComp
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Data;
@using DataDrivenViewEngine.Models.Core;

@{
    ViewBag.Title = "Total Compensation";

}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else if ((bool)ViewBag.NoHistory)
{
    <div class="alert alert-danger alert-dismissible" role="alert" id="globalFail">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <p>No compensation history has been found to display.</p>
    </div>
}
else
{
    <script>
        $(function() {
            // bind change event to select
            $('#sel_year').on('change', function(evt) {
                var url = "@Url.Action("TotalComp", "Employees")";
                url = url + "?y=" + $(this).val();
                window.location.href = url;
            });
        });
    </script>
    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>

                <div class="colored-line-left"></div>
            </div>
            <div class="col-md-6 col-sm-6">
                <div class="text-right">
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <select id="sel_year" name="sel_year" class="form-control">
                @foreach (var y in ViewBag.Years)
                {
                    <option value="@y" @((ViewBag.SelectedYear.ToString() == y.ToString()) ? "selected" : string.Empty)>@y</option>
                }
            </select>
        </div>
    </div>
    <div class="row" style="padding-top: 15px;">
        <div class="col-md-6">
            <div id="chart"></div>
            <script>
                function createChart() {
                    $("#chart").kendoChart({
                        title: {
                            position: "bottom",
                            text: "Total Compensation"
                        },
                        legend: {
                            visible: false
                        },
                        chartArea: {
                            background: ""
                        },
                        seriesDefaults: {
                            type: "donut",
                            startAngle: 150
                        },
                        series: [
                            {
                                name: "2011",
                                data: [
                                    {
                                        category: "Total Wages",
                                        value: @Model.totalAnnualWages,
                                        color: "#59ABE3"
                                    }, {
                                        category: "Total Benefits",
                                        value: @Model.totalBenefits,
                                        color: "#D91E18"
                                    }, {
                                        category: "Total Taxes",
                                        value: @Model.totalTaxes,
                                        color: "#87D37C"
                                    },{
                                        category: "Total Reimbursements",
                                        value: @Model.totalReim,
                                        color: "#F4D03F"
                                    }, {
                                        category: "Total PTO",
                                        value: @Model.totalPTO,
                                        color: "#eb8d2f"
                                    }
                                    , {
                                        category: "Total Holiday",
                                        value: @Model.totalHoliday,
                                        color: "#9B59B6"
                                    }
                                ]
                            }
                        ],
                        tooltip: {
                            visible: true,
                            template: "#= category # : #= kendo.toString(value,'c2') #"
                        }
                    });
                }

                $(document).ready(createChart);
                $(document).bind("kendo:skinChange", createChart);
            </script>
            @*<div class="col-md-12">
                $1$<table class="table">
                    <tr>
                        <th>Annual Wages</th>
                        <th>Paid Benefits</th>
                        <th>Paid Taxes</th>
                        <th>Paid PTO</th>
                        <th>Total Compensation</th>
                    </tr>
                    <tr>
                        <td>@string.Format("{0:C}", @Model.totalAnnualWages)</td>
                        <td>@string.Format("{0:C}", @Model.totalBenefits)</td>
                        <td>@string.Format("{0:C}", @Model.totalTaxes)</td>
                        <td>@string.Format("{0:C}", @Model.totalPTO)</td>
                        <td>@string.Format("{0:C}", @Model.totalCompensation)</td>
                    </tr>
                </table>#1#
                <div class="text-center">
                    <span style="display: inline-block; padding-right: 5px;">
                        <strong style="font-size: 16px;">Annual Wages</strong><br /> @string.Format("{0:C}", @Model.totalAnnualWages)
                    </span>
                    <span style="display: inline-block; padding-right: 5px;">
                        <strong style="font-size: 16px;">Paid Benefits</strong><br /> @string.Format("{0:C}", @Model.totalBenefits)
                    </span>
                    <span style="display: inline-block; padding-right: 5px;">
                        <strong style="font-size: 16px;">Paid Taxes</strong><br /> @string.Format("{0:C}", @Model.totalTaxes)
                    </span>
                    <span style="display: inline-block; padding-right: 5px;">
                        <strong style="font-size: 16px;">Paid PTO</strong><br /> @string.Format("{0:C}", @Model.totalPTO)
                    </span>
                    <span style="display: inline-block;">
                        <strong style="font-size: 16px;">Total Compensation</strong><br /> @string.Format("{0:C}", @Model.totalCompensation)
                    </span>
                </div>
            </div>*@
        </div>
        <div class="col-md-6">
            <div>
                <div id="line"></div>
                <script>
                    var lineData = [];
                </script>
                @foreach (EETotalComp thisComp in ViewBag.theComps)
                {
                    <script>
                        var data = { "year": @thisComp.year, "value": @thisComp.totalCompensation };
                        lineData.push(data);
                    </script>
                }
                <script>
                    function createChart2() {
                        $("#line").kendoChart({
                            dataSource: {
                                data: lineData
                            },
                            title: {
                                text: "@FieldTranslation.GetLabel("Total Comp Trend since Hire", GlobalVariables.LanguageID)"
                            },
                            legend: {
                                visible: false
                            },
                            seriesDefaults: {
                                type: "line",
                                labels: {
                                    visible: true,
                                    background: "transparent",
                                    format: "{0:c}"
                                }
                            },
                            series: [
                                {
                                    field: "value",
                                    name: "Total Comp"
                                }
                            ],
                            valueAxis: {
                                labels: {
                                    format: "{0:c}"
                                },
                                line: {
                                    visible: true
                                }
                            },
                            categoryAxis: {
                                field: "year",
                                majorGridLines: {
                                    visible: true
                                }
                            }
                        });
                    }

                    $(document).ready(createChart2);
                    $(document).bind("kendo:skinChange", createChart2);
                </script>

            </div>
        </div>
    </div>
    <div class="row" style="padding-top: 15px;">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><strong>@FieldTranslation.GetLabel("Total Compensation Breakdown", GlobalVariables.LanguageID)</strong>
                    </h3>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped">
                        <tr>
                            <td>
                                @FieldTranslation.GetLabel("Annual Wages", GlobalVariables.LanguageID)
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalAnnualWages)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                @FieldTranslation.GetLabel("Company Paid Benefits", GlobalVariables.LanguageID)
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalBenefits)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                @FieldTranslation.GetLabel("Company Paid Taxes", GlobalVariables.LanguageID)
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalTaxes)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                @FieldTranslation.GetLabel("Company Paid PTO", GlobalVariables.LanguageID)
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalPTO)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                @FieldTranslation.GetLabel("Company Paid Holiday", GlobalVariables.LanguageID)
                            </td>
                            <td>
                                @String.Format("{0:c}", Model.totalHoliday)
                            </td>
                        </tr>
                        <tr>
                            <td>
                                @FieldTranslation.GetLabel("Total Compensation", GlobalVariables.LanguageID)
                            </td>
                            <td>
                                @string.Format("{0:C}", @Model.totalCompensation)
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><strong>@FieldTranslation.GetLabel("Other Employer Paid Items", GlobalVariables.LanguageID)</strong></h3>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>@FieldTranslation.GetLabel("Item", GlobalVariables.LanguageID)</th>
                                <th>@FieldTranslation.GetLabel("Cost", GlobalVariables.LanguageID)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    @FieldTranslation.GetLabel("FICA SS", GlobalVariables.LanguageID)
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalFicaSS)
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @FieldTranslation.GetLabel("FICA Med", GlobalVariables.LanguageID)
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalFicaM)
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @FieldTranslation.GetLabel("Unemployment", GlobalVariables.LanguageID)
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalUnEmployment)
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @FieldTranslation.GetLabel("Workers Compensation", GlobalVariables.LanguageID)
                                </td>
                                <td>
                                    @String.Format("{0:c}", Model.totalWc)
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
     

    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><strong>@FieldTranslation.GetLabel("Annual Pay Breakdown", GlobalVariables.LanguageID)</strong></h3>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>@FieldTranslation.GetLabel("Pay Code", GlobalVariables.LanguageID)</th>
                                <th>@FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)</th>
                                <th>@FieldTranslation.GetLabel("YTD Amount", GlobalVariables.LanguageID)</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (EmployeeCodes item in Model.allPay)
                            {
                                <tr>
                                    <td>
                                        @item.Code
                                    </td>
                                    <td>
                                        @item.Description
                                    </td>
                                    <td>
                                        @String.Format("{0:c}", item.Additional)
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
        <div class="col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <strong>@FieldTranslation.GetLabel("Benefits Breakdown", GlobalVariables.LanguageID)</strong>
                    </h3>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>@FieldTranslation.GetLabel("Benefit Code", GlobalVariables.LanguageID)</th>
                                <th>@FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)</th>
                                <th>@FieldTranslation.GetLabel("YTD Amount", GlobalVariables.LanguageID)</th>
                            </tr>
                        </thead>

                        @foreach (EmployeeCodes item in Model.allBenefits)
                        {
                            <tr>
                                <td>
                                    @item.Code
                                </td>
                                <td>
                                    @item.Description
                                </td>
                                <td>
                                    @String.Format("{0:c}", item.Additional)
                                </td>
                            </tr>
                        }
                    </table>
                </div>
            </div>

        </div>
    </div>






}
<script>
    $(window).resize(function () {
        var chart = $("#chart").data("kendoChart");
        var line = $("#line").data("kendoChart");
        chart.redraw();
        line.redraw();
    });
</script>