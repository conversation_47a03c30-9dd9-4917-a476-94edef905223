@model ClientContacts
@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DataDrivenViewEngine.Models.Core;


@{
    //Layout = null;
    ViewBag.Title = "Add New Contact";
    ViewBag.ParentCrumb = "Company,Information";
    //Breadcrumb Requiredness
    //var profileid = Url.RequestContext.RouteData.Values["id"];
    //var taskid = HttpContext.Current.Request.QueryString["tid"];
    ViewBag.CrumbName = "Contacts";
    ViewBag.Crumb = Url.Action("Contacts", "CompanyInformation");
    //Breadcrumb End

}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{


    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                <div class="colored-line-left"></div>
            </div>

        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            @*<h2>Edit Contact</h2>*@

            @using (Html.BeginForm("EditContact", "CompanyInformation", FormMethod.Post, new { id = "companyInfoEdit", enctype = "multipart/form-data" }))
            {
                @Html.AntiForgeryToken()

                <div class="form-horizontal">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    @Html.HiddenFor(model => model.Id)
                    <input type="hidden" id="CompanyID" name="CompanyID" value="@GlobalVariables.CompanyID" />
                    <input type="hidden" id="ClientID" name="ClientID" value="@GlobalVariables.Client" />

                    <div class="form-group">
                        @Html.LabelFor(model => model.ContactType, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("ContactType", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            <select id="ContactType" name="ContactType" class="form-control field-required" required>
                                @if (ViewBag.ContactTypes != null)
                                {
                                    foreach (ClientContactTypes contactType in ViewBag.ContactTypes)
                                    {
                                        if (Model.ContactType == contactType.ContactType)
                                        {
                                            <option value="@contactType.ContactType" selected>@contactType.ContactType</option>
                                        }
                                        else
                                        {
                                            <option value="@contactType.ContactType">@contactType.ContactType</option>
                                        }
                                    }
                                }
                            </select>
                            @Html.ValidationMessageFor(model => model.ContactType, "", new { @class = "text-danger" })
                        </div>

                        @Html.LabelFor(model => model.ContactName, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("ContactName", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            @Html.TextBoxFor(model => model.ContactName, new { @class = "form-control field-required", @maxlength = "255", @required = "required" })
                            @Html.ValidationMessageFor(model => model.ContactName, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.Phone1, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Phone1", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            @Html.TextBoxFor(model => model.Phone1, new { @class = "form-control phone-type-masking", @maxlength = "15" })
                            @Html.ValidationMessageFor(model => model.Phone1, "", new { @class = "text-danger" })
                        </div>
                        @Html.LabelFor(model => model.Phone2, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Phone2", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            @Html.TextBoxFor(model => model.Phone2, new { @class = "form-control phone-type-masking", @maxlength = "15" })
                            @Html.ValidationMessageFor(model => model.Phone2, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            @Html.TextBoxFor(model => model.Email, new { @class = "form-control", @maxlength = "255" })
                            @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                        </div>
                        @Html.LabelFor(model => model.Website, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Website", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            @Html.TextBoxFor(model => model.Website, new { @class = "form-control", @maxlength = "255" })
                            @Html.ValidationMessageFor(model => model.Website, "", new { @class = "text-danger" })
                        </div>

                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.NotAnEmployee, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("NotAnEmployee", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            <div class="checkbox" style="padding-left: 20px;">
                                @Html.CheckBoxFor(model => model.NotAnEmployee)
                                @Html.ValidationMessageFor(model => model.NotAnEmployee, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.EmployeeID, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            <select id="EmployeeID" name="EmployeeID" class="form-control">
                                <option value=""></option>
                                @if (ViewBag.AvailableEmployees != null)
                                {
                                    foreach (Code_Description ee in ViewBag.AvailableEmployees)
                                    {
                                        <option value="@ee.Code" @((Model.EmployeeID == ee.Code) ? "selected" : string.Empty)>@ee.Description</option>
                                    }
                                }
                            </select>
                            @*@Html.TextBoxFor(model => model.EmployeeID, new {@class = "form-control", @maxlength = "15"})*@
                            @Html.ValidationMessageFor(model => model.EmployeeID, "", new { @class = "text-danger" })
                        </div>
                        @Html.LabelFor(model => model.Department, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            <select id="Department" name="Department" class="form-control">
                                <option value=""></option>
                                @if (ViewBag.AvailableDepartments != null)
                                {
                                    foreach (Code_Description department in ViewBag.AvailableDepartments)
                                    {
                                        <option value="@department.Code" @((Model.Department == department.Code) ? "selected" : string.Empty)>@department.Description</option>
                                    }
                                }
                            </select>
                            @*@Html.EditorFor(model => model.Department, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.ValidationMessageFor(model => model.Department, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Image, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Picture", GlobalVariables.LanguageID))
                        <div class="col-md-4">
                            <input type="file" id="file" name="file" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-12">
                            <div class="pull-right">
                                <a href="@Url.Action("Contacts", "CompanyInformation")" class="btn btn-thinkware">@FieldTranslation.GetLabel("Cancel", GlobalVariables.LanguageID)</a>
                                <input type="submit" id="submitContact" value="@FieldTranslation.GetLabel("Save", GlobalVariables.LanguageID)" class="btn btn-thinkware" />
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>


    <script>
        $("#NotAnEmployee").change(function () {
            setFields();
        });
        function setFields() {
            var empChecked = $("#NotAnEmployee").is(":checked");
            if (empChecked) {
                $("#Department").attr("disabled", "disabled");
                $("#Department").val('');
                $("#EmployeeID").attr("disabled", "disabled");
                $("#EmployeeID").val('');
                $("#file").removeAttr("disabled", "disabled");
            }
            if (!empChecked) {
                $("#file").attr("disabled", "disabled");
                $("#file").val('');
                $("#Department").removeAttr("disabled", "disabled");
                $("#EmployeeID").removeAttr("disabled", "disabled");
            }
        }
    </script>

    <script src="~/Scripts/MaskedInput.js"></script>
    <script>
        $(document).ready(function () {
            //$("#PhoneHome").kendoMaskedTextBox({
            //    mask: "(*************",
            //    unmaskedOnPost: false
            //});

            $(".phone-type-masking").mask("(*************");
            setFields();

        });
        /*$("#submitContact").click(function () {
            $("#companyInfoEdit").submit(function () {
                $(".phone-type-masking").mask("*********9");
            });
        });*/
    </script>
    <script>
        var form = $("#companyInfoEdit");
        form.validate({
            rules: {
                //SocialSecNumber:{
                //    required: true,
                //    minlength: 9,
                //    maxlength:11
                //},
                PercentRate: {
                    required: true,
                    max: 100,
                    min: 0.01
                },
                Amount: {
                    required: true,
                    min: 0.01
                }
            },
            messages: {
                SocialSecNumber: "Please enter a valid Social Security Number",
                PercentRate: "Percentage rate must be between 1 and 100",
                Amount: "Deposit Amount must be greater than 0"
            }
        });
        jQuery.validator.addClassRules("ssn-type-required", {
            required: true,
            minlength: 9,
            maxlength: 11
        });
        $("input[type=submit]").click(function () {
            if (form.valid() == true) {
                $("#companyInfoEdit").submit(function () {
                    $('#SocialSecNumber').mask('*********');
                    $('.ssn-type').mask('*********');
                    $('.ssn-type-required').mask('*********');
                    $("#PhoneHome").mask("*********9");
                    $(".phone-type-masking").mask("*********9");
                });
            }
        });
        @*$("#companyInfoEdit").submit(function (e) {
        e.preventDefault();
        $(".phone-type-masking").mask("*********9");
        var formData = new FormData($(this)[0]);
        var url2 = '@Url.Action("EditContact", "CompanyInformation")';
        $.ajax({
            url: url2,
            type: 'POST',
            data: formData,
            async: false,
            cache: false,
            contentType: false,
            processData: false,
            success: function () {
                window.location.href = "@Url.Action("Contacts", "CompanyInformation")"
            },
            error: function () {
                alert("An error occurred please try again.");
            }
        });
    });*@
    </script>

}
@if (ViewBag.Access == MenuAccessLevel.ReadOnly)
{
    <script>
        $(document).ready(function () {
            $('#companyInfoEdit input').attr('disabled', true);
            $('#companyInfoEdit select').attr('disabled', true);
        })
    </script>
}