@using DarwiNet2._0.Core;

<style type="text/css" id="payroll-cockpit-filters-modal-style" scoped>
    .alignRight {
        text-align: right;
    }

    .modal-mask {
        position: fixed;
        z-index: 9998;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: table;
        transition: opacity 0.3s ease;
    }

    .modal-wrapper {
        display: table-cell;
        vertical-align: middle;
    }

    .filters-modal-container {
        width: 20%;
        margin: 0px auto;
        background-color: #fff;
        border-radius: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
        transition: all 0.3s ease;
        font-family: Helvetica, Arial, sans-serif;
    }

    .modal-body {
        width: 100%;
        max-height: calc(100vh - 275px);
        overflow-y: auto;
        margin: 0 !important;
        padding: 0 !important;
    }

    .modal-header {
        padding: 0 !important;
    }

    .modal-enter {
        opacity: 0;
    }

    .modal-leave-active {
        opacity: 0;
    }

        .modal-enter .filters-modal-container,
        .modal-leave-active .filters-modal-container {
            -webkit-transform: scale(1.1);
            transform: scale(1.1);
        }

    .fieldError {
        border-color: red;
    }
</style>

<script type="text/x-template" id="payroll-cockpit-filters-modal-template">
    <transition name="modal">
        <div class="modal-mask" tabindex="0" @@keydown.escape="escapePressed" id="PayrollCockpitFiltersModal">
            <div class="modal-wrapper">
                <div class="filters-modal-container">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <table style="width: 100%">
                                <tr>
                                    <td>
                                        <strong>Filters</strong>
                                    </td>
                                    <td class="text-right" style="vertical-align: middle">
                                        <div style="display: inline-flex;">
                                            <button type="button" class="custom-action-button" title="Add New Filter" style="--color: #449d44" @@click="addNewFilter = !addNewFilter">
                                                <i class="fa fa-plus"></i>
                                            </button>
                                            &nbsp;
                                            <button type="button" class="close" title="Close" @@click="escapePressed()">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="panel-body">
                            <div class="modal-body">
                                <table class="table table-hover table-condensed" style="margin-bottom: 0px !important">
                                    <tbody>
                                        <tr v-if="savedFilters.length == 0 && !addNewFilter"><td colspan="2" style="text-align:center">No saved filters for this table.</td></tr>
                                        <tr v-if="addNewFilter">
                                            <td>
                                                <input type="text" class="form-control" placeholder="Name your filter..." v-model="newFilterName" maxlength="100" />
                                            </td>
                                            <td class="text-right" style="vertical-align: middle">
                                                <div style="display: inline-flex;">
                                                    <button type="button" class="custom-action-button" :disabled="newFilterName.length < 1" title="Save New Filter" style="--color: #449d44" 
                                                        @@click="loading ? null : saveChanges(newFilterName)">
                                                        <i :class="[loading ? 'fa fa-circle-o-notch rotating' : 'fa fa-check']"></i>
                                                    </button>
                                                    &nbsp;
                                                    <button type="button" class="custom-action-button" :disabled="loading" title="Cancel" style="--color: #c9302c" @@click="addNewFilter = !addNewFilter">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr v-for="(filter, index) in savedFilters">
                                            <td class="truncated-table-cell" style="vertical-align: middle;" :title="filter.Name">
                                                <i v-if="processing === filter.Name" class="fa fa-circle-o-notch rotating"></i>&nbsp;{{filter.Name}}
                                            </td>
                                            <td class="text-right" style="vertical-align: middle">
                                                <div style="display: inline-flex;">
                                                    <button type="button" class="custom-action-button" :disabled="loading" title="Replace Saved Filter with Current Filter Settings" style="--color: #0095ff" @@click="loading ? null: saveChanges(filter.Name)">
                                                        <i class="fa fa-refresh"></i>
                                                    </button>
                                                    &nbsp;
                                                    <button type="button" class="custom-action-button" :disabled="loading" title="Delete this Saved Filter" style="--color: #c9302c" @@click="deleteFilter(filter.Name)">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="modal-footer">
                                <button :class="['btn', loading ? 'btn-bg' : 'btn-thinkware-close']" v-on:click="loading ? null : escapePressed()">
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</script>

<script type="text/javascript" id="payroll-cockpit-filters-modal-script">
    var PayrollCockpitFiltersModalComponent = VueComponent('payroll-cockpit-filters-modal', {
        props: {
            savedFilters: {
                type: Array,
                default: () => ([])
            },
            filter: {
                type: Object,
                default: () => ({})
            }
        },
        data: function () {
            return {
                loading: false,
                addNewFilter: false,
                newFilterName: "",
                processing: "",
                events: {
                    escapePressed: "escape-pressed"
                }
            };
        },
        computed: {

        },
        mounted: function () {
            var modal = document.getElementById("PayrollCockpitFiltersModal");
            modal.focus();
        },
        methods: {
            escapePressed: function () {
                this.$emit(this.events.escapePressed);
            },
            saveChanges: function (name) {
                this.loading = true;
                this.processing = name;

                var self = this;

                var newFilter = {
                    filterTableId: @FilterTableConstants.PayrollCockpit,
                    referenceId: name,
                    filter: this.filter,
                };

                var updating = this.savedFilters.some(f => f.Name == name);

                ThinkwareCommon.ajax.postJson('@Url.Action("SaveOrUpdate", "UserTableFilter")',
                {
                    data: newFilter,
                    onSuccess: function (result)
                    {
                        if (result.status === 'Success')
                        {
                            self.addNewFilter = false;
                            self.newFilterName = "";

                            if (!updating) {
                                self.savedFilters.push({ Name: name, Filter: JSON.parse(JSON.stringify(newFilter.filter)) });
                            }
                        }
                        else
                        {
                            ThinkwareCommon.showAlert('danger', result.message);
                        }
                        self.loading = false;
                        self.processing = "";
                    },
                    onError: function (jqXHR, textStatus, errorThrown)
                    {
                        ThinkwareCommon.showAlert('danger', errorThrown);
                        self.loading = false;
                        self.processing = "";
                    }
                });

            },

            deleteFilter: function (name) {
                this.loading = true;

                var self = this;

                this.processing = name;

                var deletedFilter = {
                    filterTableId: @FilterTableConstants.PayrollCockpit,
                    referenceId: name,
                };

                var index = this.savedFilters.findIndex(f => f.Name === name);

                ThinkwareCommon.ajax.postJson('@Url.Action("Delete", "UserTableFilter")',
                {
                    data: deletedFilter,
                    onSuccess: function (result)
                    {
                        if (result.status === 'Success')
                        {
                            self.savedFilters.splice(index, 1);
                        }
                        else
                        {
                            ThinkwareCommon.showAlert('danger', result.message);
                        }
                        self.loading = false;
                        self.processing = "";
                    },
                    onError: function (jqXHR, textStatus, errorThrown)
                    {
                        ThinkwareCommon.showAlert('danger', errorThrown);
                        self.loading = false;
                        self.processing = "";
                    }
                });

            },
        },
        components: [
        ],
    });

</script>
