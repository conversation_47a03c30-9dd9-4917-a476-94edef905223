@using Thinkware.Cohesion.Payroll;

<template id="payroll-settings-responsible-template">
    <div class="panel panel-thinkware">
        <div class="panel-heading">Responsible</div>
        <div class="panel-body">
            <div v-for="setting in settings">
                <div class="row padding-bottom-15">
                    <div class="col-sm-12">
                        <label :for="setting.Name">{{setting.DisplayText}}</label>
                        <i class="fa-sharp fa-solid fa-circle-question"
                           data-toggle="tooltip"
                           data-placement="top"
                           :title="setting.Description"></i>
                        <select v-model="setting.Value" :id="setting.Name" class="form-control">
                            <option value=""></option>
                            <option v-for="item in users" :value="item.value">{{item.text}}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script id="payroll-settings-responsible-script">
    const PayrollSettingsResponsibleComponent = VueComponent('payroll-settings-responsible', {
        props: {
            value: Object
        },
        computed: {
            settings(self) {
                return [
                    self.value['@SettingNames.Responsible'],
                    self.value['@SettingNames.InvoiceProcessAssigned'],
                    self.value['@SettingNames.FinalizeAssigned']
                ];
            },
            users(self) {
                return self.value['@SettingNames.Users'].Value
            }
        },
        watch: {
            value: {
                deep: true,
                handler(newVal) {
                    this.$emit('input', newVal);
                }
            }
        }
    });
</script>

<style scoped>
</style>