@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DataDrivenViewEngine.Models.Core
@model IEnumerable<DarwiNet2._0.Data.EETotalComp>

@{
    ViewBag.Title = "Total Burden by Employee";
    ViewBag.ParentCrumb = "Payroll,Invoices";
}


<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
    </div>
</div>
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <script>
        $(function() {
            // bind change event to select
            $('#sel_year').on('change', function(evt) {
                var url = "@Url.Action("TotalBurden", "PayrollInvoices")";
                url = url + "?y=" + $(this).val();
                window.location.href = url;
            });
        });
    </script>



    <script>

        $(function() {
            $('#toggle_view').click(function() {
                var $this = $(this);
                $this.toggleClass('showFilter');
                $('#filterdiv').toggle('slow');
                //$('#tbl_401k').toggle();
                if ($this.hasClass('showFilter')) {
                    $this.text('Hide Filters');
                } else {
                    $this.text('Advanced Filters');
                }
            });
        });
    </script>

@*<div id="filterdiv" class="row" style="padding-bottom: 15px; display: none;">
        <div style="padding-top: 15px; border: solid 1px #ccc; padding: 10px;">
            <div class="row">
                @Html.Partial("~/Views/PayrollInvoices/_Filter.cshtml")
            </div>
        </div>
    </div>*@
    <div class="row" style="padding-top: 15px;">
        <div class="col-md-12">
            <div class="col-md-6">
                <div id="chart"></div>
                <script>
                    function createChart() {
                        $("#chart").kendoChart({
                            title: {
                                position: "bottom",
                                text: "Total Burden for @ViewBag.SelectedYear"
                            },
                            legend: {
                                position: "bottom",
                                visible: true
                            },
                            chartArea: {
                                background: ""
                            },
                            seriesDefaults: {
                                type: "donut",
                                startAngle: 150
                            },
                            series: [
                                {
                                    name: "Total Burden",
                                    data: [
                                        {
                                            category: "Total Gross",
                                            value: @ViewBag.TotalGross,
                                            color: "#59ABE3"
                                        }, {
                                            category: "Total Benefits",
                                            value: @ViewBag.TotalBenefits,
                                            color: "#D91E18"
                                        }, {
                                            category: "Total Taxes",
                                            value: @ViewBag.TotalTaxes,
                                            color: "#87D37C"
                                        }, {
                                            category: "Total Fees",
                                            value: @ViewBag.TotalFees,
                                            color: "#9B59B6"
                                        }
                                    ]
                                }
                            ],
                            tooltip: {
                                visible: true,
                                template: "#= category # : #= kendo.toString(value,'c2') #"
                            }
                        });
                    }

                    $(document).ready(createChart);
                    $(document).bind("kendo:skinChange", createChart);
                </script>
            </div>
            <div class="col-md-6">
                <div style="padding-top: 25px;">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Total Burden</h3>
                        </div>
                        <div class="panel-body">
                            <div class="">
                                <table class="table table-striped table-bordered">
                                    <tr>
                                        <th>Type</th>
                                        <th style="text-align: right">Total</th>
                                    </tr>
                                    <tr>
                                        <td>Gross Wages</td>
                                        <td style="text-align: right">@String.Format("{0:c2}", ViewBag.TotalGross)</td>
                                    </tr>
                                    <tr>
                                        <td>Benefits</td>
                                        <td style="text-align: right">@String.Format("{0:c2}", ViewBag.TotalBenefits)</td>
                                    </tr>
                                    <tr>
                                        <td>Employer Taxes</td>
                                        <td style="text-align: right">@String.Format("{0:c2}", ViewBag.TotalTaxes)</td>
                                    </tr>
                                    <tr>
                                        <td>Fees</td>
                                        <td style="text-align: right">@String.Format("{0:c2}", ViewBag.TotalFees)</td>
                                    </tr>
                                    <tr>
                                        <td>Total Burden</td>
                                        <td style="text-align: right">@String.Format("{0:c2}", ViewBag.AllTotal)</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="contacts-toolbar">
                <div class="toolbar" style="padding-bottom: 15px;">
                    <div class="row">
                        <div class="col-md-3 pull-left">
                            <div class="input-group">
                                <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                                <input type="text" class="form-control" id='FieldFilter' placeholder="Search Employee Name">
                            </div>
                        </div>
                        @*<div class="row">*@
                        <div class="col-md-6">
                            <select id="sel_year" name="sel_year" class="form-control">
                                @foreach (var y in ViewBag.Years)
                                {
                                    <option value="@y" @((ViewBag.SelectedYear.ToString() == y.ToString()) ? "selected" : string.Empty)>@y</option>
                                }
                            </select>
                        </div>
                        @*</div>*@
                        @*<div class="col-md-3">
                            <a href="javascript:void(0)" id="toggle_view">Advanced Filters</a>
                        </div>*@
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <table class="table table-bordered" id="totalburden-table">
                        <thead>
                        <tr>
                            <th>Employee Name</th>
                            <th>Employee ID</th>
                            <th>Gross Wages</th>
                            <th>Benefits</th>
                            <th>Taxes</th>
                            <th>Fees</th>
                            <th>Total Burden</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.EmployeeName</td>
                                <td>@FieldTranslation.EmployeeIDFormatted(item.EmployeeID)</td>
                                <td>@String.Format("{0:c2}", item.totalAnnualWages)</td>
                                <td>@String.Format("{0:c2}", item.totalBenefits)</td>
                                <td>@String.Format("{0:c2}", item.totalTaxes)</td>
                                <td>@String.Format("{0:c2}", item.totalFees)</td>
                                <td>@String.Format("{0:c2}", item.totalBurden)</td>
                            </tr>
                        }

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
}
        @section scripts{

            <script>
                $(document).ready(function() {
                    var grid = $("#totalburden-table").kendoGrid({
                        toolbar: ["excel"],
                        excel: {
                            fileName: "TotalBurden.xlsx",
                            filterable: true,
                            allPages: true
                        },
                        dataSource: {
                            pageSize: 15
                        },
                        sortable: true,
                        pageable: true,
                        filterable: true,
                        groupable: true,
                        scrollable: false,
                        resizable: true
                    }).data("kendoGrid");


                    $("#FieldFilter").keyup(function() {

                        var value = $("#FieldFilter").val();
                        var grid = $("#totalburden-table").data("kendoGrid");

                        if (value) {
                            grid.dataSource.filter({
                                logic: "or",
                                filters: [
                                    { field: "EmployeeName", operator: "contains", value: value }
                                ]
                            })
                        } else {
                            grid.dataSource.filter({});
                        }
                    });

                });
                $(window).resize(function () {
                    var chart = $("#chart").data("kendoChart");
                    chart.redraw();
                });
                $(document).ready(function() {
                    setTimeout(function() {
                        var chart = $("#chart").data("kendoChart");
                        chart.redraw();
                    }, 100);
                });
                $(document).on('click', '.sidebar-toggle', function () {
                    setTimeout(function () {
                        var chart = $("#chart").data("kendoChart");
                        chart.redraw();
                    }, 300);
                });
            </script>

        }
