@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Data;
@using DataDrivenViewEngine.Models.Core
@*@using OBSignatureType = DataDrivenViewEngine.Models.Core.enOBSignatureType
    @using OBBankPresentation = DataDrivenViewEngine.Models.Core.enOBBankPresentation
    @using OBSortOptions = DataDrivenViewEngine.Models.Core.enOBSortOptions*@

@model DarwiNet2._0.Data.Invoice
@{
    ViewBag.Title = "Current Invoice";
    ViewBag.ParentCrumb = "Payroll,Invoices";
}

<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.0.1/css/toastr.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.0.1/js/toastr.js"></script>

<style type="text/css">
    .custom-toggle-button {
        font-size: 21px;
        font-weight: 700;
        line-height: 1;
        text-shadow: 0 1px 0 #fff;
        filter: alpha(opacity=60);
        opacity: .6;
        -webkit-appearance: none;
        padding: 0;
        cursor: pointer;
        background: 0 0;
        border: 0;
    }

        .custom-toggle-button:focus, .custom-toggle-button:hover {
            text-decoration: none;
            cursor: pointer;
            filter: alpha(opacity=80);
            opacity: .8
        }

    .active-toggle-color {
        color: #449d44;
    }

    .toggle-label {
        width: 100px;
    }

    .modal-lg-width {
        width: 75vw;
    }

    .pd {
        padding: 0 30px 15px 30px;
    }
</style>

<script type="text/javascript">

    // mini jQuery plugin that formats to two decimal places
    (function ($) {
        $.fn.currencyFormat = function () {
            this.each(function (i) {
                $(this).change(function (e) {
                    if (isNaN(parseFloat(this.value))) return;
                    this.value = parseFloat(this.value).toFixed(2);
                });
            });
            return this; //for chaining
        }
    })(jQuery);

    // apply the currencyFormat behavior
    //$(function () {
    //    $('#currency').currencyFormat("{0:n2}%");
    //});
    $(function () {
        $('#curreny').currencyFormat("${0:n2}");
    });
    $('#currency').keypress(function (event) {
        var $this = $(this);
        if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
            ((event.which < 48 || event.which > 57) &&
                (event.which != 0 && event.which != 8))) {
            event.preventDefault();
        }

        var text = $(this).val();
        if ((event.which == 46) && (text.indexOf('.') == -1)) {
            setTimeout(function () {
                if ($this.val().substring($this.val().indexOf('.')).length > 3) {
                    $this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
                }
            }, 1);
        }

        if ((text.indexOf('.') != -1) &&
            (text.substring(text.indexOf('.')).length > 2) &&
            (event.which != 0 && event.which != 8) &&
            ($(this)[0].selectionStart >= text.length - 2)) {
            event.preventDefault();
        }
    });
</script>
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else if (ViewBag.NoInvoice == true)
{
    <div class="alert alert-danger alert-dismissible" role="alert" id="globalFail">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <p>No current invoice has been found to display.</p>
    </div>
}
else
{
    <div class="row" id="invoices">
        <div class="">
            <p style="font-size: 22px;">Current Invoice # @Model.DarwinInvoiceNumber - @FieldTranslation.ToShortDate(Model.Date.ToString())</p>
            <div class="colored-line-left"></div>
            @if (!ViewBag.DarwinInvoice)
            {
                <div class="row">
                    <div class="col-md-12 pd">
                        <button class="btn btn-thinkware" data-toggle="modal" data-target="#agencyModal">Agency Checks/Credits</button>
                        <button class="btn btn-thinkware" data-toggle="modal" data-target="#recaptureModal">Recapture</button>
                    </div>
                </div>
            }
            <div class="col-md-8">
                <div class="row">
                    <div class="col-xs-12 col-md-6">
                        <div class="panel panel-thinkware">
                            <div class="panel-heading">
                                <a href="" class="invoice-link" data-url='@Url.Action("Wages", "PayrollInvoices")' style="text-decoration: underline">Wages</a>
                                <a href='@Url.Action("PayCodes", "PayrollInvoices", new { i = @Model.DarwinInvoiceNumber })' class="pull-right" style="text-decoration: underline" data-toggle="modal" data-target="#payCodesModal">Pay Codes</a>
                            </div>
                            <div class="panel-body height">
                                <span class="invoice-sub-type">Gross</span> <span class="pull-right">
                                    @Html.FormatValue(ViewBag.GrossWages, "{0:C}")
                                </span><br>
                                @if (@Html.FormatValue(ViewBag.GrossWagesCredit, "{0:C}") != "$0.00")
                                {
                                    <span class="invoice-sub-type">Gross Wages - Credit</span><span class="pull-right">
                                        @Html.FormatValue(ViewBag.GrossWagesCredit, "{0:C}")
                                    </span><br>
                                }
                                @if (@Html.FormatValue(ViewBag.GrossWagesCredit, "{0:C}") != "$0.00")
                                {
                                    <span class="invoice-sub-type">Non-Gross</span><span class="pull-right">
                                        @Html.FormatValue(ViewBag.NonGrossWages, "{0:C}")
                                    </span><br>
                                }

                                <span class="invoice-sub-type">Net</span> <span class="pull-right">@Html.FormatValue(Model.NetWagesPayRun, "{0:C}")</span><br>
                            </div>
                            <div class="panel-footer">
                                <span>Total</span> <span class="pull-right">@Html.FormatValue(ViewBag.WagesTotal, "{0:C}")</span>
                            </div>
                        </div>
                    </div>
                    @if (!ViewBag.MultiCharge)
                    {
                        if (!ViewBag.HideAll)
                        {
                            <div class="col-xs-12 col-md-6">
                                <div class="panel panel-thinkware" id="GetInvoiceTaxes">
                                    <div class="panel-heading">
                                        <span class="invoice">Taxes</span>
                                    </div>
                                    <div class="panel-body height">
                                        <div class="text-center loading-spinner-check">
                                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                                        </div>
                                    </div>
                                    <div class="panel-footer">
                                        <span>Total</span>

                                    </div>
                                </div>
                            </div>
                        }
                    }
                    <div class="col-xs-12 col-md-6">
                        <div class="panel panel-thinkware" id="GetInvoiceBenefits">
                            <div class="panel-heading">
                                <span class="invoice">Benefits</span>
                            </div>
                            <div class="panel-body height">
                                <div class="text-center loading-spinner-check">
                                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <span>Total</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 @( (ViewBag.MultiCharge || ViewBag.HideAll) ? "col-md-12" : "col-md-6")">
                        <div class="panel panel-thinkware" id="GetInvoiceFees">
                            <div class="panel-heading">
                                <a href="" class="invoice-link" data-url='@Url.Action("Fees", "PayrollInvoices")' style="text-decoration: underline">Fees</a>

                            </div>
                            <div class="panel-body height">
                                <div class="text-center loading-spinner-check">
                                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                                </div>
                            </div>
                            <div class="panel-footer">
                                <span>Total</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="">
                        <div class="col-xs-12 hidden-md col-lg-1"></div>
                        <div class="col-lg-2 col-md-6 col-sm-6">
                            <div id="loading" style="padding-top: 15px; display: none;">
                                <i style="color: black;" class=" fa fa-spinner fa-spin fa-4x"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="">
                        <div class="col-md-12 col-sm-6">
                            <div id="partialDiv"></div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-md-4" style="border: solid 1px #ccc">
                <div class="current-invoice-content">
                    <p style="font-size: 22px;">Invoice Details</p>
                    <div class="colored-line-left"></div>
                    <div class="select-center">
                        <select id="select-invoice" class="form-control">
                            <option value="0">-- ALL --</option>
                            @if (ViewBag.theInvoices != null)
                            {
                                foreach (Code_Description invoice in ViewBag.theInvoices)
                                {
                                    if (Convert.ToInt32(invoice.Code) == Model.DarwinInvoiceNumber)
                                    {
                                        <option value="@invoice.Code" selected>@invoice.Description</option>
                                    }
                                    else
                                    {
                                        <option value="@invoice.Code">@invoice.Description</option>
                                    }
                                    @*<option value="@role.Code">@role.Description</option>*@
                                }
                            }
                        </select>
                    </div>
                    <div class="current-invoice-list">
                        <ul class="list-unstyled inner">
                            <li><strong>Invoice #</strong><span class="pull-right">@Model.DarwinInvoiceNumber</span></li>
                            <li><strong>Division</strong><span class="pull-right">@Model.DivisionID</span></li>
                            <li><strong>Invoice Date</strong><span class="pull-right"> @FieldTranslation.ToShortDate(Model.Date.ToString())</span></li>
                            <li><strong>Check Date</strong> <span class="pull-right">@FieldTranslation.ToShortDate(Model.CheckDate.ToString())</span></li>
                            <li><strong>Pay Period Begins</strong><span class="pull-right"> @FieldTranslation.ToShortDate(Model.StartDate.ToString())</span></li>
                            <li><strong>Pay Period Ends</strong> <span class="pull-right">@FieldTranslation.ToShortDate(Model.EndDate.ToString())</span></li>
                            <li><strong>Invoice Total</strong> <span class="pull-right">@String.Format("{0:c}", Model.GrandTotal)</span></li>
                            <li><strong>Applied</strong> <span class="pull-right">@String.Format("{0:c}", @ViewBag.TotalPaid)</span></li>
                            <li>
                                <strong><a href="" class="invoice-link" data-url='@Url.Action("Checks", "PayrollInvoices")' style="text-decoration: underline">Checks</a></strong> <span class="pull-right">@ViewBag.CheckCount</span>

                            </li>
                            @*<li><strong>EE Paid</strong> <span class="pull-right">@Model.Employees</span></li>*@
                        </ul>
                    </div>
                    <div>
                        <p style="font-size: 22px;">Reports</p>
                        <div class="colored-line-left"></div>
                        <div class="select-center">
                            <select class="form-control" id="report-select">
                                <option value="" selected>Choose a Report</option>
                                @foreach (var item in ViewBag.AvailableReports)
                                {
                                    <option value="@item.Code">@item.Description</option>
                                }
                            </select>
                        </div>
                        <div>
                            <p style="font-size: 22px;">Invoice Comments</p>
                            <div class="colored-line-left"></div>
                            <div name="comments" class="well invoice-current-comments">
                                <ul class="list-unstyled">
                                    <li>
                                        @Model.CommentArray1&nbsp;
                                        @Model.CommentArray2&nbsp;
                                        @Model.CommentArray3&nbsp;
                                        @Model.CommentArray4
                                    </li>
                                    <li>@Model.Comment1</li>
                                    <li>@Model.Comment2</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>

        <!-- Modal -->
        <div class="modal fade" id="payCodesModal" tabindex="-1" role="dialog" aria-labelledby="payCodesModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="payCodesModalLabel">Pay Codes</h4>
                    </div>
                    <div class="modal-body">
                        Generating...
                    </div>
                </div>
            </div>
        </div>

        <!-- Agency checks/credits modal -->
        <div class="modal fade" id="agencyModal" tabindex="-1" role="dialog" aria-labelledby="agencyModalLabel">
            <div class="modal-dialog modal-lg modal-lg-width" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="agencyModalLabel">Agency Checks/Credits</h4>
                    </div>
                    <div class="modal-body">
                        <!-- Vue agency script template -->
                        <div id="agencyTemplate">
                            <div class="row">
                                <div class="form-group">
                                    <div class="input-row col-md-12">
                                        <div class="col-md-6">
                                            <label class="control-label toggle-label">Deductions</label>
                                            <button class="custom-toggle-button">
                                                <i :class="isButtonActiveClass('deductions')" @@click="toggleFilter('deductions')"></i>
                                            </button>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="control-label toggle-label">Checks</label>
                                            <button class="custom-toggle-button">
                                                <i :class="isButtonActiveClass('checks')" @@click="toggleFilter('checks')"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="input-row col-md-12">
                                        <div class="col-md-6">
                                            <label class="control-label toggle-label">Benefits</label>
                                            <button class="custom-toggle-button">
                                                <i :class="isButtonActiveClass('benefits')" @@click="toggleFilter('benefits')"></i>
                                            </button>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="control-label toggle-label">Credits</label>
                                            <button class="custom-toggle-button">
                                                <i :class="isButtonActiveClass('credits')" @@click="toggleFilter('credits')"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>EmployeeID</th>
                                                <th>Employee</th>
                                                <th>Code Type</th>
                                                <th>Code</th>
                                                <th class="text-right">Amount</th>
                                                <th class="text-right">EE Amount</th>
                                                <th>Agency Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="a in agencyCreditHistoriesFiltered">
                                                <td>{{ a.EmployeeID }}</td>
                                                <td>{{ a.EmployeeName }}</td>
                                                <td>{{ a.CodeType }}</td>
                                                <td>{{ a.Code }}</td>
                                                <td class="text-right">{{ formatCurrency(a.Amount) }}</td>
                                                <td class="text-right">{{ formatCurrency(a.EEAmount) }}</td>
                                                <td>{{ a.AgencyType }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recapture modal -->
        <div class="modal fade" id="recaptureModal" tabindex="-1" role="dialog" aria-labelledby="recaptureModalLabel">
            <div class="modal-dialog modal-lg modal-lg-width" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="recaptureModalLabel">Recapture / Missed Codes</h4>
                    </div>
                    <div class="modal-body">
                        <!-- Vue agency script template -->
                        <div id="recaptureTemplate">
                            <div class="row">
                                <div class="col-md-12">
                                    <table class="table table-striped table-bordered">
                                        <thead>
                                            <tr>
                                                <th>EmployeeID</th>
                                                <th>Employee</th>
                                                <th>Type</th>
                                                <th>Code</th>
                                                <th class="text-right">Expected</th>
                                                <th class="text-right">Taken</th>
                                                <th class="text-right">Recover</th>
                                                <th>Times</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="a in missedCodes">
                                                <td>{{ a.EmployeeID }}</td>
                                                <td>{{ a.FullName }}</td>
                                                <td>{{ a.PayrollRecordTypeString }}</td>
                                                <td>{{ a.PayrollCode }}</td>
                                                <td class="text-right">{{ formatCurrency(a.AttemptedAmount) }}</td>
                                                <td class="text-right">{{ formatCurrency(a.Amount) }}</td>
                                                <td class="text-right">{{ formatCurrency(a.RecoverAmount) }}</td>
                                                <td>{{ a.TimeRemaining }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    @section scripts{
        <!-- Recapture vue scripting -->
        <script>
            var recaptureApp = new Vue({
                // Unique ID from the div
                el: '#recaptureTemplate',
                data: {
                    missedCodes: @Html.Raw(Json.Encode(ViewBag.MissedCodes)),
                },
                methods: {
                    formatCurrency: function(number) {
                        var formatter = new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                        });

                        return formatter.format(number);
                    },
                },
                watch: {
                },
            })
        </script>

        <!-- Agency vue scripting -->
        <script>
            var agencyApp = new Vue({
                // Unique ID from the div
                el: '#agencyTemplate',
                data: {
                    agencyCreditHistories: @Html.Raw(Json.Encode(ViewBag.AgencyData)),
                    agencyCreditHistoriesFiltered: @Html.Raw(Json.Encode(ViewBag.AgencyData)),
                    filters: {
                        deductions: true,
                        benefits: true,
                        checks: true,
                        credits: true
                    }
                },
                methods: {
                    formatCurrency: function(number) {
                        var formatter = new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                        });

                        return formatter.format(number);
                    },

                    isButtonActiveClass: function(val) {
                        if (this.filters[val]) {
                            return 'fa fa-lg fa-toggle-on active-toggle-color';
                        }

                        return 'fa fa-lg fa-toggle-off';
                    },

                    toggleFilter: function(val) {
                        this.filters[val] = !this.filters[val];
                    },

                    filter: function() {
                        // Reset filters to reapply filters
                        this.agencyCreditHistoriesFiltered = this.agencyCreditHistories;

                        // Deductions and benefits
                        // If they both are not checked
                        if (this.filters.deductions != this.filters.benefits) {
                            if (this.filters.deductions) {
                                this.agencyCreditHistoriesFiltered = this.agencyCreditHistoriesFiltered.filter(a => {
                                    return a.CodeType == 'Deduction';
                                })
                            }
                            if (this.filters.benefits) {
                                this.agencyCreditHistoriesFiltered = this.agencyCreditHistoriesFiltered.filter(a => {
                                    return a.CodeType == 'Benefit';
                                })
                            }
                        }

                        // Checks and credits
                        // If they both are not checked
                        if (this.filters.checks != this.filters.credits) {
                            if (this.filters.checks) {
                                this.agencyCreditHistoriesFiltered = this.agencyCreditHistoriesFiltered.filter(a => {
                                    return a.Amount > 0;
                                })
                            }
                            if (this.filters.credits) {
                                this.agencyCreditHistoriesFiltered = this.agencyCreditHistoriesFiltered.filter(a => {
                                    return a.Amount < 0;
                                })
                            }
                        }

                    },
                },
                watch: {
                    'filters.deductions': function(val) {
                        if (this.filters.deductions == false && this.filters.benefits == false) {
                            this.filters.deductions = !val;
                        } else {
                            this.filter();
                        }
                    },

                    'filters.benefits': function(val) {
                        if (this.filters.deductions == false && this.filters.benefits == false) {
                            this.filters.benefits = !val;
                        } else {
                            this.filter();
                        }
                    },

                    'filters.checks': function(val) {
                        if (this.filters.checks == false && this.filters.credits == false) {
                            this.filters.checks = !val;
                        } else {
                            this.filter();
                        }
                    },

                    'filters.credits': function(val) {
                        if (this.filters.checks == false && this.filters.credits == false) {
                            this.filters.credits = !val;
                        } else {
                            this.filter();
                        }
                    }
                },
            })
        </script>

        <script>
            $(document).ready(function() {
                $.get( "@Url.Action("GetInvoiceTaxes", "PayrollInvoices", new { i = Model.DarwinInvoiceNumber })", function( data ) {
                    $( "#GetInvoiceTaxes" ).html( data );
                }).done(function() {
                    $.get( "@Url.Action("GetInvoiceFees", "PayrollInvoices", new { i = Model.DarwinInvoiceNumber })", function( data ) {
                        $( "#GetInvoiceFees" ).html( data );
                    }).done(function() {
                        $.get( "@Url.Action("GetInvoiceBenefits", "PayrollInvoices", new { i = Model.DarwinInvoiceNumber })", function( data ) {
                            $( "#GetInvoiceBenefits" ).html( data );
                        }).done(function() {
                            //nothing
                        });
                    });
                });


            });
        </script>

        <script>
            $(document).on("click", '.invoice-link', function(evt) {
                window.scrollTo(0, document.body.scrollHeight);
                $('#loading').show();
                evt.preventDefault();
                evt.stopPropagation();
                $('#partialDiv').hide();
                var div = $('#partialDiv'),
                    url = $(this).data('url');
                url = url + "?i=" + @Model.DarwinInvoiceNumber;
                $.get(url, function(data) {
                    div.html(data);
                    $('#partialDiv').delay(1000).show(0);
                    $('#loading').delay(1000).hide(0);


                });
            });

            $(document).ready(function() {
                $('#loading').show();
                //evt.preventDefault();
                //evt.stopPropagation();
                $('#partialDiv').hide();
                var div = $('#partialDiv'),
                    url = '@Url.Action("Wages", "PayrollInvoices")';
                url = url + "?i=" + @Model.DarwinInvoiceNumber;
                $.get(url, function(data) {
                    div.html(data);
                    $('#partialDiv').delay(1000).show(0);
                    $('#loading').delay(1000).hide(0);


                });
            });

            $(function() {
                // bind change event to select
                $('#report-select').on('change', function() {
                    var reportId = $(this).val();
                    if (reportId == "xx") {
                        return false;
                    }
                    if (reportId.startsWith("wh347")) {
                        try {
                            const jobCostingName = reportId.match(/(?<=job).*/)[0];
                            const params = new URLSearchParams({
                                darwinInvoiceNumber: @Model.DarwinInvoiceNumber,
                                jobCostingName
                            });
                            const baseUrl = "@Url.Action("GetWH347", "PayrollInvoices")";
                            window.open(`${baseUrl}?${params}`);
                        } catch {
                            toastr.error('Unable to open WH347');
                        }

                        return false;
                    }
                    if (reportId.indexOf("pdf") > -1) {
                        reportId = reportId.replace("pdf-", "");
                        $('#pdfReportId').val(reportId);
                        $("#pdfViewerForm").submit();
                        return false;
                    }
                    if (reportId) {
                        $('#reportId').val(reportId);
                        $('#invoiceBegin').val(@Model.DarwinInvoiceNumber);
                        $("#reportViewerForm").submit();
                    }

                    return false;
                });
            });
            $(function() {
                $('#select-invoice').change(function() {
                    //var selected = $(this).val();
                    var url = "@Url.Action("Current", "PayrollInvoices")";
                    var value = $(this).val();
                    window.location.href = url + "?i=" + value;
                });
            });
        </script>
    }

    using (Html.BeginForm("Index", "SSRSReportViewer", FormMethod.Post, new { name = "reportViewerForm", id = "reportViewerForm", target = "_blank" }))
    {
        <input type="hidden" id="invoiceBegin" name="invoiceBegin" />
        <input type="hidden" id="invoiceEnd" name="invoiceEnd" value="0" />
        <input type="hidden" id="dateFrom" name="dateFrom" value="12/31/1969" />
        <input type="hidden" id="dateTo" name="dateTo" value="12/31/1969" />
        <input type="hidden" id="reportId" name="reportId" />
        <input type="hidden" id="year" name="year" value="0" />
        <input type="hidden" id="displayType" name="displayType" value="1" />
        <input type="hidden" id="Preview" name="Preview" value="0" />
    }
    using (Html.BeginForm("InvoicePDF", "PayrollInvoices", FormMethod.Post, new { name = "pdfViewerForm", id = "pdfViewerForm", target = "_blank" }))
    {
        <input type="hidden" id="pdfReportId" name="pdfReportId" />
    }
}
