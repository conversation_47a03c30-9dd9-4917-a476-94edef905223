
<script type="text/x-template" id="notification-groups-table-template">
    <table class="table table-striped table-bordered" id="notificationGroupsDataTable">
        <thead>
            <tr>
                <th>
                    Group Name
                </th>
                <th>
                    Shared
                </th>
                <th>
                    Created By
                </th>
                <th>
                    Actions
                </th>
                <th>

                </th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
</script>
<script type="text/javascript" id="notification-groups-table-script">
    var NotificationGroupsTable = VueComponent('notification-groups-table', {
        props: {
        },
        data: function () {
            return{
                events: {
                    btnDeleteClick: "btn-delete-click",
                    btnEditClick: "btn-edit-click",
                    dataTableInit: "data-table-init"
                },
                dataTable: undefined,
                dataTableRenderer: {
                    renderDeleteIcon: function (data, type, row, meta) {
                        return '<i data-id="' + row.ID + '" class="fa fa-lg fa-times icon-red btn-delete" title="delete"></i><i  data-id="' + row.ID + '"  data-group-name="' + row.GroupName + '"  data-shared="' + row.Shared +'" class="fa fa-pencil btn-edit text-success"></i>';
                    },
                }

            }
        },
        mounted: function () {
            var self = this;
            this.initDataTable('notificationGroupsDataTable');
            $('#notificationGroupsDataTable').on('click', '.btn-delete', function () {
                var $btn = $(this)
                var row = $btn.parent().parent();
                var id = $btn.data('id');
                self.$emit(self.events.btnDeleteClick, id)
            })
            $('#notificationGroupsDataTable').on('click', '.btn-edit', function () {
                var $btn = $(this)
                var row = $btn.parent().parent();
                var id = $btn.data('id');
                var groupName = $btn.data('groupName');
                var shared = $btn.data('shared')
                self.$emit(self.events.btnEditClick, id, groupName, shared);
            })
        },
        methods: {
            initDataTable: function (id) {
                var component = this;

                $(document).ready(function () {
                    var $table = $('#' + id);

                    component.dataTable = $table.DataTable({
                        processing: true,
                        serverSide: true,
                        ordering: true,
                        lengthChange: true,
                        paging: true,
                        pagingType: 'simple_numbers',
                        searching: false,
                        searchDelay: 1500,
                        stateSave: false,
                        autoWidth: true,
                        ajax: {
                            url: '@Url.Action("GetNotificationGroups", "NotificationGroup")',
                            type: 'POST',
                            data: function (data) {
                            },
                            dataFilter: function (json) {
                                var data = $.parseJSON(json);
                                return json;
                            }
                        },
                        columns: [
                            { searchable: true, orderable: true, name: 'GroupName', data: 'GroupName', width: 800 },
                            { searchable: true, orderable: true, name: 'Shared', data: 'Shared' },
                            { searchable: true, orderable: true, name: 'CreatedBy', data: 'CreatedBy' },
                            { searchable: false, orderable: false, name: 'action', render: component.dataTableRenderer.renderDeleteIcon },
                            { searchable: false, orderable: false, name: 'ID', data: 'ID', visible: false}
                        ],
                        order: [[ 1, "asc" ]],
                        lengthMenu: [ 10, 25, 50, 100 ],
                        dom: 'Bfrtip',
                        initComplete: function (settings, json) {
                            // Setup nested 'schedule details' table.
                        }

                    });
                    component.$emit(component.events.dataTableInit, component.dataTable)
                });
            },

        },
    });
</script>