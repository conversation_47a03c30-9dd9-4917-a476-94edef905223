@using DarwiNet2._0.DNetSynch;

@model DarwiNet2._0.ViewModels.Home.ExistingSessionVM

@{
    ViewBag.Title = "Login";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
<script language="javascript">
    if (self != top) top.location.replace(self.location.href);
</script>
<div class="container">
    <div style="margin-top: 50px;" class="mainbox col-md-9 col-centered login-box">
        <div class="panel">
            <div style="padding-top: 30px" class="panel-body">
                <div class="col-md-12 text-center" style="padding-bottom: 25px;">
                    <a href="@Url.Action("Index", "Home")"><img src="@ViewBag.LogoURL" class="img-responsive" /></a>
                </div>
                <div class="col-md-12">
                    <div class="alert alert-danger alert-dismissible" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <p>Another session is already active for this UserID. What would you like to do?</p>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <a href="@Url.Action("AbortMe", "Home")" class="btn btn-lg  btn-danger">Cancel my login<br/>Return to the login screen.</a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <a href="@Url.Action("KillExistingSession", "Home", new { continueLoggingIn = true })" 
                               class="btn btn-lg btn-success">Continue logging in <br/>Terminate the other session.</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



