@model DarwiNet2._0.ViewModels.NIL.DealOfferByEmailViewModel

@{
    ViewBag.Title = "DealOfferByEmail";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<style>
    .message-panel {
        margin: 0;
        padding: 50px 50px;
        height: fit-content;
    }

    .flex {
        height: 100%;
        display: flex;
    }

    .flex-center {
        justify-content: center;
        align-items: center;
    }
</style>

<div class="flex flex-center">
    @{ 
        string classNames = "alert col-sm-10 col-md-4 message-panel text-center ";
        classNames += Model.Status == "Invalid" || Model.Status == "Error" ? "alert-danger" : Model.Status == "Accepted" ? "alert-success" : Model.Status == "Declined" ? "alert-warning" : "alert-info";
    }
    <div class="@classNames">
        <div>@Html.Raw(Model.Message)</div>
        @{
            if (Model.Status == "Invalid" || Model.Status == "Error")
            {
                var homeUri = Url.Action("Index", "Home");
                <div>
                    <br />The deal offer has been modified or is currently unavailable.
                    <br />Click <a href="@homeUri">here</a> to log into your account.
                </div>
            }
        }
    </div>
</div>
