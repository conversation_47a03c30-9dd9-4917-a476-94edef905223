@model IEnumerable<DarwiNet2._0.Data.ActivityLog>
@{
    Layout = null;
}

@foreach (var item in Model)
{
    if (item.Controller == "Employees" && item.Action != "Index" && item.Action != "List" && item.Action !="Snapshot")
    {
        <li><a href="@Url.Action("Index", item.Controller)/@item.QueryString&A=@item.Action">@item.DisplayName</a></li>
    }
    else
    {
        if (item.Controller != "CustomLinks" && item.Action != "SaveGrid")
        {
            <li><a href="@Url.Action(item.Action, item.Controller)/@item.QueryString">@item.DisplayName</a></li>
        }
    }
}
