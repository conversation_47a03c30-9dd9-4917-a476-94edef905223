@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DataDrivenViewEngine.Models.Core;

@{
    Layout = null;
    string innerDwld = "";
    string action = ViewBag.Action;
    bool fromDocs = action == "EmployeeDocuments";
    List<int> folders = ViewBag.Folders;
    string user = GlobalVariables.DNETOwnerID;
    string ee = ViewBag.DocEmployee;
    int co = ViewBag.DocCompany;
    bool editable = ViewBag.Access != MenuAccessLevel.ReadOnly;
    string filePath = Url.Content("~/Assets/") + GlobalVariables.Customer + "." + GlobalVariables.Company + "/" + Folders.Library + "/";
    ViewBag.Title = "Documents";

    if (ViewBag.HasDocuments)
    {
        innerDwld += "<div id='DwnldSel' style='display:none' class='pull-right'><button onclick='DownloadSelected()' title = 'Download Selected Documents' class='view-stub-click btn btn-thinkware'><i class='fa fa-download fa-lg fa-fw'></i>Download Selected</button>&nbsp;</div>";
    }
}

@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 18px; text-align: center;"><i><b><u>@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</u></b></i></p>
        </div>
    </div>
    <div class="toolbar" style="padding-bottom: 2px;">
        <div class="row">
            <div class="col-md-3 pull-left">
                <div class="input-group">
                    <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                    <input type="text" class="form-control" id='FieldFilter' placeholder="Search Document Name">
                </div>
            </div>
            @if (innerDwld != "")
            {
                <p class="create-pad pull-right">
                    @Html.Raw(innerDwld)
                </p>
            }
        </div>
    </div>
    <!--=====================================================================================-->
    if (GlobalVariables.DNETLevel != DNetAccessLevel.Employee)
    {
        @(Html.Kendo().Grid<EEDocView>()
                    .Name("documents-table")
                    .Columns(columns =>
                    {
                    columns.Template(@<text></text>).ClientTemplate("<div class='text-center'><input type='checkbox' class='chkbx' data-field='Selected' #= Selected ? checked='checked':'' # #= Used ? disabled='disabled':'' #  /></div>").HeaderTemplate("<div class='text-center'><input type='checkbox' class='checkbox' id='cbSelectAll' value='' /></div>").Width(30);
                    columns.Bound(p => p.AttachmentID).Hidden();
                    columns.Bound(p => p.DocName).Title("Document");
                    columns.Bound(p => p.DocType).Title("Doc.Type");
                    columns.Bound(p => p.OwnedDoc).Title("Created By");
                    columns.Bound(p => p.Source).Title("From");
                    columns.Bound(p => p.Subject);
                    columns.Bound(p => p.Used).Hidden();
                    columns.Template(@<text></text>).ClientTemplate("#if (NoteText != '') {# <i class='icon-light-yellow fa fa-sticky-note fa-fw notesDetail talk-to-the-hand' data-url='" + Url.Action("Details?id=#=Id #", "Notes") + "' data-subject='#=Subject #'></i> #} #").Title("Note Text").Width(85);
                        columns.Bound(p => p.dispNoteDate).Title("Note Date").Width(130);
                        columns.Template(@<span></span>)
                        .ClientTemplate("#if (IconType == 'PDF') { # <a href='" + Url.Action("GetPdf?filePath=" + filePath + "#=SavedDocName #&emplId=#=EmployeeID#", "WebServer") + "' target='_blank'><i class='icon-red fa fa-file-pdf-o fa-fw fa-lg'></i></a> # } # " +
                                        "#if (IconType == 'IMG') { # <a href='" + Url.Action("GetImage?filePath=" + filePath + "#=SavedDocName #", "WebServer") + "' target='_blank'><i class='fa fa-picture-o fa-fw fa-lg'></i></a> # } # " +
                                        "#if (IconType == 'DOC') { # <a href='" + Url.Action("GetFileForDownload?filePath=" + filePath + "#=SavedDocName #&fileName=#=SavedDocName #", "WebServer") + "' target='_blank'><i class='fa fa-file-text fa-fw fa-lg'></i></a> # }  #" +
                                        "<a href='" + Url.Action("EditNote", "CompanyTools") + "?id=#= Id #&fromDocs=" + fromDocs + "&a=" + action + "' title='Edit Note' class='view-stub-click' data-toggle='modal' data-target='\\#noteModal' data-remote='false'><i class='icon-edit fa fa-pencil fa-lg fa-fw'></i></a>"
                                        //"<a href='\\#' data-id='#= Id #' data-name='#=DocName#' id='delete1'><i class='icon-red fa fa-times fa-lg fa-fw'></i></a>"
                                        ).Title("Actions").Width(110);
                    })
                    .Pageable()
                    .Sortable()
                    .Scrollable()
                    .Filterable()
                    .HtmlAttributes(new { style = "height:100%;" })
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .PageSize(10)
                        .Read(read => read.Action("GetEEDocuments_Read", "HRLibrary", new { a = action, eid = ee, c = "", co = co })))
        )
    }
    else
    {
        @(Html.Kendo().Grid<EEDocView>()
                    .Name("documents-table")
                    .Columns(columns =>
                    {
                    columns.Template(@<text></text>).ClientTemplate("<div class='text-center'><input type='checkbox' class='chkbx' data-field='Selected' #= Selected ? checked='checked':'' # #= Used ? disabled='disabled':'' #  /></div>").HeaderTemplate("<div class='text-center'><input type='checkbox' class='checkbox' id='cbSelectAll' value='' /></div>").Width(30);
                    columns.Bound(p => p.AttachmentID).Hidden();
                    columns.Bound(p => p.DocName).Title("Document");
                    columns.Bound(p => p.DocType).Title("Doc.Type");
                    columns.Bound(p => p.OwnedDoc).Title("Created By");
                    columns.Bound(p => p.Source).Title("From");
                    columns.Bound(p => p.Subject);
                    columns.Bound(p => p.Used).Hidden();
                    columns.Template(@<text></text>).ClientTemplate("#if (NoteText != '') {# <i class='icon-light-yellow fa fa-sticky-note fa-fw notesDetail talk-to-the-hand' data-url='" + Url.Action("Details?id=#=Id #", "Notes") + "' data-subject='#=Subject #'></i> #} #").Title("Note Text").Width(85);
                        columns.Bound(p => p.dispNoteDate).Title("Note Date").Width(130);
                        columns.Template(@<span></span>)
                        .ClientTemplate("#if (IconType == 'PDF') { # <a href='" + Url.Action("GetPdf?filePath=" + filePath + "#=SavedDocName #&emplId=#=EmployeeID#", "WebServer") + "' target='_blank'><i class='icon-red fa fa-file-pdf-o fa-fw fa-lg'></i></a> # } # " +
                                        "#if (IconType == 'IMG') { # <a href='" + Url.Action("GetImage?filePath=" + filePath + "#=SavedDocName #", "WebServer") + "' target='_blank'><i class='fa fa-picture-o fa-fw fa-lg'></i></a> # } # " +
                                        "#if (IconType == 'DOC') { # <a href='" + Url.Action("GetFileForDownload?filePath=" + filePath + "#=SavedDocName #&fileName=#=SavedDocName #", "WebServer") + "' target='_blank'><i class='fa fa-file-text fa-fw fa-lg'></i></a> # }  #"
                                        //"<a href='\\#' data-id='#= Id #' data-name='#=DocName#' id='delete1'><i class='icon-red fa fa-times fa-lg fa-fw'></i></a>"
                                        ).Title("Actions").Width(110);
                    })
                    .Pageable()
                    .Sortable()
                    .Scrollable()
                    .Filterable()
                    .HtmlAttributes(new { style = "height:100%;" })
                    .DataSource(dataSource => dataSource
                        .Ajax()
                        .PageSize(10)
                        .Read(read => read.Action("GetEEDocuments_Read", "HRLibrary", new { a = action, eid = ee, c = "", co = co })))
        )
    }

}
<script src="~/Scripts/bootbox.min.js"></script>

<script>
    $('#cbSelectAll').on('click', function () {
        var ischecked = this.checked;
        if (ischecked) {
            $('#DwnldSel').show();
        }
        else {
            $('#DwnldSel').hide();
        }
        $('#documents-table').find('input:checkbox').each(function () {
            if (!this.disabled)  this.checked = ischecked;
        });
        var gridData = $("#documents-table").data("kendoGrid").dataSource.data();
        gridData.forEach(function (dataItem) {
            if (dataItem.Used) {
                dataItem.Selected = false;
            }
            else {
                dataItem.Selected = ischecked;
            }
        });
        var url = '@Url.Action("SetAllDocumentSelected", "HRLibrary")';
        url += "?value=" + ischecked;
        $.post(url,
                function (data) {
                    console.log(data);
                });
    });

    $('#documents-table').on('mouseup', '.chkbx', function () {
        var dataItem = $('#documents-table').data().kendoGrid.dataItem($(this).closest('tr'));
        console.log(dataItem.Selected);
        var checked = !dataItem.Selected;
        var field = $(this).attr('data-field');
        var id = dataItem.Id;
        dataItem.set(field, checked);

        var url = '@Url.Action("SetDocumentSelected", "HRLibrary")';
        url += "?id=" + id + "&value=" + checked;
        $.post(url,
                function (data) {
                    console.log(data);
                    if (data) {
                        $('#DwnldSel').show();
                    }
                    else {
                        $('#DwnldSel').hide();
                    }
                });
    })

    $(document).ready(function () {
        if (IsAnyDocumentChecked()) {
            $('#DwnldSel').show();
        }
        else {
            $('#DwnldSel').hide();
        }
        $("#FieldFilter").keyup(function () {

            var value = $("#FieldFilter").val();
            var grid = $("#documents-table").data("kendoGrid");

            if (value) {
                grid.dataSource.filter({
                    logic: "or",
                    filters: [
                        { field: "DocName", operator: "contains", value: value }
                    ]
                })
            } else {
                grid.dataSource.filter({});
            }
        });


    });

    $(document).on("click", "td #delete1", function (e) {
        var deletedID = $(this).attr('data-id');
        var url = "@Url.Action("Delete", "Notes", null)";
        var name = $(this).attr('data-name');
        bootbox.dialog({
            message: "Are you sure you want to delete: " + "<strong>" + name + "</strong>",
            title: "Delete Document",
            buttons: {
                main: {
                    label: "Cancel",
                    className: "btn-primary",
                    callback: function () {
                        //Example.show("Primary button");
                    }
                },
                danger: {
                    label: "Delete",
                    className: "btn-danger",
                    callback: function () {
                        $.get("@Url.Action("Delete", "Notes", null)" + "/" + deletedID, function (evt) {
                            var url = '@Url.Action("GetEmployeeDocuments", "Employees", new { eid = ee, co = co })';
                            window.location.href = url;
                        });
                    }
                }
            }
        });
    });


    function IsAnyDocumentChecked() {
        var result = false;
        var gridData = $("#documents-table").data("kendoGrid").dataSource.data();
        gridData.forEach(function (dataItem) {
            if (dataItem.Selected) result = true;
        });
        return result;
    }

    function DownloadSelected() {
        var url = "@Url.Action("SelectedLibraryDocumentsDownload", "HRLibrary", new { a = action })";
        url += "&useUniqueFiles=true";
        $.post(url,
            function (data) {
                if (data) {
                    $('#documents-table').data().kendoGrid.dataSource.read();
                    $('#documents-table').data().kendoGrid.refresh();
                    $('#cbSelectAll').prop('checked', false);
                    if (IsAnyDocumentChecked()) {
                        $('#DwnldSel').show();
                    }
                    else {
                        $('#DwnldSel').hide();
                    }
                    var durl = "@Url.Action("DocumentDownload", "HRLibrary")";
                    durl += "?fn=" + data;
                    console.log(durl);
                    window.location.href = durl;
                }
           });
    };
</script>
<script>
    function StartSearch() {
        var v = $('#searchClient').val();
        $('#mdClient').val(v);
        var v = $('#searchEmployee').val();
        $('#mdEmployee').val(v);
        var v = $('#searchDoc').val();
        $('#mdDoc').val(v);
        var v = $('#searchDocType').val();
        $('#mdDocType').val(v);
        var v = $('#searchDocSourse').val();
        $('#mdDocSource').val(v);
        $('#ModalForm').attr('action', 'SearchDocIndex');
        $('#ModalForm').submit();
    }


</script>
