@using DataDrivenViewEngine.Models.Core
@using DarwiNet2._0.Extensions;

<style type="text/css" id="employee-selections-style">
    .z-index-reset li {
        z-index: 998;
    }
</style>

<script type="text/x-template" id="employee-selections-template">
    <div id="EmployeeSelections">
        <div class="row">
            <div class="col-md-12">
                <settings-card :view-model="settingCards"
                               @@update-option="updateOption">
                    <template v-slot:select2>
                        <vue-multiselect v-model="settingCards.workStatus"
                                         :options="settingCards.workStatusList"
                                         :multiple="true"
                                         id="settingsOptionsList"
                                         :close-on-select="false"
                                         :clear-on-select="false"
                                         :preserve-search="true"
                                         placeholder="Select Work Status(es)"
                                         label="Description"
                                         track-by="Description"
                                         :preselect-first="false"
                                         class="z-index-reset" />
                    </template>
                    <template v-slot:select5>
                        <vue-multiselect v-model="settingCards.PayrollTeamID"
                                         :options="settingCards.payrollTeamsList"
                                         :multiple="false"
                                         id="settingsTeamsOptionsList"
                                         :close-on-select="false"
                                         :clear-on-select="false"
                                         :preserve-search="true"
                                         placeholder="Select Payroll Team"
                                         label="Description"
                                         track-by="Description"
                                         :preselect-first="false" />
                    </template>
                    <template v-slot:payFrequency>
                        <vue-multiselect v-model="settingCards.payFrequency"
                                         :options="settingCards.payFrequencyList"
                                         :multiple="true"
                                         id="payFrequencyList"
                                         :close-on-select="false"
                                         :clear-on-select="false"
                                         :preserve-search="true"
                                         placeholder="Select Pay Frequencies"
                                         label="Description"
                                         track-by="Description"
                                         :preselect-first="false"
                                         class="z-index-reset" />
                    </template>
                </settings-card>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 text-center">
                <thinkware-select-list :view-model="departmentClassCode">
                </thinkware-select-list>
            </div>
            <div class="col-md-6 text-center">
                <thinkware-select-list :view-model="positions">
                </thinkware-select-list>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 text-center">
                <thinkware-select-list :view-model="payCode">
                </thinkware-select-list>
            </div>
            <div class="col-md-6 text-center">
                <thinkware-select-list :view-model="worksiteCode">
                </thinkware-select-list>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 text-center">
                <thinkware-select-list :view-model="employeeClassCode" :key="viewModel.UseAllEmployeeClasses">
                    <template v-slot:top>
                        <thinkware-toggle-button :enabled="viewModel.UseAllEmployeeClasses" :onClick="toggleUseAllEmployeeClasses" />
                        <label>Use all classes within company</label>
                    </template>
                </thinkware-select-list>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="pull-right">
                    <button type="button" id="btn_EmployeeSaveChanges" class="btn btn-thinkware" @@click="btnSaveChanges_Click">Save Changes</button>
                </div>
            </div>
        </div>
        <!--do not delete, to be added later - Marcus Casswell -->
        @*<div class="row">
            <div class="col-md-6 text-center">
                    <array-select-card :array-select-data="locationCode.currentData"
                                        :moved-items="locationCode.movedItems"
                                        :title="locationCode.title">

                    </array-select-card>
                </div>
            <div class="col-md-6 text-center">
                <array-select-card :array-select-data="payGroupCode.currentData"
                                    :moved-items="payGroupCode.movedItems"
                                    :title="payGroupCode.title">

                </array-select-card>
            </div>
        </div>*@
    </div>
</script>

<script src="~/Scripts/vue-loading-overlay.js"></script>
<link href="~/Scripts/vue-loading.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/vue-multiselect@2.1.0"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue-multiselect@2.1.0/dist/vue-multiselect.min.css">

@Html.VueComponent("~/PayrollSchedule/modal")
@Html.VueComponent("~/PayrollProfileEmployeeSelections/settings-card")
@Html.VueComponent("~/shared/thinkware-select-list")
@Html.VueComponent("~/shared/thinkware-toggle-button")

<script type="text/javascript" id="employee-selections-script">
    Vue.component('vue-multiselect', window.VueMultiselect.default);
    Vue.use(VueLoading);
    Vue.use(VueToastr, {
        defaultPosition: 'toast-bottom-left',
        defaultType: 'info',
        defaultTimeout: 1000
    });

    var EmployeeSelectionsComponent = VueComponent('employee-selections', {
        props: {
            viewModel: {
                type: Object,
                required: true,
            }
        },
        data: function () {
            return {
                showModal: false,
                companyId: this.viewModel.PayrollProfileSettingsHeader.CompanyID,
                clientId: this.viewModel.PayrollProfileSettingsHeader.ClientID,
                profileId: this.viewModel.PayrollProfileSettingsHeader.ProfileID,
                companyName: this.viewModel.PayrollProfileSettingsHeader.CompanyName,
                clientName: this.viewModel.PayrollProfileSettingsHeader.ClientName,
                navbar: { // TODO: This needs to be its own object on the model.
                    CompanyID: this.viewModel.PayrollProfileSettingsHeader.CompanyID,
                    ClientID: this.viewModel.PayrollProfileSettingsHeader.ClientID,
                    ProfileID: this.viewModel.PayrollProfileSettingsHeader.ProfileID,
                },
                departmentClassCode: {
                    data: this.viewModel.Departments,
                    title: "Departments",
                    id: 'department',
                    search: true
                },
                employeeClassCode: {
                    data: this.viewModel.EmployeeClassCodes,
                    title: "Employee Classes",
                    id: 'employeeClass',
                    search: true,
                },
                positions: {
                    data: this.viewModel.Positions,
                    title: "Positions",
                    id: 'positionClass',
                    search: true
                },
                payCode: {
                    data: this.viewModel.PayCodes,
                    title: "Pay Codes",
                    id: 'payCode',
                    search: true
                },
                worksiteCode: {
                    data: this.viewModel.Worksites,
                    title: "Worksites",
                    id: 'worksiteCode',
                    search: true
                },
                settingCards: {
                    workStatusList: this.viewModel.WorkStatusList,
                    workStatus: this.viewModel.SelectedWorkStatuses,
                    payFrequency: this.viewModel.SelectedPayFrequencies,
                    payFrequencyList: this.viewModel.PayFrequencyList,
                    payrollTeamsList: this.viewModel.PayrollTeamsList,
                    payrollTeamID: this.viewModel.PayrollTeamID,
                    refreshAutoListPayRun: this.viewModel.RefreshAutoListPayRun,
                    ignoreManuallyAddedEmployeesOnRefresh: this.viewModel.IgnoreManuallyAddedEmployeesOnRefresh,
                },
                headerTitle: 'Employee Selections',
                events: {
                    changesSaved: "changes-saved",
                    useAllEmployeeClassesToggled: "use-all-employee-classes-toggled",
                }
            }
        },
        components: [
            ModalComponent,
            SettingsCardComponent,
            VueLoading,
            ThinkwareSelectListComponent,
            ThinkwareToggleButtonComponent,
        ],
        methods: {
            btnSaveChanges_Click: function () {
                const self = this;
                let loader = this.$loading.show({
                    loader: 'dots'
                });
                var workStatusSelected = [];
                var selectedWorkStatus = this.settingCards.workStatus;
                var selectedDepartmentIds = this.departmentClassCode.data.filter(x => x.Selected).map(x => x.Code);
                var selectedEmployeeIds = this.employeeClassCode.data.filter(x => x.Selected).map(x => x.Code);
                var selectedPositionIds = this.positions.data.filter(x => x.Selected).map(x => x.Code);
                var selectedPayCodes = this.payCode.data.filter(x => x.Selected).map(x => x.Code);
                var selectedWorksiteCodes = this.worksiteCode.data.filter(x => x.Selected).map(x => x.Code);
                var selectedPayFrequencies = this.settingCards.payFrequency.map(pf => pf.Code);
                ThinkwareCommon.ajax.postJson('@Url.Action("EmployeeSelections", "PayrollProfileEmployeeSelections")', {
                    data: {
                        CompanyId: this.companyId,
                        ClientId: this.clientId,
                        ProfileId: this.profileId,
                        WorkStatus: selectedWorkStatus,
                        PayrollTeamID: this.settingCards.payrollTeamID,
                        RefreshAutoListPayRun: this.settingCards.refreshAutoListPayRun.Value,
                        IgnoreManuallyAddedEmployeesOnRefresh: this.settingCards.ignoreManuallyAddedEmployeesOnRefresh.Value,
                        DepartmentCodes: selectedDepartmentIds,
                        EmployeeClassCodes: selectedEmployeeIds,
                        PositionCodes: selectedPositionIds,
                        PayCodes: selectedPayCodes,
                        WorksiteCodes: selectedWorksiteCodes,
                        PayFrequencies: selectedPayFrequencies,
                    },
                    onSuccess: function (result) {
                        if (result.status === 'Success') {
                            vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                            vm.$toastr.defaultPosition = "toast-bottom-right";
                            vm.$toastr.s(result.message);
                            loader.hide();
                            self.$emit(self.events.changesSaved);
                        }
                        else {
                            vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                            vm.$toastr.defaultPosition = "toast-bottom-right";
                            vm.$toastr.e(result.message);
                            loader.hide();
                        }
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        loader.hide();
                        vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                        vm.$toastr.defaultPosition = "toast-bottom-right";
                        vm.$toastr.e(errorThrown);
                    }
                });

                for (let i = 0; i < selectedWorkStatus.length; i++) {
                    delete selectedWorkStatus[i].__ob__;
                    workStatusSelected.push(selectedWorkStatus[i].Code);
                };
            },
            updateSelectedItems: function (selectedItems, type) {
                this.$data.codes[type] = selectedItems;
            },
            updateOption: function (option, value, showToast) {
                let model = this.settingCards[option];

                ThinkwareCommon.ajax.postJson(`@Url.Action("UpdateOption", "PayrollProfileEmployeeSelections")?CompanyId=@Model.PayrollProfileSettingsHeader.CompanyID&ClientId=@Model.PayrollProfileSettingsHeader.ClientID&ProfileId=@Model.PayrollProfileSettingsHeader.ProfileID`, {
                    data: {
                        type: model.Type,
                        value: value
                    },
                    onSuccess: function (result) {
                        // Handle response client side
                        if (result.status === "Success") {
                            if (showToast == true) {
                                vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                                vm.$toastr.defaultPosition = "toast-bottom-right";
                                vm.$toastr.s(result.message);
                            }
                            model.Value = value;
                        }
                        else {
                                vm.$toastr.type = "error"
                                vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                                vm.$toastr.defaultPosition = "toast-bottom-right";
                                vm.$toastr.e("Error while saving" );
                        }
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                        vm.$toastr.defaultPosition = "toast-bottom-right";
                        vm.$toastr.e( errorThrown );
                    }
                });
            },
            toggleUseAllEmployeeClasses() {
                this.$emit(this.events.useAllEmployeeClassesToggled);
            },
        },
        watch: {
            'viewModel.EmployeeClassCodes': {
                deep: true,
                handler(val) {
                    this.employeeClassCode.data = val;
                },
            }
        }
    });
</script>
