@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;

@{
ViewBag.Title = "Employee Time Punch Edit";
}

@model DarwiNet2._0.Data.EmployeeTimePunch

    @using (Html.BeginForm("EditTime", "TimePunches", FormMethod.Post, new{ id = "editTimePunches"}))
    {
        @Html.AntiForgeryToken()
        
        <div class="form-horizontal">
            <div class="company-info">
                <div class="row">
                    <div class="col-md-6 col-sm-6">
                        <p style="font-size: 22px;">@ViewBag.Title</p>
                        <div class="colored-line-left"></div>
                    </div>
                </div>
            </div>
            <hr />
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.HiddenFor(model => model.ID)
            <input type="hidden" id="flt" value="@ViewBag.FLT" name="flt" />
            <input type="hidden" id="r" name="r" value="@ViewBag.Range" />
            <div class="form-group">
                @Html.LabelFor(model => model.EmployeeName, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.EmployeeName, new { @class = "form-control", @readonly = "readonly" })
                    @Html.ValidationMessageFor(model => model.EmployeeName, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.PunchDay, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("PunchDay", GlobalVariables.LanguageID))
                <div class="col-md-4">

                    <input type="text" id="PunchDay" name="PunchDay" class="form-control" value="@FieldTranslation.ToShortDate(Model.PunchDay.ToString())" readonly="readonly" />
                    @Html.ValidationMessageFor(model => model.PunchDay, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.EmployeeID, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.EmployeeID, new { @class = "form-control", @readonly = "readonly"})
                    @Html.ValidationMessageFor(model => model.EmployeeID, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.DayType, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DayType", GlobalVariables.LanguageID))
                <div class="col-md-4">

                    <input type="text" id="DayType" name="DayType" class="form-control" value="@FieldTranslation.TranslateSelectList(Model.DayType.ToString(), ViewBag.DayTypes)" readonly="readonly" />
                    @Html.ValidationMessageFor(model => model.DayType, "", new { @class = "text-danger" })
                </div>
            </div>
    
            <div class="form-group">
                @Html.LabelFor(model => model.PunchIn, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("PunchIn", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <div class="input-group my-group" style="width: 100%">
                        @if (!String.IsNullOrEmpty(Model.PunchIn.ToString()))
                        {
                            <input type="text" class="form-control" name="PunchInInput" id="PunchInInput" value="@Model.PunchIn.Value.ToString("hh:mm") ">
                        }
                        else
                        {
                            <input type="text" class="form-control" name="PunchInInput" id="PunchInInput" value="">
                        }
                        <select id="PunchInAMPM" class="selectpicker form-control" data-live-search="true">
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                        <input type="hidden" value="" name="PunchIn" id="PunchIn"/>
                    </div>
                    @Html.ValidationMessageFor(model => model.PunchIn, "", new {@class = "text-danger"})
                </div>
                @Html.LabelFor(model => model.PunchOut, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("PunchOut", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <div class="input-group my-group" style="width: 100%">
                        @if (!String.IsNullOrEmpty(Model.PunchOut.ToString()))
                        {
                            <input type="text" class="form-control" name="PunchOutInput" id="PunchOutInput" value="@Model.PunchOut.Value.ToString("hh:mm")">
                        }
                        else
                        {
                            <input type="text" class="form-control" name="PunchOutInput" id="PunchOutInput" value="">
                        }
                        <select id="PunchOutAMPM" class="selectpicker form-control" data-live-search="true">
                            <option value="AM">AM</option>
                            <option value="PM">PM</option>
                        </select>
                        <input type="hidden" value="" name="PunchOut" id="PunchOut"/>
                    </div>
                    @Html.ValidationMessageFor(model => model.PunchOut, "", new {@class = "text-danger"})
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.Department, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" id="Department" name="Department">
                        @{
                            if (ViewBag.Departments != null)
                            {
                                foreach (Code_Description profile in ViewBag.Departments)
                                {
                                    <option value="@profile.Code" @((Model.Department == profile.Code) ? "selected" : string.Empty)>@profile.Description</option>
                                }
                            }
                        }
                    </select>
                    @Html.ValidationMessageFor(model => model.Department, "", new {@class = "text-danger"})
                </div>
                @Html.LabelFor(model => model.Position, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Position", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    <select class="form-control" id="Position" name="Position">
                        @{
                            if (ViewBag.Positions != null)
                            {
                                foreach (Code_Description profile in ViewBag.Positions)
                                {
                                    <option value="@profile.Code" @((Model.Position == profile.Code) ? "selected" : string.Empty)>@profile.Description</option>
                                }
                            }
                        }
                    </select>
                    @Html.ValidationMessageFor(model => model.Position, "", new {@class = "text-danger"})
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.WorkHours, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("WorkHours", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.WorkHours, new { @class = "form-control", @readonly = "readonly" })
                    @Html.ValidationMessageFor(model => model.WorkHours, "", new { @class = "text-danger" })
                </div>
                @if (ViewBag.PTORequest > 0)
                {
                    <div class="control-label col-md-2"></div>
                    <div class="col-md-4">
                        <!-- this button has to call "PTORequest_Read(id = ViewBag.PTORequest)" in "TimePunches" controller and use json to display data -->
                        <a href="@Url.Action("PTORequest_Read", "TimePunches", new { id = ViewBag.PTORequest })" class="btn btn-thinkware btn-block" id="PTORequest" data-toggle="modal" data-target="#ptoRequestModal" data-remote="false">View PTO Request</a>
                    </div>
                    
                }

            </div>
            @if (ViewBag.CanAdjust)
            {
                <div class="form-group">
                    @Html.LabelFor(model => model.AdjustTime, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Adjustment (+/- in hours)", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        @Html.TextBoxFor(model => model.AdjustTime, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.AdjustTime, "", new { @class = "text-danger" })
                    </div>
                    @Html.LabelFor(model => model.AdjustDescr, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Adjustment Description", GlobalVariables.LanguageID))
                    <div class="col-md-4">
                        @Html.TextBoxFor(model => model.AdjustDescr, new { @class = "form-control", @maxlength = "100" })
                        @Html.ValidationMessageFor(model => model.AdjustDescr, "", new { @class = "text-danger" })
                    </div>
                </div>
            }


            @Html.HiddenFor(model => model.ClientID, new { htmlAttributes = new { @class = "form-control" } })

        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    <a href="@Url.Action("ClientTPIndex", "TimePunches", new {client = Model.ClientID, flt = ViewBag.FLT})" class="btn btn-thinkware">Cancel</a>
                    <button type="button" class="btn btn-thinkware" id="Save">Save</button>
                </div>
            </div>
        </div>
            </div>
    }
@if (ViewBag.PTORequest > 0)
{
    <div class="modal fade" id="ptoRequestModal" tabindex="-1" role="dialog" aria-labelledby="ptoRequestModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="ptoRequestModalLabel">PTO Request</h4>
                </div>
                <div class="modal-body">
                    <div class="form-horizontal" id="editPTO">
                        
                        <div class="form-group">
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="text" id="modalEmpId" value="" class="form-control" readonly="readonly"/>
                            </div>
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("PTOType", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="text" readonly="readonly" class="form-control" id="modalPtoType" value=""/>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("StartDate", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="text" id="modalStartDate" class="form-control" value="" readonly="readonly" />
                            </div>
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("EndDate", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="text" id="modalEndDate" class="form-control" value="" readonly="readonly"/>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("AllDay", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="checkbox" id="modalAllDay" value="" disabled="disabled"/>
                            </div>
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("Comments", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="text" id="modalComments" class="form-control" value="" readonly="readonly" />
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("TotalRequestedTime", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="text" id="modalTotalRequestedTime" class="form-control" value="" readonly="readonly" />
                            </div>
                            <div class="control-label col-md-2">@FieldTranslation.GetLabel("Status", GlobalVariables.LanguageID)</div>
                            <div class="col-md-4">
                                <input type="text" id="modalStatus" class="form-control" value="" readonly="readonly" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    @*<button type="button" class="btn btn-primary">Save changes</button>*@
                </div>
            </div>
        </div>
    </div>
}
@section scripts {
    <script src="~/Scripts/MaskedInput.js"></script>
    <script src="~/Scripts/bootbox.min.js"></script>
@if (!String.IsNullOrEmpty(Model.PunchIn.ToString()))
{
    <script>
        var timeInAMPM = "@Model.PunchIn.Value.ToString("HH:mm")";
       
        timeInAMPM = timeInAMPM.split(':')[0];
        if (timeInAMPM >= 12) {
            $('#PunchInAMPM').val('PM');
        }
    </script>
}
@if (!String.IsNullOrEmpty(Model.PunchOut.ToString()))
{
    <script>
        var timeOutAMPM = "@Model.PunchOut.Value.ToString("HH:mm")";
        timeOutAMPM = timeOutAMPM.split(':')[0];
        if (timeOutAMPM >= 12) {
            $('#PunchOutAMPM').val('PM');
        }
    </script>
}
<script>
        $(document).ready(function() {
            $('#PunchInInput').mask('99:99', { placeholder: "__:__" });
            $('#PunchOutInput').mask('99:99', { placeholder: "__:__" });
            //set the am pm based on edited values
            var tm = "@Model.AdjustTime.Value";
            if (isNaN(tm) || tm == 0) {
                $('#AdjustDescr').val('');
                $('#AdjustDescr').attr("disabled", "disabled");
            }
            else
                $('#AdjustDescr').removeAttr("disabled");


        });
        $('#PunchInInput').keyup(function() {
            var startTime = $('#PunchInInput').val();
            var startTimeTrim = startTime.split(':')[0];
            var startMinTrim = startTime.split(':')[1];
            if (startTimeTrim >= 13 || startMinTrim >= 60) {
                bootbox.dialog({
                    message: "Punch in time must be in the correct 12 hour format",
                    title: "Status",
                    buttons: {
                        main: {
                            label: "Ok",
                            className: "btn-primary",
                            callback: function () {
                                //Example.show("Primary button");
                            }
                        }
                    }
                });
                $('#PunchInInput').val('');
            }
        });
        $('#PunchOutInput').keyup(function () {
            var endTime = $('#PunchOutInput').val();
            var endTimeTrim = endTime.split(':')[0];
            var endMinTrim = endTime.split(':')[1];
            //alert(endTimeTrim)
            if (endTimeTrim >= 13 || endMinTrim >= 60) {
                bootbox.dialog({
                    message: "Punch out time must be in the correct 12 hour format",
                    title: "Status",
                    buttons: {
                        main: {
                            label: "Ok",
                            className: "btn-primary",
                            callback: function () {
                                //Example.show("Primary button");
                            }
                        }
                    }
                });
                $('#PunchOutInput').val('');
            }
        });
        $('#AdjustTime').keyup(function () {
            var tm = $('#AdjustTime').val();
            if (isNaN(tm) || tm == 0) {
                $('#AdjustDescr').val('');
                $('#AdjustDescr').attr("disabled", "disabled");
            }
            else
                $('#AdjustDescr').removeAttr("disabled");
        });

        $(document).on("click", "#Save", function (ev) {
            var frm = $('#editTimePunches');
            var endTime = $('#PunchOutInput').val();
            var endTimeAMPM = $('#PunchOutAMPM').val();
            var startTime = $('#PunchInInput').val();
            var startTimeAMPM = $('#PunchInAMPM').val();
            $('#PunchOut').val(endTime + " " + endTimeAMPM);
            $('#PunchIn').val(startTime + " " + startTimeAMPM);
            frm.submit();
        });


</script>
    @if (ViewBag.PTORequest > 0)
    {
        <script>
    $("#ptoRequestModal").on("show.bs.modal",
        function (e) {
            var link = $(e.relatedTarget);
            var url = encodeURI(link.attr("href"));
            $.get(url, function (data) {
                var arr = $.map(data, function (el) { return el });
                //console.log(arr);
                $('#modalEmpId').val(arr[0]['EmployeeID']);
                $('#modalPtoType').val(arr[0]['dispType']);
                $('#modalStartDate').val(arr[0]['dispSDate']);
                $('#modalEndDate').val(arr[0]['dispEDate']);
                if (arr[0]['AllDay']) {
                    $('#modalAllDay').prop('checked', true);
                }

                $('#modalComments').val(arr[0]['Comments']);
                $('#modalTotalRequestedTime').val(arr[0]['TotalRequestedTime']);
                $('#modalStatus').val(arr[0]['dispStatus']);
            });
        });
        </script>
    }
    }