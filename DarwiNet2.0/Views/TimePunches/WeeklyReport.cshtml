@{
    Layout = null;
}
@using (Html.BeginForm("WeeklyReport", "TimePunches", FormMethod.Post, new { id = "weeklyreport" }))
{
    <div class="form-horizontal">
        <input type="hidden" id="flt" name="flt" value="@ViewBag.FLT" />
        <input type="hidden" id="r" name="r" value="@ViewBag.Range" />
        <div class="form-group">
            <label class="control-label col-md-3">Date From</label>
            <div class="col-md-9">
                <input type="text" name="DateFrom" id="DateFrom" class="field-required" style="width: 100%" required="required" />
                <div class="error-msg"></div>
            </div>
        </div>

        <div class="form-group">
            <label class="control-label col-md-3">Date To</label>
            <div class="col-md-9">
                <input type="text" name="DateTo" id="DateTo" class="field-required" style="width: 100%" required="required" />
                <div class="error-msg1"></div>
            </div>
        </div>


        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                    <input type="submit" value="Submit" class="btn btn-thinkware" />
                </div>
            </div>
        </div>
    </div>
}
<script src="~/Scripts/jquery.datetimepicker.js"></script>
<link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
<script>
    $(document).ready(function () {
        $('#DateFrom, #DateTo').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
    })
</script>
<script>
    $("#weeklyreport").validate({
        rules: {
            DateFrom: {
                date: true
            },
            DateTo: {
                date: true
            }
        },
        errorPlacement: function (error, element) {
            if (element.attr("name") == "DateFrom")
                error.insertAfter(".error-msg");
            else if (element.attr("name") == "DateTo")
                error.insertAfter(".error-msg1");
            else
                error.insertAfter(element);
        }
    });
</script>
<script>
    $(function () {
        var today = new Date();
        $(".datepicker").datetimepicker({
            timepicker: false,
            format: 'm/d/Y',
            formatDate: 'm/d/Y'
        });
        $(".mindatepicker").datetimepicker({
            timepicker: false,
            format: 'm/d/Y',
            formatDate: 'm/d/Y',
            minDate: today
        });
        $(".datetimepicker").datetimepicker({
            formatTime: 'g:i A',
            format: 'm/d/Y h:i A'
        });
        $(".timepicker").datetimepicker({
            datepicker: false,
            format: 'g:i A',
            formatTime: 'g:i A'
        });
        //$('input').tooltip({
        //    placement: "right",
        //    trigger: "focus"
        //});
    });
    $("form").submit(function (event) {
        $('#reportModal').modal('toggle');
    });

</script>
