@model DarwiNet2._0.Data.ClientTimeImportSetup

@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>ImportSetupDetail</title>
</head>
<body>
    <div>
        <h4>ClientTimeImportSetup</h4>
        <hr />
        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.CompanyID)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.CompanyID)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.ClientID)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.ClientID)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.ModelID)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.ModelID)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.EmployeePrefix)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.EmployeePrefix)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.EmployeeDir)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.EmployeeDir)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.EmployeeLen)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.EmployeeLen)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.ProfilePrefix)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.ProfilePrefix)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.OTFactor)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.OTFactor)
            </dd>
    
            <dt>
                @Html.DisplayNameFor(model => model.LocationByDepartment)
            </dt>
    
            <dd>
                @Html.DisplayFor(model => model.LocationByDepartment)
            </dd>
    
        </dl>
    </div>
    <p>
        @Html.ActionLink("Edit", "Edit", new { /* id = Model.PrimaryKey */ }) |
        @Html.ActionLink("Back to List", "Index")
    </p>
</body>
</html>
