@using DarwiNet2._0.Controllers
@model dynamic

@{
    Layout = null;
}

<label class="col-md-3 control-label">DNet Code</label>
<div class="col-md-9">
    <select class="form-control" id="DNetCode" name="DNetCode">
        @{
            if (ViewBag.DNetCodes != null)
            {
                foreach (Code_Description dnet in ViewBag.DNetCodes)
                {
                    <option value="@dnet.Code" @((ViewBag.SelectedCode1 == dnet.Code) ? "selected" : string.Empty)>@dnet.Description</option>
                }
            }
        }
    </select>
</div>