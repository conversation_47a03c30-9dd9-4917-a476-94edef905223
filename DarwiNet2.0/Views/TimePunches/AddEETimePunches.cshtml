@using DarwiNet2._0.Controllers

@{
    Layout = null;
}

@using (Html.BeginForm())
{
    <div class="form-horizontal">
        <div class="form-group">
            <label class="control-label col-md-3">Employees</label>
            <div class="col-md-9">
                <select multiple="multiple" class="form-control" name="selEEs">
                    @if (ViewBag.PunchEmployees != null)
                        {
                            foreach (Code_Description profile in ViewBag.PunchEmployees)
                            {
                                <option value="@profile.Code">@profile.Description</option>
                            }
                        }
                    
                </select>

            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-3">Punch Day</label>
            <div class="col-md-9">
                @*<input type="text" name="PunchDay" class="form-control datepicker"/>*@
                @(Html.Kendo().DatePicker()
                        .Name("PunchDay")
                        .Start(CalendarView.Month)
                        .Depth(CalendarView.Month)
                        .Format("MM/dd/yyyy")
                        .HtmlAttributes(new { style = "width: 100%", id = "PunchDay", name = "PunchDay" }))
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-3">Department</label>
            <div class="col-md-9">
                <select class="form-control" id="Department" name="Department">
                    @{
    if (ViewBag.Departments != null)
    {
        foreach (Code_Description profile in ViewBag.Departments)
        {
            <option value="@profile.Code">@profile.Description</option>
        }
    }
                    }
                </select>



            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-3">Position</label>
            <div class="col-md-9">
                <select class="form-control" id="Position" name="Position">
                    @{
    if (ViewBag.Positions != null)
    {
        foreach (Code_Description profile in ViewBag.Positions)
        {
            <option value="@profile.Code">@profile.Description</option>
        }
    }
                    }
                </select>



            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                    <input type="submit" value="Save" class="btn btn-thinkware"/>
                </div>
            </div>
        </div>
    </div>
}
<script src="~/Scripts/jquery.datetimepicker.js"></script>
<link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
<script>
    $(document).ready(function () {
        var datepicker = $("#PunchDay").data("kendoDatePicker");
        datepicker.max(new Date());
    });
        $(function () {
            var today = new Date();
            $(".datepicker").datetimepicker({
                timepicker: false,
                format: 'm/d/Y',
                formatDate: 'm/d/Y',
                maxDate: today
            });
            $(".mindatepicker").datetimepicker({
                timepicker: false,
                format: 'm/d/Y',
                formatDate: 'm/d/Y',
            //    minDate: today
            });
            $(".datetimepicker").datetimepicker({
                formatTime: 'g:i A',
                format: 'm/d/Y h:i A'
            });
            $(".timepicker").datetimepicker({
                datepicker: false,
                format: 'g:i A',
                formatTime: 'g:i A'
            });
            //$('input').tooltip({
            //    placement: "right",
            //    trigger: "focus"
            //});
        });


</script>