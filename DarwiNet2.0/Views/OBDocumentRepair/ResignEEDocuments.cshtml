@using DarwiNet2._0.Controllers
@using DarwiNet2._0.D<PERSON>Synch
@using DarwiNet2._0.ViewModels

@{
    ViewBag.Title = "Add signature to EE On-Boarding Documents with empty signature";
}

<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>

    </div>
</div>
<div class="toolbar">
    <table id="clientsDb">
        <tr>
            <th title="@FieldTranslation.GetLabel("Company", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Company", GlobalVariables.LanguageID)
            </th>

            <th title="@FieldTranslation.GetLabel("Employee ID", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Employee ID", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("Employee Name", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Employee Name", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("Document ID", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Document ID", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("Document Name", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Document Name", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("Status", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Status", GlobalVariables.LanguageID)
            </th>
        </tr>

        @foreach (var item in ViewBag.Documents)
        {
            <tr>
                <td>@item.CompanyID.ToString()</td>
                <td>@item.EmployeeID.ToString()</td>
                <td>@item.EmployeeName</td>
                <td>@item.DocumentID.ToString()</td>
                <td>@item.DocumentName</td>
                <td>@item.Description</td>
            </tr>
        }

    </table>
</div>



