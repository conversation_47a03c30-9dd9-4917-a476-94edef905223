@using DarwiNet2._0.Extensions;

<style scoped>
    .expansion-table {
        border-radius: 0 !important;
    }

    .expansion-table th, td  {
        text-align: left;
    }

    .expansion-table-td {
        padding: 0 !important;
    }

    .select-label {
        width: 40%;
    }

    .select-component {
        width: 60%;
    }

    .completed-payrolls-btn {
        color: white !important;
        width: 100px;
    }

    .date-filter-col {
        justify-content: center;
    }

    .completed-payroll-icon {
        cursor: pointer;
    }

    .completed-payroll-icon:hover {
        color: red !important;
    }

    .merge-icon {
        cursor: pointer;
    }

    .merge-icon:hover {
        color: #244061 !important;
    }

    .j-center {
        justify-content: center;
    }

    .j-space-around {
        justify-content: space-around;
    }

    .datetype-col button {
        width: 50%;
    }

    .date-picker-row {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
    }

    .arrow {
        margin: 0 5px;
    }

    .quick-filters-btn-col button {
        width: 100%;
    }

    th {
        font-weight: 900;
        font-size: 1rem !important;
    }

    .t-right {
        text-align: right;
    }

    .filter-chevron {
        cursor: pointer;
    }

    .date-picker-row-input {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .j-space-around {
        display: flex;
        justify-content: space-around;
    }
    
    .header-align {
        display: flex;
        justify-content: end;
        align-items: center;
    }

    .v-data-table-header__icon {
        opacity: 1 !important;
    }

    .payroll-icon {
        cursor: pointer;
    }

    .thinkware-icon-color {
        color: #244061;
    }
</style>

<script type="text/x-template" id="completed-payrolls-table-template">
    <v-app>
        <v-container>
            <!-- Search Row -->
            <v-row>
                <v-col
                    class="d-flex"
                    cols="8"
                >
                    <v-text-field
                        v-model="search"
                        append-icon="mdi-magnify"
                        label="Search"
                        single-line
                        hide-details
                        @@input="onSearch"
                    ></v-text-field>
                </v-col>
                <!-- Toggle datetype filter (process or check date) -->
                <v-col
                    class="d-flex j-center datetype-col"
                    cols="4"
                >
                    <button @@click="changeDateType('Process_Date')" :class="['btn', { 'btn-thinkware': dateType == 'Process_Date', 'btn-default': dateType != 'Process_Date'}]">Process Date</button>
                    <button @@click="changeDateType('CheckDate')" :class="['btn', { 'btn-thinkware': dateType == 'CheckDate', 'btn-default': dateType != 'CheckDate'}]">Check Date</button>
                </v-col>
            </v-row>
            @* show/hide filter *@
            <v-row>
                <v-col cols="12">
                    <div v-if="showFilters" @@click="toggleFilters">
                        Hide Filters <i class="fa-solid fa-chevron-down filter-chevron"></i>
                    </div>
                    <div v-else @@click="toggleFilters">
                        Show Filters <i class="fa-solid fa-chevron-right filter-chevron"></i>
                    </div>
                </v-col>
            </v-row>

            <!-- Filters -->
            <template v-if="showFilters">
                <v-row>
                    <!-- Company Selection -->
                    <v-col
                        class="d-flex input-item"
                        cols="12"
                        sm="4"
                    >
                        <div class="select-label">Company</div>
                        <v-select v-model="selectedCompany"
                                    :options="availableCompanies"
                                    class="select-component"
                                    @@input="onCompanySelected"></v-select>
                    </v-col>
                    <!-- Client Selection -->
                    <v-col
                        class="d-flex input-item"
                        cols="12"
                        sm="4"
                    >
                        <div class="select-label">Client</div>
                        <v-select v-model="selectedClient"
                                    :options="availableClients"
                                    class="select-component"
                                    @@input="onClientSelected"></v-select>
                    </v-col>
                    <!-- Profile Selection -->
                    <v-col
                        class="d-flex input-item"
                        cols="12"
                        sm="4"
                    >
                        <div class="select-label">Profile</div>
                        <v-select v-model="selectedProfile"
                                    :options="availableProfiles"
                                    class="select-component"
                                    @@input="onProfileSelected"></v-select>
                    </v-col>
                </v-row>
                <v-row>
                    <!-- Processing group selection *needs clarification -->
                    <v-col
                        class="d-flex"
                        cols="12"
                        sm="4"
                    >
                        <div class="select-label">Processing Group</div>
                        <v-select v-model="selectedProcessingGroup"
                                    :options="availableProcessingGroups"
                                    class="select-component"
                                    @@input="onProcessingGroupSelected"></v-select>
                    </v-col>
                    <!-- Payroll completed by selection -->
                    <v-col
                        class="d-flex"
                        cols="12"
                        sm="4"
                    >
                        <div class="select-label">Completed By</div>
                        <v-select v-model="selectedCompletedBy"
                                    :options="availableCompletedBy"
                                    class="select-component"
                                    @@input="onCompletedBySelected"></v-select>
                    </v-col>
                    <!-- payroll finalized by selection-->
                    <v-col
                        class="d-flex"
                        cols="12"
                        sm="4"
                    >
                        <div class="select-label">Finalized By</div>
                        <v-select v-model="selectedFinalizedBy"
                                    :options="availableFinalizedBy"
                                    class="select-component"
                                    @@input="onFinalizedBySelected"></v-select>
                    </v-col>
                </v-row>
            
                <v-row class="j-space-around">
                    <v-col
                            class="d-flex quick-filters-btn-col j-space-around"
                            cols="12"
                            sm="4"
                        >
                        <!-- Date Range Selection -->
                        <v-date-picker
                            v-model="range"
                            mode="date"
                            is-range
                            class="date-picker-row"
                        >
                            <template v-slot="{ inputValue, inputEvents, isDragging }">
                                    <div class="date-picker-row-input">
                                        <label for="fromDate">From:&nbsp;</label>
                                        <input
                                            id="fromDate"
                                            class="form-control"
                                            :class="isDragging ? 'text-gray-600' : 'text-gray-900'"
                                            :value="inputValue.start"
                                            v-on="inputEvents.start"
                                        />
                                    </div>
                                    <div class="date-picker-row-input">
                                        <label for="toDate">To:&nbsp;</label>
                                        <input
                                            id="toDate"
                                            class="form-control"
                                            :class="isDragging ? 'text-gray-600' : 'text-gray-900'"
                                            :value="inputValue.end"
                                            v-on="inputEvents.end"
                                        />
                                    </div>
                            </template>
                        </v-date-picker>
                    </v-col>
                    <!-- Quick Date Filters -->
                    <v-col
                        class="d-flex quick-filters-btn-col"
                    >
                        <button type="button" class="btn btn-thinkware completed-payrolls-btn" @@click="filterByQuickDate(quickDateFilterOptions.today)">Today</button>
                    </v-col>
                    <v-col
                        class="d-flex quick-filters-btn-col"
                    >
                        <button type="button" class="btn btn-thinkware completed-payrolls-btn" @@click="filterByQuickDate(quickDateFilterOptions.yesterday)">Yesterday</button>
                    </v-col>
                    <v-col
                        class="d-flex quick-filters-btn-col"
                    >
                        <button type="button" class="btn btn-thinkware completed-payrolls-btn" @@click="filterByQuickDate(quickDateFilterOptions.thisWeek)">This Week</button>
                    </v-col>
                    <v-col
                        class="d-flex quick-filters-btn-col"
                    >
                        <button type="button" class="btn btn-thinkware completed-payrolls-btn" @@click="filterByQuickDate(quickDateFilterOptions.lastWeek)">Last Week</button>
                    </v-col>
                    <v-col
                        class="d-flex quick-filters-btn-col"
                    >
                        <button type="button" class="btn btn-danger completed-payrolls-btn" @@click="clearFilters">Clear</button>
                    </v-col>
                </v-row>
            </template>

            <!-- Data Table -->
            <v-row>
                <v-data-table
                    :headers="headers"
                    :items="filteredData"
                    item-key="id"
                    :expanded.sync="expanded"
                    show-expand
                >
                    @* Slot for changing expand header *@
                    <template v-slot:header.data-table-expand="{ header }">
                        <i class="fa-solid fa-plus"></i>
                    </template>

                    @* Slot to change expand icon *@
                    <template v-slot:item.data-table-expand="{item, isExpanded}">
                        <i :class="isExpanded ? 'fa-solid fa-caret-down' : 'fa-solid fa-caret-right'"
                           @@click="handleExpansion(item, isExpanded)"></i>
                    </template>

                    @* Slot for company *@
                    <template v-slot:item.CompanyID="{ item }">
                        <div :title="item.CompanyName">{{ item.CompanyID }}</div>
                    </template>

                    <!-- Slot for gross wages -->
                    <template v-slot:item.GrossWages="{ item }">
                        <div class="t-right">{{ formatCurrency(item.GrossWages) }}</div>
                    </template>
                    <!-- Slot for invoice total -->
                    <template v-slot:item.InvoiceTotal="{ item }">
                        <div class="t-right" v-if="item.MultipleInvoices">Multiple</div>
                        <div class="t-right" v-else>{{ formatCurrency(item.InvoiceTotal) }}</div>
                    </template>
                    <!-- Slot for voiding payroll -->
                    <template v-slot:item.VoidPayroll="{ item }">
                        <a @@click="routePayrollVoid(item.CompletedPayrollInvoiceRows)">
                            <v-icon small title="Void Payroll" class="completed-payroll-icon">
                                mdi-cancel
                            </v-icon>
                        </a>
                    </template>

                    <!-- Slot for expansion row -->
                    <template v-slot:top>
                        <v-toolbar flat>
                            <v-toolbar-title>Completed Payrolls</v-toolbar-title>
                        </v-toolbar>
                    </template>
                    <template v-slot:expanded-item="{ headers, item }">
                        <td :colspan="headers.length" class="expansion-table-td">
                            <v-simple-table class="expansion-table">
                                <template v-slot:default>
                                    <thead>
                                        <tr>
                                            <th>Payroll #</th>
                                            <th>Audit Control Code</th>
                                            <th>PP Begin</th>
                                            <th>PP End</th>
                                            <th>Process Date</th>
                                            <th>Posted Date</th>
                                            <th>Completed By</th>
                                            <th>Finalized By</th>
                                            <th>Off Cycle</th>
                                            <th>Checks Printed</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><i class="fa-solid fa-circle-info payroll-icon thinkware-icon-color" :title="item.PayrollNumber" @@click="routePayrollReview(item.PayrollNumber)"></i></td>
                                            <td>{{ item.PayrollAuditTrailCode }}</td>
                                            <td>{{ item.PPBeginDate }}</td>
                                            <td>{{ item.PPEndDate }}</td>
                                            <td>{{ item.ProcessDate }}</td>
                                            <td>{{ item.PostedDate }}</td>
                                            <td>{{ item.PayrollCompletedBy }}</td>
                                            <td>{{ item.PayrollFinalizedBy }}</td>
                                            <td>
                                                <i class="fa-solid fa-check text-success" v-if="item.OffCyclePayroll"></i>
                                                <i class="fa-solid fa-xmark text-danger" v-else></i> 
                                            </td>
                                            <td>
                                                <i class="fa-solid fa-check text-success" v-if="item.ChecksPrinted"></i>
                                                <i class="fa-solid fa-xmark text-danger" v-else></i> 
                                            </td>
                                        </tr>
                                    </tbody>
                                </template>
                            </v-simple-table>
                            <!-- Invoice row -->
                            <v-simple-table class="expansion-table" v-if="item.CompletedPayrollInvoiceRows.length > 0">
                                <template v-slot:default>
                                    <thead>
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Division ID</th>
                                            <th>Invoice Date</th>
                                            <th>Invoice Total</th>
                                            <th>Invoice Audit Trail Code</th>
                                            <th>A/R Audit Trail Code</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="row in item.CompletedPayrollInvoiceRows">
                                            <td><a @@click="routeInvoiceReview(item.PayrollNumber, row.InvoiceNumber)" title="Invoice Review">{{ row.InvoiceNumber }}</a></td>
                                            <td>{{ row.DivisionID }}</td>
                                            <td>{{ row.InvoiceDate }}</td>
                                            <td class="t-right">{{ formatCurrency(row.InvoiceTotal) }}</td>
                                            <td>{{ row.InvoiceAuditTrailCode }}</td>
                                            <td>{{ row.ARAuditTrailCode }}</td>
                                            <td>
                                                <span v-if="row.IsMergedInvoice">
                                                    <v-icon small title="Associated Payrolls" class="merge-icon" @@click="showAssociatedPayrolls(row)">
                                                        mdi-merge
                                                    </v-icon>
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </template>
                            </v-simple-table>
                        </td>
                    </template>
                </v-data-table>
            </v-row>
        </v-container>
        <thinkware-vue-modal :active="showAssociatedPayrollsModal"
                         title="New Invoice"
                         width="60%"
                         v-on:close-modal="onClickCloseAssociatedPayrolls">
            <associated-payrolls-modal :associated-payrolls="associatedPayrollData"></associated-payrolls-modal>
    </thinkware-vue-modal>
    </v-app>
</script>


<!-- use the latest vue-select release -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/vue-select/3.10.8/vue-select.min.js" integrity="sha512-YKIQqtelpb88teIP+6w4Fk0cgRT5frFgeu1PRXu539SPTYz/gOJcAJ7FOUe7YyFUPcQgHOnAslgJRz7BsdRoeA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vue-select/3.10.8/vue-select.css" integrity="sha512-bHIocRfiCRcIvzrExk2/NpzkoL+Jh3b0ZL9mGJ2oua7UpeDee8NBuNnRc+fsHTc8NIbIQriTOmGxfgYHSygXTg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

@Html.VueComponent("~/shared/thinkware-date-picker")
@Html.VueComponent("~/Shared/thinkware-vue-modal")
@Html.VueComponent("associated-payrolls-modal")

<script type="text/javascript" id="completed-payrolls-table-script">
    Vue.component('v-select', VueSelect.VueSelect);
    const today = moment().startOf('day').format("M/D/YYYY");
    const CompletedPayrollsTableComponent = new VueComponent('completed-payrolls-table', {
        data: function () {
            return {
                showFilters: true,
                headers: [
                    { text: 'asdf', value: 'data-table-expand' },
                    { text: 'Company', value: 'CompanyID' },
                    { text: 'Client ID', value: 'ClientID' },
                    { text: 'Client', value: 'ClientName' },
                    { text: 'Profile ID', value: 'ProfileID' },
                    { text: 'Check Date', value: 'CheckDate' },
                    { text: 'Gross Wages', value: 'GrossWages', align: 'end' },
                    { text: 'Invoice Total', value: 'InvoiceTotal', align: 'end' },
                    { text: '# EE\'s', value: 'NumberEmployees' },
                    { text: 'Void', value: 'VoidPayroll', sortable: false },
                ],
                expansionRowHeaders: [
                    { text: 'Payroll #', value: 'PayrollNumber' },
                    { text: 'Audit Trail Code', value: 'PayrollAuditTrailCode' },
                    { text: 'PP Begin Date', value: 'PPBeginDate' },
                    { text: 'PP End Date', value: 'PPEndDate' },
                    { text: 'Process Date', value: 'ProcessDate' },
                    { text: 'Posted Date', value: 'PostedDate' },
                    { text: 'Completed By', value: 'PayrollCompletedBy' },
                    { text: 'Finalized By', value: 'PayrollFinalizedBy' },
                ],
                tableData: [],
                filteredData: [],

                // Quick date filter options
                quickDateFilterOptions: {
                    today: 'today',
                    yesterday: 'yesterday',
                    thisWeek: 'thisweek',
                    nextWeek: 'nextWeek'
                },

                // Date picker range
                range: {
                    start: null,
                    end: null
                },

                // Filters
                selectedCompany: "",
                selectedClient: "",
                selectedProfile: "",
                selectedProcessingGroup: "",
                selectedCompletedBy: "",
                selectedFinalizedBy: "",

                // vm data
                availableCompanies: @Html.Raw(ViewBag.CompaniesID),
                availableClients: [],
                availableProfiles: [],
                // *needs clarification
                availableProcessingGroups: @Html.Raw(ViewBag.AvailableProcessingGroups),
                availableCompletedBy: @Html.Raw(ViewBag.PayrollCompletedBy),
                availableFinalizedBy: @Html.Raw(ViewBag.FinalizedBy),

                // Search
                search: "",

                // v-data-table expansion
                expanded: [],
                singleExpand: false,

                // Date type to filter by (process date or check date)
                dateType: "Process_Date",

                // Modal
                showAssociatedPayrollsModal: false,
                associatedPayrollData: null,
            }
        },
        watch: {
            // When daterange end is changed (selected), fetch payrolls in that daterange
            'range.end': function (value) {
                this.fetchCompletedPayrollsInDateRange();
            }
        },
        components: [
            VueSelect.VueSelect,
            ThinkwareDatePickerComponent,
            ThinkwareVueModalComponent,
            AssociatedPayrollsModalComponent
        ],
        methods: {
            handleExpansion(item, state) {
                const itemIndex = this.expanded.indexOf(item);
                state ? this.expanded.splice(itemIndex, 1) : this.expanded.push(item);
            },

            toggleFilters: function() {
                this.showFilters = !this.showFilters;
            },

            // Filtering Methods
            onSearch: _.debounce(function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("SearchCompletedPayrolls", "CompletedPayrolls")' + `?searchText=${self.search}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            }, 300),

            onCompanySelected: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("SearchCompletedPayrollsByCompany", "CompletedPayrolls")' + `?companyId=${this.selectedCompany.code}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                        self.getClientsForCompany();
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            onClientSelected: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("SearchCompletedPayrollsByClient", "CompletedPayrolls")' + `?client=${this.selectedClient.code}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                        self.getProfilesForClient();
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            onProfileSelected: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("SearchCompletedPayrollsByProfile", "CompletedPayrolls")' + `?profile=${this.selectedProfile}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },
            
            // *Needs clarification
            onProcessingGroupSelected: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("SearchCompletedPayrollsByTeam", "CompletedPayrolls")' + `?team=${this.selectedProcessingGroup}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            onCompletedBySelected: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("SearchCompletedPayrollsByCompletedBy", "CompletedPayrolls")' + `?completedBy=${this.selectedCompletedBy}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            onFinalizedBySelected: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("SearchCompletedPayrollsByFinalizedBy", "CompletedPayrolls")' + `?finalizedBy=${this.selectedFinalizedBy}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            /*
                Quick date filter function to fetch completed payrolls via quick date type
                DateTypes:
                - today
                - yesterday
                - thisweek
                - lastweek
            */
            filterByQuickDate: function(dateType) {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("GetCompletedPayrollsByQuickDate", "CompletedPayrolls")' + `?dateType=${dateType}&filterType=${self.dateType}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            // Uses the datepicker range (to - from) to get completed payrolls in that daterange
            fetchCompletedPayrollsInDateRange: function() {
                let sendStart = new Date(this.range.start.getFullYear() + '-' + (this.range.start.getMonth() + 1) + '-' + this.range.start.getDate()).toISOString().slice(0,10);
                let sendEnd = new Date(this.range.end.getFullYear() + '-' + (this.range.end.getMonth() + 1) + '-' + this.range.end.getDate()).toISOString().slice(0,10);

                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("GetCompletedPayrollsByDateRange", "CompletedPayrolls")' + `?startDate=${sendStart}&endDate=${sendEnd}&filterType=${self.dateType}`, {
                    onSuccess: function (response) {
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            clearFilters: function() {
                this.filteredData = this.tableData;
                this.selectedCompany = null;
                this.selectedClient = null;
                this.selectedProfile = null;
                this.selectedProcessingGroup = null;
                this.selectedCompletedBy = null;
                this.selectedFinalizedBy = null;
                this.range = {
                    start: null,
                    end: null
                }
            },
            // ******************************************* //

            // Get Data
            getTable() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("LoadAllCompletedPayrollLastSevenDays", "CompletedPayrolls")', {
                    onSuccess: function (response) {
                        self.tableData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                        self.filteredData = response.data.map((item, index) => ({
                            id: index,
                            ...item
                        }));
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            getClientsForCompany: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("GetClientsByCompany", "CompletedPayrolls")' + `?companyId=${this.selectedCompany.code}`, {
                    onSuccess: function (response) {
                        self.availableClients = response.data;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            getProfilesForClient: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("GetProfilesByClient", "CompletedPayrolls")' + `?clientId=${this.selectedClient.code}`, {
                    onSuccess: function (response) {
                        self.availableProfiles = response.data;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });
            },

            //  ******************************************* //

            // Routes

            routePayrollReview: function(payrollNumber) {
                window.location = `@Url.Action("Review", "PayrollReview")?payrollNumber=${payrollNumber}&fromCompletedPayrolls=true`;
            },

            routeInvoiceReview: function(payrollNumber, invoiceNumber) {
                window.location = `@Url.Action("Review", "PayrollInvoicesReview")?payrollNumber=${payrollNumber}&invoiceNumber=${invoiceNumber}`;
            },

            routePayrollVoid: function(payrollInvoices) {
                let codes = []
                for (inv in payrollInvoices) {
                    if (!codes.includes(payrollInvoices[inv].ARAuditTrailCode)) {
                        codes.push(payrollInvoices[inv].ARAuditTrailCode)
                    }
                }
                window.location = `@Url.Action("Index", "PayrollVoid")?auditControlCodes=${codes}`;
            },

            // ******************************************** //

            // Modals
    
            showAssociatedPayrolls: function(invoice) {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("GetAssociatedPayrolls", "CompletedPayrolls")' + `?companyId=${invoice.CompanyID}&clientId=${invoice.ClientID}&invoiceNum=${invoice.InvoiceNumber}`, {
                    onSuccess: function (response) {
                        self.associatedPayrollData = response.data;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log(jqXHR, textStatus, errorThrown);
                    }
                });

                this.showAssociatedPayrollsModal = true;
            },

            onClickCloseAssociatedPayrolls: function() {
                this.showAssociatedPayrollsModal = false;
            },

            // Set date type (process date or check date)
            changeDateType: function(dType) {
                this.dateType = dType;
                console.log(this.dateType)
            },
        },
        mounted() {
            this.getTable();
        },
        computed: {
            // Searches objects by specified field by search bar text
            searchFilterPayrolls: function() {
                if (!this.search) return this.filteredData;

                let searchCase = this.search.toLowerCase();

                let filterReturn = this.filteredData.filter(pr => {
                    return
                    (pr.ClientID && pr.ClientID.toString().toLowerCase().includes(searchCase)) ||
                    (pr.ClientName && pr.ClientName.toString().toLowerCase().includes(searchCase)) ||
                    (pr.ProfileID && pr.ProfileID.toString().toLowerCase().includes(searchCase)) ||
                    (pr.PayrollNumber && pr.PayrollNumber.toString().toLowerCase().includes(searchCase)) ||
                    (pr.PayrollAuditTrailCode && pr.PayrollAuditTrailCode.toString().toLowerCase().includes(searchCase))
                })

                return filterReturn;
            }
        }
    });

</script>