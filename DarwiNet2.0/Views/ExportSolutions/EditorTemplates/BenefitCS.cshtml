@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model DarwiNet2._0.Models.BenefitCS
@{
    var displayItem = "table-row";

    if (!((bool)ViewData["IsEdit"]) && (bool)Model.Grouped)
    {
        displayItem = "none";
    }
}

@if (!((bool)ViewData["IsGrouping"]))
{
    <tr class="csRow" style="display: @displayItem;">
        @Html.HiddenFor(model => model.DataBaseField)
        @Html.HiddenFor(model => model.Description)
        @Html.HiddenFor(model => model.Grouped)
        @Html.HiddenFor(model => model.GroupBy)
        <td>@Html.CheckBoxFor(model => model.Export, new { id = "export" })</td>
        <td>@FieldTranslation.GetLabel(Model.DataBaseField, GlobalVariables.LanguageID)</td>
        <td>
            @FieldTranslation.GetLabel(Model.Description, GlobalVariables.LanguageID)
        </td>
        <td>
            @Html.TextBoxFor(model => model.DisplayName, new { @maxlength = "50", @class = "form-control" })
            @Html.ValidationMessageFor(model => model.DisplayName, "", new { @class = "text-danger" })
        </td>
    </tr>
}
else
{
    <tr class="csRow" style="display: @displayItem;">
        @Html.HiddenFor(model => model.DataBaseField)
        @Html.HiddenFor(model => model.Description)
        <td class="text-center">@Html.CheckBoxFor(model => model.Grouped, new { id = "grp" })</td>
        <td>@FieldTranslation.GetLabel(Model.DataBaseField, GlobalVariables.LanguageID)</td>
        <td>
            @FieldTranslation.GetLabel(Model.Description, GlobalVariables.LanguageID)
        </td>
    </tr>
}
