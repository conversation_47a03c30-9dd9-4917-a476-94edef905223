@model DarwiNet2._0.Models.ConstantField

@Html.HiddenFor(x => x.Name)
<div class="row" style="margin-top: 3px; padding-bottom: 2px;">
    <div class="col-md-3" style="padding-top: 4px;">
        <span style="white-space: nowrap;">
            <span style="display: inline-block; width: 25px; text-align: center;">
                @Html.CheckBoxFor(x => x.Export)
            </span>
            <span style="white-space: nowrap;">
                @Html.DisplayFor(x => x.Name)
            </span>
        </span>
    </div>
    <div class="col-md-5">
        @Html.TextBoxFor(x => x.Value, new { @class = "form-control", @maxlength = "50" })
    </div>
</div>