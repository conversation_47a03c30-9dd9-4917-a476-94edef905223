@using DarwiNet2._0.Controllers
@using DarwiNet2._0.D<PERSON>Synch
@using DarwiNet2._0.Helpers
@model DarwiNet2._0.ViewModels.ReportOptionVM
<style>
    /*.Dirty {
        background-color: #ffcaca;
    }*/

    .ui-dialog {
        background-color: rgba(255, 255, 255, 1);
    }

    .header {
        font-weight: bold;
        font-size: 18px;
    }
</style>

<div class="row" style="padding-top: 15px; margin-bottom: 6px;">
    @*<div class="col-md-12">
        <div class="headerRow">
            <p style="font-size: 22px;">Report Options</p>
            <div class="colored-line-left"></div>
        </div>
    </div>*@
    <div class="col-md-10" id="saveMessage">
        <div class="col-md-12">
            <div class="alert alert-success alert-dismissible" role="alert" style="padding: 4px 15px; margin-bottom: 0; display: none;">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close" style="top: 0; right: 0;">
                    <span aria-hidden="true">&times;</span>
                </button>Your options have been saved.
            </div>
        </div>
        <div class="col-md-12">
            <div class="alert alert-danger alert-dismissible" role="alert" style="padding: 4px 15px; margin-bottom: 0; display: none;">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close" style="top: 0; right: 0;">
                    <span aria-hidden="true">&times;</span>
                </button>There was an error saving your request. Please try again.
            </div>
        </div>
    </div>
    
</div>
<div class="row">
    @using (Html.BeginForm("SaveReportOps", "ExportSolutions", FormMethod.Post, new {id = "roTarget"}))
    {
        @Html.HiddenFor(model => model.ExportSolutionRpts.ReportName)
        <div id="divAddSort"></div>
        <div id="basic" class="row">
            <div class="col-md-4 col-md-offset-1">
                @Html.EditorFor(x => x.LogicFlags)
                <div style="padding-left: 25px; padding-top: 15px;" class="animated bounce">
                    <a href="#" id="reorder" type="button" value="Reorder" class="btn btn-thinkware"><i class="fa fa-sort fa-lg fa-fw" aria-hidden="true"></i> Reorder Columns</a>
                </div>
            </div>
        </div>
    }
</div>
@*<div class="row">
    <span class="pull-right" id="show-action-toggle1">
        
        <input id="save" type="button" value="Save Report Options" class="btn btn-success btn-lg" />
    </span>
</div>*@
<script>
    // var declared in DistributeByEmp
    window.currPartial = 'ReportOps';

    function SaveReportOps(showAlert) {
        $.ajax({
            url: '@Url.Action("SaveReportOps", "ExportSolutions")',
            type: "POST",
            data: $("#roTarget").serialize(),
            success: function (data) {
                //$(":input").each(function () {
                //    if ($(["id$='SubTotalOption'"])) {
                //        $(this).removeClass("Dirty");
                //    }
                //    var dirt = $(this).closest("tr").find("#isdirty");
                //    if (dirt.val()) {
                //        $(this).removeClass("Dirty");
                //        dirt.val(false);
                //    }
                //});
                if (showAlert) {
                    $(".alert-success").show();
                    $(".alert-success").delay(2750).fadeOut(250);
                }
            },
            error: function () {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    }

    $(":input").change(function () { //trigers change in all input fields including text type
        if ($(["id$='SubTotalOption'"])) {
            $(this).addClass("Dirty");
        }
        $(this).closest("tr").find("#isdirty").val(true);
    });

    function unloadPage() {
        if (unsaved) {
            return "You have unsaved changes on this page. Do you want to leave this page and discard your changes or stay on this page?";
        }
    }

    window.onbeforeunload = unloadPage;
    $(document).ready(function () {
        $('#report').removeClass('btn-gray').addClass('btn-thinkware');
        $('#select').removeClass('btn-thinkware').addClass('btn-gray');
        $('#ops').removeClass('btn-thinkware').addClass('btn-gray');
    })
</script>


