@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model DarwiNet2._0.ViewModels.PayWageVM
@using (Html.BeginForm("PayWageSave", "ExportSolutions", FormMethod.Post, new { id = "pwTarget" }))
{
    string displayBlock = "display: block;";
    string displayNone = "display: none;";
    string showPayWageGroups = (Model.PayWageGroups != null && Model.PayWageGroups.Count > 0) ? displayBlock : displayNone;
    string showPayWageCS = (@Model.PayWageCS != null && @Model.PayWageCS.Where(x => !x.Grouped).Count() > 0) ? displayBlock : displayNone;

    @Html.HiddenFor(model => model.ExportSolutionRpts.ReportName)
    <div id="paywages-basic-setup" style="padding-bottom: 10px;">
        <div class="row">
            <div class="col-md-12">
                <div class="form-horizontal" style="border: 1px solid #ccc; padding-top: 10px;">
                    @*<p style="padding: 5px 0 0 10px; font-size: 22px;">Choose a display name for the Wage items below.</p>*@
                    @Html.EditorFor(x => x.PayWageOptions)
                </div>
            </div>
        </div>
    </div>

    <div style="padding-bottom: 5px; @showPayWageCS">
        <div class="row">
            <div class="col-md-12">
                <input id="groupCS" type="button" value="Add Group" class="btn btn-thinkware btn-lg" href="#" data-toggle="modal" data-target="#groupingModal" />
            </div>
        </div>
    </div>
    <div class="row" style="@showPayWageGroups">
        <div class="col-md-10">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><label>@FieldTranslation.GetLabel("Group Name", GlobalVariables.LanguageID)</label></th>
                        <th>Incuded Pay Codes</th>
                        <th class="text-center" style="width: 125px;"><label>@FieldTranslation.GetLabel("Display Hours", GlobalVariables.LanguageID)</label></th>
                        <th><label>@FieldTranslation.GetLabel("Display Name Hours", GlobalVariables.LanguageID)</label></th>
                        <th class="text-center" style="width: 50px;">Edit</th>
                    </tr>
                </thead>
                <tbody>
                    @Html.EditorFor(x => x.PayWageGroups)
                </tbody>
            </table>
        </div>
    </div>
    <div class="row" style="@showPayWageCS">
        <div class="col-md-12">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th class="text-center" style="width: 75px;"><label>@FieldTranslation.GetLabel("Select", GlobalVariables.LanguageID)</label></th>
                        <th style="width: 100px;"><label>@FieldTranslation.GetLabel("Pay Code", GlobalVariables.LanguageID)</label></th>
                        <th><label>@FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)</label></th>
                        <th><label>@FieldTranslation.GetLabel("Display Name", GlobalVariables.LanguageID)</label></th>
                        @*<th class="text-center" style="width: 85px;"><label>@FieldTranslation.GetLabel("Group", GlobalVariables.LanguageID)</label></th>*@
                        <th class="text-center" style="width: 125px;"><label>@FieldTranslation.GetLabel("Display Rate", GlobalVariables.LanguageID)</label></th>
                        <th><label>@FieldTranslation.GetLabel("Display Name Rate", GlobalVariables.LanguageID)</label></th>
                        <th class="text-center" style="width: 125px;"><label>@FieldTranslation.GetLabel("Display Hours", GlobalVariables.LanguageID)</label></th>
                        <th><label>@FieldTranslation.GetLabel("Display Name Hours", GlobalVariables.LanguageID)</label></th>
                    </tr>
                </thead>
                <tbody>
                    @Html.EditorFor(x => x.PayWageCS, new { IsGrouping = false, IsEdit = false })
                </tbody>
            </table>
        </div>
    </div>
    <div class="row">
        <div class="pull-right" id="show-action-toggle">
            @*<a href="javascript:void(0)" id="show-basic-setup" class="btn btn-thinkware setup-shown">Toggle Basic Setup Options</a>*@
            <input id="save" type="button" value="Save Report" class="btn btn-success btn-lg" />
        </div>
    </div>
}

<!-- Modal -->
<div class="modal fade" id="groupingModal" tabindex="-1" role="dialog" aria-labelledby="groupingModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="groupingModalLabel">Select items to group:</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-bottom: 5px;">
                    <div class="col-md-3">Group Name: </div>
                    <div class="col-md-9">
                        <div><input id="groupName" type="text" class="form-control" /></div>
                        <div><span id="requiredGroupName" class="required-error" style="display: none;">Must have a group name to select items.</span></div>
                    </div>
                </div>
                <div class="form-horizontal">
                    <table id="groupTable" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th class="text-center" style="width: 75px;"><label>@FieldTranslation.GetLabel("Group", GlobalVariables.LanguageID)</label></th>
                                <th style="width: 100px;"><label>@FieldTranslation.GetLabel("Pay Code", GlobalVariables.LanguageID)</label></th>
                                <th><label>@FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)</label></th>
                            </tr>
                        </thead>
                        <tbody>
                            @Html.EditorFor(x => x.PayWageCS, new { IsGrouping = true, IsEdit = false })
                        </tbody>
                    </table>

                    <div class="row" id="runReport">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="text-center">
                                    <div class="btn btn-lg btn-thinkware" onclick="createGroup()">Save Group</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="closeReportModal" onclick="clearGroupingModal()">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="editGroupingModal" tabindex="-1" role="dialog" aria-labelledby="editGroupingModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="editGroupingModalLabel">Select items to group:</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-bottom: 5px;">
                    <div class="col-md-3">Group Name: </div>
                    <div class="col-md-9">
                        <div>
                            <input id="editGroupName_New" type="text" class="form-control" />
                            <input id="editGroupName" type="hidden" />
                        </div>
                        <div><span id="requiredEditGroupName" class="required-error" style="display: none;">Must have a group name to select items.</span></div>
                    </div>
                </div>
                <div class="form-horizontal">
                    <div id="editGroupSelection"></div>
                    <div class="row" id="runReport">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="text-center">
                                    <div class="btn btn-lg btn-thinkware" onclick="editGroup()">Save Group</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="closeReportModal" onclick="clearGroupingModal()">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // var declared in DistributeByEmp
    window.currPartial = 'PayWage';

    function SavePayWage(showAlert) {
        var form = $('#pwTarget');
        form.validate();
        if (form.valid() === true) {
            $.ajax({
                url: '@Url.Action("PayWageSave", "ExportSolutions")',
                type: "POST",
                data: $("#pwTarget").serialize(),
                success: function (data) {
                    if (showAlert) {
                        $(".alert-success").show();
                        $(".alert-success").delay(2750).fadeOut(250);
                    }
                },
                error: function () {
                    $(".alert-danger").show();
                    $(".alert-danger").delay(2750).fadeOut(250);
                }
            });
        }
    }

    $('#groupCS').on("click", function () {
        SavePayWage(false);
    });

    function createGroup() {
        var groupName = $("#groupName").val();
        updateGroup('', groupName, 'groupTable');
    }

    function editGroup() {
        var groupName = $("#editGroupName_New").val();
        var originalGroupName = $("#editGroupName").val();
        updateGroup(originalGroupName, groupName, 'editGroupTable');
    }

    function updateGroup(originalGroupName, groupName, groupTable)
    {
        SavePayWage(false);
        var reportName = '@Model.ExportSolutionRpts.ReportName';
        var displayHours = $("#displayHours").is(':checked');
        var displayNameHours = '';
        if (displayHours)
            displayNameHours = $("#DisplayNameHours").val();

        var pwgList = [];

        var eachRow = "#" + groupTable + " tr";
        $(eachRow).each(function () {
            var dataRow = $(this).find('td');
            var grouped = dataRow.find("[id$='grp']").is(':checked');
            if (grouped) {
                pwg = { DatabaseFieldName: $(this).find("input[name$='DataBaseField']").val(), Description: $(this).find("input[name$='Description']").val() };
                pwgList.push(pwg);
            }
        });

        var sendData = JSON.stringify({ ReportName: reportName, GroupName: groupName, OriginalGroupName: originalGroupName, DisplayHours: displayHours, DisplayNameHours: displayNameHours, GroupedList: pwgList });

        $.ajax({
            url: '@Url.Action("CreatePayWageGroup", "ExportSolutions")',
            contentType: "application/json",
            type: "POST",
            data: sendData,
            success: function (data) {
                $("#selectData").html(data);
                $('#groupingModal').modal('hide');
                $('body').removeClass('modal-open');
                $('.modal-backdrop').remove();
            },
            error: function (errorData) {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    }

    $('.editGroup').on("click", function () {
        SavePayWage(false);
        var displayName = $(this).attr('data-displayname');
        $('#editGroupName').val(displayName);
        $('#editGroupName_New').val(displayName);

        var sendData = JSON.stringify({ ReportName: displayName, PayWageCS: @Html.Raw(Json.Encode(Model.PayWageCS)), PayWageGroups: @Html.Raw(Json.Encode(Model.PayWageGroups)) });

        $.ajax({
            url: '@Url.Action("EditGroup", "ExportSolutions")',
            contentType: "application/json",
            type: "POST",
            data: sendData,
            success: function (data) {
                $("#editGroupSelection").html(data);
            },
            error: function (errorData) {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    });

    $('.deleteGroup').on("click", function () {
        SavePayWage(false);
        var sendData = JSON.stringify({ groupName: $(this).attr('data-displayname'), reportName: $("#ReportName").val()});

        $.ajax({
            url: '@Url.Action("DeletePayWageGroup", "ExportSolutions")',
            contentType: "application/json",
            type: "POST",
            data: sendData,
            success: function (data) {
                $("#selectData").html(data);
            },
            error: function (errorData) {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    });

    $(document).ready(function () {
        SetGroups();
        $(".csRow").each(function () {
            var exportCheckBox = $(this).find("input[id='export']");
            EnablePayWageCSRow(!exportCheckBox.prop('checked'), exportCheckBox);
        });
    });

    function EnablePayWageCSRow(isEnabled, exportCheckBox) {
        var row = exportCheckBox.closest('tr');
        row.find("[id$='_DisplayName']").prop('disabled', isEnabled);
        row.find("[id='grp']").prop('disabled', isEnabled);
        row.find("[id$='_DisplayRate']").prop('disabled', isEnabled);
        row.find("[id$='_DisplayNameRate']").prop('disabled', isEnabled);
        row.find("[id$='_DisplayHours']").prop('disabled', isEnabled);
        row.find("[id$='_DisplayNameHours']").prop('disabled', isEnabled);
    }

    $("input[id$='DisplayName']").dblclick(function () {
        var e = $(this);
        var tempName = e.closest("tr").find("[id$='DataBaseField']").val();
        if (tempName === e.val()) {
            return false;
        }
        e.val(tempName);
        e.select();
        e.trigger("change");
    });

    // ungroup
    $("input:checkbox").on('change', function () {
        var thisName = $(this).attr('name');
        var lengthName = thisName.length;
        var indexOfName = thisName.indexOf('].') + 2;
        var inputName = thisName.substr(indexOfName, lengthName - indexOfName);
        var indexOfType = thisName.indexOf('[');
        var typeName = thisName.substr(0, indexOfType);
        var sectionTypeCS = typeName === "PayWageCS";

        var isChecked = $(this).is(':unchecked');

        switch (inputName) {
            case 'Export':
                if (sectionTypeCS) {
                    EnablePayWageCSRow(isChecked, $(this));
                    if (!isChecked) {
                        var dbf = $(this).closest("tr").find("[id$='DataBaseField']").val();
                        if ($(this).closest("tr").find("[id$='DisplayName']").val() === "") {
                            $(this).closest("tr").find("[id$='DisplayName']").val(dbf);
                        }
                    } else {
                        clearRow($(this).closest("tr"));
                    }
                } else {
                    // Basic Setup Option
                    if (!isChecked) {
                        var desc = $(this).closest('div').next().find("label").text();
                        if ($(this).closest('div.control-label').next().find('input').val() === "") {
                            $(this).closest('div.control-label').next().find('input').val(desc);
                        }
                    } else {
                        $(this).closest('div.control-label').next().find('input').val('')
                    }
                }
                break;

            case 'DisplayRate':
                if (!isChecked) {
                    if ($(this).closest("tr").find("[id$='DisplayNameRate']").val() === "") {
                        $(this).closest("tr").find("[id$='DisplayNameRate']").val('Rate');
                    }
                }
                break;

            case 'DisplayHours':
                if (!isChecked) {
                    if ($(this).closest("tr").find("[id$='DisplayNameHours']").val() === "") {
                        $(this).closest("tr").find("[id$='DisplayNameHours']").val('Hours');
                    }
                }
                break;

            default:
                break;
        }
    });

    $('#groupingModal').on('shown.bs.modal', function () {
        clearGroupingModal();
        $('#groupName').focus();
    });

    function clearRow(row)
    {
        row.find("[id$='DisplayName']").val('');
        row.find("[id$='DisplayRate']").prop('checked', false);
        row.find("[id$='DisplayNameRate']").val('');
        row.find("[id$='DisplayHours']").prop('checked', false);
        row.find("[id$='DisplayNameHours']").val('');
    }

    function clearGroupingModal() {
        $('#groupName').val('');

        $("#displayHours").attr('checked', false);
        $("#displayHours").prop("disabled", true);

        $("#DisplayNameHours").val('');
        $("#displayHours").prop("disabled", true);

        var checkboxes = $("#groupTable").find("input:checkbox");
        for (var i = 0; i < checkboxes.length; i++) { checkboxes[i].checked = false; }
        $("#groupTable").find("input,button,textarea,select").prop("disabled", true);
    }

    $('#groupName').keyup(function () {
        var allowEnable = $(this).val() === "";
        $("#groupTable").find("input,button,textarea,select").prop("disabled", allowEnable);

        if (allowEnable)
            $("#requiredGroupName").show();
        else
            $("#requiredGroupName").hide();

    });

    $('#editGroupName_New').keyup(function () {
        var allowEnable = $(this).val() === "";
        $("#editGroupTable").find("input,button,textarea,select").prop("disabled", allowEnable);

        if (allowEnable)
            $("#requiredEditGroupName").show();
        else
            $("#requiredEditGroupName").hide();

    });
</script>