@using DarwiNet2._0.Models
@model DarwiNet2._0.ViewModels.GlAccountVM
<style>
    .childBtn
    {
        
    }
</style>
@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "glAccTarget" }))
{
    <fieldset>
        @Html.HiddenFor(model => model.ExportSolutionRpts.ReportName)
        @Html.HiddenFor(model => model.CurrParent)
        <input id="btnParentRow" type="button" value="Add Parent"/>
        <table id="tblAccount">
            <tr><th>Item</th><th>Account</th><th>Description</th><th></th></tr>
            @Html.EditorFor(x => Model.Accounts)
        </table>
    </fieldset>
}
<script>
    // var declared in DistributeByEmp
    window.currPartial = 'GlSetup';

    $(".childBtn").on('click', function ()
    {
        AddChildRow($(this));
    });
    $("#btnParentRow").on('click', function ()
    {
        AddParentRow();
    });
    $("input[id$='Description'").on('change', function ()
    {
        AddParentRow();
    });

    function AddParentRow()
    {
        alert('called add parent');
        $.ajax(
        {
            url: '@Url.Action("AddParentRow", "ExportSolutions")',
            type: "POST",
            data: $("#glAccTarget").serialize(),
            success: function (data)
            {
                $("#glDynamicData").html(data);
            }
        });
    }

    function AddChildRow(e)
    {
        var id = e.closest("tr").find("[id$='Account']").val();
        if (id === "")
        {
            alert('must have account number before allocating');
            return false;
        }
        $("#CurrParent").val(id);
        $.ajax(
        {
            url: '@Url.Action("AddChildRow", "ExportSolutions")',
            type: "POST",
            data: $("#glAccTarget").serialize(),
            success: function (data)
            {
                $("#glDynamicData").html(data);
            }
        });
    }

    function SaveGlSetup()
    {
        $.ajax(
        {
            url: '@Url.Action("SaveGlSetup", "ExportSolutions")',
            type: "POST",
            data: $("#glAccTarget").serialize(),
            success: function (data)
            {
                $(":input").each(function ()
                {
                    var dirt = $(this).closest("tr").find("#isdirty");
                    if (dirt.val())
                    {
                        dirt.val(false);
                    }
                    $(this).removeClass("Dirty");
                });
                var success = '<div class="col-md-12"><div class="alert alert-success alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>Your options have been saved.</div>';
                $('.saveMessage').append(success);
                $(".alert").delay(2750).fadeOut(250);

            },
            error: function ()
            {
                var error = '<div class="col-md-12"><div class="alert alert-danger alert-dismissible" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>There was an error saving your request. Please try again.</div>';
                $('.saveMessage').append(error);
                $(".alert").delay(2750).fadeOut(250);
            }
        });
    }

    $(":input").change(function ()
    { //triggers change in all input fields including text type
        $(this).addClass("Dirty");

        $(this).closest("tr").find("#isdirty").val(true);
    });

</script>
