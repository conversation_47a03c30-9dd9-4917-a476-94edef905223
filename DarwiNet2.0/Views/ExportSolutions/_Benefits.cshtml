@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model DarwiNet2._0.ViewModels.BenefitVM
@using (Html.BeginForm("BenefitSave", "ExportSolution", FormMethod.Post, new { id = "bTarget" }))
{
    string displayBlock = "display: block;";
    string displayNone = "display: none;";
    string showBenefitsGroup = (Model.BenefitsGroup != null && Model.BenefitsGroup.Count > 0) ? displayBlock : displayNone;
    string showBenefitCS = (@Model.BenefitCS != null && @Model.BenefitCS.Where(x => !x.Grouped).Count() > 0) ? displayBlock : displayNone;

    @Html.HiddenFor(model => model.ExportSolutionRpts.ReportName);
    <div id="benefits-basic-setup" style="padding-bottom: 25px;">
        <div class="row">
            <div class="col-md-12">
                <div class="form-horizontal" style="border: 1px solid #ccc; padding-top: 10px;">
                    @*<p style="padding: 5px 0 0 10px; font-size: 22px;">Choose a display name for the items below.</p>*@
                    @Html.EditorFor(x => x.BenefitOptions)
                </div>
            </div>
        </div>
    </div>

    <div style="padding-bottom: 5px; @showBenefitCS">
        <div class="row">
            <div class="col-md-12">
                <input id="groupCS" type="button" value="Add Group" class="btn btn-thinkware btn-lg" href="#" data-toggle="modal" data-target="#groupingModal" />
            </div>
        </div>
    </div>
    <div class="row" style="@showBenefitsGroup">
        <div class="col-md-10">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th><label>@FieldTranslation.GetLabel("Group Name", GlobalVariables.LanguageID)</label></th>
                        <th>Incuded Pay Codes</th>
                        <th class="text-center" style="width: 50px;">Edit</th>
                    </tr>
                </thead>
                <tbody>
                    @Html.EditorFor(x => x.BenefitsGroup)
                </tbody>
            </table>
        </div>
    </div>
    <div class="row" style="@showBenefitCS">
        <div class="col-md-12">
            <table class="table table-striped table-bordered ">
                <thead>
                    <tr>
                        <th class="text-center" style="width: 75px;"><label>@FieldTranslation.GetLabel("Select", GlobalVariables.LanguageID)</label></th>
                        <th><label class="control-label ">@FieldTranslation.GetLabel("DataBaseField", GlobalVariables.LanguageID)</label></th>
                        <th><label class="control-label">@FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)</label></th>
                        <th><label class="control-label">@FieldTranslation.GetLabel("DisplayName", GlobalVariables.LanguageID)</label></th>
                    </tr>
                </thead>
                <tbody>
                    @Html.EditorFor(x => x.BenefitCS, new { IsGrouping = false, IsEdit = false })
                </tbody>

            </table>
        </div>
    </div>
    <div class="row">
        <div class="pull-right" id="show-action-toggle">
            @*<a href="javascript:void(0)" id="show-basic-setup" class="btn btn-thinkware setup-shown">Toggle Basic Setup Options</a>*@
            <input id="save" type="button" value="Save Report" class="btn btn-success btn-lg" />
        </div>
    </div>
}

<!-- Modal -->
<div class="modal fade" id="groupingModal" tabindex="-1" role="dialog" aria-labelledby="groupingModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="groupingModalLabel">Select items to group:</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-bottom: 5px;">
                    <div class="col-md-3">Group Name: </div>
                    <div class="col-md-9">
                        <div><input id="groupName" type="text" class="form-control" /></div>
                        <div><span id="requiredGroupName" class="required-error" style="display: none;">Must have a group name to select items.</span></div>
                    </div>
                </div>
                <div class="form-horizontal">
                    <table id="groupTable" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th class="text-center" style="width: 75px;"><label>@FieldTranslation.GetLabel("Group", GlobalVariables.LanguageID)</label></th>
                                <th style="width: 150px;"><label>@FieldTranslation.GetLabel("Benefit Code", GlobalVariables.LanguageID)</label></th>
                                <th><label>@FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)</label></th>
                            </tr>
                        </thead>
                        <tbody>
                            @Html.EditorFor(x => x.BenefitCS, new { IsGrouping = true, IsEdit = false })
                        </tbody>
                    </table>

                    <div class="row" id="runReport">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="text-center">
                                    <div class="btn btn-lg btn-thinkware" onclick="createGroup()">Save Group</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="closeReportModal" onclick="clearGroupingModal()">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="editGroupingModal" tabindex="-1" role="dialog" aria-labelledby="editGroupingModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="editGroupingModalLabel">Select items to group:</h4>
            </div>
            <div class="modal-body">
                <div class="row" style="margin-bottom: 5px;">
                    <div class="col-md-3">Group Name: </div>
                    <div class="col-md-9">
                        <div>
                            <input id="editGroupName_New" type="text" class="form-control" />
                            <input id="editGroupName" type="hidden" />
                        </div>
                        <div><span id="requiredEditGroupName" class="required-error" style="display: none;">Must have a group name to select items.</span></div>
                    </div>
                </div>
                <div class="form-horizontal">
                    <div id="editGroupSelection"></div>
                    <div class="row" id="runReport">
                        <div class="col-md-12">
                            <div class="form-group">
                                <div class="text-center">
                                    <div class="btn btn-lg btn-thinkware" onclick="editGroup()">Save Group</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" id="closeReportModal" onclick="clearGroupingModal()">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // var declared in DistributeByEmp
    window.currPartial = 'Benefits';
    function SaveBenefits(showAlert) {
        var form = $('#bTarget');
        form.validate();
        if (form.valid() === true) {
            $.ajax({
                url: '@Url.Action("BenefitSave", "ExportSolutions")',
                type: "POST",
                data: $("#bTarget").serialize(),
                success: function (data) {
                    if (showAlert) {
                        $(".alert-success").show();
                        $(".alert-success").delay(2750).fadeOut(250);
                    }
                },
                error: function () {
                    $(".alert-danger").show();
                    $(".alert-danger").delay(2750).fadeOut(250);
                }
            });
        }
    }

    $('#groupCS').on("click", function () {
        SaveBenefits(false);
    });

    function createGroup() {
        var groupName = $("#groupName").val();
        updateGroup('', groupName, 'groupTable');
    }

    function editGroup() {
        var groupName = $("#editGroupName_New").val();
        var originalGroupName = $("#editGroupName").val();
        updateGroup(originalGroupName, groupName, 'editGroupTable');
    }

    function updateGroup(originalGroupName, groupName, groupTable)
    {
        SaveBenefits(false);
        var reportName = '@Model.ExportSolutionRpts.ReportName';
        var pwgList = [];
        var eachRow = "#" + groupTable + " tr";
        $(eachRow).each(function () {
            var dataRow = $(this).find('td');
            var grouped = dataRow.find("[id$='grp']").is(':checked');
            if (grouped) {
                pwg = { DatabaseFieldName: $(this).find("input[name$='DataBaseField']").val(), Description: $(this).find("input[name$='Description']").val() };
                pwgList.push(pwg);
            }
        });

        var sendData = JSON.stringify({ ReportName: reportName, GroupType: 'Benefits', OriginalGroupName: originalGroupName, GroupName: groupName, GroupedList: pwgList });

        $.ajax({
            url: '@Url.Action("CreateBenefitGroup", "ExportSolutions")',
            contentType: "application/json",
            type: "POST",
            data: sendData,
            success: function (data) {
                $("#selectData").html(data);
                $('#groupingModal').modal('hide');
                $('body').removeClass('modal-open');
                $('.modal-backdrop').remove();
            },
            error: function (errorData) {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    }

    $('.editGroup').on("click", function () {
        SaveBenefits(false);
        var displayName = $(this).attr('data-displayname');
        $('#editGroupName_New').val(displayName);
        $('#editGroupName').val(displayName);

        var sendData = JSON.stringify({ ReportName: displayName, BenefitCS: @Html.Raw(Json.Encode(Model.BenefitCS)), BenefitsGroup: @Html.Raw(Json.Encode(Model.BenefitsGroup)) });

        $.ajax({
            url: '@Url.Action("EditBenefitGroup", "ExportSolutions")',
            contentType: "application/json",
            type: "POST",
            data: sendData,
            success: function (data) {
                $("#editGroupSelection").html(data);
            },
            error: function (errorData) {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    });

    $('.deleteGroup').on("click", function () {
        SaveBenefits(false);
        var sendData = JSON.stringify({ groupName: $(this).attr('data-displayname'), reportName: $("#ReportName").val()});

        $.ajax({
            url: '@Url.Action("DeleteBenefitGroup", "ExportSolutions")',
            contentType: "application/json",
            type: "POST",
            data: sendData,
            success: function (data) {
                $("#selectData").html(data);
            },
            error: function (errorData) {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    });

    $(document).ready(function () {
        SetGroups();
        $(".csRow").each(function () {
            var exportCheckBox = $(this).find("input[id='export']");
            EnableBenefitsCSRow(!exportCheckBox.prop('checked'), exportCheckBox);
        });
    });

    function EnableBenefitsCSRow(isEnabled, exportCheckBox) {
        var row = exportCheckBox.closest('tr');
        row.find("[id$='DisplayName']").prop('disabled', isEnabled);
    }

    $("input[id$='DisplayName']").dblclick(function () {
        var e = $(this);
        var tempName = e.closest("tr").find("[id$='DataBaseField']").val();
        if (tempName === e.val()) { return false; }
        e.val(tempName);
        e.select();
        e.trigger("change");
    });

    // ungroup
    $("input:checkbox").on('change', function () {
        var thisName = $(this).attr('name');
        var lengthName = thisName.length;
        var indexOfName = thisName.indexOf('].') + 2;
        var inputName = thisName.substr(indexOfName, lengthName - indexOfName);
        var indexOfType = thisName.indexOf('[');
        var typeName = thisName.substr(0, indexOfType);
        var sectionTypeCS = typeName === "BenefitCS";

        switch (inputName) {
            case 'Export':
                var isChecked = $(this).is(':unchecked');
                if (sectionTypeCS) {
                    EnableBenefitsCSRow(isChecked, $(this));
                    if (!isChecked) {
                        $(this).closest("tr").find("[id$='GroupBy']").val(0);
                        var dbf = $(this).closest("tr").find("[id$='DataBaseField']").val();
                        if ($(this).closest("tr").find("[id$='DisplayName']").val() === "") {
                            $(this).closest("tr").find("[id$='DisplayName']").val(dbf);
                        }
                    } else {
                        $(this).closest("tr").find("[id$='DisplayName']").val('');
                    }
                } else {
                    // Basic Setup Option
                    if (!isChecked) {
                        var desc = $(this).closest('div').next().find("label").text();
                        if ($(this).closest('div.control-label').next().find('input').val() === "") {
                            $(this).closest('div.control-label').next().find('input').val(desc);
                        }
                    } else {
                        $(this).closest('div.control-label').next().find('input').val('');
                    }
                }
                break;
            default:
                break;
        }
    });

    $('#groupingModal').on('shown.bs.modal', function () {
        clearGroupingModal();
        $('#groupName').focus();
    });

    function clearGroupingModal() {
        $('#groupName').val('');

        var checkboxes = $("#groupTable").find("input:checkbox");
        for (var i = 0; i < checkboxes.length; i++) { checkboxes[i].checked = false; }
        $("#groupTable").find("input,button,textarea,select").prop("disabled", true);
    }

    $('#groupName').keyup(function () {
        var allowEnable = $(this).val() === "";
        $("#groupTable").find("input,button,textarea,select").prop("disabled", allowEnable);

        if (allowEnable)
            $("#requiredGroupName").show();
        else
            $("#requiredGroupName").hide();

    });

    $('#editGroupName_New').keyup(function () {
        var allowEnable = $(this).val() === "";
        $("#editGroupTable").find("input,button,textarea,select").prop("disabled", allowEnable);

        if (allowEnable)
            $("#requiredEditGroupName").show();
        else
            $("#requiredEditGroupName").hide();

    });
</script>
