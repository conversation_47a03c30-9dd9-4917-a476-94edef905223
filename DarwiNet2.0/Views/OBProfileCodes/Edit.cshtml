@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using OBCodeSetupType = DataDrivenViewEngine.Models.Core.enOBCodeSetupType
@using OBCodeSection = DataDrivenViewEngine.Models.Core.enOBCodeSections
@model DarwiNet2._0.Data.OBProfile

@{
    ViewBag.Title = "Edit Codes - " + @GlobalVariables.ProfileName;
    var mynewvalue = Model.ProfileID;
}

<script type="text/javascript">


    function setAssign(id) {
        document.getElementById('DestSection').value = id;
        document.getElementById('Index').submit();
    }
</script>
<div class="company-info">
    <div class="row" style="padding-bottom: 10px;">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-6 col-sm-6">
            <div class="text-right">

            </div>
        </div>
    </div>
</div>

@Html.Partial("~/Views/Navigation/_PartialTabs.cshtml", mynewvalue)
<div class="div-10"></div>
@using (Html.BeginForm("Edit", "OBProfileCodes", new { id = Model.ProfileID, section = ViewBag.Section }, FormMethod.Post, new { @id = "Index", @name = "Index" }))
{
    @*@Html.AntiForgeryToken()*@
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-4">
                @Html.LabelFor(model => model.ProfileName, htmlAttributes: new { @class = "control-label col-md-4" }, labelText: FieldTranslation.GetLabel("ProfileName", GlobalVariables.LanguageID))
                <div class="col-md-8">
                    @Html.TextBoxFor(model => model.ProfileName, new { @class = "form-control not-allowed", @readonly = true })
                </div>
            </div>
            <div class="col-md-5">
                @Html.LabelFor(model => model.CodeSetup, htmlAttributes: new { @class = "control-label col-md-4" }, labelText: FieldTranslation.GetLabel("CodeSetup", GlobalVariables.LanguageID))
                <div class="col-md-8">
                    @Html.DropDownList("CodeSetup", EnumHelper.GetSelectList(typeof(OBCodeSetupType)), new { id = "CodeSetup", name = "CodeSetup", @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.CodeSetup, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="col-md-3">
                <div id="canaddcodes">
                    <div class="setup-code-edit">
                        @Html.LabelFor(model => model.AllowCCAddCodes, htmlAttributes: new { @class = "control-label col-md-9 label-blue-small" }, labelText: FieldTranslation.GetLabel("AllowOutsideCodes", GlobalVariables.LanguageID))
                        <div class="col-md-3">
                            @Html.CheckBoxFor(model => model.AllowCCAddCodes, new { htmlAttributes = new { @class = "form-control", id="AllowCCAddCodes", name="AllowCCAddCodes" } })
                            @Html.ValidationMessageFor(model => model.AllowCCAddCodes, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="select-codes">
        <div class="col-md-12">

            <div class="form-horizontal">
                <div class="">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="">

                        <div class="col-md-4">

                            @Html.ValidationMessageFor(model => model.ProfileID, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div>
                        @Html.HiddenFor(model => model.ProfileID, new { htmlAttributes = new { @class = "form-control" } })
                        @*@Html.HiddenFor(model => model.ProfileID, new { htmlAttributes = new { @class = "form-control" } })*@
                        <input type="hidden" value="@ViewBag.Section" name="DestSection" id="DestSection" />
                    </div>
                </div>
                <div class="div-10"></div>

                <div class="row">
                    <p style="font-size: 22px;">Select:</p>
                    <div class="colored-line-left"></div>
                </div>
                <div class="col-md-12">
                    <div class="row">
                        <div class="button-nav form-horizontal">
                            <div class="form-group">
                                <div class="col-md-10">
                                    <button id="assigned1" onclick="setAssign(1);" style="width: 150px;" class="btn btn-outlined btn-gray">
                                        Pay Codes
                                    </button>
                                    <button id="assigned3" onclick="setAssign(3);" style="width: 150px;" class="btn btn-outlined btn-gray">Deduction Codes</button>

                                    <button id="assigned2" onclick="setAssign(2);" style="width: 150px;" class="btn btn-outlined btn-gray">Benefit Codes</button>
                                    <button id="assigned9" onclick="setAssign(9);" style="width: 150px;" class="btn btn-outlined btn-gray">Position Codes</button>
                                    <button id="assigned16" onclick="setAssign(16);" style="width: 150px;" class="btn btn-outlined btn-gray">Local Taxes</button>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-10">
                                    <button id="assigned10" onclick="setAssign(10);" style="width: 150px;" class="btn btn-outlined btn-gray">Department Codes</button>
                                    <button id="assigned4" onclick="setAssign(4);" style="width: 150px;" class="btn btn-outlined btn-gray">WC Codes</button>

                                    <button id="assigned5" onclick="setAssign(5);" style="width: 150px;" class="btn btn-outlined btn-gray">SUTA States</button>
                                    <button id="assigned7" onclick="setAssign(7);" style="width: 150px;" class="btn btn-outlined btn-gray">License Types</button>
                                    <button id="assigned18" onclick="setAssign(18);" style="width: 150px;" class="btn btn-outlined btn-gray">State Taxes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row client-setup-codes-select">
                <div class="col-md-12">
                    <div style="clear: both;"></div>

                    <div class="col-md-5">

                        <label>Available @Enum.GetName(typeof(OBCodeSection), (int)ViewBag.Section).ToString() Codes</label><br />
                        <select multiple id="select1" class="col-md-10 col-xs-12" style="height: 400px;">
                            @{
    foreach (var client in ViewBag.AvailableCodes)
    {
        <option value="@client.Code">@client.Code - @client.Description</option>
    }
                            }
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="chevron-select">
                            <div class="chevron-add-bottom">
                                <div style="padding-bottom: 15px;">
                                    <a href="#" title="Add" id="add"><i class="icon-thinkware fa fa-arrow-right fa-3x"></i></a>
                                </div>
                                <div>
                                    <a href="#" title="Add All" id="addAll"><i class="add-codes-arrow icon-thinkware fa fa-arrow-right fa-3x"></i></a>
                                </div>

                            </div>
                            <div>
                                <div style="padding-bottom: 15px;">
                                    <a href="#" title="Remove" id="remove"><i class="icon-thinkware fa fa-arrow-left fa-3x"></i></a>
                                </div>
                                <div>
                                    <a href="#" title="Remove All" id="removeAll"><i class="add-codes-arrow icon-thinkware fa fa-arrow-left fa-3x"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">

                        <label>Selected @Enum.GetName(typeof(OBCodeSection), (int)ViewBag.Section).ToString() Codes</label><br />
                        <select multiple id="Selected" name="Selected" class="col-md-10 col-xs-12" style="height: 400px;">
                            @{
                                foreach (var client in ViewBag.SelectedCodes)
                                {
                                    <option value="@client">@client</option>
                                }
                            }
                        </select>
                        <input type="hidden" name="selectedItems" id="selectedItems" value="" />
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="row">
        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    @Html.ActionLink("Return to Profiles", "Index", "OBProfile", null, new { @class = "btn btn-thinkware" })
                    <input type="submit" value="Save Changes" class="btn btn-thinkware" />
                </div>
            </div>
        </div>
    </div>
}


@section scripts{
    <script>
        $().ready(function () {
            $('#add').click(function () {
                return !$('#select1 option:selected').remove().appendTo('#Selected');
            });
            $('#addAll').click(function () {
                return !$('#select1 option').remove().appendTo('#Selected');
            });
            $('#remove').click(function () {
                return !$('#Selected option:selected').remove().appendTo('#select1');
            });
            $('#removeAll').click(function () {
                return !$('#Selected option').remove().appendTo('#select1');
            });
            $('form').submit(function () {
                setSelect();
            });
            function setSelect() {
                var arr = [];
                $("#Selected > option").each(function () {
                    arr.push(this.value);
                });
                var str = arr.join(',');
                $('#selectedItems').val(str);
            }
        });
    </script>
    <script>
        $(document).ready(function () {
            $("#CodeSetup").on("change", function () {
                if ($(this).val() == 2) {
                    $('#canaddcodes').show();
                } else {
                    $('#canaddcodes').hide(); /* If you want to be hidden if it's not */
                }
            }).trigger("change");

        })
    </script>
    <script>
        $(document).ready(function () {
            $("#CodeSetup").on("change", function () {
                if ($(this).val() == 2) {
                    $('#select-codes').show();
                } else {
                    $('#select-codes').hide(); /* If you want to be hidden if it's not */
                }
            }).trigger("change");

        })
    </script>

    <script>
        $(document).ready(function () {
            var assign = document.getElementById("DestSection").value;
            $("#assigned" + assign).removeClass("btn-gray")
                    .addClass("btn-thinkware");
            $(".assigned-button").click(function () {
                $(this).toggleClass("btn-gray btn-thinkware");
                $(".assigned-button").not(this)
                    .removeClass("btn-thinkware")
                    .addClass("btn-gray");
            });
        });

    </script>

    <script>
        $("#Index").validate();
    </script>
}