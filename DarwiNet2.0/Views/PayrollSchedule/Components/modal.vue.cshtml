<style type="text/css" id="modal-style">
    .modal-mask {
        position: fixed;
        z-index: 9998;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: table;
        transition: opacity 0.3s ease;
    }

    .modal-wrapper {
        display: table-cell;
        vertical-align: middle;
    }

    .modal-container {
        width: 600px;
        margin: 0px auto;
        background-color: #fff;
        border-radius: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
        transition: all 0.3s ease;
        font-family: Helvetica, Arial, sans-serif;
        border-radius: 1.618%;
    }

    .modal-header {
        margin-top: 0;
    }

    .modal-body {
        margin: 20px 0;
    }

    .modal-default-button {
        float: right;
    }

    /*
    * The following styles are auto-applied to elements with
    * transition="modal" when their visibility is toggled
    * by Vue.js.
    *
    * You can easily play with the modal transition by editing
    * these styles.
    */

    .modal-enter {
        opacity: 0;
    }

    .modal-leave-active {
        opacity: 0;
    }

        .modal-enter .modal-container,
        .modal-leave-active .modal-container {
            -webkit-transform: scale(1.1);
            transform: scale(1.1);
        }
</style>
<script type="text/x-template" id="modal-template">
    <transition name="modal">
        <div class="modal-mask">
            <div class="modal-wrapper">
                <div class="modal-container">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <button type="button" class="close" aria-label="Close" @@click="onCloseClick"><span aria-hidden="true">&times;</span></button>
                            <slot name="header">
                            </slot>
                        </div>
                        <div class="panel-body">
                            <div class="modal-body">
                                <slot name="body">
                                </slot>
                            </div>

                            <div class="modal-footer">
                                <slot name="footer">

                                </slot>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</script>
<script type="text/javascript" id="modal-script">
    var ModalComponent = VueComponent('modal', {
        props: {

        },
        data: function () {
            return {
                events: {
                    closeClick: 'close-click'
                }
            }
        },
        methods: {
            onCloseClick: function () {
                var self = this;

                self.$emit(self.events.closeClick);
            }
        }
    });

</script>
