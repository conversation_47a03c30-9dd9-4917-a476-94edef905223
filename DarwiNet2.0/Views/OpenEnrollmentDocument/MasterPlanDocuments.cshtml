@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model IEnumerable<DarwiNet2._0.ViewModels.OpenEnrollment.SetupPlanVM>
@using DataDrivenViewEngine.Models.Core
@{
    ViewBag.Title = "Master Plan Documents";
}

<style type="text/css">
    .k-grid {
        overflow-x: auto;
        overflow-y: hidden;
        min-height: 400px;
    }

    .k-grid-content {
        height: 365px !important;
    }
</style>
<p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
<div class="colored-line-left"></div>

<div class="form-group" id="errorplan" style="display: none">
    <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" id="btnclose" class="close"><span aria-hidden="true">&times;</span></button>
        <div id="errResponsepln"></div>
    </div>
</div>
@if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
{
    <div class="toolbar" style="padding-top: 5px;">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <div class="col-md-6 yeardrp">
                        <b>Year<span style="color:red">*</span> :</b>
                        <select id="drpYear" name="drpYear" class="form-control"></select>
                    </div>
                    @*<div class="col-md-6">
                            <b>Plan Type<span style="color:red">*</span> :</b> &nbsp;
                            <select id="drpPlanType" name="drpPlanType" class="form-control"></select>
                        </div>*@
                    <div class="col-md-2 col-xs-12">
                        <button type="button" id="btnsearch" class="btn btn-thinkware pull-left" style="margin-top:22px;">Search</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <div class="toolbar" style="padding-top: 5px;">
        <div class="row">
            <div class="col-md-3 pull-left">

            </div>
        </div>
    </div>
}
<div class="row" style="padding-top: 5px;">
    <div class="col-md-12">
        @(Html.Kendo().Grid<DarwiNet2._0.ViewModels.OpenEnrollment.SetupPlanVM>()
                    .Name("PlansGrid")
            .Columns(c =>
            {
                c.Bound(u => u.YEAR1).Title(@FieldTranslation.GetLabel("Year", GlobalVariables.LanguageID)).Width(120);
                c.Bound(u => u.PlanType).Title(@FieldTranslation.GetLabel("PlanType", GlobalVariables.LanguageID)).Width(150);
                //c.Bound(u => u.PlanName).Title(@FieldTranslation.GetLabel("Plan Name", GlobalVariables.LanguageID)).Width(250);
                c.Bound("").ClientTemplate("<a href='" + Url.Action("MasterPlanIndex", "OpenEnrollmentDocument") + "?year=#=YEAR1#&plantype=#=PlanType#'><i class='fa fa-file-o fa-fw fa-lg'></i></a>").Title("Attach Documents").Width(180);
                //c.Bound(u => u.WaitingPeriod).Title(@FieldTranslation.GetLabel("Documents Count", GlobalVariables.LanguageID)).Width(180);
             })
            .Sortable(s => s.Enabled(true))
            .Pageable()
            .Filterable(f => f.Enabled(true))
            .Scrollable(s => s.Enabled(true))
            .Groupable(g => g.Enabled(true))
            .Resizable(r => r.Columns(true))
            .DataSource(d => d
            .Ajax()
            .PageSize(10)
            //.Read(read => read.Action("SetupPlans", "OpenEnrollment"))
            .ServerOperation(false)
            )
        )

    </div>
</div>

<script src="~/Scripts/jquery.datetimepicker.js"></script>
<link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
<script src="~/Scripts/jquery.cookie.min.js"></script>
<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
<script type="text/javascript">

    $(document).ready(function () {
        BindYearData();
        if ('@ViewBag.Year' != '')
        {
            $('#drpYear').val('@ViewBag.Year');
            FilterPlansData('@ViewBag.Year');
        }
        $('#btnsearch').click(function (e) {
            e.preventDefault();
            var year = $('#drpYear').val();
            FilterPlansData(year);
        });
    });
    function FilterPlansData(year) {
        if (year > 0) {
                $.ajax({
                    type: "POST",
                    url: "/OpenEnrollmentDocument/GetPlanHDRDetails",
                    data: JSON.stringify({ Year: year }),
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (data) {
                        $('#errorplan').hide();
                        $("#PlansGrid").data("kendoGrid").dataSource.data(data);
                    }
                });
        }
        else {
            $('#errorplan').show();
            $('#errResponsepln').text("Please select the Year");
        }
    }
    function BindYearData() {
        $.ajax({
            type: "POST",
            async: false,
            url: "/OpenEnrollmentDocument/GetYearsData",
            data: JSON.stringify({}),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (data) {
                $('#drpYear').empty();
                $('#drpYear').append('<option value="0"></option>');
                for (var i = 0; i < data.length; i++) {
                    $('#drpYear').append('<option value="' + data[i] + '">' + data[i] + '</option>');
                }
                $("#PlansGrid").data("kendoGrid").dataSource.data([]);
            }
        });
    }

    $("#btnclose").click(function () {
        $("#errorplan").hide();
    });
</script>




