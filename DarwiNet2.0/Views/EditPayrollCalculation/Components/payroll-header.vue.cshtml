<style type="text/css">
</style>
<script type="text/x-template" id="payroll-header-template">
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>Process Date</th>
                    <th>Check Date</th>
                    <th>Pay Period</th>
                    <th>Processor</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ processDate }}</td>
                    <td>{{ displayCheckDate }}</td>
                    <td>{{ beginDate }}  -  {{ endDate }}</td>
                    <td>{{ processor }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</script>

<script type="text/javascript">
    const PayrollHeaderComponent = {
        selector: "payroll-header",
        template: "#payroll-header-template",
        props: {
            viewModel: {
                type: Object,
                default: () => ({})
            },
            checkDate: {
                type: String,
                default: ""
            },
        },
        data: function () {
            return {
                displayCheckDate: this.formatDate(this.checkDate),
                beginDate: this.formatDate(this.viewModel.BeginDate),
                endDate: this.formatDate(this.viewModel.EndDate),
                processor: this.viewModel.Processor,
                processDate: this.formatDate(this.viewModel.ProcessDate)
            }
        },
        methods: {
            formatDate: function (datetimeString) {
                return ThinkwareCommon.dateFormat(datetimeString, '@System.Configuration.ConfigurationManager.AppSettings["TimezoneID"]');
            }
        },
        components: {
        }
    };
</script>