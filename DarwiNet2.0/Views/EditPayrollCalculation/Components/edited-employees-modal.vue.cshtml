@using DarwiNet2._0.Extensions;

<style type="text/css" id="pending-changes-modal-style" scoped>
    .alignRight {
        text-align: right;
    }

    .modal-mask {
        position: fixed;
        z-index: 9998;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: table;
        transition: opacity 0.3s ease;
    }

    .modal-wrapper {
        display: table-cell;
        vertical-align: middle;
    }

    .pending-modal-container {
        width: 50%;
        min-width: 500px;
        margin: 0px auto;
        background-color: #fff;
        border-radius: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
        transition: all 0.3s ease;
        font-family: Helvetica, Arial, sans-serif;
    }

    .add-modal-body {
        width: 100%;
        margin: 0 !important;
        padding: 0 !important;
    }

    .modal-enter {
        opacity: 0;
    }

    .modal-leave-active {
        opacity: 0;
    }

        .modal-enter .add-modal-container,
        .modal-leave-active .add-modal-container {
            -webkit-transform: scale(1.1);
            transform: scale(1.1);
        }

    .fieldError {
        border-color: red;
    }

    .voidChecksTableText {
        font-size: 14px;
    }

    .tableOverflow {
        max-height: 300px;
        overflow: auto;
    }

    .deleteColumn {
        width: 40px;
    }

    .text-ellipsis {
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    .option-loading-overlay {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        background-color: rgba(0,0,0,.05);
        border-radius: 5px;
    }

    .table.table-hover:hover {
        cursor: pointer;
    }
</style>

<script type="text/x-template" id="edited-employees-modal-template">
    <transition name="modal">
        <div class="modal-mask" tabindex="0" @@keydown.escape="escapePressed" id="PendingChangesModal">
            <div class="modal-wrapper">
                <div class="pending-modal-container">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <table style="width: 100%">
                                <tr>
                                    <td>
                                        <strong>Edited Employees</strong>
                                    </td>
                                    <td class="text-right">
                                        <button type="button" class="close" :disabled="loading" @@click="escapePressed()">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="panel-body">
                            <p class="margin-bottom-15">These are the employees with the most recently edited payroll calculations.</p>
                            <div class="tableOverflow">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="col-sm-2">Employee ID</th>
                                            <th class="col-sm-2">First Name</th>
                                            <th class="col-sm-2">Last Name</th>
                                            <th class="col-sm-2">Gross Wages</th>
                                            <th class="col-sm-2">Net Wages</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="employee in editedEmployees" @@click="employeeClicked(payrollNumber, employee)">
                                            <td class="col-sm-2">{{employee.EmployeeId}}</td>
                                            <td class="col-sm-2">{{employee.FirstName}}</td>
                                            <td class="col-sm-2">{{employee.LastName}}</td>
                                            <td class="col-sm-2">${{formatWages(employee.GrossWages)}}</td>
                                            <td class="col-sm-2">${{formatWages(employee.NetWages)}}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer justify-end">
                            <div class="justify-end">
                                <button :class="['btn', loading ? 'btn-bg' : 'btn-danger']" v-on:click="loading ? null : escapePressed()">
                                    Close
                                </button>
                                @*<button :class="['btn', loading ? 'btn-bg' : 'btn-success']" :disabled="loading" v-on:click="loading ? null : onSaveClicked()">
                                    <i v-if="loading" class="fa fa-circle-o-notch rotating"></i>
                                    {{loading ? "Saving Changes" : "Save Changes"}}
                                </button>*@
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</script>

<script type="text/javascript" id="edited-employees-modal-script">
    var EditedEmployeesModalComponent = VueComponent('edited-employees-modal', {
        props: {
            payrollNumber: {
                type: String,
                required: true,
                default: "",
            },
            editedEmployees: {
                type: Array,
                required: true,
                default: () => ([]),
            },
            loading: {
                type: Boolean,
                required: true,
                default: false,
            },
        },
        data: function () {
            return {
                events: {
                    escapePressed: "escape-pressed",
                },
            };
        },
        watch: {
            editedEmployees: function (value) {
                this.editedEmployees = value;
            },
            loading: function (value) {
                this.loading = value;
            },
        },
        methods: {
            escapePressed: function () {
                this.$emit(this.events.escapePressed);
            },
            employeeClicked: function (payrollNumber, employee) {
                let employeeID = employee.EmployeeId;
                window.location = '@Url.Action("Edit", "EditPayrollCalculation", new { id = UrlParameter.Optional })/' + `${employeeID}/?payrollNumber=${payrollNumber}`;
            },
            formatWages: function (value) {
                return value.toFixed(2);
            }
        }

    })
</script>