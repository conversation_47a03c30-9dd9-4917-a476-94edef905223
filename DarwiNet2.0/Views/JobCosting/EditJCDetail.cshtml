@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@model DarwiNet2._0.Data.ClientJobCostAssignmentDetail
@{
    ViewBag.Title = "Edit Job Cost Detail";
    ViewBag.ParentCrumb = "Company,Information";
}
<style>
    .panel-heading .accordion-toggle:after {
        /* symbol for "opening" panels */
        font-family: 'Glyphicons Halflings'; /* essential for enabling glyphicon */
        content: "\e114"; /* adjust as needed, taken from bootstrap.css */
        float: right; /* adjust as needed */
        color: #fff; /* adjust as needed */
    }

    .panel-heading .accordion-toggle.collapsed:after {
        /* symbol for "collapsed" panels */
        content: "\e080"; /* adjust as needed, taken from bootstrap.css */
    }
</style>
<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>

    </div>
</div>
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    using (Html.BeginForm("EditJCDetail", "JobCosting", FormMethod.Post, new { id = "createJCDetail" }))
    {
        <div class="form-horizontal">
            @Html.AntiForgeryToken()
            

            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @Html.HiddenFor(model => model.CompanyID, new { @class = "form-control" })
            @Html.HiddenFor(model => model.ClientID, new { @class = "form-control" })

            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-thinkware">
                    <div class="panel-heading" role="tab" id="headingOne">
                        <h4 class="panel-title">
                            <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseOne" aria-expanded="true" aria-controls="collapseOne" class="generalSettings accordion-toggle">
                                General Settings
                            </a>
                        </h4>
                    </div>
                    <div id="collapseOne" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne">
                        <div class="panel-body">
                            <div class="form-group">
                                @Html.LabelFor(model => model.JobCostingName, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobCostingName", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.JobCostingName, new {@class = "form-control field-required", @readonly = "readonly"})
                                    @Html.ValidationMessageFor(model => model.JobCostingName, "", new {@class = "text-danger"})
                                </div>
                                @Html.LabelFor(model => model.JBSID, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JBSID", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="JBSID" name="JBSID">
                                        @{
                                            if (ViewBag.JBSCodes != null)
                                            {
                                                foreach (Code_Description codes in ViewBag.JBSCodes)
                                                {
                                                    <option value="@codes.Code" @((@Model.JBSID == codes.Code) ? "selected" : string.Empty)>@codes.Description</option>
                                                }
                                            }
                                        }
                                    </select>
                                    @Html.ValidationMessageFor(model => model.JBSID, "", new {@class = "text-danger"})
                                </div>

                            </div>
                            <div class="form-group">

                                @Html.LabelFor(model => model.SequenceNumber, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("SequenceNumber", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.SequenceNumber, new {@class = "form-control"})
                                    @Html.ValidationMessageFor(model => model.SequenceNumber, "", new {@class = "text-danger"})
                                </div>
                                @Html.LabelFor(model => model.EstimatedStartDate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("EstimatedStartDate", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.EditorFor(model => model.EstimatedStartDate, new {@class = "form-control"})
                                    @Html.ValidationMessageFor(model => model.EstimatedStartDate, "", new {@class = "text-danger"})
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="pull-right">
                                        <a href="@Url.Action("JCDetailsIndex", "JobCosting", new {client = GlobalVariables.Client, job = @Model.JobCostingName})" value="Save" class="btn btn-thinkware">Cancel</a>
                                        <input type="submit" value="Save" class="btn btn-thinkware" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel panel-thinkware">
                    <div class="panel-heading" role="tab" id="headingTwo">
                        <h4 class="panel-title">
                            <a class="collapsed accordion-toggle" role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                Job Levels
                            </a>
                        </h4>
                    </div>
                    <div id="collapseTwo" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingTwo">
                        <div class="panel-body">
                            <div class="form-group">
                                @Html.LabelFor(model => model.JobLevel2, htmlAttributes: new {@class = "control-label col-md-2"}
                                    , labelText: FieldTranslation.GetLabel("JobLevel2", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.JobLevel2, new {@class = "form-control", @readonly = "readonly"})
                                    @Html.ValidationMessageFor(model => model.JobLevel2, "", new {@class = "text-danger"})
                                </div>
                                @Html.LabelFor(model => model.Description2, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description2", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.Description2, new {@class = "form-control"})
                                    @Html.ValidationMessageFor(model => model.Description2, "", new {@class = "text-danger"})
                                </div>


                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.JobLevel3, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobLevel3", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.JobLevel3, new {@class = "form-control", @readonly = "readonly"})
                                    @Html.ValidationMessageFor(model => model.JobLevel3, "", new {@class = "text-danger"})
                                </div>
                                @Html.LabelFor(model => model.Description3, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description3", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.Description3, new {@class = "form-control"})
                                    @Html.ValidationMessageFor(model => model.Description3, "", new {@class = "text-danger"})
                                </div>


                            </div>



                            <div class="form-group">
                                @Html.LabelFor(model => model.JobLevel4, htmlAttributes: new {@class = "control-label col-md-2"}
                                    , labelText: FieldTranslation.GetLabel("JobLevel4", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.JobLevel4, new {@class = "form-control", @readonly = "readonly"})
                                    @Html.ValidationMessageFor(model => model.JobLevel4, "", new {@class = "text-danger"})
                                </div>
                                @Html.LabelFor(model => model.Description4, htmlAttributes: new {@class = "control-label col-md-2"}
                                    , labelText: FieldTranslation.GetLabel("Description4", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.Description4, new {@class = "form-control"})
                                    @Html.ValidationMessageFor(model => model.Description4, "", new {@class = "text-danger"})
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.JobLevel5, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobLevel5", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.JobLevel5, new {@class = "form-control", @readonly = "readonly"})
                                    @Html.ValidationMessageFor(model => model.JobLevel5, "", new {@class = "text-danger"})
                                </div>
                                @Html.LabelFor(model => model.Description5, htmlAttributes: new {@class = "control-label col-md-2"}
                                    , labelText: FieldTranslation.GetLabel("Description5", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.Description5, new {@class = "form-control"})
                                    @Html.ValidationMessageFor(model => model.Description5, "", new {@class = "text-danger"})
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.JobLevel6, htmlAttributes: new {@class = "control-label col-md-2", @readonly = "readonly"}, labelText: FieldTranslation.GetLabel("JobLevel6", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.JobLevel6, new {@class = "form-control", @readonly = "readonly"})
                                    @Html.ValidationMessageFor(model => model.JobLevel6, "", new {@class = "text-danger"})
                                </div>

                                @Html.LabelFor(model => model.Description6, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description6", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.Description6, new {@class = "form-control"})
                                    @Html.ValidationMessageFor(model => model.Description6, "", new {@class = "text-danger"})
                                </div>

                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="pull-right">
                                        <a href="@Url.Action("JCDetailsIndex", "JobCosting", new {client = GlobalVariables.Client, job = @Model.JobCostingName})" value="Save" class="btn btn-thinkware">Cancel</a>
                                        <input type="submit" value="Save" class="btn btn-thinkware" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel panel-thinkware">
                    <div class="panel-heading" role="tab" id="headingThree">
                        <h4 class="panel-title">
                            <a class="collapsed accordion-toggle" role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                Additional Settings
                            </a>
                        </h4>
                    </div>
                    <div id="collapseThree" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingThree">
                        <div class="panel-body">
                            <div class="form-group">
                                @Html.LabelFor(model => model.EstimatedCompletion, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EstimatedCompletion", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.EditorFor(model => model.EstimatedCompletion, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.EstimatedCompletion, "", new { @class = "text-danger" })
                                </div>
                                @Html.LabelFor(model => model.EstimatedHours, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EstimatedHours", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @*Html.TextBoxFor(model => model.EstimatedHours, new { @class = "form-control" })*@
                                    <input type="text" style="width: 100%" value="@FieldTranslation.EstimatedHours(Html.DisplayFor(model => model.EstimatedHours).ToString())" id id="EstimatedHours" name="EstimatedHours" />
                                    <div class="error-msg"></div>
                                    @Html.ValidationMessageFor(model => model.EstimatedHours, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.Cost, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Cost", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <input id="Cost" name="Cost" class="form-control currency" value="@String.Format("{0:n2}", Model.Cost)" />
                                    @Html.ValidationMessageFor(model => model.Cost, "", new { @class = "text-danger" })
                                </div>
                                @Html.LabelFor(model => model.ProjectManager, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("ProjectManager", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.ProjectManager, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.ProjectManager, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.Phone1, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Phone1", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.Phone1, new { @class = "form-control phone-type-masking" })
                                    @Html.ValidationMessageFor(model => model.Phone1, "", new { @class = "text-danger" })
                                </div>
                                @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    @Html.TextBoxFor(model => model.Email, new { @class = "form-control" })
                                    @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.Department, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="Department" name="Department">
                                        @{
                                            if (ViewBag.Departments != null)
                                            {
                                                foreach (Code_Description dept in ViewBag.Departments)
                                                {
                                                    <option value="@dept.Code" @((@Model.Department == dept.Code) ? "selected" : string.Empty)>@dept.Description</option>
                                                }
                                            }
                                        }
                                    </select>
                                    @Html.ValidationMessageFor(model => model.Department, "", new {@class = "text-danger"})
                                </div>

                                @Html.LabelFor(model => model.WorkersComp, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Workers Compensation", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="WorkersComp" name="WorkersComp">
                                        @{
                                            if (ViewBag.WorkersComps != null)
                                            {
                                                foreach (Code_Description dept in ViewBag.WorkersComps)
                                                {
                                                    <option value="@dept.Code" @((@Model.WorkersComp == dept.Code) ? "selected" : string.Empty)>@dept.Description</option>
                                                }
                                            }
                                        }
                                    </select>
                                    @Html.ValidationMessageFor(model => model.WorkersComp, "", new {@class = "text-danger"})
                                </div>

                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="pull-right">
                                        <a href="@Url.Action("JCDetailsIndex", "JobCosting", new {client = GlobalVariables.Client, job = @Model.JobCostingName})" value="Save" class="btn btn-thinkware">Cancel</a>
                                        <input type="submit" value="Save" class="btn btn-thinkware" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel panel-thinkware">
                    <div class="panel-heading" role="tab" id="headingFour">
                        <h4 class="panel-title">
                            <a class="collapsed accordion-toggle" role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseFour" aria-expanded="false" aria-controls="collapseThree">
                                Codes and Pay Rates
                            </a>
                        </h4>
                    </div>
                    <div id="collapseFour" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingFour">
                        <div class="panel-body">
                            <div class="form-group">
                                @Html.LabelFor(model => model.DefaultHourlyCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultHourlyCode", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="DefaultHourlyCode" name="DefaultHourlyCode">
                                        @{
        if (ViewBag.DefHourlyCodes != null)
        {
            foreach (Code_Description hourly in ViewBag.DefHourlyCodes)
            {
                        <option value="@hourly.Code" @((@Model.DefaultHourlyCode == hourly.Code) ? "selected" : string.Empty)>@hourly.Description</option>
            }
        }
                                        }
                                    </select>

                                    @Html.ValidationMessageFor(model => model.DefaultHourlyCode, "", new { @class = "text-danger" })
                                </div>
                                @Html.LabelFor(model => model.DefaultOTCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultOTCode", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="DefaultOTCode" name="DefaultOTCode">
                                        @{
        if (ViewBag.DefHourlyOTCodes != null)
        {
            foreach (Code_Description hourly in ViewBag.DefHourlyOTCodes)
            {
                        <option value="@hourly.Code" @((@Model.DefaultOTCode == hourly.Code) ? "selected" : string.Empty)>@hourly.Description</option>
            }
        }
                                        }
                                    </select>

                                    @Html.ValidationMessageFor(model => model.DefaultOTCode, "", new { @class = "text-danger" })
                                </div>



                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.DefaultSalaryCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultSalaryCode", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="DefaultSalaryCode" name="DefaultSalaryCode">
                                        @{
        if (ViewBag.DefSalaryCodes != null)
        {
            foreach (Code_Description salary in ViewBag.DefSalaryCodes)
            {
                        <option value="@salary.Code" @((@Model.DefaultSalaryCode == salary.Code) ? "selected" : string.Empty)>@salary.Description</option>
            }
        }
                                        }
                                    </select>

                                    @Html.ValidationMessageFor(model => model.DefaultSalaryCode, "", new { @class = "text-danger" })
                                </div>
                                @Html.LabelFor(model => model.DefaultSalOTCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultSalOTCode", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="DefaultSalOTCode" name="DefaultSalOTCode">
                                        @{
        if (ViewBag.DefSalaryOTCodes != null)
        {
            foreach (Code_Description salary in ViewBag.DefSalaryOTCodes)
            {
                        <option value="@salary.Code" @((@Model.DefaultSalOTCode == salary.Code) ? "selected" : string.Empty)>@salary.Description</option>
            }
        }
                                        }
                                    </select>

                                    @Html.ValidationMessageFor(model => model.DefaultSalOTCode, "", new { @class = "text-danger" })
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.StateCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("StateCode", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="StateCode" name="StateCode">
                                        @{
        if (ViewBag.StateCodes != null)
        {
            foreach (Code_Description state in ViewBag.StateCodes)
            {
                        <option value="@state.Code" @((@Model.StateCode == state.Code) ? "selected" : string.Empty)>@state.Description</option>
            }
        }
                                        }
                                    </select>
                                    @Html.ValidationMessageFor(model => model.StateCode, "", new { @class = "text-danger" })
                                </div>
                                @Html.LabelFor(model => model.LocalTax, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("LocalTax", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <select class="form-control" id="LocalTax" name="LocalTax">
                                        @{
        if (ViewBag.LocalTax != null)
        {
            foreach (Code_Description local in ViewBag.LocalTax)
            {
                        <option value="@local.Code" @((@Model.LocalTax == local.Code) ? "selected" : string.Empty)>@local.Description</option>
            }
        }
                                        }
                                    </select>
                                    @Html.ValidationMessageFor(model => model.LocalTax, "", new { @class = "text-danger" })
                                </div>
                            </div>



                            <div class="form-group">
                                @Html.LabelFor(model => model.RegularRate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("RegularRate", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <input id="RegularRate" name="RegularRate" class="form-control currency" value="@String.Format("{0:n2}", Model.RegularRate)" />
                                    @Html.ValidationMessageFor(model => model.RegularRate, "", new { @class = "text-danger" })
                                </div>
                                @Html.LabelFor(model => model.OTRate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("OTRate", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <input id="OTRate" name="OTRate" class="form-control currency" value="@String.Format("{0:n2}", Model.OTRate)" />
                                    @Html.ValidationMessageFor(model => model.OTRate, "", new { @class = "text-danger" })
                                </div>



                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.DefaultSalaryRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DefaultSalaryRate", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <input id="DefaultSalaryRate" name="DefaultSalaryRate" class="form-control currency" value="@String.Format("{0:n2}", Model.DefaultSalaryRate)"/>
                                    @Html.ValidationMessageFor(model => model.DefaultSalaryRate, "", new {@class = "text-danger"})
                                </div>
                                @Html.LabelFor(model => model.DefaultSalaryOTRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DefaultSalaryOTRate", GlobalVariables.LanguageID))
                                <div class="col-md-4">
                                    <input id="DefaultSalaryOTRate" name="DefaultSalaryOTRate" class="form-control currency" value="@String.Format("{0:n2}", Model.DefaultSalaryOTRate)"/>
                                    @Html.ValidationMessageFor(model => model.DefaultSalaryOTRate, "", new {@class = "text-danger"})
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="pull-right">
                                        <a href="@Url.Action("JCDetailsIndex", "JobCosting", new {client = GlobalVariables.Client, job = @Model.JobCostingName})" value="Save" class="btn btn-thinkware">Cancel</a>
                                        <input type="submit" value="Save" class="btn btn-thinkware" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        

            

           

            

            



            
        </div>
    }
}
        @if (ViewBag.Access == MenuAccessLevel.ReadOnly)
{
            <script>
                $(document).ready(function () {
                    $('#createJCDetail input').attr('disabled', true);
                    $('#createJCDetail select').attr('disabled', true);
                })
            </script>
}
        @section scripts{

            <script src="~/Scripts/MaskedInput.js"></script>
            <script>
                $(document).ready(function() {
                    $(".phone-type-masking").mask("(*************");
                });
                var form = $("#createJCDetail");
                form.validate();
                $("input[type=submit]").click(function() {
                    if (form.valid() == true) {
                        $("#createJCDetail").submit(function() {
                            $(".phone-type-masking").mask("9999999999");
                        });
                    }
                });
            </script>
            <script>
                //clear cascade dropdowns if department chagnes
                jQuery.ajaxSetup({async:false});
                $('#Department').change(function() {
                    newCascades();
                });
                function newCascades() {
                    //get workers comp codes
                    $('#WorkersComp').find('option').remove().end();
                    var compdept = $('#Department').val();
                    var compclient = @ViewBag.ClientID;
                    var compyurl = "@Url.Action("GetCascadeWC", "JobCosting")";
                    compyurl = compyurl + "?c=" + compclient + "&d=" + compdept;
                    $.post(compyurl, function (data) {
                        $.each(data, function (i, item) {
                            $('#WorkersComp').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                    //get new hourly codes
                    $('#DefaultHourlyCode').find('option').remove().end();
                    var hourlydept = $('#Department').val();
                    var hourlyclient = @ViewBag.ClientID;
                    var hourlyurl = "@Url.Action("GetCascadeHourlyOTCodes", "JobCosting")";
                    hourlyurl = hourlyurl + "?c=" + hourlyclient + "&d=" + hourlydept;
                    $.post(hourlyurl, function (data) {
                        $.each(data, function (i, item) {
                            $('#DefaultHourlyCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                    //get new salary codes
                    $('#DefaultSalaryCode').find('option').remove().end();
                    var salarydept = $('#Department').val();
                    var salaryclient = @ViewBag.ClientID;
                    var salaryurl = "@Url.Action("GetCascadeSalaryCodes", "JobCosting")";
                    salaryurl = salaryurl + "?c=" + salaryclient + "&d=" + salarydept;
                    $.post(salaryurl, function (data) {
                        $.each(data, function (i, item) {
                            $('#DefaultSalaryCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                    //get new hourly OT codes
                    $('#DefaultOTCode').find('option').remove().end();
                    var val = $('#DefaultHourlyCode').val();
                    var dept = $('#Department').val();
                    var client = @ViewBag.ClientID;
                    var url = "@Url.Action("GetCascadeHourlyOTCodes", "JobCosting")";
                    url = url + "?c=" + client + "&d=" + dept+ "&cd=" + val;
                    $.post(url, function (data) {
                        $.each(data, function (i, item) {
                            $('#DefaultOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                    //get new salary OT Codes
                    $('#DefaultSalaryOTCode').find('option').remove().end();
                    var val1 = $('#DefaultSalaryCode').val();
                    var dept1 = $('#Department').val();
                    var client1 = @ViewBag.ClientID;
                    var url1 = "@Url.Action("GetCascadeSalaryOTCodes", "JobCosting")";
                    url1 = url1 + "?c=" + client1 + "&d=" + dept1 + "&cd=" + val1;
                    $.post(url1, function (data) {
                        $.each(data, function (i, item) {
                            $('#DefaultSalaryOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                    //get state code
                    $('#StateCode').find('option').remove().end();
                    var statedept = $('#Department').val();
                    var stateclient = @ViewBag.ClientID;
                    var stateurl = "@Url.Action("GetCascadeStateTax", "JobCosting")";
                    stateurl = stateurl + "?c=" + stateclient + "&d=" + statedept;
                    $.post(stateurl, function (data) {
                        $.each(data, function (i, item) {
                            $('#StateCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                    //get local tax
                    $('#LocalTax').find('option').remove().end();
                    var localdept = $('#Department').val();
                    var localclient = @ViewBag.ClientID;
                    var localurl = "@Url.Action("GetCascadeLocalTax", "JobCosting")";
                    localurl = localurl + "?c=" + localclient + "&d=" + localdept;
                    $.post(localurl, function (data) {
                        $.each(data, function (i, item) {
                            $('#LocalTax').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                }
                //get default ot code on change
                $('#DefaultHourlyCode').change(function () {
                    $('#DefaultOTCode').find('option').remove().end();
                    var val = $('#DefaultHourlyCode').val();
                    var dept = $('#Department').val();
                    var client = @ViewBag.ClientID;
                    var url = "@Url.Action("GetCascadeHourlyOTCodes", "JobCosting")";
                    url = url + "?c=" + client + "&d=" + dept+ "&cd=" + val;
                    $.post(url, function (data) {
                        $.each(data, function (i, item) {
                            $('#DefaultOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                });
                //get default salary ot code on change
                $('#DefaultSalaryCode').change(function () {
                    $('#DefaultSalOTCode').find('option').remove().end();
                    var val = $('#DefaultSalaryCode').val();
                    var dept = $('#Department').val();
                    var client = @ViewBag.ClientID;
                    var url = "@Url.Action("GetCascadeSalaryOTCodes", "JobCosting")";
                    url = url + "?c=" + client + "&d=" + dept+ "&cd=" + val;
                    $.post(url, function (data) {
                        $.each(data, function (i, item) {
                            $('#DefaultSalOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                        });
                    });
                });
                // mini jQuery plugin that formats to two decimal places
                (function ($) {
                    $.fn.currencyFormat = function () {
                        this.each(function (i) {
                            $(this).change(function (e) {
                                if (isNaN(parseFloat(this.value))) return;
                                this.value = parseFloat(this.value).toFixed(2);
                            });
                        });
                        return this; //for chaining
                    }
                })(jQuery);

                //apply the currencyFormat behavior
                $(function () {
                    $('.currency').currencyFormat("{0:n2}%");
                });
                $('.currency').keypress(function (event) {
                    var $this = $(this);
                    if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
                       ((event.which < 48 || event.which > 57) &&
                       (event.which != 0 && event.which != 8))) {
                        event.preventDefault();
                    }

                    var text = $(this).val();
                    if ((event.which == 46) && (text.indexOf('.') == -1)) {
                        setTimeout(function () {
                            if ($this.val().substring($this.val().indexOf('.')).length > 3) {
                                $this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
                            }
                        }, 1);
                    }

                    if ((text.indexOf('.') != -1) &&
                        (text.substring(text.indexOf('.')).length > 2) &&
                        (event.which != 0 && event.which != 8) &&
                        ($(this)[0].selectionStart >= text.length - 2)) {
                        event.preventDefault();
                    }
                });
            </script>
        }
