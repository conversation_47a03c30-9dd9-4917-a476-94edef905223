@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@model DarwiNet2._0.Data.ClientJobCostAssignmentDetail
@{
    ViewBag.Title = "Create Job Cost Detail";
    ViewBag.ParentCrumb = "Company,Information";
}

    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                <div class="colored-line-left"></div>
            </div>

        </div>
    </div>
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    using (Html.BeginForm("CreateJCDetail", "JobCosting", FormMethod.Post, new { id = "createJCDetail" }))
    {
         @Html.AntiForgeryToken()

         <div class="form-horizontal">
         @Html.ValidationSummary(true, "", new {@class = "text-danger"})
         @Html.HiddenFor(model => model.CompanyID, new {@class = "form-control"})
         @Html.HiddenFor(model => model.ClientID, new {@class = "form-control"})
             <input type="hidden" id="JobCostingName" name="JobCostingName" value="@ViewBag.JobCostName" />

         <div class="form-group">
             @Html.LabelFor(model => model.JobCostingName, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobCostingName", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 <input type="text" id="JobName" name="JobName" value="@ViewBag.JobCostName" class="form-control" disabled />
                 @*@Html.TextBoxFor(model => model.JobCostingName, new {@class = "form-control field-required", @maxlength = "30"})*@
                 @Html.ValidationMessageFor(model => model.JobCostingName, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.JobLevel2, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobLevel2", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.JobLevel2, new { @class = "form-control field-required", @maxlength = "30" })
                 @Html.ValidationMessageFor(model => model.JobLevel2, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.JobLevel3, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobLevel3", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.JobLevel3, new { @class = "form-control", @maxlength = "30" })
                 @Html.ValidationMessageFor(model => model.JobLevel3, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.JobLevel4, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobLevel4", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.JobLevel4, new { @class = "form-control", @maxlength = "30" })
                 @Html.ValidationMessageFor(model => model.JobLevel4, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.JobLevel5, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobLevel5", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.JobLevel5, new { @class = "form-control", @maxlength = "30" })
                 @Html.ValidationMessageFor(model => model.JobLevel5, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.JobLevel6, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JobLevel6", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.JobLevel6, new { @class = "form-control", @maxlength = "30" })
                 @Html.ValidationMessageFor(model => model.JobLevel6, "", new {@class = "text-danger"})
             </div>
         </div>

         @*<div class="form-group">
             @Html.LabelFor(model => model.JBSID, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("JBSID", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 <select class="form-control" id="JBSID" name="JBSID">
                     @{
                         if (ViewBag.JBSCodes != null)
                         {
                             foreach (Code_Description codes in ViewBag.JBSCodes)
                             {
                                 <option value="@codes.Code">@codes.Description</option>
                             }
                         }
                     }
                 </select>
                 @Html.ValidationMessageFor(model => model.JBSID, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.SequenceNumber, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("SequenceNumber", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.SequenceNumber, new {@class = "form-control"})
                 @Html.ValidationMessageFor(model => model.SequenceNumber, "", new {@class = "text-danger"})
             </div>
         </div>*@

         <div class="form-group">
             @Html.LabelFor(model => model.Description2, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description2", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Description2, new { @class = "form-control", @maxlength = "50" })
                 @Html.ValidationMessageFor(model => model.Description2, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.Description3, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description3", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Description3, new { @class = "form-control", @maxlength = "50" })
                 @Html.ValidationMessageFor(model => model.Description3, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.Description4, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description4", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Description4, new { @class = "form-control", @maxlength = "50" })
                 @Html.ValidationMessageFor(model => model.Description4, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.Description5, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description5", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Description5, new { @class = "form-control", @maxlength = "50" })
                 @Html.ValidationMessageFor(model => model.Description5, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.Description6, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Description6", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Description6, new { @class = "form-control", @maxlength = "50" })
                 @Html.ValidationMessageFor(model => model.Description6, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.EstimatedStartDate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("EstimatedStartDate", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.EditorFor(model => model.EstimatedStartDate, new {@class = "form-control"})
                 @Html.ValidationMessageFor(model => model.EstimatedStartDate, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.EstimatedCompletion, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("EstimatedCompletion", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.EditorFor(model => model.EstimatedCompletion, new {@class = "form-control"})
                 @Html.ValidationMessageFor(model => model.EstimatedCompletion, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.EstimatedHours, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("EstimatedHours", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @*Html.TextBoxFor(model => model.EstimatedHours, new { @class = "form-control" })*@
                 <input type="text" style="width: 100%" value="@FieldTranslation.EstimatedHours(Html.DisplayFor(model => model.EstimatedHours).ToString())" id id="EstimatedHours" name="EstimatedHours" />
                 <div class="error-msg"></div>
                 @Html.ValidationMessageFor(model => model.EstimatedHours, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.Cost, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Cost", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Cost, new {@class = "form-control"})
                 @Html.ValidationMessageFor(model => model.Cost, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.ProjectManager, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("ProjectManager", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.ProjectManager, new { @class = "form-control", @maxlength = "30" })
                 @Html.ValidationMessageFor(model => model.ProjectManager, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.Phone1, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Phone1", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Phone1, new {@class = "form-control phone-type-masking"})
                 @Html.ValidationMessageFor(model => model.Phone1, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.Email, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.Email, new { @class = "form-control", @maxlength = "255" })
                 @Html.ValidationMessageFor(model => model.Email, "", new {@class = "text-danger"})
             </div>
         </div>

             <div class="form-group">
                 @Html.LabelFor(model => model.Department, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select class="form-control" id="Department" name="Department">
                         @{
        if (ViewBag.Departments != null)
        {
            foreach (Code_Description dept in ViewBag.Departments)
            {
             <option value="@dept.Code">@dept.Description</option>
            }
        }
                         }
                     </select>

                     @Html.ValidationMessageFor(model => model.Department, "", new { @class = "text-danger" })
                 </div>
                 @Html.LabelFor(model => model.WorkersComp, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("WorkersCompensation", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select class="form-control" id="WorkersComp" name="WorkersComp"></select>
                     @Html.ValidationMessageFor(model => model.WorkersComp, "", new { @class = "text-danger" })
                 </div>
             </div>



             <div class="form-group">
                 @Html.LabelFor(model => model.DefaultHourlyCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultHourlyCode", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select class="form-control" id="DefaultHourlyCode" name="DefaultHourlyCode"></select>

                     @Html.ValidationMessageFor(model => model.DefaultHourlyCode, "", new { @class = "text-danger" })
                 </div>
                 @Html.LabelFor(model => model.DefaultOTCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultOTCode", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select id="DefaultOTCode" name="DefaultOTCode" class="form-control"></select>
                     @Html.ValidationMessageFor(model => model.DefaultOTCode, "", new { @class = "text-danger" })
                 </div>
             </div>

             <div class="form-group">
                 @Html.LabelFor(model => model.DefaultSalaryCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultSalaryCode", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select class="form-control" id="DefaultSalaryCode" name="DefaultSalaryCode"></select>
                     @Html.ValidationMessageFor(model => model.DefaultSalaryCode, "", new { @class = "text-danger" })
                 </div>
                 @Html.LabelFor(model => model.DefaultSalOTCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DefaultSalaryOTCode", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select id="DefaultSalaryOTCode" name="DefaultSalaryOTCode" class="form-control"></select>
                     @Html.ValidationMessageFor(model => model.DefaultSalOTCode, "", new { @class = "text-danger" })
                 </div>
             </div>

             <div class="form-group">
                 @Html.LabelFor(model => model.StateCode, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("StateCode", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select class="form-control" id="StateCode" name="StateCode"></select>

                     @Html.ValidationMessageFor(model => model.StateCode, "", new { @class = "text-danger" })
                 </div>
                 @Html.LabelFor(model => model.LocalTax, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("LocalTax", GlobalVariables.LanguageID))
                 <div class="col-md-4">
                     <select class="form-control" id="LocalTax" name="LocalTax"></select>
                     @Html.ValidationMessageFor(model => model.LocalTax, "", new { @class = "text-danger" })
                 </div>
             </div>

         <div class="form-group">
             @Html.LabelFor(model => model.RegularRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("RegularRate", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.RegularRate, new { @class = "form-control currency" })
                 @Html.ValidationMessageFor(model => model.RegularRate, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.OTRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("OTRate", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.OTRate, new { @class = "form-control currency" })
                 @Html.ValidationMessageFor(model => model.OTRate, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             @Html.LabelFor(model => model.DefaultSalaryRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DefaultSalaryRate", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.DefaultSalaryRate, new { @class = "form-control currency" })
                 @Html.ValidationMessageFor(model => model.DefaultSalaryRate, "", new {@class = "text-danger"})
             </div>
             @Html.LabelFor(model => model.DefaultSalaryOTRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DefaultSalaryOTRate", GlobalVariables.LanguageID))
             <div class="col-md-4">
                 @Html.TextBoxFor(model => model.DefaultSalaryOTRate, new {@class = "form-control currency"})
                 @Html.ValidationMessageFor(model => model.DefaultSalaryOTRate, "", new {@class = "text-danger"})
             </div>
         </div>

         <div class="form-group">
             <div class="col-md-12">
                 <div class="pull-right">
                     <a href="@Url.Action("JCDetailsIndex", "JobCosting", new {client = ViewBag.ClientID, job = ViewBag.JobCostName})" class="btn btn-thinkware">@FieldTranslation.GetLabel("Cancel", GlobalVariables.LanguageID)</a>
                     <input type="submit" value="Save" class="btn btn-thinkware"/>
                 </div>
             </div>
         </div>
         </div>
     }
}
@if (ViewBag.Access == MenuAccessLevel.ReadOnly)
{
    <script>
        $(document).ready(function () {
            $('#createJCDetail input').attr('disabled', true);
            $('#createJCDetail select').attr('disabled', true);
        })
    </script>
}
@section scripts{

    <script src="~/Scripts/MaskedInput.js"></script>
    <script>
        $(document).ready(function() {
            $(".phone-type-masking").mask("(*************");
        });
        var form = $("#createJCDetail");
        form.validate();
        $("input[type=submit]").click(function() {
            if (form.valid() == true) {
                $("#createJCDetail").submit(function() {
                    $(".phone-type-masking").mask("9999999999");
                });
            }
        });
    </script>
    <script>
        jQuery.ajaxSetup({async:false});
        //run new cascades on load
        $(document).ready(function() {
            newCascades();
        });
        //clear cascade dropdowns if department chagnes
        $('#Department').change(function() {
            newCascades();
        });
        function newCascades() {
            //get workers comp codes
            $('#WorkersComp').find('option').remove().end();
            var compdept = $('#Department').val();
            var compclient = @ViewBag.ClientID;
            var compyurl = "@Url.Action("GetCascadeWC", "JobCosting")";
            compyurl = compyurl + "?c=" + compclient + "&d=" + compdept;
            $.post(compyurl, function (data) {
                $.each(data, function (i, item) {
                    $('#WorkersComp').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
            //get new hourly codes
            $('#DefaultHourlyCode').find('option').remove().end();
            var hourlydept = $('#Department').val();
            var hourlyclient = @ViewBag.ClientID;
            var hourlyurl = "@Url.Action("GetCascadeHourlyOTCodes", "JobCosting")";
            hourlyurl = hourlyurl + "?c=" + hourlyclient + "&d=" + hourlydept;
            $.post(hourlyurl, function (data) {
                $.each(data, function (i, item) {
                    $('#DefaultHourlyCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
            //get new salary codes
            $('#DefaultSalaryCode').find('option').remove().end();
            var salarydept = $('#Department').val();
            var salaryclient = @ViewBag.ClientID;
            var salaryurl = "@Url.Action("GetCascadeSalaryCodes", "JobCosting")";
            salaryurl = salaryurl + "?c=" + salaryclient + "&d=" + salarydept;
            $.post(salaryurl, function (data) {
                $.each(data, function (i, item) {
                    $('#DefaultSalaryCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
            //get new hourly OT codes
            $('#DefaultOTCode').find('option').remove().end();
            var val = $('#DefaultHourlyCode').val();
            var dept = $('#Department').val();
            var client = @ViewBag.ClientID;
            var url = "@Url.Action("GetCascadeHourlyOTCodes", "JobCosting")";
            url = url + "?c=" + client + "&d=" + dept+ "&cd=" + val;
            $.post(url, function (data) {
                $.each(data, function (i, item) {
                    $('#DefaultOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
            //get new salary OT Codes
            $('#DefaultSalaryOTCode').find('option').remove().end();
            var val1 = $('#DefaultSalaryCode').val();
            var dept1 = $('#Department').val();
            var client1 = @ViewBag.ClientID;
            var url1 = "@Url.Action("GetCascadeSalaryOTCodes", "JobCosting")";
            url1 = url1 + "?c=" + client1 + "&d=" + dept1 + "&cd=" + val1;
            $.post(url1, function (data) {
                $.each(data, function (i, item) {
                    $('#DefaultSalaryOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
            //get state code
            $('#StateCode').find('option').remove().end();
            var statedept = $('#Department').val();
            var stateclient = @ViewBag.ClientID;
            var stateurl = "@Url.Action("GetCascadeStateTax", "JobCosting")";
            stateurl = stateurl + "?c=" + stateclient + "&d=" + statedept;
            $.post(stateurl, function (data) {
                $.each(data, function (i, item) {
                    $('#StateCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
            //get local tax
            $('#LocalTax').find('option').remove().end();
            var localdept = $('#Department').val();
            var localclient = @ViewBag.ClientID;
            var localurl = "@Url.Action("GetCascadeLocalTax", "JobCosting")";
            localurl = localurl + "?c=" + localclient + "&d=" + localdept;
            $.post(localurl, function (data) {
                $.each(data, function (i, item) {
                    $('#LocalTax').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
        }
        //get default ot code on change
        $('#DefaultHourlyCode').change(function () {
            $('#DefaultOTCode').find('option').remove().end();
            var val = $('#DefaultHourlyCode').val();
            var dept = $('#Department').val();
            var client = @ViewBag.ClientID;
            var url = "@Url.Action("GetCascadeHourlyOTCodes", "JobCosting")";
            url = url + "?c=" + client + "&d=" + dept+ "&cd=" + val;
            $.post(url, function (data) {
                $.each(data, function (i, item) {
                    $('#DefaultOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
        });
        //get default salary ot code on change
        $('#DefaultSalaryCode').change(function () {
            $('#DefaultSalaryOTCode').find('option').remove().end();
            var val = $('#DefaultSalaryCode').val();
            var dept = $('#Department').val();
            var client = @ViewBag.ClientID;
            var url = "@Url.Action("GetCascadeSalaryOTCodes", "JobCosting")";
            url = url + "?c=" + client + "&d=" + dept+ "&cd=" + val;
            $.post(url, function (data) {
                $.each(data, function (i, item) {
                    $('#DefaultSalaryOTCode').append('<option value=' + data[i].Code + '>' + data[i].Description + '</option>');
                });
            });
        });
        // mini jQuery plugin that formats to two decimal places
        (function ($) {
            $.fn.currencyFormat = function () {
                this.each(function (i) {
                    $(this).change(function (e) {
                        if (isNaN(parseFloat(this.value))) return;
                        this.value = parseFloat(this.value).toFixed(2);
                    });
                });
                return this; //for chaining
            }
        })(jQuery);

        //apply the currencyFormat behavior
        $(function () {
            $('.currency').currencyFormat("{0:n2}%");
        });
        $('.currency').keypress(function (event) {
            var $this = $(this);
            if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
               ((event.which < 48 || event.which > 57) &&
               (event.which != 0 && event.which != 8))) {
                event.preventDefault();
            }

            var text = $(this).val();
            if ((event.which == 46) && (text.indexOf('.') == -1)) {
                setTimeout(function () {
                    if ($this.val().substring($this.val().indexOf('.')).length > 3) {
                        $this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
                    }
                }, 1);
            }

            if ((text.indexOf('.') != -1) &&
                (text.substring(text.indexOf('.')).length > 2) &&
                (event.which != 0 && event.which != 8) &&
                ($(this)[0].selectionStart >= text.length - 2)) {
                event.preventDefault();
            }
        });
    </script>
}