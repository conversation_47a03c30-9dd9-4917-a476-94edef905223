@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@model IEnumerable<DarwiNet2._0.Data.EmployeeJobCostAssignment>
@{
    ViewBag.Title = "Employee Job Costs";
    ViewBag.ParentCrumb = "Company,Information";
}

<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>

    </div>
</div>
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div class="toolbar">
        <div class="row">
            <div class="col-md-3 pull-left">
                <div class="input-group">
                    <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                    <input type="text" class="form-control" id='FieldFilter' placeholder="Search Task Name">
                </div>
            </div>
            <p class="create-pad pull-right">
                @if (GlobalVariables.CurrentTimeSheet != 0)
                {
                    <a href="@Url.Action("BackToTimeSheet", "JobCosting")" class="btn btn-thinkware">@FieldTranslation.GetLabel("Back to Timesheet", GlobalVariables.LanguageID)</a>
                }
                <a href="@Url.Action("JCIndex", "JobCosting")" class="btn btn-thinkware">Return to Job Cost</a>
                @*<a href="@Url.Action("CreateJCDetail", "JobCosting", new { client = GlobalVariables.Client, jobname = @ViewBag.Job })" class="btn btn-thinkware"><i class="fa fa-plus fa-lg fa-fw"></i>Add Job Cost</a>*@

            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <table class="table" id="jcDetailsIndex">
                <tr>
                    <th>
                        @Html.DisplayNameFor(model => model.JobCostingName)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.JobLevel2)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.JobLevel3)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.JobLevel4)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.JobLevel5)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.JobLevel6)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.JBSID)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.EstimatedStartDate)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.EstimatedCompletionDate)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.EstimatedHours)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.Cost)
                    </th>
                    <th>Actions</th>
                </tr>

                @foreach (var item in Model)
                {
                    <tr>
                        <td>
                            @Html.DisplayFor(modelItem => item.JobCostingName)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.JobLevel2)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.JobLevel3)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.JobLevel4)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.JobLevel5)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.JobLevel6)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.JBSID)
                        </td>
                        <td>
                            @FieldTranslation.ToShortDate(Html.DisplayFor(modelItem => item.EstimatedStartDate).ToString())
                        </td>
                        <td>
                            @FieldTranslation.ToShortDate(Html.DisplayFor(modelItem => item.EstimatedCompletionDate).ToString())
                        </td>
                        <td>
                            @FieldTranslation.EstimatedHours(Html.DisplayFor(modelItem => item.EstimatedHours).ToString())
                            @*Html.DisplayFor(modelItem => item.EstimatedHours*@
                       </td>
                        <td>
                            @FieldTranslation.CurrencyAmount(Html.DisplayFor(modelItem => item.Cost).ToString())
                        </td>
                        <td>
                            @*<a href="@Url.Action("AssignJobToEmployees", "JobCosting", new {client = GlobalVariables.Client, jobname = item.JobCostingName, j2 = item.JobLevel2, j3 = item.JobLevel3, j4 = item.JobLevel4, j5 = item.JobLevel5, j6 = item.JobLevel6})" data-toggle="modal" data-target="#assignJobModal" data-remote="false" title="Assign Employees"><i class="icon-green fa fa-users fa-lg fa-fw"></i></a>
                            <a href="@Url.Action("EmployeeJobAssignsIndex", "JobCosting", new {client = GlobalVariables.Client, job = item.JobCostingName})" title="Manage Assigned Employees"><i class="fa fa-user fa-lg fa-fw"></i></a>
                            <a href="@Url.Action("EditJCDetail", "JobCosting", new {client = GlobalVariables.Client, jobname = item.JobCostingName, j2 = item.JobLevel2, j3 = item.JobLevel3, j4 = item.JobLevel4, j5 = item.JobLevel5, j6 = item.JobLevel6})" title="Edit Job Cost Details"><i class="icon-edit fa fa-pencil fa-lg fa-fw"></i></a>
                            <a href="#" data-id="@item.JobCostingName" data-client="@GlobalVariables.Client" data-j2="@item.JobLevel2" data-j3="@item.JobLevel3" data-j4="@item.JobLevel4" data-j5="@item.JobLevel5" data-j6="@item.JobLevel6" id="delete" title="Delete"><i class="icon-red fa fa-times fa-lg fa-fw"></i></a>*@
                        </td>
                    </tr>
                }

            </table>
        </div>
    </div>
}
<script>
        $(document).ready(function() {
            var grid = $("#jcDetailsIndex").kendoGrid({
                dataSource: {
                    pageSize: 15
                },
                sortable: true,
                pageable: true,
                filterable: true,
                scrollable: false,
                groupable: true,
                resizable: true,
            }).data("kendoGrid");

            $("#createEEExp").validate();
            $("#editEEExp").validate();

        });

        $("#FieldFilter").keyup(function() {

            var value = $("#FieldFilter").val();
            var grid = $("#jcDetailsIndex").data("kendoGrid");

            if (value) {
                grid.dataSource.filter({
                    logic: "or",
                    filters: [
                        { field: "ExpenseType", operator: "contains", value: value }
                    ]
                })
            } else {
                grid.dataSource.filter({});
            }
        });




</script>