@using DarwiNet2._0.DNetSynch
@model IEnumerable<DarwiNet2._0.Data.OBProfileTaskField>

@{
    ViewBag.Title = "Field Order - " + @GlobalVariables.ProfileName;
    var mySetupID = Model.First().ProfileID;
    //Do not change this
    ViewBag.Page = "OBProfileTaskFieldsOrder";
    //
}
@Html.Partial("~/Views/Navigation/_PartialTabs.cshtml", mySetupID)
<div class="col-md-12">
<div class="row order-header">

    <p style="font-size: 22px;">Field Order</p>
    <div class="colored-line-left"></div>
    <p><small>Use the arrows to set the field order.</small>
        <span class="pull-right">
            @Html.ActionLink("Return to Fields", "Index", "OBProfileTaskFields", new { id = HttpContext.Current.Request.RequestContext.RouteData.Values["id"], tid = Request.QueryString["tid"] }, new { @class = "btn btn-thinkware" })
        </span></p>
</div>
    <div class="row select-order" style="font-size: 16px;">
<table class="table table-striped table-bordered">
    <thead>
        <tr><th>Column 1</th><th>Column 2</th></tr>
    </thead>
    <tbody>
    @{
        var cellCnt = 1;
        foreach (var item in Model)
        {
            if (cellCnt == 0)
            {
                cellCnt = 1;
            }
            
            if (cellCnt == 1)
            {
                @:<tr>
                <td>
                    <span>@item.FLabel</span>
                    <span class="pull-right">
                        @if (item.SeqNbr != ViewBag.FirstField && item.SeqNbr != ViewBag.SecondField){<a href="@Url.Action("MoveToTop", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, newOrder = (short)item.SeqNbr})"><i style="font-size: 18px" class="fa fa-caret-up"></i></a>}
                        @if (item.SeqNbr != ViewBag.LastField){<a href="@Url.Action("SetFieldOrder", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, oldOrder = (short)item.SeqNbr, newOrder = ((short)item.SeqNbr + 1)})"><i style="font-size: 18px" class="fa fa-arrow-right"></i></a>}
                        @if (item.SeqNbr != ViewBag.LastField && item.SeqNbr != ViewBag.SecondToLast){<a href="@Url.Action("SetFieldOrder", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, oldOrder = (short)item.SeqNbr, newOrder = ((short)item.SeqNbr + 2)})"><i style="font-size: 18px" class="fa fa-arrow-down"></i></a>}
                        @if(item.SeqNbr != ViewBag.FirstField && item.SeqNbr != ViewBag.SecondField){<a href="@Url.Action("SetFieldOrder", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, oldOrder = (short)item.SeqNbr, newOrder = ((short)item.SeqNbr - 2)})"><i style="font-size: 18px" class="fa fa-arrow-up"></i></a>}
                        @if (item.SeqNbr != ViewBag.LastField && item.SeqNbr != ViewBag.SecondToLast){<a href="@Url.Action("MoveToBottom", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, newOrder = (short)item.SeqNbr})"><i style="font-size: 18px" class="fa fa-caret-down"></i></a>}
                    </span>
                </td>
            }

            if (cellCnt == 2)
            {
                <td>
                    <span>@item.FLabel</span>
                    <span class="pull-right">
                       @if(item.SeqNbr != ViewBag.FirstField && item.SeqNbr != ViewBag.SecondField){<a href="@Url.Action("MoveToTop", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, newOrder = (short)item.SeqNbr})"><i style="font-size: 18px" class="fa fa-caret-up"></i></a>}
                        <a href="@Url.Action("SetFieldOrder", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, oldOrder = (short)item.SeqNbr, newOrder = ((short)item.SeqNbr - 1)})"><i style="font-size: 18px" class="fa fa-arrow-left"></i></a>
                        @if (item.SeqNbr != ViewBag.LastField){<a href="@Url.Action("SetFieldOrder", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, oldOrder = (short)item.SeqNbr, newOrder = ((short)item.SeqNbr + 2)})"><i style="font-size: 18px" class="fa fa-arrow-down"></i></a>}
                        @if(item.SeqNbr != ViewBag.FirstField && item.SeqNbr != ViewBag.SecondField){<a href="@Url.Action("SetFieldOrder", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, oldOrder = (short)item.SeqNbr, newOrder = ((short)item.SeqNbr - 2)})"><i style="font-size: 18px" class="fa fa-arrow-up"></i></a>}
                        @if (item.SeqNbr != ViewBag.LastField && item.SeqNbr != ViewBag.SecondField){<a href="@Url.Action("MoveToBottom", "OBProfileTaskFields", new { id = item.ProfileID, tid = item.TaskID, newOrder = (short)item.SeqNbr})"><i style="font-size: 18px" class="fa fa-caret-down"></i></a>}
                    </span>
                </td>
                @:</tr>
            }

            if (cellCnt == 1)
            {
                cellCnt = 2;
            }
            else
            {
                cellCnt = 0;
            }
        }

        if (cellCnt == 5)
        {
            @:<tr>
        }

        if (cellCnt == 1)
        {
            @:</tr>
        }
}
        </tbody>
</table>
</div>
    </div>
