@model IEnumerable<DarwiNet2._0.Data.OnBoardingDocument>

@{
    ViewBag.Title = "TWDocuments_Read";
}

<h2>TWDocuments_Read</h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.DocumentCode)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Document)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.DFile)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.AssignedToState)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.DefaultForState)
        </th>
        <th></th>
    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.DocumentCode)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Document)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.DFile)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.AssignedToState)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.DefaultForState)
            </td>
            <td>
                @Html.ActionLink("Edit", "Edit", new { /* id=item.PrimaryKey */ }) |
                @Html.ActionLink("Delete", "Delete", new { /* id=item.PrimaryKey */ })
            </td>
        </tr>
    }

</table>

