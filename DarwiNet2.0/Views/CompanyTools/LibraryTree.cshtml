@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.Data;


@{
    Layout = null;
}

@*
<div class="row">
    <div>
        <input id="appendNodeText" value="Folder" class="k-textbox" />
        <button class="k-button" id="appendNodeToSelected">Add New Folder</button>
    </div>
</div>
*@

<script>

    function ExpandMe(e) {
        this.expand(".k-item");
    }
</script>


@*<div class="row">
    <div class="tree-menu">
        <button class="btn btn-sm btn-thinkware" title="Add Folder" id="addFolder">Add Folder</button>
        <button class="btn btn-sm btn-thinkware" title="Expand All" id="expandAllNodes">Expand</button>
        <button class="btn btn-sm btn-thinkware" title="Collapse All" id="collapseAllNodes">Collapse</button>
        <i class="icon-red fa fa-times fa-2x" title="Remove Folder" id="removeNode"></i>
    </div>
</div>*@
<div class="row" id="addFolderInput" style="display: none;">
    <div class="">
        <input id="appendNodeText" value="Folder" class="k-textbox" />
        <button class="btn btn-sm btn-thinkware" id="appendNodeToSelected">Add</button>
    </div>
</div>
<div class="row">
    @(Html.Kendo().TreeView()
        .Name("treeview")
                .DataTextField("Name")
               .Checkboxes(false)
                       .Events(e => e.DataBound("ExpandMe").Select("onSelect"))
        .DataSource(dataSource => dataSource
            .Read(read => read
            .Action("FolderTree", "CompanyTools")
            )

        )
    )
</div>

<script>
    $(document).ready(function () {
        var treeview = $("#treeview").data("kendoTreeView"),
            handleTextBox = function (callback) {
                return function (e) {
                    if (e.type != "keypress" || kendo.keys.ENTER == e.keyCode) {
                        callback(e);
                    }
                };
            };
        $("#disableNode").click(function () {
            var selectedNode = treeview.select();

            treeview.enable(selectedNode, false);
        });

        $("#enableAllNodes").click(function () {
            var selectedNode = treeview.select();

            treeview.enable(".k-item");
        });
        $("#removeNode").click(function () {
            var selectedNode = treeview.select();

            treeview.remove(selectedNode);
        });
        $("#expandAllNodes").click(function () {
            treeview.expand(".k-item");
        });
        $("#collapseAllNodes").click(function () {
            treeview.collapse(".k-item");
        });
        var append = handleTextBox(function (e) {
            var selectedNode = treeview.select();
            if (selectedNode.length == 0)
            {
                selectedNode = null;
            }
            treeview.append({
                Name: $("#appendNodeText").val()
            }, selectedNode);
            $("#addFolderInput").hide('slow');
        });

        $("#appendNodeToSelected").click(append);
        $("#appendNodeText").keypress(append);

        // datasource actions

        var ascending = false;

        $("#sortDataSource")
            .text(ascending ? "Sort ascending" : "Sort descending")
            .click(function () {
                treeview.dataSource.sort({
                    field: "text",
                    dir: ascending ? "asc" : "desc"
                });

                ascending = !ascending;

                $(this).text(ascending ? "Sort ascending" : "Sort descending")
            });

        var filter = handleTextBox(function (e) {
            var filterText = $("#filterText").val();

            if (filterText !== "") {
                treeview.dataSource.filter({
                    field: "text",
                    operator: "contains",
                    value: filterText
                });
            } else {
                treeview.dataSource.filter({});
            }
        });

        $("#filterDataSource").click(filter);
        $("#filterText").keypress(filter);
    });

    $("#addFolder").click(function() {
        $("#addFolderInput").toggle('slow');
    });
</script>