
@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Helpers;

@{
    ViewBag.Title = "Preview Form " + ViewBag.DocId;
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script>

    $(document).ready(function () {
        url = "@Url.Action("ShowPreviewForm", "CompanyDocument", new { docId = ViewBag.DocId, DocLevel = DocumentSetLevel.DocumentSetup})"
        var html = "<iframe src=" + url + " style='width: 100%; height: 700px' ></iframe>";

        $('#pdfDiv').html(html);
    });
</script>


<!--Create dummy Task Navigation-->
<div class="row" style="width: 98%">
    <div class="col-md-3">
    </div>


    <!--Preview Task Instruction-->
    <div class="col-md-9 instructions-box-dynamic">


        <div class="div-10"></div>
        <p class="pull-right">
            <a href="@Url.Action("DocumentFieldsIndex", "CompanyDocument", new { id = ViewBag.DocId })" title="Return To Fields List" data-toggle="modal" class="btn btn-thinkware">Return To Field List</a>
            <a href="@Url.Action("Index", "CompanyDocument" )" title="Return To Document List" data-toggle="modal" class="btn btn-thinkware">Return To Document List</a>
        </p>
        <div id="pdfDiv"></div>
    </div>
    <!--End SideBar Nav Loop-->
</div>
<!--Slider-->
