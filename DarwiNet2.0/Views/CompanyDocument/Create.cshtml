@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core

@model DarwiNet2._0.ViewModels.Documents.CompanyDocumentVM

@{
    ViewBag.Title = (GlobalVariables.DNETLevel == DNetAccessLevel.System) ? "Create Company Document" : "Create Client Document";
}

<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-6 col-sm-6">
            <div class="text-right">
                <div class="company-info-header">
                </div>
            </div>
        </div>
    </div>
</div>

@using (Html.BeginForm("Create", "CompanyDocument", FormMethod.Post, new { @enctype = "multipart/form-data", @id = "documentscreate" }))
{
    @Html.AntiForgeryToken()

<div class="form-horizontal plain-editor">

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })

    @Html.HiddenFor(model => model.Document.CompanyID)
    @Html.HiddenFor(model => model.Document.DocumentID)

    <div class="form-group">
        <label class="control-label col-md-2" for="file">File</label>
        <div class="col-md-4">
            @*Comment this line back in if you want to enable the tooltip for Internet Explorer *@
            @*<a class="tooltip-bottom" title="" data-placement="center" href="#" data-orginal-title="#file">@Html.TextBoxFor(model => model.Document, new { type = "file", name = "file", id = "file" })</a>*@
            <a class="bottom" title="" data-placement="center" href="#" data-orginal-title="#file">@Html.TextBoxFor(model => model.Document.DFile, new { type = "file", name = "file", id = "file" })</a>
        </div>

        @Html.LabelFor(model => model.Document.DocumentName, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Document Name", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.TextBoxFor(model => model.Document.DocumentName, new { @class = "field-required form-control", required = "required", @maxlength = "30" })
            @Html.ValidationMessageFor(model => model.Document.DocumentName, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Document.DocType, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Document Type", GlobalVariables.LanguageID))
        <div class="col-md-4">
            <select id="Document.DocType" name="DocType" class="form-control dtype">
                @{
                    foreach (var item in Model.DocTypes)
                    {
                        if (Model.Document.DocType != null)
                        {
                            if (item.Code == Model.Document.DocType)
                            {
                                <option value="@item.Code" selected>@item.Description</option>
                            }
                            else
                            {
                                <option value="@item.Code">@item.Description</option>
                            }
                        }
                        else
                        {
                            <option value="@item.Code">@item.Description</option>
                        }
                    }
                }
            </select>
            @Html.ValidationMessageFor(model => model.Document.DocType, "", new { @class = "text-danger" })
        </div>
        @Html.LabelFor(model => model.Document.Signature, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Signature", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.DropDownListFor(model => model.Document.Signature, EnumHelper.GetSelectList(typeof(enOBSignatureType)), new { id = "Signature", name = "Signature", @class = "form-control sg" })
            @Html.ValidationMessageFor(model => model.Document.Signature, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Document.UseToDo, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Add ToDo Item", GlobalVariables.LanguageID))
        <div class="col-md-4">
            <div class="checkbox">
                @Html.EditorFor(model => model.Document.UseToDo)
                @Html.ValidationMessageFor(model => model.Document.UseToDo, "", new { @class = "text-danger" })
            </div>
        </div>
        @Html.LabelFor(model => model.Document.EmailNotification, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Email Notification", GlobalVariables.LanguageID))
        <div class="col-md-4">
            <div class="checkbox">
                @Html.EditorFor(model => model.Document.EmailNotification)
                @Html.ValidationMessageFor(model => model.Document.EmailNotification, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
    <div class="form-group">
        @*Html.LabelFor(model => model.Document.NotificationOption, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Notification Option", GlobalVariables.LanguageID))
            <!--<div class="col-md-4">-->
                @Html.DropDownListFor(model => model.Document.NotificationOption, EnumHelper.GetSelectList(typeof(enDocNotification)), new { id = "Document.NotificationOption", name = "NotificationOption", @class = "form-control nopt" })
                @Html.ValidationMessageFor(model => model.Document.NotificationOption, "", new { @class = "text-danger" })
            <!--</div>-->*@
        @Html.LabelFor(model => model.Document.AddAlert, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Add Alert", GlobalVariables.LanguageID))
        <div class="col-md-4">
            <div class="checkbox">
                @Html.EditorFor(model => model.Document.AddAlert)
                @Html.ValidationMessageFor(model => model.Document.AddAlert, "", new { @class = "text-danger" })
            </div>
        </div>
        @Html.LabelFor(model => model.Document.SignAtLogin, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Sign At Login", GlobalVariables.LanguageID))
        <div class="col-md-4">
            <div class="checkbox">
                @Html.EditorFor(model => model.Document.SignAtLogin)
                @Html.ValidationMessageFor(model => model.Document.SignAtLogin, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Document.VerificationType, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Verification Type", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.DropDownList("VerificationType", EnumHelper.GetSelectList(typeof(enDocVerification)), new { id = "Document.VerificationType", name = "VerificationType", @class = "form-control vertype" })
            @Html.ValidationMessageFor(model => model.Document.VerificationType, "", new { @class = "text-danger" })
        </div>
        <div id="AskReq">
            @Html.LabelFor(model => model.Document.UserRequiring, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("User Requiring", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("UserRequiring", EnumHelper.GetSelectList(typeof(enDocUserRequiring)), new { id = "Document.UserRequiring", name = "UserRequiring", @class = "form-control ureq" })
                @Html.ValidationMessageFor(model => model.Document.UserRequiring, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Document.VerificationAgreement, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Verification Agreement", GlobalVariables.LanguageID))
        <div class="col-md-10">
            @Html.TextAreaFor(model => model.Document.VerificationAgreement, new { @class = "form-control editor", @width = "100%", @rows = "10" })
            @Html.ValidationMessageFor(model => model.Document.VerificationAgreement, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Document.Instruction, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Instruction", GlobalVariables.LanguageID))
        <div class="col-md-10">
            @Html.TextAreaFor(model => model.Document.Instruction, new { @class = "form-control editor", @width = "100%", @rows = "10" })
            @Html.ValidationMessageFor(model => model.Document.Instruction, "", new { @class = "text-danger" })
        </div>
    </div>
    <!--<div class="form-group">
        @*Html.LabelFor(model => model.Document.UseToDo, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Send To Do", GlobalVariables.LanguageID))
        <div class="col-md-10">
            <div class="checkbox">
                @Html.EditorFor(model => model.Document.UseToDo)
                @Html.ValidationMessageFor(model => model.Document.UseToDo, "", new { @class = "text-danger" })*@
            </div>
        </div>
    </div>-->
    <div class="form-group">
        <div class="col-md-12">
            <div class="pull-right">
                <a href="@Url.Action("Index", "CompanyDocument")" class="btn btn-thinkware">Cancel</a>
                <input type="submit" value="Save" class="btn btn-thinkware" />
            </div>
        </div>
    </div>
</div>
}
<script type="text/javascript">
    $(document).ready(function () {
        $("#BtnAssignEE").hide();
        $("#BtnAssignCC").hide();
        var vtype = $('.vertype').val();
        console.log("type=" + vtype);
        if (vtype == "3") {

            $("#AskReq").show();
        } else {
            $("#AskReq").hide();
        }
    });
</script>

<script type="text/javascript">
    $(document).ready(function () {
        // create Editor from textarea HTML element with default set of tools
        kendo.ui.editor.ColorTool.prototype.options.palette = "basic";
        $(".editor").kendoEditor({
            resizable: {
                content: true,
                toolbar: true
            },
            tools: [
                {
                    name: "insertHtml",
                    items: [
                        { text: "Employee ID", value: "[EMPLOYEEID]" },
                        { text: "First Name", value: "[FIRSTNAME]" },
                        { text: "Last Name", value: "[LASTNAME]" },
                        { text: "Employee Name", value: "[EMPLOYEENAME]" },
                        { text: "Employee Email", value: "[EMPLOYEEMAIL]" },
                        { text: "Client User ID", value: "[CLIENTUSERID]" },
                        { text: "Client Name", value: "[CLIENTNAME]" },
                        { text: "Client Phone", value: "[CLIENTPHONE]" },
                        { text: "Client Email", value: "[CLIENTMAIL]" },
                        { text: "PEO Name", value: "[PEONAME]" },
                        { text: "PEO Phone", value: "[PEOPHONE]" },
                        { text: "PEO Email", value: "[PEOMAIL]" },
                    ]
                },
                "formatting",
                "bold",
                "italic",
                "underline",
                "justifyLeft",
                "justifyCenter",
                "justifyRight",
                "justifyFull",
                "insertUnorderedList",
                "insertOrderedList",
                "indent",
                "outdent",
                "createLink",
                "unlink",
                "foreColor",
                "backColor"
            ],
            paste: function (ev) {
                ev.html = $(ev.html).text();
            },
            messages: {
                insertHtml: "SmartTags"
            }
        });
    });
</script>
<script>
    $("#documentscreate").validate();
    $('.vertype').change(function () {
        var vtype = $('.vertype').val();
        console.log("type=" + vtype);
        if (vtype == "3") {

            $("#AskReq").show();
        } else {
            $("#AskReq").hide();
        }
    });
</script>
