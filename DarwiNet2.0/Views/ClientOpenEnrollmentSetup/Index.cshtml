@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model IEnumerable<DarwiNet2._0.Data.ClientOpenEnrollmentSetup>
@{
    ViewBag.Title = "Client Open Enrollment";

}
<div class="toolbar margin-bottom-5">
    <div class="row">
        <div class="col-md-3 pull-left">
            <div class="input-group">
                <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                <input type="text" class="form-control" id="FieldFilter" placeholder="Search Client ID or Plan Type">
            </div>
        </div>
        
    </div>
</div>
<div class="table-bottom table-icons">
    <table id="ClientOpenEnrollment" class="grid-tooltips">
        <tr>
            <th width="25px;" class="hidden-filter">
                <div style="visibility: hidden;">Controls</div>
            </th>
            <th title="@FieldTranslation.GetLabel("ClientID", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("ClientID", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("PlanType", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("PlanType", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("Instruction", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Instruction", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("InstructionDoc", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("InstructionDoc", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("TipText", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("TipText", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("TipImage", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("TipImage", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("TipDocument", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("TipDocument", GlobalVariables.LanguageID)
            </th>
            <th></th>
        </tr>

        @foreach (var item in Model)
        {
            <tr>
                <td>
                    <div class="icon-center table-icons">
                        <a href="@Url.Action("Edit", "ClientOpenEnrollmentSetup", new {cid = item.ClientID, pt = item.PlanType})" title="Edit"><i class="icon-edit fa fa-pencil fa-fw fa-lg"></i></a>
                    </div>
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.ClientID)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.PlanType)
                </td>
                <td>
                    @Html.Raw(item.Instruction)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.InstructionDoc)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.TipText)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.TipImage)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.TipDocument)
                </td>
                <td></td>
            </tr>
        }

    </table>
</div>
    @section scripts{
        <script>
            $(document).ready(function() {
                var grid = $("#ClientOpenEnrollment").kendoGrid({
                    dataSource: {
                        pageSize: 15
                    },
                    sortable: true,
                    pageable: true,
                    filterable: true,
                    scrollable: false,
                    groupable: true,
                    resizable: true,
                }).data("kendoGrid");

            });
        </script>
<script>
    $(document).ready(function () {
        $("#FieldFilter").keyup(function () {

            var value = $("#FieldFilter").val();
            var grid = $("#ClientOpenEnrollment").data("kendoGrid");

            if (value) {
                grid.dataSource.filter({
                    logic: "or",
                    filters: [
                        { field: "ClientID", operator: "contains", value: value },
                        { field: "PlanType", operator: "contains", value: value }
                    ]
                });
            } else {
                grid.dataSource.filter({});
            }
        });
    });
</script>

    }
