

<style type="text/css" id="controls-variance-per-payroll-cumlative-card-style">
</style>
<script type="text/x-template" id="controls-variance-per-payroll-cumlative-card-template">
        <div class="col-md-3">
            <div class="card-d2" style="min-height: 300px;">
                <p class="card-title text-center">Per Payroll - Cumulative</p>
                <p class="text-center">Gross Wages</p>
                <div class="card-content">
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <input type="checkbox" /> MAX
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <input type="text" class="form-control" />
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <input type="checkbox" /> MIN
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12">
                        <div class="form-group">
                            <input type="text" class="form-control" />
                        </div>
                    </div>
                    <hr />
                    <div class="col-md-12 col-sm-12">
                        <div class="form-group">
                            <input type="checkbox" /> Stop Payroll
                        </div>
                    </div>
                    <div class="col-md-12 col-sm-12">
                        <div class="form-group">
                            <button type="button" class="btn btn-thinkware btn-block text-center" data-toggle="modal" data-target="#addModalPayroll">Select Recipients</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</script>
<script type="text/javascript" id="controls-variance-per-payroll-cumlative-card-script">
    var ControlsVariancePerPayrollCumlativeCardComponent = VueComponent('controls-variance-per-payroll-cumlative-card', {
        props: {
            ControlsVariancesData: {
                type: Object,
                default: () => ({})
            },

        },
        data: function () {
            return{
                events:{
                }
            }
        },
        mounted: function () {
        },
        methods: {

        }
    });
</script>
