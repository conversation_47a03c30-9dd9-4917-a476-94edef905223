@using DarwiNet2._0.Extensions;

<style type="text/css" scoped>
.box {
    width: fit-content;
}

.inner-circle {
    font-size: 25px;
}

.z-index-reset {
    z-index: 1;
}

.percentage-title {
    font-size: 25px;
    margin-right: 5px;
}

.square-fix * {
    stroke-linecap: unset !important;
}
</style>

<script type="text/x-template" id="payroll-dashboard-radial-percentage-template">
    <div class="box">
        <div class="text-center percentage-title">{{ percentage.Title }}</div>
        <div class="text-center">{{ 'Records: ' + percentage.Records.length }}</div>
        <div @@click="toggleTableModal">
            <circle-progress :percent="percentage.Percentage" size="230" :dynamic-color="huePercentage(percentage.Percentage)" dynamic-shape="square" class="z-index-reset square-fix" static-width="15" dynamic-width="15">
                <p class="inner-circle">{{ percentage.Percentage }}%</p>
            </circle-progress>
        </div>
        <thinkware-vue-modal :active="showTableModal"
                            :title="percentage.Title + ' records'"
                            width="80%"
                            v-on:close-modal="onClickCloseTableModal">
            <payroll-dashboard-client-payroll-schedules-table-modal :data="percentage.Records" v-on:show-alert="showAlert"></payroll-dashboard-client-payroll-schedules-table-modal>
        </thinkware-vue-modal>
    </div>
</script>

<!-- https://github.com/anthinkingcoder/circle-progress -->
<script src="https://cdn.jsdelivr.net/npm/vue-circle-xprogress@0.0.1/dist/circle-progress.min.js"></script>

@Html.VueComponent("~/Shared/thinkware-vue-modal")
@Html.VueComponent("payroll-dashboard-client-payroll-schedules-table-modal")

<script type="text/javascript" id="payroll-dashboard-radial-percentage">
    var PayrollDashboardRadialPercentageComponent = VueComponent('payroll-dashboard-radial-percentage', {
        props: {
            percentage: {
                type: Object,
            },
        },
        data: function () {
            return {
                showTableModal: false,
            }
        },
        mounted: function () {
        },
        methods: {
            huePercentage: function(percent) {
                if (percent <= 25) {
                    return '#ee0000';
                }
                else if (percent <= 50) {
                    return '#e28700';
                }
                else if (percent <= 75) {
                    return '#9cc97d';
                }
                else {
                    return '#649b40';
                }
            },
            toggleTableModal: function() {
                this.showTableModal = !this.showTableModal;
            },
            onClickCloseTableModal: function() {
                this.showTableModal = false;
            },
            showAlert: function(msg) {
                this.$emit('show-alert', msg)
                this.onClickCloseTableModal();
            }
        },
        components: [
            ThinkwareVueModalComponent,
            PayrollDashboardClientPayrollSchedulesTableModal
        ],
    });
</script>

