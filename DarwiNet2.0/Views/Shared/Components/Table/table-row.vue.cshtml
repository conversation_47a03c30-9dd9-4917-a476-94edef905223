@using DarwiNet2._0.Extensions;

<script type="text/x-template" id="table-row-template">
    <tr @@click="onRowClick">
        <td id="TableSelectCell" v-if="options.Selectable.Active">
            <input type="checkbox" v-model="selected" :value="row" :disabled="disableCheckbox" @@click.stop="checkboxClicked()" />
        </td>
        <td v-for="(col, index) in columns" :id="idLabelString(col.Label)" v-if="col.Render !== types.RENDER.HIDDEN">
            <table-cell v-if="col.Render !== types.RENDER.TEMPLATE" :types="types" :config="col" :data="row" />
            <div v-else-if="col.Render === types.RENDER.TEMPLATE">
                <component :is="renderName(col.Label)">
                    <slot :name="renderName(col.Label)" v-bind:data="row"></slot>
                </component>
            </div>
        </td>
        <table-action v-if="actions" :actions="actions" :row="row" :selected="selected" :types="types" v-on:refresh-table="refreshTable" />
    </tr>
</script>

@Html.VueComponent("~/Shared/Table/table-cell")
@Html.VueComponent("~/Shared/Table/table-action")

<script type="text/javascript">
    TableRowComponent = VueComponent('table-row', {
        props: {
            types: {
                type: Object,
                required: true,
            },
            columns: {
                type: Array,
                required: true,
            },
            row: {
                type: Object,
                required: true,
            },
            options: {
                type: Object,
                required: true,
            },
            actions: {
                type: Object,
                required: false,
            },
            selected: {
                type: Array,
            }
        },

        data: function () {
            return {
                events: {
                    selectClick: "select-click",
                    refreshTable: "refresh-table",
                    rowClick: 'row-click'
                },
            }
        },

        computed: {
            disableCheckbox: function () {
                if (this.row.isVoid) return true;
                if (this.row.IsProcessed) return true;
                if (this.row.hasOwnProperty("hasPermissions") && !this.row.hasPermissions) return true;
                return false;
            }
        },

        methods: {
            toLowerCase: function (string) {
                return string.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
            },

            renderName: function (string) {
                let str = string.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
                return str + 'render';
            },

            idLabelString: function (string) {
                var tempString = string.replace("!", "").replace(" ", "");
                return tempString;
            },

            checkboxClicked: function () {
                this.$emit(this.events.selectClick, this.row)
            },

            refreshTable: function () {
                this.$emit(this.events.refreshTable)
            },

            onRowClick: function () {
                this.$emit(this.events.rowClick, this.row);
            },
        },

        components: [
            TableCellComponent,
            TableActionComponent
        ],
    });
</script>