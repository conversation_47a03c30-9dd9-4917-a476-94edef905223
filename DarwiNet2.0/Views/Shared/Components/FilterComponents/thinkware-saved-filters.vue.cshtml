@using DarwiNet2._0.Extensions
@using DataDrivenViewEngine.Models.Core;
@using DarwiNet2._0.Controllers;
@using Newtonsoft.Json;
@using DarwiNet2._0.Core;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Extensions;

<style type="text/css">
</style>

<script type="text/x-template" id="thinkware-saved-filters-template">
    <div class="thinkware-saved-filters-wrapper">
        <div :class="hasDefaultFilter ? 'col-md-8' : 'col-md-12'">
            <thinkware-select
                :multi-select="false"
                :list="savedFilterOptions"
                v-model="selectedSavedFilter"
                placeholder="Saved Filters"
                :tabindex="tabindex"
                ></thinkware-select>
        </div>
        <div class="col-md-4" v-if="hasDefaultFilter">
            <button class="btn btn-thinkware" @@click="loadDefaultFilter">Load Default Filter</button>
        </div>
    </div>
</script>

@Html.VueComponent("~/Shared/FilterComponents/thinkware-select")

<script type="text/javascript" id="thinkware-saved-filters-script">
    var ThinkwareSavedFiltersComponent = VueComponent('thinkware-saved-filters', {
        props: {
            filterTableId: {
                required: true,
                type: Number
            },
            currentSavedFilter: {
                required: false,
                type: String
            },
            tabindex: {
                required: false,
                type: Number
            },
            savedFilterOptions: {
                required: false,
                type: Array,
                default: () => ([])
            },
            autoFetchSaved: {
                required: false,
                type: Boolean,
                default: true
            }
        },
        data: function () {
            return {
                selectedSavedFilter: null
            }
        },
        watch: {
            selectedSavedFilter: function (val) {
                if (this.selectedSavedFilter != null && this.selectedSavedFilter.length > 0) {
                    this.$emit('filter-select', JSON.parse(this.selectedSavedFilter[0].Filter), this.selectedSavedFilter[0].Code)
                }
            },
            currentSavedFilter: function(val) {
                if (val == null) {
                    this.selectedSavedFilter = null;
                }
            }
        },
        computed: {
            hasDefaultFilter: function() {
                return this.savedFilterOptions.some(x => x.Default == true);
            },
        },
        mounted: function () {
            this.fetchSavedFilters();
        },
        methods: {
            loadDefaultFilter: function() {
                let defaultFilter = this.savedFilterOptions.find(x => x.Default == true);

                this.selectedSavedFilter = [ defaultFilter ];
            },

            fetchSavedFilters: function() {
                if (this.filterTableId == null) {
                    console.log('The thinkare-saved-filters component requires a filter-table-id to be passed in order to fetch saved filters.')
                } else {
                    let self = this;
                    ThinkwareCommon.ajax.get('@Url.Action("UserTableFilters", "Filter")' + `?filterTableId=${this.filterTableId}`, {
                        onSuccess: function (result) {
                            // If there are no filters (including previous), emit `no-previous-filter-apply`
                            if (result.data.length == 0) {
                                self.$emit('no-filters')
                            } else {
                                result.data.map(x => {
                                    if (x.ReferenceID == "" || x.ReferenceID == null) {
                                        self.savedFilterOptions.push(
                                            {
                                                Display: 'Previous Filters',
                                                Code: 'Previous Filters',
                                                Filter: x.Filter,
                                                Default: x.Default
                                            }
                                        )
                                        if (self.autoFetchSaved) {
                                            self.selectedSavedFilter = [
                                                {
                                                    Display: 'Previous Filters',
                                                    Code: 'Previous Filters',
                                                    Filter: x.Filter,
                                                    Default: x.Default
                                                }
                                            ]
                                        }

                                    } else {
                                        self.savedFilterOptions.push(
                                            {
                                                Display: x.ReferenceID,
                                                Code: x.ReferenceID,
                                                Filter: x.Filter,
                                                Default: x.Default
                                            }
                                        )
                                    }
                                });
                            }
                        },
                        onError: function (jqXHR, textStatus, errorThrown) {
                            ThinkwareCommon.showAlert('danger', errorThrown);
                        }
                    });
                }
            },

            refresh: function() {
                this.savedFilterOptions = []
                this.fetchSavedFilters();
            }
        },
        components: [
            ThinkwareSelectComponent
        ]
    });
</script>
