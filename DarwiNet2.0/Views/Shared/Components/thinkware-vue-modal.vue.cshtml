<style type="text/css">
    .thinkware-vue-modal-mask {
        position: fixed;
        z-index: 999;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s ease;
        display: table;
    }

    .thinkware-vue-modal-wrapper {
        display: table-cell;
        vertical-align: middle;
    }

    .thinkware-vue-modal-container {
        width: var(--width);
        margin: 0px auto;
        background-color: #fff;
        border-radius: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
        transition: all 0.3s ease;
        font-family: Helvetica, Arial, sans-serif;
    }

    .thinkware-vue-modal-body {
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
        padding-bottom: 15px;
    }

    .thinkware-vue-modal-footer {
        text-align: right;
        border-top: 1px solid #e5e5e5;
        padding-top: 15px;
    }

    .thinkware-vue-modal-transition-enter {
        opacity: 0;
    }

    .thinkware-vue-modal-transition-leave-active {
        opacity: 0;
    }

        .thinkware-vue-modal-transition-enter .thinkware-vue-modal-container,
        .thinkware-vue-modal-transition-leave-active .thinkware-vue-modal-container {
            -webkit-transform: scale(1.1);
            transform: scale(1.1);
        }

.max-height-85 {
    max-height: 85%;
    overflow: hidden;
}
</style>

@*Using seperate templates for body and footer. Use for basic use-case, where shared logic can happen in parent component.
    <thinkware-vue-modal :active="boolean"
                       title="string"
                       width="25-100%"
                       dismissable="boolean"
                       v-on:close-modal="methodCallToToggleActive">
             <template v-slot:modal-body></template>
             <template v-slot:modal-footer></template>
    </thinkware-vue-modal>*@

@*Using default template where logic can't happen in root component. Utilize classes 'thinkware-vue-modal-body'
    and 'thinkware-vue-modal-footer' to seperate styling within the 'other-component'.
    <thinkware-vue-modal :active="boolean"
                       title="string"
                       width="25-100%"
                       dismissable="boolean"
                       v-on:close-modal="methodCallToToggleActive">
             <other-component />
    </thinkware-vue-modal>*@

<script type="text/x-template" id="thinkware-vue-modal-template">
    <transition name="thinkware-vue-modal-transition">
        <div v-show="active" class="thinkware-vue-modal-mask" :id="id" tabindex="0" @@keydown.escape="dismissable ? closeModal() : null">
            <div class="thinkware-vue-modal-wrapper">
                <div class="thinkware-vue-modal-container" :style="{'--width':  modalWidth}" :class="limitHeight ? 'max-height-85' : ''">
                    <div class="panel panel-default">
                        <section class="panel-heading">
                            <table style="width: 100%">
                                <tr>
                                    <td>
                                        <strong>{{ title }}</strong>
                                    </td>
                                    <td class="text-right">
                                        <button v-if="dismissable" type="button" class="close" @@click="closeModal()">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        </section>
                        <section class="panel-body" v-if="displayBody">
                            <slot></slot>
                            <modal-body v-if="hasSlot('modal-body')">
                                <section class="thinkware-vue-modal-body">
                                    <slot name="modal-body"></slot>
                                </section>
                            </modal-body>
                            <modal-footer v-if="hasSlot('modal-footer')">
                                <section class="thinkware-vue-modal-footer">
                                    <slot name="modal-footer"></slot>
                                </section>
                            </modal-footer>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</script>

<script type="text/javascript" id="thinkware-vue-modal-script">
    var ThinkwareVueModalComponent = VueComponent('thinkware-vue-modal', {
        props: {
            active: {
                type: Boolean,
                default: false
            },
            title: {
                type: String,
                default: ""
            },
            dismissable: {
                type: Boolean,
                default: true
            },
            width: {
                type: String,
                default: "60%"
            },
            limitHeight: {
                type: Boolean,
                default: false,
                required: false
            }
        },

        data: function () {
            return {
                displayBody: false,
                id: "TWModal-"
            }
        },

        computed:
        {
            modalWidth: function () {
                var modalWidth = 25;
                var temp = Number(this.width.replace("%", ""));
                modalWidth = isNaN(temp) ? 60 : temp < 25 ? 25 : temp;

                return `${modalWidth}%`
            },

        },

        watch: {
            active: {
                handler: function (val) {
                    if (this.active) {
                        this.displayBody = true;
                        document.getElementById(this.id).focus();
                    }
                },
            },
        },

        mounted: function () {
            this.id += this.generatedID();
        },

        methods: {
            closeModal: function () {
                this.$emit("close-modal");
                setTimeout(function () { this.displayBody = false }, 2000)
            },
            generatedID: function (a, b) {
                for (b = a = ''; a++ < 36; b += a * 51 & 52 ? (a ^ 15 ? 8 ^ Math.random() * (a ^ 20 ? 16 : 4) : 4).toString(16) : '-');
                return b
            },
            hasSlot: function (name) {
                return !!this.$slots[name] || !!this.$scopedSlots[name];
            }
        }
    });
</script>