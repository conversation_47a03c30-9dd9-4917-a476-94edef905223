@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core

@model DarwiNet2._0.ViewModels.ThirdPartyConnections.IndexVM

@{
    ViewBag.Title = "Third Party Connections";
}

<style>
    .container-fluid {
        margin: 30px 0px 0px 0px;
        max-width: 1400px;
    }

    .row.service-provider,
    .buttons {
        min-width: 600px;
        padding: 0px 0px 20px 0px;
        width: 65%;
    }

        .row.service-provider.border-bottom {
            border-bottom: 1px solid #c8c2c2;
            margin-bottom: 20px;
        }

    .buttons {
        padding: 30px 0px 0px 0px;
    }

    .service-provider-logo {
        display: inline-block;
        float: none;
        margin: 0px 0px 0px 0px;
        padding: 0px 0px 0px 0px;
        text-align: left;
        vertical-align: middle;
    }

        .service-provider-logo > img {
            height: 60px;
        }

    .service-provider-description {
        display: inline-block;
        float: none;
        margin: 0px -5px 0px -5px;
        padding: 0px 0px 0px 5px;
        text-align: left;
        vertical-align: middle;
    }

        .service-provider-description > span {
            font-size: 28px;
            font-weight: bold;
        }

        .service-provider-description > p {
            font-size: 14px;
            padding: 0px 0px 10px 0px;
        }

    .service-provider-validation-message {
        padding: 0px 0px 5px 17px;
    }

    .service-provider-input-control {
        display: inline-block;
        margin: 0px 0px -12px 0px;
        width: 320px;
    }

    .button-inside {
        position: relative;
    }

        .button-inside > button {
            border: none;
            border-radius: 0px 4px 4px 0px;
            height: 32px;
            margin: -3px 1px 0px 0px;
            position:absolute;
            right: 0px;
            top: 4px;
            width: 40px;
        }

        .button-inside > button:hover{
                cursor: pointer;
            }

    .service-provider-label {
        display: inline-block;
        text-align: right;
        width: 130px;
    }

    .control-label {
        padding: 0px 10px 0px 0px;
    }

    .confirmation-message {
        margin: auto;
        text-align: center;
        width: 85%;
    }
</style>


@*Security*@
@Html.AntiForgeryToken()

@if (Model.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    @*Success Message*@
    if (!string.IsNullOrWhiteSpace(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <p>@Model.SuccessMessage</p>
        </div>
    }

    @*Error Message*@
    if (!string.IsNullOrWhiteSpace(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <p>@Model.ErrorMessage</p>
        </div>
    }

    @*Success Message*@
    if (TempData["IsSuccess"] != null && TempData["IsSuccess"].ToString() != "")
    {
        <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <p>@TempData["IsSuccess"]</p>
        </div>
    }

    @*Error Message*@
    if (TempData["IsErr"] != null && TempData["IsErr"].ToString() != "")
    {
        <div class="alert alert-danger alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <p>@TempData["IsErr"]</p>
        </div>
    }

    @*Page Title*@
    <div class="company-info">
        <div class="row">
            <div class="col-xs-12">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                <div class="colored-line-left"></div>
            </div>
        </div>
    </div>

    @*Confirm changes modal*@
    <div class="modal fade" id="confirmModal" name="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModal">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="confirmModal">Confirm Third Party Connection Changes</h4>
                </div>
                <div class="modal-body">
                    <p class="confirmation-message">
                        If you are disconnecting from Twilio, all users currently set up to use Text/SMS authentication will be notified that it has
                        been disabled and their Text/SMS settings will be reset.<br />
                        <br />
                        Are you sure you want to save?
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                    <button id="saveModal" name="saveModal" type="button" class="btn btn-thinkware">Save</button>
                </div>
            </div>
        </div>
    </div>

    @*Verify Code modal*@
    <div class="modal fade" id="verifycodeModal" name="verifycodeModal" tabindex="-1" role="dialog" aria-labelledby="verifycodeModal">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="verifycodeModal">Confirm Third Party Connection Changes</h4>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-xs-4 col-sm-4 col-xmd-4 col-lg-4">
                                <img src="~/Content/images/two-factor.png" />
                            </div>
                            <div class="col-xs-8 col-sm-8 col-md-8 col-lg-8">
                                <p>
                                    Enter the verification code we sent to:<br />
                                    @Model.TwilioPhoneNumber
                                </p>
                                @using (Html.BeginForm("Verifycode", "ThirdPartyConnections", FormMethod.Post, new { }))
                                {
                                    @Html.HiddenFor(x => Model.TwilioAuthToken)
                                    @Html.HiddenFor(x => Model.TwilioPhoneNumber)
                                    @Html.HiddenFor(x => Model.TwilioSID)
                                    @Html.HiddenFor(x => Model.TwilioVerificationSID)
                                    @Html.HiddenFor(x => Model.VerificationSid)
                                    @Html.TextBoxFor(x => Model.VerificationCode, new { @class = "form-control input-control-modal-text", autocomplete = "off" })
                                    <button id="btnVerify" name="btnVerify" type="submit" class="btn btn-thinkware verify-button" style="margin-top: 10px;">Verify</button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    using (Html.BeginForm("Index", "ThirdPartyConnections", FormMethod.Post, new { id = "frmThirdPartyIndex", name = "frmThirdPartyIndex", autocomplete = "off" }))
    {
        @* *** We only need hidden fields for values that are not editable (i.e. disabled controls, or viewmodel data that does not get edited by the user) *** *@
        @Html.HiddenFor(x => x.TwilioAccountVerified)

        @*Service Provider List*@
        <div class="container-fluid">

            @*Twilio*@
            <div class="row service-provider">
                <div class="col-xs-2 service-provider-logo">
                    <img src="~/Content/images/twilio-logo-transparent.png" alt="Twilio" title="Twilio" />
                </div>
                <div class="col-xs-10 service-provider-description">
                    <span>Twilio</span>
                    <p>
                        Twilio is a cloud communications platform as a service (PaaS) company based in San Francisco, California. Twilio allows
                        software developers to programmatically make and receive phone calls, send and receive text messages, and perform other
                        communication functions using its web service APIs.
                    </p>
                    <p>
                        To begin sending two factor authentication codes via Text/SMS using Twilio, first visit
                        <a href="https://www.twilio.com/" target="_blank">Twilio's website</a> and create an account.  Once your account is created,
                        fund the account and purchase a phone number.  Next, enter your Twilio account SID, auth token, and phone number below and save.
                        Once this information is saved, you will be able to enable Text/SMS on the
                        <a href="@Url.Action("SystemSetup", "TwoFactorAuthentication")">System Authentication Settings</a> page.
                    </p>
                </div>
            </div>

            <div class="row service-provider">
                @*Twilio SID*@
                <div class="col-xs-10 col-xs-push-2">
                    <div class="service-provider-label">
                        @Html.LabelFor(x => x.TwilioSID, new { @class = "control-label" })
                    </div>
                    <div class="service-provider-input-control button-inside">
                        @Html.TextBoxFor(x => x.TwilioSID, new { @class = "form-control hidden", autocomplete = "off" })
                        <button id="toggleAccountSID" name="toggleAccountSID" type="button">
                            <i id="eyeIconAccountSID" name="eyeIconAccountSID" class="fa fa-eye-slash" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
                <div class="col-xs-10 col-xs-push-2 service-provider-validation-message">
                    <div class="service-provider-label"></div>
                    @Html.ValidationMessageFor(x => x.TwilioSID, "", new { @class = "text-danger" })
                </div>

                @*Twilio Auth Token*@
                <div class="col-xs-10 col-xs-push-2">
                    <div class="service-provider-label">
                        @Html.LabelFor(x => x.TwilioAuthToken, new { @class = "control-label" })
                    </div>
                    <div class="service-provider-input-control button-inside">
                        @Html.TextBoxFor(x => x.TwilioAuthToken, new { @class = "form-control hidden", autocomplete = "off" })
                        <button id="toggleAuthToken" name="toggleAuthToken" type="button">
                            <i id="eyeIconAuthToken" name="eyeIconAuthToken" class="fa fa-eye-slash" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
                <div class="col-xs-10 col-xs-push-2 service-provider-validation-message">
                    <div class="service-provider-label"></div>
                    @Html.ValidationMessageFor(x => x.TwilioAuthToken, "", new { @class = "text-danger" })
                </div>

                @*Twilio Verify SID*@
                @if (bool.TryParse(System.Configuration.ConfigurationManager.AppSettings["UseTwilioVerify"], out var useTwilioVerify) && useTwilioVerify)
                {
                    <div class="col-xs-10 col-xs-push-2">
                        <div class="service-provider-label">
                            @Html.LabelFor(x => x.TwilioVerificationSID, new { @class = "control-label" })
                        </div>
                        <div class="service-provider-input-control button-inside">
                            @Html.TextBoxFor(x => x.TwilioVerificationSID, new { @class = "form-control hidden", autocomplete = "off" })
                            <button id="toggleVerificationSID" name="toggleVerificationSID" type="button">
                                <i id="eyeIconVerificationSID" name="eyeIconVerificationSID" class="fa fa-eye-slash" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-xs-10 col-xs-push-2 service-provider-validation-message">
                        <div class="service-provider-label"></div>
                        @Html.ValidationMessageFor(x => x.TwilioVerificationSID, "", new { @class = "text-danger" })
                    </div>
                }
                else
                {
                    @Html.HiddenFor(x => x.TwilioVerificationSID)
                }

                @*Phone Number*@
                <div class="col-xs-10 col-xs-push-2">
                    <div class="service-provider-label">
                        @Html.LabelFor(x => x.TwilioPhoneNumber, new { @class = "control-label" })
                    </div>
                    <div class="service-provider-input-control">
                        @Html.TextBoxFor(x => x.TwilioPhoneNumber, new { @class = "form-control" })
                    </div>
                </div>
                <div class="col-xs-10 col-xs-push-2 service-provider-validation-message">
                    <div class="service-provider-label"></div>
                    @Html.ValidationMessageFor(x => x.TwilioPhoneNumber, "", new { @class = "text-danger" })
                </div>
            </div>

            @*Save*@
            <div class="row buttons">
                <div class="col-xs-12">
                    <div class="pull-right">
                        <button type="button" class="btn btn-thinkware" data-toggle="modal" data-target="#confirmModal" data-backdrop="static">Save</button>
                    </div>
                </div>
            </div>
        </div>
    }
}

<script type="text/javascript">
    $(document).ready(function () {
        var sid = '@Model.VerificationSid';
        if (!!sid) {
            $('#verifycodeModal').modal('show');
        } else {
            $('#verifycodeModal').modal('hide');
        }

        @*Hide Twilio SID and Auth Token value on page load*@
        $("#toggleAccountSID").prev().attr("type", "password");
        $("#toggleAuthToken").prev().attr("type", "password");
        $("#toggleVerificationSID").prev().attr("type", "password");

        @*Prevent Account SID and Auth Token values from flashing on page load before they are converted to passwords*@
        $("#toggleAccountSID").prev().removeClass("hidden");
        $("#toggleAuthToken").prev().removeClass("hidden");
        $("#toggleVerificationSID").prev().removeClass("hidden");

        @*Show/Hide Twilio SID when the eye icon is clicked*@
        $("#toggleAccountSID").click(function(){
            var inputType = $(this).prev().attr("type");

            if (inputType === "password") {
                $(this).prev().attr("type", "text");
                $('#eyeIconAccountSID').removeClass('fa-eye-slash');
                $('#eyeIconAccountSID').addClass('fa-eye');
            }
            else {
                $(this).prev().attr("type", "password");
                $('#eyeIconAccountSID').removeClass('fa-eye');
                $('#eyeIconAccountSID').addClass('fa-eye-slash');
            }
        });

        @*Show/Hide Twilio Auth Token when the eye icon is clicked*@
        $("#toggleAuthToken").click(function(){
            var inputType = $(this).prev().attr("type");

            if (inputType === "password") {
                $(this).prev().attr("type", "text");
                $('#eyeIconAuthToken').removeClass('fa-eye-slash');
                $('#eyeIconAuthToken').addClass('fa-eye');
            }
            else {
                $(this).prev().attr("type", "password");
                $('#eyeIconAuthToken').removeClass('fa-eye');
                $('#eyeIconAuthToken').addClass('fa-eye-slash');
            }
        });

        @*Show/Hide Twilio Auth Token when the eye icon is clicked*@
        $("#toggleVerificationSID").click(function(){
            var inputType = $(this).prev().attr("type");

            if (inputType === "password") {
                $(this).prev().attr("type", "text");
                $('#eyeIconVerificationSID').removeClass('fa-eye-slash');
                $('#eyeIconVerificationSID').addClass('fa-eye');
            }
            else {
                $(this).prev().attr("type", "password");
                $('#eyeIconVerificationSID').removeClass('fa-eye');
                $('#eyeIconVerificationSID').addClass('fa-eye-slash');
            }
        });

        @*Un verify.......*@
        $('#TwilioSID').on('keyup', function () {
            AssessTwilioAccountVerification();
        });

        @*Un verify.......*@
        $('#TwilioAuthToken').on('keyup', function () {
            AssessTwilioAccountVerification();
        });

        @*Un verify.......*@
        $('#TwilioVerificationSID').on('keyup', function () {
            AssessTwilioAccountVerification();
        });

        @*Un verify.......*@
        $('#TwilioPhoneNumber').on('keyup', function () {
            AssessTwilioAccountVerification();
        });

        @*Show the submit confirmation modal*@
        $("#confirmModal").on("show.bs.modal", function (e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-body").load(link.attr("href"));
        });

        @*Submit the form when the modal is clicked*@
        $('#saveModal').on('click', function () {
            $('#frmThirdPartyIndex').submit();
        });



        // ------------------------------------------------------------
        // Determines whether saved account info has been modified and
        // un-verifies the info in the viewmodel if it has.
        // ------------------------------------------------------------
        function AssessTwilioAccountVerification() {
            var twilioSID = $('#TwilioSID').val();
            var twilioAuthToken = $('#TwilioAuthToken').val();
            var twilioPhoneNumber = $('#TwilioPhoneNumber').val();
            var twilioVerificationSID = $('#TwilioVerificationSID').val();

            if ('@Model.TwilioSID' !== twilioSID || '@Model.TwilioAuthToken' !== twilioAuthToken || '@Model.TwilioPhoneNumber' !== twilioPhoneNumber || '@Model.TwilioVerificationSID' !== twilioVerificationSID) {
                $('#TwilioAccountVerified').val(false);
            }
            else {
                $('#TwilioAccountVerified').val(true);
            }
        }
    });
</script>