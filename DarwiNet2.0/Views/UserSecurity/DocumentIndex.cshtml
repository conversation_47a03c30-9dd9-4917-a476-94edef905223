@using DarwiNet2._0.ViewModels;
@using DataDrivenViewEngine.Models.Core;
@model UserSecurityViewModel

@using DarwiNet2._0.Data;
@{
    ViewBag.Title = "Users";
    ViewBag.ParentCrumb = "Setup";
}

<style type="text/css">
    .toolbar .form-horizontal .form-group .row {
        margin: 10px;
    }
</style>

<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <h3>Editing @Model.SecurityType.ToString() Security</h3>
            <div class="colored-line-left"></div>
        </div>
        <p class="create-pad pull-right">
            @Html.ActionLink("Back to User", "Edit", "Users", new { id = Model.ID }, new { @class = "btn btn-thinkware" })
        </p>
    </div>
</div>
<div class="toolbar">
    <div class="form-horizontal">
        <div class="form-group">
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-6">
                        <label class="control-label col-md-3">User ID</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.UserID, new { @class = "form-control", disabled = true })
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="control-label col-md-4">Client ID</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.ClientID, new { @class = "form-control", disabled = true })
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-6">
                        <label class="control-label col-md-3">User Name</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.UserName, new { @class = "form-control", disabled = true })
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="control-label col-md-4">Client Name</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.ClientName, new { @class = "form-control", disabled = true })
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@{
    Html.RenderPartial("_DocumentSecurity", Model);
}

<script type="text/javascript">
    function error_handler(e) {
        if (e.errors) {
            var message = "Errors:\n";
            $.each(e.errors, function (key, value) {
                if ('errors' in value) {
                    $.each(value.errors, function () {
                        message += this + "\n";
                    });
                }
            });
            alert(message);
        }
    }
</script>