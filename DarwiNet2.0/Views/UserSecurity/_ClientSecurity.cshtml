@using DarwiNet2._0.Data;
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.ViewModels;
@using DarwiNet2._0.DNetSynch
@model UserClientSecurityViewModel
<div class="row">
    @(Html.Kendo().Grid<UserClientAccessViewModel>()
        .Name("grid")
        .Columns(columns =>
        {
            columns.Template(@<text></text>).ClientTemplate("<div style='text-align: center;'><input type='checkbox' #= Selected ? checked='checked':'' # class='chkbx' data-field='Selected' /></div>").Width(30);
            columns.Bound(p => p.ID).Hidden();
            columns.Bound(p => p.UserID).Hidden();
            columns.Bound(p => p.CompanyID).Hidden();
            columns.Bound(p => p.ClientID).Title("Client ID").Width(200);
            columns.Bound(p => p.ClientName).Title("Client Name");
            columns.Template(@<text></text>).ClientTemplate("<div style='text-align: center;'><input type='checkbox' #= OverridePayroll ? checked='checked':'' # class='chkbx' data-field='OverridePayroll' /></div>").Title("Override Payroll Approval").Width(200).HeaderHtmlAttributes(new { @class = "header-center" });
            columns.Template(@<text></text>).ClientTemplate("<div style='text-align: center;'><input type='checkbox' #= OverrideInvoice ? checked='checked':'' # class='chkbx' data-field='OverrideInvoice' /></div>").Title("Override Invoice Approval").Width(200).HeaderHtmlAttributes(new { @class = "header-center" });
            columns.Template(@<text></text>).ClientTemplate("<div style='text-align: center;'><input type='checkbox' #= BillCostAccess ? checked='checked':'' # class='chkbx' data-field='BillCostAccess' /></div>").Title("Access Bill vs Cost").Width(200).HeaderHtmlAttributes(new { @class = "header-center" });
            columns.Template(@<text></text>).ClientTemplate("<div style='text-align: center;'><input type='checkbox' #= DownloadACHFile ? checked='checked':'' # class='chkbx' data-field='DownloadACHFile' /></div>").Hidden(GlobalVariables.DNETOwnerID.ToLower() != "system").Title("Download ACH File").Width(200).HeaderHtmlAttributes(new { @class = "header-center" });
            columns.Template(@<text></text>) //.Title("Action")
                .ClientTemplate(
                    "<div class=' icon-center'><a href='" + Url.Action("RemoveClientAccess", "UserSecurity", new { id = "#= ID#", uid = @Model.UID }) + "' title='Remove access' class=''><i class='icon-red fa fa-times fa-fw fa-lg'></i></a></div>"
                    ).Width(60).HeaderHtmlAttributes(new { @class = "header-center" });
        })
        .Editable(editable => editable.Mode(GridEditMode.InCell))
        .Pageable()
        .Sortable()
        .Scrollable()
        .HtmlAttributes(new { style = "height:550px;" })
        .DataSource(dataSource => dataSource
            .Ajax()
            .Read(read => read.Action("GetAssignedClients", "UserSecurity", new { id = Model.UID }))
            .AutoSync(false)
            .PageSize(20)
            .Events(events => events.Error("error_handler"))
            .Model(model =>
            {
                model.Id(p => p.ClientID);
                model.Field(p => p.Selected).Editable(true);
                model.Field(p => p.ClientID).Editable(false);
                model.Field(p => p.ClientName).Editable(false);
                model.Field(p => p.OverridePayroll).Editable(true);
                model.Field(p => p.OverrideInvoice).Editable(true);
                model.Field(p => p.BillCostAccess).Editable(true);
                model.Field(p => p.DownloadACHFile).Editable(GlobalVariables.DNETOwnerID.ToLower() == "system");
            })
        )
    )
    *Changes made in grid will save automatically
</div>

<script type="text/javascript">
    $(function () {
        $('#grid').on('click', '.chkbx', function () {
            var dataItem = $('#grid').data().kendoGrid.dataItem($(this).closest('tr'));
            var checked = $(this).is(':checked');
            var field = $(this).attr('data-field');
            dataItem.set(field, checked);
            console.log(dataItem);
            if (field != "Selected") {
                var data = {
                    CompanyID: dataItem.CompanyID,
                    ClientID: dataItem.ClientID,
                    UserID: dataItem.UserID,
                    OverridePayroll: dataItem.OverridePayroll,
                    OverrideInvoice: dataItem.OverrideInvoice,
                    BillCostAccess: dataItem.BillCostAccess,
                    DownloadACHFile: dataItem.DownloadACHFile
                };
                $.ajax({
                    url: '@Url.Action("UpdateClientSecurity", "UserSecurity")',
                    type: "POST",
                    data: data
                });
            }
        })
    })
</script>
