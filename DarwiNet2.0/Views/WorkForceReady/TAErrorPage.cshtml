@model DarwiNet2._0.ViewModels.WorkForceReadyVM

@{
    ViewBag.Title = "TransAmerica - Error";
}

<div class="panel panel-default">
    <div class="panel-heading bg-danger text-white">
        <h4><i class="fa fa-exclamation-triangle"></i> TransAmerica SSO Error</h4>
    </div>

    <div style="height: 70vh" class="panel-body text-center p-4">
        <div class="alert alert-danger mt-4" role="alert">
            <i class="fa fa-exclamation-triangle fa-2x mb-3"></i>
            <h5 class="mb-3">TransAmerica Single Sign-On Failed</h5>
            <span id="status_text_err">
                @if (!string.IsNullOrEmpty(@TempData["ErrorMessage"] as string))
                {
                    @TempData["ErrorMessage"]
                }
                else
                {
                    <text>An unexpected error occurred while trying to connect to TransAmerica. Please try again or contact
                        support.</text>
                }
            </span>
        </div>

        <div class="mt-4">
            <a href="@Url.Action("TASSO", "WorkForceReady")" class="btn btn-primary mr-2">
                <i class="fa fa-refresh"></i> Try Again
            </a>
            <a href="@Url.Action("EmployeeIndex", "Dashboard")" class="btn btn-default">
                <i class="fa fa-arrow-left"></i> Return to Dashboard
            </a>
        </div>

        <div class="alert alert-info mt-4">
            <i class="fa fa-info-circle"></i>
            <strong>Need Help?</strong>
            <p class="mb-0">If you continue to experience issues, please contact your administrator or IT support.</p>
        </div>
    </div>

    <div class="panel-footer text-center">
        <small class="text-muted">
            <i class="fa fa-shield"></i>
            Secure single sign-on powered by Employer Flexible
        </small>
    </div>
</div>
