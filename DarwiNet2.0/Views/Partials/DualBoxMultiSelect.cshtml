<script>
        $().ready(function () {
            $('#add1').click(function () {
                return !$('#select1 option:selected').remove().appendTo('#Selected1');
            });
            $('#addAll1').click(function () {
                return !$('#select1 option').remove().appendTo('#Selected1');
            });
            $('#remove1').click(function () {
                return !$('#Selected1 option:selected').remove().appendTo('#select1');
            });
            $('#removeAll1').click(function () {
                return !$('#Selected1 option').remove().appendTo('#select1');
            });
            $('#add2').click(function () {
                return !$('#select2 option:selected').remove().appendTo('#Selected2');
            });
            $('#addAll2').click(function () {
                return !$('#select2 option').remove().appendTo('#Selected2');
            });
            $('#remove2').click(function () {
                return !$('#Selected2 option:selected').remove().appendTo('#select2');
            });
            $('#removeAll2').click(function () {
                return !$('#Selected2 option').remove().appendTo('#select2');
            });
            $('#add3').click(function () {
                return !$('#select3 option:selected').remove().appendTo('#Selected3');
            });
            $('#addAll3').click(function () {
                return !$('#select3 option').remove().appendTo('#Selected3');
            });
            $('#remove3').click(function () {
                return !$('#Selected3 option:selected').remove().appendTo('#select3');
            });
            $('#removeAll3').click(function () {
                return !$('#Selected3 option').remove().appendTo('#select3');
            });
            /*$('form').submit(function () {
                $('#Selected1 option').each(function (i) {
                    $(this).attr("selected", "selected");
                });
                $('#Selected2 option').each(function (i) {
                    $(this).attr("selected", "selected");
                });
                $('#Selected3 option').each(function (i) {
                    $(this).attr("selected", "selected");
                });
            });*/
            $('form').submit(function () {
                setSelect1();
                setSelect2();
                setSelect3();
            });
            function setSelect1() {
                var arr = [];
                $("#Selected1 > option").each(function () {
                    arr.push(this.value);
                });
                var str = arr.join(',');
                $('#itemSelect1').val(str);
            }
            function setSelect2() {
                var arr = [];
                $("#Selected2 > option").each(function () {
                    arr.push(this.value);
                });
                var str = arr.join(',');
                $('#itemSelect2').val(str);
            }
            function setSelect3() {
                var arr = [];
                $("#Selected3 > option").each(function () {
                    arr.push(this.value);
                });
                var str = arr.join(',');
                $('#itemSelect3').val(str);
            }
        });
</script>
