@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;

@{
    ViewBag.Title = "Company Calendar";
    ViewBag.ParentCrumb = "Company,Setup";
}
@section styles{
    <link href="~/Content/fullcalendar.css" rel="stylesheet" />
    <style>
        .k-nav-current > .k-link span + span {
            max-width: 200px;
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            vertical-align: top;
        }
    </style>
}
@*<div id="calendar"></div>*@

<div class="company-info">
    <div class="row">
        <div class="col-md-4">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-8">
            <div class="pull-right">
                <div id="team-schedule">
                    <div id="events">
                        <input checked type="checkbox" id="Birthdays" value="1"><strong class="calendar-select" style="border-bottom: 1px solid #f8a398">Birthdays</strong>&nbsp;&nbsp;
                        @*<label>Work Schedules</label><input checked type="checkbox" id="WS" value="2">*@
                        <input checked type="checkbox" id="WS" value="2"><strong class="calendar-select" style="border-bottom: 1px solid #51a0ed">Work Schedule </strong>
                       <input checked type="checkbox" id="PS" value="3"><strong class="calendar-select" style="border-bottom: 1px solid #56ca85">Payroll Schedules </strong>&nbsp;&nbsp;
                        <input checked type="checkbox" id="RD" value="4"><strong class="calendar-select" style="border-bottom: 1px solid #BF55EC">Next Review Dates </strong>&nbsp;&nbsp;
                        <input checked type="checkbox" id="PTO" value="5"><strong class="calendar-select" style="border-bottom: 1px solid #19B5FE">PTO </strong>&nbsp;&nbsp;
                        <input checked type="checkbox" id="HOL" value="6"><strong class="calendar-select" style="border-bottom: 1px solid #CF000F">Holidays </strong>
                        <input checked type="checkbox" id="OTH" value="7"><strong class="calendar-select" style="border-bottom: 1px solid #F4B350">Other Events </strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="calendar">
    <div id="scheduler"></div>
</div>


@section scripts{
    <script src="~/Scripts/moment.min.js"></script>
    <script src="~/Scripts/fullcalendar.min.js"></script>
    <script>

    $(function () {
        $("#scheduler").kendoScheduler({
            date: new Date(),
            startTime: new Date(),
            height: 700,
            views: [
                "day",
                "week",
                { type: "month", selected: true },
                /*"agenda"//,*/
                //"year"
            ],
            timezone: "Etc/UTC",
            dataSource: {
                batch: true,
                transport: {
                    read: {
                        url: "@Url.Action("getCalendarEntries", "CompanySetup")",
                        dataType: "json"
                    },
                    update: {
                        url: "@Url.Action("UpdateCustomCalendar", "CompanySetup")", //"//demos.telerik.com/kendo-ui/service/tasks/update",
                        dataType: "json"
                    },
                    create: {
                        url: "@Url.Action("CreateCustomCalendar", "CompanySetup")",
                    dataType: "json"
                },
                destroy: {
                    url: "@Url.Action("RemoveCustomCalendar", "CompanySetup")", //"//demos.telerik.com/kendo-ui/service/tasks/destroy",
                    dataType: "json"
                },
                parameterMap: function (options, operation) {
                    if (operation !== "read" && options.models) {
                        return { models: kendo.stringify(options.models) };
                    }
                }
            },
            schema: {
                model: {
                    id: "taskId",
                    fields: {
                        taskId: { from: "TaskID", type: "number" },
                        title: { from: "Title", defaultValue: "No title", validation: { required: true } },
                        start: { type: "date", from: "Start" },
                        end: { type: "date", from: "End" },
                        startTimezone: { from: "StartTimezone" },
                        endTimezone: { from: "EndTimezone" },
                        description: { from: "Description" },
                        recurrenceId: { from: "RecurrenceID" },
                        recurrenceRule: { from: "RecurrenceRule" },
                        recurrenceException: { from: "RecurrenceException" },
                        ownerId: { from: "OwnerID", defaultValue: 1 },
                        isAllDay: { type: "boolean", from: "IsAllDay" }
                    }
                }
            },
            //filter: {
            //    logic: "or",
            //    filters: [
            //        { field: "ownerId", operator: "eq", value: 1 },
            //        { field: "ownerId", operator: "eq", value: 2 },
            //        { field: "ownerId", operator: "eq", value: 3 }
            //    ]
            //}
        },
            resources: [
                {
                    field: "ownerId",
                    title: "Type",
                    dataSource: [
                        //{ text: "Birthdays", value: 1, color: "#f8a398" },
                        { text: "Work Schedules", value: 2, color: "#51a0ed" },
                        //{ text: "Payroll Schedules", value: 3, color: "#56ca85" },
                        //{ text: "Review Dates", value: 4, color: "#BF55EC" },
                        { text: "PTO", value: 5, color: "#19B5FE" },
                        { text: "Holidays", value: 6, color: "#CF000F" },
                        { text: "Other", value: 7, color: "#F4B350" }
                    ]
                }
            ]
    });
    $("#events :checkbox").change(function (e) {
        var checked = $.map($("#events :checked"), function (checkbox) {
            return parseInt($(checkbox).val());
        });

        var scheduler = $("#scheduler").data("kendoScheduler");

        scheduler.dataSource.filter({
            operator: function (task) {
                return $.inArray(task.ownerId, checked) >= 0;
            }
        });
    });


    });
    </script>
}
