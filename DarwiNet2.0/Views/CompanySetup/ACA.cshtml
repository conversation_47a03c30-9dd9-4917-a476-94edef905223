@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@model IEnumerable<DarwiNet2._0.Data.ACA_Setup>
@{
    ViewBag.Title = "Company ACA";
    ViewBag.ParentCrumb = "Company,Information";
}
<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>

    </div>
</div>
<div class="toolbar">
    <div class="row" style="padding-bottom: 10px;">
        <div class="col-md-3 pull-left">
            <div class="input-group">
                <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                <input type="text" class="form-control" id='FieldFilter' placeholder="Search Profile ID">
            </div>
        </div>
        <p class="create-pad pull-right">
            <a href="@Url.Action("CreateACA", "CompanySetup")" class="btn btn-thinkware">Create ACA</a>
        </p>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <table class="table" id="acaTable">
            <tr>
                @*<th>
                        @Html.DisplayNameFor(model => model.CompanyID)
                    </th>*@
                <th title="@FieldTranslation.GetLabel("Profile ID", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Profile ID", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Include Seasonal Variable", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Include Seasonal Variable", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Use Adjustment Hours", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Use Adjustment Hours", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Pay Method", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Pay Method", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Exclude Terminated", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Exclude Terminated", GlobalVariables.LanguageID)
                </th>

                <th class="hidden-filter" title="@FieldTranslation.GetLabel("Actions", GlobalVariables.LanguageID)">Actions</th>
            </tr>

            @foreach (var item in Model)
            {
                <tr>
                    @*<td>
                            @Html.DisplayFor(modelItem => item.CompanyID)
                        </td>*@
                    <td>
                        @Html.DisplayFor(modelItem => item.ProfileID)
                    </td>
                    <td>
                        @(item.Include_Seasonal_Variable ? Html.CheckBoxFor(modelItem => item.Include_Seasonal_Variable, new { @disabled = "disabled", @checked = "checked" }) : Html.CheckBoxFor(modelItem => item.Include_Seasonal_Variable, new { @disabled = "disabled" }))
                    </td>
                    <td>
                        @(item.Use_AdjHours ? Html.CheckBoxFor(modelItem => item.Use_AdjHours, new { @disabled = "disabled", @checked = "checked" }) : Html.CheckBoxFor(modelItem => item.Use_AdjHours, new { @disabled = "disabled" }))
                    </td>
                    <td>
                        @FieldTranslation.GetEnumDescription(typeof(enACAPayMethod), item.Pay_Method)
                    </td>
                    <td>
                        @(item.Exclude_Terminated ? Html.CheckBoxFor(modelItem => item.Exclude_Terminated, new { @disabled = "disabled", @checked = "checked" }) : Html.CheckBoxFor(modelItem => item.Exclude_Terminated, new { @disabled = "disabled" }))
                    </td>
                    <td>
                        <a href="@Url.Action("EditACA", "CompanySetup", new {id = @item.id})" title="Edit"><i class="icon-edit fa fa-pencil fa-lg fa-fw"></i></a>
                        <a href="#" title="Delete" id="delete" data-id="@item.id"><i class="icon-red fa fa-times fa-lg fa-fw"></i></a>
                    </td>
                </tr>
            }

        </table>
    </div>
</div>
@section scripts{
    @*<script src="~/Scripts/sweetalert.min.js"></script>*@
    <script src="~/Scripts/bootbox.min.js"></script>
    <script>
        $(document).ready(function () {
            var grid = $("#acaTable").kendoGrid({
                dataSource: {
                    pageSize: 15
                },
                sortable: true,
                pageable: true,
                filterable: true,
                scrollable: false,
                groupable: true,
                resizable: true
            }).data("kendoGrid");

        });
    </script>
    <script>
        $(document).ready(function () {
            $("#FieldFilter").keyup(function () {

                var value = $("#FieldFilter").val();
                var grid = $("#acaTable").data("kendoGrid");

                if (value) {
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            { field: "ProfileID", operator: "contains", value: value }
                        ]
                    })
                } else {
                    grid.dataSource.filter({});
                }
            });
        });
    </script>
    <script>
        $(document).on("click", "td #delete", function (e) {
            var deletedID = $(this).attr('data-id');
            var url = "@Url.Action("DeleteACAProfile", "CompanySetup", null)";
            bootbox.dialog({
                message: "Are you sure you want to delete this profile?",
                title: "Delete Profile",
                buttons: {
                    main: {
                        label: "Cancel",
                        className: "btn-primary",
                        callback: function () {
                            //Example.show("Primary button");
                        }
                    },
                    danger: {
                        label: "Delete",
                        className: "btn-danger",
                        callback: function () {
                            window.location.href = url + "/" + deletedID;
                        }
                    }
                }
            });
        });
    </script>

}
