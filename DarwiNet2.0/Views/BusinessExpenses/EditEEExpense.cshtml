@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@model DarwiNet2._0.Data.EmployeeBusinessExpens

@{
    Layout = null;
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    if (!String.IsNullOrEmpty(ViewBag.Response))
    {
        <div class="alert alert-danger" role="alert">
            @ViewBag.Response
        </div>
    }
    else
    {
        using (Html.BeginForm("EditEEExpense", "BusinessExpenses", FormMethod.Post, new { id = "editEEExp", ENCTYPE = "multipart/form-data" }))
        {
            @Html.AntiForgeryToken()

            <div class="form-horizontal">
                @Html.ValidationSummary(true, "", new {@class = "text-danger"})

                <div class="form-group">
                    @Html.LabelFor(model => model.Amount, htmlAttributes: new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        <input id="Amount" name="Amount" class="form-control field-required" required="required" value="@String.Format("{0:n2}", Model.Amount)"/>
                        @Html.ValidationMessageFor(model => model.Amount, "", new {@class = "text-danger"})
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Date, htmlAttributes: new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        @*<input id="Date" name="Date" class="form-control datepicker field-required" required="required" value="@FieldTranslation.ToShortDate(Model.Date.ToString())"/>*@
                        <input type="text" style="width: 100%" value="@FieldTranslation.ToShorterDate(Model.Date.ToString())" id="Date" name="Date" class="field-required"/>
                        <div class="error-msg"></div>
                        @Html.ValidationMessageFor(model => model.Date, "", new {@class = "text-danger"})
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">Expense Type</label>
                    <div class="col-md-10">
                        <select class="form-control" id="ExpenseType" name="ExpenseType">
                            @{
                                if (ViewBag.ExpTypes != null)
                                {
                                    foreach (Code_Description type in ViewBag.ExpTypes)
                                    {
                                        <option value="@type.Code" @((Model.ExpenseType == type.Code) ? "selected" : string.Empty)>@type.Description</option>
                                    }
                                }
                            }
                        </select>
                    </div>

                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">Vendor</label>
                    <div class="col-md-10">
                        <select class="form-control" id="Vendor" name="Vendor">
                            @{
                                if (ViewBag.Vendors != null)
                                {
                                    foreach (Code_Description vendor in ViewBag.Vendors)
                                    {
                                        <option value="@vendor.Code" @((Model.Vendor == vendor.Code) ? "selected" : string.Empty)>@vendor.Description</option>
                                    }
                                }
                            }
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">Department</label>
                    <div class="col-md-10">
                        <select class="form-control" id="Department" name="Department">
                            @{
                                if (ViewBag.Departments != null)
                                {
                                    foreach (Code_Description dept in ViewBag.Departments)
                                    {
                                        <option value="@dept.Code" @((Model.Department == dept.Code) ? "selected" : string.Empty)>@dept.Description</option>
                                    }
                                }
                            }
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">Position</label>
                    <div class="col-md-10">
                        <select class="form-control" id="Position" name="Position">
                            @{
                                if (ViewBag.Positions != null)
                                {
                                    foreach (Code_Description pos in ViewBag.Positions)
                                    {
                                        <option value="@pos.Code" @((Model.Position == pos.Code) ? "selected" : string.Empty)>@pos.Description</option>
                                    }
                                }
                            }
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-2 control-label">Attachment</label>
                    <div class="col-md-10">
                        <input type="file" id="file" name="file" />
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.Note, htmlAttributes: new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        @Html.TextAreaFor(model => model.Note, new {@class = "form-control", rows = "10"})
                        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
                    </div>
                </div>
                <input type="hidden" value="@Model.ID" name="recId" id="recId"/>
                <div class="form-group">
                    <div class="col-md-12">
                        <div class="pull-right">
                            <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                            <input type="submit" value="Save" class="btn btn-thinkware"/>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    if (ViewBag.Access == MenuAccessLevel.ReadOnly)
    {
        <script>
            $(document).ready(function () {
                $('#editEEExp input').attr('disabled', true);
                $('#editEEExp select').attr('disabled', true);
            })
        </script>
    }
    <script src="~/Scripts/jquery.datetimepicker.js"></script>
    <link href="~/Content/jquery.datetimepicker.css" rel="stylesheet"/>
    <script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
    if (ViewBag.Access != MenuAccessLevel.ReadOnly)
    {
        <script>
            $(document).ready(function() {
                $('#Date').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
            });
        </script>
    
    }
    else
    {
        <script>
            $(document).ready(function () {
                $('#Date').addClass('form-control');
            });
        </script>
    }
    <script>
        $("#editEEExp").validate({
            rules: {
                Date: {
                    required: true,
                    date: true
                }
            },
            errorPlacement: function (error, element) {
                if (element.attr("name") == "Date")
                    error.insertAfter(".error-msg");
                else
                    error.insertAfter(element);
            }
        });
    </script>
    <script>
        $(function () {
            $('#Date').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
            var today = new Date();
            $(".datepicker").datetimepicker({
                timepicker: false,
                format: 'm/d/Y',
                formatDate: 'm/d/Y'
            });
            $(".mindatepicker").datetimepicker({
                timepicker: false,
                format: 'm/d/Y',
                formatDate: 'm/d/Y',
                minDate: today
            });
            $(".datetimepicker").datetimepicker({
                formatTime: 'g:i A',
                format: 'm/d/Y h:i A'
            });
            $(".timepicker").datetimepicker({
                datepicker: false,
                format: 'g:i A',
                formatTime: 'g:i A'
            });
            //$('input').tooltip({
            //    placement: "right",
            //    trigger: "focus"
            //});
        });
        /*// mini jQuery plugin that formats to two decimal places
        (function ($) {
            $.fn.currencyFormat = function () {
                this.each(function (i) {
                    $(this).change(function (e) {
                        if (isNaN(parseFloat(this.value))) return;
                        this.value = parseFloat(this.value).toFixed(2);
                    });
                });
                return this; //for chaining
            }
        })(jQuery);

        // apply the currencyFormat behavior
        //$(function () {
        //    $('#currency').currencyFormat("{0:n2}%");
        //});
        $(function () {
            $('#Amount').currencyFormat("${0:n2}");
        });
        $('#Amount').keypress(function (event) {
            var $this = $(this);
            if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
               ((event.which < 48 || event.which > 57) &&
               (event.which != 0 && event.which != 8))) {
                event.preventDefault();
            }

            var text = $(this).val();
            if ((event.which == 46) && (text.indexOf('.') == -1)) {
                setTimeout(function () {
                    if ($this.val().substring($this.val().indexOf('.')).length > 3) {
                        $this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
                    }
                }, 1);
            }

            if ((text.indexOf('.') != -1) &&
                (text.substring(text.indexOf('.')).length > 2) &&
                (event.which != 0 && event.which != 8) &&
                ($(this)[0].selectionStart >= text.length - 2)) {
                event.preventDefault();
            }
        });*/
    </script>

    if (ViewBag.Locked == true)
    {
        <script>
            $(document).ready(function () {
                $('#editEEExp input').attr('disabled', true);
                $('#editEEExp select').attr('disabled', true);
            })
        </script>
    }
}
    
