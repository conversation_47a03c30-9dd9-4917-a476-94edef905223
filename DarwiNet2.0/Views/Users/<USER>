@using DataDrivenViewEngine.Models.Core
@{
    Layout = null;
}

@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div>
        @using (Html.BeginForm())
        {
            <div class="form-horizontal">
                <div class="form-group">
                    <label class="control-label col-md-2">Name</label>
                    <div class="col-md-10">
                        <input type="text" id="Name" name="Name" class="form-control"/>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-12">
                        <div class="pull-right">
                            <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-thinkware">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}