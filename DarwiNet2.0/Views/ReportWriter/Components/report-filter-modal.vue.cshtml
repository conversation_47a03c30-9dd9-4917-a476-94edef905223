@using DarwiNet2._0.Extensions;

<script type="text/x-template" id="report-filter-modal-template">
    <div id="report-filter-modal" class="report-filter-modal modal fade" tabindex="-1" aria-labelledby="report-filter-label">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content p-3">
                <div class="modal-header">
                    <div class="modal-title">
                        <h3 class="text">Filter</h3>
                    </div>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i class="fa fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col col-sm-12">
                                <div class="form-input" :class="{ 'has-error': errors?.fieldName }">
                                    <label>Field</label>
                                    <drop-down :disabled="!!reportFilter.id" :is-required="true" :is-searchable="true" :items="fields" :placeholder="'Please select...'" :selected-item-ids="reportFilter.fieldName ? [ reportFilter.tableName + '.' + reportFilter.fieldName ] : []" @@select-item="changeFilterField($event)" />
                                    <span v-if="errors?.fieldName" class="text-danger">{{ errors.fieldName }}</span>
                                </div>
                                <div class="form-input" :class="{ 'has-error': errors?.operator }">
                                    <label>Operator</label>
                                    <drop-down :items="filterOperators" :selected-item-ids="reportFilter.operator ? [ reportFilter.operator ] : []" @@select-item="reportFilter.operator = $event.id" />
                                    <span v-if="errors?.operator" class="text-danger">{{ errors.operator }}</span>
                                </div>
                                <div class="form-input">
                                    <div class="radio-button-list">
                                        <div class="radio-button">
                                            <input type="radio" name="value-type" id="value-type-value" value="value" :checked="operandType === OperandType.Value" @@change="changeOperandType(OperandType.Value)" />
                                            <label for="value-type-field">Filter by Value</label>
                                        </div>
                                        <div class="radio-button" v-if="isDateField">
                                            <input type="radio" name="value-type" id="value-type-field" value="field" :checked="operandType === OperandType.SmartValue" @@change="changeOperandType(OperandType.SmartValue)" />
                                            <label for="value-type-field">Filter by Smart Value</label>
                                        </div>
                                        <div class="radio-button">
                                            <input type="radio" name="value-type" id="value-type-field" value="field" :disabled="operandFields.length === 0" :checked="operandType === OperandType.Field" @@change="changeOperandType(OperandType.Field)" />
                                            <label for="value-type-field">Filter by Field</label>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="operandType === OperandType.Value" class="form-input" :class="{ 'has-error': errors?.operandValue }">
                                    <label>Filter Value</label>
                                    <input type="text" class="form-control" required v-model="reportFilter.operandValue" />
                                    <span v-if="errors?.operandValue" class="text-danger">{{ errors.operandValue }}</span>
                                </div>
                                <div v-else-if="operandType === OperandType.SmartValue" class="form-input" :class="{ 'has-error': errors?.operandSmartValue }">
                                    <label>Filter Smart Value</label>
                                    <drop-down :is-searchable="true" :items="operandSmartValues" :placeholder="'Please select...'" :selected-item-ids="reportFilter.operandSmartValue ? [ reportFilter.operandSmartValue ] : []" @@select-item="reportFilter.operandSmartValue = $event.id" />
                                    <span v-if="errors?.operandSmartValue" class="text-danger">{{ errors.operandSmartValue }}</span>
                                </div>
                                <div v-else class="form-input" :class="{ 'has-error': errors?.operandField }">
                                    <label>Filter Field</label>
                                    <drop-down :is-searchable="true" :items="operandFields" :placeholder="'Please select...'" :selected-item-ids="reportFilter.operandFieldName ? [ reportFilter.operandTableName + '.' + reportFilter.operandFieldName ] : []" @@select-item="changeOperandField($event)" />
                                    <span v-if="errors?.operandField" class="text-danger">{{ errors.operandField }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" :tabindex="0" @@click="selectFilterField()">OK</button>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    const OperandType = { Value: 'value', SmartValue: 'smart-value', Field: 'field' }

    var ReportFilterModalComponent = VueComponent('report-filter-modal', {
        components: [
            DropDownComponent
        ],
        props: {
            reportFields: { type: Array, default: () => { return [] } },
            filterField: { type: Object, default: null },
            filterGroup: { type: [Number, String], default: null}
        },
        data: function () {
            return {
                errors: null,
                operandType: null,
                reportFilter: {}
            };
        },
        setup() {
            return {
                OperandType
            };
        },
        created() {
            this.reportFilter = {
                id: this.filterField?.id || null,
                tableName: this.filterField?.tableName || null,
                fieldName: this.filterField?.fieldName || null,
                operator: this.filterField?.operator || null,
                operandValue: this.filterField?.operandValue || null,
                operandSmartValue: this.filterField?.operandSmartValue || null,
                operandTableName: this.filterField?.operandTableName || null,
                operandFieldName: this.filterField?.operandFieldName || null,
                filterGroup: this.filterField?.filterGroup || this.filterGroup || 0,
                fieldType: this.filterField?.fieldType || null
            };
            this.operandType = !!this.reportFilter.operandTableName && !!this.reportFilter.operandFieldName ? OperandType.Field : !!this.reportFilter.operandSmartValue ? OperandType.SmartValue : OperandType.Value;
        },
        computed: {
            fields() {
                return this.reportFields.map(field => {
                    return {
                        id: field.tableName + "." + field.fieldName,
                        tableName: field.tableName,
                        fieldName: field.fieldName,
                        text: field.tableDescription + " - " + field.fieldDescription
                    }
                });
            },
            filterOperators() {
                if ([2, 3, 5, 6, 7, 14, 134].includes(this.reportFilter.fieldType))
                    return [{ id: "=", text: "Equal To" }, { id: "!=", text: "Not Equal To" }, { id: "<", text: "Less Than" }, { id: "<=", text: "Less Than or Equal To" }, { id: ">", text: "Greater Than" }, { id: ">=", text: "Greater Than or Equal To" }];
                else if (this.reportFilter.fieldType === 11)
                    return [{ id: "=", text: "Equal To" }, { id: "!=", text: "Not Equal To" }];
                else
                    return [{ id: "=", text: "Equal To" }, { id: "!=", text: "Not Equal To" }, { id: "<", text: "Less Than" }, { id: "<=", text: "Less Than or Equal To" }, { id: ">", text: "Greater Than" }, { id: ">=", text: "Greater Than or Equal To" }, { id: "Starts With", text: "Starts With" }, { id: "Contains", text: "Contains" }];
            },
            isDateField() {
                return [7, 134].includes(this.reportFilter.fieldType);
            },
            operandFields() {
                return this.reportFields.filter(field => field.tableName !== this.reportFilter.tableName && field.fieldType === this.reportFilter.fieldType).map(field => {
                    return {
                        id: field.tableName + "." + field.fieldName,
                        tableName: field.tableName,
                        fieldName: field.fieldName,
                        text: field.tableDescription + " - " + field.fieldDescription
                    }
                });
            },
            operandSmartValues() {
                return [
                    { id: "TODAY", text: "TODAY" },
                    { id: "YESTERDAY", text: "YESTERDAY" },
                    { id: "THIS WEEK", text: "THIS WEEK" },
                    { id: "PREV WEEK", text: "PREV WEEK" },
                    { id: "THIS MONTH", text: "THIS MONTH" },
                    { id: "PREV MONTH", text: "PREV MONTH" },
                    { id: "THIS QUARTER", text: "THIS QUARTER" },
                    { id: "PREV QUARTER", text: "PREV QUARTER" },
                    { id: "THIS YEAR", text: "THIS YEAR" },
                    { id: "PREV YEAR", text: "PREV YEAR" }
                ];
            }
        },
        methods: {
            changeFilterField(field) {
                this.reportFilter.tableName = field.tableName;
                this.reportFilter.fieldName = field.fieldName;
                this.reportFilter.fieldType = this.reportFields.find(fld => fld.tableName === field.tableName && fld.fieldName === field.fieldName)?.fieldType || null;
                this.reportFilter.operandTableName = null;
                this.reportFilter.operandFieldName = null;
                this.operandType = OperandType.Value;
            },
            changeOperandField(operandField) {
                this.reportFilter.operandTableName = operandField.tableName;
                this.reportFilter.operandFieldName = operandField.fieldName;
            },
            changeOperandType(operandType) {
                this.operandType = operandType;
                this.reportFilter.operandValue = null;
                this.reportFilter.operandSmartValue = null;
                this.reportFilter.operandTableName = null;
                this.reportFilter.operandFieldName = null
            },
            selectFilterField() {
                this.errors = {};
                if (!this.reportFilter?.tableName) this.errors["tableName"] = "Table is required."
                if (!this.reportFilter?.fieldName) this.errors["fieldName"] = "Field is required."
                if (!this.reportFilter?.operator) this.errors["operator"] = "Operator is required."
                if (!this.reportFilter?.operandValue && this.operandType === OperandType.Value) this.errors["operandValue"] = "Value is required."
                if (!this.reportFilter?.operandSmartValue && this.operandType === OperandType.SmartValue) this.errors["operandSmartValue"] = "Smart Value is required."
                if (!this.reportFilter?.operandTableName && this.operandType === OperandType.Field) this.errors["operandTableName"] = "Table is required."
                if (!this.reportFilter?.operandFieldName && this.operandType === OperandType.Field) this.errors["operandFieldName"] = "Field is required."
                if (Object.keys(this.errors).length) return;
                if (this.filterField) this.$emit('change-report-filter', this.reportFilter); else this.$emit('add-report-filter', this.reportFilter)
                $('#report-filter-modal').modal('hide');
            }
       }
    });

</script>

<style lang="scss" scoped>
    .report-filter-modal {
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;

            h3 {
                margin: 0;
            }
        }

        .modal-body {
            .form-input {
                position: relative;
                margin-bottom: 15px;
                label {
                    font-weight: 600;
                    margin: 0;
                }
                .text-danger {
                    position: absolute;
                    right: 0;
                    font-size: 14px;
                }
                &.has-error {
                    .drop-down .btn {
                        border-color: #a94442;
                    }
                }
                .radio-button-list {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    gap: 60px;
                    .radio-button {
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        gap: 6px;
                        input[type=radio] {
                            margin: 0;
                        }
                    }
                }
            }
        }
    }
</style>