<script type="text/x-template" id="accordion-template">
    <div class="accordion">
        <div class="accordion-header" :class="{ 'is-expanded': !isMutableCollapsed }" @@click.stop="toggleContentState()">
            <div class="accordion-header-text">
                <i class="fas" :class="isMutableCollapsed ? 'fa-chevron-right' : 'fa-chevron-down'"></i>
                <div class="text">{{ text }}</div>
            </div>
            <div class="accordion-actions" @@click.stop>
                <slot name="accordion-actions"></slot>
            </div>
        </div>
        <div :id="`accordion-content${id}`" class="collapse" :class="{ 'show': !isMutableCollapsed }">
            <div class="accordion-body">
                <slot></slot>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    const AccordionComponent = VueComponent('accordion', {
        name: 'accordion',
        props: {
            isCollapsed: { type: Boolean, default: true },
            text: { type: String, default: null }
        },
        data() {
            return {
                isMutableCollapsed: this.isCollapsed
            }
        },
        computed: {
            id() {
                return this._uid;
            }
        },
        methods: {
            toggleContentState() {
                this.isMutableCollapsed = !this.isMutableCollapsed;
                this.$emit('change', this.isMutableCollapsed);
            }
        }
    });
</script>

<style lang="scss" scoped>
    .accordion {
        .accordion-header {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: transparent;
            border-style: none;
            border-radius: 4px;
            min-height: 32px;
            padding: 0 8px;
            cursor: pointer;
            &:hover::before {
                content: '';
                background-color: #6c757d;
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                opacity: 0.075;
            }
            &.is-expanded::before {
                content: '';
                background-color: #6c757d;
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                opacity: 0.15;
            }
            .accordion-header-text {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .fa-chevron-right, .fa-chevron-down {
                    width: 16px;
                    color: #6c757d;
                }

                .text {
                    margin-left: 9px;
                    font-size: 14px;
                    color: #404040;
                    white-space: nowrap;
                }
            }
            .accordion-actions {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                z-index: 1;
            }
        }

        .accordion-body {
            padding: 18px 20px 18px 20px;
        }
    }
</style>
