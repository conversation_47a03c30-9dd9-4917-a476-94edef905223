@using DarwiNet2._0.Extensions;

<script type="text/x-template" id="report-sort-modal-template">
    <div id="report-sort-modal" class="report-sort-modal modal fade" tabindex="-1" aria-labelledby="report-sort-label">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content p-3">
                <div class="modal-header">
                    <div class="modal-title">
                        <h3 class="text">Sort</h3>
                    </div>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><i class="fa fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col col-sm-12">
                                <div class="form-input" :class="{ 'has-error': errors?.fieldName }">
                                    <label>Field</label>
                                    <drop-down :disabled="!!reportSort.id" :is-required="true" :is-searchable="true" :items="reportSorts" :placeholder="'Please select...'" :invalid-item-ids="reportSortNames" :selected-item-ids="reportSort.fieldName ? [ reportSort.tableName + '.' + reportSort.fieldName ] : []" @@select-item="changeSortField($event)" />
                                    <span v-if="errors?.fieldName" class="text-danger">{{ errors.fieldName }}</span>
                                </div>
                                <div class="form-input" :class="{ 'has-error': errors?.type }">
                                    <label>Order</label>
                                    <drop-down :items="sortTypes" :selected-item-ids="reportSort.type ? [ reportSort.type ] : []" @@select-item="reportSort.type = $event.id" />
                                    <span v-if="errors?.type" class="text-danger">{{ errors.type }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" :tabindex="0" @@click="selectSortField()">OK</button>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    var ReportSortModalComponent = VueComponent('report-sort-modal', {
        components: [
            DropDownComponent
        ],
        props: {
            reportFields: { type: Array, default: () => { return [] } },
            sortFields: { type: Array, default: () => { return [] } },
            sortField: { type: Object, default: null }
        },
        data() {
            return {
                reportSort: {},
                errors: null
            };
        },
        created() {
            this.reportSort = {
                id: this.sortField?.id || null,
                tableName: this.sortField?.tableName || null,
                fieldName: this.sortField?.fieldName || null,
                type: this.sortField?.type || 1,
                order: this.sortField?.order || this.sortFields.length + 1
            };
        },
        computed: {
            reportSorts() {
                return this.reportFields.map(field => {
                    return {
                        id: field.tableName + "." + field.fieldName,
                        tableName: field.tableName,
                        fieldName: field.fieldName,
                        text: field.tableDescription + " - " + field.fieldDescription
                    }
                });
            },
            reportSortNames() {
                return this.sortFields.map(field => field.tableName + "." + field.fieldName);
            },
            sortTypes() {
                return [
                    { id: 1, text: "Ascending" },
                    { id: 2, text: "Descending" }
                ]
            }
        },
        methods: {
            changeSortField(value) {
                this.reportSort.tableName = value.tableName;
                this.reportSort.fieldName = value.fieldName;
            },
            selectSortField() {
                this.errors = {};
                if (!this.reportSort?.tableName) this.errors["tableName"] = "Table is required."
                if (!this.reportSort?.fieldName) this.errors["fieldName"] = "Field is required."
                if (!this.reportSort?.type) this.errors["type"] = "Operator is required."
                if (Object.keys(this.errors).length) return;
                this.$emit('change-report-sort', this.reportSort);
                $('#report-sort-modal').modal('hide');
            },
        }
    });

</script>

<style lang="scss" scoped>
    .report-sort-modal {
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;

            h3 {
                margin: 0;
            }
        }
        .modal-body {
            .form-input {
                position: relative;
                margin-bottom: 15px;
                label {
                    font-weight: 600;
                    margin: 0;
                }
                .text-danger {
                    position: absolute;
                    right: 0;
                    font-size: 14px;
                }
                &.has-error {
                    .drop-down .btn {
                        border-color: #a94442;
                    }
                }

            }
        }
    }
</style>