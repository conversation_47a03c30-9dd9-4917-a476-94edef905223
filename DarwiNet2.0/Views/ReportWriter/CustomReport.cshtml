@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Extensions;

@{
    ViewBag.Title = "Other Report";
}

<div id="CustomReport" class="custom-report">
    <template v-cloak>
        <div class="row">
            <div class="col-sm-12">
                <div class="panel panel-thinkware">
                    <div class="panel-heading">
                        <div class="header-group">
                            <i class="fa fa-arrow-left header-button" title="Back to Reports" @@click="goBack()"></i>
                            <div class="header-title">{{ reportID ? 'Edit Report' : 'New Report' }}</div>
                            <template v-if="reportName"><div>-</div><div>{{ reportName }}</div></template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-row">
            <div>
                <div class="btn-group btn-group-lg radioBtn" role="group">
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.Details ? 'active' : 'notActive']" @@click="activeTab = tabs.Details">Details</button>
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.DataSources ? 'active' : 'notActive']" :disabled="!customReport.name || (customReport.type === 1 && (customReport.companyID === 0 || customReport.clientID === '***'))" @@click="activeTab = tabs.DataSources">Data Sources</button>
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.Fields ? 'active' : 'notActive']" :disabled="!selectedTables.length" @@click="activeTab = tabs.Fields">Fields</button>
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.ColumnOrder ? 'active' : 'notActive']" :disabled="!customReport.fields.length" @@click="activeTab = tabs.ColumnOrder">Column Order</button>
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.Filters ? 'active' : 'notActive']" :disabled="!customReport.fields.length" @@click="activeTab = tabs.Filters">Filters</button>
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.Sorts ? 'active' : 'notActive']" :disabled="!customReport.fields.length" @@click="activeTab = tabs.Sorts">Sort</button>
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.UserAccess ? 'active' : 'notActive']" :disabled="!customReport.name || (customReport.creator !== '@ViewBag.ReportUser') || (customReport.type === 1 && (customReport.companyID === 0 || customReport.clientID === '***'))" @@click="activeTab = tabs.UserAccess">User Access</button>
                    <button type="button" class="btn btn-primary" :class="[activeTab == tabs.Preview ? 'active' : 'notActive']" :disabled="!customReport.fields.length" @@click="activeTab = tabs.Preview">Preview</button>
                </div>
            </div>
            <div class="settings">
                <toggle v-if="!customReport.parentID" :label="'Roll-Down Changes'" :value="customReport.rollDown" @@input="customReport.rollDown = $event"></toggle>
                <toggle :disabled="!isReadyEnabled" :label="'Ready for Use'" :value="isReady" @@input="isReady = $event"></toggle>
            </div>
            <div class="pull-right">
                <button type="button" class="btn btn-thinkware" :disabled="!selectedTables.length || !customReport.fields.length" @@click="saveCustomReport()">Save Report</button>
            </div>
        </div>
        <div class="panel panel-thinkware margin-top-5">
            <div v-if="activeTab === tabs.Details" class="panel-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="control-label col-md-2" for="name">Report Name</label>
                        <div class="col-md-4" :class="{ 'has-error': errors?.name }">
                            <input type="text" id="name" class="form-control" v-model="customReport.name" @@change="reportName = customReport.name">
                            <span v-if="errors?.name" class="text-danger">{{ errors.name }}</span>
                        </div>
                        <label class="control-label col-md-2" for="page-size">Page Size</label>
                        <div class="col-md-4" :class="{ 'has-error': errors?.pageSize }">
                            <input type="number" id="page-size" class="form-control" min="0" v-model.number="customReport.pageSize">
                            <span v-if="errors?.pageSize" class="text-danger">{{ errors.pageSize }}</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-2" for="type">Report Type</label>
                        <div class="col-md-4" :class="{ 'has-error': errors?.type }">
                            <drop-down :items="reportTypes" :disabled="'@ViewBag.DNETLevel' !== 'System' || !!customReport.id" :selected-item-ids="customReport.type !== null ? [ customReport.type ] : []" @@select-item="changeReportType($event.id)" />
                            <span v-if="errors?.type" class="text-danger">{{ errors.type }}</span>
                        </div>
                        <template v-if="'@ViewBag.DNETLevel' === 'System'">
                            <label class="control-label col-md-2" for="company-id">Company</label>
                            <div class="col-md-4" :class="{ 'has-error': errors?.companyID }">
                                <drop-down id="company-id" :is-required="true" :is-searchable="true" :items="companies" :selected-item-ids="customReport.companyID !== null ? [ customReport.companyID ] : []" @@select-item="changeCompanyID($event.id)" />
                                <span v-if="errors?.type" class="text-danger">{{ errors.companyID }}</span>
                            </div>
                        </template>
                    </div>
                    <div v-if="'@ViewBag.DNETLevel' === 'System' && customReport.type === 1" class="form-group"> <!-- client report type  --->
                        <div class="col-md-6"></div>
                        <label class="control-label col-md-2" for="client-id">Client</label>
                        <div class="col-md-4" :class="{ 'has-error': errors?.clientID }">
                            <drop-down id="client-id" :is-required="true" :is-searchable="true" :items="clients" :selected-item-ids="customReport.clientID !== null ? [ customReport.clientID ] : []" @@select-item="changeClientID($event.id)" />
                            <span v-if="errors?.type" class="text-danger">{{ errors.clientID }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else-if="activeTab === tabs.DataSources" class="panel-body">
                <div class="row margin-bottom-10">
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                            <input type="text" class="form-control" placeholder="Search Table Name or Field Name" :value="reportTableSearchText" @@input="changeReportTableSearch($event.target.value)">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div v-if="customReport.type === 0 && reportTableCategories.length > 0"> <!-- system level report -->
                            <accordion v-for="(category, index) in reportTableCategories" :key="index" :text="category" :is-collapsed="index !== 0">
                                <div class="check-box-list">
                                    <check-box v-for="reportTable in filteredReportTablesByCategory(category)" :key="reportTable.name" :label="reportTable.description" :value="reportTable.isSelected" @@input="changeReportTable(reportTable, $event)" />
                                </div>
                            </accordion>
                        </div>
                        <div v-else class="check-box-list">
                            <check-box v-for="reportTable in filteredReportTables" :key="reportTable.name" :label="reportTable.description" :value="reportTable.isSelected" @@input="changeReportTable(reportTable, $event)" />
                        </div>
                    </div>
                </div>
            </div>
            <div v-else-if="activeTab === tabs.Fields" class="panel-body">
                <accordion v-for="reportTable in selectedTables" :key="reportTable.name" :text="reportTable.description" :is-collapsed="false">
                    <grid-table
                        :columns="fieldColumns"
                        :items="customReport.fields.filter(x => x.tableName === reportTable.name)"
                        :has-table-actions="true"
                        :has-item-custom-actions="true"
                        :is-item-deletable="true"
                    >
                        <template v-slot:custom-table-actions>
                            <div class="button-group">
                                <button type="button" class="btn btn-thinkware" @@click="showReportQuickAddFieldsDialog(reportTable)">Quick Add Fields</button>
                                <button type="button" class="btn btn-thinkware" @@click="showReportFieldDialog(reportTable)">Add Field</button>
                            </div>
                        </template>
                        <template slot="fieldName" slot-scope="slotScope">
                            <a class="link" href="javascript:" @@click="showReportFieldDialog(reportTable, slotScope.item)">{{ slotScope.item.fieldName }}</a>
                        </template>
                        <template slot="isHidden" slot-scope="slotScope">
                            <toggle :value="slotScope.item.isHidden" @@input="slotScope.item.isHidden = $event;if ($event) slotScope.item.sectionFld = false" />
                        </template>
                        <template slot="isGroupBy" slot-scope="slotScope">
                            <toggle :value="slotScope.item.isGroupBy" @@input="slotScope.item.isGroupBy = $event;if ($event) slotScope.item.sectionFld = false" />
                        </template>
                        <template slot="isSummarize" slot-scope="slotScope">
                            <toggle :value="slotScope.item.isSummarize" @@input="slotScope.item.isSummarize = $event;if ($event) slotScope.item.sectionFld = false" />
                        </template>
                        <template slot="sectionFld" slot-scope="slotScope">
                            <toggle :disabled="!isDateField(slotScope.item.fieldType) && !isTextField(slotScope.item.fieldType)" :value="slotScope.item.sectionFld" @@input="slotScope.item.sectionFld = $event;if ($event) { slotScope.item.isHidden = false; slotScope.item.isGroupBy = false; slotScope.item.isSummarize = false; slotScope.item.pivotFld = false; slotScope.item.calcFld = false; };resetSectionFlds(slotScope.item)" />
                        </template>
                        <template slot="pivotFld" slot-scope="slotScope">
                            <toggle :disabled="slotScope.item.calcFld" :value="slotScope.item.pivotFld" @@input="slotScope.item.pivotFld = $event;if ($event) slotScope.item.sectionFld = false;resetPivotFlds(slotScope.item)" />
                        </template>
                        <template slot="calcFld" slot-scope="slotScope">
                            <toggle :disabled="slotScope.item.pivotFld" :value="slotScope.item.calcFld" @@input="slotScope.item.calcFld = $event;if ($event) slotScope.item.sectionFld = false;resetCalcFlds(slotScope.item)" />
                        </template>
                        <template slot="item-custom-action" slot-scope="slotScope">
                            <button type="button" tabindex="0" @@click="showReportFieldDialog(reportTable, slotScope.item)"><i class="fa fa-pencil"></i></button>
                            <button type="button" tabindex="0" @@click="deleteReportField(slotScope.item)"><i class="fa fa-times"></i></button>
                        </template>
                    </grid-table>
                </accordion>
            </div>
            <div v-else-if="activeTab === tabs.ColumnOrder" class="panel-body">
                <div class="row">
                    <div class="col-sm-5">
                        <sortable-list :items="sortableFields" @@change-order="changeFieldOrder($event)" />
                    </div>
                </div>
            </div>
            <div v-else-if="activeTab === tabs.Filters" class="panel-body">
                <div class="filter-groups">
                    <accordion v-for="filterGroup in customReport.filterGroups" :key="filterGroup" :text="'OR'" :is-collapsed="false">
                        <template v-slot:accordion-actions>
                            <button type="button" tabindex="0" title="Delete" v-if="filterGroup !== 0 && groupFilters(filterGroup).length === 0" @@click="deleteReportFilterGroup(filterGroup)"><i class="fa fa-times"></i></button>
                        </template>
                        <grid-table
                            :columns="filterColumns"
                            :items="groupFilters(filterGroup)"
                            :has-table-actions="true"
                            :has-item-custom-actions="true"
                            :is-item-deletable="true"
                        >
                            <template v-slot:custom-table-actions>
                                <button type="button" class="btn btn-thinkware" @@click="showReportFilterDialog(filterGroup)">Add Filter</button>
                            </template>
                            <template slot="tableName" slot-scope="slotScope">
                                <a class="link" href="javascript:" @@click="showReportFilterDialog(filterGroup, slotScope.item)">{{ getReportField(slotScope.item)?.tableDescription }}</a>
                            </template>
                            <template slot="fieldName" slot-scope="slotScope">
                                <a class="link" href="javascript:" @@click="showReportFilterDialog(filterGroup, slotScope.item)">{{ getReportField(slotScope.item)?.fieldDescription }}</a>
                            </template>
                            <template slot="operator" slot-scope="slotScope">
                                {{ getFilterOperator(slotScope.item) }}
                            </template>
                            <template slot="value" slot-scope="slotScope">
                                {{ getFilterOperand(slotScope.item) }}
                            </template>
                            <template slot="conditionOperator" slot-scope="slotScope">
                                {{ groupFilters(filterGroup).findIndex(filter => JSON.stringify(filter) === JSON.stringify(slotScope.item)) < groupFilters(filterGroup).length - 1 ? "AND" : null }}
                            </template>
                            <template slot="item-custom-action" slot-scope="slotScope">
                                <button type="button" tabindex="0" title="Edit" @@click="showReportFilterDialog(filterGroup, slotScope.item)"><i class="fa fa-pencil"></i></button>
                                <button type="button" tabindex="0" title="Delete" @@click="deleteReportFilter(slotScope.item)"><i class="fa fa-times"></i></button>
                            </template>
                        </grid-table>
                        <div v-if="!filterGroup" class="row margin-top-20">
                            <div class="col-sm-12">
                                <button type="button" class="btn btn-thinkware pull-right" v-if="groupFilters(filterGroup).length > 0" @@click="addFilterGroup()">Add 'OR' Group</button>
                            </div>
                        </div>
                    </accordion>
                </div>
            </div>
            <div v-else-if="activeTab === tabs.Sorts" class="panel-body">
                <grid-table
                    :columns="sortColumns"
                    :items="sortedSorts"
                    :has-table-actions="true"
                    :has-item-custom-actions="true"
                    :is-item-deletable="true"
                >
                    <template v-slot:custom-table-actions>
                        <button type="button" class="btn btn-thinkware" @@click="showReportSortDialog()">Add Sort</button>
                    </template>
                    <template slot="tableName" slot-scope="slotScope">
                        <a class="link" href="javascript:" @@click="showReportSortDialog(slotScope.item)">{{ getReportField(slotScope.item)?.tableDescription }}</a>
                    </template>
                    <template slot="fieldName" slot-scope="slotScope">
                        <a class="link" href="javascript:" @@click="showReportSortDialog(slotScope.item)">{{ getReportField(slotScope.item)?.fieldDescription }}</a>
                    </template>
                    <template slot="type" slot-scope="slotScope">
                        {{ slotScope.item.type === 2 ? "Descending" : "Ascending" }}
                    </template>
                    <template slot="item-custom-action" slot-scope="slotScope">
                        <button type="button" tabindex="0" title="Edit" @@click="showReportSortDialog(slotScope.item)"><i class="fa fa-pencil"></i></button>
                        <button type="button" tabindex="0" title="Move Up" :disabled="slotScope.item.order === Math.min(...customReport.sorts.map(x => x.order)) " @@click="moveUpSort(slotScope.item)"><i class="fa fa-arrow-up"></i></button>
                        <button type="button" tabindex="0" title="Move Down" :disabled="slotScope.item.order === Math.max(...customReport.sorts.map(x => x.order))" @@click="moveDownSort(slotScope.item)"><i class="fa fa-arrow-down"></i></button>
                        <button type="button" tabindex="0" title="Delete" @@click="deleteReportSort(slotScope.item)"><i class="fa fa-times"></i></button>
                    </template>
                </grid-table>
            </div>
            <div v-else-if="activeTab === tabs.UserAccess" id="user-access-tab" class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="radio-button-list">
                            <div class="radio-button">
                                <input type="radio" name="value-type" id="value-type-value" value="value" :checked="customReport.isPublic" @@change="customReport.isPublic = true; customReport.assignments = []" />
                                <label for="value-type-field">All Users</label>
                            </div>
                            <div class="radio-button">
                                <input type="radio" name="value-type" id="value-type-field" value="field" :checked="!customReport.isPublic" @@change="customReport.isPublic = false" /> <!--:disabled="users.length === 0"-->
                                <label for="value-type-field">Selected Users</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 margin-top-15">
                        <grid-table v-if="!customReport.isPublic"
                            class="padding-left-25 padding-right-25"
                            :columns="userColumns"
                            :items="filteredUsers"
                            :is-searchable="true"
                            @@change-search-text="userSearchText = $event"
                        >
                            <template slot="canEdit" slot-scope="slotScope">
                                <toggle :value="slotScope.item.canEdit" @@input="changeUserEditAccess(slotScope.item, $event)" />
                            </template>
                            <template slot="canView" slot-scope="slotScope">
                                <toggle :value="slotScope.item.canView" @@input="changeUserViewAccess(slotScope.item, $event)" />
                            </template>
                        </grid-table>
                    </div>
                </div>
            </div>
            <div v-else-if="activeTab === tabs.Preview" class="panel-body">
                <div class="row">
                    <div class="col-sm-12">
                        <report-previewer :custom-report="customReport" />
                    </div>
                </div>
            </div>
            <report-quick-add-fields-modal :key="`fields${reportQuickAddFieldsModalKey}`" :report-table="reportTable" :custom-report-field-names="customReportFieldNames" @@add-report-fields="addReportFields($event)"></report-quick-add-fields-modal>
            <report-field-modal :key="`field${reportFieldModalKey}`" :report-table="reportTable" :report-field="reportField" :custom-report-field-names="customReportFieldNames" @@change-report-field="changeReportField($event)"></report-field-modal>
            <report-filter-modal :key="`filter${reportFilterModalKey}`" :report-fields="customReport.fields" :filter-field="filterField" :filter-group="filterGroup" @@add-report-filter="addFilter($event)" @@change-report-filter="changeReportFilter($event)"></report-filter-modal>
            <report-sort-modal :key="`sort${reportSortModalKey}`" :report-fields="customReport.fields" :sort-fields="customReport.sorts" :sort-field="sortField" @@change-report-sort="changeReportSort($event)"></report-sort-modal>
        </div>
    </template>
    <loading :active="isLoading" :is-full-page="true" :enforce-focus="true" loader="dots" />
</div>

<script src="https://cdn.jsdelivr.net/npm/vue-toastr/dist/vue-toastr.umd.min.js"></script>
<script src="~/Scripts/vue-loading-overlay.js"></script>
<link href="~/Scripts/vue-loading.css" rel="stylesheet">

@Html.VueComponent("accordion")
@Html.VueComponent("check-box")
@Html.VueComponent("search-input")
@Html.VueComponent("drop-down")
@Html.VueComponent("grid-table")
@Html.VueComponent("toggle")
@Html.VueComponent("report-field-modal")
@Html.VueComponent("report-filter-modal")
@Html.VueComponent("report-quick-add-fields-modal")
@Html.VueComponent("report-sort-modal")
@Html.VueComponent("report-previewer")
@Html.VueComponent("sortable-list")

<script type="text/javascript">
    Vue.use(VueToastr, {
        defaultPosition: 'toast-bottom-right',
        defaultType: 'info',
        defaultTimeout: 2000
    });
    Vue.use(VueLoading);
    Vue.component('loading', VueLoading)

    var vm = new VueInstance('CustomReport', {
        components: [
            VueLoading,
            AccordionComponent,
            CheckBoxComponent,
            DropDownComponent,
            GridTableComponent,
            ToggleComponent,
            ReportFieldModalComponent,
            ReportFilterModalComponent,
            ReportQuickAddFieldsModalComponent,
            ReportSortModalComponent,
            ReportPreviewerComponent,
            SortableListComponent
        ],
        data: {
            activeTab: null,
            clients: [],
            companies: [],
            customReport: {
                id: null,
                companyID: @ViewBag.CompanyID,
                clientID: '@ViewBag.ClientID',
                creator: '@GlobalVariables.DNETOwnerID',
                name: null,
                type: @ViewBag.ReportType,
                status: 0,
                pageSize: 10,
                isPublic: false,
                parentID: null,
                currentUser: '@GlobalVariables.DNETOwnerID',
                userSecurity: null,
                rollDown: false,
                assignments: [],
                fields: [],
                filterGroups: [0],
                filters: [],
                sorts: []
            },
            errors: null,
            filterField: null,
            filterGroup: null,
            isLoading: false,
            originalReport: {},
            reportID: '@ViewBag.ReportID',
            reportFieldModalKey: 0,
            reportFilterModalKey: 0,
            reportQuickAddFieldsModalKey: 0,
            reportSortModalKey: 0,
            reportName: null,
            reportField: null,
            reportTable: null,
            reportTables: [],
            reportTableSearchText: null,
            sortField: null,
            tabs: {
                Details: "details",
                DataSources: "datasources",
                Fields: "fields",
                ColumnOrder: "columnorder",
                Filters: "filters",
                Sorts: "sorts",
                UserAccess: "useraccess",
                Preview: "preview"
            },
            users: [],
            userSearchText: null
        },
        async created() {
            this.isLoading = true;
            if (this.reportID) this.customReport = await this.fetchCustomReport(this.reportID);
            this.reportName = this.customReport.name;
            this.originalReport = JSON.parse(JSON.stringify(this.customReport));
            Promise.all([
                this.fetchReportTables(),
                this.fetchUsers(this.customReport.companyID, this.customReport.clientID),
                '@ViewBag.DNETLevel' === 'System' ? this.fetchCompanies() : Promise.resolve([]),
                '@ViewBag.DNETLevel' === 'System' && this.customReport.type === 1 ? this.fetchClients(this.customReport.companyID) : Promise.resolve([])
            ]).then(data => {
                this.reportTables = data[0].map(table => ({ ...table, isSelected: this.customReport.fields.some(field => field.tableName === table.name) }));
                this.users = data[1];
                this.companies = data[2].map(company => ({ ...company, id: Number(company.id) }));
                this.clients = data[3];
            })
                .finally(() => {
                    this.isLoading = false;
                })
            this.activeTab = this.tabs.Details;
            window.addEventListener('beforeunload', this.beforeUnload)
        },
        beforeDestroy() {
            window.removeEventListener('beforeunload', this.beforeUnload)
        },
        computed: {
            customReportFieldNames() {
                return this.customReport.fields.filter(field => field.tableName === this.reportTable?.name).map(field => field.fieldName);
            },
            fieldColumns() {
                return [
                    { id: 'fieldName', dataType: 'string', label: 'Field', customRender: 'fieldName' },
                    { id: 'label', dataType: 'string', label: 'Label' },
                    { id: 'isHidden', dataType: 'boolean', label: 'Hidden', width: '90px', customRender: 'isHidden' },
                    { id: 'isGroupBy', dataType: 'boolean', label: 'Grouped By', width: '90px', customRender: 'isGroupBy' },
                    { id: 'isSummarize', dataType: 'boolean', label: 'Totals', width: '90px', customRender: 'isSummarize' },
                    { id: 'function', dataType: 'string', label: 'Function', width: '96px' },
                    { id: 'sectionFld', dataType: 'boolean', label: 'Section Field', width: '90px', customRender: 'sectionFld' },
                    { id: 'pivotFld', dataType: 'boolean', label: 'Pivot Field', width: '90px', customRender: 'pivotFld' },
                    { id: 'calcFld', dataType: 'boolean', label: 'Calc Pivot Field', width: '120px', customRender: 'calcFld' }
                ]
            },
            filterColumns() {
                return [
                    { id: 'tableName', dataType: 'string', label: 'Table', customRender: 'tableName' },
                    { id: 'fieldName', dataType: 'string', label: 'Field', customRender: 'fieldName' },
                    { id: 'operator', dataType: 'string', label: 'Operator', customRender: 'operator' },
                    { id: 'value', dataType: 'string', label: 'Filter Value', customRender: 'value' },
                    { id: 'conditionOperator', dataType: 'string', label: '', textAlign: 'center', width: '60px', customRender: 'conditionOperator' }
                ]
            },
            filteredReportTables() {
                let reportTables = this.reportTables.filter(table => table.accessLevel === this.customReport.type);
                reportTables.forEach(table => {
                    table.fields = table.fields.filter(field => field.accessLevel === this.customReport.type || field.accessLevel === 2);
                });
                if (!this.reportTableSearchText) return reportTables;
                return reportTables.reduce((tables, table) => {
                    if (table.description.toLowerCase().includes(this.reportTableSearchText.toLowerCase()) ||
                        table.fields.some(field => field.fieldName.toLowerCase().includes(this.reportTableSearchText.toLowerCase()))) {
                        tables.push(table);
                    }
                    return tables;
                }, []);
            },
            filteredReportTablesByCategory() {
                return (category) => {
                    return this.filteredReportTables.filter(table => table.category === category && table.accessLevel === this.customReport.type);
                }
            },
            filteredUsers() {
                return this.userSearchText
                    ? this.users.filter(user => {
                        return user.clientID.toLowerCase().includes(this.userSearchText.toLocaleLowerCase()) || user.userName.toLowerCase().includes(this.userSearchText.toLocaleLowerCase());
                    })
                    : this.users;
            },
            groupFilters() {
                return (filterGroup) => {
                    return this.customReport.filters.filter(filter => filter.filterGroup === filterGroup);
                }
            },
            isReady: {
                get() {
                    return this.customReport.status === 2
                },
                set(value) {
                    this.customReport.status = value ? 2 : this.customReport.id ? 1 : 0;
                }
            },
            isReadyEnabled() {
                return this.isReady ||
                    (this.customReport.fields.some(field => field.pivotFld) && this.customReport.fields.some(field => field.calcFld)) ||
                    (!this.customReport.fields.some(field => field.pivotFld) && !this.customReport.fields.some(field => field.calcFld));
            },
            reportFilterFields() {
                return this.customReport.fields
                    .sort((a, b) => (a.seqNbr > b.seqNbr ? 1 : -1))
                    .map(field => { return { id: field.tableName + '.' + field.fieldName, text: field.tableDescription + ' - ' + field.fieldDescription } });
            },
            reportTableCategories() {
                return [...new Set(this.filteredReportTables.filter(table => !!table.category).map(table => table.category))];
            },
            reportTypes() {
                return [
                    { id: 0, text: "System" },
                    { id: 1, text: "Client" }
                ]
            },
            selectedTables() {
                return this.reportTables.filter(reportTable => reportTable.isSelected);
            },
            sortColumns() {
                return [
                    { id: 'tableName', dataType: 'string', label: 'Table', customRender: 'tableName' },
                    { id: 'fieldName', dataType: 'string', label: 'Field', customRender: 'fieldName' },
                    { id: 'type', dataType: 'string', label: 'Order', customRender: 'type' }
                ]
            },
            sortableFields() {
                return this.customReport.fields
                    .sort((a, b) => (a.seqNbr > b.seqNbr ? 1 : -1))
                    .map(field => { return { id: field.tableName + '.' + field.fieldName, text: field.tableDescription + ' - ' + field.fieldDescription } });
            },
            sortedSorts() {
                return this.customReport.sorts.sort((a, b) => (a.order > b.order ? 1 : -1));
            },
            userColumns() {
                let columns = [
                    { id: 'userID', dataType: 'string', label: 'User ID' },
                    { id: 'userName', dataType: 'string', label: 'User' },
                    { id: 'userType', dataType: 'string', label: 'User Type' },
                    { id: 'canEdit', dataType: 'boolean', label: 'Can Edit', width: '96px', customRender: 'canEdit' },
                    { id: 'canView', dataType: 'boolean', label: 'Can View', width: '96px', customRender: 'canView' }
                ];
                if (this.customReport.type === 0) { // system level report
                    columns.unshift({ id: 'clientID', dataType: 'string', label: 'Client' });
                    columns.unshift({ id: 'companyID', dataType: 'string', label: 'Company' });
                }
                return columns;
            }
        },
        methods: {
            addFilterGroup() {
                this.customReport.filterGroups.push(Math.max(...this.customReport.filterGroups) + 1);
            },
            addFilter(field) {
                this.customReport.filters.push(field);
            },
            addReportFields(fields) {
                this.customReport.fields = this.customReport.fields.concat(fields);
            },
            beforeUnload(e) {
                if (JSON.stringify(this.customReport) !== JSON.stringify(this.originalReport)) {
                    e.returnValue = "Are you sure that you want to leave this page?  Unsaved changes will be lost.";
                }
            },
            changeClientID(id) {
                this.customReport.clientID = id;
                this.customReport.isPublic = false;
                this.customReport.assignments = [];
                this.getUsers();
            },
            changeCompanyID(id) {
                this.customReport.companyID = id;
                this.customReport.clientID = "***";
                this.customReport.isPublic = false;
                this.customReport.assignments = [];
                this.getUsers();
                if (this.customReport.type === 1) this.getClients();
            },
            changeFieldOrder(columns) {
                columns.forEach((column, index) => {
                    let field = this.customReport.fields.find(field => field.tableName === column.split(".")[0] && field.fieldName === column.split(".")[1]);
                    if (field) field.seqNbr = index + 1;
                });
            },
            changeReportField(field) {
                const index = this.customReport.fields.findIndex(fld => fld.tableName === field.tableName && fld.fieldName === field.fieldName);
                if (index > -1) {
                    this.customReport.fields.splice(index, 1, field);
                } else {
                    this.customReport.fields.push(field);
                }
                this.resetCalcFlds(field);
                this.resetPivotFlds(field);
                this.resetSectionFlds(field);
            },
            changeReportFilter(field) {
                const index = this.customReport.filters.findIndex(fld => fld.tableName === field.tableName && fld.fieldName === field.fieldName);
                if (index > -1) this.customReport.filters.splice(index, 1, field);
            },
            changeReportSort(field) {
                const index = this.customReport.sorts.findIndex(fld => fld.tableName === field.tableName && fld.fieldName === field.fieldName);
                if (index > -1) {
                    this.customReport.sorts.splice(index, 1, field);
                } else {
                    this.customReport.sorts.push(field);
                }
            },
            changeReportTable(reportTable, isSelected) {
                reportTable.isSelected = isSelected;
                if (!isSelected) {
                    this.customReport.fields = this.customReport.fields.filter(field => field.tableName !== reportTable.name);
                    this.customReport.filters = this.customReport.filters.filter(filter => filter.tableName !== reportTable.name);
                    this.customReport.sorts = this.customReport.sorts.filter(sort => sort.tableName !== reportTable.name);
                }
            },
            changeReportTableSearch: _.debounce(function (value) {
                this.reportTableSearchText = value || null;
            }, 500),
            changeReportType(type) {
                this.customReport.type = type;
                this.customReport.clientID = "***";
                this.customReport.isPublic = false;
                this.customReport.parentID = null;
                this.customReport.rollDown = false;
                this.customReport.assignments = [];
                this.customReport.fields = [];
                this.customReport.filters = [];
                this.customReport.sorts = [];
                this.reportTables = this.reportTables.map(reportTable => ({ ...reportTable, isSelected: false }));
                this.getUsers();
                if (type === 1) this.getClients();
            },
            changeUserEditAccess(user, canEdit) {
                user.canEdit = canEdit;
                let assignment = this.customReport.assignments.find(assignment => assignment.userID === user.userID);
                switch (true) {
                    case assignment && canEdit:
                        assignment.canEdit = true;
                        break;
                    case assignment && assignment.canView && !canEdit:
                        assignment.canEdit = false;
                        break;
                    case assignment && !assignment.canView && !canEdit:
                        this.customReport.assignments = this.customReport.assignments.filter(assignment => assignment.userID !== user.userID)
                        break;
                    case !assignment && canEdit:
                        this.customReport.assignments.push({ ...user, canView: false, canEdit: true });
                        break;
                    default:
                        break;
                }
            },
            changeUserViewAccess(user, canView) {
                user.canView = canView;
                let assignment = this.customReport.assignments.find(assignment => assignment.userID === user.userID);
                switch (true) {
                    case assignment && canView:
                        assignment.canView = true;
                        break;
                    case assignment && assignment.canEdit && !canView:
                        assignment.canView = false;
                        break;
                    case assignment && !assignment.canEdit && !canView:
                        this.customReport.assignments = this.customReport.assignments.filter(assignment => assignment.userID !== user.userID)
                        break;
                    case !assignment && canView:
                        this.customReport.assignments.push({ ...user, canView: true, canEdit: false });
                        break;
                    default:
                        break;
                }
            },
            deleteReportField(item) {
                this.customReport.fields = this.customReport.fields.filter(field => field.tableName !== item.tableName || field.fieldName !== item.fieldName);
                this.customReport.filters = this.customReport.filters.filter(filter => filter.tableName !== item.tableName || filter.fieldName !== item.fieldName);
                this.customReport.sorts = this.customReport.sorts.filter(sort => sort.tableName !== item.tableName || sort.fieldName !== item.fieldName);
            },
            deleteReportFilter(item) {
                this.customReport.filters = this.customReport.filters.filter(filter => JSON.stringify(filter) !== JSON.stringify(item));
            },
            deleteReportFilterGroup(filterGroup) {
                this.customReport.filterGroups = this.customReport.filterGroups.filter(fg => fg !== filterGroup)
            },
            deleteReportSort(item) {
                this.customReport.sorts = this.customReport.sorts.filter(sort => sort.tableName !== item.tableName || sort.fieldName !== item.fieldName);
            },
            fetchClients(companyID) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "GET",
                        contentType: 'application/json',
                        url: '@Url.Action("GetClients", "ReportWriter")' + '?co=' + companyID,
                        success: function (response) {
                            resolve(response.map(client => {
                                return { id: client.Id, text: client.Text }
                            }));
                        },
                        error: function (error) {
                            ThinkwareCommon.showErrorAlert(error.statusText);
                            reject(error);
                        }
                    });
                });
            },
            fetchCompanies() {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "GET",
                        contentType: 'application/json',
                        url: '@Url.Action("GetCompanies", "ReportWriter")',
                        success: function (response) {
                            resolve(response.map(company => {
                                return { id: company.Id, text: company.Text }
                            }));
                        },
                        error: function (error) {
                            ThinkwareCommon.showErrorAlert(error.statusText);
                            reject(error);
                        }
                    });
                });
            },
            fetchCustomReport(reportID) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "GET",
                        contentType: 'application/json',
                        url: '@Url.Action("GetCustomReport", "ReportWriter")' + '?id=' + reportID,
                        success: function (response) {
                            resolve({
                                id: response.ReportID,
                                companyID: response.CompanyID,
                                clientID: response.ClientID,
                                creator: response.Creator,
                                name: response.ReportName,
                                type: response.ReportType,
                                status: response.ReportStatus,
                                pageSize: response.PageSize,
                                isPublic: response.IsPublic,
                                parentID: response.ParentID,
                                currentUser: response.CurrentUser,
                                userSecurity: response.UserSecurity,
                                rollDown: response.RollDown,
                                assignments: response.CustomReportAssignments.map(x => {
                                    return {
                                        companyID: x.CompanyID,
                                        clientID: x.ClientID,
                                        userID: x.UserID,
                                        userType: x.UserType,
                                        canView: x.CanView,
                                        canEdit: x.CanEdit
                                    }
                                }),
                                fields: response.CustomReportDetails.map(x => {
                                    return {
                                        id: x.ID,
                                        tableName: x.DB_Tbl,
                                        tableDescription: x.Tbl,
                                        fieldName: x.DB_Field,
                                        fieldDescription: x.Field,
                                        label: x.Label,
                                        dbType: x.DB_Type,
                                        fieldType: x.FType,
                                        fieldSize: x.Size,
                                        isHidden: x.Hide,
                                        isSummarize: x.Summarize,
                                        isGroupBy: x.GroupBy,
                                        seqNbr: x.SeqNbr,
                                        function: x.UseFunction,
                                        format: x.Format,
                                        pivotFld: x.Pivot_Fld,
                                        calcFld: x.Calc_Fld,
                                        sectionFld: x.Section_Fld
                                    }
                                }),
                                filterGroups: [...new Set([0].concat(response.CustomReportFilters.map(x => x.FilterGroup)))].sort((a, b) => (a > b ? 1 : -1)),
                                filters: response.CustomReportFilters.map(x => {
                                    return {
                                        id: x.ID,
                                        tableName: x.DB_Tbl,
                                        fieldName: x.DB_Field,
                                        operator: x.Operator,
                                        operandValue: x.Filter_Value,
                                        operandSmartValue: x.Filter_SmartValue,
                                        operandFieldName: x.Filter_Field,
                                        operandTableName: x.Filter_Tbl,
                                        filterGroup: x.FilterGroup,
                                        fieldType: x.FType,
                                        paramId: x.ParamID
                                    }
                                }),
                                sorts: response.CustomReportSorts.map(x => {
                                    return {
                                        id: x.ID,
                                        tableName: x.DB_Tbl,
                                        fieldName: x.DB_Field,
                                        type: x.SortType,
                                        order: x.SortOrder
                                    }
                                })
                            });
                        },
                        error: function (error) {
                            ThinkwareCommon.showErrorAlert(error.statusText);
                            reject(error);
                        }
                    });
                });
            },
            fetchReportTables() {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "GET",
                        contentType: 'application/json',
                        url: '@Url.Action("GetReportTables", "ReportWriter")',
                        success: function (response) {
                            resolve(response.sort((a, b) => (a.Category || "").localeCompare(b.Category || "") || a.Description.localeCompare(b.Description)).map(table => {
                                return {
                                    name: table.DBObjectName,
                                    description: table.Description,
                                    accessLevel: table.AccessLevel,
                                    category: table.Category,
                                    fields: table.Fields.map(x => {
                                        return {
                                            tableName: x.DB_ObjectName,
                                            fieldName: x.DB_FLD,
                                            label: x.Description,
                                            dbType: x.DB_Type,
                                            fieldType: x.FType,
                                            fieldSize: x.FSize,
                                            accessLevel: x.ReportAccessLevel
                                        }
                                    })
                                }
                            }));
                        },
                        error: function (error) {
                            ThinkwareCommon.showErrorAlert(error.statusText);
                            reject(error);
                        }
                    });
                });
            },
            fetchUsers(companyID, clientID) {
                let self = this;
                var param = "?type=" + this.customReport.type;
                if (companyID) param += "&comp=" + this.customReport.companyID;
                if (clientID && clientID !== "***" || this.customReport.type === 0) param += "&client=" + this.customReport.clientID;
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "GET",
                        contentType: "application/json",
                        url: "@Url.Action("GetAvailableUsers", "ReportWriter")" + param,
                        success: function (response) {
                            resolve(response.map(user => {
                                const assignment = self.customReport.assignments.find(x => x.userID === user.UserID);
                                return {
                                    companyID: user.CompanyID,
                                    clientID: user.ClientID,
                                    userID: user.UserID,
                                    userName: user.UserName,
                                    userType: user.UserType,
                                    canView: assignment?.canView || false,
                                    canEdit: assignment?.canEdit || false
                                }
                            }));
                        },
                        error: function (error) {
                            ThinkwareCommon.showErrorAlert(error.statusText);
                            reject(error);
                        }
                    });
                });
            },
            getClients() {
                this.isLoading = true;
                this.fetchClients(this.customReport.companyID)
                    .then(data => {
                        this.clients = data;
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },
            getFilterOperand(filter) {
                if (filter.operandTableName && filter.operandFieldName) {
                    const field = this.customReport.fields.find(field => field.tableName === filter.operandTableName && field.fieldName === filter.operandFieldName) || null;
                    return field ? field.tableDescription + " - " + field.fieldDescription : filter.operandTableName + " - " + filter.operandFieldName;
                }
                return filter.operandSmartValue || filter.operandValue;
            },
            getFilterOperator(filter) {
                switch (filter.operator) {
                  case "=": return "Equal To";
                  case "!=": return "Not Equal To";
                  case "<": return "Less Than";
                  case "<=": return "Less Than or Equal To";
                  case ">": return "Greater Than";
                  case ">=": return "Greater Than or Equal To";
                  default: return filter.operator;
                }
            },
            getReportField(item) {
                return this.customReport.fields.find(field => field.tableName === item.tableName && field.fieldName === item.fieldName) || null;
            },
            getUsers() {
                const companyID = "@ViewBag.DNETLevel" === "System" && this.customReport.type === 1 ? this.customReport.companyID : null;
                const clientID = "@ViewBag.DNETLevel" === "System" && this.customReport.type === 1 ? this.customReport.clientID : null;
                this.isLoading = true;
                this.fetchUsers(companyID, clientID)
                    .then(data => {
                        this.users = data;
                    })
                    .finally(() => {
                        this.isLoading = false;
                    });
            },
            goBack() {
                window.location.href = '@Url.Action("List", "ReportWriter")';
            },
            isDateField(fieldType) {
                return [7, 134].includes(fieldType);
            },
            isTextField(fieldType) {
                return [202, 51].includes(fieldType);
            },
            moveDownSort(item) {
                let nextSort = this.customReport.sorts.find(sort => sort.order === item.order + 1);
                if (nextSort) nextSort.order--;
                item.order++;
            },
            moveUpSort(item) {
                let previousSort = this.customReport.sorts.find(sort => sort.order === item.order - 1);
                if (previousSort) previousSort.order++;
                item.order--;
            },
            resetCalcFlds(field) {
                if (field.calcFld) {
                    this.customReport.fields.filter(fld => fld.tableName !== field.tableName || fld.fieldName !== field.fieldName).map(fld => fld.calcFld = false);
                }
            },
            resetPivotFlds(field) {
                if (field.pivotFld) {
                    this.customReport.fields.filter(fld => fld.tableName !== field.tableName || fld.fieldName !== field.fieldName).map(fld => fld.pivotFld = false);
                }
            },
            resetSectionFlds(field) {
                if (field.sectionFld) {
                    this.customReport.fields.filter(fld => fld.tableName !== field.tableName || fld.fieldName !== field.fieldName).map(fld => fld.sectionFld = false);
                }
            },
            saveCustomReport() {
                this.isLoading = true;
                let self = this;
                $.ajax({
                    url: '@Url.Action("SaveCustomReport", "ReportWriter")',
                    contentType: 'application/json',
                    type: "POST",
                    data: JSON.stringify({
                        ReportID: this.customReport.id,
                        CompanyID: this.customReport.companyID,
                        ClientID: this.customReport.clientID,
                        ReportName: this.customReport.name,
                        ReportStatus: this.customReport.status,
                        ReportType: this.customReport.type,
                        PageSize: this.customReport.pageSize,
                        ReportQuery: null,
                        TotalQuery: null,
                        SectionQuery: null,
                        ReportDocumentID: null,
                        IsPublic: this.customReport.isPublic,
                        ParentID: this.customReport.parentID,
                        RollDown: this.customReport.rollDown,
                        CustomReportAssignments: this.customReport.assignments.map(x => {
                            return {
                                CompanyID: x.companyID,
                                ClientID: x.clientID,
                                UserID: x.userID,
                                UserType: x.userType,
                                CanView: x.canView,
                                CanEdit: x.canEdit
                            }
                        }),
                        CustomReportDetails: this.customReport.fields.map(x => {
                            return {
                                Field: x.fieldDescription,
                                Tbl: x.tableDescription,
                                DB_Field: x.fieldName,
                                DB_Tbl: x.tableName,
                                Label: x.label,
                                Size: x.fieldSize,
                                Hide: x.isHidden,
                                Summarize: x.isSummarize,
                                GroupBy: x.isGroupBy,
                                Ftype: x.fieldType,
                                DB_Type: x.dbType,
                                SeqNbr: x.seqNbr,
                                UseFunction: x.function,
                                Format: x.format,
                                Pivot_Fld: x.pivotFld,
                                Calc_Fld: x.calcFld,
                                Section_Fld: x.sectionFld
                            }
                        }),
                        CustomReportFilters: this.customReport.filters.map(x => {
                            return {
                                DB_Field: x.fieldName,
                                DB_Tbl: x.tableName,
                                Operator: x.operator,
                                Filter_Value: x.operandValue,
                                Filter_SmartValue: x.operandSmartValue,
                                Filter_Field: x.operandFieldName,
                                Filter_Tbl: x.operandTableName,
                                FilterGroup: x.filterGroup,
                                FType: x.fieldType,
                                ParamID: x.paramId
                            }
                        }),
                        CustomReportSorts: this.customReport.sorts.map(x => {
                            return {
                                DB_Field: x.fieldName,
                                DB_Tbl: x.tableName,
                                SortType: x.type,
                                SortOrder: x.order
                            }
                        })
                    }),
                    success: function (response) {
                        var id = response.ReportID;
                        self.customReport.id = Math.abs(id); //response.ReportID;
                        self.originalReport = JSON.parse(JSON.stringify(self.customReport));
                        self.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                        self.$toastr.defaultPosition = "toast-bottom-right";
                        if (id > 0) {
                            self.$toastr.s("Report was successfully saved.");
                        }
                        if (id == 0) {
                            self.$toastr.e("Cannot create report record.");
                        }
                        if (id < 0) {
                            self.$toastr.e("Report was saved with the status 'Edit': cannot create report query.");
                        }
                    },
                    error: function (error) {
                        ThinkwareCommon.showErrorAlert(error.statusText);
                    },
                    complete: function () {
                        self.isLoading = false;
                    }
                });
            },
            showReportFieldDialog(table, field) {
                this.reportTable = table;
                this.reportField = field || null;
                this.reportFieldModalKey++; // force refresh of modal component with current data
                this.$nextTick(() => {
                    $('#report-field-modal').modal("show");
                });
            },
            showReportFilterDialog(filterGroup, field) {
                this.filterGroup = filterGroup;
                this.filterField = field;
                this.reportFilterModalKey++; // force refresh of modal component with current data
                this.$nextTick(() => {
                    $('#report-filter-modal').modal("show");
                });
            },
            showReportQuickAddFieldsDialog(table) {
                this.reportTable = table;
                this.reportQuickAddFieldsModalKey++; // force refresh of modal component with current data
                this.$nextTick(() => {
                    $('#report-quick-add-fields-modal').modal("show");
                });
            },
            showReportSortDialog(field) {
                this.sortField = field;
                this.reportSortModalKey++; // force refresh of modal component with current data
                this.$nextTick(() => {
                    $('#report-sort-modal').modal("show");
                });
            }
        }
    });
</script>

<style lang="scss" scoped>
    .custom-report {
        .header-group {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 10px;

            .header-button {
                padding: 3px;
                background-color: rgba(0,0,0,0.2);

                &:hover {
                    background-color: rgba(0,0,0,0.3);
                    cursor: pointer;
                }
            }
        }

        .tab-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
            .settings {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
                .toggle-label {
                    font-size: 15px;
                    font-weight: 400;
                    white-space: nowrap;
                }
            }
        }

        .btn.active {
            outline: none !important;
        }

        .accordion {
            .accordion-actions {
                button {
                    background-color: transparent;
                    border-style: none;
                    outline: none;

                    i {
                        font-size: 20px;

                        &.fa-times {
                            color: #d34f4f;
                        }
                    }
                }
            }

            .accordion-header {
                margin-bottom: 4px;
            }

            .accordion-body {
                padding-top: 0;
            }
        }

        .grid-table {
            .table-actions {
                .button-group {
                    display: flex;
                    gap: 14px;
                }
            }

            .header-row {
                .header-cell {
                    .header-text {
                        white-space: nowrap;
                    }
                }
            }

            .column-actions {
                min-width: 88px;

                button {
                    background-color: transparent;
                    border-style: none;
                    outline: none;

                    i {
                        font-size: 20px;

                        &.fa-pencil {
                            color: #2574a9;
                        }

                        &.fa-arrow-up, &.fa-arrow-down {
                            color: #A6A6A6;
                        }

                        &.fa-times {
                            color: #d34f4f;
                        }
                    }

                    &:disabled {
                        i {
                            opacity: .5;
                        }
                    }
                }
            }

            .column-boolean .toggle {
                justify-content: center;
            }
        }

        .check-box-list {
            display: grid;
            grid-template-columns: 33% 33% 33%;
            margin: 0 20px;
            .check-box {
                padding: 2px 0;
            }
        }

        .radio-button-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 60px;
            padding: 0 5px;

            .radio-button {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 6px;

                input[type=radio] {
                    margin: 0;
                }

                label {
                    margin-bottom: 0;
                }
            }
        }

        .filter-groups {
            .accordion:first-child {
                .accordion-header {
                    display: none;
                }
            }

            .accordion:not(:first-child) {
                margin-top: 20px;
            }

            .fa-chevron-down {
                display: none;
            }

            .accordion-body {
                padding-left: 0;
                padding-right: 0;
            }
        }

        #user-access-tab {
            .grid-table {
                .table-container {
                    max-height: 507px;
                    overflow-y: auto;

                    .header-row {
                        position: sticky;
                        top: 0;
                        z-index: 2;
                    }
                }
            }
        }
    }
</style>
