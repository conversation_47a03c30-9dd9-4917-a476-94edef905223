@using DarwiNet2._0.DNetSynch
@{
    ViewBag.Title = "Login";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

@model DarwiNet2._0.ViewModels.Onboard.OnboardVerifyVM

@if (GlobalVariables.WelcomeBackground != null)
{
    <style>

        .login-box {
            box-shadow: 10px 10px 5px rgba(0, 0, 0, 0.5);
        }
    </style>
}

<style>
    label.error {
        position: absolute;
        margin-top: 34px;
        left: 0;
    }
</style>

<div class="home-background  welcome-background" style="background: URL('@(GlobalVariables.WelcomeBackground != null ? GlobalVariables.WelcomeBackground : "~")') no-repeat center center fixed; -webkit-background-size: cover; -moz-background-size: cover; -o-background-size: cover; background-size: cover; background-color: #efefef">
    <div class="container">
        <div style="margin-top: 50px;" class="mainbox col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2 login-box">
            <div class="panel">
                @*<div class="panel-heading">
                        <div class="panel-title">
                            <h3>On-Boarding Verification</h3></div>
                    </div>*@
                <div style="padding-top: 30px" class="panel-body">
                    <div class="col-md-12 text-center" style="padding-bottom: 25px;">
                        <a href="@Url.Action("Index", "Home")"><img src="@ViewBag.LogoURL" class="img-responsive" /></a>
                    </div>
                    <div class="col-md-12 text-center" style="padding-bottom: 25px;">
                        <p>Verify your information in the form below.</p>
                    </div>
                    <div class="col-md-12 message-pad-top">
                        @Html.Partial("~/Views/Shared/_MessagePartial.cshtml")
                    </div>
                    <div style="display: none" id="login-alert" class="alert alert-danger col-sm-12"></div>
                    <form id="loginform" class="form-horizontal" role="form" method="post" action="@Url.Action("Verify", "Onboard")">
                        @*if (ViewBag.FNameUsed)
        {
            <div>
                <label>First Name:</label>
            </div>
            <div style="margin-bottom: 25px" class="input-group">
                <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
                @if (ViewBag.FNameRequired)
                {
                    <input id="login-FName" type="text" class="form-control field-required" name="FName" value="" placeholder="First Name" autofocus="">
                }
                else
                {
                    <input id="login-FName" type="text" class="form-control" name="FName" value="" placeholder="First Name" autofocus="">
                }
            </div>
        }
        @if (ViewBag.LNameUsed)
        {
            <div>
                <label>Last Name:</label>
            </div>
            <div style="margin-bottom: 25px" class="input-group">
                <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
                @if (ViewBag.LNameRequired)
                {
                    <input id="login-LName" type="text" class="form-control field-required" name="LName" value="" placeholder="Last Name" autofocus="">
                }
                else
                {
                    <input id="login-LName" type="text" class="form-control" name="LName" value="" placeholder="Last Name" autofocus="">
                }

            </div>
        }*@
                        @if (Model.EmailUsed)
                        {
                            <div>
                                <label>Email:</label>
                            </div>
                            <div style="margin-bottom: 25px" class="input-group">
                                <span class="input-group-addon"><i class="glyphicon glyphicon-envelope"></i></span>
                                @if (Model.EmailRequired)
                                {
                                    <input id="login-email" type="text" class="form-control field-required" name="email" value="" placeholder="Email Address" autofocus="">
                                }
                                else
                                {
                                    <input id="login-email" type="text" class="form-control" name="email" value="" placeholder="Email Address" autofocus="">
                                }

                            </div>
                        }
                        @if (Model.BirthDateUsed)
                        {

                            <div>
                                <label>Birth Date:</label>
                            </div>
                            <div style="margin-bottom: 25px" class="input-group">
                                <span class="input-group-addon"><i class="glyphicon glyphicon-calendar"></i></span>
                                @if (Model.BirthDateRequired)
                                {
                                    <input id="login_birthdate" type="text" class="field-required" name="birthdate" value="" autofocus="" style="width: 100%">
                                }
                                else
                                {
                                    <input id="login_birthdate" type="text" class="f" name="birthdate" value="" autofocus="" style="width: 100%">
                                }

                            </div>
                        }
                        @if (Model.SocSecNumberUsed)
                        {
                            <div>
                                <label>SSN:</label>
                            </div>
                            <div>
                                <input type="hidden" id="hidden-login-ssn" disabled="disabled" />
                            </div>
                            <div id="ssn-container" style="margin-bottom: 25px" class="input-group">
                                <span class="input-group-addon"><i class="glyphicon glyphicon-lock"></i></span>

                                @if (Model.SocSecNumberRequired)
                                {
                                    <input id="login-ssn" type="text" class="form-control field-required" name="ssn" value="" autofocus="" required>
                                }
                                else
                                {
                                    <input id="login-ssn" type="text" class="form-control" name="ssn" value="" autofocus="">
                                }
                            </div>
                        }
                        <!-- Change this to a button or input when using this as a form -->
                        <div style="margin-top: 10px" class="form-group">
                            <!-- Button -->
                            <div class="col-sm-12 controls">
                                <div class="pull-right">
                                    <a href="@Url.Action("Index", "Home")">Already signed on as a new user?</a>&nbsp;
                                    <button type="submit" class="btn btn-thinkware btn-lg">Verify </button>
                                </div>

                            </div>
                        </div>
                    </form>

                </div>

            </div>
        </div>
    </div>
</div>

<script src="~/Scripts/jquery.datetimepicker.js"></script>
<link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
<script src="~/Scripts/MaskedInput.js"></script>
<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
<script>
    $(document).ready(function () {
        $('#login_birthdate').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
    })
</script>
<script>
    $("#loginform").validate({
        rules: {
            login_birthdate: {
                date: true,
                required: true
            }
        },
        errorPlacement: function (error, element) {
            if (element.attr("name") == "birthdate")
                error.insertAfter(".k-datepicker");
            else
                error.insertAfter(element);
        }
    });
</script>
<script>
    $(function () {
        var today = new Date();
        $(".datepicker").datetimepicker({
            timepicker: false,
            format: 'm/d/Y',
            formatDate: 'm/d/Y',
            scrollInput: false
        });
        $(".mindatepicker").datetimepicker({
            timepicker: false,
            format: 'm/d/Y',
            formatDate: 'm/d/Y',
            minDate: today,
            scrollInput: false
        });
        $(".datetimepicker").datetimepicker({
            formatTime: 'g:i A',
            format: 'm/d/Y h:i A',
            scrollInput: false
        });
        //$('input').tooltip({
        //    placement: "right",
        //    trigger: "focus"
        //});
    });
    $(document).ready(function () {
        $("#login-ssn").mask("***********", { placeholder: " " });
    });
    $('#login-ssn').on('blur', function () {
        $('#login-ssn').attr({ 'type': 'password' });
    });
    $('#login-ssn').on('focus', function () {
        $('#login-ssn').attr({ 'type': 'text' });
    });
    $("button[type=submit]").click(function () {
        $("#loginform").submit(function () {
            $('#login-ssn').mask('999999999');
        });
    });
</script>
<script>
    $(document).ready(function () {
        $('input.field-required').closest('.k-picker-wrap').css("border-color", "#e50000");
    })
</script>




