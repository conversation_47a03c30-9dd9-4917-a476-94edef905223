@using DarwiNet2._0.DNetSynch
@{
    ViewBag.Title = "Login";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}
@if (GlobalVariables.WelcomeBackground != null)
{
    <style>

        .login-box {
            box-shadow: 10px 10px 5px rgba(0, 0, 0, 0.5);
        }
    </style>
}
<div class="home-background  welcome-background" style="background: URL('@(GlobalVariables.WelcomeBackground != null ? GlobalVariables.WelcomeBackground : "~")') no-repeat center center fixed; -webkit-background-size: cover; -moz-background-size: cover; -o-background-size: cover; background-size: cover; background-color: #efefef">
    <div class="container">
        <div style="margin-top: 50px;" class="mainbox col-md-9 col-centered login-box">
            <div class="panel ">
                @*<div class="panel-heading">
                        <div class="panel-title">Login</div>
                    </div>*@
                <div style="padding-top: 30px" class="panel-body">
                    <div class="col-md-12 text-center" style="padding-bottom: 25px;">
                        <a href="@Url.Action("Index", "Home")"><img src="@ViewBag.LogoURL" class="img-responsive" /></a>
                    </div>
                    <div class="col-md-12 message-pad-top">
                        @Html.Partial("~/Views/Shared/_MessagePartial.cshtml")
                    </div>
                    <div class="col-md-6">
                        <form id="loginform" class="form-horizontal" role="form" method="post" action="@Url.Action("Index", "Onboard")">
                            <div>
                                <label>Login Code:</label>
                            </div>
                            <div style="margin-bottom: 25px" class="input-group">
                                <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
                                <input id="login-username" type="text" class="form-control" name="lc" value="" placeholder="Login Code" autofocus="">
                            </div>
                            <div style="margin-top: 10px" class="form-group">
                                <!-- Button -->
                                <div class="col-md-12 controls">
                                    <div class="pull-right">
                                        <a href="@Url.Action("Index", "Home")" class="btn btn-lg btn-danger">Cancel </a>
                                        <button type="submit" class="btn btn-lg btn-success">Login </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <div>
                            <p>
                                <strong>@ViewBag.WelcomeTitle</strong>
                            </p>
                            @Html.Raw(ViewBag.Message)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    






