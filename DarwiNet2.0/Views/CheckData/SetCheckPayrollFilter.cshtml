@{
    Layout = null;
    var ptype = ViewBag.Filter.ProcessDateType;
    var ctype = ViewBag.Filter.CheckDateType;
}
@using (Html.BeginForm("SetCheckPayrollFilter", "CheckData", FormMethod.Post, new { @id = "setCPFlt" }))
{
    @Html.AntiForgeryToken()
    <input type="hidden" id="showprinted" name="showprinted" value="@ViewBag.ShowPrinted" />
    <div class="form-horizontal">
        <div class="form-group">
            <label class="control-label col-md-4">Company</label>
            <div>
                @(Html.Kendo().DropDownList()
                          .Name("ecompanies")
                          .HtmlAttributes(new { style = "width:300px" })
                          .OptionLabel("Select Company...")
                          .DataTextField("Description")
                          .DataValueField("Code")
                          .DataSource(source =>
                          {
                              source.Read(read =>
                              {
                                  read.Action("GetCascadeCompanies", "CheckData");
                                  //                              .Data("efilterComps");
                              });
                          })
                          .Events(e => e.Select("onCompanyChange"))
                )
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-4">Client</label>
            <div>
                @(Html.Kendo().DropDownList()
                        .Name("eclients")
                        .HtmlAttributes(new { style = "width:300px" })
                        .OptionLabel("Select Client...")
                        .DataTextField("Description")
                        .DataValueField("Code")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetCascadeClients", "CheckData")
                                    .Data("efilterClients");
                            })
                                .ServerFiltering(true);
                        })
                        .Enable(false)
                        .AutoBind(false)
                        .CascadeFrom("ecompanies")
                        .Events(e =>
                        {
                            e.Select("onFilterChange");
                            e.Change("onClientChange");
                        })
                    )
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-4">Profile</label>
            <div>
                @(Html.Kendo().DropDownList()
                                .Name("eprofiles")
                                .HtmlAttributes(new { style = "width:300px" })
                                .OptionLabel("Select Profile...")
                                .DataTextField("Description")
                                .DataValueField("Code")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetCascadeClientProfiles", "CheckData")
                                            .Data("efilterProfiles");
                                    })
                                        .ServerFiltering(true);
                                })
                                .Enable(false)
                                .AutoBind(false)
                                .CascadeFrom("ecompanies")
                                //.Events(e =>  e.Select("onFilterChange"))
                    )
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-4">Responsible User</label>
            <div>
                @(Html.Kendo().DropDownList()
                                .Name("eusers")
                                .HtmlAttributes(new { style = "width:300px" })
                                .OptionLabel("Select User...")
                                .DataTextField("Description")
                                .DataValueField("Code")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetCascadeUsers", "CheckData")
                                            .Data("efilterUsers");
                                    })
                                        .ServerFiltering(true);
                                })
                                .Enable(false)
                                .AutoBind(false)
                                .CascadeFrom("ecompanies")
                                //.Events(e =>  e.Select("onFilterChange"))
                    )
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-4">Process Date</label>
            <div>
                <input type="radio" name="ProcessDateType" onclick="ShowHideProcessDates()" value="0" @((ptype == 0) ? "checked" : string.Empty)>&nbsp;Any&nbsp;
                <input type="radio" name="ProcessDateType" onclick="ShowHideProcessDates()" value="1" @((ptype == 1) ? "checked" : string.Empty)>&nbsp;Today&nbsp;
                <input type="radio" name="ProcessDateType" onclick="ShowHideProcessDates()" value="2" @((ptype == 2) ? "checked" : string.Empty)>&nbsp;Tomorrow&nbsp;
                <input type="radio" name="ProcessDateType" onclick="ShowHideProcessDates()" value="3" @((ptype == 3) ? "checked" : string.Empty)>&nbsp;Select&nbsp;
            </div>
            <div id="pDate" style="display:none" class="col-md-4">
                <input type="text" name="processDate1" id="processDate1" value="" placeholder="From" class="form-control" />
                <input type="text" name="processDate2" id="processDate2" value="" placeholder="To" class="form-control" />
            </div>
        </div>
        <div class="form-group">
            <label class="control-label col-md-4">Check Date</label>
            <div>
                <input type="radio" name="CheckDateType" onclick="ShowHideCheckDates()" value="0" @((ctype == 0) ? "checked" : string.Empty)>&nbsp;Any&nbsp;
                <input type="radio" name="CheckDateType" onclick="ShowHideCheckDates()" value="1" @((ctype == 1) ? "checked" : string.Empty)>&nbsp;Today&nbsp;
                <input type="radio" name="CheckDateType" onclick="ShowHideCheckDates()" value="2" @((ctype == 2) ? "checked" : string.Empty)>&nbsp;Tomorrow&nbsp;
                <input type="radio" name="CheckDateType" onclick="ShowHideCheckDates()" value="3" @((ctype == 3) ? "checked" : string.Empty)>&nbsp;Select&nbsp;
            </div>
            <div id="cDate" style="display:none" class="col-md-4">
                <input type="text" name="checkDate1" id="checkDate1" value="" placeholder="From" class="form-control" />
                <input type="text" name="checkDate2" id="checkDate2" value="" placeholder="To" class="form-control" />
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                    <a href="@Url.Action("Index", "CheckData", new { p = @ViewBag.ShowPrinted })" class="k-button" id="clearFlt">Clear Filter</a>
                    <input type="submit" value="Set Filter" class="btn btn-thinkware" />
                </div>
            </div>
        </div>
    </div>
}

        <script src="~/Scripts/MaskedInput.js"></script>
        <script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
        <script>
    $(document).ready(
        function () {
            $('#processDate1, #checkDate1, #processDate2, #checkDate2').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
            var co = '@ViewBag.Filter.CompanyIDs'; //$('#fltComp').val();
            var cl = '@ViewBag.Filter.ClientIDs'; //$('#fltComp').val();
            var pr = '@ViewBag.Filter.ProfileIDs'; //$('#fltComp').val();
            var us = '@ViewBag.Filter.UserIDs'; //$('#fltComp').val();
            if ( co != '') $('#ecompanies').data("kendoDropDownList").value(co);
            if ($(cl != '') $('#eclients').data("kendoDropDownList").value(cl);
            if ($(pr != '') $('#eprofiles').data("kendoDropDownList").value(pr);
            if ($(us != '') $('#eusers').data("kendoDropDownList").value(us);
            $('#processDate1').val('@ViewBag.Filter.ProcessDateFrom');
            $('#processDate2').val('@ViewBag.Filter.ProcessDateTo');
            $('#checkDate1').val('@ViewBag.Filter.CheckDateFrom');
            $('#checkDate2').val('@ViewBag.Filter.CheckDateTo');
                ShowHideProcessDates();
                ShowHideCheckDates();
            });

    function ShowHideProcessDates() {
        var radio = $('input:radio[name=ProcessDateType]:checked').val();
        if (radio === "3")
            $('#pDate').show();
        else
            $('#pDate').hide();
    }

    function ShowHideCheckDates() {
        var radio = $('input:radio[name=CheckDateType]:checked').val();
        if (radio === "3")
            $('#cDate').show();
        else
            $('#cDate').hide();
    }
        </script>

