@model DarwiNet2._0.Data.TimeSheetProfile
@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core;

@{
    ViewBag.Title = "Edit Time Sheet Profile" + ViewBag.ProfileName;
    ViewBag.ParentCrumb = "Setup";
    ViewBag.CrumbName = "Time Sheets";
    ViewBag.Crumb = Url.Action("Index", "TSProfiles");
}

<p style="font-size: 22px;">Edit Time Sheet Profile</p>
<div class="colored-line-left"></div>
@using (Html.BeginForm("Edit", "TSProfiles", FormMethod.Post, new { @id = "tsform" }))
{
    @Html.AntiForgeryToken()
    <input type="hidden" name="selectedUserList" id="selectedUserList" value="" />
    <div class="form-horizontal">
    @Html.HiddenFor(model => model.CompanyID)

    <div class="form-group">
        @Html.LabelFor(model => model.ProfileID, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("ProfileID", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.TextBoxFor(model => model.ProfileID, new {@class = "form-control field-required ", @maxlength = "12", @required = "required", @readonly = "readonly"})
            @Html.ValidationMessageFor(model => model.ProfileID, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.ProfileName, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("ProfileName", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.TextBoxFor(model => model.ProfileName, new {@class = "form-control", @maxlength = "50"})
            @Html.ValidationMessageFor(model => model.ProfileName, "", new {@class = "text-danger"})
        </div>
    </div>
    
    
    <p style="font-size: 22px;">Settings</p>
    <div class="colored-line-left"></div>
    <div class="form-group">
        @Html.LabelFor(model => model.ProfileType, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("ProfileType", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("ProfileType", EnumHelper.GetSelectList(typeof (enTimeSheetTypes)), new {id = "ProfileType", name = "ProfileType", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.ProfileType, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.SelectionType, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("SelectionType", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("SelectionType", EnumHelper.GetSelectList(typeof (enTimeSheetSelectionTypes)), new {id = "SelectionType", name = "SelectionType", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.SelectionType, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.PayPeriod, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("PayPeriod", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("PayPeriod", EnumHelper.GetSelectList(typeof (enPayPeriods)), new {id = "PayPeriod", name = "PayPeriod", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.PayPeriod, "", new {@class = "text-danger"})
        </div>
    </div>
    
    <p style="font-size: 22px;">Controls</p>
    <div class="colored-line-left"></div>
    <div class="form-group">
        @Html.LabelFor(model => model.MaxRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("MaxRate", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.MaxRate, new {@class = "form-control maxamount", id = "maxrate"})
            @Html.ValidationMessageFor(model => model.MaxRate, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.DefDays, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DefDays", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.DefDays, new {@class = "form-control"})
            @Html.ValidationMessageFor(model => model.DefDays, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.MaxDayHours, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("MaxDayHours", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.MaxDayHours, new {@class = "form-control"})
            @Html.ValidationMessageFor(model => model.MaxDayHours, "", new {@class = "text-danger"})
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.DefHours, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DefHours", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.DefHours, new {@class = "form-control"})
            @Html.ValidationMessageFor(model => model.DefHours, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.MaxEEGrossAmount, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("MaxEEGrossAmount", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.MaxEEGrossAmount, new {@class = "form-control maxamount"})
            @Html.ValidationMessageFor(model => model.MaxEEGrossAmount, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.DefWeeks, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DefWeeks", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.DefWeeks, new {@class = "form-control"})
            @Html.ValidationMessageFor(model => model.DefWeeks, "", new {@class = "text-danger"})
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.AllowModifiedRate, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("AllowModifiedRate", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.AllowModifiedRate, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AllowModifiedRate, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.AllowModifiedRatePermanent, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("AllowModifiedRatePermanent", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.AllowModifiedRatePermanent, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AllowModifiedRatePermanent, "", new { @class = "text-danger" })
            <p class="help-block">Allow Modified Rate must be checked.</p>
        </div>
        @Html.LabelFor(model => model.EditOnlyCodes, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Client Can Only Edit Codes", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.EditOnlyCodes, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.EditOnlyCodes, "", new { @class = "text-danger" })
        </div>
    </div>
    
    <div class="form-group">
        @Html.LabelFor(model => model.WCStatus, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("WCStatus", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("WCStatus", EnumHelper.GetSelectList(typeof (enTimeSheetColumnStatus)), new {id = "WCStatus", name = "WCStatus", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.WCStatus, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.SutaStateStatus, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("SutaStateStatus", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("SutaStateStatus", EnumHelper.GetSelectList(typeof (enTimeSheetColumnStatus)), new {id = "SutaStateStatus", name = "SutaStateStatus", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.SutaStateStatus, "", new {@class = "text-danger"})
        </div>

        @Html.LabelFor(model => model.StateTaxStatus, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("StateTaxStatus", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("StateTaxStatus", EnumHelper.GetSelectList(typeof (enTimeSheetColumnStatus)), new {id = "StateTaxStatus", name = "StateTaxStatus", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.StateTaxStatus, "", new {@class = "text-danger"})
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.ShiftStatus, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("ShiftStatus", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("ShiftStatus", EnumHelper.GetSelectList(typeof (enTimeSheetColumnStatus)), new {id = "ShiftStatus", name = "ShiftStatus", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.ShiftStatus, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.PositionStatus, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("PositionStatus", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("PositionStatus", EnumHelper.GetSelectList(typeof (enTimeSheetColumnStatus)), new {id = "PositionStatus", name = "PositionStatus", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.PositionStatus, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.LocalTaxStatus, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("LocalTaxStatus", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("LocalTaxStatus", EnumHelper.GetSelectList(typeof (enTimeSheetColumnStatus)), new {id = "LocalTaxStatus", name = "LocalTaxStatus", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.LocalTaxStatus, "", new {@class = "text-danger"})
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.DepartmentStatus, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DepartmentStatus", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("DepartmentStatus", EnumHelper.GetSelectList(typeof (enTimeSheetColumnStatus)), new {id = "DepartmentStatus", name = "DepartmentStatus", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.DepartmentStatus, "", new {@class = "text-danger"})
        </div>

        @Html.LabelFor(model => model.PayCodesSource, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("PayCodesSource", GlobalVariables.LanguageID))
        <div class="col-md-2">

            @Html.DropDownList("PayCodesSource", EnumHelper.GetSelectList(typeof (enTimeSheetCodeSource)), new {id = "PayCodesSource", name = "PayCodesSource", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.PayCodesSource, "", new {@class = "text-danger"})
        </div>

        @Html.LabelFor(model => model.BenefitsSource, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("BenefitsSource", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("BenefitsSource", EnumHelper.GetSelectList(typeof (enTimeSheetCodeSource)), new {id = "BenefitsSource", name = "BenefitsSource", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.BenefitsSource, "", new {@class = "text-danger"})
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.DeductionsSource, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DeductionsSource", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.DropDownList("DeductionsSource", EnumHelper.GetSelectList(typeof (enTimeSheetCodeSource)), new {id = "DeductionsSource", name = "DeductionsSource", @class = "form-control"})
            @Html.ValidationMessageFor(model => model.DeductionsSource, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.PEOApprovalRequired, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("PEOApprovalRequired", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.PEOApprovalRequired, new { htmlAttributes = new { @class = "form-control" } }) <button type="button" style="display: none;" id="selectPEO" class="btn btn-thinkware" type="button" data-toggle="modal" data-target="#peoSelect">Select PEO</button>
            @Html.ValidationMessageFor(model => model.PEOApprovalRequired, "", new { @class = "text-danger" })
        </div>
        @Html.LabelFor(model => model.IncludeAllEE, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("IncludeAllEE", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.IncludeAllEE, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.IncludeAllEE, "", new { @class = "text-danger" })
        </div>
    </div>
        <div class="form-group">
            @Html.LabelFor(model => model.HideOfferedHours, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Hide Offered Hours Column", GlobalVariables.LanguageID))
            <div class="col-md-2 check-down">
                @Html.CheckBoxFor(model => model.HideOfferedHours, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.HideOfferedHours, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.HideCheckNumber, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Hide Check Number Column", GlobalVariables.LanguageID))
            <div class="col-md-2 check-down">
                @Html.CheckBoxFor(model => model.HideCheckNumber, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.HideCheckNumber, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.AutoSave, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Auto Save Timesheet", GlobalVariables.LanguageID))
            <div class="col-md-2 check-down">
                @Html.CheckBoxFor(model => model.AutoSave, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.AutoSave, "", new { @class = "text-danger" })
            </div>

        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.HideWeeks, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Hide Weeks Column", GlobalVariables.LanguageID))
            <div class="col-md-2 check-down">
                @Html.CheckBoxFor(model => model.HideWeeks, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.HideWeeks, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.HideDays, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Hide Days Column", GlobalVariables.LanguageID))
            <div class="col-md-2 check-down">
                @Html.CheckBoxFor(model => model.HideDays, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.HideDays, "", new { @class = "text-danger" })
            </div>

        </div>
    
    <p style="font-size: 22px;">Employee Time Entry</p>
    <div class="colored-line-left"></div>
    <div class="form-group">
        @Html.LabelFor(model => model.AllowTimeEntry, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("AllowTimeEntry", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.AllowTimeEntry, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AllowTimeEntry, "", new { @class = "text-danger" })
        </div>
        @Html.LabelFor(model => model.AllowEEPositionChg, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("AllowEEPositionChg", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.AllowEEPositionChg, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AllowEEPositionChg, "", new { @class = "text-danger" })
        </div>
        @Html.LabelFor(model => model.AllowEEDeptChg, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("AllowEEDeptChg", GlobalVariables.LanguageID))
        <div class="col-md-2 check-down">
            @Html.CheckBoxFor(model => model.AllowEEDeptChg, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AllowEEDeptChg, "", new { @class = "text-danger" })
        </div>

    </div>
    <p style="font-size: 22px;">Decimal Precision</p>
    <div class="colored-line-left"></div>
    <div class="form-group">
        @Html.LabelFor(model => model.PaycodeDecimals, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("PaycodeDecimals", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.PaycodeDecimals, new {@class = "form-control"})
            @Html.ValidationMessageFor(model => model.PaycodeDecimals, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.BenefitDecimals, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("BenefitDecimals", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.BenefitDecimals, new {@class = "form-control"})
            @Html.ValidationMessageFor(model => model.BenefitDecimals, "", new {@class = "text-danger"})
        </div>
        @Html.LabelFor(model => model.DeductionDecimals, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("DeductionDecimals", GlobalVariables.LanguageID))
        <div class="col-md-2">
            @Html.TextBoxFor(model => model.DeductionDecimals, new {@class = "form-control"})
            @Html.ValidationMessageFor(model => model.DeductionDecimals, "", new {@class = "text-danger"})
        </div>
    </div>
    
    <p style="font-size: 22px;">Display Options</p>
    <div class="colored-line-left"></div>
    <div class="form-group">
        <div class="col-md-7 col-md-offset-1">
            <table class="table">
                <tr>
                    <td></td>
                    <td>TimeSheet</td>
                    <td>Reports</td>
                </tr>
                <tr><td><strong>Display SSN</strong></td><td>@Html.CheckBoxFor(model => model.PrintSocSecOnBlankTS, new { htmlAttributes = new { @class = "form-control" } })</td><td>@Html.CheckBoxFor(model => model.PrintSocSecOnReports, new { htmlAttributes = new { @class = "form-control" } })</td></tr>
                <tr><td><strong>Display PayRate</strong></td><td>@Html.CheckBoxFor(model => model.PrintRateOnBlankTS, new { htmlAttributes = new { @class = "form-control" } })</td><td>@Html.CheckBoxFor(model => model.PrintRateOnReports, new { htmlAttributes = new { @class = "form-control" } })</td></tr>
                <tr><td><strong>Display YTD</strong></td><td>@Html.CheckBoxFor(model => model.PrintYTDOnBlankTS, new { htmlAttributes = new { @class = "form-control" } })</td><td>@Html.CheckBoxFor(model => model.PrintYTDOnReports, new { htmlAttributes = new { @class = "form-control" } })</td></tr>
            </table>
        </div>

    </div>

    <div class="form-group">
        <div class="col-md-12">
            <div class="pull-right">
                <a href="@Url.Action("Index", "TSProfiles")" class="btn btn-thinkware">Cancel</a>
                <input type="submit" value="Save" class="btn btn-thinkware"/>
            </div>
        </div>
    </div>
    </div>
    <div class="modal fade" id="peoSelect" tabindex="-1" role="dialog" aria-labelledby="peoSelectLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="peoSelectLabel">Add</h4>
            </div>
            <div class="modal-body">
                <input type="hidden" name="tid" value="@ViewBag.TaskID" />
                <div class="">
                    @*@Html.AntiForgeryToken()*@
                    <div class="">
                        <input type="hidden" id="allClients" />
                        <label for="required">Select to Add</label>
                        <div class="row client-setup-codes-select" id="eeSelect">
                            <div class="col-md-12">
                                <div style="clear: both;"></div>
                                <div class="col-md-5">
                                    <label>Available PEOs</label><br />
                                    <select multiple id="select1" class="col-md-10 col-xs-12" style="height: 400px; overflow: auto;">
                                        @{
                                        if (ViewBag.AvailablePEOUsers != null)
                                        {
                                            foreach (Code_Description profile in ViewBag.AvailablePEOUsers)
                                        {
                                        <option value="@profile.Code">@profile.Description</option>
                                        }
                                        }
                                        }
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <div class="chevron-select">
                                        <div class="chevron-add-bottom">
                                            <div style="padding-bottom: 15px;">
                                                <a href="#" title="Add" id="add"><i class="icon-thinkware fa fa-arrow-right fa-3x"></i></a>
                                            </div>
                                            <div>
                                                <a href="#" title="Add All" id="addAll"><i class="add-codes-arrow icon-thinkware fa fa-arrow-right fa-3x"></i></a>
                                            </div>
                                        </div>
                                        <div>
                                            <div style="padding-bottom: 15px;">
                                                <a href="#" title="Remove" id="remove"><i class="icon-thinkware fa fa-arrow-left fa-3x"></i></a>
                                            </div>
                                            <div>
                                                <a href="#" title="Remove All" id="removeAll"><i class="add-codes-arrow icon-thinkware fa fa-arrow-left fa-3x"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <label>Selected PEOs</label><br />
                                    <select multiple id="SelectedUsers" name="SelectedUsers" class="col-md-10 col-xs-12" style="height: 400px;overflow: auto;">
                                        @{
    if (ViewBag.SelectedPEOUsers != null)
    {
        foreach (Code_Description profile in ViewBag.SelectedPEOUsers)
        {
                                <option value="@profile.Code">@profile.Description</option>
        }
    }
                                        }
                                    </select>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-thinkware-close" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
}
@section scripts{
    <script>
        var form = $("#tsform");
        form.validate({
            ignore: []
        });
        $('#AllowModifiedRate').change(function() {
            var checked = $(this).is(':checked');
            if (checked) {
                $('#AllowModifiedRatePermanent').attr('disabled', false);
            } else {
                $('#AllowModifiedRatePermanent').attr('disabled', true);
                $('#AllowModifiedRatePermanent').prop('checked', false);
            }
        });

        $(document).ready(function () {
            var checked = $('#AllowModifiedRate').is(':checked');
            if (checked) {
                $('#AllowModifiedRatePermanent').attr('disabled', false);
            } else {
                $('#AllowModifiedRatePermanent').attr('disabled', true);
                $('#AllowModifiedRatePermanent').prop('checked', false);
            }
        });

    </script>
    <script type="text/javascript">

        // mini jQuery plugin that formats to two decimal places
        (function($) {
            $.fn.currencyFormat = function() {
                this.each(function(i) {
                    $(this).change(function(e) {
                        if (isNaN(parseFloat(this.value))) return;
                        this.value = parseFloat(this.value).toFixed(2);
                    });
                });
                return this; //for chaining
            }
        })(jQuery);

        // apply the currencyFormat behavior
        $(function() {
            $('.maxamount').currencyFormat("${0:n2}");
        });
        $(document).ready(function() {
            if ($('#PEOApprovalRequired').is(':checked')) {
                $('#selectPEO').show();
            }
            SetSelectionTypes();
        });
        $('#PEOApprovalRequired').click(function() {
            if ($(this).is(':checked')) {
                $('#selectPEO').show();
            } else {
                $('#selectPEO').hide();
                $('#SelectedUsers option').remove().appendTo('#select1');
            }
        });
        $(document).ready(function () {
            $('#add').click(function () {
                return !$('#select1 option:selected').remove().appendTo('#SelectedUsers');
            });
            $('#addAll').click(function () {
                return !$('#select1 option').remove().appendTo('#SelectedUsers');
            });
            $('#remove').click(function () {
                return !$('#SelectedUsers option:selected').remove().appendTo('#select1');
            });
            $('#removeAll').click(function () {
                return !$('#SelectedUsers option').remove().appendTo('#select1');
            });
            $('#tsform').submit(function () {
                setSelect2();
            });
            function setSelect2() {
                var arr = [];
                $("#SelectedUsers > option").each(function () {
                    arr.push(this.value);
                });
                var str = arr.join(',');
                $('#selectedUserList').val(str);
            }
        });
    </script>

    <script>
        $(document).on("change", "#ProfileType", function () {
            SetSelectionTypes();
        });

        function SetSelectionTypes() {

            var val = $('#ProfileType').val();
            switch (val) {
                case "2":
                    $('#SelectionType').children().remove().end();
                    $('#SelectionType').append('<option value="8" selected>By Jobs</option>');
                    break;
                case "3":
                    $('#SelectionType').children().remove().end();
                    $('#SelectionType').append('<option value="8" selected>By Jobs</option>');
                    break;
                case "4":
                    $('#SelectionType').children().remove().end();
                    $('#SelectionType').append('<option value="2" selected>By Employee</option>');
                    break;
                case "6":
                    $('#SelectionType').children().remove().end();
                    $('#SelectionType').append('<option value="2" selected>By Employee</option>');
                    break;
                default:
                    $('#SelectionType').append('<option value="0">None</option>');
                    $('#SelectionType').append('<option value="1" selected>By Client</option>');
                    $('#SelectionType').append('<option value="2">By Employee</option>');
                    $('#SelectionType').append('<option value="3">By EE Class</option>');
                    $('#SelectionType').append('<option value="4">By Department</option>');
                    $('#SelectionType').append('<option value="6">By Division</option>');
                    $('#SelectionType').append('<option value="7">By WorkSite</option>');
                    break;
            }
        }
    </script>
}
