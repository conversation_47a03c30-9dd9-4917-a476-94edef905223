@using DarwiNet2._0.Core;
@using DarwiNet2._0.Extensions;
@using DarwiNet2._0.DNetSynch;

@model DarwiNet2._0.ViewModels.D2.CommissionsCalculationsViewModel
@{
    ViewBag.Title = "Commissions Calculated";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.2/animate.min.css"
      integrity="sha256-PHcOkPmOshsMBC+vtJdVr5Mwb7r0LkSVJPlPrp/IMpU="
      crossorigin="anonymous" />
<script src="https://cdn.jsdelivr.net/npm/vue-multiselect@2.1.0"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vue-multiselect@2.1.0/dist/vue-multiselect.min.css">

<style type="text/css">
    .fa-danger {
        color: red;
    }

    .fa-primary {
        color: #0D6EFD;
    }

    .fa-white {
        color: white;
    }

    .btn-gray {
        background-color: darkgray;
        color: white;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    .fa-thinkware {
        color: rgb(36 64 97);
    }

    .fa-hover:hover {
        text-shadow: 1px 1px 0 rgb(0 0 0 / 15%), 2px 2px 2px rgb(0 0 0 / 25%);
    }

    .page-header {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: space-between;
    }

    .header-group {
        display: flex;
    }

    .header-title {
        font-size: 25px;
        padding: 10px 5px;
    }

    .headerButton {
        padding: 10px 15px;
        font-size: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgba(0,0,0,0.2);
    }

        .headerButton:hover {
            background-color: rgba(0,0,0,0.3);
            cursor: pointer;
        }

    .filter-row {
        padding-bottom: 16px;
    }

    .justify-end {
        display: flex;
        justify-content: flex-end;
    }

    .action-row {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 100%;
    }
</style>

<div id="Calculated">
    <div class="row">
        <div class="col-sm-12">
            <div class="panel panel-thinkware">
                <div class="panel-heading page-header">
                    <div class="header-group">
                        <i class="fa fa-arrow-left headerButton"
                           title="Back to Commission Calculation"
                           @@click="goBack"></i>
                        <div class="header-title">
                            Calculated Commissions
                        </div>
                    </div>
                    <i class="fa fa-arrow-right headerButton"
                       title="Go to Commissions History"
                       @@click="gotToHistory"></i>
                </div>
                <div class="panel-body">
                    <div class="row margin-bottom-15">
                        <div class="col-md-6">
                            <input type="text" v-model="filters.SearchText" class="form-control" placeholder="Search" @@input="onSearchInput()">
                        </div>
                        <div class="col-md-4">
                            <thinkware-saved-filters :saved-filter-options="savedFilterOptions" :filter-table-id="configuration.Options.TableId" @@filter-select="setFiltersFromSaved" @@no-filters="clear" :current-saved-filter="activeFilter" :tabindex="0" ref="savedFiltersComp"></thinkware-saved-filters>
                        </div>
                        <div class="col-md-2">
                            <div class="action-row">
                                <thinkware-manage-filters :filter-table-id="configuration.Options.TableId" @@filter-update="updateFiltersComponent" @@save-new-filter="saveNewFilter" @@update-filter="updateSavedFilter"></thinkware-manage-filters>
                            </div>
                        </div>
                    </div>
                    <thinkware-show-hide-filters>
                        <template v-slot:content>
                            <thinkware-select-company-client-profile v-model="filters"
                                                                     class="filter-row"
                                                                     :multi-select="false"
                                                                     :show-profile="false"
                                                                     :colSize="3"
                                                                     :end-slot-span="2">
                                <template v-slot:end>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <thinkware-select :multi-select="true"
                                                              :fetch-url="'@Url.Action("DarwinInvoiceNumbers", "Filter")'"
                                                              v-model="filters.InvoiceNumber"
                                                              placeholder="Invoice Number"
                                                              :fetch-url-parameters="invoiceNumberParams"
                                                              :cascading="true"
                                                              ref="invoiceNumberSelectComponent"></thinkware-select>
                                        </div>
                                        <div class="col-md-6">
                                            <thinkware-select :multi-select="true"
                                                              :fetch-url="'@Url.Action("Salespersons", "Filter")'"
                                                              v-model="filters.Salesperson"
                                                              placeholder="Salesperson"
                                                              :fetch-url-parameters="salespersonParams"
                                                              :cascading="true"
                                                              ref="salespersonSelectComponent"></thinkware-select>
                                        </div>
                                    </div>
                                </template>
                            </thinkware-select-company-client-profile>
                            <div class="row justify-end">
                                <thinkware-filters-apply-clear @@apply="apply" @@clear="clear" :col-size="3"></thinkware-filters-apply-clear>
                            </div>
                        </template>
                    </thinkware-show-hide-filters>
                    <thinkware-vue-table :configuration="configuration" ref="calculatedCommissionsTable" no-resize>
                        <template v-slot:table-actions="table">
                            <div class="btn-group margin-y-10" role="group">
                                <button role="button"
                                        title="Payroll"
                                        :class="['btn', hasSelected(table) ? 'btn-success' : 'btn-bg']"
                                        :disabled="disableActions(table)"
                                        @@click="hasSelected(table) ? payByPayroll(table.selected) : null">
                                    Payroll
                                </button>
                                <button role="button"
                                        title="Vendor"
                                        :class="['btn', hasSelected(table) ? 'btn-primary' : 'btn-bg']"
                                        :disabled="disableActions(table)"
                                        @@click="hasSelected(table) ? payByVendorAP(table.selected) : null">
                                    Vendor
                                </button>
                                <button role="button"
                                        title="Other"
                                        :class="['btn', hasSelected(table) ? 'btn-gray' : 'btn-bg']"
                                        :disabled="disableActions(table)"
                                        @@click="hasSelected(table) ? payByOther(table.selected) : null">
                                    Other
                                </button>
                                <button role="button"
                                        title="Remove Calculated Commission"
                                        :class="['btn', hasSelected(table) ? 'btn-warning' : 'btn-bg']"
                                        :disabled="disableActions(table)"
                                        @@click="hasSelected(table) ? removeCommissionAmount(table.selected) : null">
                                    Remove
                                </button>
                                <button role="button"
                                        title="Clear Selected"
                                        :class="['btn', hasSelected(table) ? 'btn-danger' : 'btn-bg']"
                                        :disabled="disableActions(table)"
                                        @@click="hasSelected(table) ? table.clearSelected() : null">
                                    <i class="fa-solid fa-xmark"></i>
                                </button>
                                <button role="button"
                                        title="View Selected"
                                        class="btn btn-bg"
                                        :disabled="disableActions(table)"
                                        @@click="hasSelected(table) ? table.onViewSelected() : null">
                                    <i :class="['fa-regular', table.viewSelected ? 'fa-search-minus' : 'fa-search-plus']"></i>
                                </button>
                            </div>
                        </template>
                        <template v-slot:invoicenumber="row">
                            <div class="center-items">
                                <span>{{row.data.InvoiceNumber}}</span>
                            </div>
                        </template>
                        <template v-slot:expandable="row">
                            <div class="col-md-8 col-centered margin-top-10">
                                <calculations-table-expanded-row-details :row-data="row.data"
                                                                         :commission-methods="commissionMethods">
                                </calculations-table-expanded-row-details>
                            </div>
                        </template>
                    </thinkware-vue-table>
                </div>
            </div>
        </div>
    </div>

    <loading :active="loading"
             :is-full-page="isFullPage"
             :enforce-focus="enforceFocus"
             loader="dots"></loading>
</div>

<script src="~/Scripts/vue.min.js"></script>
<script src="~/Scripts/vue-loading-overlay.js"></script>
<link href="~/Scripts/vue-loading.css" rel="stylesheet">
<script src='https://unpkg.com/v-calendar@1.0.0/lib/v-calendar.umd.min.js'></script>

@Html.VueComponent("~/Shared/thinkware-vue-table")
@Html.VueComponent("~/Shared/thinkware-vue-modal")
@Html.VueComponent("calculations-table-expanded-row-details")
@Html.VueComponent("~/Shared/FilterComponents/thinkware-select")
@Html.VueComponent("~/Shared/FilterComponents/thinkware-select-company-client-profile")
@Html.VueComponent("~/Shared/FilterComponents/thinkware-filters-apply-clear")
@Html.VueComponent("~/Shared/FilterComponents/thinkware-show-hide-filters")
@Html.VueComponent("~/Shared/FilterComponents/thinkware-saved-filters")
@Html.VueComponent("~/Shared/FilterComponents/thinkware-manage-filters")
@Html.VueComponent("~/Shared/thinkware-alert-modal")

<script type="text/javascript">
    Vue.use(VueLoading);
    Vue.component('vue-multiselect', window.VueMultiselect.default);


    var vm = VueInstance('Calculated', {
        data: {
            filters: {
                SearchText: "",
                CompanyID: null,
                ClientID: null,
                InvoiceNumber: null,
                Salesperson: null,
            },
            showFilterDeleteModal: false,
            invoiceNumberParams: [],
            salespersonParams: [],
            activeFilter: null,
            savedFilterOptions: [],
            loading: false,
            isFullPage: true,
            enforceFocus: true,
            companyID: @Html.Raw(Model.CompanyID.ToJson()),
            commissionMethods: @Html.Raw(Model.CommissionMethods.ToJson()),
            configuration: {
                Options: {
                    TableId: @FilterTableConstants.CommissionsCalculated,
                    DataUri: '@Url.Action("GetCalculatedCommissionsTableData", "Commissions")',
                    SavedState: false,
                    Filterable: true,
                    DynamicFilter: true,
                    HideFilterControls: true,
                    HtmlRef: "calculatedCommissionsTable",
                    Groupable: {
                        Active: false,
                    },
                    Sortable: {
                        Active: true,
                        Codes: ["ClientID", "ClientName", "InvoiceNumber", "InvoiceDate", "Salesperson"]
                    },
                    Callable: {
                        Active: true,
                    },
                    Selectable: {
                        Active: true,
                        Align: "left",
                        Type: "all",
                    },
                    Expandable: {
                        Active: true,
                        Align: "left",
                    },
                    Scrollable: {
                        Active: false,
                    },
                    Searchable: {
                        Active: false,
                    },
                    Totals: {
                        Active: false,
                    },
                },
                Columns: [
                    {
                        Label: "SearchText!",
                        Code: "SearchText",
                        Width: "0px",
                        Render: "template",
                        FilterType: "NonSelect",
                        PreFilterValue: {},
                        ActiveFilters: [],
                    },
                    {
                        Label: "Company ID!",
                        Code: "CompanyID",
                        Width: "0px",
                        Render: "template",
                        FilterType: "Select",
                        PreFilterValue: {},
                        ActiveFilters: [],
                    },
                    {
                        Label: "Company",
                        Code: "CompanyName",
                        Width: "10%",
                    },
                    {
                        Label: "Client ID",
                        Code: "ClientID",
                        Width: "10%",
                        FilterType: "Select",
                        PreFilterValue: {},
                        ActiveFilters: [],
                    },
                    {
                        Label: "Client Name",
                        Code: "ClientName",
                        Width: "15%",
                    },
                    {
                        Label: "Invoice Number",
                        Code: "InvoiceNumber",
                        Render: "template",
                        Width: "15%",
                        HeaderAlign: "center",
                        FilterType: "Select",
                        PreFilterValue: {},
                        ActiveFilters: [],
                    },
                    {
                        Label: "Invoice Date",
                        Code: "InvoiceDate",
                        Render: "date",
                        Width: "13%",
                    },
                    {
                        Label: "Salesperson",
                        Code: "Salesperson",
                        Width: "12%",
                        FilterType: "Salesperson",
                        FilterType: "Select",
                        PreFilterValue: {},
                        ActiveFilters: [],
                    },
                    {
                        Label: "Commissionable",
                        Code: "Commissionable",
                        Render: "currency",
                        Width: "15%",
                    },
                    {
                        Label: "Commission",
                        Code: "Commission",
                        Render: "currency",
                        Width: "10%",
                    },
                ]
            }
        },
        mounted: function () {

        },
        watch: {
            'filters.ClientID': function (val) {
                if (this.filters.ClientID != null && this.filters.ClientID.length > 0) {
                    this.setInvoiceNumberParameters();
                }
                else {
                    this.$refs.invoiceNumberSelectComponent.clearOptions();
                }
            },
            'filters.CompanyID': function (val) {
                if (this.filters.CompanyID != null && this.filters.CompanyID.length > 0) {
                    this.setSalespersonParameters();
                }
                else {
                    this.$refs.salespersonSelectComponent.clearOptions();
                }
            },
        },
        components: [
            ThinkwareVueTableComponent,
            CalculationsTableExpandedRowDetailsComponent,
            ThinkwareSelectComponent,
            ThinkwareSelectCompanyClientProfileComponent,
            ThinkwareFiltersApplyClearComponent,
            ThinkwareShowHideFiltersComponent,
            ThinkwareSavedFiltersComponent,
            ThinkwareVueModalComponent,
            ThinkwareAlertModal,
            ThinkwareManageFiltersComponent,
        ],
        methods: {
            // *********** Filtering ************** //
            onSearchInput: _.debounce(function () {
                this.apply();
            }, 300),
            setFiltersFromSaved: function(filter, filterName) {
                this.activeFilter = filterName;

                this.clearOnlyFilter();

                // Pass configuration object, filter data object, and the filter you are loading
                this.filters = ThinkwareCommon.setFilterObjectFromSavedFilter(this.configuration, this.filters, filter);

                this.updateTableSettings(filter);

                this.updateTableConfig();
            },
            updateTableSettings: function (filter) {
                var table = this.$refs.calculatedCommissionsTable;
                table.tableSettings.CurrentPage = filter.CurrentPage;
                table.tableSettings.PerPage = filter.PerPage;
                table.tableSettings.PerPageOptions = filter.PerPageOptions;
            },
            updateFiltersComponent: function() {
                this.savedFilterOptions = [];
                this.$refs.savedFiltersComp.refresh();
            },
            saveNewFilter: function(filterName) {
                this.updateTableConfig();

                ThinkwareCommon.saveFilter('@Url.Action("SaveOrUpdate", "UserTableFilter")', this.configuration.Options.TableId, this.$refs.calculatedCommissionsTable.tableSettings, filterName);

                this.savedFilterOptions.push({ Display: filterName, Code: filterName, Filter: JSON.stringify(this.$refs.calculatedCommissionsTable.tableSettings), Default: false })
            },
            updateSavedFilter: function(filterName) {
                this.updateTableConfig();
                
                let self = this;
                ThinkwareCommon.ajax.postJson('@Url.Action("UpdateSavedFilter", "Filter")', {
                    data: {
                        filterTableId: self.configuration.Options.TableId,
                        referenceId: filterName,
                        filter: JSON.stringify(self.$refs.calculatedCommissionsTable.tableSettings)
                    },
                    onSuccess: function (result) {
                        self.updateFiltersComponent();
                        console.log('Successfully updated filter.')
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        console.log('err', errorThrown);
                    }
                });
            },
            closeSaveFilterModal: function() {
                this.showSaveFilterModal = false;
                this.saveFilterName = null;
            },
            apply: function () {
                this.$refs.calculatedCommissionsTable.tableSettings.CurrentPage = 1;
                this.updateTableConfig();
            },
            clear: function () {
                this.filters = {
                    SearchText: "",
                    CompanyID: null,
                    ClientID: null,
                    InvoiceNumber: null,
                    Salesperson: null,
                };

                this.activeFilter = null;

                this.configuration.Columns.forEach(x => {
                    x.ActiveFilters = []
                });

                this.$refs.calculatedCommissionsTable.clearTable();
            },
            clearOnlyFilter: function () {
                this.filters = {
                    SearchText: "",
                    CompanyID: null,
                    ClientID: null,
                    InvoiceNumber: null,
                    Salesperson: null,
                }
            },
            updateTableConfig: function () {
                this.configuration = ThinkwareCommon.updateConfigurationObjectFromFilterObject(this.filters, this.configuration)

                this.$refs.calculatedCommissionsTable.reloadTable();
            },
            setInvoiceNumberParameters: function () {
                // First clear the params so we can reset them
                this.invoiceNumberParams = []
                this.filters.ClientID.forEach(x => this.invoiceNumberParams.push(
                    {
                        ParameterKey: 'clientIDs',
                        ParameterValue: x.Code
                    }
                ));
            },
            setSalespersonParameters: function () {
                // First clear the params so we can reset them
                this.salespersonParams = []
                this.filters.CompanyID.forEach(x => this.salespersonParams.push(
                    {
                        ParameterKey: 'companyIDs',
                        ParameterValue: x.Code
                    }
                ));
            },
            // *********************************** //
            goBack: function () {
                window.location = '@Url.Action("Calculation", "Commissions")'
            },
            gotToHistory: function () {
                window.location = '@Url.Action("History", "Commissions")'
            },
            hasSelected: function (table) {
                if (table.selected.length > 0) {
                    return true;
                }
                return false;
            },
            disableActions: function (table) {
                if (table.selected.length === 0) {
                    return true;
                }
                return false;
            },
            removeCommissionAmount: function (selected) {
                const self = this;
                self.loading = true;
                const data = selected.map((x, index) => ({
                    CompanyID: x.CompanyID,
                    ClientID: x.ClientID,
                    InvoiceNumber: x.InvoiceNumber,
                    SalespersonID: x.Salesperson,
                    EmployeeID: x.EmployeeID,
                    InvoiceDate: self.convertDate(x.InvoiceDate),
                    CommissionMethod: x.CommissionMethod
                }));
                ThinkwareCommon.ajax.postJson('@Url.Action("RemoveCommissionAmounts", "Commissions")', {
                    data: data,
                    onSuccess: function (results) {
                        self.loading = false;
                        window.location.reload();
                    },
                    onError: function (jqHRX, testStatus, errorThrown) {
                        self.loading = false;
                        console.log(errorThrown)
                    }
                });
            },
            payByPayroll: function (selected) {
                const self = this;
                self.loading = true;
                const data = selected.map((x, index) => ({
                    CompanyID: x.CompanyID,
                    ClientID: x.ClientID,
                    InvoiceNumber: x.InvoiceNumber,
                    SalespersonID: x.Salesperson,
                    EmployeeID: x.EmployeeID,
                    InvoiceDate: self.convertDate(x.InvoiceDate),
                    CommissionMethod: x.CommissionMethod,
                    CommissionDestination: x.CommissionDestination,
                    HoursWorkedDefault: x.HoursWorkedDefault,
                }));
                console.log(data)
                ThinkwareCommon.ajax.postJson('@Url.Action("ProcessPayrollPayments", "Commissions")', {
                    data: data,
                    onSuccess: function (results) {
                        window.location = '@Url.Action("History", "Commissions")';
                    },
                    onError: function (jqHRX, testStatus, errorThrown) {
                        self.loading = false;
                        console.log(errorThrown)
                    }
                });
            },
            payByVendorAP: function (selected) {
                const self = this;
                self.loading = true;
                const data = selected.map((x, index) => ({
                    CompanyID: x.CompanyID,
                    ClientID: x.ClientID,
                    InvoiceNumber: x.InvoiceNumber,
                    SalespersonID: x.Salesperson,
                    EmployeeID: x.EmployeeID,
                    InvoiceDate: self.convertDate(x.InvoiceDate),
                    CommissionMethod: x.CommissionMethod,
                    CommissionDestination: x.CommissionDestination,
                    HoursWorkedDefault: x.HoursWorkedDefault,
                }));
                ThinkwareCommon.ajax.postJson('@Url.Action("ProcessVendorPayments", "Commissions")', {
                    data: data,
                    onSuccess: function (results) {
                        window.location = '@Url.Action("History", "Commissions")';
                    },
                    onError: function (jqHRX, testStatus, errorThrown) {
                        self.loading = false;
                        ThinkwareCommon.showAlert('danger', errorThrown);
                    }
                });
            },
            payByOther: function (selected) {
                const self = this;
                self.loading = true;
                const data = selected.map((x, index) => ({
                    CompanyID: x.CompanyID,
                    ClientID: x.ClientID,
                    InvoiceNumber: x.InvoiceNumber,
                    SalespersonID: x.Salesperson
                }));
                console.log(data)
                ThinkwareCommon.ajax.postJson('@Url.Action("ProcessOtherPayments", "Commissions")', {
                    data: data,
                    onSuccess: function (results) {
                        window.location = '@Url.Action("History", "Commissions")';
                    },
                    onError: function (jqHRX, testStatus, errorThrown) {
                        self.loading = false;
                        ThinkwareCommon.showAlert('danger', errorThrown);
                    }
                })
            },
            convertDate: function (date) {
                var temp = date;
                if (temp == null || temp == '') {
                    return new Date();
                }

                if (typeof temp === 'string' && temp.startsWith("/")) {
                    temp = Number(temp.substring(temp.indexOf("(") + 1, temp.indexOf(")")));
                }

                return new Date(temp);
            }
        }
    });
</script>
