@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DataDrivenViewEngine.Models.Core

@{
    ViewBag.Title = "Hours";
    ViewBag.ParentCrumb = "Insight,Payroll";
}
<style>
    .total {
        font-size: 26px
    }

    .overlay {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        text-align: center;
    }

        .overlay div {
            position: relative;
            font-size: 34px;
            margin-top: -17px;
            top: 50%;
            color: #b1aea6;
        }
</style>
<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
    </div>
</div>
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <script src="~/Scripts/loading.js"></script>
    <script>
        var companyID = @GlobalVariables.CompanyID;
        var clientID = '@GlobalVariables.Client';
        var startDate;
        var endDate;
        var chartAction;
        var interval;
        var readAction;
        var position;
        var department;
        var division;
        var ival;
        var chartid = 'Payroll_Summary_Json';
        var detailBy = 'Department';
        var IntervalType = "None";
        var NumIntervals = 0;
        var wc = "";
        var paytype = "";
        var paycode = "";
        var state = "";

        function getCharts() {
            var tableWagesUrl = '@Url.Action("Payroll_Summary_Wages", "ClientReporting")';
            var chartWagesDetailUrl = '@Url.Action("PayrollSummaryChartView", "ClientReporting")';
            var tableWagesDetailUrl = '@Url.Action("Payroll_Summary_WageDetail", "ClientReporting")';
            var tableEmployeesUrl = '@Url.Action("Payroll_Summary_Employees", "ClientReporting")';
            var chartEmployeesUrl = '';

            var value = $("#routing").val();
            readAction = chartid;
            detailBy = $("#detailBy").val();
            position = $("#position").val();
            department = $("#departments").val();
            division = $("#division").val();
            ival = "";
        
            getSliderDates();
            $('#ChartDiv').load(chartWagesDetailUrl, { ra: chartid, companyID: companyID, clientID: clientID, startdate: startDate, enddate: endDate, i: ival, comparetype: IntervalType, comparevalue: NumIntervals, detailBy: detailBy, divisionID: division, wcompID: wc, payType: paytype, payCode: paycode, state: state, positionID: position, departmentID: department }, function(response, status, xhr) {});
            $('#SummaryTableDiv').load(tableWagesUrl, { companyID: companyID, clientID: clientID, startdate: startDate, enddate: endDate, divisionID: division, departmentID: department, positionID: position }, function(response, status, xhr) {});
            $('#DetailTableDiv').load(tableWagesDetailUrl, { companyID: companyID, clientID: clientID, startdate: startDate, enddate: endDate, divisionID: division, departmentID: department, positionID: position }, function(response, status, xhr) {});
            $('#EmployeeTableDiv').load(tableEmployeesUrl, { companyID: companyID, clientID: clientID, startdate: startDate, enddate: endDate, divisionID: division, departmentID: department, positionID: position }, function(response, status, xhr) { createChart(); });
        }
    </script>

    @Html.Partial("~/Views/PEOReporting/_SliderPartial.cshtml")

    <div class="row">
        <div>
            <h2>Filter Criteria</h2>
            <div class="col-md-12">
                <div>
                    <span>
                    @(Html.Kendo().DropDownList()
                          .Name("division")
                          .HtmlAttributes(new {style = "width:300px"})
                          .OptionLabel("Select Division...")
                          .DataTextField("Description")
                          .DataValueField("Code")
                          .DataSource(source =>
                          {
                              source.Read(read =>
                              {
                                  read.Action("GetCascadeDivisionsAll", "ClientReporting");
                              });
                          })
                          )
                </span>
                    <span>
                    @(Html.Kendo().DropDownList()
                          .Name("departments")
                          .HtmlAttributes(new {style = "width:300px"})
                          .OptionLabel("Select Department...")
                          .DataTextField("Description")
                          .DataValueField("Code")
                          .DataSource(source =>
                          {
                              source.Read(read =>
                              {
                                  read.Action("GetCascadeDepartmentsAll", "ClientReporting");
                              })
                                  .ServerFiltering(true);
                          })
                          .Enable(false)
                          .AutoBind(false)
                          .CascadeFrom("division")
                          )
                </span>
                    <span>
                    @(Html.Kendo().DropDownList()
                          .Name("position")
                          .HtmlAttributes(new {style = "width:300px"})
                          .OptionLabel("Select Position...")
                          .DataTextField("Description")
                          .DataValueField("Code")
                          .DataSource(source =>
                          {
                              source.Read(read =>
                              {
                                  read.Action("GetPositionsAll", "ClientReporting");
                                  //.Data("cfilterComps");
                              });
                          })
                          )
                </span>
                    <input type="button" value="Build Chart" class="btn btn-danger" onclick="getCharts();"/>
                </div>
            </div>
        </div>

        <br clear="all"/>
        <div>
            <div class="" style="border-bottom: 1px solid #ccc;">
                <div class="row">
                    <div class="col-md-12" style="padding-top: 25px;">
                        <div class="text-center">
                            <div id="SummaryTableDiv"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="" style="border-bottom: 1px solid #ccc;">
                <div class="col-md-12">
                    <div class="col-md-6" style="padding-top: 25px;">
                        <div id="ChartDiv"></div>
                    </div>
                    <div class="col-md-6" style="padding-top: 125px;">
                        <div id="DetailTableDiv"></div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="">
                <div class="col-md-6" style="padding-top: 100px;">
                    <div id="EmployeeTableDiv"></div>
                </div>
                <div class="col-md-6" style="padding-top: 25px;">
                    <div id="chart"></div>
                    <div class="overlay">
                        <div>No data available</div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <input type="hidden" id="new" name="new" value="55"/>
    <script>
        function createChart() {
            var data = [
                {
                    "source": "New",
                    "percentage": $("#newEE").text(),
                },
                {
                    "source": "Term",
                    "percentage": $("#termEE").text(),
                }
            ];
            $("#chart").kendoChart({
                title: {
                    text: "Workforce Metric"
                },
                legend: {
                    position: "bottom"
                },
                dataSource: {
                    data: data
                },
                series: [
                    {
                        type: "pie",
                        field: "percentage",
                        categoryField: "source",
                        explodeField: "exploded"
                    }
                ],
                dataBound: function(e) {
                    var view = e.sender.dataSource.view();
                    var newEmployees = parseInt(view[0].percentage);
                    var termEmployees = parseInt(view[1].percentage);
                    $(".overlay").toggle((newEmployees + termEmployees) === 0);
                },
                seriesColors: ["#03a9f4", "#ff9800", "#fad84a", "#4caf50"],
                tooltip: {
                    visible: true,
                    template: "${ category } - ${ value }"
                }
            });
        }

        // $(document).ready(createChart);
        $(document).bind("kendo:skinChange", createChart);
    </script>
    <script>
        $('#chkComp').click(function() {
            if ($(this).is(':checked')) {
                //alert('checked');
            } else
                $('#selComp').val('0');
            $('#radInt').val('Years');
        });
    </script>
    <script src="~/Scripts/chartRedraw.js"></script>

}