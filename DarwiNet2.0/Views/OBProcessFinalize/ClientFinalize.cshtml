@model IEnumerable<DarwiNet2._0.Data.OBProcessMonitorVM>
@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@{
    ViewBag.Title = "Client Finalize";
    var myLevel = @GlobalVariables.DNETLevel;
}
@section styles{
    <link href="~/Content/sweetalert.css" rel="stylesheet" />
    <style>
        .k-grid {
            overflow: inherit;
        }
    </style>
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div class="toolbar">
        <div class="row">
            <p class="create-pad pull-right">
                <a href="@Url.Action("SaveClientFinalizeDetails", "OBProcess")" class="btn btn-thinkware">Sign & Finalize</a>
            </p>
        </div>
    </div>
    <div class="table-bottom">
        @(Html.Kendo().Grid<DarwiNet2._0.Data.OBProcessMonitorVM>()
        .Name("grid")
        .Columns(columns =>
        {
            //if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
            //{
            //    columns.Bound(e => e.ClientID).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Client", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Client", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Client", GlobalVariables.LanguageID) }).Width(120);
            //}
            if (GlobalVariables.DNETLevel == DNetAccessLevel.System || GlobalVariables.DNETLevel == DNetAccessLevel.Client)
            {
                columns.Bound(e => e.ProfileName).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Profile", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Profile", GlobalVariables.LanguageID)).Width(250).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Profile", GlobalVariables.LanguageID) });
            }
            columns.Bound(e => e.EmployeeID).Hidden(true).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Employee ID", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Employee ID", GlobalVariables.LanguageID)).Width(150).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID) });
            columns.Bound(e => e.EmployeeName).Width(300).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID));
            columns.Bound(e => e.eeTaskName).Width(180).Title(FieldTranslation.GetLabel("EmployeeTask", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeTask", GlobalVariables.LanguageID) });
            columns.Bound(e => e.eeTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) }).Width(150);
            columns.Bound(e => e.ccTaskname).Title(FieldTranslation.GetLabel("ClientTask", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("ClientTask", GlobalVariables.LanguageID) }).Width(150);
            columns.Bound(e => e.ccTaskStatus).Title(FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("TaskStatus", GlobalVariables.LanguageID) }).Width(150);
            //columns.Bound(e => e.DueDate).Format("{0:MM/dd/yyyy}").Width(150).Title(FieldTranslation.GetLabel("DueDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("DueDate", GlobalVariables.LanguageID) });
            columns.Bound(e => e.Division).Hidden(true).Title(FieldTranslation.GetLabel("Division", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Division", GlobalVariables.LanguageID) });
            columns.Bound(e => e.CCSubmitBy).Hidden(true).Title(FieldTranslation.GetLabel("Submitted By", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Submitted By", GlobalVariables.LanguageID) });
            columns.Bound(e => e.Comments).Title("Comments").HeaderHtmlAttributes(new { title = "Comments"}).Width(600);
        })
            .ColumnMenu()
            .Sortable()
            .Pageable()
            .Filterable()
            .Scrollable()
            .HtmlAttributes(new { style = "height: 450px" })
            .DataSource(dataSource => dataSource
            .Ajax()
            .Read(read => read.Action("GetOBClientFinalizeEEs_Read", "OBProcessMonitor"))
            .PageSize(15)
            )
                )
    </div>
}

