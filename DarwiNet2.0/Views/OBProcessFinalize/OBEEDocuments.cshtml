@using DataDrivenViewEngine.Models.Core
@{
    Layout = null;
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div>
        @using (Html.BeginForm("Download", "OBProcessMonitor", FormMethod.Post, new { id = "frmDownloads", name = "frmDownloads" }))
        {
            <input type="hidden" name="emplId" id="emplId" value="@ViewBag.EmployeeID" />
            <div class="form-horizontal">
                <div class="form-group">
                    <label class="col-md-2 control-label">Include Signed Documents</label>
                    <div class="col-md-9 check-down">
                        <input type="checkbox" name="addDoc" id="addDoc" checked />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">Include Attachments</label>
                    <div class="col-md-9 check-down">
                        <input type="checkbox" name="addAttach" id="addDocs" />
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-12">
                        <div class="pull-right">
                            <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                            <button type="submit" id="dwld" class="btn btn-thinkware">Download</button>
                        </div>
                    </div>
                </div>
            </div>
        }

    </div>
    <script>
        $('#dwld').submit(function (e) {
            e.preventDefault();
            // Coding
            $('#docModal').modal('toggle'); //or  $('#docModal').modal('hide');
            return false;
        });
    </script>
}