@using DarwiNet2._0.Helpers;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.OpenAccess;
@using DataDrivenViewEngine.Models.Core;

<div class="sidebar-form">
    @{
        if (GlobalVariables.HasMultiRole)
        {
            <select id="test-select-role" class="form-control">
                <option>-- Select Role --</option>
                @if (GlobalVariables.AvailableRoles != null)
                {
                    foreach (Code_Description role in GlobalVariables.AvailableRoles)
                    {
                        <option value="@role.Code">@role.Description</option>
                    }
                }
            </select>
        }
    }
</div>
<ul class="sidebar-menu">
    <li class="header">MENU</li>
    @if (GlobalVariables.DnetRoleMenu != null)
    {
        bool openedUL = false;
        int LastParent = 0;
        bool FirstItemMade = false;
        foreach (MenuItems theItem in GlobalVariables.DnetRoleMenu)
        {
            if (theItem.ItemLevel == 1)
            {
                if (openedUL)
                    {
                    if(LastParent == 32000){
                        @:<ul>
                    }
                    @:</ul>
                    openedUL = false;
                    }
                if (FirstItemMade)
                {
                    if (LastParent == 32000)
                    {
                        @:<li>
                    }
                    
                    @:</li>
                }
                else
                {
                    FirstItemMade = true;
                }
                @:<li class="treeview">
                    if (string.IsNullOrEmpty(theItem.MenuPath))
                    {
                        <a href="javascript:void(0)"><i style="font-size 20px" class="@theItem.IconClass"></i><span>&nbsp;&nbsp;@theItem.MenuItem</span>
                            @if (theItem.HasChildren)
                            {
                                <i class="fa fa-angle-left pull-right"></i>
                            }
                        </a>
                    }
                    else
                    {
                        <a href="@Url.Action(theItem.MenuPath, theItem.ControllerName)"><i style="font-size 20px" class="@theItem.IconClass"></i><span>&nbsp;&nbsp;@theItem.MenuItem</span>
                            @if (theItem.HasChildren)
                            {
                                <i class='fa fa-angle-left pull-right'></i>
                            }
                        </a>
                    }
            }
            else
            {
                if (theItem.ParentID != LastParent)
                {
                    if (openedUL)
                    {
                        @:</ul>
                        openedUL = false;
                    }
                }
                if (theItem.ParentID != 0 && !openedUL)
                {
                    @:<ul class='treeview-menu'>
                    openedUL = true;
                }
                if (LastParent == 32000)
                {
                    @:<li>
                }
                if (theItem.ParentID == 0)
                {
                    @:</li>
                }
               
                if (string.IsNullOrEmpty(theItem.MenuPath))
                {

                    <li><a href="javascript:void(0)"><span>&nbsp;&nbsp;@theItem.MenuItem</span>
                        @if (theItem.HasChildren)
                        {
                            <i class="fa fa-angle-left pull-right"></i>
                        }
                    </a></li>
                }
                else
                {
                    <li><a href="@Url.Action(theItem.MenuPath, theItem.ControllerName)"><span>&nbsp;&nbsp;@theItem.MenuItem</span>
                        @if (theItem.HasChildren)
                        {
                            <i class='fa fa-angle-left pull-right'></i>
                        }
                    </a></li>

                }
            }
            LastParent = theItem.ParentID;
        }
    }

</ul>

@if (GlobalVariables.DNETLevel == DNetAccessLevel.System)
{
    <!-- select form  -->
    <div class="sidebar-form">
        <select id="select-level" class="form-control">
            <option>-- Select Level --</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "System"})">System</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "Client"})">Client</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "Employee"})">Employee</option>
        </select>
    </div>
    <ul class="sidebar-menu">
        <li class="header">MENU</li>
        <li class="treeview">
            <a href="@Url.Action("Index","Users")"><i style=" font-size 20px" class="ion-person-stalker"></i> <span>&nbsp;&nbsp;&nbsp;Users</span></a>
        </li>
        <li class="treeview @Html.IsActive("Roles", "Users") @Html.IsActive("CreateRole", "Users") @Html.IsActive("EditRole", "Users") @Html.IsActive("Security", "CompanySetup")">
            <a href="javascript:void(0);"><i style="font-size: 20px" class="ion-locked"></i> <span>&nbsp;&nbsp;&nbsp;Security</span> <i class="fa fa-angle-left pull-right"></i></a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("Roles", "Users") @Html.IsActive("CreateRole", "Users") @Html.IsActive("EditRole", "Users")"><a href="@Url.Action("Roles","Users")"> Roles</a></li>
                <li class="@Html.IsActive("Security", "CompanySetup")"><a href="@Url.Action("Security","CompanySetup")"> Client Division/Dept</a></li>
            </ul>
        </li>
        <li class="treeview @Html.IsActive("Index", "Clients" ) @Html.IsActive("Customization", "Clients") @Html.IsActive("Users", "Clients")">
            <a href=" javascript:void(0);"><i style="font-size: 20px" class="ion-wrench"></i> <span>&nbsp;&nbsp;Client Maintenance</span> <i class="fa fa-angle-left pull-right"></i></a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("Index", "Clients")"><a href="@Url.Action("Index", "Clients")"> Clients</a></li>
                <li><a href="javascript:void(0);"> Check Printer</a></li>
            </ul>
        </li>
        <li class="treeview">
            <a href=" javascript:void(0);"><i style="font-size: 20px" class="ion-planet"></i> <span>&nbsp;On-Boarding</span> <i class="fa fa-angle-left pull-right"></i></a>
            <ul class="treeview-menu">
                <li><a href="javascript:void(0);"> Documents</a></li>
                <li><a href="javascript:void(0);"> Profiles</a></li>
                <li><a href="javascript:void(0);"> Monitor</a></li>
            </ul>
        </li>
        <li class="treeview @Html.IsActive("Index", "CompanySetup") @Html.IsActive("Setup", "CompanySetup")">
            <a href="javascript:void(0);"><i style="font-size: 20px" class="ion-ios-gear"></i> <span>&nbsp;&nbsp;&nbsp;Setup</span> <i class="fa fa-angle-left pull-right"></i></a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("Index", "CompanySetup")"><a href="@Url.Action("Index", "CompanySetup")"> Company List</a></li>
                <li class="@Html.IsActive("News", "CompanyTools")"><a href="@Url.Action("News", "CompanyTools")"> News</a></li>
            </ul>
        </li>
        <li class="treeview @Html.IsActiveControl("PEOReporting") ">
            <a href=" javascript:void(0);"><i style="font-size: 20px" class="fa fa-bar-chart"></i> <span>&nbsp;&nbsp;&nbsp;PEO Reporting</span> <i class="fa fa-angle-left pull-right"></i></a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("Index", "PEOReporting")"><a href="@Url.Action("Index", "PEOReporting")"> Overview</a></li>
                <li class="@Html.IsActive("Billing", "PEOReporting")"><a href="@Url.Action("Billing", "PEOReporting")"> Billing</a></li>
                <li class="@Html.IsActive("Fees", "PEOReporting")"><a href="@Url.Action("Fees", "PEOReporting")"> Fees</a></li>
            </ul>
        </li>
        <li class="treeview">
            <a href="javascript:void(0);"><i style="font-size: 20px" class="ion-ios-book"></i> <span>&nbsp;&nbsp;&nbsp;Library</span></a>
        </li>
        <li>
            <a href="@Url.Action("LogOut", "Home" )"><i class="fa fa-sign-out"></i> <span>&nbsp;&nbsp;Logout</span></a>
        </li>
    </ul>
        @*<li class="treeview">
                <a href="javascript:void(0);">
                    <i style="font-size: 20px" class="ion-ios-flower"></i> <span>&nbsp;&nbsp;Workflows</span> <i class="fa fa-angle-left pull-right"></i>
                </a>
                <ul class="treeview-menu">
                    <li><a href="javascript:void(0);"> Setup</a></li>
                    <li><a href="javascript:void(0);"> Assignment</a></li>
                    <li><a href="javascript:void(0);"> Workflow Monitor</a></li>
                </ul>
            </li>
            <li class="treeview">
                <a href="javascript:void(0);">
                    <i style="font-size: 20px" class="ion-android-notifications"></i> <span>&nbsp;&nbsp;&nbsp;Notifications</span> <i class="fa fa-angle-left pull-right"></i>
                </a>
                <ul class="treeview-menu">
                    <li><a href="javascript:void(0);"> Setup</a></li>
                    <li><a href="javascript:void(0);"> Assignment</a></li>
                    <li><a href="javascript:void(0);"> Monitor</a></li>
                </ul>
            </li>*@
        
}
else if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
{
    <!-- select form  -->
    <div class="sidebar-form">
        @{
    if (GlobalVariables.HasMultiRole)
    {
        <select id="test-select-role" class="form-control">
            <option>-- Select Role --</option>
            @if (GlobalVariables.AvailableRoles != null)
            {
                foreach (Code_Description role in GlobalVariables.AvailableRoles)
                {
                    <option value="@role.Code">@role.Description</option>
                }
            }
        </select>
    }
        }
        <select id="select-level" class="form-control">
            <option>-- Select Level --</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "System"})">System</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "Client"})">Client</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "Employee"})">Employee</option>
        </select>
    </div>
            <!-- /.select form -->
    <ul class="sidebar-menu">
        <li class="header">MENU</li>
        <li class="treeview @Html.IsActiveControl("DashBoard")">
            <a href="@Url.Action("Index", "Dashboard")">
                <i class="fa fa-dashboard"></i> <span>&nbsp;Dashboard</span>
            </a>
        </li>
        <li class="treeview @Html.IsActiveControl("CompanyInformation") @Html.IsActiveControl("CompanySetup") @Html.IsActiveControl("CompanyTools")">
            <a href="javascript:void(0);">
                <i class="fa fa-building fa-fw"></i> <span>&nbsp;Company</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActiveControl("CompanyInformation")">
                    <a href="javascript:void(0);"> Information <i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("Index", "CompanyInformation")"><a href="@Url.Action("Index", "CompanyInformation")"> Information</a></li>
                        <li class="@Html.IsActive("Contacts", "CompanyInformation")"><a href="@Url.Action("Contacts", "CompanyInformation")"> Contacts</a></li>
                        <li class="@Html.IsActive("Calendar", "CompanyInformation")"><a href="@Url.Action("Calendar", "CompanyInformation")"> Calendar</a></li>
                    </ul>
                </li>
                <li class="@Html.IsActiveControl("CompanySetup")">
                    <a href="javascript:void(0);"> Setup <i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("Ach", "CompanySetup")"><a href="@Url.Action("Ach", "CompanySetup")"> ACH</a></li>
                        <li class="@Html.IsActive("Hr", "CompanySetup")"><a href="@Url.Action("Hr", "CompanySetup", new {a="t" })"> HR</a></li>
                        <li class="@Html.IsActive("Calendar", "CompanySetup")"><a href="@Url.Action("Calendar", "CompanySetup")"> Calendar</a></li>
                        <li class="@Html.IsActive("Users", "CompanySetup")"><a href="@Url.Action("Users", "CompanySetup")"> Users</a></li>
                        <li class="@Html.IsActive("TimeSheets", "CompanySetup")"><a href="@Url.Action("TimeSheets", "CompanySetup")"> Time Sheets</a></li>
                        <li class="@Html.IsActive("TimeClock", "CompanySetup")"><a href="@Url.Action("TimeClock", "CompanySetup")"> Time Clock</a></li>
                    </ul>
                </li>
                <li class="@Html.IsActiveControl("CompanyTools")">
                    <a href="javascript:void(0);"> Tools <i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("Workflow", "CompanyTools")"><a href="@Url.Action("Workflow", "CompanyTools")"> Workflow</a></li>
                        <li class="@Html.IsActive("Notifications", "CompanyTools")"><a href="@Url.Action("Notifications", "CompanyTools")"> Notifications</a></li>
                        <li class="@Html.IsActive("DocumentManagement", "CompanyTools")"><a href="@Url.Action("DocumentManagement", "CompanyTools")"> Document Management</a></li>
                        <li class="@Html.IsActive("News", "CompanyTools")"><a href="@Url.Action("News", "CompanyTools")"> News</a></li>
                        <li class="@Html.IsActive("Customize", "CompanyTools")"><a href="@Url.Action("Customize", "CompanyTools")"> Customize</a></li>
                    </ul>
                </li>
            </ul>
        </li>

        <li class="treeview @Html.IsActiveControl("Employees") @Html.IsActiveControl("OBProcessMonitor")">
            <a href="javascript:void(0);">
                <i style="font-size: 20px" class="ion-person-stalker"></i> <span>&nbsp;&nbsp;Employees</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("List", "Employees")"><a href="@Url.Action("List", "Employees")"> List</a></li>
                <li class="@Html.IsActive("Snapshot", "Employees")"><a href="@Url.Action("Snapshot", "Employees")"> Snapshot</a></li>
                <li class="@Html.IsActive("Index", "OBProcessMonitor") @Html.IsActive("Create", "OBProcessMonitor")"><a href="@Url.Action("Index", "OBProcessMonitor")"> New</a></li>
            </ul>
        </li>

        <li class="treeview @Html.IsActiveControl("PayrollInvoices") @Html.IsActiveControl("Payroll")">
            <a href="javascript:void(0);">
                <i class="fa fa-usd"></i> <span>&nbsp;Payroll</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActiveControl("PayrollInvoices")">
                    <a href="javascript:void(0);"> Invoices <i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("Preview", "PayrollInvoices")"><a href="@Url.Action("Preview", "PayrollInvoices")"> Preview</a></li>
                        <li class="@Html.IsActive("Current", "PayrollInvoices")"><a href="@Url.Action("Current", "PayrollInvoices", new { i = 0 })"> Current</a></li>
                        <li class="@Html.IsActive("List", "PayrollInvoices")"><a href="@Url.Action("List", "PayrollInvoices")"> List</a></li>
                        <li class="@Html.IsActive("Balance", "PayrollInvoices")"><a href="@Url.Action("Balance", "PayrollInvoices")"> Balance</a></li>
                        <li class="@Html.IsActive("TotalBurden", "PayrollInvoices")"><a href="@Url.Action("TotalBurden", "PayrollInvoices")"> Total Burden</a></li>
                    </ul>
                </li>
                <li class="@Html.IsActiveControl("Payroll")">
                    <a href="javascript:void(0);"> Payroll <i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("Schedule", "Payroll")"><a href="@Url.Action("Schedule", "Payroll")"> Schedule</a></li>
                        <li class="@Html.IsActive("CheckHistory", "Payroll")"><a href="@Url.Action("CheckHistory", "Payroll")"> Check History</a></li>
                        <li class="@Html.IsActive("TimeEntry", "Payroll") @Html.IsActive("StandardTimeSheet", "Payroll") @Html.IsActive("DetailTimeSheet", "Payroll") @Html.IsActive("JobCostByEmployee", "Payroll") @Html.IsActive("CertifiedTimeSheet", "Payroll") @Html.IsActive("EmployeeDetailTimeSheet", "Payroll")"><a href="@Url.Action("TimeEntry", "Payroll")"> Time Entry</a></li>
                        <li class="@Html.IsActive("TimeClock", "Payroll")"><a href="@Url.Action("TimeClock", "Payroll")"> Time Clock</a></li>
                    </ul>
                </li>
            </ul>
        </li>

        <li class="treeview @Html.IsActiveControl("HRCenter") @Html.IsActiveControl("HR") @Html.IsActiveControl("HRLibrary")">
            <a href="javascript:void(0);">
                <i style="font-size: 20px" class="ion-ios-people"></i> <span>&nbsp;&nbsp;Human Resources</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("Index", "HRCenter")"><a href="@Url.Action("Index", "HRCenter")"> HR Center</a></li>
                <li class="@Html.IsActiveControl("HR")">
                    <a href="javascript:void(0);"> HR <i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("PTO", "HR")"><a href="@Url.Action("PTO", "HR")"> PTO</a></li>
                        <li class="@Html.IsActive("Trainings", "HR")"><a href="@Url.Action("Trainings", "HR")"> Trainings</a></li>
                        <li class="@Html.IsActive("Reviews", "HR")"><a href="@Url.Action("Reviews", "HR")"> Reviews</a></li>
                        <li class="@Html.IsActive("Licenses", "HR")"><a href="@Url.Action("Licenses", "HR")"> Licenses</a></li>
                        <li class="@Html.IsActive("I9", "HR")"><a href="@Url.Action("I9", "HR")"> I-9</a></li>
                        <li class="@Html.IsActive("FMLALOA", "HR")"><a href="@Url.Action("FMLALOA", "HR")">FMLA/LOA</a></li>
                    </ul>
                </li>
                <li class="@Html.IsActive("Index", "HRLibrary")"><a href="@Url.Action("Index", "HRLibrary")"> Library</a></li>
            </ul>
        </li>

        <li class="treeview @Html.IsActiveControl("Benefits") ">
            <a href="javascript:void(0);">
                <i style="font-size: 20px;" class="ion-ios-pulse-strong"></i> <span>&nbsp;&nbsp;Benefits</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li><a href="javascript:void(0);"> Benefits Center</a></li>
                <li class="@Html.IsActive("MyBenefits", "Benefits")"><a href="@Url.Action("MyBenefits", "Benefits")"> Benefit Plans</a></li>
                <li class="@Html.IsActive("History", "Benefits")"><a href="@Url.Action("History", "Benefits")">History</a></li>
                <li class="@Html.IsActive("ACA", "Benefits")"><a href="@Url.Action("ACA", "Benefits")"> ACA</a></li>
                <li><a href="javascript:void(0);"> Enrollment</a></li>
            </ul>
        </li>
        <li class="treeview @Html.IsActiveControl("QuickLists")  @Html.IsActiveControl("PEOReporting") @Html.IsActiveControl("ExportSolutions")">
            <a href="javascript:void(0);">
                <i class="fa fa-bar-chart"></i> <span>&nbsp;&nbsp;Reporting</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li><a href="javascript:void(0);"> Info Center</a></li>
                <li class="@Html.IsActiveControl("QuickLists")">
                    <a href="@Url.Action("Index", "QuickLists")"> Quick Lists @*<i class="fa fa-angle-left pull-right"></i>*@</a>
                </li>
                <li><a href="javascript:void(0);"> Reports Center</a></li>
                @*<li><a href="javascript:void(0);"> Report Writer</a></li>*@
                <li>@Html.ActionLink("Report Writer", "ReportList", "Reporting")</li>
                <li class="@Html.IsActiveControl("ExportSolutions")"><a href="@Url.Action("Index", "ExportSolutions")">Export Solutions</a></li>
            </ul>
        </li>
        <li>
            <a href="@Url.Action("LogOut", "Home" )">
                <i class="fa fa-sign-out"></i> <span>&nbsp;&nbsp;Logout</span>
            </a>
        </li>
    </ul>
}
else if (GlobalVariables.DNETLevel == DNetAccessLevel.Employee)
{
    <!-- select form  -->
    <div class="sidebar-form">
        @{
    if (GlobalVariables.HasMultiRole)
    {
        <select id="test-select-role" class="form-control">
            <option>-- Select Role --</option>
            @if (GlobalVariables.AvailableRoles != null)
            {
                foreach (Code_Description role in GlobalVariables.AvailableRoles)
                {
                    <option value="@role.Code">@role.Description</option>
                }
            }
        </select>
    }
        }
        <select id="select-level" class="form-control">
            <option>-- Select Level --</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "System"})">System</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "Client"})">Client</option>
            <option value="@Url.Action("SetLevel", "Home", new {l = "Employee"})">Employee</option>
        </select>
    </div>
    <!-- /.select form -->
    <ul class="sidebar-menu">
        <li class="header">MENU</li>
        <li class="treeview @Html.IsActiveControl("DashBoard")">
            <a href="@Url.Action("EmpIndex", "Dashboard")">
                <i class="fa fa-dashboard"></i> <span>&nbsp;Dashboard</span>
            </a>
        </li>
        <li class="treeview @Html.IsActiveControl("Employees") @Html.IsActiveControl("EmployeeSetup")">
            <a href="javascript:void(0);">
                <i style="font-size: 20px" class="ion-ios-person"></i> <span>&nbsp;&nbsp;&nbsp;My Info</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("Index", "Employees")"><a href="@Url.Action("Index", "Employees", new {E=""})"> Info</a></li>
                <li><a href="javascript:void(0);"> Preferences</a></li>
                <li class="@Html.IsActive("Calendar", "EmployeeSetup")"><a href="@Url.Action("Calendar", "EmployeeSetup")"> Calendar</a></li>
                <li><a href="javascript:void(0);"> Org Chart</a></li>
            </ul>
        </li>
        <li class="treeview @Html.IsActiveControl("EmployeePayroll")">
            <a href="javascript:void(0);">
                <i class="fa fa-usd"></i> <span>&nbsp;Payroll</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("CurrentPay", "EmployeePayroll") @Html.IsActive("PayHistory", "EmployeePayroll") @Html.IsActive("TotalComp", "EmployeePayroll") @Html.IsActive("W2", "EmployeePayroll")">
                    <a href="javascript:void(0);"> Payroll Info <i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("CurrentPay", "EmployeePayroll")"><a href="@Url.Action("CurrentPay", "EmployeePayroll")"> Current Pay</a></li>
                        <li class="@Html.IsActive("PayHistory", "EmployeePayroll")"><a href="@Url.Action("PayHistory", "EmployeePayroll")"> Check History</a></li>
                        <li class="@Html.IsActive("TotalComp", "EmployeePayroll")"><a href="@Url.Action("TotalComp", "EmployeePayroll")"> Total Comp</a></li>
                        <li class="@Html.IsActive("W2", "EmployeePayroll")"><a href="@Url.Action("W2", "EmployeePayroll")"> W-2</a></li>
                    </ul>
                </li>
                <li class="@Html.IsActive("TimeSheet", "EmployeePayroll") @Html.IsActive("TimeClock", "EmployeePayroll")">
                    <a href="javascript:void(0);"> Time Entry<i class="fa fa-angle-left pull-right"></i></a>
                    <ul class="treeview-menu">
                        <li class="@Html.IsActive("TimeSheet", "EmployeePayroll")"><a href="@Url.Action("TimeSheet","EmployeePayroll")"> Time Sheet</a></li>
                        <li class="@Html.IsActive("TimeClock", "EmployeePayroll")"><a href="@Url.Action("TimeClock","EmployeePayroll")"> Time Clock</a></li>
                        <li><a href="javascript:void(0);"> Bus Expense</a></li>
                    </ul>
                </li>
            </ul>
        </li>
        <li class="treeview @Html.IsActiveControl("EmployeeHR")">
            <a href="javascript:void(0);">
                <i style="font-size: 20px" class="ion-ios-people"></i> <span>&nbsp;&nbsp;Human Resources</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("EmployeePTO", "EmployeeHR")"><a href="@Url.Action("EmployeePTO", "EmployeeHR")"> PTO</a></li>
                <li class="@Html.IsActive("MyHRItems", "EmployeeHR")"><a href="@Url.Action("MyHRItems", "EmployeeHR")"> My HR Items</a></li>
                <li class="@Html.IsActive("FMLALOA", "EmployeeHR")"><a href="@Url.Action("FMLALOA", "EmployeeHR")"> Leave/FMLA</a></li>
            </ul>
        </li>
        <li class="treeview @Html.IsActiveControl("Benefits")">
            <a href="javascript:void(0);">
                <i style="font-size: 20px;" class="ion-ios-pulse-strong"></i> <span>&nbsp;&nbsp;Benefits</span> <i class="fa fa-angle-left pull-right"></i>
            </a>
            <ul class="treeview-menu">
                <li class="@Html.IsActive("MyBenefits", "Benefits")"><a href="@Url.Action("MyBenefits", "Benefits")"> My Benefits</a></li>
                <li class="@Html.IsActive("Dependents", "Benefits")"><a href="@Url.Action("Dependents", "Benefits")"> Dependents</a></li>
                <li><a href="javascript:void(0);"> Enrollment</a></li>
            </ul>
        </li>
        <li class="treeview">
            <a href="javascript:void(0);">
                <i style="font-size: 20px" class="ion-person-stalker"></i> <span>&nbsp;&nbsp;Library</span>
            </a>
        </li>
        <li>
            <a href="@Url.Action("LogOut", "Home" )">
                <i class="fa fa-sign-out"></i> <span>&nbsp;&nbsp;Logout</span>
            </a>
        </li>
    </ul>
}


