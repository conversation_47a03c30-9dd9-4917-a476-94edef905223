@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core;
@{
    ViewBag.Title = "Benefit Plans";
}
<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-6 col-sm-6">
            <div class="text-right">

            </div>
        </div>
    </div>
</div>

@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div class="table-bottom">
        @(Html.Kendo().Grid<DarwiNet2._0.Data.ClientPlanHDR>()
            .Name("grid")
            .Columns(columns =>
            {
                columns.Bound(e => e.PlanName);
                columns.Bound(e => e.PlanDescription);
                columns.Bound(e => e.PlanType);
                columns.Bound(e => e.BenefitType);
                columns.Bound(e => e.BeginEnrollment).Format("{0:MM/dd/yyyy}");
                columns.Bound(e => e.EndEnrollment).Format("{0:MM/dd/yyyy}");
                columns.Bound(e => e.YEAR1);
                columns.Bound(e => e.ACTIVE);
            })
            .ColumnMenu()
            .Sortable()
            .Pageable()
            .ToolBar(tools => tools.Excel())
            .Excel(e => e.AllPages(true))
            .Excel(excel => excel
                .FileName("Kendo UI Grid Export.xlsx")
                .Filterable(true)
                .ProxyURL(Url.Action("Excel_Export_Save", "QuickLists"))
                )
            .Groupable()
            .Filterable()
            .Scrollable()
            .ClientDetailTemplateId("template")
            .HtmlAttributes(new { style = "height: 647px" })
            .DataSource(dataSource => dataSource
                .Ajax()
                .PageSize(15)
                .Read(read => read.Action("getPlans_Read", "Benefits"))
                )
        )
    </div>

    <!-- Modal -->
    <div class="modal fade" id="plansModal" tabindex="-1" role="dialog" aria-labelledby="plansModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="plansModalLabel">Plan Information</h4>
                </div>
                <div class="modal-body">
                    Generating...
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    @section scripts
    {
        <script id="template" type="text/kendo-tmpl">
            @(Html.Kendo().Grid<DarwiNet2._0.Data.EmployeeAssignedPlan>()
                .Name("grid_#=ID#")
                .Columns(columns =>
                {
                    columns.Bound(o => o.EmployeeID);
                    columns.Bound(o => o.SSN);
                    columns.Bound(o => o.Name);
                    columns.Bound(o => o.Active);
                    columns.Template(@<text> </text>)
                        .Title("View")
                        .ClientTemplate(
                            "<div class='table-icons icon-center'>" +
                            "<button style='background-color: transparent' data-gohere='" +
                            Url.Action("getEEDetails", "Benefits", new { pn = "#= PlanName#", y = "#= YEAR1#", e = "\\#=EmployeeID\\#" }) +
                            "'title='Details' onclick='showDetails(this, event);' data-remote='false'><i class='icon-blue fa fa-eye fa-fw fa-lg' title='Details'></i></button>" +
                            "</div>")
                        .Width(75)
                        .HeaderHtmlAttributes(new { @class = "header-center" });

                })
                .DataSource(dataSource => dataSource
                    .Ajax()
                    .PageSize(10)
                        .Read(read => read.Action("getEEPlans_Read", "Benefits", new { id = "#=ID#" }))
                    )
                .Pageable()
                .Sortable()
                .ToClientTemplate()
            )
        </script>
        <script>
            function showDetails(identifier, e) {
                var link = $(identifier).attr("data-gohere");
                $('#plansModal').modal('show');
                $('#plansModal').find(".modal-body").load(encodeURI(link));
                e.preventDefault();
            };
        </script>
    }
}