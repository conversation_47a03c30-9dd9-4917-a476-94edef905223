@using DarwiNet2._0.Core;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Extensions;
@using DataDrivenViewEngine.Models.Core;

<style type="text/css" scoped>
    .deal-athlete-payments {
        position: relative;
    }

    .deal-athlete-payments .my-table {
        min-width: 1000px !important;
    }

    .deal-athlete-payments .my-table th {
        padding-left: 0px;
    }

    .deal-athlete-payments .my-table td {
        padding: 5px 10px 5px 0px;
        vertical-align: middle;
    }

    .btn {
        outline: none !important;
    }

    @@keyframes spinner {
        to {
            transform: rotate(360deg);
        }
    }

    .fa-spinner {
        animation: spinner 1s linear infinite;
    }

    .btn-table-action {
        position: absolute;
        top: 2px;
        right: 12px;
        padding: 5px 10px;
        min-width: 84px;
        z-index: 10;
    }

    .deal-athlete-payment-actions {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 34px;
        min-width: 90px;
        padding-left: 10px;
    }

    .deal-athlete-payment-actions .btn {
        padding: 5px 10px;
        min-width: 84px;
    }

    .deal-athlete-payment-actions i {
        cursor: pointer;
    }

    .deal-athlete-payment-actions i:hover {
        text-shadow: 1px 1px 0 rgba(0,0,0,.15), 2px 2px 2px rgba(0,0,0,.25);
    }

    .fa-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
    }

    .deal-athlete-payment-expandable {
        margin-top: 10px;
        padding: 0 150px;
    }

    .deal-athlete-payments #Actions {
        position: sticky;
        right: 0;
        z-index: 2;
    }
</style>
<script src="~/Scripts/ThinkWareScripts/DataUtilities.js"></script>

@Html.VueComponent("~/Shared/thinkware-vue-table")
@Html.VueComponent("~/Deals/deal-athlete-deliverables")
@Html.VueComponent("~/Deals/deal-athlete-payments-status-stepper")

<script type="text/x-template" id="deal-athlete-payments-template">
    <div>
        <thinkware-vue-table class="deal-athlete-payments" :configuration="configuration" ref="dealAthletePaymentTable">
            <template v-slot:table-actions="table">
                <button v-if="hasPayReadyAthletes(table.data)" type="button" class="btn btn-success btn-table-action" title="Pay All Eligible Athletes" @@click="dealAthletePayment = null;isVisibleDealAthletePaymentModal = true">Pay</button>
            </template>
            <template v-slot:expandable="row">
                <div class="deal-athlete-payment-expandable">
                    <deal-athlete-deliverables :key="dealAthleteDeliverablesKey" :deal-athlete-payment="toCamelCase(row.data)" :deal-activity-types="dealActivityTypes" @@deal-athlete-deliverables-changed="getDealAthleteDeliverableCompletionState(row.data)" />
                </div>
            </template>
            <template v-if="isVisibleStepper" v-slot:stepper="row">
                <deal-athlete-payments-status-stepper :deal-athlete-payment="toCamelCase(row.data)" :deal-athlete-payment-statuses="dealAthletePaymentStatuses" />
            </template>
            <template v-slot:athlete="row">
                {{ row.data.Employee.FullName }}
            </template>
            <template v-slot:status="row">
                {{ getStatusText(row.data) }}
            </template>
            <template v-slot:actions="row">
                <div class="deal-athlete-payment-actions">
                    <button type="button" v-if="actions.map(a => a.code).includes(row.data.Status)" class="btn btn-success" :disabled="processingId === row.data.EmployeeID" @@click="changeDealAthletePaymentStatus(row.data)">
                        <template v-if="processingId !== row.data.EmployeeID">{{ getStatusActionText(row.data) }}</template>
                        <i v-else class="fa fa-spinner"></i>
                    </button>
                </div>
            </template>
        </thinkware-vue-table>
        <thinkware-vue-modal title="Payment" width="40%" :active="isVisibleDealAthletePaymentModal" @@close-modal="isVisibleDealAthletePaymentModal = false">
            <deal-athlete-payment-modal v-if="isVisibleDealAthletePaymentModal"
                :deal="deal" 
                :athlete-payment="dealAthletePayment"
                @@saved="isVisibleDealAthletePaymentModal = false;dealAthleteDeliverableCompletionStates = [];$refs.dealAthletePaymentTable.reloadTable();"
                @@close="isVisibleDealAthletePaymentModal = false"
            />
        </thinkware-vue-modal>
    </div>
</script>

@Html.VueComponent("~/Shared/thinkware-vue-modal")
@Html.VueComponent("~/Shared/thinkware-vue-table")
@Html.VueComponent("~/Deals/deal-athlete-payment-modal")

<script type="text/javascript" id="deal-athlete-payments">
    var DealAthletePaymentsComponent = VueComponent('deal-athlete-payments', {
        components: [
            ThinkwareVueTableComponent,
            ThinkwareVueModalComponent,
            DealAthleteDeliverablesComponent,
            DealAthletePaymentModalComponent,
            DealAthletePaymentsStatusStepperComponent
        ],
        props: {
            deal: { type: Object, required: true, default: () => ({}) },
            dealActivityTypes: { type: Array, default: () => ([]) },
            dealAthletePaymentStatuses: { type: Array, required: true, default: () => ([]) },
            isExpandable: { type: Boolean, default: false },
            isVisiblePaymentInfo: { type: Boolean, default: false },
            isVisibleStepper: { type: Boolean, default: true }
        },
        data: function () {
            return {
                actions: [],
                configuration: {
                    Options: {
                        TableId: @FilterTableConstants.DealAthletePayments,
                        DataUri: '@Url.Action("GetDealAthletePaymentsTableData", "DealAthletePayments")' + '?companyID=' + this.deal.companyID + '&clientID=' + this.deal.clientID + '&dealID=' + this.deal.dealID,
                        SavedState: false,
                        Filterable: false,
                        DynamicFilter: false,
                        HideFilterControls: true,
                        HtmlRef: "dealAthletePaymentTable",
                        Classes: "table-hover",
                        Groupable: {
                            Active: false
                        },
                        Sortable: {
                            Active: false
                        },
                        Callable: {
                            Active: true,
                        },
                        Selectable: {
                            Active: false
                        },
                        Expandable: {
                            Active: this.isExpandable,
                            Align: "left"
                        },
                        Scrollable: {
                            Active: false,
                        },
                        Searchable: {
                            Active: false,
                            Config: {}
                        },
                        Totals: {
                            Active: false,
                        }
                    },
                    Columns: [
                        {
                            Label: "Athlete",
                            Code: "Employee",
                            Render: "template",
                            FilterType: "NonSelect",
                            ActiveFilters: []
                        },
                        {
                            Label: "Status",
                            Code: "Status",
                            Render: "template",
                            Width: "15%",
                            FilterType: "Select",
                            ActiveFilters: []
                        },
                        {
                            Label: "Deal Amount",
                            Code: "DealAmount",
                            Render: "currency",
                            Width: "15%",
                            FilterType: "NonSelect",
                            ActiveFilters: []
                        },
                        {
                            Label: "Actions!",
                            Render: "template",
                            Width: "105px",
                            FilterType: "NonSelect",
                            ActiveFilters: []
                        }
                    ]
                },
                dealAthleteDeliverableCompletionStates: [],
                dealAthleteDeliverablesKey: 1,
                dealAthletePayment: null,
                isVisibleDealAthletePaymentModal: false,
                processingId: null
            }
        },
        created: function () {
            if (this.isVisibleStepper) {
                this.configuration.Columns.splice(0, 0,
                    {
                        Label: "Stepper!",
                        Code: "Stepper",
                        Render: "template",
                        Width: "440px",
                        FilterType: "NonSelect",
                        ActiveFilters: []
                    }
                );
            }
            if (this.isVisiblePaymentInfo) {
                this.configuration.Columns.splice(this.configuration.Columns.length - 1, 0,
                    {
                        Label: "Payment Amount",
                        Code: "PaymentAmount",
                        Render: "currency",
                        Width: "15%",
                        FilterType: "NonSelect",
                        ActiveFilters: []
                    },
                    {
                        Label: "Payment Date",
                        Code: "PaymentDate",
                        Render: "date",
                        Width: "15%",
                        FilterType: "NonSelect",
                        ActiveFilters: []
                    }
                );
            }
            this.actions = this.getStatusActions();
        },
        computed: {
            hasPayReadyAthletes: function () {
                return (dealAthletePayments) => {
                    return dealAthletePayments.some(dealAthletePayment => dealAthletePayment.Status === @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()) && !this.hasIncompleteDeliverables(dealAthletePayment));
                }
            },
            hasIncompleteDeliverables: function() {
                return (dealAthletePayment) => {
                    const dealAthleteDeliverableCompletionState = this.dealAthleteDeliverableCompletionStates.find(x => x.employeeID === dealAthletePayment.EmployeeID);
                    return dealAthleteDeliverableCompletionState ? dealAthleteDeliverableCompletionState.hasIncompleteDeliverables : Boolean(dealAthletePayment.HasIncompleteDeliverables);
                }
            }
        },
        methods: {
            changeDealAthletePaymentStatus: function (dealAthletePayment) {
                if (this.processingId === dealAthletePayment.EmployeeID) return;
                const index = this.actions.findIndex(s => s.code === dealAthletePayment.Status) ?? -1;
                const nextStatus = index > -1 && index < this.actions.length ? this.actions[index].nextStatus : null;
                if (!nextStatus) return;
                if (nextStatus === @Html.Raw(DealAthletePaymentStatus.PaymentPending.ToJson())) {
                    if (this.hasIncompleteDeliverables(dealAthletePayment)) { // complete deliverables
                        this.completeDealAthleteDeliverables(dealAthletePayment);
                    } else { // show Payment dialog
                        this.dealAthletePayment = toCamelCase(dealAthletePayment);
                        this.isVisibleDealAthletePaymentModal = true;
                    }
                    return;
                }
                this.processingId = dealAthletePayment.EmployeeID;
                var self = this;
                ThinkwareCommon.ajax.postJson('@Url.Action("UpdateDealAthletePaymentStatus", "DealAthletePayments")', {
                    data: {
                        companyID: dealAthletePayment.CompanyID,
                        clientID: dealAthletePayment.ClientID,
                        dealID: dealAthletePayment.DealID,
                        employeeID: dealAthletePayment.EmployeeID,
                        status: nextStatus
                    },
                    onSuccess: function (response) {
                        if (response.status === 'Success') {
                            dealAthletePayment.Status = response.data.dealAthletePayment.Status;
                            self.$emit("deal-changed", self.toCamelCase(response.data.deal));
                        }
                    },
                    onError: function (error) {
                        ThinkwareCommon.showErrorAlert(error);
                    },
                    onComplete: function () {
                        self.processingId = null;
                    }
                });
            },
            completeDealAthleteDeliverables: function (dealAthletePayment) {
                this.processingId = dealAthletePayment.EmployeeID;
                var self = this;
                ThinkwareCommon.ajax.postJson('@Url.Action("CompleteDealAthleteDeliverables", "DealAthleteDeliverables")', {
                    data: {
                        companyID: dealAthletePayment.CompanyID,
                        clientID: dealAthletePayment.ClientID,
                        dealID: dealAthletePayment.DealID,
                        employeeID: dealAthletePayment.EmployeeID
                    },
                    onSuccess: function (response) {
                        if (response.status === 'Success') {
                            self.dealAthleteDeliverableCompletionStates = [];
                            self.$refs.dealAthletePaymentTable.reloadTable();
                            self.dealAthleteDeliverablesKey++;
                        }
                    },
                    onError: function (error) {
                        ThinkwareCommon.showErrorAlert(error);
                    },
                    onComplete: function () {
                        self.processingId = null;
                    }
                });
            },
            getDealAthleteDeliverableCompletionState: function (dealAthletePayment) {
                var self = this;
                ThinkwareCommon.ajax.getJson(`@Url.Action("HasIncompleteDeliverables", "DealAthleteDeliverables")?companyId=${dealAthletePayment.CompanyID}&clientId=${dealAthletePayment.ClientID}&dealId=${dealAthletePayment.DealID}&employeeId=${dealAthletePayment.EmployeeID}`, {
                    onSuccess: function (data) {
                        let dealAthleteDeliverableCompletionState = self.dealAthleteDeliverableCompletionStates.find(x => x.employeeID === dealAthletePayment.EmployeeID);
                        if (dealAthleteDeliverableCompletionState) dealAthleteDeliverableCompletionState.hasIncompleteDeliverables = data
                        else self.dealAthleteDeliverableCompletionStates.push({ employeeID: dealAthletePayment.EmployeeID, hasIncompleteDeliverables: data });
                    },
                    onError: function (error) {
                        ThinkwareCommon.showErrorAlert(error);
                    },
                    onComplete: function () {
                    }
                });
            },
            getStatusActions: function () {
                const getText = function (statusCode) {
                    switch (statusCode) {
                        case @Html.Raw(DealAthletePaymentStatus.Offered.ToJson()): return "Assign";
                        case @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()): return "Pay";
                        case @Html.Raw(DealAthletePaymentStatus.PaymentCompleted.ToJson()): return "Disclose";
                        case @Html.Raw(DealAthletePaymentStatus.PaymentDisclosed.ToJson()): return "Finalize";
                        default: return null;
                    }
                };
                const getNextStatus = function (statusCode) {
                    switch (statusCode) {
                        case @Html.Raw(DealAthletePaymentStatus.Offered.ToJson()): return @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson());
                        case @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()): return @Html.Raw(DealAthletePaymentStatus.PaymentPending.ToJson());
                        case @Html.Raw(DealAthletePaymentStatus.PaymentCompleted.ToJson()): return @Html.Raw(DealAthletePaymentStatus.PaymentDisclosed.ToJson());
                        case @Html.Raw(DealAthletePaymentStatus.PaymentDisclosed.ToJson()): return @Html.Raw(DealAthletePaymentStatus.Finalized.ToJson());
                        default: return null;
                    }
                };
                return this.dealAthletePaymentStatuses
                    .filter(status => [
                        @Html.Raw(DealAthletePaymentStatus.Offered.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.PaymentCompleted.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.PaymentDisclosed.ToJson())
                    ].includes(status.code))
                    .map(status => { return { code: status.code, text: getText(status.code), nextStatus: getNextStatus(status.code) } });
            },
            getStatusActionText: function (dealAthletePayment) {
                return dealAthletePayment.Status === @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()) && this.hasIncompleteDeliverables(dealAthletePayment)
                    ? "Complete"
                    : this.actions.find(action => action.code === dealAthletePayment.Status)?.text || null;
            },
            getStatusText: function (dealAthletePayment) {
                return dealAthletePayment.Status === @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()) && !this.hasIncompleteDeliverables(dealAthletePayment)
                ? "Completed"
                : this.dealAthletePaymentStatuses.find(status => status.code === dealAthletePayment.Status)?.text || null;
            },
            toCamelCase: function (dealAthletePayment) {
                return toCamelCase(dealAthletePayment);
            }
        }
    });
</script>
