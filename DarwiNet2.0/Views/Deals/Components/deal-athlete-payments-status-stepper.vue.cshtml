@using DarwiNet2._0.Extensions;
@using DataDrivenViewEngine.Models.Core;

<style type="text/css" scoped>
    .step-table {
        position: relative;
        border-collapse: collapse;
        color: transparent;
        white-space: nowrap;
        margin-left: 10px;
    }

    .step {
        padding: 0px !important;
        min-width: 50px;
        background-color: #b4b3ac;
    }

    .step-button {
        margin: 0px 8px;
        text-decoration: none;
        color: #fff;
        font-weight: 400;
        cursor: pointer;
    }

    .step-name {
        margin: 0px 8px;
        text-decoration: none;
        color: #fff;
        font-weight: 400;
    }

    .step-spacer {
        padding: 0px !important;
        padding-right: 2px !important;
    }

    .step-arrow {
        border-top: 18px solid transparent;
        border-bottom: 18px solid transparent;
        border-left: 18px solid var(--left-color);
        height: 0px;
        width: 0px;
        position: relative;
    }

    .step-arrow:after {
        content: "";
        border-top: 18px solid var(--right-color);
        border-bottom: 18px solid var(--right-color);
        border-left: 18px solid transparent;
        height: 0px;
        width: 0px;
        position: absolute;
        margin-left: -15px;
        margin-top: -18px;
    }

    .step-end {
        padding: 0px !important;
    }

    .step-arrow-end {
        border-top: 18px solid transparent;
        border-bottom: 18px solid transparent;
        border-left: 18px solid;
    }

    .step-canceled {
        background-color: #d9534f;
    }

    .step-complete {
        background-color: #005ced;
    }

    .step-next {
        background-color: #5cb85c;
    }
</style>

<script type="text/x-template" id="deal-athlete-payments-status-stepper-template">
    <table class="step-table">
        <tr>
            <template v-for="(step, index) in virtualSteps">
                <td class="step" v-bind:class="{ 'step-canceled': canceledStep(step.code), 'step-complete': completeStep(step.code), 'step-next': nextStep(step.code) }">
                    @*<a class="step-button" tabindex="0" @@click.stop="changeAthleteStatus(step.code)">{{ step.text }}</a>*@
                    <div class="step-name">{{ step.text }}</div>
                </td>
                <td v-if="index < virtualSteps?.length - 1" class="step-spacer">
                    <div class="step-arrow" :style="{'--left-color': arrowColor(step.code), '--right-color': arrowColor(virtualSteps[index+1].code)}"></div>
                </td>
                <td v-else class="step-end">
                    <div class="step-arrow-end" :style="{ borderLeftColor: arrowColor(step.code) }"></div>
                </td>
            </template>
        </tr>
    </table>
</script>

<script type="text/javascript" id="deal-athlete-payments-status-stepper">
    var DealAthletePaymentsStatusStepperComponent = VueComponent('deal-athlete-payments-status-stepper', {
        props: {
            dealAthletePayment: {
                type: Object,
                required: true,
                default: () => ({}),
            },
            dealAthletePaymentStatuses: {
                type: Array,
                required: true,
                default: () => ([])
            }
        },
        data: function () {
            return {
                allSteps: []
            }
        },
        mounted: function () {
            this.allSteps = this.getSteps();
        },
        computed: {
            arrowColor: function () {
                return (status) => {
                    return this.nextStep(status) ? "#5cb85c" : this.canceledStep(status) ? "#d9534f" : this.completeStep(status) ? "#005ced" : "#b4b3ac";
                }
            },
            canceledStep: function () {
                return (status) => {
                    return this.dealAthletePayment.status === status && status == @Html.Raw(DealAthletePaymentStatus.Canceled.ToJson());
                }
            },
            completeStep: function () {
                return (status) => {
                    return this.dealAthletePayment.status >= status && !this.canceledStep(status);
                }
            },
            nextStep: function () {
                return (status) => {
                    let index = this.allSteps.findIndex(step => step.code === this.dealAthletePayment.status) + 1;
                    const nextStep = index > 0 && index < this.allSteps?.length ? this.allSteps[index].code : null;
                    index = this.virtualSteps.findIndex(step => step.code === this.dealAthletePayment.status) + 1;
                    const nextVirtualStep = index > 0 && index < this.virtualSteps?.length ? this.virtualSteps[index].code : null;
                    return nextStep === nextVirtualStep && nextStep === status;
                }
            },
            virtualSteps: function () {
                let allSteps = this.dealAthletePayment.status >= @Html.Raw(DealAthletePaymentStatus.PaymentCompleted.ToJson())
                    ? this.allSteps.filter(step => step.code !== @Html.Raw(DealAthletePaymentStatus.PaymentPending.ToJson()))
                    : this.allSteps.filter(step => step.code !== @Html.Raw(DealAthletePaymentStatus.PaymentCompleted.ToJson()));
                return this.dealAthletePayment.status === @Html.Raw(DealAthletePaymentStatus.Canceled.ToJson())
                    ? allSteps.filter(step => step.code !== @Html.Raw(DealAthletePaymentStatus.Finalized.ToJson()))
                    : allSteps.filter(step => step.code !== @Html.Raw(DealAthletePaymentStatus.Canceled.ToJson()));
            }
        },
        methods: {
            changeAthleteStatus(statusCode) {
                if (this.dealAthletePayment.status === statusCode) return;
                this.$emit("change-athlete-status", statusCode);
            },
            getSteps: function () {
                const getText = function (statusCode) {
                    switch (statusCode) {
                        case @Html.Raw(DealAthletePaymentStatus.Offered.ToJson()): return "Offer";
                        case @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()): return "Assign";
                        case @Html.Raw(DealAthletePaymentStatus.PaymentPending.ToJson()): return "Pay";
                        case @Html.Raw(DealAthletePaymentStatus.PaymentCompleted.ToJson()): return "Pay";
                        case @Html.Raw(DealAthletePaymentStatus.PaymentDisclosed.ToJson()): return "Disclose";
                        case @Html.Raw(DealAthletePaymentStatus.Finalized.ToJson()): return "Finalize";
                        case @Html.Raw(DealAthletePaymentStatus.Canceled.ToJson()): return "Finalize";
                        default: return null;
                    }
                };
                let steps = this.dealAthletePaymentStatuses
                    .filter(status => [
                        @Html.Raw(DealAthletePaymentStatus.Offered.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.Assigned.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.PaymentPending.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.PaymentCompleted.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.PaymentDisclosed.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.Finalized.ToJson()),
                        @Html.Raw(DealAthletePaymentStatus.Canceled.ToJson())
                    ].includes(status.code))
                    .map(status => { return { code: status.code, value: status.text, text: getText(status.code) } });
                return steps;
            }
        }
    });
</script>
