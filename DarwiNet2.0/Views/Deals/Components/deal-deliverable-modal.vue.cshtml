@using DarwiNet2._0.Core;
@using DarwiNet2._0.Extensions;

<script type="text/x-template" id="deal-deliverable-modal-template">
    <div>
        <div class="panel panel-thinkware">
            <div class="panel-body">
                <form>
                    <div class="flex-column">
                        <div class="form-group" :class="{ 'has-error': errors && errors.name }">
                            <label class="col-md-4" for="name">Deliverable</label>
                            <div class="col-md-8">
                                <input type="text" id="name" class="form-control" maxlength="50" required v-model="dealDeliverable.name" />
                                <span v-if="errors && errors.name" class="text-danger">{{ errors.name }}</span>
                            </div>
                        </div>
                        <div class="form-group" :class="{ 'has-error': errors && errors.activityType }">
                            <label class="col-md-4" for="activity-type">Activity Type</label>
                            <div class="col-md-8">
                                <select id="activity-type" class="form-control" required v-model="dealDeliverable.activityType">
                                    <option v-for="activityType in dealActivityTypes" :value="activityType.code">{{ activityType.description }}</option>
                                </select>
                                <span v-if="errors && errors.activityType" class="text-danger">{{ errors.activityType }}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4" style="white-space: nowrap; margin: -1px 0px 0px 0px" for="required">Required for Payment</label>
                            <div class="col-md-8">
                                <div class="custom-toggle-button" @@click="dealDeliverable.required = !dealDeliverable.required">
                                    <i class="fa fa-lg" :class="dealDeliverable.required ? 'fa-toggle-on active-toggle-color' : 'fa-toggle-off'"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4" for="description">Description</label>
                            <div class="col-md-8">
                                <textarea class="form-control fill-border-box" id="description" rows="8" maxlength="255" placeholder="(255 character max)" v-model="dealDeliverable.description"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="row pull-right margin-0">
            <button class="btn btn-danger" @@click="$emit('close')">Cancel</button>
            <button class="btn btn-success" @@click="saveDealDeliverable">Save</button>
        </div>
    </div>
</script>

<script type="text/javascript" id="deal-deliverable-modal-script">
    var DealDeliverableModalComponent = VueComponent('deal-deliverable-modal', {
        components: [],
        props: {
            deal: {
                type: Object,
                default: () => { }
            },
            deliverable: {
                type: Object,
                default: () => { }
            },
            dealActivityTypes: {
                type: Array,
                default: () => ([])
            }
        },
        data: function () {
            return {
                dealDeliverable: {},
                errors: null
            }
        },
        mounted: function () {
            this.initDealDeliverable();
        },
        computed: {

        },
        methods: {
            initDealDeliverable: function () {
                this.dealDeliverable = {
                    companyID: this.deliverable?.companyID || this.deal.companyID,
                    clientID: this.deliverable?.clientID || this.deal.clientID,
                    dealID: this.deliverable?.dealID || this.deal.dealID,
                    deliverableID: this.deliverable?.deliverableID || null,
                    name: this.deliverable?.name || null,
                    activityType: this.deliverable?.activityType || null,
                    description: this.deliverable?.description || null,
                    required: this.deliverable?.required || false,
                    completed: this.deliverable?.completed || false
                };
            },
            saveDealDeliverable: function () {
                this.errors = null;
                var self = this;
                ThinkwareCommon.ajax.postJson('@Url.Action("SaveDealDeliverable", "Deals")', {
                    data: {
                        dealDeliverable: self.dealDeliverable
                    },
                    onSuccess: function (response) {
                        if (response.status != ResponseStatus.SUCCESS) {
                            self.errors = response.data;
                            return;
                        }
                        self.$emit('saved');
                        self.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                        self.$toastr.defaultPosition = "toast-bottom-right";
                        self.$toastr.s("Deliverable was successfully " + (self.dealDeliverable.deliverableID ? "updated" : "added") + ".");
                    },
                    onError: function (error) {
                        self.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                        self.$toastr.defaultPosition = "toast-bottom-right";
                        self.$toastr.e(error);
                    }
                });
            }
        },
        watch: {
            deliverable: {
                handler() {
                    this.initDealDeliverable();
                },
                deep: true
            }
        }
    });
</script>

