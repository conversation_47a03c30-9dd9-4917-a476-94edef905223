@{
    Layout = "~/Views/Shared/_LayoutNoWrap.cshtml";
}

@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;
@{

    ViewBag.Title = "SubmitPDF";
    bool isMobile = (ViewBag.Mobile != null) ? ViewBag.Mobile : false;
}
<div class="col-md-12">

    <h2>Document Verification</h2>
    @if (ViewBag.CanContinue != null)
    {
        <script>
            $(document).ready(function () {
                if (window.parent.location != window.top) {
                    var redirectURL = "@Url.Action("ContinueProcess", "OBProcess")";
                    window.parent.location.href = redirectURL;
                }
            });

        </script>
        <div style="padding-top: 10px;">Congratulations! That Document is completed. You should be auto-redirected to a read-only copy of the pdf and have a 'Continue' button. </div>
    }

    @if (ViewBag.VerificationText != String.Empty && ViewBag.CanContinue == null)
    {
        <div style="padding-bottom: 10px;">@Html.Raw(ViewBag.VerificationText)</div>
        if (!ViewBag.VerificationOnly)
        {
            if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
            {
                if (ViewBag.IsReadyToSign)
                {
                    using (Html.BeginForm("VerifySign", "OBProcess", new { id = ViewBag.DocId }, FormMethod.Post, new { id = "obclientsetupedit" }))
                    {
                        <div class="form-horizontal">
                            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                            <div class="form-group">
                                @Html.Label("User ID", new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @Html.TextBox("userID", "", new { @class = "field-required form-control", @required = "required", @maxlength = "30" })
                                </div>
                                @Html.Label("Password", new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @Html.Password("pwd", "", new { @class = "field-required form-control", @required = "required", @maxlength = "30" })
                                </div>
                                @Html.Hidden("ssn", "", new { @class = "field-required form-control", @maxlength = "30" })
                            </div>
                            <div class="form-group">
                                <div class="col-md-12">
                                    <div class="pull-right">
                                        <a href="@Url.Action("ShowForm", "OBProcess", new {docId = ViewBag.DocId, url = Url.Action("SubmitPDF", "OBProcess", new {docId = ViewBag.DocId}, Request.Url.Scheme)})" class="btn btn-thinkware back-to-document">Back to Document</a>
                                        <input type="submit" value="Verify & Sign" class="btn btn-success" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div style="padding-top: 10px;">Warning! This Document must be completed by the employee first. </div>
                }
            }
            else
            {
                using (Html.BeginForm("VerifySign", "OBProcess", new { id = ViewBag.DocId }, FormMethod.Post, new { id = "obclientsetupedit" }))
                {

                    <div class="form-horizontal">
                        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                        <div class="form-group">
                            @Html.Label("User ID", new { @class = "control-label col-md-2" })
                            <div class="col-md-4">
                                @Html.TextBox("userID", "", new { @class = "field-required form-control", @required = "required", @maxlength = "30" })
                            </div>
                            @Html.Label("Password", new { @class = "control-label col-md-2" })
                            <div class="col-md-4">
                                @Html.Password("pwd", "", new { @class = "field-required form-control", @required = "required", @maxlength = "30" })
                            </div>
                        </div>
                        <div class="form-group">

                            @if (!isMobile)
                            {
                                @Html.Label("SSN", new { @class = "control-label col-md-2" })
                                <div class="col-md-4">
                                    @if (GlobalVariables.MaskSSN)
                                    {
                                        @Html.Password("ssn", "", new { @class = "field-required form-control", @required = "required", @maxlength = "11" })
                                    }
                                    else
                                    {
                                        @Html.TextBox("ssn", "", new { @class = "field-required form-control", @required = "required", @maxlength = "11" })
                                    }

                                </div>
                            }
                            else
                            {
                                <div class="col-md-2 col-xs-12">
                                    @Html.Label("SSN", new { @class = "control-label" })
                                </div>
                                <div class="col-xs-4">
                                    <input type="number" id="ssn1" class="field-required form-control" max="999" min="0" placeholder="XXX" required="required" />
                                </div>
                                <div class="col-xs-4">
                                    <input type="number" id="ssn2" class="field-required form-control" max="99" min="0" placeholder="XX" required="required" />
                                </div>
                                <div class="col-xs-4">
                                    <input type="number" id="ssn3" class="field-required form-control" max="9999" min="0" placeholder="XXXX" required="required" />
                                </div>
                                <input type="hidden" name="ssn" id="ssn" />

                            }

                        </div>
                        <div class="form-group">
                            <div class="col-md-12">
                                <div class="pull-right">
                                    <a href="@Url.Action("ShowForm", "OBProcess", new {docId = ViewBag.DocId, url = Url.Action("SubmitPDF", "OBProcess", new {docId = ViewBag.DocId}, Request.Url.Scheme)})" class="btn btn-thinkware back-to-document">Back to Document</a>
                                    <input type="submit" value="Verify & Sign" class="btn btn-success" />
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
        }
        else
        {
            using (Html.BeginForm("VerifySign", "OBProcess", new { id = ViewBag.DocId }, FormMethod.Post, new { id = "obclientsetupedit" }))
            {

                <div class="form-horizontal">
                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="form-group">
                        @*@Html.Label("User ID", new { @class = "control-label col-md-2" })*@
                        <div class="col-md-6">
                            @Html.CheckBox("chkVeri", false, new { @class = "field-required", @required = "required", })&nbsp;
                            I verify that I have read and understand the above agreement.

                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-12">
                            <div class="pull-right">
                                <a href="@Url.Action("ShowForm", "OBProcess", new {docId = ViewBag.DocId, url = Url.Action("SubmitPDF", "OBProcess", new {docId = ViewBag.DocId}, Request.Url.Scheme)})" class="btn btn-thinkware">Back to Document</a>
                                <input type="submit" value="Verify" class="btn btn-thinkware" />
                            </div>
                        </div>
                    </div>
                </div>


                @*<div>
                        <input type="checkbox" id="chkVeri" name="chkVeri" required />
                        <input type="submit" value="Verify" />
                    </div>*@
            }
        }
    }
</div>
<script>
    $('.back-to-document').click(function () {
        window.parent.$('#externalSign').show();
    });
</script>
<script src="~/Scripts/MaskedInput.js"></script>
<script>
    $("#ssn").mask("***********", { placeholder: " " });

</script>
@if (isMobile)
{
    <script>
        $('#ssn1').on("input", function () {
            if (this.value.length > 3)
                this.value = this.value.slice(0, 3);
        });
        $('#ssn2').on("input", function () {
            if (this.value.length > 2)
                this.value = this.value.slice(0, 2);
        });
        $('#ssn3').on("input", function () {
            if (this.value.length > 4)
                this.value = this.value.slice(0, 4);
        });
    </script>
}
@if (!GlobalVariables.MaskSSN && !isMobile)
{
    <script>
        $('#ssn').on('blur', function () {
            $('#ssn').attr({ 'type': 'password' });
        });
        $('#ssn').on('focus', function () {
            $('#ssn').attr({ 'type': 'text' });
        });
    </script>


}
else if (!isMobile)
{
    <script>
        $('#ssn').on('blur', function () {
            $('#ssn').attr({ 'type': 'password' });
        });
        $('#ssn').on('focus', function () {
            $('#ssn').attr({ 'type': 'text' });
            $('#ssn').val('');
        });
    </script>

}
<script>
    $("input[type=submit]").click(function () {
        $("#obclientsetupedit").submit(function () {
            var mobile = '@isMobile';
            if (mobile == "False") {
            $('#ssn').mask('999999999');
            } else {
                var ssn1 = $('#ssn1').val();
                var ssn2 = $('#ssn2').val();
                var ssn3 = $('#ssn3').val();
                $('#ssn').val(ssn1 + ssn2 + ssn3);
            }
        });
    });


</script>