@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;

@*@model IEnumerable<DarwiNet2._0.Models.OBEEPaycodes>*@

@{
    Layout = "";
    ViewBag.Title = "State Tax";
    var Updatable = (ViewBag.FinalizeStatus != 2) ? "1" : "0";
}
<link href="~/Content/sweetalert.css" rel="stylesheet" />

<div class="toolbar">

    <div class="row">
        <div class="col-md-4 pull-left">
            <div class="input-group">
                <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                <input type="text" class="form-control" id='FieldFilter' placeholder="Search State Name">
            </div>
        </div>
        <!-- Button trigger modal -->
        @if (Updatable == "1")
        {
            if (!ViewBag.AtLeastOne)
            {
                <p class="create-pad pull-right">
                    <a href="@Url.Action("RefreshStateCodes", new {id = 0, tid = ViewBag.TaskID, a = "StateTaxIndex"})" title="Refresh State Codes" class="btn btn-thinkware"><i class="fa fa-fw fa-refresh fa-lg"></i>Refresh State Codes</a>
                </p>
            }
            <p class="create-pad pull-right">
                <a href="#" title="Add State Tax" data-toggle="modal" data-target="#addCodes" class="btn btn-thinkware"><i class="fa fa-fw fa-plus fa-lg"></i>Add State Tax</a>
            </p>
        }

    </div>
</div>
<div class="table-bottom table-icons">
    @if (Updatable == "1")
    {
        <div id="state" class="dynamic-process">
            @(Html.Kendo().Grid<DarwiNet2._0.Models.OBEEStateTax>()
                  .Name("grid")
                  .Columns(columns =>
                  {
                      columns.Bound(p => p.State).Title(FieldTranslation.GetLabel("State", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("State", GlobalVariables.LanguageID) }).Width(80);
                      columns.Bound(p => p.EstimatedAmount).Title(FieldTranslation.GetLabel("EstimatedAmount", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EstimatedAmount", GlobalVariables.LanguageID) }).Width(160);
                      columns.Bound(p => p.AdditionalAmount).Title(FieldTranslation.GetLabel("AdditionalAmount", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("AdditionalAmount", GlobalVariables.LanguageID) }).Width(165);
                      columns.Bound(p => p.Dependents).Title(FieldTranslation.GetLabel("Dependents", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Dependents", GlobalVariables.LanguageID) }).Width(120);
                     // columns.Bound(p => p.DocId ).Title(FieldTranslation.GetLabel("Dependents", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Dependents", GlobalVariables.LanguageID) }).Width(120);
                      //columns.Bound(p => p.Completed).Title("Status").HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Status", GlobalVariables.LanguageID) }).Width(100);
                      columns.Template(@<text>
                    </text>)
                          .Title("Status")
                          .ClientTemplate(
                              "# " +
                              "if(Completed){# <div>Completed</div>#} else {#<div>Not Completed</div>#} " +
                               "#"
                          ).Width(200);
                      columns.Template(@<text>
                                        </text>)
                          .Title("")
                          .ClientTemplate(
                              "# " +
                              "if(DocId == 0)" +
                              "{#" +
                                  @*The state has no available forms*@
                                  "<div class='icon-center table-icons'>No Forms Available</div>" +
                              "#}" +
                              "else if(!Completed || DocId == 0) " +
                              "{#" +
                                  "#if(DocId>0){#" +
                                      "<div class='icon-center table-icons'>" +
                                      "<a href='" + @Url.Action("ProcessDocWebForm", "OBProcess", new {id = "#=DocId#", tid = @ViewBag.TaskID}, null) + "' class='btn btn-nav btn-thinkware' >" +
                                      "Select Form(s)</a></div>" +
                                  "#}" +
                                  "else if(DocId =='Multi' || DocId == 0){#" +
                                      "<div class='icon-center table-icons'>" +
                                      "<a href='" + @Url.Action("GetStateForms", "OBProcess", new { id = 0,  s = "#=State#", tid = @ViewBag.TaskID }, null) + "' class='btn btn-nav btn-thinkware'  data-toggle='modal' data-target='\\#completeModal' data-remote='false' >" +
                                      "Select Form(s)</a></div>" +
                                  "#}" +
                                  "else" +
                                  "{#" +
                                      "<div class='icon-center table-icons'>No Forms Available</div>" +
                                  "#}#" +
                              "#} " +
                              "else " +
                              "{# " +
                                  "#if(DocId>0){#" +
                                      "<div class='icon-center table-icons'>" +
                                      "<a href='" + @Url.Action("ProcessDocWebForm", "OBProcess", new { id = "#=DocId#", tid = @ViewBag.TaskID }, null) + "' class='btn btn-nav btn-thinkware' >" +
                                      "Complete Form</a></div>" +
                                  "#}" +
                                  "else if(DocId =='Multi'){#" +
                                      "<div class='icon-center table-icons'>" +
                                      "<a href='" + @Url.Action("GetStateForms", "OBProcess", new { id = 0, s = "#=State#", tid = @ViewBag.TaskID }, null) + "' class='btn btn-nav btn-thinkware'  data-toggle='modal' data-target='\\#completeModal' data-remote='false' >" +
                                      "Select Form(s)</a></div>" +
                                  "#}" +
                                  "else" +
                                  "{#" +
                                      "<div class='icon-center table-icons'>No Forms Available</div>" +
                                  "#}#" +
                              "#}" +
                              "#"
                          ).Width(200);
            columns.Template(@<text>
                                        </text>)
                          .Title("Action")
                      .ClientTemplate(
                          "<center><div class=''>" +
                          //"<a href='" + Url.Action("ProcessForm", "OBProcess", new {id = "#= RecordID#", tid = @ViewBag.TaskID, a = "Dynamic"}) + "' title='Edit' class=''><i class='icon-edit fa fa-pencil fa-fw fa-lg'></i></a>" +
                          "<a href='\\#' title='Delete' class='' data-desc='#= State#' id='delete' data-id='#= RecordID#'><i class='icon-red fa fa-times fa-fw fa-lg'></i></a>" +
                          "</center>").Width(85).HeaderHtmlAttributes(new { @class = "header-center" });
                    


                  })
      
        .Pageable()
        .Sortable()
        .Filterable()
        .Groupable()
        // .Navigatable()
        .Scrollable()
        // .Selectable(selectable=> selectable.Mode(GridSelectionMode.Multiple).Type(GridSelectionType.Cell))
        .HtmlAttributes(new {style = "height:430px;"})
        .Events(events => events.Change("onChange"))
        .DataSource(dataSource => dataSource
        .Ajax()
        .PageSize(15)
        //.Events(events => events.Error("error_handler"))
        .Model(model =>
        {
            //The unique identifier (primary key) of the model is the ProductID property
            model.Id(p => p.RecordID);
            // Declare a model field and optionally specify its default value (used when a new model instance is created)
            //model.Field(p => p.State).Editable(false);
            //model.Field(p => p.MaritalStatus).Editable(false);
            //model.Field(p => p.Dependents).Editable(false);
            //model.Field(p => p.EstimatedAmount).Editable(false);
            //model.Field(p => p.AdditionalAmount).Editable(false);


        })
        // .Create(update => update.Action("EditingInline_Create", "Grid"))
        .Read(read => read.Action("StateTaxIndex_Read", "OBProcess"))
        //        .Update(update => update.Action("StateTaxIndex_Update", "OBProcess"))
        .Destroy(update => update.Action("EditingInline_Destroy", "Grid"))
        )

        )
        </div>
    }
    else
    {
<div id="state" class="dynamic-process">
    @(Html.Kendo().Grid<DarwiNet2._0.Models.OBEEStateTax>()
                  .Name("grid")
                  .Columns(columns =>
                  {
        columns.Bound(p => p.State).Title(FieldTranslation.GetLabel("State", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new {title = FieldTranslation.GetLabel("State", GlobalVariables.LanguageID)}).Width(80);
        columns.Bound(p => p.EstimatedAmount).Title(FieldTranslation.GetLabel("EstimatedAmount", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new {title = FieldTranslation.GetLabel("EstimatedAmount", GlobalVariables.LanguageID)}).Width(160);
        columns.Bound(p => p.AdditionalAmount).Title(FieldTranslation.GetLabel("AdditionalAmount", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new {title = FieldTranslation.GetLabel("AdditionalAmount", GlobalVariables.LanguageID)}).Width(165);
        columns.Bound(p => p.Dependents).Title(FieldTranslation.GetLabel("Dependents", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new {title = FieldTranslation.GetLabel("Dependents", GlobalVariables.LanguageID)}).Width(120);
        //columns.Bound(p => p.Status).Title(FieldTranslation.GetLabel("Status", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Status", GlobalVariables.LanguageID) }).Width(160);
        // columns.Bound(p => p.MaritalStatus).Title(FieldTranslation.GetLabel("MaritalStatus", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("MaritalStatus", GlobalVariables.LanguageID) });


        columns.Template(@<text>
                                        </text>)
                          .Title("Status")
                          .ClientTemplate(
                              "# " +
                              "if(!Completed)" +
                              "{#" +
                              "#if(DocId>0){#" +
                              "<div class='icon-center table-icons'>" +
                              "#if(GlobalVariables.IsIE){#" +
                              "<a href='" + @Url.Action("ProcessDocWebForm", "OBProcess", new { id = "#=DocId#", tid = @ViewBag.TaskID }, null) + "' style='color:blue; text-decoration: underline;' >" +
                              "#}#" +
                              "Select Form(s)</a></div>" +
                              "#}" +
                              "else if(DocId =='Multi'){#" +
                              "<div class='icon-center table-icons'>" +
                              "#if(GlobalVariables.IsIE){#" +
                              "<a href='" + @Url.Action("GetStateForms", "OBProcess", new { id = 0, tid = @ViewBag.TaskID, s = "#=State#" }, null) + "' style='color:blue; text-decoration: underline;' data-toggle='modal' data-target='\\#completeModal' data-remote='false'>" +
                              "#}#" +
                              "Select Form(s)</a></div>" +
                              "#}" +
                              "else" +
                              "{#" +
                              "<div class='icon-center table-icons'>No Forms Available</div>" +
                              "#}#" +
                              "#} " +
                              "else " +
                              "{# " +
                              "<div class='icon-center table-icons'>Completed</div>" +
                              "#}"
                              + "#"
                          ).Width(200);

                      columns.Template(@<text>

                                        </text>)
                          .Title("Action")
                          .ClientTemplate(
                              "<div class=' icon-center'>" +
                              "<a href='" + Url.Action("ProcessForm", "OBProcess", new { id = "#= RecordID#", tid = @ViewBag.TaskID, a = "Dynamic" }) + "' title='Edit' class=''><i class='icon-edit fa fa-search fa-fw fa-lg'></i></a>"
                          ).Width(85).HeaderHtmlAttributes(new { @class = "header-center" });
                    

                  })
   

    .Pageable()
    .Sortable()
    .Filterable()
    .Groupable()
    // .Navigatable()
    .Scrollable()
    // .Selectable(selectable=> selectable.Mode(GridSelectionMode.Multiple).Type(GridSelectionType.Cell))
    .HtmlAttributes(new {style = "height:430px;"})
    .Events(events => events.Change("onChange"))

    .DataSource(dataSource => dataSource
    .Ajax()
    .PageSize(15)
    //.Events(events => events.Error("error_handler"))
    .Model(model =>
    {
        //The unique identifier (primary key) of the model is the ProductID property
        model.Id(p => p.RecordID);
        // Declare a model field and optionally specify its default value (used when a new model instance is created)
        //model.Field(p => p.State).Editable(false);
        //model.Field(p => p.MaritalStatus).Editable(false);
        //model.Field(p => p.Dependents).Editable(false);
        //model.Field(p => p.EstimatedAmount).Editable(false);
        //model.Field(p => p.AdditionalAmount).Editable(false);


    })
    // .Create(update => update.Action("EditingInline_Create", "Grid"))
    .Read(read => read.Action("StateTaxIndex_Read", "OBProcess"))
    //        .Update(update => update.Action("StateTaxIndex_Update", "OBProcess"))
    .Destroy(update => update.Action("EditingInline_Destroy", "Grid"))
    )

    )
</div>
    }
</div>
@if (!GlobalVariables.IsOBAuditMode)
{
    <div class="modal fade" id="addLocalModal" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="addLocalModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="addLocalModalLabel">Add State Tax</h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="icon-red fa fa-exclamation fa-3x" aria-hidden="true"></i>
                        @if (ViewBag.AtLeastOne)
                        {
                            <h3>Select State tax form option</h3>
                        }
                        else
                        {
                            <h3>What would you like to do?</h3>
                        }

                        <div>
                            <button type="button" class="btn btn-danger" id="localNo">Continue to Next Task</button>
                            @if (ViewBag.AtLeastOne)
                            {
                                <button type="button" class="btn btn-info" data-dismiss="modal" id="localEdit">Edit an Existing State</button>
                            }
                            else
                            {
                                <a href="@Url.Action("RefreshStateCodes", new {id = 0, tid = ViewBag.TaskID, a = "StateTaxIndex"})" title="Refresh State Codes" class="btn btn-thinkware">Refresh State Codes</a>
                            }
                            @if (!GlobalVariables.IsOBAuditMode)
                            {
                                <button type="button" class="btn btn-success" id="localYes">Add a new State</button>
                            }
                        </div>
                    </div>
                </div>
                @*<div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                       $1$ <button type="button" class="btn btn-primary">Save changes</button>#1#
                    </div>*@
            </div>
        </div>
    </div>
}
<!-- Complete Modal -->
<div class="modal fade" id="completeModal" tabindex="-1" role="dialog" aria-labelledby="completeModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="completeModalLabel">Forms</h4>
            </div>
            <div class="modal-body">
                Generating...
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                @*<button type="button" class="btn btn-primary">Save changes</button>*@
            </div>
        </div>
    </div>
</div>
@if ((!ViewBag.AtLeastOne) || (ViewBag.AtLeastOne && string.IsNullOrEmpty(ViewBag.State)))
{
    <script>
        $(document).ready(function() {
            loadModal();
        });
        function loadModal() {
            if (@Updatable == "1") $('#addLocalModal').modal('toggle');
        }
    </script>
}

<script>
    $('#localNo').click(function(e) {
        $('#localNo').text("Updating...");
        $('#localNo').prepend("<i class='fa fa-spinner fa-spin fa-fw'></i>");
        var url = $("#continue a").attr('href');
        window.location.href = url;
        //$("#continue a").trigger( "click" );
    });

        $("#completeModal").on("show.bs.modal", function (e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-body").load(link.attr("href"));
        });

        $('#localYes').click(function() {
            if (@Updatable == "1")
            {
                $('#addLocalModal').modal('toggle');
                $('#addCodes').modal('toggle');
            }
        });
</script>
@*<script src="~/Scripts/sweetalert.min.js"></script>*@
<script src="~/Scripts/bootbox.min.js"></script>
<script type="text/javascript">
    function error_handler(e) {
        if (e.errors) {
            var message = "Errors:\n";
            $.each(e.errors,
                function(key, value) {
                    if ('errors' in value) {
                        $.each(value.errors,
                            function() {
                                message += this + "\n";
                            });
                    }
                });
            alert(message);
        }
    }

    function onChange(arg) {
        var selected = $.map(this.select(),
            function(item) {
                //alert($(item).text() + ' ' + $(item).attr('id'));
                // return $(item).text();
            });
    }
</script>
<script>
    $(document)
        .on("click",
            '#showDocs',
            function() {
                var $this = $(this);
                $this.toggleClass('showDocsButton');
                $('#otherDocs').toggle();
                if ($this.hasClass('showDocsButton')) {
                    $this.text('Hide Additional Documents');
                } else {
                    $this.text('Show Additional Documents');
                }
            });
</script>

<script type="text/javascript">
    $(document).on("click", "#btnIframe" ,function () {
        var docNum = $("#W4Doc").val();
        //var submitURL = "@Url.Action("SubmitPDF", "OBProcess", null, Request.Url.Scheme)" + "/?docId=" + docNum;
        //var url = "@Url.Action("ShowWebForm", "OBProcess")" + "/?id=" + docNum + "&tid=" + @ViewBag.TaskId + "&hideView=1";
        var url2 = "@Url.Action("ProcessDocWebForm", "OBProcess", new { tid = @ViewBag.TaskID }, null)";
        url2 += "&id=" + docNum;
        @*$('#pdfDiv2').load(url,null, function (response, status, xhr) {
                if (response.indexOf('NoForm') >= 0) {
                    var redirectURL = "@Url.Action("ProcessPDFMulti", "OBProcess", new { id = ViewBag.DocId, tid = ViewBag.TaskId })";*@
        window.parent.location.href = url2;
        //    }
        //});
    });
   // 08/11/2020 DG TFS # 6597
    $(document).on("click", "#btnDefIframe" ,function () {
    var docNum = $("#W4DefDoc").val();
    var url2 = "@Url.Action("ProcessDocWebForm", "OBProcess")" + "/?id=" + docNum + "&tid=" + @ViewBag.TaskId;
        window.parent.location.href = url2;
    });
   // 08/11/2020 DG TFS # 6597
</script>
<script>
    function deleteit(e){

        e.preventDefault();
        var dataItem = this.dataItem($(e.currentTarget).closest("tr"));
        alert("you clicked delete" + ' ' + dataItem);
    }
    $(document).on("click", "td #delete", function (e) {
        var deletedID = $(this).attr('data-id')
        var url = "@Url.Action("Delete", "OBProcess", null)"
        var desc = $(this).attr('data-desc')
        bootbox.dialog({
            message: "Are you sure you want to delete: " + "<strong>" + desc + "</strong>",
            title: "Delete",
            buttons: {
                main: {
                    label: "Cancel",
                    className: "btn-primary",
                    callback: function () {
                        //Example.show("Primary button");
                    }
                },
                danger: {
                    label: "Delete",
                    className: "btn-danger",
                    callback: function () {
                        window.location.href = url + "/?id=" + deletedID + "&taskId=" + @ViewBag.TaskID;
                    }
                }
            }
        });
    });
</script>

@*<script type="text/javascript">
        $(document).ready(function () {
            $('body').on('click', 'td #delete', function () {
                var deletedID = $(this).attr('data-id')
                var url = "@Url.Action("Delete", "OBProcess", null)"
                swal({
                    title: "Are you sure?",
                    text: "You will not be able to recover this profile.",
                    type: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: 'Delete',
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: true
                },
                function (isConfirm) {
                    if (isConfirm) {
                        window.location.href = url + "/" + deletedID + "?taskId=" + @ViewBag.TaskID;
                    }
                });
            });

        })
    </script>*@
@section scripts{

    <script>
        $(document).ready(function () {

            $("#FieldFilter").keyup(function () {
                var value = $("#FieldFilter").val();
                var grid = $("#grid").data("kendoGrid");

                if (value) {
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            { field: "State", operator: "contains", value: value }
                        ]
                    })
                } else {
                    grid.dataSource.filter({});
                }
            });
        });


    </script>
}
