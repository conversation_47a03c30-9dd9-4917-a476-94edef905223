@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
<h2>Paycodes</h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>

@(Html.<PERSON>().Grid<DarwiNet2._0.Models.OBEEPaycodes>()
    .Name("grid")
    .Columns(columns =>
    {
        columns.Bound(p => p.Paycode).Title(FieldTranslation.GetLabel("PayCode", GlobalVariables.LanguageID));
        columns.Bound(p => p.Description).Title(FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID));
        columns.Bound(p => p.PayType).Title(FieldTranslation.GetLabel("PayType", GlobalVariables.LanguageID));
        columns.Bound(p => p.Payrate).Format("${0:n2}").Title(FieldTranslation.GetLabel("PayRate", GlobalVariables.LanguageID));
        columns.Bound(p => p.PayUnit).Title(FieldTranslation.GetLabel("PayUnit", GlobalVariables.LanguageID));

        columns.Template(@<text>

        </text>)
                      .Title("Action")
                      .ClientTemplate(
                          "<div class=' icon-center'>" +
                          "<a href='" + Url.Action("ProcessForm", "OBProcessMonitor", new { id = "#= RecordID#", tid = 35, a="Dynamic" }) + "' title='Edit' class=''><i class='icon-edit fa fa-pencil fa-fw fa-lg'></i></a>" +
                          "<a href='" + Url.Action("Delete", "OBProcessMonitor", new { id = "#= RecordID#" }) + "' title='Delete' class=''><i class='icon-red fa fa-times fa-fw fa-lg'></i></a>" 
                          ).Width(85).HeaderHtmlAttributes(new { @class = "header-center" });
                        @*columns.Template(@<text>

                            </text>)
                                          .ClientTemplate(
                                             @Enum.GetName(typeof(PayPeriods), (int)("#= PayPeriod#"))).Title(FieldTranslation.GetLabel("PayPeriod", GlobalVariables.LanguageID));*@
        //columns.Bound(p => p.PayPeriod).Title(FieldTranslation.GetLabel("PayPeriod", GlobalVariables.LanguageID));
        //columns.Bound(p => p.BasedUpon).Title(FieldTranslation.GetLabel("BasedUpon", GlobalVariables.LanguageID));
       // columns.Command(command => { command.Edit(); command.Destroy(); }).Width(172);
    })
        .ToolBar(toolbar =>
                {
                    //toolbar.Create();
                    toolbar.Save();
                })
    .Editable(editable => editable.Mode(GridEditMode.InCell))
    .Pageable()
    .Sortable()
        .Navigatable()
    .Scrollable()
    .HtmlAttributes(new { style = "height:430px;" })
    .DataSource(dataSource => dataSource
        .Ajax()
        .PageSize(15)
        .Batch(true)
        .ServerOperation(false)
        .Events(events => events.Error("error_handler"))
        .Model(model =>
        {
            //The unique identifier (primary key) of the model is the ProductID property
            model.Id(p => p.RecordID);

            // Declare a model field and optionally specify its default value (used when a new model instance is created)
            model.Field(p => p.Paycode).Editable(false);
            model.Field(p => p.Description ).Editable(false);
            model.Field(p => p.PayType).Editable(false);
            model.Field(p => p.PayUnit).Editable(false);
            //model.Field(p => p.BasedUpon).Editable(false);
            //model.Field(p => p.PayPeriod).DefaultValue(ViewData["PayPeriods"] as DarwiNet2._0.Models.PayPeriods);


        })
        .Create(update => update.Action("EditingInline_Create", "Grid"))
            .Read(read => read.Action("PaycodeIndex_Read","OBProcess"))
            .Update(update => update.Action("PaycodeIndex_Update", "OBProcess"))
        .Destroy(update => update.Action("EditingInline_Destroy", "Grid"))
    )
)
<script type="text/javascript">
    function error_handler(e) {
        if (e.errors) {
            var message = "Errors:\n";
            $.each(e.errors, function (key, value) {
                if ('errors' in value) {
                    $.each(value.errors, function () {
                        message += this + "\n";
                    });
                }
            });
            alert(message);
        }
    }
</script>
