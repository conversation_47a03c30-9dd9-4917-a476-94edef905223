@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;

@*@model IEnumerable<DarwiNet2._0.Models.OBEEPaycodes>*@

@{
    Layout = "";
    ViewBag.Title = "PTO Plans";
    var Updatable = (ViewBag.FinalizeStatus != 2) ? "1" : "0"; 
}
<link href="~/Content/sweetalert.css" rel="stylesheet" />
    <div class="toolbar">

        <div class="row">
            <div class="col-md-4 pull-left">
                <div class="input-group">
                    <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                    <input type="text" class="form-control" id='FieldFilter' placeholder="Search PTO Type">
                </div>
            </div>
            @if (Updatable == "1")
            {
            <p class="create-pad pull-right">
                <a href="#" title="Add PTO" data-toggle="modal" data-target="#addCodes" class="btn btn-thinkware"><i class="fa fa-fw fa-plus fa-lg"></i>Add PTO</a>
            </p>
            }
        </div>
    </div>
    <div class="table-bottom table-icons">
       @if (Updatable == "1")
       {
        <div id="pto" class="dynamic-process">

            @(Html.Kendo().Grid<DarwiNet2._0.Models.OBEEPTO>()
    .Name("grid")
    .Columns(columns =>
    {
        columns.Bound(p => p.PTOType).Title(FieldTranslation.GetLabel("PTOType", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("PTOType", GlobalVariables.LanguageID) });
        columns.Bound(p => p.Description).Title(FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID) });
        columns.Template(@<text>

        </text>)
                       .ClientTemplate(
                          "<div class=' icon-center'>" +
                          "<a href='" + Url.Action("ProcessForm", "OBProcess", new { id = "#= RecordID#", tid = @ViewBag.TaskID, a = "Dynamic" }) + "' title='Edit' class=''><i class='icon-edit fa fa-pencil fa-fw fa-lg'></i></a>" +
                          "<a href='\\#' title='Delete' class='' id='delete' data-desc='#= PTOType#' data-id='#= RecordID#'><i class='icon-red fa fa-times fa-fw fa-lg'></i></a>"
                          ).Width(100).Title(FieldTranslation.GetLabel("Actions", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Actions", GlobalVariables.LanguageID) }).HeaderHtmlAttributes(new { @class = "header-center" }); 
    })
                                   //    .ToolBar(toolbar =>
                                   //            {
                                   //                //toolbar.Create();
                                   //                toolbar.Save();
                                   //            })
                                   //.Editable(editable => editable.Mode(GridEditMode.InCell))
                                   .Pageable()
                                   .Sortable()
                                   .Filterable()
                                   .Groupable()
                                       .Navigatable()
                                   .Scrollable()
                                   .HtmlAttributes(new { style = "height:430px;" })
                                   .DataSource(dataSource => dataSource
                                       .Ajax()
                                       .PageSize(15)
                                       .Batch(true)
                                       .ServerOperation(false)
                                       .Events(events => events.Error("error_handler"))
                                       .Model(model =>
                                       {
                                           //The unique identifier (primary key) of the model is the ProductID property
                                           model.Id(p => p.RecordID);
                                           // Declare a model field and optionally specify its default value (used when a new model instance is created)
                                           model.Field(p => p.PTOType).Editable(false);
                                           model.Field(p => p.Description).Editable(false);

                                       })
                                       .Create(update => update.Action("EditingInline_Create", "Grid"))
                                           .Read(read => read.Action("PTOIndex_Read", "OBProcess"))
                                           .Update(update => update.Action("PTOIndex_Update", "OBProcess"))
                                       .Destroy(update => update.Action("EditingInline_Destroy", "Grid"))
                                   )
            )
        </div>
       }
       else
       {
        <div id="pto" class="dynamic-process">

            @(Html.Kendo().Grid<DarwiNet2._0.Models.OBEEPTO>()
    .Name("grid")
    .Columns(columns =>
    {
        columns.Bound(p => p.PTOType).Title(FieldTranslation.GetLabel("PTOType", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("PTOType", GlobalVariables.LanguageID) });
        columns.Bound(p => p.Description).Title(FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID) });
        columns.Template(@<text>

        </text>)
                       .Title("Action")
                       .ClientTemplate(
                          "<center><div class=''>" +
                          "<a href='" + Url.Action("ProcessForm", "OBProcess", new { id = "#= RecordID#", tid = @ViewBag.TaskID, a = "Dynamic" }) + "' title='Edit' class=''><i class='icon-edit fa fa-search fa-fw fa-lg'></i></a>" +
                         
                          "</center>").Width(70);
    })
                                   //    .ToolBar(toolbar =>
                                   //            {
                                   //                //toolbar.Create();
                                   //                toolbar.Save();
                                   //            })
                                   //.Editable(editable => editable.Mode(GridEditMode.InCell))
                                   .Pageable()
                                   .Sortable()
                                   .Filterable()
                                   .Groupable()
                                       .Navigatable()
                                   .Scrollable()
                                   .HtmlAttributes(new { style = "height:430px;" })
                                   .DataSource(dataSource => dataSource
                                       .Ajax()
                                       .PageSize(15)
                                       //.Batch(true)
                                       //.ServerOperation(false)
                                       .Events(events => events.Error("error_handler"))
                                       .Model(model =>
                                       {
                                           //The unique identifier (primary key) of the model is the ProductID property
                                           model.Id(p => p.RecordID);
                                           // Declare a model field and optionally specify its default value (used when a new model instance is created)
                                           model.Field(p => p.PTOType).Editable(false);
                                           model.Field(p => p.Description).Editable(false);

                                       })
                                       .Create(update => update.Action("EditingInline_Create", "Grid"))
                                           .Read(read => read.Action("PTOIndex_Read", "OBProcess"))
                                           .Update(update => update.Action("PTOIndex_Update", "OBProcess"))
                                       .Destroy(update => update.Action("EditingInline_Destroy", "Grid"))
                                   )
            )
        </div>
       }
    </div>
@if (!GlobalVariables.IsOBAuditMode)
{
<div class="modal fade" id="addLocalModal" tabindex="-1" role="dialog" aria-labelledby="addLocalModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="addLocalModalLabel">Add PTO Plans</h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="icon-red fa fa-exclamation fa-3x" aria-hidden="true"></i>
                    @if (ViewBag.AtLeastOne)
                    {
                        <h3>Would you like to add another PTO plan?</h3>
                    }
                    else
                    {
                        <h3>Would you like to add a PTO plan?</h3>
                    }
                    
                    <div>
                        <button type="button" class="btn btn-lg btn-danger" id="localNo">No</button>
                        @if (ViewBag.AtLeastOne)
                        {
                            <button type="button" class="btn btn-lg btn-info" data-dismiss="modal" id="localEdit">Edit Existing</button>
                        }
                        <button type="button" class="btn btn-lg btn-success" id="localYes">Yes</button>
                    </div>
                </div>
            </div>
            @*<div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                   $1$ <button type="button" class="btn btn-primary">Save changes</button>#1#
                </div>*@
        </div>
    </div>
</div>
}
<script>
    $(document).ready(function() {
        loadModal();
    });
    function loadModal() {
        if (@Updatable == "1") $('#addLocalModal').modal('toggle');
    }

    $('#localNo').click(function(e) {
        $('#localNo').text("Updating...");
        $('#localNo').prepend("<i class='fa fa-spinner fa-spin fa-fw'></i>");
        var url = $("#continue a").attr('href');
        window.location.href = url;
        //$("#continue a").trigger( "click" );
    });



    $('#localYes').click(function() {
        if (@Updatable == "1")
        {
            $('#addLocalModal').modal('toggle');
            $('#addCodes').modal('toggle');
        }
    });
</script>
@*<script src="~/Scripts/sweetalert.min.js"></script>*@
<script src="~/Scripts/bootbox.min.js"></script>
<script type="text/javascript">
    function error_handler(e) {
        if (e.errors) {
            var message = "Errors:\n";
            $.each(e.errors, function (key, value) {
                if ('errors' in value) {
                    $.each(value.errors, function () {
                        message += this + "\n";
                    });
                }
            });
            alert(message);
        }
    }
</script>
<script>
    $(document).on("click", "td #delete", function (e) {
        var deletedID = $(this).attr('data-id')
        var url = "@Url.Action("Delete", "OBProcess", null)"
        var desc = $(this).attr('data-desc')
        bootbox.dialog({
            message: "Are you sure you want to delete: " + "<strong>" + desc + "</strong>",
            title: "Delete",
            buttons: {
                main: {
                    label: "Cancel",
                    className: "btn-primary",
                    callback: function () {
                        //Example.show("Primary button");
                    }
                },
                danger: {
                    label: "Delete",
                    className: "btn-danger",
                    callback: function () {
                        window.location.href = url + "/?id=" + deletedID + "&taskId=" + @ViewBag.TaskID;
                    }
                }
            }
        });
    });
</script>

@*<script type="text/javascript">
    $(document).ready(function () {
        $('body').on('click', 'td #delete', function () {
            var deletedID = $(this).attr('data-id')
            var url = "@Url.Action("Delete", "OBProcess", null)"
            swal({
                title: "Are you sure?",
                text: "You will not be able to recover this profile.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: 'Delete',
                cancelButtonText: "Cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            },
            function (isConfirm) {
                if (isConfirm) {
                    window.location.href = url + "/" + deletedID + "?taskId=" + @ViewBag.TaskID;
                }
            });
        });

    })
</script>*@
@section scripts{

    <script>
        $(document).ready(function () {
            
            $("#FieldFilter").keyup(function () {
                var value = $("#FieldFilter").val();
                var grid = $("#grid").data("kendoGrid");

                if (value) {
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            { field: "PTOType", operator: "contains", value: value }
                        ]
                    })
                } else {
                    grid.dataSource.filter({});
                }
            });
        });
    </script>
}