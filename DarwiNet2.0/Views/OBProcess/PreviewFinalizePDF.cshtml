@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Helpers;


@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = ViewBag.EmployeeName + " (" + (FieldTranslation.EmployeeIDFormatted(GlobalVariables.EmployeeID)) + ")";
}
<script>

    $(document).ready(function () {
        url = "@Url.Action("ShowForm", "OBProcess", new { docId = ViewBag.DocId, url = "" })"

       // var url = '/OBProcess/ShowForm/?docId=195';// + pParams;
        var html = "<iframe src=" + url + " style='width: 100%; height: 700px' ></iframe>";

        $('#pdfDiv').html(html);
    });
</script>

<div class="row">
    <div class="col-md-9 instructions-box-dynamic">
        <div class="div-10"></div>
        <p class="pull-right">
            <a href="@Url.Action("Index", "OBProcessFinalize")" title="Return to Finalize" data-toggle="modal" class="btn btn-thinkware">Return To Finalize</a>
        </p>
        <div id="pdfDiv"></div>
    </div>



</div>







