@{
    ViewBag.Title = "FMLALOA";
    ViewBag.ParentCrumb = "Human Resources";
}

<div style="margin:15px">
    @Html.Action("_EmployeeInfo", "Employees", new { e = ViewBag.EmployeeID })
</div>

<p style="font-size: 22px;">FMLA / LOA</p>
<div class="colored-line-left"></div>

<div class="animate fadeInRight">
    <fieldset style="margin-top: 20px">
        <legend>Request Leave of Absence or FMLA</legend>
        <p>
            Please complete this quick form to request a Leave of Absence or FMLA. Complete all the fields as best that
            you can, then click the "Request Leave/FMLA" button. An HR Representative will contact you shortly.
        </p>
    </fieldset>
</div>

<div class="">
    @Html.Partial("~/Views/EmployeeHR/_FMLAPartial.cshtml")
</div>
<div class="row">
    <div class="col-md-12">
        <div id="contentTable">
            <table id="fmla">
                <thead>
                    <tr>
                        <th>
                            Reason
                        </th>
                        <th>
                            Begin Date
                        </th>
                        <th>
                            End Date
                        </th>
                        <th>
                            FMLA
                        </th>
                        <th>
                            Approved
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            FMLA
                        </td>
                        <td>
                            01/14/2015
                        </td>
                        <td>
                            01/14/2015
                        </td>
                        <td>
                            <input type="radio" />
                        </td>
                        <td>
                            <input type="radio" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

   
@section scripts{
    <script>
        //Creates the kendo grid table
        $(document).ready(function () {
            var grid = $("#fmla").kendoGrid({
                toolbar: ["excel"],
                excel: {
                    fileName: "FMLA.xlsx",
                    filterable: true
                },
                dataSource: {
                    pageSize: 15
                },
                sortable: true,
                pageable: true,
                filterable: true,
                groupable: true,
                scrollable: false,                
            }).data("kendoGrid");
        });
        $('.emp-text').hide();
        $('.click-collapse').click(function () {
            $('#employeeInfoPartial').slideToggle('slow');
            $("i", this).toggleClass("fa-minus-square-o fa-plus-square-o");
            $('.emp-text').toggle('slow');
        });
    </script>
    }
