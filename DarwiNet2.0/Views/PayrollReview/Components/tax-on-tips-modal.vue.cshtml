
<style type="text/css" id="tax-on-tips-modal-style">
    
</style>

<script type="text/x-template" id="tax-on-tips-modal-template">
    <div class="panel panel-thinkware">
        <div class="panel-body">
            <table class="table">
                <thead>
                    <tr>
                        <th></th>
                        <th class="text-right">Tax On Wages</th>
                        <th class="text-right">Tax On Tips</th>
                        <th class="text-right">Total Tax</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th>Federal</th>
                        <td class="text-right">${{applyPrecision(viewModel.FederalWithholding - viewModel.FederalTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.FederalTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.FederalWithholding)}}</td>
                    </tr>
                    <tr>
                        <th>FICA Med</th>
                        <td class="text-right">${{applyPrecision(viewModel.EEFICAMed - viewModel.EEFICAMedTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.EEFICAMedTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.EEFICAMed)}}</td>
                    </tr>
                    <tr>
                        <th>FICA SS</th>
                        <td class="text-right">${{applyPrecision(viewModel.EEFICASS - viewModel.EEFICASSTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.EEFICASSTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.EEFICASS)}}</td>
                    </tr>
                    <tr>
                        <th>State</th>
                        <td class="text-right">${{applyPrecision(viewModel.StateWithholding - viewModel.StateTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.StateTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.StateWithholding)}}</td>
                    </tr>
                    <tr>
                        <th>Local</th>
                        <td class="text-right">${{applyPrecision(viewModel.LocalWithholding - viewModel.LocalTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.LocalTaxOnTips)}}</td>
                        <td class="text-right">${{applyPrecision(viewModel.LocalWithholding)}}</td>
                    </tr>
                    <tr style="border-top: 2px solid darkgray;">
                        <th>Totals</th>
                        <th class="text-right">${{getTotal('withholding')}}</th>
                        <th class="text-right">${{getTotal('tips')}}</th>
                        <th class="text-right">${{getTotal('total')}}</th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</script>

<script type="text/javascript" id="tax-on-tips-modal-script">
    var TaxOnTipsModalComponent = VueComponent('tax-on-tips-modal', {
        props: {
            viewModel: {
                type: Object,
                required: true,
            }
        },
        data: function () {
            return {
                
            }
        },

        created: function () {
           
        },
        methods: {
            getTotal: function (column) {
                let total = 0;
                switch (column) {
                    case "withholding":
                        total = (this.viewModel.FederalWithholding - this.viewModel.FederalTaxOnTips) +
                            (this.viewModel.EEFICAMed - this.viewModel.EEFICAMedTaxOnTips) +
                            (this.viewModel.EEFICASS - this.viewModel.EEFICASSTaxOnTips)+
                            (this.viewModel.StateWithholding - this.viewModel.StateTaxOnTips) +
                            (this.viewModel.LocalWithholding - this.viewModel.LocalTaxOnTips);
                        break;
                    case "tips":
                        total = this.viewModel.FederalTaxOnTips +
                            this.viewModel.EEFICAMedTaxOnTips +
                            this.viewModel.EEFICASSTaxOnTips +
                            this.viewModel.StateTaxOnTips +
                            this.viewModel.LocalTaxOnTips;
                        break;
                    case "total":
                        total = this.viewModel.FederalWithholding +
                            this.viewModel.EEFICAMed +
                            this.viewModel.EEFICASS +
                            this.viewModel.StateWithholding +
                            this.viewModel.LocalWithholding;
                        break
                    default:
                        break;
                }
                return this.applyPrecision(total);
            },
            applyPrecision: function (val) {
                return Number.parseFloat(val).toFixed(2);
            }
        }
    });

</script>
