<style type="text/css" scoped>
    .highlight-locked-row {
        background-color: #fff3cd !important;
    }

    .payroll-checks-action-icon i.fa {
        cursor: pointer;
    }

        .payroll-checks-action-icon i.fa:hover {
            text-shadow: 1px 1px 0 rgba(0,0,0,.15), 2px 2px 2px rgba(0,0,0,.25);
        }

    .fa-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
    }

    .highlight-warning-row {
        background-color: #fcf8e3 !important;
    }

    .highlight-warning-row:hover {
        background-color: #f9f2cd !important;
    }
</style>

<script type="text/x-template" id="payroll-checks-actions-template">
    <span class="payroll-checks-action-icon" :id="`${rowData.EmployeeID}-check-actions`">

        <i tabindex="0"
           v-if="!isLocked"
           :title="deleteClickable ? 'Delete Check' : 'Unable to Delete Check'"
           class="fa fa-fw fa-times"
           v-bind:class="[deleteClickable ? '' : 'fa-disabled']"
           :style="{color: deleteClickable ? 'red' : 'grey'}"
           @@click="deleteClickable ? onDeleteClicked() : null"></i>

        <i tabindex="0"
           :title="editClickable ? 'Edit Payroll Calculation' : 'Edit Locked'"
           class="fa fa-fw"
           v-bind:class="[editClickable ? 'fa-pencil' : 'fa-lock fa-disabled']"
           :style="{color: actionIconColor(editClickable)}"
           @@click="editClickable ? onEditClicked() : null"></i>

        <i tabindex="0"
           :title="'View Control and Variance Records'"
           class="fa fa-fw fa-file-o"
           v-if="rowData.Warning"
           @@click="onControlAndVarianceRecordsClicked()"></i>

    </span>
</script>

<script type="text/javascript" id="payroll-checks-actions-script">
    var PayrollChecksActionsComponent = VueComponent('payroll-checks-actions', {
        props: {
            rowData: {
                type: Object,
                required: true,
                default: () => ({}),
            },
            isLocked: {
                type: Boolean,
                required: false,
                default: false,
            },
            disableActions: {
                type: Boolean,
                required: false,
                default: false,
            }
        },

        data: function () {
            return {
                events: {
                    onDeleteClicked: "click-delete-check-modal",
                    onEditClicked: "click-edit-payroll-calc",
                    onControlAndVarianceRecordsClicked: "click-control-and-variance-records-modal"
                }
            }
        },

        mounted: function () {
            var el = document.getElementById(`${this.rowData.EmployeeID}-check-actions`);
            var row = el.closest("tr");
            row.classList.remove("highlight-warning-row");
            row.classList.remove("highlight-locked-row");

            if (this.rowData.Warning) {
                row.classList.add("highlight-warning-row");
            }

            if (this.rowData.IsLocked) {
                row.classList.add("highlight-locked-row")
            }
        },

        computed: {
            deleteClickable: function () {
                return !this.rowData.IsLocked && !this.disableActions;
            },
            editClickable: function () {
                return !this.isLocked && !this.disableActions;
            },
        },
        methods: {
            actionIconColor: function (val) {
                return val ? "#244061" : "grey";
            },
            onDeleteClicked: function () {
                this.$emit(this.events.onDeleteClicked, this.rowData);
            },
            onEditClicked: function () {
                this.$emit(this.events.onEditClicked, this.rowData);
            },
            onControlAndVarianceRecordsClicked: function () {
                this.$emit(this.events.onControlAndVarianceRecordsClicked, this.rowData.EmployeeID);
            }
        },
        watch: {
            'rowData.Warning': function () {
                var el = document.getElementById(`${this.rowData.EmployeeID}-check-actions`);

                var row = el.closest("tr");

                if (this.rowData.Warning) {
                    row.classList.add("highlight-warning-row");
                } else {
                    row.classList.remove("highlight-warning-row");
                }
            }
        }
    });
</script>
