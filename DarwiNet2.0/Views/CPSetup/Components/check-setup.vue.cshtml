@using DarwiNet2._0.Extensions;
@using Thinkware.Pay360.Payroll;
@using Thinkware.UI.Vue

<script type="text/x-template" id="check-setup-template">
    <div class="panel panel-thinkware">
        <div class="panel-heading">Check Setup</div>
        <div class="panel-body">
            <p style="font-size: 22px;">Check File Settings</p>
            <div class="colored-line-left"></div>
            <div class="row">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-2 col-sm-2 margin-bottom-15">
                            <label>Check File: </label>
                        </div>
                        <div class="col-md-4 col-sm-4 margin-bottom-15">
                            <select id="CheckFileID" v-model="viewModel.CheckFileID" class="form-control" @@change="onCheckChange()">
                                <option v-for="item in viewModel.CheckFiles" :value="item.Code"> {{item.Description}} </option>
                            </select>
                        </div>
                        <div class="form-group col-md-3 col-sm-3">
                            <label>Uses As Client Default</label>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <input type="checkbox" id="Default" v-model="viewModel.Default" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Max Pay Codes</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="text" id="MaxPaycodes" v-model="viewModel.MaxPaycodes" class="form-control" />
                        </div>
                        <div class="form-group col-md-3 col-sm-3">
                            <label>Use YTD Amounts Column</label>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <input type="checkbox" id="PrintYTD" v-model="viewModel.PrintYTD" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Max Benefits</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="text" id="MaxBenefits" v-model="viewModel.MaxBenefits" class="form-control" />
                        </div>
                        <div class="form-group col-md-3 col-sm-3">
                            <label>Use Department Column</label>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <input type="checkbox" id="SummarizeByDepartment" v-model="viewModel.SummarizeByDepartment" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Max Deductions</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="text" id="MaxDeductions" v-model="viewModel.MaxDeductions" class="form-control" />
                        </div>
                        <div class="form-group col-md-3 col-sm-3">
                            <label>Use Position Column</label>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <input type="checkbox" id="SummarizeByPosition" v-model="viewModel.SummarizeByPosition" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Max State Taxes</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="text" id="MaxStateTaxes" v-model="viewModel.MaxStateTaxes" class="form-control" />
                        </div>
                        <div class="form-group col-md-3 col-sm-3">
                            <label>Use Rate Column</label>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <input type="checkbox" id="SummarizeByRate" v-model="viewModel.SummarizeByRate" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Max Local Taxes</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="text" id="MaxLocalTaxes" v-model="viewModel.MaxLocalTaxes" class="form-control" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Max Deposits</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="text" id="MaxDeposits" v-model="viewModel.MaxDeposits" class="form-control" />
                        </div>
                    </div>
                </div>
            </div>
            <p style="font-size: 22px;">Other Settings</p>
            <div class="colored-line-left"></div>
            <div class="row">
                <div class="col-md-12">
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Print Inactive</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="checkbox" id="PrintInactive" v-model="viewModel.PrintInactive" class="chkitem" style="padding-top: 3px;" />
                        </div>
                        <div class="form-group col-md-3 col-sm-3">
                            <label>Transaction Based Department Code</label>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <input type="checkbox" id="UseTransactionDepartment" v-model="viewModel.UseTransactionDepartment" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Include Manual Checks</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="checkbox" id="IncludeManualChecks" v-model="viewModel.IncludeManualChecks" class="chkitem" style="padding-top: 3px;" />
                        </div>
                        <div class="form-group col-md-3 col-sm-3">
                            <label>Split Amounts By Job Costing</label>
                        </div>
                        <div class="col-md-3 col-sm-3">
                            <input type="checkbox" id="SplitByJobCosting" v-model="viewModel.SplitByJobCosting" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                </div>
            </div>
            <p style="font-size: 22px;">Printers Settings</p>
            <div class="colored-line-left"></div>
            <div class="row">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group col-md-2 col-sm-2">
                            <label>Do Not Email Deposits</label>
                        </div>
                        <div class="col-md-4 col-sm-4">
                            <input type="checkbox" id="PrintDeposits" v-model="viewModel.PrintDeposits" class="chkitem" style="padding-top: 3px;" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="pull-right">
                        <button type="button" id="btn_CheckSetupSaveChanges" class="btn btn-thinkware" @@click="btnSaveChanges_Click">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>

<!-- AUTO PAY -->
@Html.VueComponent("~/PayrollProfileAutoPayOptions/auto-pay-options-card")
@Html.VueComponent("~/PayrollProfileAutoPayOptions/auto-pay-options-table")
@Html.VueComponent("~/shared/thinkware-alert-modal")
<!-- ------------- -->

<script src="https://cdn.jsdelivr.net/npm/vue-toastr/dist/vue-toastr.umd.min.js"></script>

<script type="text/javascript" id="check-setup-script">
    Vue.use(VueToastr, {
        defaultPosition: 'toast-bottom-left',
        defaultType: 'info',
        defaultTimeout: 5000
    })
    var CheckSetupComponent = VueComponent('check-setup', {
        props: {
            viewModel: {
                type: Object
            },
            companyId: {
                type: Number
            },
            clientId: {
                type: String
            },
            profileId: {
                type: String
            }
        },
        data: function () {
            return {
            }
        },
        methods: {
            // AUTO PAY
            onCheckChange: function () {
                let self = this;
                var url = "@Url.Action("CheckFileSetup", "CPSetup")";
                var e = document.getElementById("CheckFileID");
                url = url + "?id=" + e.value;
                $.get(url, function (data) {
                    self.viewModel.MaxPaycodes = data['MaxPaycodes'];
                    self.viewModel.MaxBenefits = data['MaxBenefits'];
                    self.viewModel.MaxDeductions = data['MaxDeductions'];
                    self.viewModel.MaxDeposits = data['MaxDeposits'];
                    self.viewModel.MaxStateTaxes = data['MaxStateTaxes'];
                    self.viewModel.MaxLocalTaxes = data['MaxLocalTaxes'];
                    self.viewModel.IncludesYTD = data['IncludesYTD'];
                    self.viewModel.IncludesDepartments = data['IncludesDepartments'];
                    self.viewModel.IncludesPositions = data['IncludesPositions'];
                    self.viewModel.IncludesRates = data['IncludesRates'];
                });
            },
            setDataTable: function (dataTable) {
                this.dataTable = dataTable;
            },
            btnSaveChanges_Click: function () {
                let self = this;
                ThinkwareCommon.ajax.postJson('@Url.Action("EditCheckSetupSettings", "CPSetup")', {
                    data: {
                        companyId: self.companyId,
                        clientId: self.clientId,
                        profileId: self.profileId,
                        vm: {
                            CheckFileID: self.viewModel.CheckFileID,
                            Default: self.viewModel.Default,
                            Inactive: self.viewModel.Inactive,
                            IncludeManualChecks: self.viewModel.IncludeManualChecks,
                            MaxBenefits: self.viewModel.MaxBenefits,
                            MaxDeductions: self.viewModel.MaxDeductions,
                            MaxPaycodes: self.viewModel.MaxPaycodes,
                            MaxLocalTaxes: self.viewModel.MaxLocalTaxes,
                            MaxStateTaxes: self.viewModel.MaxStateTaxes,
                            MaxDeposits: self.viewModel.MaxDeposits,
                            NameFormat: self.viewModel.NameFormat,
                            PrintDeposits: self.viewModel.PrintDeposits,
                            PrintInactive: self.viewModel.PrintInactive,
                            PrintYTD: self.viewModel.PrintYTD,
                            SplitByJobCosting: self.viewModel.SplitByJobCosting,
                            SummarizeByDepartment: self.viewModel.SummarizeByDepartment,
                            SummarizeByPosition: self.viewModel.SummarizeByPosition,
                            SummarizeByRate: self.viewModel.SummarizeByRate,
                            UseTransactionDepartment: self.viewModel.UseTransactionDepartment,
                            UseDefaultPrinterForChecks: self.viewModel.UseDefaultPrinterForChecks,
                            UseDefaultPrinterForDeposits: self.viewModel.UseDefaultPrinterForDeposits,
                        }
                    },
                    onSuccess: function (result) {
                        // Handle response client side
                            vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                            vm.$toastr.defaultPosition = "toast-bottom-right";
                            vm.$toastr.s(result);
                            //window.location.reload()
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                         vm.$toastr.defaultClassNames = ["animated", "zoomInUp"];
                         vm.$toastr.defaultPosition = "toast-bottom-right";
                         vm.$toastr.e(errorThrown);
                    }
                });
            },
            // *************************

            decline: function() {
                this.$emit("decline")
            }
        },
        components: [
            ThinkwareAlertModal
        ],
    });

</script>