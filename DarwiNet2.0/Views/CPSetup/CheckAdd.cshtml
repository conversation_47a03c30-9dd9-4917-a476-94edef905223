@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core

@model DarwiNet2._0.ViewModels.CheckPrinter.CheckPrinterCrystalFilesVM


@{
    ViewBag.Title = "Add Crystal Report File";
}
<div class="company-info">
    <div class="row" style="padding-bottom: 10px;">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-6 col-sm-6">
            <div class="text-right">

            </div>
        </div>
    </div>
</div>

@using (Html.BeginForm("CheckAdd", "CPSetup", FormMethod.Post, new { @enctype = "multipart/form-data", @id = "cpsetupcheckadd" }))
{
    @*@Html.AntiForgeryToken()*@

<div class="form-horizontal plain-editor">

    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.Record.CompanyID)

    <div class="form-group">
        <label class="control-label col-md-2" for="file">File</label>
        <div class="col-md-4">
            @*Comment this line back in if you want to enable the tooltip for Internet Explorer *@
            @*<a class="tooltip-bottom" title="" data-placement="center" href="#" data-orginal-title="#file">@Html.TextBoxFor(model => model.Document, new { type = "file", name = "file", id = "file" })</a>*@
            <a class="bottom" title="" data-placement="center" href="#" data-orginal-title="#file">@Html.TextBoxFor(model => model.Record.FileName, new { type = "file", name = "file", id = "file" })</a>
        </div>

        @Html.LabelFor(model => model.Record.UseDefault, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Use as Default Check File", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.EditorFor(model => model.Record.UseDefault, new { @class = "form-control" })
            @Html.ValidationMessageFor(model => model.Record.UseDefault, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Record.ClientID, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Assigned To Client", GlobalVariables.LanguageID))

        @if (GlobalVariables.DNETLevel == DNetAccessLevel.Client)
        {
            <div class="col-md-4">
                <input type="text" id="Record.ClientID" name="Record.ClientID" value="@GlobalVariables.Client" readonly class="form-control not-allowed" />
            </div>
        }
        else
        {
            <div class="col-md-4">
                <select id="Record.ClientID" name="Record.ClientID" class="form-control">
                    @{
                        foreach (var item in Model.Clients)
                        {
                            if (item.Code == Model.Record.ClientID)
                            {
                                <option value="@item.Code" selected="selected">@item.Description</option>
                            }
                            else
                            {
                                <option value="@item.Code">@item.Description</option>
                            }
                        }
                    }
                </select>
                @Html.ValidationMessageFor(model => model.Record.ClientID, "", new { @class = "text-danger" })
            </div>
        }

    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Record.MaxPaycodes, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Max Earnings", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.EditorFor(model => model.Record.MaxPaycodes, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.MaxPaycodes, "", new { @class = "text-danger" })
        </div>

        @Html.LabelFor(model => model.Record.MaxDeposits, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Max Direct Deposits", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.EditorFor(model => model.Record.MaxDeposits, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.MaxPaycodes, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Record.MaxBenefits, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Max Benefits", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.EditorFor(model => model.Record.MaxBenefits, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.MaxBenefits, "", new { @class = "text-danger" })
        </div>

        @Html.LabelFor(model => model.Record.MaxDeductions, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Max Deductions", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.EditorFor(model => model.Record.MaxDeductions, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.MaxDeductions, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Record.MaxStateTaxes, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Max State Taxes", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.EditorFor(model => model.Record.MaxStateTaxes, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.MaxStateTaxes, "", new { @class = "text-danger" })
        </div>

        @Html.LabelFor(model => model.Record.MaxLocalTaxes, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Max Local Taxes", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.EditorFor(model => model.Record.MaxLocalTaxes, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.MaxLocalTaxes, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Record.IncludesRates, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Earning includes Pay Rates", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.CheckBoxFor(model => model.Record.IncludesRates, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.IncludesRates, "", new { @class = "text-danger" })
        </div>

        @Html.LabelFor(model => model.Record.IncludesYTD, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Earning includes YTD Amount", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.CheckBoxFor(model => model.Record.IncludesYTD, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.IncludesYTD, "", new { @class = "text-danger" })
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Record.IncludesPositions, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Earning includes Position", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.CheckBoxFor(model => model.Record.IncludesPositions, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.IncludesPositions, "", new { @class = "text-danger" })
        </div>

        @Html.LabelFor(model => model.Record.IncludesDepartments, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Earning includes Department", GlobalVariables.LanguageID))
        <div class="col-md-4">
            @Html.CheckBoxFor(model => model.Record.IncludesDepartments, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Record.IncludesDepartments, "", new { @class = "text-danger" })
        </div>
    </div>



    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            <div class="pull-right">
                @Html.ActionLink("Cancel", "CheckFilesList", null, new { @class = "btn btn-thinkware" }) <input type="submit" value="Add" class="btn btn-thinkware" />
            </div>
        </div>
    </div>
</div>

}

@section scripts{

    <script>
        $("#cpsetupcheckedit").validate();
    </script>
    <script>
        $(document).ready(function () {
            $("#Record.MaxPaycodes", "#Record.MaxDeposits", "#Record.MaxBenefits", "#Record.MaxDeductions", "#Record.MaxStateTaxes", "#Record.MaxLocalTaxes").kendoNumericTextBox({
                min: 1,
                max: 50,
                step: 1,
                format: "#",
                decimals: 0
            });

        });
    </script>
}