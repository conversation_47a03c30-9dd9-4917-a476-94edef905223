@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@{
    ViewBag.Title = "Licenses";
    ViewBag.ParentCrumb = "Human Resources,HR";
}



<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-6 col-sm-6">
            <div class="text-right">

            </div>
        </div>
    </div>
</div>
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div class="contacts-toolbar" style="padding-bottom: 10px;">
        <div class="toolbar">
            <div class="row">
                <div class="col-md-3 pull-left">
                    <div class="input-group">
                        <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                        <input type="text" class="form-control" id='FieldFilter' placeholder="Search License ID">
                    </div>
                </div>

            </div>
        </div>
    </div>
    <button type="button" class="btn btn-thinkware" title="Export to Excel" onclick="ExportRequests()">Export to Excel</button>

    <div class="table-bottom">
        @(Html.Kendo().Grid<DarwiNet2._0.Data.LicenseGroup>()
              .Name("grid")
              .Columns(columns =>
              {
                  columns.Bound(e => e.LicenseID).Width(300).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("LicenseID", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("LicenseID", GlobalVariables.LanguageID));
                  columns.Bound(e => e.Description).Width(300).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID));
              })
              .ColumnMenu()
              .Sortable()
              .Pageable()
              //.ToolBar(tools => tools.Excel())
              //.Excel(e => e.AllPages(true))
              //.Excel(excel => excel
                  //.FileName("Kendo UI Grid Export.xlsx")
                 // .Filterable(true)
                  //.ProxyURL(Url.Action("Excel_Export_Save", "HR")))
              .Groupable()
              .Filterable()
              .Scrollable()
              .ClientDetailTemplateId("template")
              //.Events(e => e.ExcelExport("parent_excelExport"))
              .HtmlAttributes(new { style = "height: 647px" })
              .DataSource(dataSource => dataSource
                  .Ajax()
                  .PageSize(15)
                  .Read(read => read.Action("GetLicenses_Read", "HR"))
              )
              .Events(events => events.DataBound("dataBound"))
        )
        <script id="template" type="text/kendo-tmpl">
            @(Html.Kendo().Grid<DarwiNet2._0.Data.LicenseHR>()
                  .Name("grid_#=EncryptedID#") // template expression, to be evaluated in the master context
                  .Columns(columns =>
                  {
                      columns.Bound(o => o.EmployeeName).Title(FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID) });
                      columns.Bound(o => o.LicenseNumber).Title(FieldTranslation.GetLabel("LicenseNumber", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("LicenseNumber", GlobalVariables.LanguageID) });
                      columns.Bound(o => o.EffectiveDate).Title(FieldTranslation.GetLabel("EffectiveDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EffectiveDate", GlobalVariables.LanguageID) });
                      columns.Bound(o => o.ExpirationDate).Title(FieldTranslation.GetLabel("ExpirationDate", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("ExpirationDate", GlobalVariables.LanguageID) });
                      columns.Bound(o => o.Inactive).Title(FieldTranslation.GetLabel("Inactive", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Inactive", GlobalVariables.LanguageID) });
                  })
                  .DataSource(dataSource => dataSource
                      .Ajax()
                      .PageSize(10)
                      .Read(read => read.Action("GetEELicenses_Read", "HR", new { L = "#=EncryptedID#" }))
                  )
                  .Events(e => e.ExcelExport("child_excelExport").Change("OnChange"))
                  .Pageable()
                  .Sortable()
                  .Selectable()
                  .ToClientTemplate()
            )
        </script>
        <script>
        function dataBound() {
            // this.expandRow(this.tbody.find("tr.k-master-row").first());
        }

        function OnChange(arg) {
            var selected = $.map(this.select(), function(item) {
                return $(item).text();
            });
            var EmployeeId = this.dataItem(this.select()).EmployeeID;
            var PayCode = this.dataItem(this.select()).LicenseNumber;
            var url = '@Url.Action("Index", "Employees", new {A = "EditLicenses"})';
            url = url + "&c=" + PayCode + "&e=" + EmployeeId;
            window.location.href = url;

        }
        </script>



        <script>
        var detailExportPromises = [];
        var dataSource = new kendo.data.DataSource({
            transport: {
                read: {
                    url: "@Url.Action("GetEELicenses_Read", "HR")",
                    datatype: "json",
                    data: { L: "Dummy", AllDetail: true }
                }
            },
            schema: {
                data: "Data",
                total: "Total",
                error: "Errors"
            }
        });

        dataSource.read();

        function dataBound() {
            detailExportPromises = [];

            //this.expandRow(this.tbody.find("tr.k-master-row").first());
        }

        function child_excelExport(e) {
            e.preventDefault();
        }

        function parent_excelExport(e) {
            e.preventDefault();

            var workbook = e.workbook;

            detailExportPromises = [];

            var masterData = e.data;

            for (var rowIndex = 0; rowIndex < masterData.length; rowIndex++) {
                exportChildData(masterData[rowIndex].EmployeeID, rowIndex);
            }

            // wait for all detail grids to finish exporting
            $.when.apply(null, detailExportPromises)
                .then(function() {
                    // get the export results
                    var detailExports = $.makeArray(arguments);

                    // sort by masterRowIndex
                    detailExports.sort(function(a, b) {
                        return a.masterRowIndex - b.masterRowIndex;
                    });
                    for (var c = 0; c < workbook.sheets[0].columns.length; c++) {
                        workbook.sheets[0].columns[c].autoWidth = true;
                        workbook.sheets[0].columns[c].width = null;
                    }
                    // add an empty column
                    workbook.sheets[0].columns.unshift({ width: 30 });

                    // colSpan the Header rows.
                    for (var i = 0; i < workbook.sheets[0].rows.length; i++) {
                        workbook.sheets[0].rows[i].cells[0].colSpan = 2;
                    }
                    // prepend an empty cell to each row
                    for (var i = 0; i < workbook.sheets[0].rows.length; i++) {
                        workbook.sheets[0].rows[i].cells.unshift({});
                        for (var j = 0; j < workbook.sheets[0].columns.length; j++) {
                            workbook.sheets[0].rows[i].cells[j].background = "#244061";
                            workbook.sheets[0].rows[i].cells[j].format = "[White]";
                        }
                        workbook.sheets[0].rows[i].cells[1].colSpan = 2;
                    }

                    // merge the detail export sheet rows with the master sheet rows
                    // loop backwards so the masterRowIndex doesn't need to be updated
                    for (var i = detailExports.length - 1; i >= 0; i--) {
                        var masterRowIndex = detailExports[i].masterRowIndex + 1;

                        var sheet = detailExports[i].sheet;
                        for (var c = 0; c < sheet.columns.length; c++) {
                            sheet.columns[c].autoWidth = true;
                            sheet.columns[c].width = null;
                        }

                        // prepend TWO empty cells to each row
                        for (var ci = 0; ci < sheet.rows.length; ci++) {
                            if (sheet.rows[ci].cells[0].value) {
                                sheet.rows[ci].cells.unshift({ width: 15 });
                                sheet.rows[ci].cells.unshift({});
                            }
                        }

                        // insert the detail sheet rows after the master row
                        [].splice.apply(workbook.sheets[0].rows, [masterRowIndex + 1, 0].concat(sheet.rows));
                    }

                    // save the workbook
                    var date = new Date;
                    var day = date.getDate();
                    var month = date.getMonth() + 1;
                    var year = date.getFullYear();
                    var name = "@ViewBag.title" + " - " + day + month + year + ".xlsx";
                    workbook.sheets[0].filter = null;

                    workbook.sheets[0].title = "Licenses";
                    kendo.saveAs({
                        dataURI: new kendo.ooxml.Workbook(workbook).toDataURL(),
                        fileName: name
                    });
                });
        }

        function exportChildData(EmployeeID, rowIndex) {
            var deferred = $.Deferred();
            detailExportPromises.push(deferred);

            var rows = [
                {
                    cells: [
                        { value: "EmployeeName" },
                        { value: "LicenseNumber" },
                        { value: "EffectiveDate" },
                        { value: "ExpirationDate" },
                        { value: "Inactive" }
                    ]
                }
            ];
            dataSource.filter({ field: "EmployeeID", operator: "eq", value: EmployeeID });

            var exporter = new kendo.ExcelExporter({
                columns: [
                    { field: "EmployeeName" },
                    { field: "LicenseNumber" },
                    { field: "EffectiveDate" },
                    { field: "ExpirationDate" },
                    { field: "Inactive" }
                ],
                dataSource: dataSource
            });

            exporter.workbook().then(function(book, data) {
                deferred.resolve({
                    masterRowIndex: rowIndex,
                    sheet: book.sheets[0]
                });
            });
        }
        </script>
    </div>
}
@section scripts{
    <script>
        //Filter on the grid
        $(document).ready(function () {
            $("#FieldFilter").keyup(function () {

                var value = $("#FieldFilter").val();
                var grid = $("#licenses").data("kendoGrid");

                if (value) {
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            { field: "LicenseID", operator: "contains", value: value }
                        ]
                    })
                } else {
                    grid.dataSource.filter({});
                }
            });
        });
    </script>
}
@using (Html.BeginForm("ExportFile1", "HR", FormMethod.Post, new { name = "exportFileForm", id = "exportFileForm" }))
{
    <input type="hidden" id="filePath" name="filePath" />
    
}
@*DG - 7341 -09/10/2021 - START*@
<script>
    function ExportRequests() {
        $('#loadingSpinner').show(100);
        var flt = "";
        var order = "";
        var grid = $("#grid").data("kendoGrid");
        if (grid.dataSource.filter()) {
            for (var i = 0; i < grid.dataSource.filter().filters.length; i++) {
                var filter = grid.dataSource.filter().filters[i];
                if (flt != '') flt += " " + grid.dataSource.filter().logic + " ";
                if (filter.operator == 'startswith') {
                    flt += "data." + filter.field + " " + "like " + "'" + filter.value + "%" + "'";
                }
                if (filter.operator == 'endswith') {
                    flt += "data." + filter.field + " " + "like " + "'" + "%" + filter.value + "'";
                }
                if (filter.operator == 'contains') {
                    flt += "data." + filter.field + " " + "like " + "'" + "%" + filter.value + "%" + "'";
                }
                if (filter.operator == 'doesnotcontain') {
                    flt += "data." + filter.field + " " + "like " + "'" + "%" + filter.value + "%" + "'";
                }
                if (filter.operator == 'eq') {
                    flt += "data." + filter.field + " " + "=" + "'" + filter.value + "'";
                }
                if (filter.operator == 'neq') {
                    flt += "data." + filter.field + " " + "<>" + "'" + filter.value + "'";
                }
            }
        }
        if (grid.dataSource.sort()) {

            // Get the datasource bound to the grid
            var ds = grid.dataSource;
            // Get current sorting
            var sort = ds.sort();
            // Display sorting fields and direction
            if (sort) {
                for (var i = 0; i < sort.length; i++) {
                    order += "order by " + "data." + sort[i].field + " " + sort[i].dir;
                }
            }
        }
        $.ajax({
            traditional: true,
            url: "@Url.Action("ExportRequestsLicensesList", "HR")",
            type: "POST",
            contentType: "application/json; charset=utf-8",
            data: JSON.stringify({ 'flt': flt, 'order': order }),
            success: function (data) {
                if (data) {
                    $('#filePath').val(data);
                    $("#exportFileForm").submit();
                }
                $('#loadingSpinner').hide(250);
            },
            failure: function (response) {
                $('#loadingSpinner').hide(250);
                alert(response.responseText);
            },
            error: function (errorData) {
                $('#loadingSpinner').hide(250);
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });

    }

</script>
@*DG - 7341 -09/10/2021 - END*@