@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Data
@using DataDrivenViewEngine.Models.Core

@{
    ViewBag.Title = "On-Boarding Profiles";

    string clientTemplate =
        "<div class=' icon-center'>" +
        "<a href='" + Url.Action("Edit", "OBProfile", new { id = "#= ProfileID#" }) + "' title='Edit' class=''><i class='icon-edit fa fa-pencil fa-fw fa-lg'></i></a>";

    if (GlobalVariables.AvailableRoles != null)
    {
        clientTemplate += "<a href='" + Url.Action("Copy", "OBProfile", new { i = "#= ProfileID#" }) + "' title='Replicate' class='' data-toggle='modal' data-target='\\#copyOBProfileModal' data-remote='false'><i class='icon-replicate fa fa-clipboard fa-fw fa-lg'></i></a>";
        clientTemplate += "</div>";
    }
}

@section styles{
    <link href="~/Content/sweetalert.css" rel="stylesheet" />
}
@if (ViewBag.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    <div class="modal fade" id="copyOBProfileModal" tabindex="-1" role="dialog" aria-labelledby="copyOBProfileModal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="copyOBProfileModalLabel">Copy On Boarding Profile</h4>
                </div>
                <div class="modal-body">
                    Generating...
                </div>
            </div>
        </div>
    </div>

    if (ViewBag.IsSampleCompany)
    {
        <div class="alert alert-danger alert-dismissible" role="alert" id="globalFail">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <p>This screen is unavailable in this company. Please log into a company that has been synced from Darwin</p>
        </div>
    }
    else
    {
        <div class="company-info">
            <div class="row" style="padding-bottom: 10px;">
                <div class="col-md-6 col-sm-6">
                    <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                    <div class="colored-line-left"></div>
                </div>
                <div class="col-md-6 col-sm-6">
                    <div class="text-right">

                    </div>
                </div>
            </div>
        </div>
        if (!ViewData.ModelState.IsValid)
        {
            @Html.Partial("~/Views/Home/_ErrorNotifications.cshtml")
        }
        <style>
        </style>
        <div class="toolbar">

            <div class="row">
                <div class="col-md-3 pull-left">
                    <div class="input-group">
                        <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                        <input type="text" class="form-control" id='FieldFilter' placeholder="Search Profile Name">
                    </div>
                </div>
                <p class="create-pad pull-right">
                    <a href="@Url.Action("Create", "OBProfile")" class="btn btn-thinkware"><i class="fa fa-plus fa-lg fa-fw"></i>Add New Profile</a>

                </p>


            </div>
        </div>
        <div class="table-bottom table-icons">
            <div id="clientsDb" class="onboarding grid-tooltips">
                @(Html.Kendo().Grid<OBProfile>()
              .Name("grid")
              .Columns(columns =>
              {
                  columns.Template(@<text>

                </text>)
                      .ClientTemplate(clientTemplate).Width(120);

                  columns.Bound(obcs => obcs.ProfileName).HeaderHtmlAttributes(new { @title = "Profile Name" }).Title(FieldTranslation.GetLabel("ProfileName", GlobalVariables.LanguageID));
                  columns.Bound(obcs => obcs.EEFinalize).ClientTemplate("#= EEFinalize ? 'Yes' : 'No' #").HeaderHtmlAttributes(new { @title = "Employee Finalize" }).Title(FieldTranslation.GetLabel("EEFinalize", GlobalVariables.LanguageID));

                  columns.Template(@<text>

                </text>)
                      .ClientTemplate(
                      "<div class='icon-center'><a href='\\#' id='delete' data-name='#= ProfileName#' data-id='#= ProfileID#'><i class='icon-red fa fa-times fa-fw fa-lg' title='Delete'></i></a></div>"

                      )

                      .Title("Action").Width(80).HeaderHtmlAttributes(new { @class = "header-center" });


              })

            .HtmlAttributes(new { style = "height: 647px" })
            .Pageable()
            .Sortable()
            .Filterable()
            .Groupable()
            .Scrollable()
            .Resizable(resize => resize.Columns(true))
            .DataSource(dataSource => dataSource
                .Ajax()
                .Read(read => read.Action("OBProfiles_Read", "OBProfile"))
                .PageSize(15)
                .Update(update => update.Action("Edit", "OBProfile"))
                .Model(model => model.Id(obcs => obcs.ProfileID))
            )
                )
            </div>
        </div>
        @section scripts{
            <script src="~/Scripts/bootbox.min.js"></script>
            @*Field Filter*@
            <script>
                $(document).ready(function () {
                    $("#FieldFilter").keyup(function () {

                        var value = $("#FieldFilter").val();
                        var grid = $("#grid").data("kendoGrid");

                        if (value) {
                            grid.dataSource.filter({
                                logic: "or",
                                filters: [
                                    { field: "ProfileName", operator: "contains", value: value }
                                ]
                            })
                        } else {
                            grid.dataSource.filter({});
                        }
                    });
                });
            </script>
            @*Delete Profile*@
            <script>
                $(document).on("click", "td #delete", function (e) {
                    var deletedID = $(this).attr('data-id')
                    var url = "@Url.Action("Delete", "OBProfile", null)"
                    var name = $(this).attr('data-name')
                    bootbox.dialog({
                        message: "Are you sure you want to delete: " + "<strong>" + name + "</strong>",
                        title: "Delete Profile",
                        buttons: {
                            main: {
                                label: "Cancel",
                                className: "btn-primary",
                                callback: function () {
                                    //Example.show("Primary button");
                                }
                            },
                            danger: {
                                label: "Delete",
                                className: "btn-danger",
                                callback: function () {
                                    window.location.href = url + "/" + deletedID;
                                }
                            }
                        }
                    });
                });
            </script>
        }
    }

    <script type="text/javascript">

        $("#copyOBProfileModal").on("show.bs.modal", function (e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-body").load(link.attr("href"));
        });
    </script>


}
