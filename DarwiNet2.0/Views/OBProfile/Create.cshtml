@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model DarwiNet2._0.Data.OBProfile
@using OBSignatureType = DataDrivenViewEngine.Models.Core.enOBSignatureType
@using OBBankPresentation = DataDrivenViewEngine.Models.Core.enOBBankPresentation
@using OBSortOptions = DataDrivenViewEngine.Models.Core.enOBSortOptions
@{
    ViewBag.Title = "Create On-Boarding Profile - " + @GlobalVariables.ProfileName;
}
<script src="~/Scripts/jquery.validate.js"></script>
<div class="company-info">
    <div class="row" style="padding-bottom: 10px;">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-6 col-sm-6">
            <div class="text-right">

            </div>
        </div>
    </div>
</div>

@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "obprofilecreate" }))
{
    @*@Html.AntiForgeryToken()*@

    <div class="form-horizontal">

        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">

            @Html.LabelFor(model => model.ProfileName, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("ProfileName", GlobalVariables.LanguageID))

                @Html.HiddenFor(model => model.CompanyID, new { @class = "form-control", @readonly = "readonly" })
            <div class="col-md-4">

                @Html.TextBoxFor(model => model.ProfileName, new { @class = "field-required form-control", @required = "required", @maxlength = "30" })
                @Html.ValidationMessageFor(model => model.ProfileName, "", new { @class = "text-danger" })

            </div>
            @Html.LabelFor(model => model.SortOption, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("SortOption", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("SortOption", EnumHelper.GetSelectList(typeof(OBSortOptions)), new { id = "SortOption", name = "SortOption", @class = "form-control" })
                @*@Html.DropDownList("SortOption", (SelectList)ViewBag.SortTypes, new { id = "SortOption", name = "SortOption", @class = "form-control" })*@

                @Html.ValidationMessageFor(model => model.SortOption, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.EEFinalize, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EEFinalize", GlobalVariables.LanguageID))
            <div class="col-md-4">
                <div class="pull-left" data-toggle="tooltip" data-placement="right" title="Requires employee interaction to finalize their profile.">
                    @Html.EditorFor(model => model.EEFinalize)
                </div>
                @Html.ValidationMessageFor(model => model.EEFinalize, "", new { @class = "text-danger" })

            </div>
            @Html.LabelFor(model => model.Signature, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Signature", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.DropDownList("Signature", EnumHelper.GetSelectList(typeof(OBSignatureType)), new { id = "Signature", name = "Signature", @class = "form-control" })
                @*@Html.DropDownList("Signature", (SelectList)ViewBag.SignatureTypes, new { id = "Signature", name = "Signature", @class = "form-control" })*@

                @Html.ValidationMessageFor(model => model.Signature, "", new { @class = "text-danger" })
            </div>

        </div>



        <div class="form-group">
            @Html.LabelFor(model => model.MaskSSNInSign, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("MaskSSNInSign", GlobalVariables.LanguageID))
            <div class="col-md-4">
                <div class="pull-left" data-toggle="tooltip" data-placement="right" title="Mask SSN (xxx-xx-1234)">
                    @Html.EditorFor(model => model.MaskSSNInSign, new { htmlAttributes = new { @class = "form-control" } })
                </div>
                @Html.ValidationMessageFor(model => model.MaskSSNInSign, "", new { @class = "text-danger" })
            </div>
           @Html.LabelFor(model => model.AllowCodesOutsideOfPeriod, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Allow PayCodes Outside Of Period", GlobalVariables.LanguageID))
        <div class="col-md-4">
            <div class="pull-left" data-toggle="tooltip" data-placement="right" title="Allow user to assign paycodes that do not match the employees pay period frequency.">
                @Html.EditorFor(model => model.AllowCodesOutsideOfPeriod, new { htmlAttributes = new { @class = "form-control" } })
            </div>
            @Html.ValidationMessageFor(model => model.AllowCodesOutsideOfPeriod, "", new { @class = "text-danger" })
        </div>

        </div>


        

        <div class="form-group">
            @Html.LabelFor(model => model.DayToDueDate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("DayToDueDate", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.TextBoxFor(model => model.DayToDueDate, new { @class = "form-control", @type = "number", @min = "0" })
                @Html.ValidationMessageFor(model => model.DayToDueDate, "", new { @class = "text-danger" })
            </div>

            @Html.LabelFor(model => model.NotifyBeforeDueDate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("NotifyBeforeDueDate", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.TextBoxFor(model => model.NotifyBeforeDueDate, new { @class = "form-control", @type = "number", @min = "0" })
                @Html.ValidationMessageFor(model => model.NotifyBeforeDueDate, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group plain-editor">
            @Html.LabelFor(model => model.VerificationAgreement, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Default Form Verification Agreement", GlobalVariables.LanguageID))
            <div class="col-md-10">
                @Html.TextAreaFor(model => model.VerificationAgreement, new { @class = "form-control", rows = "10", @id = "editor" })
                @Html.ValidationMessageFor(model => model.VerificationAgreement, "", new { @class = "text-danger" })
            </div>
        </div>

       <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <div class="pull-right">

                    @Html.ActionLink("Cancel", "Index", null, new { @class = "btn btn-thinkware" })
                    <input type="submit" value="Create" class="btn btn-thinkware" />
                </div>
            </div>
        </div>
    </div>
}

@section scripts{
    <script>
        $("#obprofilecreate").validate();
    </script>
<script>
    $(document).ready(function () {
        // create Editor from textarea HTML element with default set of tools
        kendo.ui.editor.ColorTool.prototype.options.palette = "basic";
        $("#editor").kendoEditor({
            resizable: {
                content: true,
                toolbar: true
            },
            tools: [

            "formatting",
                "bold",
            "italic",
            "underline",
            "justifyLeft",
            "justifyCenter",
            "justifyRight",
            "justifyFull",
            "insertUnorderedList",
            "insertOrderedList",
            "indent",
            "outdent",
            "createLink",
            "unlink",
            "foreColor",
            "backColor"
            ],
            paste: function (ev) {
                ev.html = $(ev.html).text();
            }
        });
    });
</script>


}
