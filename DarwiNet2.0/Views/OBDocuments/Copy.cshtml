@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Data
@model DarwiNet2._0.ViewModels.OBDocumentCopyVM

@{
    Layout = "~/Views/Shared/_LayoutModal.cshtml";
}

@Html.AntiForgeryToken()

@using (Html.BeginForm("Copy", "OBDocuments", FormMethod.Post, new { id = "copyForm", name = "copyForm" }))
{
    <div style="padding-left: 25px; padding-right: 25px;">
        <div class="row">
            <div class="form-group col-md-3">
                <label class="control-label">Choose a Company</label>
            </div>
            <div class="form-group col-md-4">
                @Html.DropDownListFor(model => model.CopyToCompanyID, Model.CompaniesSelectListItems, "-- Select Company --", new { id = "CopyToCompanyID", name = "CopyToCompanyID", @class = "form-control" })
                <div id="copyError" name="copyError" class="text-danger">
                    <span>A company must be selected</span>
                </div>
            </div>
            <div class="form-group col-md-2">
                <input type="button" id="btnCopy" name="btnCopy" class="btn btn-thinkware" value="Copy" />
            </div>
            <div class="form-group col-md-3 pull-right">
            </div>
        </div>
    </div>

    @Html.HiddenFor(model => model.CopyToCompanyID);
    @Html.HiddenFor(model => model.DocumentID);
    @Html.HiddenFor(model => model.DocumentName);
}


<script src="~/Scripts/bootbox.min.js"></script>
<script src="~/Scripts/jquery.datetimepicker.js"></script>
<link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
<script src="~/Scripts/MaskedInput.js"></script>

<script>
    $(document).ready(function () {
        $('#copyError').hide();
    });

    $('#btnCopy').on('click', function () {
        var dropdown = $('#CopyToCompanyID');

        // Confirm that a company has been selected
        if (dropdown.val() == '' || dropdown.val() == null || dropdown.val() === 'undefined') {
            $('#copyError').show();
            return false;
        }

        $('#copyError').hide();
        $('#copyForm').submit();
        return true;
    });

    $('#CopyToCompanyID').on('click', function () {
        $('#copyError').hide();
    });
</script>

