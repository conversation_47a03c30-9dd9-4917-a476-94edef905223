@using DarwiNet2._0.Extensions;

@model DarwiNet2._0.ViewModels.TimeSheets.TimeSheetCreateViewModel

@{
    ViewBag.Title = "Create Timesheet";
}

<link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />

<div id="VueInstance">
    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@ViewBag.Title</p>
                <div class="colored-line-left"></div>
            </div>
        </div>
    </div>

    <div class="form-horizontal">
        <div class="form-group">
            <label for="Profile" class="control-label col-md-2">Profile</label>
            <div class="col-md-4">
                <select id="Profile" v-model="profile" class="form-control">
                    <option v-for="x in profileList" :value="x.value">{{x.text}}</option>
                </select>
            </div>

            <label for="Name" class="control-label col-md-2">Name</label>
            <div class="col-md-4">
                <input id="Name" type="text" v-model="name" class="form-control" />
            </div>
        </div>

        <div class="form-group">
            <label for="DateFrom" class="control-label col-md-2">Date From</label>
            <div class="col-md-4">
                <input id="DateFrom" type="text" v-model="dateFrom" style="width: 100%;" />
            </div>

            <label for="DateTo" class="control-label col-md-2">Date To</label>
            <div class="col-md-4">
                <input id="DateTo" type="text" v-model="dateTo" style="width: 100%;" />
            </div>
        </div>

        <div class="form-group">
            <label for="CheckDate" class="control-label col-md-2">Check Date</label>
            <div class="col-md-4">
                <input id="CheckDate" type="text" v-model="checkDate" style="width: 100%;" />
            </div>

            <label for="RowsPerEE" class="control-label col-md-2">Rows Per Employee</label>
            <div class="col-md-4">
                <input id="RowsPerEE" type="text" v-model="rowsPerEE" class="form-control" style="width: 100%;" />
            </div>
        </div>

        <div class="form-group">
            <label for="SortBy" class="control-label col-md-2">Sort By</label>
            <div class="col-md-4">
                <select id="SortBy" v-model="sortBy" class="form-control">
                    <option v-for="x in sortByList" :value="x.value">{{x.text}}</option>
                </select>
            </div>

            <label for="AutoSave" class="control-label col-md-2">Auto Save</label>
            <div class="col-md-4 check-down">
                <input id="AutoSave" type="checkbox" v-model="autoSave" />
            </div>
        </div>

        <div class="form-group">
            <label for="DeptDescr" class="control-label col-md-2">Display Department Description</label>
            <div class="col-md-4 check-down">
                <input id="DeptDescr" type="checkbox" v-model="deptDescr" />
            </div>

            <label for="PosDescr" class="control-label col-md-2">Display Position Description</label>
            <div class="col-md-4 check-down">
                <input id="PosDescr" type="checkbox" v-model="posDescr" />
            </div>
        </div>

        <div class="form-group" v-if="canImport">
            <label class="control-label col-md-2">Select TimeSheet Import file (Optional)</label>
            <div class="col-md-4">
                <input type="file" id="file" name="file" />
            </div>
        </div>

        <div class="form-group">
            <label for="Comment" class="control-label col-md-2">Comment</label>
            <div class="col-md-10">
                <textarea id="Comment" rows="5" v-model="comment" class="form-control"></textarea>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    <button type="button" id="btnCancel" class="btn btn-thinkware" @@click="onCancelClick">Cancel</button>
                    <button type="button" id="btnSave" class="btn btn-thinkware" @@click="onSaveClick">Save</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="createTimesheetModal" tabindex="-1" role="dialog" aria-labelledby="createTimesheetLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="createTimesheetLabel">Creating Timesheet Data...</h4>
                </div>
                <div class="modal-body">
                    <div class="text-center"><i class="fa fa-spinner fa-spin fa-5x"></i></div>
                    <br /><br />
                </div>
            </div>
        </div>
    </div>
</div>

<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>

<script type="text/javascript">
    var vm = new VueInstance("VueInstance", {
        data: {
            payrollNumber: '@Model.PayrollNumber',
            profile: '',
            profileList: @Html.Raw(Model.Profiles.ToJson()),
            name: '',
            dateFrom: '@Model.DateFrom.GetValueOrDefault().ToString("MM/dd/yyyy")',
            dateTo: '@Model.DateTo.GetValueOrDefault().ToString("MM/dd/yyyy")',
            checkDate: '@Model.CheckDate.GetValueOrDefault().ToString("MM/dd/yyyy")',
            rowsPerEE: 1,
            sortBy: @Model.SortBy,
            sortByList: @Html.Raw(Model.SortOptions.ToJson()),
            autoSave: false,
            deptDescr: false,
            posDescr: false,
            canImport: '@Model.CanImport' === 'True',
            comment: ''
        },
        mounted: function () {
            var self = this;
            self.initializeDates();
        },
        methods: {
            initializeDates: function () {
                var self = this;
                $('#DateFrom, #DateTo, #CheckDate').kendoMaskedDatePicker({
                    change: function () {
                        var value = ThinkwareCommon.formatDate(this.value());
                        switch (this.element.context.id) {
                            case 'DateFrom':
                                self.dateFrom = value;
                                break;
                            case 'DateTo':
                                self.dateTo = value;
                                break;
                            case 'CheckDate':
                                self.checkDate = value;
                                break;
                        }
                    }
                }).parent().parent().removeClass('k-header');
            },
            onSaveClick: function () {
                var self = this;
                var $modal = $('#createTimesheetModal');
                $modal.modal('show');
                ThinkwareCommon.ajax.postJson('@Url.Action("Create", "TimeSheet")', {
                    data: {
                        payrollNumber: self.payrollNumber,
                        profileId: self.profile,
                        timeSheetName: self.name,
                        dateFrom: self.dateFrom,
                        dateTo: self.dateTo,
                        checkDate: self.checkDate,
                        rowsPerEE: self.rowsPerEE,
                        sortBy: self.sortBy,
                        autoSave: self.autoSave,
                        deptDescr: self.deptDescr,
                        posDescr: self.posDescr,
                        comment: self.comment
                    },
                    onSuccess: function (response) {
                        if (response.status != ResponseStatus.SUCCESS) {
                            ThinkwareCommon.showErrorAlert(response.message);
                            $modal.modal('hide');
                            return;
                        }
                        ThinkwareCommon.showSuccessAlert(response.message);
                        window.location = `@Url.Action("Edit", "TS")?id=${response.data.timeSheetId}`;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        ThinkwareCommon.showErrorAlert(errorThrown);
                        $modal.modal('hide');
                    }
                });
            },
            onCancelClick: function () {
                window.location = '@Url.Action("Index", "TS")';
            }
        }
    });
</script>
