@using DarwiNet2._0.DNetSynch;

<script type="text/x-template" id="invoice-notes-form-template">
    <div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="subject">Subject</label>
                    <input type="text" name="Subject" v-model="viewModel.Subject" class="form-control" maxlength="50" required />
                </div>
                <div class="margin-bottom-15">
                    <label for="FromDate">From Date</label>
                    <v-date-picker class="" v-model="viewModel.FromDate" :masks="masks" :popover="{ visibility: 'hidden' }" :mode="'date'" :update-on-input="false">
                        <template v-slot="{ inputValue, togglePopover, inputEvents }">
                            <div class="input-group date">
                                <span class="input-group-addon" @@click="togglePopover()"><i class="glyphicon glyphicon-th"></i></span>
                                <input :value="inputValue"
                                       class="form-control"
                                       v-on="inputEvents"
                                       required />
                            </div>
                        </template>
                    </v-date-picker>
                </div>
                <div class="margin-bottom-15">
                    <label for="ToDate">To Date</label>
                    <v-date-picker class="" v-model="viewModel.ToDate" :masks="masks" :popover="{ visibility: 'hidden' }" :mode="'date'" :update-on-input="false">
                        <template v-slot="{ inputValue, togglePopover, inputEvents }">
                            <div class="input-group date">
                                <span class="input-group-addon" @@click="togglePopover()"><i class="glyphicon glyphicon-th"></i></span>
                                <input :value="inputValue"
                                       class="form-control"
                                       v-on="inputEvents"
                                       required />
                            </div>
                        </template>
                    </v-date-picker>
                </div>
                <div class="margin-bottom-15" v-if="isClient != 'True'">
                    <input type="checkbox" v-model="viewModel.ShowClient" /> Show Client
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="Notes">Notes</label>
                    <textarea name="Notes" class="form-control" v-model="viewModel.Notes" style="resize: none" rows="12" required maxlength="200"></textarea>
                </div>
            </div>
        </div>
    </div>
</script>

<style>
    *:required:invalid {
        border-color: #dc3545;
    }
</style>

<script type="text/javascript" id="invoice-notes-form-script">
    var InvoiceNotesFormComponent = VueComponent('invoice-notes-form', {
        props: {
            viewModel: {
                type: Object,
                default: () => ({})
            },
            errors: {
                type: Object,
                default: () => ({})
            }
        },
        data: function () {
            return {
                isClient: '@GlobalVariables.CurrentUser.ClientLevelEnabled',
                onlyDate: false,
                date: new Date(),
                masks: {
                    input: 'MM/DD/YYYY'
                },
                events: {
                    submit: 'submit-form'
                },
            }
        },
        components: [
        ],
        methods: {
            display: function (id) {
                if (this.errors === undefined || this.errors[id] === undefined)
                    return false;
                return true;
            },
            errorMsg: function (id) {
                if (this.errors === undefined || this.errors[id] === undefined)
                    return '';
                return this.errors[id][0];
            }
        }
    });

</script>
