@using DarwiNet2._0.Extensions;
@using Thinkware.Pay360.Payroll;

<style type="text/css" scoped>
    .modal-body {
        margin: 0px !important;
    }

    .active-reset {
        background-color: white !important;
        color: black !important;
    }

    .payroll-modal-styles .modal-wrapper .modal-container {
        width: 80%;
    }
</style>

<link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.2/animate.min.css"
      integrity="sha256-PHcOkPmOshsMBC+vtJdVr5Mwb7r0LkSVJPlPrp/IMpU="
      crossorigin="anonymous" />

<script type="text/x-template" id="payroll-notes-comp-template">
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-thinkware">
                <div class="panel-heading">{{noteView}} Notes</div>
                <div class="panel-body">
                    <div class="col-md-12">
                        <div class="row margin-10">
                            <div class="btn-group btn-group-lg radioBtn" role="group" aria-label="Payroll/Invoices">
                                <button type="button" class="btn btn-primary" v-bind:class="[noteView == '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.PAYROLL' ? 'active' : 'notActive']" @@click="noteView = '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.PAYROLL'">Payroll</button>
                                <button type="button" class="btn btn-primary" v-bind:class="[noteView == '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.INVOICE' ? 'active' : 'notActive']" @@click="noteView = '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.INVOICE'">Invoice</button>
                            </div>
                            <div class="pull-right">
                                <button class="btn btn-success margin-top-10 margin-left-10 margin-bottom-10" @@click="showModal" :disabled="isBusy">New {{noteView}} Note</button>
                            </div>
                        </div>
                        <div v-if="noteView == '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.PAYROLL'">
                            <payroll-notes-table :note-view="noteView"
                                                 :payroll-notes-settings="payrollProfileSettingsHeader"
                                                 :company-id="companyId"
                                                 :client-id="clientId"
                                                 :profile-id="profileId"
                                                 :client-name="clientName"
                                                 :is-busy="isBusy"
                                                 :expanded="isExpanded"
                                                 @@delete-click="onDeleteClick"
                                                 @@complete-click="onCompleteClick"
                                                 ref="payrollNotesTable" />
                        </div>
                        <div v-else-if="noteView == '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.INVOICE'">
                            <payroll-notes-table :note-view="noteView"
                                                 :payroll-notes-settings="payrollProfileSettingsHeader"
                                                 :company-id="companyId"
                                                 :client-id="clientId"
                                                 :profile-id="profileId"
                                                 :client-name="clientName"
                                                 :is-busy="isBusy"
                                                 :expanded="isExpanded"
                                                 @@delete-click="onDeleteClick"
                                                 @@complete-click="onCompleteClick"
                                                 ref="invoiceNotesTable" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <modal v-show="showPayrollModal"
            class="payroll-modal-styles"
            @@close-click="clearForm">
            <template v-slot:header>
                <center><strong>New Payroll Note</strong></center>
            </template>
            <template v-slot:body>
                <payroll-notes-form :view-model="formData"
                                    :errors="errors"
                                    :company-id="companyId"
                                    :client-id="clientId"
                                    :profile-id="profileId"
                                    :key="reRender">
                </payroll-notes-form>
            </template>
            <template v-slot:footer>
                <button type="button" class="btn btn-danger" @@click="clearForm">Cancel</button>
                <button type="button" class="btn btn-success" @@click="createNote" :disabled="isBusy">Create</button>
            </template>
        </modal>
        <modal v-show="showInvoiceModal"
            @@close-click="clearForm">
            <template v-slot:header>
                <center><strong>New Invoice Note</strong></center>
            </template>
            <template v-slot:body>
                <invoice-notes-form :view-model="formData"
                                    :errors="errors">
                </invoice-notes-form>
            </template>
            <template v-slot:footer>
                <button type="button" class="btn btn-danger" @@click="clearForm">Cancel</button>
                <button type="button" class="btn btn-success" @@click="createNote" :disabled="isBusy">Create</button>
            </template>
        </modal>
        <thinkware-confirm-delete-modal v-show="showDeleteModal"
                                        :title="'Delete Note?'"
                                        v-on:delete="deleteNote"
                                        v-on:cancel="showDeleteModal = false">
            <template v-slot:body>
                Are you sure you want to delete this Note?
            </template>
        </thinkware-confirm-delete-modal>
    </div>
</script>

@Html.VueComponent("~/PayrollSchedule/modal")
@Html.VueComponent("~/Shared/thinkware-confirm-delete-modal")
@Html.VueComponent("~/PayrollNotes/payroll-notes-table")
@Html.VueComponent("~/PayrollNotes/payroll-notes-form")
@Html.VueComponent("~/PayrollNotes/invoice-notes-form")

<script src="https://cdn.jsdelivr.net/npm/vue-toastr/dist/vue-toastr.umd.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/v-calendar"></script>

<script type="text/javascript" id="payroll-notes-comp-script">

    var allEmployees = [];
    var PayrollNotesCompComponent = VueComponent('payroll-notes-comp', {
        props: {
            viewModel: {
                type: Object
            },
            companyId: {
                type: Number
            },
            clientId: {
                type: String
            },
            profileId: {
                type: String
            },
            payrollProfileSettingsHeader: {
                type: Object
            },
        },
        data: function () {
            return {
                isBusy: false,
                isExpanded: false,
                showPayrollModal: false,
                showInvoiceModal: false,
                showDeleteModal: false,
                noteView: '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.PAYROLL',
                clientName: this.payrollProfileSettingsHeader.ClientName,
                formData: {
                    NoteType: '',
                    EmployeeID: [],
                    Subject: '',
                    ToDate: new Date(),
                    FromDate: new Date(),
                    Notes: '',
                    AllSelected: false,
                    ShowClient: false,
                    PayrollNoteLevel: 'Employee'
                },
                errors: {},
                toBeDeleted: {
                    noteId: undefined,
                    index: undefined
                },
                reRender: 0
            }
        },
        methods: {
            createNote: function () {
                var self = this;
                var formData = self.formData;

                var formValidation = self.validateForm(formData);
                if (!formValidation.isValid) {
                    self.errors = formValidation.errors;
                    return;
                }

                self.isBusy = true;
                ThinkwareCommon.ajax.postJson('@Url.Action("Createv2", "PayrollNotes")', {
                    data:
                    {
                        CompanyID: self.companyId,
                        ClientID: self.clientId,
                        ProfileID: self.profileId,
                        EmployeeID: formData.EmployeeID,
                        NoteType: self.noteView,
                        Subject: formData.Subject,
                        FromDate: formData.FromDate,
                        ToDate: formData.ToDate,
                        Notes: formData.Notes,
                        AllSelected: formData.allSelected,
                        ShowClient: formData.ShowClient,
                        PayrollNoteLevel: formData.PayrollNoteLevel
                    },
                    onSuccess: function (response) {
                        self.showToastMessage(response.message, response.status);

                        if (response.status === ResponseStatus.SUCCESS) {
                            self.refreshTable();
                        }
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        self.showToastMessage(errorThrown);
                    },
                    onComplete: function () {
                        self.isBusy = false;
                    }
                });
                this.clearForm();
            },
            onCompleteClick: function (id, completed) {
                var self = this;

                self.isBusy = true;
                ThinkwareCommon.ajax.postJson('@Url.Action("Complete", "PayrollNotes")', {
                    data: {
                        companyId: self.companyId,
                        clientId: self.clientId,
                        profileId: self.profileId,
                        noteId: id,
                        isComplete: completed
                    },
                    onSuccess: function (result) {
                        self.showToastMessage(result.message, result.status);
                        if (result.status == ResponseStatus.SUCCESS) {
                            self.refreshTable();
                        }
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        self.showToastMessage(errorThrown);
                    },
                    onComplete: function () {
                        self.isBusy = false;
                    }
                });
            },
            onDeleteClick: function (noteId) {
                var self = this;

                self.toBeDeleted.noteId = noteId;
                self.showDeleteModal = true;
            },
            deleteNote: function () {
                var self = this;

                self.isBusy = true;
                ThinkwareCommon.ajax.postJson('@Url.Action("Delete", "PayrollNotes")', {
                    data: {
                        companyId: self.companyId,
                        clientId: self.clientId,
                        profileId: self.profileId,
                        noteId: self.toBeDeleted.noteId
                    },
                    onSuccess: function (result) {
                        self.showToastMessage(result.message, result.status);
                        self.refreshTable();
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        self.showToastMessage(errorThrown);
                    },
                    onComplete: function () {
                        self.toBeDeleted.noteId = undefined;
                        self.toBeDeleted.index = undefined;
                        self.showDeleteModal = false;
                        self.isBusy = false;
                    }
                });
                self.clearForm();
            },
            clearForm: function () {
                var self = this;
                var formData = self.formData;

                self.errors = {}
                self.showPayrollModal = false;
                self.showInvoiceModal = false;
                self.showDeleteModal = false;

                formData.NoteType = '';
                formData.EmployeeID = [];
                formData.Subject = '';
                formData.ToDate = new Date();
                formData.FromDate = new Date();
                formData.Notes = '';
                formData.AllSelected = false;
                formData.PayrollNoteLevel = 'Employee';
                this.$refs.payrollNotesTable.allSelected = false;
                this.reRender++;
            },
            refreshTable: function () {
                var self = this;

                switch (self.noteView) {
                    case '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.PAYROLL':
                        self.$refs.payrollNotesTable.$refs.PayrollNotesTable.refreshTable();
                        break;

                    case '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.INVOICE':
                        self.$refs.invoiceNotesTable.$refs.InvoiceNotesTable.refreshTable();
                        break;

                    default:
                        throw 'Invalid note type.';
                        break;
                }
            },
            showModal: function () {
                var self = this;

                self.showInvoiceModal = false;
                self.showPayrollModal = false;
                self.showDeleteModal = false;

                switch (self.noteView) {
                    case '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.INVOICE':
                        self.showInvoiceModal = true;
                        break;

                    case '@Thinkware.Pay360.Constants.PayrollProfileNoteTypes.PAYROLL':
                        self.showPayrollModal = true;
                        break;

                    default:
                        throw 'Invalid note type.';
                }
            },
            showToastMessage: function (message, type) {
                var self = this;

                self.$toastr.defaultClassNames = ['animated', 'zoomInUp'];
                self.$toastr.defaultPosition = 'toast-bottom-right';

                switch (type) {
                    case ResponseStatus.SUCCESS:
                        self.$toastr.s(message);
                        break;

                    default:
                        self.$toastr.e(message);
                        break;
                }
            },
            validateForm: function (formData) {
                var result = {
                    isValid: false,
                    errors: {}
                };
                var requiredInputs = [
                    'Subject',
                    'FromDate',
                    'ToDate',
                    'Notes'
                ];
                if (this.noteView === "Payroll" && this.formData.PayrollNoteLevel == 'Employee') {
                    requiredInputs.push('EmployeeID');
                };

                requiredInputs.forEach(data => {
                    var value = formData[data];
                    if (data == 'FromDate' || data == 'ToDate') {
                        if (!value) {
                            result.errors[data] = ['Required'];
                            return;
                        } else if (new Date(formData.FromDate) > new Date(formData.ToDate)) {
                            result.errors.FromDate = ['Must be before To Date'];
                            result.errors.ToDate = ['Must be after From Date'];
                            return;
                        }
                    } else if (Object.keys(value).length == 0) {
                        result.errors[data] = ['Required'];
                        return;
                    }
                });

                if (Object.keys(result.errors).length > 0) {
                    result.isValid = false;
                    return result;
                }

                result.isValid = true;
                return result;
            },
        },
        components: [
            ModalComponent,
            ThinkwareConfirmDeleteModal,
            PayrollNotesTableComponent,
            PayrollNotesFormComponent,
            InvoiceNotesFormComponent,
        ],
    });

</script>