@model DataDrivenViewEngine.Models.Core.DataForm

@{
    ViewBag.Title = "Edit";
}

<h2>Edit</h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true)

    <fieldset>
        <legend>DataForm</legend>

        @Html.HiddenFor(model => model.Id)

        <div class="editor-label">
            @Html.LabelFor(model => model.Name)
        </div>
        <div class="editor-field">
            @Html.EditorFor(model => model.Name)
            @Html.ValidationMessageFor(model => model.Name)
        </div>

        <div class="editor-label">
            @Html.LabelFor(model => model.Description)
        </div>
        <div class="editor-field">
            @Html.EditorFor(model => model.Description)
            @Html.ValidationMessageFor(model => model.Description)
        </div>
        <div class="editor-label">
            @Html.LabelFor(model => model.SubmitName)
        </div>
        <div class="editor-field">
            @Html.EditorFor(model => model.SubmitName)
            @Html.ValidationMessageFor(model => model.SubmitName)
        </div>

        <div class="editor-label">
            @Html.LabelFor(model => model.SubmitUrl)
        </div>
        <div class="editor-field">
            @Html.EditorFor(model => model.SubmitUrl)
            @Html.ValidationMessageFor(model => model.SubmitUrl)
        </div>


        <div class="editor-label">
            @Html.LabelFor(model => model.Template)
        </div>
        <div class="editor-field">
            @Html.EditorFor(model => model.Template)
            @Html.ValidationMessageFor(model => model.Template)
        </div>

        <p>
            <input type="submit" value="Save" />
        </p>
    </fieldset>
}

<div>
    @Html.ActionLink("Back to List", "Index")
</div>

@section Scripts {
    @Scripts.Render("~/bundles/jqueryval")
}
