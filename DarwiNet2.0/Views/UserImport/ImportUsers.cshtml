@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core

@model DarwiNet2._0.ViewModels.UserImport.ImportUsersVM

@{
    ViewBag.Title = "Import Users";
    Layout = null;
}

<link href="@Url.Content("~/Content/css/Spinners.css")" rel="stylesheet" type="text/css" />

@*Security*@
@Html.AntiForgeryToken()

@if (Model.Access == MenuAccessLevel.NoAccess)
{
    @Html.Partial("~/Views/Shared/_NoAccessMessage.cshtml")
}
else
{
    using (@Html.BeginForm("ImportUsers", "UserImport", null, FormMethod.Post, new { id = "formImportUsers", name = "formImportUsers", encType = "multipart/form-data" }))
    {
        <div style="padding-left: 25px; padding-right: 25px;" >

            @*Choose File*@
            <div class="row">
                <div class="form-group">
                    <div class="col-md-12" style="padding: 0px 0px 25px 0px;">
                        @Html.TextBoxFor(model => model.File, new { type = "file", id = "file", name = "file" })
                        @Html.ValidationMessageFor(model => model.File, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label">Generate Password</label>
                    <div class="col-md-4">
                        <input type="checkbox" name="AutoPwd" id="AutoPwd" checked value="true" />
                    </div>
                    <div id="defaultpassword" style="display: none">
                        <label class="col-md-2 control-label">Set Password</label>
                        <div class="col-md-4">
                            <input type="text" name="DefaultPwd" id="DefaultPwd" value="" maxlength="15" />
                        </div>
                    </div>
                </div>
            </div>
                    @*Cancel/Submit Buttons*@
                    <div class="row">
                        <div class="form-group">
                            <div class="col-md-7"></div>
                            <div class="col-md-5" style="text-align: right;">
                                <button type="button" class="btn btn-thinkware" data-dismiss="modal" style="margin-right: 5px;">Cancel</button>
                                <button id="import" name="import" class="btn btn-thinkware has-spinner">
                                    <span class="spinner"><i class="fa fa-refresh fa-spin"></i></span> Import
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
    }
}

<script src="~/Scripts/jquery.validate.unobtrusive.js"></script>

<script type="text/javascript">
    @*Update validation UX immediately*@
    $('#file').on('change', function () {
        ValidateForm();
    });

    $('#AutoPwd').on('click', function () {
        $('#DefaultPwd').val('');
        var ischecked = this.checked;
        if (ischecked) {
            $('#defaultpassword').hide();
        }
        else
            $('#defaultpassword').show();
    });
    @*Set submit button loading spinner*@
    $('#import').click(function () {
        if (ValidateForm()) {
            $(this).attr('disabled', 'disabled');
            $(this).toggleClass('active');
            $('#formImportUsers').submit();
        }
    });

    function ValidateForm() {
        var validator = $('#formImportUsers').validate();
        validator.element($('#file'));

        if ($('#formImportUsers').valid()) {
            return true;
        }

        return false;
    }
                </script>
