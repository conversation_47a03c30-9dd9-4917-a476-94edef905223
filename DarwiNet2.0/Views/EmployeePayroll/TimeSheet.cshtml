
@{
    ViewBag.Title = "Time Sheet";
    ViewBag.ParentCrumb = "Payroll,Time Entry";
}

<div>
    @Html.Action("_EmployeeInfo", "Employees", new { e = ViewBag.EmployeeID })
</div>

<p style="font-size: 22px;">Time Sheet</p>
<div class="colored-line-left"></div>

<div class="row" style="padding-top: 35px;">
    <div class="col-md-12">
        <div class="col-lg-3">
            <div class="row">                
                <div class="col-md-6">
                    <span style="margin-bottom: 10px;">
                        <button class="btn btn-thinkware" id="submitBtn" style="margin-bottom:5px;width:120px">Submit</button>
                    </span>
                </div>
            </div>
            <div class="row">                
                <div class="col-md-6">
                    <span style="margin-bottom: 10px;">
                        <button class="btn btn-thinkware" id="consolidateBtn" style="margin-bottom:5px;width:120px">Consolidate</button>
                    </span>
                </div>
            </div>
            <div class="row">                
                <div class="col-md-6">
                    <span style="margin-bottom: 10px;">
                        <button class="btn btn-thinkware" id="addCommentBtn" style="margin-bottom:5px;width:120px">Add Comment</button>
                    </span>
                </div>
            </div>
        </div>
        <div class="col-lg-5">
            <div class="row">
                <div class="col-lg-12">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="control-label col-md-2">Comments:</label>
                            <div class="col-md-10">
                                <textarea name cols="10" rows="5" class="form-control" placeholder="Enter time sheet comments..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>    
</div>
<div class="row">
    @Html.Action("ProcessTimeEntry", "TE", new {id= ViewBag.TimeEntryID })
</div>
<div class="row" style="padding-top: 35px;">
    <div class="col-lg-12">
        <div class="col-lg-3">
            <div class="row">
                <div class="col-md-6">
                    <label>Employee Name:</label>
                </div>
                <div class="col-md-6">
                    <span style="padding-left: 10px;">
                        James Winton
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label>Employee ID:</label>
                </div>
                <div class="col-md-6">
                    <span style="padding-left: 10px;">
                        01-001-0001
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label>Employee SSN:</label>
                </div>
                <div class="col-md-6">
                    <span style="padding-left: 10px;">
                        ***********
                    </span>
                </div>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="row">
                <div class="col-md-6">
                    <label>Pay Period:</label>
                </div>
                <div class="col-md-6">
                    <span style="padding-left: 10px;">
                        12/14/15 - 12/18/15
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label>Status</label>
                </div>
                <div class="col-md-6">
                    <span style="padding-left: 10px;">
                        Edit
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <label>RDE Profile:</label>
                </div>
                <div class="col-md-6">
                    <span style="padding-left: 10px;">
                        REG
                    </span>
                </div>
            </div>
        </div>      
    </div>
</div>
<h2>Employee Time Sheet</h2>
<table id="timeSheetTbl">
    <thead>
        <tr>
            <th>Date</th>
            <th>Reg Hours</th>
            <th>OT Hours</th>
            <th>Department</th>
            <th>Position</th>
            <th>Code</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Monday - December 14, 2015</td>
            <td>0.00</td>
            <td>0.00</td>
            <td>
                <select>
                    <option>001001</option>
                    <option>001002</option>
                    <option>001003</option>
                </select>
            </td>
            <td>
                <select>
                    <option>Staff</option>
                    <option>Manager</option>
                </select>
            </td>
            <td>
                <select>
                    <option></option>
                </select>
            </td>
        </tr>
        <tr>
            <td>Tuesday - December 15, 2015</td>
            <td>0.00</td>
            <td>0.00</td>
            <td>
                <select>
                    <option>001001</option>
                    <option>001002</option>
                    <option>001003</option>
                </select>
            </td>
            <td>
                <select>
                    <option>Staff</option>
                    <option>Manager</option>
                </select>
            </td>
            <td>
                <select>
                    <option></option>
                </select>
            </td>
        </tr>
        <tr>
            <td>Wednesday - December 16, 2015</td>
            <td>0.00</td>
            <td>0.00</td>
            <td>
                <select>
                    <option>001001</option>
                    <option>001002</option>
                    <option>001003</option>
                </select>
            </td>
            <td>
                <select>
                    <option>Staff</option>
                    <option>Manager</option>
                </select>
            </td>
            <td>
                <select>
                    <option></option>
                </select>
            </td>
        </tr>
        <tr>
            <td>Thursday - December 17, 2015</td>
            <td>0.00</td>
            <td>0.00</td>
            <td>
                <select>
                    <option>001001</option>
                    <option>001002</option>
                    <option>001003</option>
                </select>
            </td>
            <td>
                <select>
                    <option>Staff</option>
                    <option>Manager</option>
                </select>
            </td>
            <td>
                <select>
                    <option></option>
                </select>
            </td>
        </tr>
        <tr>
            <td>Friday - December 18, 2015</td>
            <td>0.00</td>
            <td>0.00</td>
            <td>
                <select>
                    <option>001001</option>
                    <option>001002</option>
                    <option>001003</option>
                </select>
            </td>
            <td>
                <select>
                    <option>Staff</option>
                    <option>Manager</option>
                </select>
            </td>
            <td>
                <select>
                    <option></option>
                </select>
            </td>
        </tr>
    </tbody>
</table>

<script>
    $(document).ready(function () {
        var grid = $("#timeSheetTbl").kendoGrid({
            toolbar: ["excel"],
            excel: {
                fileName: "Timesheet.xlsx",
                filterable: true
            },
            dataSource: {
                pageSize: 15
            },
            /*toolbar: [
                {"name": "create"}
            ],*/
            sortable: true,
            pageable: false,
            filterable: true,
            groupable: false,
            scrollable: false,
            resizable: true
        }).data("kendoGrid");
    });
    $('.emp-text').hide();
    $('.click-collapse').click(function () {
        $('#employeeInfoPartial').slideToggle('slow');
        $("i", this).toggleClass("fa-minus-square-o fa-plus-square-o");
        $('.emp-text').toggle('slow');
    });
</script>