@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Data;
@using DataDrivenViewEngine.Models.Core
@model DarwiNet2._0.Data.Employee
@{ 
 ViewBag.Title = "Check History";
 ViewBag.ParentCrumb = "Payroll";
 //Breadcrumb Requiredness
 //var profileid = Url.RequestContext.RouteData.Values["id"];
 //var taskid = HttpContext.Current.Request.QueryString["tid"];
 //ViewBag.CrumbName = "Check History";
 //ViewBag.Crumb = Url.Action("List", "Employees");
    //Breadcrumb End
}



<div>
    @Html.Action("_EmployeeInfo", "Employees", new { e = ViewBag.EmployeeID })
</div>
<div id="loading" style="padding-top: 15px; display: none; text-align: center"><i style="color: black;" class=" fa fa-spinner fa-spin fa-4x"></i></div>
<div id="partialDiv">
    <div class="company-info">
        <div class="row">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>

                <div class="colored-line-left"></div>
            </div>
            <div class="col-md-6 col-sm-6">
                <div class="text-right">
                </div>
            </div>
        </div>
    </div>

    <script src="~/Scripts/jquery.datetimepicker.js"></script>
    <link href="~/Content/jquery.datetimepicker.css" rel="stylesheet" />
    <script type="text/javascript">

        
        $(function () {
            $('#toggle_view').click(function () {
                var $this = $(this);
                $this.toggleClass('showFilter');
                $('#filterdiv').toggle();
                //$('#tbl_401k').toggle();
                if ($this.hasClass('showFilter')) {
                    $this.text('Hide Filters');
                } else {
                    $this.text('Advanced Filters');
                }
            });
        });
        $(function () {
            $(".datepicker").datetimepicker({
                timepicker: false,
                format: 'm/d/Y',
                formatDate: 'm/d/Y'
            });
            $(".datetimepicker").datetimepicker({
                formatTime: 'g:i A',
                format: 'm/d/Y h:i A'
            });
            //$('input').tooltip({
            //    placement: "right",
            //    trigger: "focus"
            //});
        });

    </script>
    <script>
        //Filters
        $("#filter").on("click", function () {
            var from = $("#from").val();
            var to = $("#to").val();
            var filter = {
                logic: "and",
                filters: [
                    { field: "CheckDate", operator: "gte", value: from },
                    { field: "CheckDate", operator: "lte", value: to }
                ]
            };
            grid.dataSource.filter(filter);
        });
        $("#yearToDate").on("click", function () {
            var grid = $("#grid").data("kendoGrid");
            var val = (new Date).getFullYear();
            var year = val.toString().toLowerCase();
            var filter = {
                filters: [
                    { field: "CheckDate", operator: "contains", value: year }
                ]
            };
            grid.dataSource.filter(filter);
        });
        $("#MonthToDate").on("click", function () {
            var grid = $("#grid").data("kendoGrid");
            var d = new Date();
            var month = d.getMonth();
            var val = month + 1;
            var mon = val.toString().toLowerCase();
            var filter = {
                filters: [
                    { field: "CheckDate", operator: "contains", value: mon }
                ]
            };
            grid.dataSource.filter(filter);
        });
    </script>
    <script>
        function dataBound() {
            // this.expandRow(this.tbody.find("tr.k-master-row").first());
        }
    </script>
    <div>
        <div class="toolbar" style="padding-bottom: 15px;">
            <div class="row">
                <div class="col-md-3 pull-left">
                    <label for="year">Select Year: </label>
                    <select id="year" class="form-control"></select>
                </div>
                <div class="col-md-3">
                    <div style="padding-top: 25px;">
                        <a href="javascript:void(0)" id="toggle_view">Advanced Filters</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="filterdiv" class="row" style="padding-bottom: 15px; display: none;">
        <div class="col-md-12">
            <div style="padding-top: 15px; border: solid 1px #ccc; padding: 10px;">
                <div class="row">
                    <div class="form-group">
                        <div class="col-md-12">
                            <div class="col-md-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="MonthToDate">Month To Date: </label>
                                    </div>
                                    <div class="col-md-1">
                                        <input type="radio" name="dates" id="MonthToDate" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="col-sm-8">
                                    <input type="radio" name="dates" id="selectdate" value="SelectDates" /> <label>Select Dates: </label>

                                    <div class="row">
                                        <div class="col-sm-6">
                                            <input type="text" id="from" class="" placeholder="From" style="width: 100%" />
                                        </div>
                                        <div class="col-sm-6">
                                            <input type="text" id="to" class="" placeholder="To" style="width: 100%" />
                                        </div>

                                    </div>

                                    <div style="padding-top: 10px;" class="pull-right">
                                        <button class="btn btn-thinkware" id="clearFilters" onclick="clearFiter()">Reset Filters</button>
                                        <button class="btn btn-thinkware" id="filterbutton">Filter by Selected Dates</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="checkhist">
        @(Html.Kendo().Grid<EmployeeCheckHistoryView>()
          .Name("grid")
          .ToolBar(tools => tools.Excel())
          .Excel(e => e.AllPages(true))
          .Excel(excel => excel
              .FileName("CheckHistory.xlsx")
          )
          .Columns(columns =>
          {
              columns.Template(@<text>

            </text>)
                  .Title("View")
                  .ClientTemplate(
                      "<center><div class=''>" +
                      "<a href='" + Url.Action("ShowCheck", "Employees", new { e = "#= EmployeeID#", p = "#= PaymentAdjustmentNumber#" }) + "' title='View' class='view-stub-click' target='_blank'><i class='icon-edit fa fa-money fa-fw fa-lg'></i></a>" +
                      "</center>").Width(85).HeaderHtmlAttributes(new { @class = "header-center" });
              columns.Bound(e => e.CheckDate).Title("Check Date").Format("{0:MM/dd/yyyy}");
              columns.Bound(e => e.GrossWagesPayRun).Title("Gross Wages").Format("{0:c}");
              columns.Bound(e => e.TotalTaxes).Title("Total Taxes").Format("{0:c}");
              columns.Bound(e => e.TotalDeductions).Title("Total Deductions").Format("{0:c}");
              columns.Bound(e => e.TotalBenefits).Title("Total Benefits").Format("{0:c}");
              columns.Bound(e => e.NetWagesPayRun).Title("Net Wages").Format("{0:c}");
              columns.Bound(e => e.Year).Title("Year").Hidden(true);
              columns.Bound(e => e.CheckNumber).Title("Check Number").Hidden(true);
              columns.Bound(e => e.PaymentAdjustmentNumber).Title("Payment Number").Hidden(true);


              // columns.Bound(e => e.BlnInactive).Title("Inactive").Hidden(true).ClientTemplate("<input type='checkbox' #= BlnInactive ? checked='checked' : '' # disabled='disabled'></input>");
          })
                              .Sortable()
                // .Selectable()
                              .Scrollable(scr => scr.Height(380))
                              .Groupable()
                              .ColumnMenu()
                              .Pageable()
                              .Filterable()
                              .ClientDetailTemplateId("template")
                //.HtmlAttributes(new {style = "height: 647px"})
                              .Reorderable(reorder => reorder.Columns(true))
                              .Resizable(resize => resize.Columns(true))
                              .DataSource(dataSource => dataSource
                                  .Ajax()
                                  //.PageSize(10)
                                  .Read(read => read.Action("CheckHistory_Read", "Employees", new { employeeID = ViewBag.EmployeeID }))
                                    .Events(ev => ev.RequestEnd("onRequestEnd"))

                              )
                              .Events(events => events.DataBound("dataBound"))
                              
        )

        <script id="template" type="text/kendo-tmpl">
            @(Html.Kendo().TabStrip()
          .Name("tabStrip_#=CheckNumber#")
          .SelectedIndex(0)
          .Animation(animation => animation.Open(open => open.Fade(FadeDirection.In)))
          .Items(items =>
          {
              items.Add().Text("Pay Codes").Content(@<text>
                @(Html.Kendo().Grid<EmployeePaycodesView>()
                              .Name("grid_pc#=CheckNumber#")
                              .Columns(columns =>
                              {
                                  columns.Bound(p => p.PayRecord).Title("Pay Code").Width(156);
                                  columns.Bound(p => p.PaycodeDescription).Title("Description");
                                  columns.Bound(p => p.Hours).Width(100);
                                  columns.Bound(p => p.PayRateAmount).Format("{0:C}").Title("Current Hourly Rate");
                                  columns.Bound(p => p.Amount).Format("{0:C}");
                                  columns.Bound(p => p.PayTypeDesc).Title("Pay Type");
                              })
                              .DataSource(dataSource => dataSource
                                  .Ajax()
                                  //.PageSize(5)
                                          .Read(read => read.Action("GetCheckCodes", "Employees", new { i = "#=PaymentAdjustmentNumber#" }))
                              )
                        // .Pageable()
                              .Sortable()
                              .ToClientTemplate())
            </text>
                  );
              items.Add().Text("Benefits").Content(@<text>
                    @(Html.Kendo().Grid<EmployeeBenefitsView>()
                                  .Name("grid_eb#=CheckNumber#")
                                  .Columns(columns =>
                                  {
                                      columns.Bound(b => b.Benefit).Title("Benefit").Width(156);
                                      columns.Bound(b => b.Description).Width(200).Title("Description");
                                      //columns.Bound(b => b.Inactive).Width(80).ClientTemplate("<input type='checkbox' />");
                                      columns.Bound(b => b.BenefitAmount1).Width(190).Title("Amount").Format("{0:C}");
                                  })
                                  .DataSource(dataSource => dataSource
                                      .Ajax()
                                      // .PageSize(5)
                                              .Read(read => read.Action("GetCheckBenefits", "Employees", new { i = "#=PaymentAdjustmentNumber#" }))
                                  )
                            // .Pageable()
                                  .Sortable()
                                  .ToClientTemplate())
            </text>
                  );
              items.Add().Text("Deductions").Content(@<text>
                    @(Html.Kendo().Grid<EmployeeDeductionsView>()
                                  .Name("grid_ed#=CheckNumber#")
                                  .Columns(columns =>
                                  {
                                      columns.Bound(d => d.Deduction).Title("Deduction").Width(156);
                                      columns.Bound(d => d.Description).Width(200).Title("Description");
                                      //columns.Bound(d => d.Inactive).Width(80).ClientTemplate("<input type='checkbox' />");
                                      columns.Bound(d => d.DeductionAmount1).Width(190).Title("Deduction Amount").Format("{0:C}");
                                  })
                      .DataSource(dataSource => dataSource
                            .Ajax()
                            .PageSize(5)
                            .Read(read => read.Action("GetCheckDeductions", "Employees", new { i = "#=PaymentAdjustmentNumber#" }))
                        )
                      .Sortable()
                      .ToClientTemplate())
            </text>
                  );
              items.Add().Text("Taxes").Content(@<text>
                    @(Html.Kendo().Grid<CheckStubTransactionRecord>()
                                  .Name("grid_et#=CheckNumber#")
                                  .Columns(columns =>
                                  {
                                      columns.Bound(d => d.code).Title("Tax Code").Width(156);
                                      columns.Bound(d => d.amount).Width(200).Title("Amount").Format("{0:C}");
                                      //columns.Bound(d => d.Inactive).Width(80).ClientTemplate("<input type='checkbox' />");
                                      //0columns.Bound(d => d.DeductionAmount1).Width(190).Title("Deduction Amount").Format("{0:C}");
                                  })
                      .DataSource(dataSource => dataSource
                            .Ajax()
                            .PageSize(5)
                                    .Read(read => read.Action("GetCheckTaxes", "Employees", new { p = "#=PaymentAdjustmentNumber#" }))
                        )

                      .ToClientTemplate())
            </text>
                  );
          })
                                                                                                              .ToClientTemplate())
        </script>

        <script src="~/Scripts/TimeZoneControl.js"></script>
    </div>

<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
    <script src="~/Scripts/date.js"></script>
    <script>
        $(document).ready(function () {
            $('#from, #to').kendoMaskedDatePicker().parent().parent().removeClass('k-header');
        })
    </script>
    <script>
        $(document).ready(function () {
            $("#FieldFilter").keyup(function () {

                var value = $("#FieldFilter").val();
                var grid = $("#grid").data("kendoGrid");

                if (value) {
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            { field: "CheckDate", operator: "contains", value: value }
                        ]
                    });
                } else {
                    grid.dataSource.filter({});
                }
            });
        });

        
        //Find some years
        var minOffset = 0, maxOffset = 5; // Change to whatever you want
        var thisYear = (new Date()).getFullYear();
        var select = $('#year');
        $('<option>', { value: "0", text: "None" }).appendTo(select);
        for (var i = minOffset; i <= maxOffset; i++) {
            var year = thisYear - i;
            $('<option>', { value: year, text: year }).appendTo(select);
        }

        //Filter by Year
        $('#year').on('change', function () {
            var grid = $("#grid").data("kendoGrid");
            var val = this.value;

            if (val == "0") {
                $("#grid").data("kendoGrid").dataSource.filter([]);
                return false;
            }
            var fd = new Date(val, 0, 1);
            var firstday = fd;
            var ld = new Date(val, 11, 31, 23, 59, 59);
            var lastday = ld;
            grid.dataSource.filter({
                logic: "and",
                filters: [
                    { field: "CheckDate", operator: "gte", value: firstday },
                    { field: "CheckDate", operator: "lte", value: lastday }
                ]
            });
        });
        //Select Date Filter
        $("#filterbutton").on("click", function () {
            var radio = $('input:radio[name=dates]:checked').val();
            if (radio === "SelectDates") {
                var grid = $("#grid").data("kendoGrid");
                var from = $("#from").val();
                var to = $("#to").val();
                grid.dataSource.filter({
                    logic: "and",
                    filters: [
                        { field: "CheckDate", operator: "gte", value: from },
                        { field: "CheckDate", operator: "lte", value: to }
                    ]
                });
            } else {
                bootbox.dialog({
                    message: "Please choose 'Select Dates' to filter by date.",
                    title: "Information",
                    buttons: {
                        main: {
                            label: "Okay",
                            className: "btn-primary",
                            callback: function () {
                                //Example.show("Primary button");
                            }
                        }
                    }
                });
            }

        });
        //Month To Date Filter
        $("#MonthToDate").on("click", function () {
            var grid = $("#grid").data("kendoGrid");
            var fd = Date.today().clearTime().moveToFirstDayOfMonth();
            var firstday = fd;
            var ld = Date.today().clearTime().moveToLastDayOfMonth();
            var lastday = ld;
            grid.dataSource.filter({
                logic: "and",
                filters: [
                    { field: "CheckDate", operator: "gte", value: firstday },
                    { field: "CheckDate", operator: "lte", value: lastday }
                ]
            });
        });

        //Clear Filters
        function clearFiter() {
            $("#grid").data("kendoGrid").dataSource.filter([]);
            $('input[name="dates"]').prop('checked', false);
            $('input[id="from"]').val("");
            $('input[id="to"]').val("");
            $('select[id="selectManager"] option[value="0"]').attr("selected", "selected");
            $('select[id="selectDept"] option[value="0"]').attr("selected", "selected");
            $('select[id="Month"] option[value="0"]').attr("selected", "selected");
            $('select[id="year"]').val('0');
            $('#FieldFilter').val('');
        }
    </script>
</div>


