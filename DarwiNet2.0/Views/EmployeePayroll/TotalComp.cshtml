@{
    ViewBag.Title = "Total Compensation";
    ViewBag.ParentCrumb = "Payroll,Payroll Info";
}


<!--Chart for showing compensation breakdown-->
<div>
    @Html.Action("_EmployeeInfo", "Employees", new { e = ViewBag.EmployeeID })
</div>

<p style="font-size: 22px;">Total Comp</p>
<div class="colored-line-left"></div>

<div id="totalCompInfo" style="margin:25px">
    <div id="compBreakdown">
        <div class="row">
            <div class="col-md-12">
                <div class="col-md-6">
                    <div id="totalCompChart"></div>
                    <div class="">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">Benefits Breakdown</h3>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label>Annual Wages:</label>
                                    </div>
                                    <div class="col-md-6">
                                        <span style="padding-left: 10px;">
                                            $75,000.00
                                        </span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label>Company Paid Benefits:</label>
                                    </div>
                                    <div class="col-md-6">
                                        <span style="padding-left: 10px;">
                                            $75,000.00
                                        </span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label>Company Paid Taxes:</label>
                                    </div>
                                    <div class="col-md-6">
                                        <span style="padding-left: 10px;">
                                            $75,000.00
                                        </span>
                                    </div>
                                </div>
                                <div class="row" style="border-bottom: 1px solid">
                                    <div class="col-md-6">
                                        <label>Company Paid PTO:</label>
                                    </div>
                                    <div class="col-md-6">
                                        <span style="padding-left: 10px;">
                                            $75,000.00
                                        </span>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label>Total Compensation:</label>
                                    </div>
                                    <div class="col-md-6">
                                        <span style="padding-left: 10px;">
                                            $150,000.00
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="col-md-6">
                <div class="row" style="margin-top: 15px">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Employer Paid Benefits</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Benefit</label>
                                </div>
                                <div class="col-md-6">
                                    <label style="padding-left: 10px;">Cost</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Health Insurance</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Dental Insurance</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Vision Insurance</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row" style="border-bottom:solid 1px">
                                <div class="col-md-6">
                                    <label>401k</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Total Paid Benefits</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" style="margin-top: 15px">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">Other Employer Paid Items</h3>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Item</label>
                                </div>
                                <div class="col-md-6">
                                    <label style="padding-left: 10px;">Cost</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>FICA SS</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>FICA Med</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Unemployment</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Workers Compensation</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row" style="border-bottom:solid 1px">
                                <div class="col-md-6">
                                    <label>PTO</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Total Paid Taxes</label>
                                </div>
                                <div class="col-md-6">
                                    <span style="padding-left: 10px;">
                                        $50,000.00
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function createTotalCompChart() {
        $("#totalCompChart").kendoChart({
            title: {
                text: "Total Wages"
            },
            legend: {
                position: "top"
            },
            seriesDefaults: {
                labels: {
                    template: "#= category # - #= kendo.format('{0:P}', percentage)#",
                    position: "outsideEnd",
                    visible: true,
                    background: "transparent"
                }
            },
            series: [{
                type: "pie",
                data: [{
                    category: "Annual Wages",
                    value: 35
                }, {
                    category: "Company Paid Benefits",
                    value: 25
                }, {
                    category: "Company Paid Taxes",
                    value: 20
                }, {
                    category: "Company Paid PTO",
                    value: 10
                }]
            }],
            tooltip: {
                visible: true,
                template: "#= category # - #= kendo.format('{0:P}', percentage) #"
            }
        });

    };
    $(function () {
        $("#totalCompChart").ready(createTotalCompChart);
        $("#totalCompChart").bind(createTotalCompChart);
    });
    $('.emp-text').hide();
    $('.click-collapse').click(function () {
        $('#employeeInfoPartial').slideToggle('slow');
        $("i", this).toggleClass("fa-minus-square-o fa-plus-square-o");
        $('.emp-text').toggle('slow');
    });
</script>