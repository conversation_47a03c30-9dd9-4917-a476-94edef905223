@*//DG*@
@using System.Text.RegularExpressions
@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Helpers;

@{
    Layout = null;
}

@if (ViewBag.NewsItems.Count > 0)
{
    <div class="scroller searchable-container">
        @foreach (var item in ViewBag.NewsItems)
        {
            {
                ViewBag.NewString = Regex.Replace(item.NewsBody, @"<[^>]+>", "");
            }
            <div class="row" style="padding-top: 5px" id="<EMAIL>">
                <div class="col-md-12">
                    <div style="border-bottom: 1px solid #ccc; padding-bottom: 8px;">
                        <strong>@item.Subject</strong>
                        <span class="pull-right">
                            @if (ViewBag.NewString.Length > 200)
                            {
                                <span>
                                    <a href="@Url.Action("Show", "NewsItems", new { id = @item.id, loc = "dashboard" })" class="label label-thinkware" style="font-weight: 500; margin-right: 5px;">Read More</a>
                                </span>
                            }
                            @*DG - Task - 7140 - 03/17/2021*@
                            @*@if (ViewBag.ShowButton)
                                {
                                    <span class="label label-danger markAsRead pull-right" data-id="@item.id" style="font-weight: 500; margin-right: 5px; cursor: pointer">Mark as Read</span>
                                }*@
                        </span>
                    </div>

                </div>
                <div class="col-md-10">
                    <div style="padding: 10px 0 10px 0;">
                        @if (ViewBag.NewString.Length > 200)
                        {
                            @Html.Raw(ViewBag.NewString.Substring(0, Math.Min(ViewBag.NewString.Length, 200)) + "...")
                        }
                        else
                        {
                            @Html.Raw(item.NewsBody)
                        }

                    </div>
                </div>
                <div class="col-md-2">
                    <div style="padding-top: 25px">
                        @if (item.NewsAttachments.Count > 0)
                        {
                            <i class="fa fa-paperclip fa-fw fa-lg talk-to-the-hand item-attached" data-id="@item.id"></i>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
    </div>
}
else
{
    <h3>No Current News/Announcements</h3>
    <div class="modal-footer">
        <button class="btn btn-thinkware" id="cancelnews" onclick="$('#newsItemModal').modal('hide');">Close</button>
    </div>
}
<script>
    //View Attach
    $(document).on("click", '.item-attached', function () {
        var attachment = $(this).attr('data-id');
        var url = "@Url.Action("NewsAttachments", "Dashboard")";
        url = url + "/" + attachment;
        $("#newsItemModal").find(".modal-body").load(url);
    });
      @*DG - Task - 7140 - 03/17/2021*@
    $('.markAsRead').click(function (e) {

        e.preventDefault();
        var val = $(this).attr('data-id');
        var url = "@Url.Action("markAsRead", "Dashboard")" + '?id=' + val;
        $.post(url, function (data) {
            $('#newsItem-' + val).fadeOut();
            var count = $('#newsCount').text();
            $('#newsCount').text(count - 1);
        });

    });

    $(document).ready(function () {
        //hide some news
        var values = "@ViewBag.Read";
        values = values.replace(/ /g, '');
        $.each(values.split(','), function (key, value) {
            $(document).ready(function () {
                $('#newsItem-' + value).hide();
            });
        });
    });
</script>
