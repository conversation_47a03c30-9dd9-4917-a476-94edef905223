@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@{
    Layout = null;
    ViewBag.Title = "Unique Active Users";
}

<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="col-md-6 col-sm-6">
            <div class="text-right">
                <button type="button" class="btn btn-thinkware" title="Export to Excel" onclick="ExportRequests()">Export to Excel</button>
            </div>
        </div>
    </div>
</div>
<div class="toolbar" style="padding-top: 5px; margin-bottom: 5px;">
    <div class="row">
        <div class="col-md-3 pull-left">
            <div class="input-group">
                <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                <input type="text" class="form-control" id='FieldFilter' placeholder="Search User ID">
            </div>
        </div>
    </div>
</div>
@(Html.Kendo().Grid<DarwiNet2._0.Data.ActiveUser>()
        .Name("grid")
        .Columns(columns =>
        {
            columns.Bound(e => e.userID).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("UserID", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("UserID", GlobalVariables.LanguageID));
            columns.Bound(e => e.name).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Name", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Name", GlobalVariables.LanguageID));
            columns.Bound(e => e.email).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID));
            columns.Bound(e => e.LastLogin).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Last Login", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Last Login", GlobalVariables.LanguageID)).Format("{0:MM/dd/yyyy}");
        })
            .Sortable()
            .Pageable()
            .Groupable()
            .Filterable()
            .DataSource(dataSource => dataSource
             .Ajax()
             .PageSize(10)
                                .Read(read => read.Action("GetActiveUsers_Read", "Dashboard"))
                )
                .Events(events => events.DataBound("dataBound"))
)

@using (Html.BeginForm("ExportActiveUsersFile", "Dashboard", FormMethod.Post, new { name = "exportFileForm", id = "exportFileForm" }))
{
    <input type="hidden" id="filePath" name="filePath" />
}
<script>
    function ExportRequests() {
        $('#loadingSpinner').show(100);

        $.ajax({
            url: '@Url.Action("ExportActiveUsers", "Dashboard")',
            contentType: "application/json",
            type: "POST",
            success: function (data) {
                if (data) {
                    $('#filePath').val(data);
                    $("#exportFileForm").submit();
                }

                $('#loadingSpinner').hide(250);
            },
            error: function (errorData) {
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    }

</script>
<script>
    $(document).ready(function () {
        $("#FieldFilter").keyup(function () {

            var value = $("#FieldFilter").val();
            var grid = $("#grid").data("kendoGrid");

            if (value) {
                grid.dataSource.filter({
                    logic: "or",
                    filters: [
                        { field: "UserID", operator: "contains", value: value }
                    ]
                })
            } else {
                grid.dataSource.filter({});
            }
        });
    });
</script>