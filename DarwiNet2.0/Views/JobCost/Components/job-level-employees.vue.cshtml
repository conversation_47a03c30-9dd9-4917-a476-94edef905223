@using DarwiNet2._0.Extensions;

<style type="text/css">
    .f-center {
        display: flex;
        justify-content: center;
    }
</style>

<script type="text/x-template" id="job-level-employees-template">
    <div class="job-level-employees-wrapper">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>Employee ID</th>
                    <th>Employee Name</th>
                    <th>Complete</th>
                    <th>Inactive</th> 
                    <th></th>       
                </tr>
            </thead>
            <tbody v-if="employees.length == 0">
                No employees available
            </tbody>
            <tbody v-else>
                <tr v-for="employee in employees">
                    <td>{{ employee.EmployeeID }}</td>
                    <td>{{ employee.EmployeeName }}</td>
                    <td><input type="checkbox" name="complete" :checked="employee.Complete" @@change="complete(employee.EmployeeID)"></td>
                    <td><input type="checkbox" name="inactive" :checked="employee.Inactive" @@change="inactive(employee.EmployeeID)"></td>
                    <td class="f-center"><button class="btn btn-danger" @@click="onOpenConfirmEmployeeModal(employee.EmployeeID, employee.EmployeeName)">Remove Employee</button></td>
                </tr>
            </tbody>
        </table>
        <thinkware-alert-modal v-show="showConfirmDeleteEmployee"
                            title="Warning!"
                            no-label="Cancel"
                            yes-label="Continue"
                            @@user-action-confirm="removeEmployee()"
                            @@user-action-decline="onCloseConfirmEmployeeModal()">
            <template v-slot:body>
                <span>Are you sure you wish to remove employee {{ activeEE }}?</span>
            </template>
        </thinkware-alert-modal>
    </div>
</script>

@Html.VueComponent("~/Shared/thinkware-alert-modal")

<script type="text/javascript" id="job-level-employees-script">
    var JobLevelEmployeesComponent = VueComponent('job-level-employees', {
        props: {
            activeWatchKey: {
                type: Number,
                required: true
            },
            companyId: {
                type: Number,
                required: true
            },
            clientId: {
                type: String,
                required: true
            },
            jobCostingName: {
                type: String
            },
            level2: {
                type: String
            },
            level3: {
                type: String
            },
            level4: {
                type: String
            },
            level5: {
                type: String
            },
            level6: {
                type: String
            },
        },
        data: function () {
            return {
                employees: [],
                activeEE: '',
                activeEEName: '',
                showConfirmDeleteEmployee: false,
            }
        },
        watch: {
            activeWatchKey: function (value) {
                this.fetchJobLevelEmployees();
            },
        },
        mounted: function () {
            this.fetchJobLevelEmployees();
        },
        methods: {
            fetchJobLevelEmployees: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("GetJobLevelEmployees", "JobCost")' + `?companyId=${self.companyId}&clientId=${self.clientId}&jobCostingName=${self.jobCostingName}&jobLevel2=${self.level2}&jobLevel3=${self.level3}&jobLevel4=${self.level4}&jobLevel5=${self.level5}&jobLevel6=${self.level6}`, {
                    onSuccess: function (result) {
                        self.employees = result.data;
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        ThinkwareCommon.showAlert('danger', errorThrown);
                    }
                });
            },

            complete: function(employeeId) {
                let self = this;
                ThinkwareCommon.ajax.postJson('@Url.Action("UpdateEmployeeJobCostAssignmentComplete", "JobCost")', {
                    data: {
                        companyId: self.companyId,
                        clientId: self.clientId,
                        employeeId,
                        jobCostingName: self.jobCostingName,
                        jobLevel2: self.level2,
                        jobLevel3: self.level3,
                        jobLevel4: self.level4,
                        jobLevel5: self.level5,
                        jobLevel6: self.level6,
                    },
                    onSuccess: function (result) {
                        self.$toastr.s('Updated employee job cost assignment completion.');
                        self.fetchJobLevelEmployees();
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        self.$toastr.s('An issue has occurred while trying to update the employee job cost assignment completion.');
                        console.log('err', errorThrown);
                    }
                });
            },

            inactive: function(employeeId) {
                let self = this;
                ThinkwareCommon.ajax.postJson('@Url.Action("UpdateEmployeeJobCostAssignmentInactive", "JobCost")', {
                    data: {
                        companyId: self.companyId,
                        clientId: self.clientId,
                        employeeId,
                        jobCostingName: self.jobCostingName,
                        jobLevel2: self.level2,
                        jobLevel3: self.level3,
                        jobLevel4: self.level4,
                        jobLevel5: self.level5,
                        jobLevel6: self.level6,
                    },
                    onSuccess: function (result) {
                        self.$toastr.s('Updated employee job cost assignment inactive status.');
                        self.fetchJobLevelEmployees();
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        self.$toastr.s('An issue has occurred while trying to update the employee job cost assignment inactive status.');
                        console.log('err', errorThrown);
                    }
                });
            },
            removeEmployee: function() {
                let self = this;
                let employeeId = self.activeEE;
                ThinkwareCommon.ajax.postJson('@Url.Action("RemoveEmployeeJobCostAssignment", "JobCost")', {
                    data: {
                        companyId: self.companyId,
                        clientId: self.clientId,
                        employeeId,
                        jobCostingName: self.jobCostingName,
                        jobLevel2: self.level2,
                        jobLevel3: self.level3,
                        jobLevel4: self.level4,
                        jobLevel5: self.level5,
                        jobLevel6: self.level6,
                    },
                    onSuccess: function (result) {
                        self.showConfirmDeleteEmployee = false;
                        self.$toastr.s('Successfully removed employee.');
                        self.$emit('removed-employee')
                        self.fetchJobLevelEmployees();
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        self.showConfirmDeleteEmployee = false;
                        self.$toastr.s('An issue has occurred while trying to update the employee job cost assignment inactive status.');
                        console.log('err', errorThrown);
                    }
                });
            },
            onOpenConfirmEmployeeModal: function(eeId, eeName) {
                this.activeEE = eeId;
                this.activeEEName = eeName;
                this.showConfirmDeleteEmployee = true;
            },
            onCloseConfirmEmployeeModal: function() {
                this.showConfirmDeleteEmployee = false;
                this.activeEE = '';
                this.activeEEName = '';
            }
        },

        components: [
            ThinkwareAlertModal
        ]
    });

</script>
