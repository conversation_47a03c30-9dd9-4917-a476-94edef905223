@using DarwiNet2._0.Extensions;

<style type="text/css">
    .t-right {
        text-align: right;
    }

    .action-row {
        justify-content: flex-end;
    }

    .action-row-section {
        justify-content: end;
        display: flex;
    }
</style>

<script type="text/x-template" id="job-level-cost-template">
    <div class="job-level-cost-wrapper">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>Employee ID</th>
                    <th>Employee Name</th>
                    <th class="t-right">Est. Cost</th>
                    <th class="t-right">Actual Cost</th>
                    <th class="t-right">Est. Completion</th>
                    <th class="t-right">Actual Completion</th>      
                </tr>
            </thead>
            <tbody v-if="employees.length == 0">
                No employees available
            </tbody>
            <tbody v-else>
                <tr v-for="employee in employees">
                    <td>{{ employee.EmployeeID }}</td>
                    <td>{{ employee.EmployeeName }}</td>
                    <td class="t-right">
                        <thinkware-currency-input v-model="employee.EstimatedCost"></thinkware-currency-input>
                    </td>
                    <td class="t-right">
                        <thinkware-currency-input v-model="employee.ActualCost"></thinkware-currency-input>
                    </td>
                    <td class="t-right">
                        <v-date-picker v-model="employee.EstimatedCompletion"
                                id="estimatedEnd"
                                :popover="{ visibility: 'click' }"
                                day-format="formatDate()" />
                    </td>
                    <td class="t-right">
                        <v-date-picker v-model="employee.ActualCompletion"
                                id="estimatedEnd"
                                :popover="{ visibility: 'click' }"
                                day-format="formatDate()" />
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="row action-row">
            <div class="col-md-4 action-row-section">
                <button class="btn btn-secondary btn-secondary-overwrite" @@click="closeModal()">Cancel</button>
                <button class="btn btn-success margin-left" @@click="save()">Save Changes</button>
            </div>
        </div>
    </div>
</script>

@Html.VueComponent("~/Shared/thinkware-currency-input")

<script type="text/javascript" id="job-level-cost-script">
    var JobLevelCostComponent = VueComponent('job-level-cost', {
        props: {
            activeWatchKey: {
                type: Number,
                required: true
            },
            companyId: {
                type: Number,
                required: true
            },
            clientId: {
                type: String,
                required: true
            },
            jobCostingName: {
                type: String
            },
            level2: {
                type: String
            },
            level3: {
                type: String
            },
            level4: {
                type: String
            },
            level5: {
                type: String
            },
            level6: {
                type: String
            },
        },
        data: function () {
            return {
                employees: [],
            }
        },
        watch: {
            activeWatchKey: function (value) {
                this.fetchJobLevelEmployees();
            },
        },
        mounted: function () {
            this.fetchJobLevelEmployees();
        },
        methods: {
            fetchJobLevelEmployees: function() {
                let self = this;
                ThinkwareCommon.ajax.get('@Url.Action("GetJobLevelEmployees", "JobCost")' + `?companyId=${self.companyId}&clientId=${self.clientId}&jobCostingName=${self.jobCostingName}&jobLevel2=${self.level2}&jobLevel3=${self.level3}&jobLevel4=${self.level4}&jobLevel5=${self.level5}&jobLevel6=${self.level6}`, {
                    onSuccess: function (result) {
                        self.employees = result.data;

                        self.employees.map(x => {
                            if (x.ActualCompletion != null) {
                                x.ActualCompletion = new Date(ThinkwareCommon.dateFormat(x.ActualCompletion, '@System.Configuration.ConfigurationManager.AppSettings["TimezoneID"]'))
                            }
                            if (x.EstimatedCompletion != null) {
                                x.EstimatedCompletion = new Date(ThinkwareCommon.dateFormat(x.EstimatedCompletion, '@System.Configuration.ConfigurationManager.AppSettings["TimezoneID"]'))
                            }
                        });
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        ThinkwareCommon.showAlert('danger', errorThrown);
                    }
                });
            },

            save: function() {
                let self = this;
                let employeesForm = this.employees.map(e => ({
                    employeeId: e.EmployeeID,
                    estimatedCost: e.EstimatedCost,
                    actualCost: e.ActualCost,
                    EstimatedCompletion: e.EstimatedCompletion,
                    ActualCompletion: e.ActualCompletion
                }));

                ThinkwareCommon.ajax.postJson('@Url.Action("UpdateEmployeeJobCostAssignmentCosts", "JobCost")', {
                    data: {
                        companyId: this.companyId,
                        clientId: this.clientId,
                        jobCostingName: this.jobCostingName,
                        level2: this.level2,
                        level3: this.level3,
                        level4: this.level4,
                        level5: this.level5,
                        level6: this.level6,
                        employees: employeesForm
                    },
                    onSuccess: function (response) {
                        self.$toastr.s('Updated employee job cost assignment cost information.');
                        self.closeModal();
                    },
                    onError: function (jqXHR, textStatus, errorThrown) {
                        self.$toastr.s('An issue has occured while updating the employee job cost assignment cost information.');
                    }
                });
            },

            formatDate: function (val) {
                return ThinkwareCommon.dateFormat(val, '@System.Configuration.ConfigurationManager.AppSettings["TimezoneID"]');
            },

            closeModal: function() {
                this.$emit("close-modal");
            }
        },
        components: [
            ThinkwareCurrencyInputComponent
        ]
    });

</script>
