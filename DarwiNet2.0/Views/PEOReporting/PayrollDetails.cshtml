@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;

@{
    ViewBag.Title = "PayrollDetails";
    ViewBag.ParentCrumb = "PEO Reporting,Billing";
}

<style>
    div.horizontalcontainer {
        margin: auto;
        margin-top: 20px;
        text-align: center;
    }

    ul.horizontal {
        list-style-type: none;
    }

        ul.horizontal li {
            display: inline;
        }

            ul.horizontal li a {
                padding: 20px 10px 10px 20px;
            }

    div.subsectionheader {
        background-color: #858a9d;
        margin-top: 30px;
        padding: 4px;
        text-align: center;
        color: #ffffff;
        font-size: 1em;
        font-weight: bold;
    }

    td.totalhdr {
        font-size: 1em;
        color: #808080;
        text-align: center;
    }

    td.total {
        font-size: 1.5em;
        color: #808080;
        text-align: center;
        font-weight: bold;
        padding-left: 20px;
        padding-right: 20px;
    }
</style>

<script src="~/Scripts/loading.js"></script>
<script>
    var companyID = @GlobalVariables.CompanyID;
    var clientID;
    var regionID;
    var divisionID;
    var payrollItem;
    var multiSelect;
    var selectionDDL;
    var detail;
    var compareValue;
    var compareType;
    var includeVoidedChecks;
    var displayOption;
    var startDate;
    var endDate;
    var chartAction;

    var chartUrl = "@Url.Action("Payroll_PayrollDetails", "PEOReporting")";

    function getCharts()
    {
        clientID =  $("#eclients").val();
        regionID =  $("#eregions").val();
        divisionID =  $("#edivisions").val();

        payrollItem = $("#selPayrollItem").val();
        multiSelect = $("#multiSelect").data("kendoMultiSelect").value();
        selectionDDL = $("#selectionDDL").data("kendoDropDownList").value();

        detail = $("#detailBy").val();
        if($("#chkComp:checked").is(':checked'))
        {
            compareType = $("#radInt").val();
            compareValue = $("#selComp").val();
        }
        else
        {
            compareType = 'None';
            compareValue = 0;
        }

        includeVoidedChecks = $("#chkVoidedChecks:checked").is(':checked');
        displayOption = $("#selDisplayOptions").val();

        getSliderDates();
        $('#ChartDiv').load(chartUrl, {companyID: companyID, clientID : clientID, region : regionID, division : divisionID, detail : detail, compareValue : compareValue, compareType : compareType, includeVoids : includeVoidedChecks, payrollItem : payrollItem, multiSelect : multiSelect, selection : selectionDDL, displayOption : displayOption, start : startDate, end : endDate}, function(response, status, xhr) {});
    }
</script>

@Html.Partial("~/Views/PEOReporting/_SliderPartial.cshtml")

<div class="row" style="margin-top: 12px;">
    <div class="col-md-3 col-md-offset-2">
        @(Html.Kendo().DropDownList()
            .Name("selPayrollItem")
            .HtmlAttributes(new { style = "width:275px" })
            .OptionLabel("Select Payroll Item...")
            .DataTextField("Text")
            .DataValueField("Value")
            .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetDarwiNetCodesByCodeType", "PEOReporting").Data("payrollItemsParam");
                            });
                        })
            .Enable(false)
            .Events(e => e.Select("onPayrollItemChange"))
        )
    </div>
    <div class="col-md-7">
        @(Html.Kendo().MultiSelect()
            .Name("multiSelect")
            .HtmlAttributes(new { style = "width:300px" })
            .Placeholder("Selection...  (Leave blank for ALL)")
            .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetPayrollItemSelection", "PEOReporting").Data("onPayrollItemData");
                            });
                        })
        )
        @(Html.Kendo().DropDownList()
            .Name("selectionDDL")
            .HtmlAttributes(new { style = "width:275px" })
            .OptionLabel("ALL")
            .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetPayrollItemSelection", "PEOReporting").Data("onPayrollItemData");
                            });
                        })
        )
    </div>
</div>

<div class="row">
    <h2>Display Options:</h2>
    <div class="col-md-12">
        <div class="col-md-1 control-label">
            <label>Detail By:</label>
        </div>
        <div class="col-md-6">
            @(Html.Kendo().DropDownList()
                      .Name("detailBy")
                      .HtmlAttributes(new { style = "width:300px" })
                      .OptionLabel("Select Detail...")
                      .DataTextField("Description")
                      .DataValueField("Code")
                      .DataSource(source =>
                      {
                          source.Read(read =>
                          {
                              read.Action("GetDetailTypesAll", "PEOReporting");
                          });
                      })
            )
            <input type="button" value="Build Chart" class="btn btn-danger" onclick="getCharts();" />
        </div>
    </div>
    <div class="col-md-12" style="padding-top: 25px;">
        <div class="col-md-1">
            <input type="checkbox" id="chkComp" name="chkComp" />
        </div>
        <div class="col-md-2 control-label">
            <label>Comparative:</label>
        </div>
        <div class="col-md-4">
            <select id="selComp" name="selComp" class="form-control">
                <option value="0">Select # of Intervals</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5</option>
            </select>
        </div>
        <div class="col-md-4">
            <select id="radInt" class="form-control">
                <option value="Years">Years</option>
                <option value="Intervals">Intervals</option>
            </select>
        </div>
    </div>

    <div class="col-md-12">
        <div class="col-md-1">
            <input type="checkbox" id="chkVoidedChecks" name="chkVoidedChecks" />
        </div>
        <div class="col-md-6 control-label">
            <label>Include Voided Checks</label>
        </div>
    </div>

    <div id="divWagesHoursSelection" class="col-md-12" style="display: none;">
        <div class="col-md-2 col-md-offset-1 control-label">
            <label>Display Options:</label>
        </div>
        <div class="col-md-4">
            <select id="selDisplayOptions" name="selDisplayOptions" class="form-control">
                <option value="Wages">Wages</option>
                <option value="Hours">Hours</option>
            </select>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12" style="padding-top: 25px;">
        <div id="ChartDiv" class="row"></div>
    </div>
</div>

<script>
    function onPayrollItemChange(e)
    {
        var selValue = this.dataItem(e.item).Value;
        switch(selValue) {
            case "1": // Pay Code
            case "2": // Pay Type
                $("#divWagesHoursSelection").show();
                showMultiSelect(true, selValue);
                showSelectionDDL(false, selValue);
                break;

            case "3": // Deduction
            case "4": // Benefit
                $("#divWagesHoursSelection").hide();
                $("#selDisplayOptions").val('Wages');
                showMultiSelect(true, selValue);
                showSelectionDDL(false, selValue);
                break;

            case "5": // Federal Tax
            case "6": // FICA SS
            case "7": // FICA Med
            case "8": // FUTA
            default:
                $("#divWagesHoursSelection").hide();
                $("#selDisplayOptions").val('Wages');
                showMultiSelect(false, selValue);
                showSelectionDDL(false, selValue);
                break;

            case "9": // SUTA
            case "10": // State Tax
            case "11": // Local Tax
            case "12": // Worker Comp
                $("#divWagesHoursSelection").hide();
                $("#selDisplayOptions").val('Wages');
                showMultiSelect(false, selValue);
                showSelectionDDL(true, selValue);
                break;
        }
    }

    function showSelectionDDL(e, selValue)
    {
        var dropdownlist = $("#selectionDDL").data("kendoDropDownList");
        var dropdownlistWidget = $("#selectionDDL").closest(".k-widget");
        dropdownlist.value([]);
        if (e)
        {
            var params =
                {
                    companyID: $("#ecompanies").data("kendoDropDownList").value(),
                    clientID: $("#eclients").data("kendoDropDownList").value(),
                    region: $("#eregions").data("kendoDropDownList").value(),
                    division: $("#edivisions").data("kendoDropDownList").value(),
                    payCode: selValue,
                    start: startDate,
                    end: endDate
                };
            dropdownlist.dataSource.read(params);
            dropdownlistWidget.show(250);
        }
        else
        {
            dropdownlistWidget.hide(250);
        }
    }

    function showMultiSelect(e, selValue)
    {
        var multiselect = $("#multiSelect").data("kendoMultiSelect");
        multiselect.value([]);
        if (e)
        {
            var params =
                {
                    companyID: $("#ecompanies").data("kendoDropDownList").value(),
                    clientID: $("#eclients").data("kendoDropDownList").value(),
                    region: $("#eregions").data("kendoDropDownList").value(),
                    division: $("#edivisions").data("kendoDropDownList").value(),
                    payCode: selValue,
                    start: startDate,
                    end: endDate
                };
            multiselect.dataSource.read(params);
            $('[name="multiSelect"]').closest('div').show(250);
        }
        else
        {
            $('[name="multiSelect"]').closest('div').hide(250);
        }
    }

    function payrollItemsParam()
    {
        return { codeType : 'PayrollItemList' }
    }

    function onPayrollItemData()
    {
        var params =
        {
            companyID: $("#ecompanies").data("kendoDropDownList").value(),
            clientID: $("#eclients").data("kendoDropDownList").value(),
            region: $("#eregions").data("kendoDropDownList").value(),
            division: $("#edivisions").data("kendoDropDownList").value(),
            payCode: $("#selPayrollItem").data("kendoDropDownList").value(),
            start: startDate,
            end: endDate

        };

        return params;
    }

    $('#chkComp').click(function() {
        if ($(this).is(':checked')) {
        } else
            $('#selComp').val('0');
        $('#radInt').val('Years');
    });


    $(document).ready(function() {
        $('[name="multiSelect"]').closest('div').hide();
        $("#selectionDDL").closest(".k-widget").hide();
        
    });
</script>
<script src="~/Scripts/chartRedraw.js"></script>