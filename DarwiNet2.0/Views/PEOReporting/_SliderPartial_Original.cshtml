@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Helpers;
<script src="~/Scripts/date.js"></script>
@*<script src="~/Scripts/jQAllRangeSliders-withRuler-min.js"></script>*@
<script src="~/Scripts/jQAllRangeSliders-withRuler-2016-min.js"></script>
<link href="~/Content/iThing-min.css" rel="stylesheet" />
<script src="~/Scripts/jquery.cookie.min.js"></script>

<style>
    div.ui-ruler-tick-inner span.ui-ruler-tick-label {
        vertical-align: top;
    }

    span.ui-ruler-tick-label {
        font-size: 0.65em;
    }
</style>

<div class="col-md-12" style="padding-bottom: 25px;">
    <div class="borderradio">
        <div class="col-md-4">
            <div class="borderradio-thinkware">
                <input type="radio" name="dateSelectionInterval" value="day" id="daily" />
                <label for="daily">Daily</label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="borderradio-thinkware">
                <input type="radio" name="dateSelectionInterval" value="month" checked id="monthly" />
                <label for="monthly">Monthly</label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="borderradio-thinkware">
                <input type="radio" name="dateSelectionInterval" value="year" id="annually" />
                <label for="annually">Annually</label>
            </div>
        </div>
    </div>
</div>
<div class="row" style="padding-bottom: 45px;">
    @if (Html.IsActive("Index", "PEOReporting") == "active")
    {
        <div class="col-md-4">
            <input type="hidden" id="UserID" name="UserID" value="@GlobalVariables.DNETOwnerID" />
            <div>
                @(Html.Kendo().DropDownList()
                  .Name("ecompanies")
                  .HtmlAttributes(new { style = "width:300px" })
                  .OptionLabel("Select Company...")
                  .DataTextField("Description")
                  .DataValueField("Code")
                  .DataSource(source =>
                  {
                      source.Read(read =>
                      {
                          read.Action("GetCascadeCompanies", "PEOReporting")
                              .Data("efilterComps");
                      });
                  })
                )
                <input type="button" value="Build Chart" class="btn btn-danger" onclick="getCharts();" />
            </div>
            <script>
                function efilterComps() {
                    return {
                        u: $("#UserID").val()
                    };
                }
            </script>
        </div>
    }
    <div class="col-md-2">
        <div class="borderradio-thinkware">
            <span style="font-size: 18px; float: left; width: 87px;">Quick Filter</span>
            <span style="float: right; width: 145px;">
                <select id="sliderDateRange" class="form-control">
                    <option value="YTD">Year to Date</option>
                    <option value="CurrentMonth">Current Month</option>
                    <option value="CurrentQuarter">Current Quarter</option>
                </select>
            </span>

        </div>
    </div>
    <div class="col-md-2">
        <div class="borderradio-thinkware">
            <span style="font-size: 18px; float: left; width: 76px;">Start Date</span>
            <span style="float: right; width: 150px;">

                @(Html.Kendo().DatePicker()
                    .Name("startdatepicker")
                    .Start(CalendarView.Year)
                    .Depth(CalendarView.Year)
                    .Format("MMMM yyyy")
                    .Events(e => e.Change("startChange"))
                    .HtmlAttributes(new { style = "width: 100%" }))

            </span>

        </div>
    </div>
    <div class="col-md-2">
        <div class="borderradio-thinkware">
            <span style="font-size: 18px; float: left; width: 70px;">End Date</span>
            <span style="float: right; width: 150px;">
                @(Html.Kendo().DatePicker()
                    .Name("enddatepicker")
                    .Start(CalendarView.Year)
                    .Depth(CalendarView.Year)
                    .Format("MMMM yyyy")
                    .Events(e => e.Change("endChange"))
                    .HtmlAttributes(new { style = "width: 100%" }))
            </span>

        </div>
    </div>



</div>
<div class="row" style="margin-bottom: 10px;" data-step="2" data-intro="Drag the slider to choose a different date range.  You can change the amount of days by using the edge of the bar to make it smaller.">
    <div class="col-md-12">
        <div id="slider"></div>
    </div>
</div>

<!-- Filter Menu -->
@if (Html.IsActive("Index", "PEOReporting") != "active" && Html.IsActiveControl("ClientReporting") != "active" && Html.IsActive("TopBottom", "PEOReporting") != "active")
{
    <div class="row">
        <div class="col-md-6 text-center">
            <!--Begin code from 'Company' selector Drop Down-->
            <div>
                <input type="hidden" id="UserID" name="UserID" value="@GlobalVariables.DNETOwnerID" />
                <div>
                    @(Html.Kendo().DropDownList()
                        .Name("ecompanies")
                        .HtmlAttributes(new { style = "width:300px" })
                        .OptionLabel("Select Company...")
                        .DataTextField("Description")
                        .DataValueField("Code")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                                {
                                    read.Action("GetCascadeCompanies", "PEOReporting")
                                    .Data("efilterComps");
                                });
                        })
                        .Events(e => e.Select("onFilterChange"))
                    )
                </div>
                <script>
                    function efilterComps() {
                        return {
                            u: $("#UserID").val()
                        };
                    }
                </script>

            </div>
            <!--End code from 'Company' selector Drop Down-->
            <!--Begin code from 'Region' selector Drop Down-->
            <div>
                <div>
                    @(Html.Kendo().DropDownList()
                                .Name("eregions")
                                .HtmlAttributes(new { style = "width:300px" })
                                .OptionLabel("Select Region...")
                                .DataTextField("Description")
                                .DataValueField("Code")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetCascadeRegionsAll", "PEOReporting")
                                            .Data("efilterRegions");
                                    })
                                        .ServerFiltering(true);
                                })
                                .Enable(false)
                                .AutoBind(false)
                                .CascadeFrom("ecompanies")
                                .Events(e => e.Select("onFilterChange"))
                    )
                </div>
                <script>
                    function efilterRegions() {
                        return {
                            i: $("#ecompanies").val()
                        };
                    }
                </script>
            </div>
            <!--End code from 'Region' selector Drop Down-->
        </div>


        <div class="col-md-6 text-center">

            <!--Begin code for 'Client' selector drop down-->
            <div>
                <div>
                    @(Html.Kendo().DropDownList()
                        .Name("eclients")
                        .HtmlAttributes(new { style = "width:300px" })
                        .OptionLabel("Select Client...")
                        .DataTextField("Description")
                        .DataValueField("Code")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetCascadeClientsAll", "PEOReporting")
                                    .Data("efilterClients");
                            })
                                .ServerFiltering(true);
                        })
                        .Enable(false)
                        .AutoBind(false)
                        .CascadeFrom("eregions")
                        .Events(e => e.Select("onFilterChange"))
                    )
                </div>
                <script>
                    function efilterClients() {
                        return {
                            i: $("#ecompanies").val(),
                            r: $("#eregions").val(),
                            u: $("#UserID").val()
                        };
                    }
                </script>
            </div>
            <!--End code for 'Client' selector drop down-->
            <!--Begin code for 'Division' selector drop down-->
            <div>
                <div>
                    @(Html.Kendo().DropDownList()
                        .Name("edivisions")
                        .HtmlAttributes(new { style = "width:300px" })
                        .OptionLabel("Select Division...")
                        .DataTextField("Description")
                        .DataValueField("Code")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                            {
                                read.Action("GetCascadeDivisionsAll", "PEOReporting")
                                    .Data("efilterDivisions");
                            })
                                .ServerFiltering(true);
                        })
                        .Enable(false)
                        .AutoBind(false)
                        .CascadeFrom("eclients")
                        .Events(e => e.Select("onFilterChange"))
                    )
                </div>
                <script>
                    function efilterDivisions() {
                        return {
                            i: $("#ecompanies").val(),
                            r: $("#eregions").val(),
                            u: $("#UserID").val(),
                            c: $("#eclients").val()
                        };
                    }
                </script>
            </div>
            <!--End code for 'Division' selector drop down-->

        </div>

    </div>
}

@if (Html.IsActive("TopBottom", "PEOReporting") == "active")
{
    <div class="row">
        <div class="col-md-6 text-center">
            <!--Begin code from 'Company' selector Drop Down-->
            <div>
                <input type="hidden" id="UserID" name="UserID" value="@GlobalVariables.DNETOwnerID" />
                <div>
                    @(Html.Kendo().DropDownList()
                        .Name("ecompanies")
                        .HtmlAttributes(new { style = "width:300px" })
                        .OptionLabel("Select Company...")
                        .DataTextField("Description")
                        .DataValueField("Code")
                        .DataSource(source =>
                        {
                            source.Read(read =>
                                {
                                    read.Action("GetCascadeCompanies", "PEOReporting")
                                    .Data("efilterComps");
                                });
                        })
                        .Events(e => e.Select("onFilterChange"))
                    )
                </div>
                <script>
                    function efilterComps() {
                        return {
                            u: $("#UserID").val()
                        };
                    }
                </script>

            </div>
            <!--End code from 'Company' selector Drop Down-->

        </div>


        <div class="col-md-6 text-center">

            <!--Begin code from 'Region' selector Drop Down-->
            <div>
                <div>
                    @(Html.Kendo().DropDownList()
                                .Name("eregions")
                                .HtmlAttributes(new { style = "width:300px" })
                                .OptionLabel("Select Region...")
                                .DataTextField("Description")
                                .DataValueField("Code")
                                .DataSource(source =>
                                {
                                    source.Read(read =>
                                    {
                                        read.Action("GetCascadeRegionsAll", "PEOReporting")
                                            .Data("efilterRegions");
                                    })
                                        .ServerFiltering(true);
                                })
                                .Enable(false)
                                .AutoBind(false)
                                .CascadeFrom("ecompanies")
                                .Events(e => e.Select("onFilterChange"))
                    )
                </div>
                <script>
                    function efilterRegions() {
                        return {
                            i: $("#ecompanies").val()
                        };
                    }
                </script>
            </div>
            <!--End code from 'Region' selector Drop Down-->

        </div>

    </div>
}

<!-- Date Slider Script -->
<script>
    var chartName;
    var companyid = @GlobalVariables.CompanyID;
    var clientID = '@GlobalVariables.Client';
    var regionid;
    var divisionid;
    var startDate;
    var endDate;
    var chartInterval = "Months";
    var chartAction;
    var months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
    var quarters = ["1","2","3","4"];
    var Today = new Date();

    //Set Bounds Start / End
    if ($.cookie('boundStart-month') != null) {
        var savedMonthStart = $.cookie('boundStart-month');
        var boundMonthStart = new Date(Today.getFullYear() - savedMonthStart, 0, 1);
    } else {
        var boundMonthStart = new Date(Today.getFullYear() - 2, 0, 1);
    }
    //Set Default start and end
    if ($.cookie('startDate-month') == null) {
        var startDefault = new Date(Today.getFullYear(), Today.getMonth()-2, Today.getDate());
    } else {
        var startDefault = new Date($.cookie('startDate-month'));
    }
    if ($.cookie('endDate-month') == null) {
        var endDefault = new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    } else {
        var endDefault = new Date($.cookie('endDate-month'));
    }

    $(document).ready(function() {
        var endDatePicker = $("#enddatepicker").data("kendoDatePicker");
        var todayDate = new Date();
        var maxDate = new Date(todayDate.getFullYear(), 11, 31);
        endDatePicker.max(maxDate);
        setDatePickers(startDefault, endDefault);
    });

    $("#slider").dateRangeSlider({
        bounds: {
            min: boundMonthStart,
            max: new Date(Today.getFullYear(),11,31)
        },
        defaultValues: {
            // *** In production, we'll use these dates. For testing, let's use dates that return data.
            min: startDefault,
            max: endDefault

            //min: new Date(Today.getFullYear() - 3, Today.getMonth(), Today.getDate()),
            //max: new Date(Today.getFullYear() - 2, Today.getMonth(), Today.getDate())
        },
        scales: [{
            first: function (value) { return value; },
            end: function (value) { return value; },
            next: function (value) {
                var next = new Date(value);
                return new Date(next.setMonth(value.getMonth() + 1));
            },
            label: function (value) {
                return months[value.getMonth()];
            }
            ,
            format: function (tickContainer, tickStart, tickEnd) {
                tickContainer.addClass("ui-rangeSlider");
            }
        }],
        step: {
            months: 1
        },
        formatter:function(value){
            var isMaxValue = value.getTime() === $("#slider").dateRangeSlider("values").max.getTime();
            var newValue = value;

            if (isMaxValue) {
                newValue = new Date(value.setDate(value.getDate() - 1));
            }
            else
            {
                var intSelect = $("input:radio[name=dateSelectionInterval]:checked").val();
                if (intSelect == "month") {
                    newValue = new Date(value.getFullYear(), newValue.getMonth(), 1);
                } else if (intSelect == "day") {
                    newValue = value;
                } else if (intSelect == "year") {

                }
            }

            var days = newValue.getDate();
            var month = newValue.getMonth() + 1;
            var year = newValue.getFullYear();

            return month + '/' + days + '/' + year;
        }
    });

    //Format Date to include 0
    function getFormattedPartTime(partTime){
        if (partTime<10)
            return "0"+partTime;
        return partTime;
    }

    $("#slider").bind("valuesChanged", function (e, data) {
        
        var sliderMin = data.values.min;
        var sliderMax = data.values.max;

        //Set cookie for callback to other date type
        $.cookie.raw = true;
        var intSelect = $("input:radio[name=dateSelectionInterval]:checked").val();
        if (intSelect == "month") {
            startDate = new Date(sliderMin.getFullYear(), sliderMin.getMonth(), 1);
            endDate = new Date(sliderMax.getFullYear(), sliderMax.getMonth() + 1, 0);
            $.removeCookie('startDate-month');
            $.cookie('startDate-month', startDate, { path: '/' });
            $.removeCookie('endDate-month');
            $.cookie('endDate-month', endDate, { path: '/' });
        } else if (intSelect == "day") {
            startDate = sliderMin;
            endDate = new Date(sliderMax.getFullYear(), sliderMax.getMonth(), sliderMax.getDate() - 1);
            $.removeCookie('startDate-day');
            $.cookie('startDate-day', startDate, { path: '/' });
            $.removeCookie('endDate-day');
            $.cookie('endDate-day', endDate, { path: '/' });
        } else if (intSelect == "year") {
            startDate = new Date(sliderMin.getFullYear, 1, 1);
            endDate = new Date(sliderMax.getFullYear() + 1, 1, 0);
            $.removeCookie('startDate-year');
            $.cookie('startDate-year', startDate, { path: '/' });
            $.removeCookie('endDate-year');
            $.cookie('endDate-year', endDate, { path: '/' });
        }

        //setVals();
        setDatePickers(startDate, endDate);


        try {
            getCharts();
        } catch (e) { }


    });

    //function setVals() {
    //    var sliderVals = $("#slider").dateRangeSlider("values");
    //    var intSelect = $("input:radio[name=dateSelectionInterval]:checked").val();
    //    if (intSelect == "month") {
    //        startDate = new Date(sliderVals.min.getFullYear(), sliderVals.min.getMonth(), 1);
    //        endDate = new Date(sliderVals.max.getFullYear(), sliderVals.max.getMonth(), 0);
    //    } else if (intSelect == "day") {
    //        startDate = sliderVals.min;
    //        endDate = sliderVals.max;
    //    } else if (intSelect == "year") {
    //        startDate = new Date(sliderVals.min.getFullYear, 1, 1);
    //        endDate = new Date(sliderVals.max.getFullYear() + 1, 1, 0);
    //    }
    //    setDatePickers(startDate, endDate);
    //}

    function getSliderDates() {
        var sliderVals = $("#slider").dateRangeSlider("values");
        startDate = (sliderVals.min.getMonth() + 1) + "/" + sliderVals.min.getDate() + "/" + sliderVals.min.getFullYear();
        var newMax = new Date(sliderVals.max.setDate(sliderVals.max.getDate() -1));
        endDate = (newMax.getMonth() + 1) + "/" + newMax.getDate() + "/" + newMax.getFullYear();
    }

    function startChange() {
        var startDate = this.value();
        var endDate = $("#slider").dateRangeSlider("values").max;

        //var endPicker = $("#enddatepicker").data("kendoDatePicker");
        //var endDate = new Date(endPicker.value());
        var adjustedStartDate = startDate, adjustedEndDate = endDate;

        if (startDate) {
            var intSelect = $("input:radio[name=dateSelectionInterval]:checked").val();
            if (intSelect == "month") {
                adjustedStartDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                adjustedEndDate = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0);
                sliderBoundsMin = new Date(adjustedStartDate.getFullYear() - 2, 0, 1);
                sliderBoundsMax = new Date(adjustedEndDate.getFullYear(), 11, 31);
            } else if (intSelect == "day") {
                sliderBoundsMin = new Date(adjustedStartDate.getFullYear(), adjustedStartDate.getMonth() - 1, 1);
                sliderBoundsMax = new Date(adjustedEndDate.getFullYear(), adjustedEndDate.getMonth() + 1, 7);
            } else if (intSelect == "year") {
                adjustedStartDate = new Date(startDate.getFullYear(), 0, 1);
                adjustedEndDate = new Date(endDate.getFullYear() + 1, 0, 0);
            }
            $("#slider").dateRangeSlider({
                bounds: {
                    min: sliderBoundsMin,
                    max: sliderBoundsMax
                }
            });
            //endPicker.min(startDate);
            $("#slider").dateRangeSlider("values", adjustedStartDate, adjustedEndDate);
        }
    }

    function endChange() {
        var endDate = this.value();
        var startDate = $("#slider").dateRangeSlider("values").min;

        //var startPicker = $("#startdatepicker").data("kendoDatePicker");
        //var startDate = new Date(startPicker.value());
        var adjustedStartDate = startDate, adjustedEndDate = endDate;

        if (endDate) {
            var intSelect = $("input:radio[name=dateSelectionInterval]:checked").val();
            if (intSelect == "month") {
                adjustedStartDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                adjustedEndDate = new Date(endDate.getFullYear(), endDate.getMonth() + 1, 0);
                sliderBoundsMin = new Date(adjustedStartDate.getFullYear() - 2, 0, 1);
                sliderBoundsMax = new Date(adjustedEndDate.getFullYear(), 11, 31);
            } else if (intSelect == "day") {
                sliderBoundsMin = new Date(adjustedStartDate.getFullYear(), adjustedStartDate.getMonth() - 1, 1);
                sliderBoundsMax = new Date(adjustedEndDate.getFullYear(), adjustedEndDate.getMonth() + 1, 7);
            } else if (intSelect == "year") {
                adjustedStartDate = new Date(startDate.getFullYear(), 0, 1);
                adjustedEndDate = new Date(endDate.getFullYear() + 1, 0, 0);
            }
            $("#slider").dateRangeSlider({
                bounds: {
                    min: sliderBoundsMin,
                    max: sliderBoundsMax
                }
            });
            //startPicker.max(endDate);
            $("#slider").dateRangeSlider("values", adjustedStartDate, adjustedEndDate);
        }
    }

    function setDatePickers(setStartDate, setEndDate)
    {
        var startDatePicker = $("#startdatepicker").data("kendoDatePicker");
        var endDatePicker = $("#enddatepicker").data("kendoDatePicker");
        var adjustedStartDate, adjustedStartDateString, adjustedEndDate, adjustedEndDateString;

        var intSelect = $("input:radio[name=dateSelectionInterval]:checked").val();
        if (intSelect == "month") {
            adjustedStartDate = new Date(setStartDate.getFullYear(), setStartDate.getMonth(), 1);
            adjustedEndDate = new Date(setEndDate.getFullYear(), setEndDate.getMonth() + 1, 0);
            adjustedStartDateString = kendo.toString(adjustedStartDate, "MMMM yyyy");
            adjustedEndDateString = kendo.toString(adjustedEndDate, "MMMM yyyy");
            
        } else if (intSelect == "day") {
            adjustedStartDate = setStartDate;
            adjustedEndDate = setEndDate;
            adjustedStartDateString = kendo.toString(setStartDate, "MM/dd/yyyy");
            adjustedEndDateString = kendo.toString(setEndDate, "MM/dd/yyyy");
        } else if (intSelect == "year") {
            adjustedStartDate = new Date(setStartDate.getFullYear(), 0, 1);
            adjustedEndDate = new Date(setEndDate.getFullYear() + 1, 0, 0);
        }

        if (startDatePicker)
        {
            //startDatePicker.min(new Date(1900,0,1));
            //startDatePicker.max(new Date(2099,11,31));
            startDatePicker.value(adjustedStartDateString);
            startDatePicker.max(adjustedEndDate);
        }

        if (endDatePicker)
        {
            //endDatePicker.min(new Date(1900,0,1));
            //endDatePicker.max(new Date(2099,11,31));
            endDatePicker.value(adjustedEndDateString);
            endDatePicker.min(adjustedStartDate);
        }
    }
</script>

<!-- Date Interval Selectors -->
<script>

    function selectSliderRange(range)
    {
        var sliderDateRange = range;

        switch (sliderDateRange) {
            case "YTD":
                $("#slider").dateRangeSlider("values", new Date(Today.getFullYear(), 1, 1), new Date(Today.getFullYear(), Today.getMonth(), Today.getDate()));
                break;
            case "CurrentMonth":
                $("#slider").dateRangeSlider("values", new Date(Today.getFullYear(), Today.getMonth(),1), new Date(Today.getFullYear(), Today.getMonth(), Today.getDate()));
                break;
            case "CurrentQuarter":
                $("#slider").dateRangeSlider("values", new Date(2012, 0, 1), new Date(2012, 0, 31));
                break;
        }
    }
    $('#sliderDateRange').on('change', function() {
        var range = $(this).val();
        selectSliderRange(range);
    });
    $("input:radio[name=dateSelectionInterval]").click(function() {
        var selection = $(this).val();
        var oneMonthAgo = (1).months().ago().set({hour: 0, minute: 0, second: 0});
        var twoMonthAgo = (1).months().ago().set({day: 1, hour: 0, minute: 0, second: 0});
        var oneYear = new Date(new Date().getFullYear(), 0, 1);
        var endOneYear = new Date(new Date().getFullYear(), 11, 31);
        var maxBounds;
        var daysYear;
        var startDatePicker, startDateView, endDatePicker, endDateView;

        if (Date.isLeapYear === true) {
            daysYear = 366;
        } else {
            daysYear = 365;
        }
        switch (selection) {
            case "day":
                ival = 'days';
                var start1 = new Date($.cookie('startDate-day'));
                var end1 = new Date($.cookie('endDate-day'));

                if ($.cookie('boundStart-day') != null) {
                    var savedDay = $.cookie('boundStart-day');
                    var boundYear1 = new Date(Today.getFullYear(), Today.getMonth() - savedDay, 1);
                } else {
                    var boundYear1 = twoMonthAgo;
                }

                maxBounds = new Date(end1.getFullYear(), end1.getMonth() + 2, 0);

                $('#slider').dateRangeSlider({
                    bounds: {
                        min: boundYear1,
                        max: maxBounds
                        //min: $.cookie('startDate-day'),
                        //max: $.cookie('endDate-day')
                    },
                    step:{
                        days: 1
                    },
                    scales: [{
                        first: function (value) { return value; },
                        end: function (value) { return value; },
                        next: function (value) {
                            var next = new Date(value);
                            return new Date(next.setDate(value.getDate() + 1));
                        },
                        label: function (value) {
                            return (value.getMonth() + 1)  + "-" + value.getDate();
                        },
                        format: function (tickContainer, tickStart, tickEnd) {
                            tickContainer.addClass("aTickClass");
                        }
                    }],
                    range:{
                        min: {days: 1},
                        max: false
                    }
                })
                if ($.cookie('startDate-day') == null) {
                    $("#slider").dateRangeSlider("values", oneMonthAgo, maxBounds);
                } else {
                    $("#slider").dateRangeSlider("values", start1, end1);
                }
                
                startDatePicker = $("#startdatepicker").data("kendoDatePicker");
                startDateView = startDatePicker.dateView;
                endDatePicker = $("#enddatepicker").data("kendoDatePicker");
                endDateView = endDatePicker.dateView;

                startDatePicker.options.depth = startDateView.options.depth = startDatePicker.options.start = startDateView.options.start = 
                    endDatePicker.options.depth = endDateView.options.depth = endDatePicker.options.start = endDateView.options.start = "month";
                endDatePicker.options.format = endDateView.options.format = startDatePicker.options.format = startDateView.options.format = "MM/dd/yyyy";
                //startDatePicker.value(start1);
                //startDatePicker.max(end1);
                //endDatePicker.value(end1);
                //endDatePicker.min(start1);
                break;
            case "month":
                ival = 'months';
                var start2 = new Date($.cookie('startDate-month'));
                var end2 = new Date($.cookie('endDate-month'));

                if ($.cookie('boundStart-month') != null) {
                    var savedMonth = $.cookie('boundStart-month');
                    var boundMonth2 = new Date(Today.getFullYear() - 2, savedMonth, 1);
                } else {
                    var boundMonth2 = new Date(Today.getFullYear() - 2, 0, 1);
                }
                maxBounds = new Date(end2.getFullYear(), 11, 31);
                $('#slider').dateRangeSlider({
                    bounds: {
                        min: boundMonth2,
                        max: maxBounds
                    },
                    step:{
                        months: 1
                    },
                    scales: [{
                        first: function (value) { return value; },
                        end: function (value) { return value; },
                        next: function (value) {
                            var next = new Date(value);
                            return new Date(next.setMonth(value.getMonth() + 1));
                        },
                        label: function (value) {
                            return months[value.getMonth()];
                        },
                        format: function (tickContainer, tickStart, tickEnd) {
                            tickContainer.addClass("aTickClass");
                        }
                    }],
                    range:{
                        min: {months: 1},
                        max: false
                    }
                });
                if ($.cookie('startDate-month') == null) {
                    $("#slider").dateRangeSlider("values", oneMonthAgo, maxBounds);
                } else {
                    $("#slider").dateRangeSlider("values", start2, end2);
                }
                chartInterval = "Months";
                
                startDatePicker = $("#startdatepicker").data("kendoDatePicker");
                startDateView = startDatePicker.dateView;
                endDatePicker = $("#enddatepicker").data("kendoDatePicker");
                endDateView = endDatePicker.dateView;

                startDatePicker.options.depth = startDateView.options.depth = startDatePicker.options.start = startDateView.options.start = 
                    endDatePicker.options.depth = endDateView.options.depth = endDatePicker.options.start = endDateView.options.start = "year";
                startDatePicker.options.format = startDateView.options.format = endDatePicker.options.format = endDateView.options.format = "MMMM yyyy";
                //startDatePicker.value(start2);
                //startDatePicker.max(end2);
                //endDatePicker.value(end2);
                //endDatePicker.min(start2);
                break;
            case "quarter":
                $('#slider').dateRangeSlider({
                    bounds: {
                        min: new Date(Today.getFullYear() - 2, 0, 1),
                        max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
                    },
                    step:{
                        months: 3
                    },
                    scales: [{
                        first: function (value) { return value; },
                        end: function (value) { return value; },
                        next: function (value) {
                            var next = new Date(value);
                            return new Date(next.setMonth(value.getMonth() + 1));
                        },
                        label: function (value) {
                            return months[value.getMonth()];
                        },
                        format: function (tickContainer, tickStart, tickEnd) {
                            tickContainer.addClass("aTickClass");
                        }
                    }],
                    range:{
                        min: {months: 3},
                        max: {months: 3}
                    }
                });
                break;
            case "year":
                if ($.cookie('boundStart-year') != null) {
                    var savedYear = $.cookie('boundStart-year');
                    var boundYear3 = new Date(Today.getFullYear() - savedYear, 0, 1);;
                } else {
                    var boundYear3 = new Date(Today.getFullYear() - 3, 0, 1);
                }
                $('#slider').dateRangeSlider({
                    bounds: {
                        min: boundYear3,
                        max: new Date(Today.getFullYear(), 11, 31)
                    },
                    step: {
                        days: daysYear
                    },
                    scales: [{
                        first: function (value) { return value; },
                        end: function (value) { return value; },
                        next: function (value) {
                            var next = new Date(value);
                            return new Date(next.setYear(value.getFullYear() + 1));
                        },
                        label: function(value){ return value.getFullYear(); },
                        format: function (tickContainer, tickStart, tickEnd) {
                            tickContainer.addClass("aTickClass");
                        }
                    }],
                    range:{
                        min: {years: 1},
                        max: false
                    }

                });
                var start3 = new Date($.cookie('startDate-year'));
                var end3 = new Date($.cookie('endDate-year'));
                if ($.cookie('startDate-year') == null) {
                    $("#slider").dateRangeSlider("values", oneYear, endOneYear);
                } else {
                    $("#slider").dateRangeSlider("values", start3, end3);
                }
                chartInterval = "Years";
                ival = 'years';
                break;
        }
    });
    //$( document ).ready(function() {
    //    var year = 2; // abstracting the year away from the click function
    //    var quarterly = 2;
    //    var month = 2;
    //    var day = 1;
    //    $('#adddates').click(function() {
    //        $.cookie.raw = true;
    //        var range = $('input:radio[name=dateSelectionInterval]:checked').val();
    //        if (range === "year") {
    //            $("#slider").dateRangeSlider({
    //                bounds: {
    //                    min: new Date(Today.getFullYear() - ++year, 0, 1),
    //                    max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                }
    //            });
    //            //Set Bounds by User
    //            $.removeCookie('boundStart-year');
    //            $.cookie('boundStart-year', year, { path: '/' });
    //        } else if (range === "day"){
    //            $("#slider").dateRangeSlider({
    //                bounds: {
    //                    min: new Date(Today.getFullYear(), Today.getMonth() - ++day, 1),
    //                    max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                }
    //            });
    //            //Set Bounds by User
    //            $.removeCookie('boundStart-day');
    //            $.cookie('boundStart-day', day, { path: '/' });
    //        } else if (range === "month"){
    //            $("#slider").dateRangeSlider({
    //                bounds: {
    //                    min: new Date(Today.getFullYear() - ++month, 0, 1),
    //                    max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                }
    //            });
    //            //Set Bounds by User
    //            $.removeCookie('boundStart-month');
    //            $.cookie('boundStart-month', month, { path: '/' });
    //        } else if (range === "quarter"){
    //            $("#slider").dateRangeSlider({
    //                bounds: {
    //                    min: new Date(Today.getFullYear() - ++quarterly, 0, 1),
    //                    max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                }
    //            });
    //        }
    //    });
    //    $('#subtractdates').click(function() {
    //        var range = $('input:radio[name=dateSelectionInterval]:checked').val();
    //        if (range === "year") {
    //            if (year !== 1) {
    //                $("#slider").dateRangeSlider({
    //                    bounds: {
    //                        min: new Date(Today.getFullYear() - --year, 0, 1),
    //                        max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                    }
    //                });
    //            }
    //        } else if (range === "day"){
    //            if (day !== 1) {
    //                $("#slider").dateRangeSlider({
    //                    bounds: {
    //                        min: new Date(Today.getFullYear(), Today.getMonth() - --day, 1),
    //                        max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                    }
    //                });
    //            }
    //        } else if (range === "month"){
    //            if (month !== 1) {
    //                $("#slider").dateRangeSlider({
    //                    bounds: {
    //                        min: new Date(Today.getFullYear() - --month, 0, 1),
    //                        max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                    }
    //                });
    //            }
    //        } else if (range === "quarter"){
    //            if (quarterly !== 1) {
    //                $("#slider").dateRangeSlider({
    //                    bounds: {
    //                        min: new Date(Today.getFullYear() - --quarterly, 0, 1),
    //                        max: new Date(Today.getFullYear(), Today.getMonth(), Today.getDate())
    //                    }
    //                });
    //            }
    //        }
    //    });
    //});
</script>

@if (Html.IsActiveControl("ClientReporting") != "active")
{
    <script>
        var chart = "@Url.Action("Trailing12Chart_GrossWages", "PEOREporting", new { companyID = GlobalVariables.CompanyID, start = new DateTime?(), end = new DateTime?() })";
    </script>
}
<script>
    $(document).on('click', '.sidebar-toggle', function () {
        setTimeout(function () {
            $("#slider").resize();
        }, 350);
    });
</script>
<script>
    function onFilterChange(e)
    {
        var isPEOReportingIndex = "@Html.IsActive("PayrollDetails", "PEOReporting")" === "active";
        if (isPEOReportingIndex)
        {
            var idDDL = e.sender.element.prop("id");
            var selPayrollItem = $("#selPayrollItem").data("kendoDropDownList");
            var multiselect = $('[name="multiSelect"]').closest('div');
            var dropdownlistWidget = $("#selectionDDL").closest(".k-widget");
            if (idDDL === "ecompanies" || idDDL === "eregions" )
            {
                selPayrollItem.enable(false);
                selPayrollItem.select(0);
                dropdownlistWidget.hide(250);
                multiselect.hide(250);
            }
            else if (idDDL === "eclients")
            {
                if (this.dataItem(e.item).Code)
                {
                    selPayrollItem.enable();
                }
                else
                {
                    selPayrollItem.enable(false);
                    selPayrollItem.select(0);
                    dropdownlistWidget.hide(250);
                    multiselect.hide(250);
                }
            }
        }

        var isPEOReportingFees = "@Html.IsActive("Fees", "PEOReporting")" === "active";
        if (isPEOReportingFees)
        {
            $("#chargeTypeSelectionDDL").closest(".k-widget").hide(250);
            $("#chargeType").data("kendoDropDownList").value("");
        }
    }
</script>