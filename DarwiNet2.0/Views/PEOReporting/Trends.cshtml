@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;

@{
    ViewBag.Title = "Trends";
    ViewBag.ParentCrumb = "PEO Reporting";
}

<style>
    div.horizontalcontainer {
        margin: auto;
        margin-top: 20px;
        text-align: center;
    }

    ul.horizontal {
        list-style-type: none;
    }

        ul.horizontal li {
            display: inline;
        }

    table.datatable {
        table-layout: auto;
        text-align: center;
        width: 100%;
    }

        table.datatable td {
            border-top: solid 1px #cfcfcf;
            padding-left: 4px;
            padding-right: 8px;
            font-size: 0.9em;
            text-align: right;
        }

            table.datatable td.subsectionheader {
                background-color: #244061;
                margin-top: 30px;
                padding: 4px;
                text-align: center;
                color: #fff;
                font-size: 1em;
                font-weight: bold;
            }

            table.datatable td.tablemultiheader {
                border-left: solid 2px #cfcfcf;
                text-align: center;
                background-color: #B1AEA6;
                font-size: 0.75em;
            }

            table.datatable td.tableheaderleft {
                border-left: solid 2px #efefef;
                padding-right: 8px;
                background-color: #D1CEC6;
                font-size: 0.6em;
            }

            table.datatable td.tableheader {
                border-left: solid 1px #efefef;
                padding-right: 8px;
                background-color: #D1CEC6;
                font-size: 0.6em;
            }

            table.datatable td.companynamecell {
                width: 280px;
                white-space: nowrap;
                text-align: left;
            }

    td.totalhdr {
        font-size: 1.5em;
        color: #808080;
        text-align: center;
    }

    td.total {
        font-size: 2em;
        color: #808080;
        text-align: center;
        font-weight: bold;
        padding-left: 20px;
        padding-right: 20px;
    }

    .clientEmployeeDiv {
        display: none;
    }

    .toggleClientEmployee {
        position: absolute;
        left: -30px;
        top: 55px;
    }
</style>

<script src="~/Scripts/loading.js"></script>
<script>
    var companyID = @GlobalVariables.CompanyID;
    var regionID;
    var clientID;
    var divisionID;
    var startDate;
    var endDate;
    var trendID;

    function getCharts()
    {
        if ($("#ecompanies").val())
            companyID = $("#ecompanies").val()
        else
            companyID = @GlobalVariables.CompanyID;

        regionID =  $("#eregions").val();
        clientID =  $("#eclients").val();
        divisionID =  $("#edivisions").val();
        trendID = $("#select-chart").val();

        getSliderDates();
        $('#ChartDiv').load('@Url.Action("TrendsChartView", "PEOReporting")', { companyID : companyID, regionID : regionID, clientID: clientID, divisionID : divisionID, trendID : trendID, start : startDate, end : endDate }, function(response, status, xhr) {});
    }
</script>

@Html.Partial("~/Views/PEOReporting/_SliderPartial.cshtml")

<div class="row" style="margin-top: 25px;">
    <div class="col-md-6 text-center">
        <label>Select Trend:</label>
        @(Html.Kendo().DropDownList()
            .Name("select-chart")
            .HtmlAttributes(new { style = "width:300px" })
            .DataTextField("Text")
            .DataValueField("Value")
            .DataSource(s => s.Read(r => { r.Action("TrendsGetChartSelection", "PEOReporting").Data("getClientID"); }))
            .Events(e => e.Change("getCharts"))
        )
    </div>
    <div class="col-md-2">
        <input type="button" value="Build Chart" class="btn btn-danger" onclick="getCharts();" />
    </div>
</div>

<div class="col-md-12" id="peo-div">
    <div class="subsectionheader"> Trends </div>
</div>

<div class="row">
    <div class="col-md-12" style="padding-top: 25px;">
        <div id="ChartDiv"></div>
    </div>
</div>
<script src="~/Scripts/chartRedraw.js"></script>
<script>
    function getClientID()
    {
        return { clientID : $("#eclients").val() };
    }

    $( document ).ready(function()
    {
        $("#ecompanies").data("kendoDropDownList").bind("change", onClientChange);
        $("#eregions").data("kendoDropDownList").bind("change", onClientChange);
        $("#eclients").data("kendoDropDownList").bind("change", onClientChange);
    });

    function onClientChange() {
        $("#select-chart").data("kendoDropDownList").dataSource.read();
    }
</script>