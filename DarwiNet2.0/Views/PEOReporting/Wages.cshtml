@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;

@{
    ViewBag.Title = "Wages";
    ViewBag.ParentCrumb = "PEO Reporting,Payroll";
}

<style>
    div.horizontalcontainer {
        margin: auto;
        margin-top: 20px;
        text-align: center;
    }

    ul.horizontal {
        list-style-type: none;
    }

        ul.horizontal li {
            display: inline;
        }

    table.datatable {
        table-layout: auto;
        text-align: center;
        width: 100%;
    }

        table.datatable td {
            border-top: solid 1px #cfcfcf;
            padding-left: 4px;
            padding-right: 8px;
            font-size: 0.9em;
            text-align: right;
        }

            table.datatable td.subsectionheader {
                background-color: #244061;
                margin-top: 30px;
                padding: 4px;
                text-align: center;
                color: #fff;
                font-size: 1em;
                font-weight: bold;
            }

            table.datatable td.tablemultiheader {
                border-left: solid 2px #cfcfcf;
                text-align: center;
                background-color: #B1AEA6;
                font-size: 0.75em;
            }

            table.datatable td.tableheaderleft {
                border-left: solid 2px #efefef;
                padding-right: 8px;
                background-color: #D1CEC6;
                font-size: 0.6em;
            }

            table.datatable td.tableheader {
                border-left: solid 1px #efefef;
                padding-right: 8px;
                background-color: #D1CEC6;
                font-size: 0.6em;
            }

            table.datatable td.companynamecell {
                width: 280px;
                white-space: nowrap;
                text-align: left;
            }

    td.totalhdr {
        font-weight: bold;
    }

    td.total {
        font-size: 2em;
        color: #808080;
        text-align: center;
        font-weight: bold;
        padding-left: 20px;
        padding-right: 20px;
    }
</style>

<script src="~/Scripts/loading.js"></script>
<script>
    var companyID = @GlobalVariables.CompanyID;
    var clientID;
    var regionID;
    var divisionID;
    var startDate;
    var endDate;
    var detailBy = 'Client';
    var compareType;
    var compareValue;
    var chartUrl = "@Url.Action("Payroll_Wages", "PEOReporting")";

    function getCharts()
    {
        regionID =  $("#eregions").val();
        clientID =  $("#eclients").val();
        divisionID =  $("#edivisions").val();
        detailBy = $("#detailBy").val();
        compareType = $("#radInt").val();
        compareValue = $("#selComp").val();
        if($("#chkComp:checked").val() === "on")
        {
            useIntervals = true;
        }
        else
        {
            useIntervals = false;
            compareType = 'None';
        }

        getSliderDates();
        $('#ChartDiv').load(chartUrl, {companyID: companyID, regionID : regionID, clientID : clientID, divisionID : divisionID, detail: detailBy, compareType: compareType, compareValue: compareValue, start : startDate, end : endDate}, function(response, status, xhr) {});
    }
</script>

@Html.Partial("~/Views/PEOReporting/_SliderPartial.cshtml")

<div class="row">
    <div>
        <h2>Display Options:</h2>
        <div class="col-md-12">
            <div class="col-md-1 control-label">
                <label>Detail By:</label>
            </div>
            <div class="col-md-6">
                @(Html.Kendo().DropDownList()
                      .Name("detailBy")
                      .HtmlAttributes(new { style = "width:300px" })
                      .OptionLabel("Select Detail...")
                      .DataTextField("Description")
                      .DataValueField("Code")
                      .DataSource(source =>
                      {
                          source.Read(read =>
                          {
                              read.Action("GetDetailTypesAll", "PEOReporting");
                          });
                      })
                )
                <input type="button" value="Build Chart" class="btn btn-danger" onclick="getCharts();" />
            </div>
        </div>
        <div class="col-md-12" style="padding-top: 25px;">
            <div class="col-md-1">
                <input type="checkbox" id="chkComp" name="chkComp" />
            </div>
            <div class="col-md-2 control-label">
                <label>Comparative:</label>
            </div>
            <div class="col-md-4">
                <select id="selComp" name="selComp" class="form-control">
                    <option value="0">Select # of Intervals</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                </select>
            </div>
            <div class="col-md-4">
                <select id="radInt" class="form-control">
                    <option value="Years">Years</option>
                    <option value="Intervals">Intervals</option>
                </select>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12" style="padding-top: 25px;">
        <div id="ChartDiv" class="row"></div>
    </div>
</div>

<script>
    $('#chkComp').click(function() {
        if ($(this).is(':checked')) {
        } else
            $('#selComp').val('0');
        $('#radInt').val('Years');
    });
</script>
<script src="~/Scripts/chartRedraw.js"></script>