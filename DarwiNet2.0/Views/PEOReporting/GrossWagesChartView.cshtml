@using DarwiNet2._0.Data;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Controllers;
@{
    Layout = null;
}
<div>
    @(Html.Kendo().Chart<PEOFeesModel>()
                .Name("chart")
            //.Title("Gross Wages by Type")
                .Legend(legend => legend
                    .Position(ChartLegendPosition.Top)
                )
                .DataSource(ds => ds.Read(read => read.Action(ViewBag.ReadAction, "PEOReporting", new { companyID = ViewBag.CompanyID, start = ViewBag.StartDate, end = ViewBag.EndDate, codeType = ViewBag.CodeType }))
            //.Group(group => group.Add(model => model.Code))
                )
                .Series(series =>
                {
                    series.Column(model => model.Total).Name("Total");
                })
                .CategoryAxis(axis => axis
                    .Categories(model => model.ClientID)
                    .Labels(labels => labels.Rotation(-90))
                    .MajorGridLines(lines => lines.Visible(false))
                )
                .ValueAxis(axis => axis.Numeric()
                    .Labels(labels => labels.Format("{0:c0}"))
                    .Line(line => line.Visible(false))
                )
                .Tooltip(tooltip => tooltip
                   .Visible(true)
                   .Format("{0:c0}")
               )
    )
</div>
