@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.DNetSynch;
@using OBDocVerification = DataDrivenViewEngine.Models.Core.enOBDocVerification

@model IEnumerable<DarwiNet2._0.Data.OBClientSetupDocument>

@{
    ViewBag.Title = "Assign Documents - " + @GlobalVariables.ProfileName;

    var mySetupID = HttpContext.Current.Request.RequestContext.RouteData.Values["id"];
    //Do not change
    ViewBag.Page = "OBClientSetupTaskFieldsDocument";
}

@section styles{
    <style>
        .demo-section input {
            height: 27px !important;
        }

        .configuration .k-textbox {
            width: 40px;
        }
        .k-button {
            min-width: 80px;
        }

        .configuration-horizontal .options li {
            padding: 3px 0;
        }

        .configHead {
            padding-top: 5px;
        }
    </style>
}

@{
    var AllDocuments = "";
    foreach (var doc in (List<DarwiNet2._0.Controllers.OBClientSetupDocumentsController.OBForms>)ViewBag.DocList)
    {
        AllDocuments += doc.ID + ",";
    }

}

<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>

    </div>
</div>

@Html.Partial("~/Views/Navigation/_ClientPartialTabs.cshtml", mySetupID)

<div class="row">

    <div class="toolbar create-pad-left">

        <div class="row">
            <div class="col-md-12">
                <div class="col-md-3 pull-left">
                    <div class="input-group">
                        <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                        <input type="text" class="form-control" id='FieldFilter' placeholder="Search Document Name">
                    </div>
                </div>
                <div class="pull-right create-pad">
                    <a href="@Url.Action("Index", "OBClientSetupTasks", new { id = ViewBag.ProfileID })" class="btn btn-thinkware"><i class="fa fa-tasks fa-fw fa-lg"></i>Back to Tasks</a>
                    <button type="button" class="btn btn-thinkware" data-toggle="modal" data-target="#assignModal">
                        <i class="fa fa-plus-circle fa-lg fa-fw"></i>Assign New
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="table-bottom table-body">
        <table class="table" id="assigned">
            <tr>
                <th title="@FieldTranslation.GetLabel("Document Name", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Document Name", GlobalVariables.LanguageID)
                </th>

                <th title=" @FieldTranslation.GetLabel("Verification", GlobalVariables.LanguageID) ">
                    @FieldTranslation.GetLabel("Verification", GlobalVariables.LanguageID)
                </th>
                <th title="@FieldTranslation.GetLabel("Manual Update", GlobalVariables.LanguageID)">
                    @FieldTranslation.GetLabel("Manual Update", GlobalVariables.LanguageID)
                </th>
                <th width="120px;" class="hidden-filter">
                    <div style="visibility: hidden;">
                        @FieldTranslation.GetLabel("Controls", GlobalVariables.LanguageID)
                    </div>
                </th>
            </tr>
            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @Html.DisplayFor(modelItem => item.DocumentName)
                    </td>
                    <td>
                        @FieldTranslation.GetLabel(@FieldTranslation.GetEnumDescription(typeof(OBDocVerification), (int)item.Verification), @GlobalVariables.LanguageID)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.ManualUpdate)
                    </td>
                    <td>
                        <a href="@Url.Action("Edit", "OBClientSetupDocuments", new { id = item.DocumentID, sid = item.SetupID })"><i class='icon-edit fa fa-pencil fa-lg fa-fw' title='Edit'></i></a>
                        @if (FieldTranslation.DocumentHasFields(2, item.DocumentID, item.SetupID))
                        {
                            <a href="@Url.Action("ClientIndex", "OBDocumentMapping", new { id = item.DocumentID, sid = item.SetupID, tid = item.TaskID })"><i class='icon-lynch fa fa-sitemap fa-lg fa-fw' title='Map Fields'></i></a>
                        }
                        else
                        {
                        <i style="color: #ccc" class=" fa fa-sitemap fa-lg fa-fw" data-toggle="tooltip" data-placement="right" title="This document contains no mappable fields."></i>
                        }
                        <a href="@Url.Action("PreviewPDF", "OBProcess", new { id = item.DocumentID, lvl = 2, pid = item.SetupID, tid = item.TaskID })"><i class='icon-blue fa fa-eye fa-lg fa-fw' title='Preview Document'></i></a>
                        <a href="#" id="delete" data-id="@item.SetupID" data-tid="@item.TaskID" data-did="@item.DocumentID" data-doc="@item.DocumentName"><i class="icon-red fa fa-times fa-fw fa-lg" title="Delete"></i></a>
                    </td>
                </tr>
            }
        </table>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="assignModal" tabindex="-1" role="dialog" aria-labelledby="assignModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="assignModalLabel">Assign Documents</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <button class="btn btn-thinkware" id="setValue">Select All</button>
                                    <button class="btn btn-thinkware" id="unsetValue">De-Select All</button><br />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row">
                                <span class="col-md-4">
                                    <select id="filterop" class="form-control">
                                        <option value="startswith">Starts with</option>
                                        <option value="contains">Contains</option>
                                        <option value="eq">Equal</option>
                                    </select>
                                </span>
                                <span class="col-md-8">
                                    <input id="word" value="" class="form-control" />
                                </span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <div class="pull-right">
                                    <span class="col-md-12">
                                        <button id="find" class="btn btn-thinkware">Find item</button>
                                        <input type="hidden" id="allDocs" />
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row assignment-top plain-editor">
                    <div class="col-md-12">
                        <p class="pull-right">

                        </p>
                        @using (Html.BeginForm("Assign", "OBClientSetupDocuments", new { id = ViewBag.ProfileID, tid = ViewBag.TaskID }, FormMethod.Post, null))
                        {
                            @*@Html.AntiForgeryToken()*@
                            <div class="demo-section">
                                <div class="form-horizontal">
                                    <div class="form-group">
                                        <label class="control-label col-md-3">Select Documents</label>
                                        <div class="col-md-6">
                                            @(Html.Kendo().MultiSelect()
                                                .Name("DocumentsToAssign")
                                                .DataTextField("Name")
                                                .DataValueField("ID")
                                                .Placeholder("Select Documents...")
                                                .AutoBind(true)
                                                .BindTo(new List<OBClientSetupDocumentsController.OBForms>(ViewBag.Doclist))
                                            )
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="pull-right">
                                <button type="button" class="btn btn-thinkware" data-dismiss="modal">Close</button>
                                <button class="btn btn-thinkware" id="get" type="Submit">Assign Documents</button>
                            </div>

                        }
                    </div>
                </div>

            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>




@section scripts{
    <script src="~/Scripts/bootbox.min.js"></script>
    <script>
        $(document).ready(function () {

            var multiselect = $("#DocumentsToAssign").data("kendoMultiSelect"),
            setValue = function (e) {
                if (e.type != "keypress" || kendo.keys.ENTER == e.keyCode) {

                    multiselect.dataSource.filter({}); //clear applied filter before setting value
                    var allDocs = '@AllDocuments';
                    multiselect.value(allDocs.split(","));
                }
            },
            unsetValue = function (e) {

                if (e.type != "keypress" || kendo.keys.ENTER == e.keyCode) {

                    multiselect.dataSource.filter({}); //clear applied filter before setting value
                    var allDocs = "";
                    multiselect.value({});
                }
            },
            setSearch = function (e) {
                if (e.type != "keypress" || kendo.keys.ENTER == e.keyCode) {
                    multiselect.search($("#word").val());
                }
            };

            $("#enable").click(function () {
                multiselect.enable();
            });

            $("#disable").click(function () {
                multiselect.enable(false);
            });

            $("#readonly").click(function () {
                multiselect.readonly();
            });

            $("#open").click(function () {
                multiselect.open();
            });

            $("#close").click(function () {
                multiselect.close();
            });

            $("#getValue").click(function () {
            });
            $("#filterop").change(function () {
                filterTypeOnChanged();
            });
            $("#setValue").click(setValue);
            $("#value").keypress(setValue);
            $("#unsetValue").click(unsetValue);
            $("#find").click(setSearch);

            $("#word").keypress(setSearch);

            function filterTypeOnChanged() {
                multiselect.options.filter = $("#filterop").val();
            }
        });
    </script>
    <script>
        $(document).ready(function () {
            var grid = $("#assigned").kendoGrid({
                toolbar: ["excel"],
                excel: {
                    fileName: "DocumentAssign.xlsx",
                    filterable: true
                },
                dataSource: {
                    pageSize: 15
                },
                sortable: true,
                pageable: true,
                filterable: true,
                groupable: true,
                scrollable: false,
                resizable: true
            }).data("kendoGrid");

        });
    </script>
    <script>
        $(document).on("click", "td #delete", function (e) {
            var deletedID = $(this).attr('data-id')
            var url = "@Url.Action("Delete", "OBClientSetupDocuments", null)"
            var deleteTaskID = $(this).attr('data-tid')
            var deletedId = $(this).attr('data-did')
            var doc = $(this).attr('data-doc')
            bootbox.dialog({
                message: "Are you sure you want to delete: " + "<strong>" + doc + "</strong>",
                title: "Delete Document",
                buttons: {
                    main: {
                        label: "Cancel",
                        className: "btn-primary",
                        callback: function () {
                            //Example.show("Primary button");
                        }
                    },
                    danger: {
                        label: "Delete",
                        className: "btn-danger",
                        callback: function () {
                            window.location.href = url + "/?id=" + deletedID + "&dId=" + deletedId + "&tid=" + deleteTaskID;
                        }
                    }
                }
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            $("#FieldFilter").keyup(function () {

                var value = $("#FieldFilter").val();
                var grid = $("#assigned").data("kendoGrid");

                if (value) {
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            { field: "DocumentName", operator: "contains", value: value }
                        ]
                    })
                } else {
                    grid.dataSource.filter({});
                }
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            // create Editor from textarea HTML element with default set of tools
            kendo.ui.editor.ColorTool.prototype.options.palette = "basic";
            $(".editor").kendoEditor({
                resizable: {
                    content: true,
                    toolbar: true
                },
                tools: [
                    {
                        name: "insertHtml",
                        items: [
                            { text: "Direct Verification Link", value: "[FASTSIGNLINK]" },
                            { text: "Direct Verification Link Custom", value: "[FASTSIGNLINK] Click Here [/FASTSIGNLINK]" },
                            { text: "Link to Login Code Entry", value: "[SIGNLINK]" },
                            { text: "Link to Login Code Entry Custom", value: "[SIGNLINK] Click Here [/SIGNLINK]" },
                            { text: "Returning On-Boarding EE Link", value: "[OBLINK]" },
                            { text: "Returning On-Boarding EE Link Custom", value: "[OBLINK] Click Here [/OBLINK]" },
                            { text: "Employee Self Service Link (Post Hire)", value: "[EMPLLINK]" },
                            { text: "Employee Self Service Link (Post Hire) Custom", value: "[EMPLLINK] Click Here [/EMPLLINK]" },
                            { text: "On-Boarding EE Login Code", value: "[LOGINCODE]" },
                            { text: "On-Boarding User ID", value: "[USERID]" },
                            { text: "Employee ID", value: "[EMPLOYEEID]" },
                            { text: "First Name", value: "[FIRSTNAME]" },
                            { text: "Last Name", value: "[LASTNAME]" },
                            { text: "Employee Name", value: "[EMPLOYEENAME]" },
                            { text: "Employee Email", value: "[EMPLOYEEMAIL]" },
                            { text: "Client User ID", value: "[CLIENTUSERID]" },
                            { text: "Client Name", value: "[CLIENTNAME]" },
                            { text: "Client Phone", value: "[CLIENTPHONE]" },
                            { text: "Client Email", value: "[CLIENTMAIL]" },
                            { text: "PEO Name", value: "[PEONAME]" },
                            { text: "PEO Phone", value: "[PEOPHONE]" },
                            { text: "PEO Email", value: "[PEOMAIL]" },
                            { text: "Due Date", value: "[DUEDATE]" }
                        ]
                    },
                "formatting",
                    "bold",
                "italic",
                "underline",
                "justifyLeft",
                "justifyCenter",
                "justifyRight",
                "justifyFull",
                "insertUnorderedList",
                "insertOrderedList",
                "indent",
                "outdent",
                "createLink",
                "unlink",
                "foreColor",
                "backColor"
                ],
                paste: function (ev) {
                    ev.html = $(ev.html).text();
                },
                messages: {
                    insertHtml: "SmartTags"
                }
            });
        });
    </script>

    @* Dual Box Multi Jquery *@
    @Html.Partial("~/Views/Partials/DualBoxMultiSelect.cshtml")

    <script>
        $(document).ready(function () {
            if ($("#ConDoc").is(":checked")) {
                $('#selection').show();
            } else {
                $('#selection').hide(); /* If you want to be hidden if it's not */
            }
        })
    </script>

    <script>
        $(document).ready(function () {
            $('#ConDoc').change(function () {
                $('#selection').toggle();
            });
        });
    </script>
}
