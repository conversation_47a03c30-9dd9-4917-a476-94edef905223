@model IEnumerable<DarwiNet2._0.Data.ClientPlanHDR>
@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch

@{
    ViewBag.Title = "Client Plans";
}
@section styles{
    <link href="~/Content/sweetalert.css" rel="stylesheet" />
}
<div class="toolbar">
    <div class="row">
        <div class="col-md-3 pull-left">
            <div class="input-group">
                <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                <input type="text" class="form-control" id='FieldFilter' placeholder="Search Plan Name">
            </div>
        </div>
        <p class="create-pad pull-right tw-button">
            <a href="@Url.Action("Create", "ClientPlanHDR")" class="btn btn-thinkware"><i class="fa fa-plus fa-lg fa-fw"></i>Add New</a>
        </p>
    </div>
</div>
<div class="table-bottom table-icons">
    <table id="clientsDb">
        <tr>
            <th width="25px;" class="hidden-filter"><div style="visibility: hidden;">Buttons</div></th>
            <th title="@FieldTranslation.GetLabel("Year1", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("Year1", GlobalVariables.LanguageID)
            </th>
            <th title="@FieldTranslation.GetLabel("PlanName", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("PlanName", GlobalVariables.LanguageID)
            </th>

            <th title="@FieldTranslation.GetLabel("ClientID", GlobalVariables.LanguageID)">
                @FieldTranslation.GetLabel("ClientID", GlobalVariables.LanguageID)
            </th>

            <th width="50px;" class="hidden-filter"><div style="visibility: hidden;">Controls</div></th>
        </tr>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    <a href=""><i class="icon-edit fa fa-pencil fa-lg fa-fw"></i></a>
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.YEAR1)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.PlanName)
                </td>

                <td>
                    @Html.DisplayFor(modelItem => item.ClientID)
                </td>

                <td>
                    <div class="icon-center">
                        <a href="#" id="delete" data-id="@item.ID"><i class="icon-red fa fa-times fa-fw fa-lg" title="Delete"></i></a>
                    </div>
                </td>
            </tr>
        }
    </table>
</div>
@section scripts{
    @*<script src="~/Scripts/sweetalert.min.js"></script>*@
    <script src="~/Scripts/bootbox.min.js"></script>
    <script>
        $(document).ready(function () {
            var grid = $("#clientsDb").kendoGrid({
                dataSource: {
                    pageSize: 15
                },
                sortable: true,
                pageable: true,
                groupable: true,
                filterable: true,
                scrollable: false,
                resizable: true
            }).data("kendoGrid");

        });
    </script>
    <script>
        $(document).ready(function () {
            $("#FieldFilter").keyup(function () {

                var value = $("#FieldFilter").val();
                var grid = $("#clientsDb").data("kendoGrid");

                if (value) {
                    grid.dataSource.filter({
                        logic: "or",
                        filters: [
                            { field: "PlanName", operator: "contains", value: value },
                            { field: "ClientID", operator: "contains", value: value }
                        ]
                    });
                } else {
                    grid.dataSource.filter({});
                }
            });
        });
    </script>
    <script>
        $(document).on("click", "td #delete", function (e) {
            var deletedID = $(this).attr('data-id')
            var url = "@Url.Action("Delete", "ClientPlanHDR", null)"
            bootbox.dialog({
                message: "Are you sure you want to delete this plan?",
                title: "Delete Profile",
                buttons: {
                    main: {
                        label: "Cancel",
                        className: "btn-primary",
                        callback: function () {
                            //Example.show("Primary button");
                        }
                    },
                    danger: {
                        label: "Delete",
                        className: "btn-danger",
                        callback: function () {
                            window.location.href = url + "/" + deletedID;
                        }
                    }
                }
            });
        });
    </script>

    @*<script type="text/javascript">
            $(document).ready(function () {
                $('body').on('click', 'td #delete', function () {
                    var deletedID = $(this).attr('data-id')
                    var url = "@Url.Action("Delete", "OBProfile", null)"
                    swal({
                        title: "Are you sure?",
                        text: "You will not be able to recover this profile.",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#DD6B55",
                        confirmButtonText: 'Delete',
                        cancelButtonText: "Cancel",
                        closeOnConfirm: true,
                        closeOnCancel: true
                    },
                    function (isConfirm) {
                        if (isConfirm) {
                            window.location.href = url + "/" + deletedID;
                        }
                    });
                });

            })
        </script>*@

}