<script type="text/x-template" id="actions-template">
    <div class="panel panel-thinkware" id="ActionsPanel">
        <div class="panel-heading">Actions</div>
        <div class="panel-body">
            <div class="row">
                <div class="col-sm-12 text-center">
                    <i class="fa fa-fw fa-2x" v-for="action in actions"
                       v-bind:class="'fa-' + action.icon"
                       v-bind:title="action.tooltip"
                       v-on:click="action.on.click"></i>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    var ActionsComponent = VueComponent('actions', {
        props: {
            actions: [{
                icon: {
                    type: String,
                    required: true
                },
                tooltip: {
                    type: String,
                    default: ''
                },
                on: {
                    click: {
                        type: Function,
                        required: true
                    }
                }
            }]
        }
    });
</script>