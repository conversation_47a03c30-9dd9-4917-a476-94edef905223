<script type="text/x-template" id="invoice-check-details-table-template">
    <div class="panel panel-thinkware">
        <div class="panel-heading">Checks</div>
        <div class="panel-body">
            <div v-if="viewModel.length" class="table-responsive">
                <table class="table table-striped display">
                    <thead>
                        <tr>
                            <th width="5%"></th>
                            <th>Employee ID</th>
                            <th>Employee Name</th>
                            <th>Check Number</th>
                            <th class="numericCell">Gross Wages</th>
                            <th class="numericCell">Taxes</th>
                            <th class="numericCell">Deductions</th>
                            <th class="numericCell">Benefits</th>
                            <th class="numericCell">Net Wages</th>
                        </tr>
                    </thead>
                    <tbody v-for="(employee, index) in viewModel" id="detailTable">
                        <tr>
                            <td><div><i class="fa fa-money text-info" /></div></td>
                            <td>{{ employee.EmployeeId }}</td>
                            <td>{{ employee.EmployeeName }}</td>
                            <td>{{ employee.CheckNumber }}</td>
                            <td class="numericCell">{{ formatCurrency(employee.GrossWages) }}</td>
                            <td class="numericCell">{{ formatCurrency(employee.TotalTaxes) }}</td>
                            <td class="numericCell">{{ formatCurrency(employee.TotalDeductions) }}</td>
                            <td class="numericCell">{{ formatCurrency(employee.TotalBenefits) }}</td>
                            <td class="numericCell">{{ formatCurrency(employee.NetWages) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div v-else>No Checks for this Invoice.</div>
        </div>
    </div>
</script>

<script type="text/javascript">
    var InvoiceCheckDetailsTableComponent = VueComponent('invoice-check-details-table', {
        props: {
            viewModel: {
                type: Object,
                default: () => ({}),
            },
        },
        data: function() {
            return {
            }
        },
        computed: {

        },
        methods: {
        },
    });
</script>