<script type="text/x-template" id="invoice-details-header-template">
    <div class="table-responsive">
        <table class="table table-striped display">
            <thead>
                <tr>
                    <th>Invoice #</th>
                    <th>Invoice Date</th>
                    <th>Process Date</th>
                    <th>Check Date</th>
                    <th>PP Begin Date</th>
                    <th>PP End Date</th>
                    <th>Processor</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ invoiceHeader.InvoiceNumber }}</td>
                    <td>{{ formatDate(invoiceHeader.InvoiceDate) }}</td>
                    <td>{{ formatDate(invoiceHeader.ProcessDate) }}</td>
                    <td>{{ formatDate(invoiceHeader.CheckDate) }}</td>
                    <td>{{ formatDate(invoiceHeader.PayPeriodBeginDate) }}</td>
                    <td>{{ formatDate(invoiceHeader.PayPeriodEndDate) }}</td>
                    <td>{{ invoiceHeader.Processor }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</script>

<script type="text/javascript">
    var InvoiceDetailsHeaderComponent = VueComponent('invoice-details-header', {
        props: {
            invoiceHeader: {
                type: Object,
                default: () => ({})
            }
        },
        data: function () {
            return {

            }
        },
        methods: {
            formatDate: function (datetimeString) {
                let d = new Date(datetimeString);

                return `${d.getMonth() + 1}/${d.getDate()}/${d.getFullYear()}`
            }
        }
    });
</script>