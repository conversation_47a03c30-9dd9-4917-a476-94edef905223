<script type="text/x-template" id="invoice-fee-details-table-template">
    <div class="panel panel-thinkware">
        <div class="panel-heading">Fees</div>
        <div class="panel-body">
            <div v-if="viewModel.length" class="col-md-6">
                <div v-for="(fee, index) in firstHalfOfList" class="panel panel-thinkware">
                    <div class="panel-heading" style="display:flex; justify-content:space-between;">
                        <div>{{ fee.Code }}</div>
                        <div>{{ codeTypeName(fee.Type) }}</div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped display">
                            <thead>
                                <tr>
                                    <th>Employee ID</th>
                                    <th>Employee Name</th>
                                    <th class="numericCell">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="employee in fee.EmployeeInfo">
                                    <td>{{ employee.EmployeeId }}</td>
                                    <td>{{ employee.FullName }}</td>
                                    <td class="numericCell">{{ formatCurrency(employee.Amount) }}</td>
                                </tr>
                            </tbody>
                            <thead>
                                <tr>
                                    <th>Total</th>
                                    <th></th>
                                    <th class="numericCell">{{ formatCurrency(fee.Total) }}</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
            <div v-if="viewModel.length" class="col-md-6">
                <div v-for="(fee, index) in secondHalfOfList" class="panel panel-thinkware">
                    <div class="panel-heading" style="display:flex; justify-content:space-between;">
                        <div>{{ fee.Code }}</div>
                        <div>{{ codeTypeName(fee.Type) }}</div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped display">
                            <thead>
                                <tr>
                                    <th>Employee ID</th>
                                    <th>Employee Name</th>
                                    <th class="numericCell">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="employee in fee.EmployeeInfo">
                                    <td>{{ employee.EmployeeId }}</td>
                                    <td>{{ employee.FullName }}</td>
                                    <td class="numericCell">{{ formatCurrency(employee.Amount) }}</td>
                                </tr>
                            </tbody>
                            <thead>
                                <tr>
                                    <th>Total</th>
                                    <th></th>
                                    <th class="numericCell">{{ formatCurrency(fee.Total) }}</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
            <div v-else>No Fees for this Invoice.</div>
        </div>
    </div>
</script>

<script type="text/javascript">
    var InvoiceFeeDetailsTableComponent = VueComponent('invoice-fee-details-table', {
        props: {
            viewModel: {
                type: Object,
                default: () => ({})
            },
        },
        data: function() {
            return {
            }
        },
        computed: {
            firstHalfOfList: function () {
                var list = this.clone(this.viewModel)
                var half = Math.ceil(list.length / 2);

                var firstHalf = list.slice(0, half);

                return firstHalf;
            },
            secondHalfOfList: function () {
                var list = this.clone(this.viewModel)
                var half = Math.ceil(list.length / 2);

                var secondHalf = list.slice(half);

                return secondHalf;
            }
        },
        mounted: function (){
        },
        methods: {
            codeTypeName: function (code){
                    switch(code){
                        case 0:
                            return "Admin";
                        case 1:
                            return "Other";
                        case 2:
                            return "Credit";
                        default:
                            return "";
                    }
                },
            clone: function (obj) {
                let json = JSON.stringify(obj);
                return JSON.parse(json);
            }
        }
    });
</script>