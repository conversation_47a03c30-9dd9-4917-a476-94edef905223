@model DarwiNet2._0.ViewModels.EmployeeDirectDepositViewModel
@using DarwiNet2._0.Controllers;
@using DarwiNet2._0.Core;
@using DarwiNet2._0.DNetSynch;
@using DarwiNet2._0.Extensions;
@using DataDrivenViewEngine.Models.Core;
@{
    ViewBag.Title = "ACH Direct Deposits";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div id="employee-direct-deposits" v-cloak>
    <div class="margin-bottom-5">
        <div class="row d-flex">
            <div class="col-md-6 col-sm-6">
                <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
                <div class="colored-line-left"></div>
            </div>
            <div class="col-md-6 col-sm-6 text-right">
                <button type="button" class="btn btn-thinkware" @@click="goBack()">Back to ACH Maintenance</button>
            </div>
        </div>
    </div>
    <div class="form-horizontal">
        <div class="form-group">
            <label for="client" class="control-label col-md-1">Client</label>
            <div class="col-md-3">
                <input type="text" id="client" class="form-control" disabled="disabled" :value="'@Model.ClientID' + ': ' + '@Model.ClientName'" />
            </div>
            <label for="audit-trail-code" class="control-label col-md-2">Audit Trail Code</label>
            <div class="col-md-3">
                <input type="text" id="audit-trail-code" class="form-control" disabled="disabled" :value="'@Model.AuditControlCode'" />
            </div>
        </div>
    </div>
    <thinkware-vue-table :configuration="configuration" ref="employeeDirectDepositTable" style="margin-bottom: -15px">
        <template v-slot:expandable="row">
            <employee-direct-deposit-detail :employee-direct-deposit="row.data" @@cancel-changes="refreshEmployeeDirectDeposits()" @@save-changes="refreshEmployeeDirectDeposits()" />
        </template>
        <template v-slot:void="row">
            <input type="checkbox" class="disabled" :value="row.data.Voided" @@click.prevent />
        </template>
        <template v-slot:manvoid="row">
            <input type="checkbox" class="disabled" :value="row.data.ManualVoided" @@click.prevent />
        </template>
        <template v-slot:multexport="row">
            <input type="checkbox" class="disabled" :value="row.data.MultipleExport" @@click.prevent />
        </template>
        <template v-slot:precheck="row">
            <input type="checkbox" class="disabled" :value="row.data.Precheck" @@click.prevent />
        </template>
        <template v-slot:pct="row">
            <span class="pull-right">{{ formatPercent(row.data.Percentages) }}</span>
        </template>
    </thinkware-vue-table>
    <loading :active="loading" :is-full-page="true" :enforce-focus="true" loader="dots" />
</div>

<script src='https://cdn.jsdelivr.net/npm/v-calendar'></script>
<script src="https://cdn.jsdelivr.net/npm/vue-toastr/dist/vue-toastr.umd.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.2/animate.min.css" integrity="sha256-PHcOkPmOshsMBC+vtJdVr5Mwb7r0LkSVJPlPrp/IMpU=" crossorigin="anonymous" />
<script src="~/Scripts/lodash.js"></script>
<script src="~/Scripts/vue-loading-overlay.js"></script>
<link href="~/Scripts/vue-loading.css" rel="stylesheet">

@Html.VueComponent("~/Shared/thinkware-alert-modal")
@Html.VueComponent("~/Shared/thinkware-vue-modal")
@Html.VueComponent("~/Shared/thinkware-vue-table")
@Html.VueComponent("employee-direct-deposit-detail")

<script type="text/javascript">
    Vue.component('loading', VueLoading);
    Vue.use(VueLoading);
    Vue.use(VueToastr, {
        defaultPosition: 'toast-bottom-right',
        defaultType: 'info',
        defaultTimeout: 2000
    });

    var vm = new VueInstance('employee-direct-deposits', {
        components: [
            ThinkwareAlertModal,
            ThinkwareVueModalComponent,
            ThinkwareVueTableComponent,
            EmployeeDirectDepositDetailComponent
        ],
        data: {
            configuration: {
                Options: {
                    TableId: @FilterTableConstants.EmployeeDirectDeposit,
                    HtmlRef: "employeeDirectDepositTable",
                    Classes: "table-hover employee-direct-deposits-table",
                    DataUri: '@Url.Action("GetEmployeeDirectDepositTableData", "ACHMaintenance")' + `?companyID=${@Html.Raw(Model.CompanyID.ToJson())}&auditControlCode=@Model.AuditControlCode`,
                    SavedState: false,
                    Filterable: true,
                    HideFilterControls: true,
                    Groupable: { Active: false },
                    Sortable: { Active: false },
                    Callable: { Active: true },
                    Selectable: { Active: false },
                    Expandable: { Active: true, Align: "left" },
                    Scrollable: { Active: false },
                    Searchable: { Active: false },
                    Totals: { Active: false }
                },
                Columns: [
                    { Label: "Void", Code: "Voided", Render: "template", HeaderAlign: "center", Width: "10%" },
                    { Label: "Man Void", Code: "ManualVoided", Render: "template", HeaderAlign: "center", Width: "10%" },
                    { Label: "Mult. Export", Code: "MultipleExport", Render: "template", HeaderAlign: "center", Width: "10%" },
                    { Label: "Employee ID", Code: "EmployeeID", Render: "string", Width: "15%" },
                    { Label: "Payment", Code: "PaymentAdjustmentNumber", Render: "string", Width: "15%" },
                    { Label: "PayCode", Code: "PayRecord", Width: "15%", Render: "string" },
                    { Label: "Deposit Amount", Code: "ActualDeposit", Render: "currency", Width: "130px" },
                    { Label: "Pct", Code: "Percentages", Render: "template", HeaderAlign: "right", Width: "10%" },
                    { Label: "Precheck", Code: "Precheck", Render: "template", HeaderAlign: "center", Width: "10%" },
                    { Label: "Actions!", Render: "template", Width: "0px", FilterType: "NonSelect", ActiveFilters: [] }
                ],
            },
            loading: false,
            filters: {
            }
        },
        methods: {
            formatPercent: function (value) {
                return (value / 100).toLocaleString('en-US', { style: 'percent', minimumFractionDigits: 0 });
            },
            goBack() {
                window.location.href = '@Url.Action("Index", "ACHMaintenance")';
            },
            refreshEmployeeDirectDeposits() {
                this.$refs.employeeDirectDepositTable.reloadTable();
            }
        }
    });
</script>

<style lang="scss" scoped>
    #employee-direct-deposits {
        .d-flex {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }
        .employee-direct-deposits-table {
            margin-top: 15px;
            th {
                padding-top: 10px;
                padding-bottom: 10px;
                vertical-align: middle;
            }
            td {
                padding-top: 10px;
                padding-bottom: 10px;
                vertical-align: middle;
            }
            #Void, #ManVoid, [id='Mult.Export'], #Precheck {
                text-align: center;
                input[type='checkbox'].disabled {
                    pointer-events: none;
                    cursor: default;
                }
            }
            .employee-direct-deposit-detail {
                margin-top: 10px;
                padding: 0 50px;
            }
        }
    }
</style>
