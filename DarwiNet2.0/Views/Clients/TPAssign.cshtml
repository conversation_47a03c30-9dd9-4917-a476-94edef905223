@using DarwiNet2._0.DNetSynch;
@model IEnumerable<DarwiNet2._0.Data.UserRoleClientEmployeeAssignment>
@{
    ViewBag.Title = "Clients List";
    ViewBag.ParentCrumb = "Client Maintenance";
}
<p style="font-size: 22px;">Time Punch Assign</p>
<div class="colored-line-left"></div>
<div class="toolbar" style="padding-bottom: 10px;">

    <div class="row">
        <div class="col-md-3 pull-left">
            <div class="input-group">
                <span class="input-group-addon"><span class="glyphicon glyphicon-search" aria-hidden="true"></span></span>
                <input type="text" class="form-control" id='FieldFilter' placeholder="Search User Id">
            </div>
        </div>
        <div class="pull-right create-pad">
            <a href="@Url.Action("TPSetupDetail", "TimePunches", new {client = @ViewBag.Client})" class="btn btn-thinkware">Back to List</a>
            @if (ViewBag.UseSwipeClock)
            {
                <button type="button" id="scExport" class="btn btn-thinkware">Export to Swipeclock</button>
            }
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div id="exportCompleted"></div>
        </div>
    </div>
</div>
@if (ViewBag.NoUsers == true)
{

}
else
{
    <table class="table" id="tpTable">
        <tr>
            <th>
                User ID
            </th>
            <th>
                Name
            </th>
            <th>
                Employee ID
            </th>
            <th>
                Enable/Disable Time Clock
            </th>
            <th>
                Enable/Disable Mobile Time Clock
            </th>
            <th class="hidden-filter">Actions</th>
        </tr>
        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.DisplayFor(modelItem => item.UserID)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.EmployeeName)
                </td>
                <td>
                    @Html.DisplayFor(modelItem => item.EmployeeID)
                </td>
                <td>
                    @if (!string.IsNullOrWhiteSpace(item.EmployeeID))
                    {
                        <input type="checkbox" data-id="@item.id" class="chkEnable" @((item.AllowEETimeClock) ? "checked" : string.Empty) />
                    }
                    else
                    {
                        <p>N/A</p>
                    }
                </td>
                <td>
                    @if (!string.IsNullOrWhiteSpace(item.EmployeeID))
                    {
                        <input type="checkbox" data-id="@item.id" class="mobileChkEnable" @((item.AllowEEMobileTimeClock) ? "checked" : string.Empty) />
                    }
                    else
                    {
                        <p>N/A</p>
                    }
                </td>
                <td>
                    @if (item.AllowEETimeClock && !string.IsNullOrWhiteSpace(item.EmployeeID))
                    {
                        @*if (GlobalVariables.AllowTimeco && ViewBag.UseTimeco)
                        {
                            <a href="@Url.Action("Edit", "Timeco", new { companyId = item.CompanyID, clientId = item.ClientID, employeeId = item.EmployeeID, userId = item.UserID })"
                               class="btn btn-thinkware" data-toggle="modal" data-target="#tcSetupModal" data-remote="false" id="<EMAIL>">
                                Timeco Setup
                            </a>
                        }
                        else
                        {*@
                        <a href="@Url.Action("ETPSetupEdit", "TimePunches", new { client = item.ClientID, emplId = item.EmployeeID, companyId = item.CompanyID })"
                            class="btn btn-thinkware" data-toggle="modal" data-target="#tcSetupModal" data-remote="false" id="<EMAIL>">
                            Time Clock Setup
                        </a>
                        @*}*@
                    }
                </td>
            </tr>
        }
    </table>
    <div class="modal fade" id="tcSetupModal" tabindex="-1" role="dialog" aria-labelledby="tcSetupModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="tcSetupModalLabel">Time Clock Setup</h4>
                </div>
                <div class="modal-body">
                    Generating...
                </div>
            </div>
        </div>
    </div>
    @section scripts{


        <script>
            $(document).ready(function () {
                var grid = $("#tpTable").kendoGrid({
                    dataSource: {
                        pageSize: 15
                    },
                    sortable: true,
                    pageable: true,
                    filterable: true,
                    scrollable: false,
                    groupable: true,
                    selectable: "multiple",
                    resizable: true
                }).data("kendoGrid");
                $("#FieldFilter").keyup(function () {

                    var value = $("#FieldFilter").val();
                    var grid = $("#tpTable").data("kendoGrid");

                    if (value) {
                        grid.dataSource.filter({
                            logic: "or",
                            filters: [
                                { field: "UserID", operator: "contains", value: value }
                            ]
                        })
                    } else {
                        grid.dataSource.filter({});
                    }
                });
            });

        </script>
        <script>
    $('#scExport').click(function () {
        var selectedId = '';
        var selectedrow = $("#tpTable").find("tbody tr.k-state-selected");
        jQuery.each(selectedrow, function (i, val) {
            var rows = $('#tpTable').data("kendoGrid").dataItem(val);
            var rowsjson = rows.toJSON();
            var records = rowsjson.EmployeeID;
            records = records.replace(/\s/g, '');
            selectedId += ',' + records;
        });
        /*$('#massupdateModal').modal('toggle');*/
        var recordsSent = selectedId.replace(/^,|,$/g, '');
        if (recordsSent == '') {
            alert('Please select records in the table to utilize mass update.');
            return false;
        }
        var url = '@Url.Action("PushSelectedEmployees", "Employees", new { c = ViewBag.Client })';
        url = url + "&eeselected=" + recordsSent;
        $.get(url, function (data) {
            if (data === "True") {
                $('#exportCompleted').show();
                $('#exportCompleted').html('<div class="alert alert-success" role="alert">Export Completed</div>')
                    .fadeOut(5000);
                var grid = $("#tpTable").data("kendoGrid");
                grid.clearSelection();
            } else {
                $('#exportCompleted').show();
                $('#exportCompleted').html('<div class="alert alert-danger" role="alert">Export Failed</div>').fadeOut(5000);
            }
        })
        /*.done(function () {
            $('#exportCompleted').html('<div class="alert alert-success" role="alert">Export Completed</div>').fadeOut(5000);
            var grid = $("#tpTable").data("kendoGrid");
            grid.clearSelection();
        })*/
        .fail(function () {
            $('#exportCompleted').html('<div class="alert alert-danger" role="alert">Export Failed</div>').fadeOut(5000);
        });
    });
        </script>
        <script>
            $("#tcSetupModal").on("show.bs.modal", function (e) {
                var link = $(e.relatedTarget);
                $(this).find(".modal-body").html("");
                $(this).find(".modal-body").load(link.attr("href"));
            });
        </script>
        <script>
    $(document).on('click', '.chkEnable' ,function () {
        //alert('cliek');
        var userID = $(this).attr('data-id');
        var ImChecked = $(this).is(':checked');
        var url = '';
        //alert(ImChecked);
        if (ImChecked) {
            url = '@Url.Action("EnableTimeClock", "Clients")';
        } else {
            url = '@Url.Action("DisableTimeClock", "Clients")';
        }
        $.ajax({
            method: "GET",
            url: url,
            data: { id: userID }
        })
            .done(function (msg) {
                if (msg == "Success") {
                    if (ImChecked) {
                        $('#btnEdit-' + userID).show();
                    } else {
                        $('#btnEdit-' + userID).hide();
                    }
                }
                //alert("Data Saved: " + msg);

            });


    });
        $(document).on('click', '.mobileChkEnable' ,function () {
        //alert('cliek');
        var userID = $(this).attr('data-id');
        var ImChecked = $(this).is(':checked');
        var url = '';
        //alert(ImChecked);
        if (ImChecked) {
            url = '@Url.Action("EnableMobileTimeClock", "Clients")';
        } else {
            url = '@Url.Action("DisableMobileTimeClock", "Clients")';
        }
        $.ajax({
                method: "GET",
                url: url,
                data: { id: userID }
            })
            .done(function (msg) {
                //alert("Data Saved: " + msg);
            });
        });
        </script>
    }

}

