@using DarwiNet2._0.Controllers
@using DataDrivenViewEngine.Models.Core
@{
    Layout = null;
}
@using (Html.BeginForm("ProfileMassUpdate", "OBDocumentMapping", null, FormMethod.Post, new { id = ViewBag.DocID }))
{
    <input type="hidden" value="@ViewBag.Selected" name="selRecordsStr" />
    <input type="hidden" value="@ViewBag.DocID" name="id" />
    <div class="form-horizontal">
        <div class="form-group">
            <label class="col-md-4 control-label">
                Use in Web Form

            </label>
            <div class="col-md-8 check-down">
                <select class="form-control" name="UpdWebForm">
                    <option value="">Do Not Change</option>
                    <option value="False">No</option>
                    <option value="True">Yes</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-4 control-label">
                Form Field Type
            </label>
            <div class="col-md-8">
                <select name="UpdType" class="form-control">
                    <option value="">Do Not Change</option>
                    @foreach (var item in ViewBag.FieldTypes)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-4 control-label">
                Form Field Status
            </label>
            <div class="col-md-8">
                <select name="UpdStatus" class="form-control">
                    <option value="">Do Not Change</option>
                    @foreach (var item in ViewBag.OBMapFieldStatus)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                    <button class="btn btn-thinkware" type="submit">Submit</button>
                </div>

            </div>
        </div>
    </div>
}