<style type="text/css" scoped>
    .highlight-warning-row {
        background-color: #fcf8e3 !important;
    }

    .highlight-warning-row:hover {
        background-color: #f9f2cd !important;
    }

    .fa.fa-exclamation-triangle {
        cursor: pointer;
    }

    .preview-warnings-icon i.fa:hover {
        text-shadow: 1px 1px 0 rgba(0,0,0,.15), 2px 2px 2px rgba(0,0,0,.25);
    }
</style>

<script type="text/x-template" id="void-pending-warning-template">
    <div :id="elementID" class="preview-warnings-icon">
        <div v-if="hasWarning" @@click="onWarningClicked">
            <i class="fa fa-exclamation-triangle" title="Void Pending: Click to Review"></i>
        </div>
    </div>
</script>

<script type="text/javascript" id="void-pending-warning-script">
    var VoidPendingWarningComponent = VueComponent('void-pending-warning', {
        props: {
            rowData: {
                type: Object,
                required: true,
                default: () => ({}),
            },
            warning: {
                type: Boolean,
                required: true,
                default: false,
            }
        },
        watch: {
            warning: function (val) {
                if (this.hasWarning !== val) {
                    this.hasWarning = val;
                    this.highlightRow();
                }
            },
        },
        data: function () {
            return {
                hasWarning: false,
                elementID: `${this.rowData.EmployeeID}-check-table-row`
            }
        },
        mounted: function () {
            this.hasWarning = this.warning;
            this.highlightRow();
        },
        methods: {
            onWarningClicked: function () {
                window.location = '@Url.Action("Review", "PayrollReview")/?payrollNumber=' + this.rowData.VoidPayrollNumber;
            },
            highlightRow: function () {
                var el = document.getElementById(this.elementID);
                var row = el.closest("tr");
                if (this.hasWarning) {
                    row.classList.add("highlight-warning-row")
                }
                else {
                    row.classList.remove("highlight-warning-row")
                }
                el.closest("td").style.paddingLeft = 0;
            },
        }
    });
</script>
