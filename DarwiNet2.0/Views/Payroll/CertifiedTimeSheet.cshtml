@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@{
    ViewBag.Title = "Certified Job Time Sheet";
}

<div class="company-info">
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
        <div class="pull-right">
            <!-- <button>Save</button><button>Approve</button><button>Delete</button> -->
            <a href="@Url.Action("Index", "TS")" class="btn btn-thinkware">Back to List</a>
            @if (ViewBag.Mode == "EDIT")
            {
                <a href="@Url.Action("ReorderColumns", "TS", new {id = @ViewBag.TimesheetID})" class="btn btn-thinkware" data-toggle="modal" data-target="#sortModal" data-remote="false">Set Column Order</a>
            }
            <button class="btn btn-thinkware" style="display: none" id="codeModalButton" data-toggle="modal" data-target="#codeModal">Add Code</button>
            <button class="btn btn-thinkware" style="display: none" id="empModalButton" data-toggle="modal" data-target="#empModal">Add Employee</button>
            <button type="button" class="btn btn-thinkware" data-toggle="modal" data-target="#empTimeCommentModal">
                @if (ViewBag.Comment != string.Empty)
                {
                    <span id="commentIcon">
                        <i style="color: yellow;" class="fa fa-exclamation-triangle fa-fw"></i>
                    </span>
                    <span>Comments</span>
                }
                else
                {
                    <span id="commentIcon" style="display: none;">
                        <i style="color: yellow;" class="fa fa-exclamation-triangle fa-fw"></i>
                    </span>
                    <span>Comments</span>
                }
            </button>
            <button id="saveTimesheet" class="btn btn-thinkware">Save</button>
            <a href="@Url.Action("Approve", "TS", new {id = ViewBag.TimesheetID})" id="canSubmit" class="btn btn-thinkware">Submit</a>
            <a href="@Url.Action("Approve", "TS", new {id = ViewBag.TimesheetID})" class="btn btn-thinkware">Approve</a>
            <a href="@Url.Action("RestoreRequests", "TS", new {id = ViewBag.TimesheetID})" style="display: none" id="needRestore" class="btn btn-thinkware">Restore Requests</a>
            <a href="@Url.Action("Reedit", "TS", new {id = ViewBag.TimesheetID})" style="display: none" id="canUnlock" class="btn btn-thinkware">Edit</a>
            <a href="@Url.Action("Remove", "TS", new { id = ViewBag.TimesheetID })" class="btn btn-thinkware">Delete</a>
        </div>
    </div>

</div>

@Html.Partial("~/Views/Payroll/_InfoHeader.cshtml")
<div class="row">
    <div class="col-lg-12">
        <div class="row" id="loading" style="height: 1000px;">
            <div class="text-center">
                <h2>Loading...</h2>
            </div>
        </div>
        <div clas="row" id="timesheetTableWrapper" style="display: none;">
            @*@using (Html.BeginForm("Edit", "TSProfiles", FormMethod.Post, new { @id = "tsform" }))
            {
                @Html.Action("ProcessTimeSheet", "TS", new { id = ViewBag.TimesheetID })
            }*@
        </div>
    </div>
</div>
<!--Check Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Employee Details</h4>
            </div>
            <div class="modal-body">
                Generating...
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-thinkware" data-dismiss="modal">Close</button>
                @*<button type="button" class="btn btn-primary">Save changes</button>*@
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="empTimeCommentModal" tabindex="-1" role="dialog" aria-labelledby="empTimeCommentLabel">
    <div class="modal-dialog  modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="empTimeCommentLabel">Comments</h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="col-md-2 control-label">Comments</label>
                        <div class="col-md-10">
                            <textarea name="comments" id="tsComments" rows="10" class="form-control">@ViewBag.Comment</textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-thinkware" data-dismiss="modal">Close</button>
                <button type="button" id="saveComment" class="btn btn-thinkware">Save changes</button>
            </div>
        </div>
    </div>

</div>
@*Rates Modal*@
<div class="modal fade" id="modRatesModal" tabindex="-1" role="dialog" aria-labelledby="modRatesModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="modRatesModalLabel">Modify Rates</h4>
            </div>
            <div class="modal-body">
                Generating...
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-thinkware" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="sortModal" tabindex="-1" role="dialog" aria-labelledby="sortModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="sortModalLabel">Sort Columns</h4>
            </div>
            <div class="modal-body">
                Generating...
            </div>
            @*<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>*@
        </div>
    </div>
</div>
@section scripts{
    <script src="//cdn.datatables.net/1.10.10/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.10/js/dataTables.bootstrap.min.js"></script>
    <script src="~/Scripts/dataTables.keyTable.js"></script>
    <link href="https://cdn.datatables.net/1.10.10/css/dataTables.bootstrap.min.css" rel="stylesheet" />
    <script>
    $(document).ready(function () {
        $.ajax({

            url: '@Url.Action("ProcessTimeSheet", "TS", new { id = ViewBag.TimesheetID })',
            type:"GET",
            dataType: "html",
            //data: { id: 1 },//this is as per your requirement
            success: function (data) {
                $("#timesheetTableWrapper").html("");
                $('#timesheetTableWrapper').html(data);
                var grid = $('#tblTimeSheet').DataTable({
                    //"scrollY": "567px",
                    "paging": true,
                    "ordering": false,
                    "sScrollX": "100%",
                    "bSortClasses": false,
                    "bStateSave": true,
                    //"iCookieDuration": 60*5,// 1 day (in seconds)
                    keys: {
                        className: 'timesheet-highlight'
                    },
                    "initComplete": function () {
                        var table = $('#tblTimeSheet').DataTable();
                        $('#loading').hide();
                        $('#emplNavSearch').show();
                        $('#timesheetTableWrapper').show();
                        table.columns.adjust().draw();
                        /*$.fn.dataTable
                            .tables({ visible: true, api: true })
                            .columns.adjust();*/
                    }
                });
                grid
                    .on('key-focus', function (e, datatable, cell) {
                        $('td.timesheet-highlight input').focus().select();

                    });
                grid
                    .on('key-blur', function (e, datatable, cell) {
                        $('input').blur();
                    });
            },
            error: function() {
                bootbox.dialog({
                    message: "The timesheet is taking too long to load. Please click refresh to reload.",
                    title: "Timesheet Message",
                    buttons: {
                        main: {
                            label: "Refresh",
                            className: "btn-primary",
                            callback: function() {
                                location.reload();
                            }
                        }
                    }
                });
            }
        });

    });
    //Update input on change
    $(document).on("change", "input", function () {
        var id = $(this).attr('id');
        var val = $(this).val();
        var tid = '@ViewBag.TimesheetID';
        var url = '@Url.Action("SaveValue", "TS")';
        var getUrl = '@Url.Action("CheckTSSession", "CheckTSSession")';
        $.get( getUrl, function( data ) {
            if (data === "SessionExpired") {
                clearInterval();
                var url = '@Url.Action("SessionExpired", "Home")';
                window.location.replace(url);
            }
        });
        $.post(url + "?id=" + id + "&value=" + val + "&tid=" + tid, function(data) {
            $.each( data, function( i, item ) {
                if (item.Type === 0) {
                    $('#' + item.ID).text(item.Value);
                } else {
                    $('#' + item.ID + ' select').val(item.Value);
                }
            });
        });
    });
    //Update select on change
    $(document).on("change", "select", function () {
        var id = $(this).attr('id');
        var val = $(this).val();
        var tid = '@ViewBag.TimesheetID';
        var url = '@Url.Action("SaveValue", "TS")';
        var getUrl = '@Url.Action("CheckTSSession", "CheckTSSession")';
        $.get( getUrl, function( data ) {
            if (data === "SessionExpired") {
                clearInterval();
                var url = '@Url.Action("SessionExpired", "Home")';
                window.location.replace(url);
            }
        });
        $.post(url + "?id=" + id + "&value=" + val + "&tid=" + tid, function(data) {
            $.each( data, function( i, item ) {
                if (item.Type === 0) {
                    $('#' + item.ID).text(item.Value);
                } else {
                    $('#' + item.ID + ' select').val(item.Value);
                }
            });
        });
    });
    //Save Timesheet
    $('#saveTimesheet').click(function() {
        var id = @ViewBag.TimesheetID;
        var url = '@Url.Action("Save", "TS")';
        var post = $.post(url + "?id=" + id);
        var getUrl = '@Url.Action("CheckTSSession", "CheckTSSession")';
        $.get( getUrl, function( data ) {
            if (data === "SessionExpired") {
                clearInterval();
                var url = '@Url.Action("SessionExpired", "Home")';
                window.location.replace(url);
            }
        });
        //Convert button text and show spinner
        $('#saveTimesheet').text("Saving...");
        $('#saveTimesheet').prepend("<i class='fa fa-spinner fa-spin fa-fw'></i>");
        post.done(function() {
            //Let user know timesheet was saved
            bootbox.dialog({
                message: "This time sheet has been saved",
                title: "Standard Time Sheet",
                buttons: {
                    main: {
                        label: "Close",
                        className: "btn-primary",
                        callback: function () {
                            $('#saveTimesheet').text("Save");
                        }
                    }
                }
            });
        });
        post.fail(function() {
            bootbox.dialog({
                message: "There was a problem saving the time sheet. Please try again.",
                title: "Standard Time Sheet",
                buttons: {
                    main: {
                        label: "Close",
                        className: "btn-primary",
                        callback: function () {
                            $('#saveTimesheet').text("Save");
                        }
                    }
                }
            });
        });
    });
    //Save Comment
    $('#saveComment').click(function() {
        var id = @ViewBag.TimeSheetID;
        var comments = $('#tsComments').val();
        var url = '@Url.Action("SaveComment", "TS")';
        var post = $.post(url + "?id=" + id + "&note=" + encodeURIComponent(comments));
        var getUrl = '@Url.Action("CheckTSSession", "CheckTSSession")';
        $.get( getUrl, function( data ) {
            if (data === "SessionExpired") {
                clearInterval();
                var url = '@Url.Action("SessionExpired", "Home")';
                window.location.replace(url);
            }
        });
        post.done(function() {
            $('#empTimeCommentModal').modal('hide');
            console.log("comment saved");
            if (!comments) {
                $('#commentIcon').hide();
            } else if (comments) {
                $('#commentIcon').show();
            }
        });
        post.fail(function() {
            console.log("comment save failed");
        });
    });
</script>

    <script>

        $("#myModal").on("show.bs.modal", function (e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-body").load(link.attr("href"));
        });
        $("#modRatesModal").on("show.bs.modal", function (e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-body").load(link.attr("href"));
        });
        // mini jQuery plugin that formats to two decimal places
        (function ($) {
            $.fn.currencyFormat = function () {
                this.each(function (i) {
                    $(this).change(function (e) {
                        if (isNaN(parseFloat(this.value))) return;
                        this.value = parseFloat(this.value).toFixed(2);
                    });
                });
                return this; //for chaining
            }
        })(jQuery);

        // apply the currencyFormat behavior
        $(function () {
            $('.rate').currencyFormat("${0:n2}");
        });
        $('.rate').keypress(function (event) {
            var $this = $(this);
            if ((event.which != 46 || $this.val().indexOf('.') != -1) &&
               ((event.which < 48 || event.which > 57) &&
               (event.which != 0 && event.which != 8))) {
                event.preventDefault();
            }

            var text = $(this).val();
            if ((event.which == 46) && (text.indexOf('.') == -1)) {
                setTimeout(function () {
                    if ($this.val().substring($this.val().indexOf('.')).length > 3) {
                        $this.val($this.val().substring(0, $this.val().indexOf('.') + 3));
                    }
                }, 1);
            }

            if ((text.indexOf('.') != -1) &&
                (text.substring(text.indexOf('.')).length > 2) &&
                (event.which != 0 && event.which != 8) &&
                ($(this)[0].selectionStart >= text.length - 2)) {
                event.preventDefault();
            }
        });
        //Next Previous Navigation
        var curNavValue = $('#CurNavValue').val();
        var curNavItem = parseInt($('#CurNavItem').val());
        var totNavItems = parseInt($('#TotalNavItems').val());
        //Show nav buttons if value is greater than 1
        if (curNavItem < totNavItems) {
            $('#nextTS').prop("disabled", false);
        }
        if (curNavItem > "1") {
            $('#prevTS').prop("disabled", false);
        }
        $(document).on('click', '#nextTS', function() {
            var url = '@Url.Action("Next", "TS", new { id = ViewBag.TimesheetID })';
            window.location.href = url;
        });
        $(document).on('click', '#prevTS', function() {
            var url = '@Url.Action("Previous", "TS", new { id = ViewBag.TimesheetID })';
            window.location.href = url;
        });
        $("#sortModal").on("show.bs.modal", function(e) {
            var link = $(e.relatedTarget);
            $(this).find(".modal-body").load(link.attr("href"));
        });
    </script>
}