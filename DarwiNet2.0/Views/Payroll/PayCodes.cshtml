@model DarwiNet2._0.Data.Employee

@{
    ViewBag.Title = "Pay Codes";
}

<style>
    .section-heading {
        padding: 10px 15px;
        border-bottom: 1px solid transparent;
        background-color: #B27300;
        color: white;
        text-align: center;
        width: 100px;
        margin-left: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 6px 0 rgba(0,0,0,0.45);
    }
</style>


<div class="row">
    <div style="margin-bottom: 15px; width: 60%">
        @Html.Partial("~/Views/Employees/_EmployeeInfo.cshtml")
    </div>
    @Html.Partial("~/Views/Partials/_EmployeeSubNav.cshtml")
    @*<nav class="navbar navbar-default" role="navigation">
        <div>
            <ul class="nav navbar-nav">
                <li><a href="#">Info</a></li>
                <li>
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">Payroll<span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li><a href="#">Pay Codes</a></li>
                        <li><a href="#">Taxes</a></li>
                        <li><a href="#">Deductions</a></li>
                        <li><a href="#">ER Paid</a></li>
                        <li><a href="#">Direct Deposit</a></li>
                    </ul>
                </li>
                <li>
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">Benefits<span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li><a href="#">Plans</a></li>
                        <li><a href="#">Dependents</a></li>
                        <li><a href="#">Beneficiaries</a></li>
                        <li><a href="#">Statement</a></li>
                    </ul>
                </li>
                <li>
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">HR<span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li><a href="#">PTO</a></li>
                        <li><a href="#">Training</a></li>
                        <li><a href="#">Reviews</a></li>
                        <li><a href="#">Licenses</a></li>
                        <li><a href="#">FMLA/LOA</a></li>
                        <li><a href="#">I-9</a></li>
                    </ul>
                </li>
                <li><a href="#">Checks</a></li>
                <li>
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">Summary<span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li><a href="#">Summary</a></li>
                        <li><a href="#">Total Comp</a></li>
                        <li><a href="#">W-2</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>*@    
</div>
<div id="payCodesList">
    <table class="table table-striped table-bordered">
    <thead>
        <tr>
            <th>Pay Code</th>
            <th>Description</th>
            <th>Pay Rate</th>
            <th>Frequency</th>
            <th>YTD</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><a href="#" id="changePayCode">HOUR1</a></td>
            <td>Hourly Pay</td>
            <td>$10.00</td>
            <td>Weekly</td>
            <td>$24,000.00</td>
            <td><a href="#" id="payHistoryModal">History</a></td>
            <td><a href="#" id="paySummaryModal">Summary</a></td>
        </tr>
    </tbody>
</table>
</div>

<div id="payHistory">
    <i class="fa fa-arrow-circle-left" id="backPayCodes"></i>
    <div>
        <div class="form-group">
            <div class="col-sm-2">
                <label for="lastPayroll">Last Payroll: </label>
                <input type="radio" id="lastPayroll" class="form-control" />
            </div>
            <div class="col-sm-2">
                <label for="MonthToDate">Month To Date: </label>
                <input type="radio" id="MonthToDate" class="form-control" />
            </div>
            <div class="col-sm-2">
                <label for="QuarterToDate">Quarter To Date: </label>
                <input type="radio" id="QuarterToDate" class="form-control" />
            </div>
            <div class="col-sm-2">
                <label for="yearToDate">Year To Date: </label>
                <input type="radio" id="yearToDate" class="form-control" />
            </div>
            <div class="col-sm-2">
                <label for="selectYear">Select Year: </label>
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">
                        Choose Year
                    </button>
                    <ul class="dropdown-menu" role="menu">
                        <li><a href="#">2015</a></li>
                        <li><a href="#">2014</a></li>
                        <li><a href="#">2013</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-sm-2">
                <label>Select Dates: </label>
                <input type="text" id="startDate" class="form-control" />
                <label>to</label>
                <input type="text" id="endDate" class="form-control" />
            </div>
        </div>
    </div>
    <table class="table table-striped table-bordered" id="payCodesList">
        <thead>
            <tr>
                <th>Check Date</th>
                <th>Check Number</th>
                <th>Pay Rate</th>
                <th>Hours</th>
                <th>Amount Paid</th>
                <th>Department</th>
                <th>Position</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>01/01/2015</td>
                <td>123456</td>
                <td>$10.00</td>
                <td>40.00</td>
                <td>$24,000.00</td>
                <td>001001</td>
                <td>ADMIN</td>
            </tr>
        </tbody>
    </table>
</div>
<div id="paySummary">
    <i class="fa fa-arrow-circle-left" id="backPaySummary"></i>
    <table class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>Month</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>January</td>
                <td>$15,000</td>
            </tr>
            <tr>
                <td>February</td>
                <td>$15,000</td>
            </tr>
            <tr>
                <td>March</td>
                <td>$15,000</td>
            </tr>
        </tbody>
    </table>
</div>
<div id="payCode">
    <i class="fa fa-arrow-circle-left" id="backPayCode"></i>
    <div class="form-group">
        <div class="col-sm-6">
            <label for="payCodeTxt">Pay Code: </label>
            <input type="text" id="payCodeTxt" class="form-control" />
        </div>
        <div class="col-sm-6">
            <label for="payRateTxt">Pay Rate: </label>
            <input type="text" id="payRateTxt" class="form-control" />
        </div>
        <div class="col-sm-6">
            <label>Pay Period: </label>
            <select class="form-control">
                <option>Weekly</option>
                <option>Biweekly</option>
                <option>Semi-Monthly</option>
                <option>Monthly</option>
                <option>Quarterly</option>
                <option>Annual</option>
            </select>
        </div>
        <div class="col-sm-6">
            <label for="unitPayTxt">Unit of Pay: </label>
            <input type="text" class="form-control" id="unitPayTxt" />
        </div>
        <div class="col-sm-6">
            <label for="SUTATxt">SUTA: </label>
            <input type="text" class="form-control" id="SUTATxt" />
        </div>
        <div class="col-sm-6">
            <label for="DescTxt">Description: </label>
            <input type="text" class="form-control" id="DescTxt" />
        </div>
        <div class=" col-sm-12 form-group">
            <label for="InactiveTxt">Inactive: </label>
            <input type="checkbox" id="InactiveTxt" />            
            <input type="text" class="form-control" id="DateTxt" placeholder="Enter Date" />
            <textarea class="form-control" rows="2" id="comment" placeholder="Enter comments here" style="margin-top: 5px"></textarea>
        </div>        
        <div class="col-sm-6">
            <label for="PayTypeTxt">Pay Type: </label>
            <input type="text" class="form-control" id="PayTypeTxt" />
        </div>
        <div class="col-sm-6">
            <label for="WCCodeTxt">WC Code: </label>
            <input type="text" class="form-control" id="WCCodeTxt" />
        </div>
        <div class="col-sm-6">
            <label>Status: </label>
            <select class="form-control">
                <option>Active</option>
                <option>Inactive</option>
            </select>
        </div>
    </div>
</div>
@section scripts {
    <script>
        //Date pickers

        //Slide mechanic
        var fadeToggle = function (id) {
            $(id).toggle(function () {
                $(id).removeClass('animated slideInLeft');
                $(id).addClass('animated slideOutRight');                
            },
                function () {
                    $(id).removeClass('animated slideOutRight');
                    $(id).addClass('animated slideInLeft');
                });
        };

        $("#payHistory").hide();
        $("#paySummary").hide();
        $("#payCode").hide();
        $(document).on("click", "#payHistoryModal", function (e) {

            //Make AJAX call here
            console.log("test");
            fadeToggle("#payHistory")
            fadeToggle("#payCodesList");
        });




        function detailInit(e) {
            $("#check-sub-list").appendTo(e.detailCell).kendoGrid({
                dataSource: {
                    serverPaging: true,
                    serverSorting: true,
                    serverFiltering: true,
                    pageSize: 10,
                    /*filter: { field: "EmployeeID", operator: "eq", value: e.data.EmployeeID }*/
                },
                scrollable: false,
                sortable: true,
                pageable: true
            });
        }
        //Swap between pay history and pay code list
        $(document).on("click", "#backPayCodes", function (e) {
            fadeToggle("#payCodesList");
            fadeToggle("#payHistory");            
        });
        //Swap between pay history and summary
        $(document).on("click", "#paySummaryModal", function (e) {
            fadeToggle("#payCodesList");
            fadeToggle("#paySummary");
        });
        $(document).on("click", "#backPaySummary", function (e) {            
            fadeToggle("#payCodesList");
            fadeToggle("#paySummary");
        });
        $(document).on("click", "#changePayCode", function (e) {
            fadeToggle("#payCodesList");
            fadeToggle("#payCode");
        });
        $(document).on("click", "#backPayCode", function (e) {
            fadeToggle("#payCodesList");
            fadeToggle("#payCode");
        });
    </script>
}





