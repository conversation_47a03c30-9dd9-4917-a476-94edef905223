@using DarwiNet2._0.Controllers
<div class="row" style="padding-top: 35px;">

    <div class="col-lg-12">
        <div class="col-lg-4">
            <div class="row">
                <div class="col-md-4">
                    <label>Pay Period From</label>
                </div>
                <div class="col-md-8">
                    <span style="padding-left: 10px;">
                        @ViewBag.StartDate
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <label>Pay Period To</label>
                </div>
                <div class="col-md-8">
                    <span style="padding-left: 10px;">
                        @ViewBag.EndDate
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <label>Check Date</label>
                </div>
                <div class="col-md-8">
                    <span style="padding-left: 10px;">
                        @ViewBag.CheckDate
                    </span>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <label>Status</label>
                </div>
                <div class="col-md-8">
                    <span style="padding-left: 10px;">
                        @ViewBag.TimeSheetStatus
                    </span>
                </div>
            </div>
        </div>
    </div>
    @if (ViewBag.KeyValue != string.Empty)
    {
        <div class="col-lg-12">
            <div class="row">
                <div class="col-md-2">
                    <button class="btn btn-thinkware" style="width: 186px; display:inline-block;" id="prevTS" disabled="disabled"><i class="fa fa-arrow-left fa-fw"></i><span id="prevSpan"></span></button>
                </div>
                <div class="col-md-1"  style="padding-right: 10px;"><label class="pull-right">@ViewBag.KeyLabel</label></div>
                <div class="col-md-4">
                    <span>
                        <select class="form-control" style="display:inline-block;" id="CurrentKey" name="CurrentKey">
                            @{ foreach (Code_Description key in ViewBag.KeyValuesList)
                                {
                                    if (key.Description == ViewBag.KeyValue)
                                    {
                                        <option value="@key.Description" selected>@key.Description</option>
                                    }
                                    else
                                    {
                                        <option value="@key.Description">@key.Description</option>
                                    }
                                }
                            }
                        </select>
                    </span>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-thinkware" style="width: 186px; display: inline-block;" id="nextTS" disabled="disabled"><span id="nextSpan"></span> <i class="fa fa-arrow-right fa-fw"></i></button>
                </div>
            </div>
        </div>
    }
    <div class="col-lg-12">
        <div class="row">
            &nbsp;
        </div>
    </div>
</div>
