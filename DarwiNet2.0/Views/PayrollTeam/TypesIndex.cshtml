@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.ViewModels.D2
@model List<PayrollTeamTypeViewModel>
@{
    ViewBag.Title = "Payroll Team Types";
}
<div class="company-info">
    <div class="row">
        <div class="col-md-6">
            <p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
            <div class="colored-line-left"></div>
        </div>
    </div>
</div>
<div class="row">
    <div class="toolbar">
        <div class="row">
            <p class="create-pad pull-right">
                <a href="@Url.Action("Index", "PayrollTeam")" class="btn btn-thinkware">Payroll Teams</a>
                <a href="@Url.Action("CreateType", "PayrollTeam")" class="btn btn-thinkware" data-toggle="modal" data-target="#addModal" data-remote="false">Add New Type</a>
            </p>
        </div>
    </div>
</div>
<div class="row">
    <p style="text-align:center;">
        @(Html.Kendo().Grid<PayrollTeamTypeViewModel>()
        .Name("grid")
        .Columns(columns =>
        {
        columns.Bound(p => p.TeamType).Title("Team Type").Width(400);
        columns.Bound(p => p.Description).Title("Description");
        columns.Bound(p => p.CanDelete).Hidden();
        columns.Template(@<text></text>)
                           .ClientTemplate(
                               "<div class=' icon-center'>" +
                               "#if(CanDelete) {#" +
                                    //"<a href='" + Url.Action("DeleteTeamType", "PayrollTeam", new { type = "#= TeamType#" }) + "' title='Delete Team Type' class=''><i class='icon-red fa fa-times fa-fw fa-lg'></i></a>" +
                                    "<span title='Delete Team Type' id='delete' data-id='#= TeamType #' class='talk-to-the-hand'><i class='icon-red fa fa-times fa-fw fa-lg'></i></span>" +
                               "#} else {#" +
                                    "<i style='color: \\#ccc' class='fa fa-times fa-fw fa-lg' title='This Team Type uses by active Teams'></i>" +
                               "#} #</div>"
                               ).Title("Actions").Width(200).HeaderHtmlAttributes(new { @class = "header-center" });
        })
        .Editable(editable => editable.Mode(GridEditMode.InCell))
        .Pageable()
        .Sortable()
        .Scrollable()
        .HtmlAttributes(new { style = "height:550px;" })
        .DataSource(dataSource => dataSource
            .Ajax()
            .Read(read => read.Action("GetPayrollTeamTypes", "PayrollTeam"))
            .AutoSync(false)
            .PageSize(20)
            .Events(events => events.Error("error_handler"))
            .Model(model =>
            {
                model.Id(p => p.TeamType);
                model.Field(p => p.TeamType).Editable(false);
                model.Field(p => p.Description).Editable(true);
                model.Field(p => p.CanDelete).Editable(false);
            })
        )
    )
    </p>
    *Changes made in grid will save automatically


</div>








<div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="addModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="importModalLabel">Add Team Type</h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group row">
                        <label class="col-md-3 control-label">
                            @FieldTranslation.GetLabel("Team Type", GlobalVariables.LanguageID)
                        </label>
                        <div class="col-md-9">
                            <input type="text" id="teamtypeadd" name="teamtypeadd" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-3 control-label">
                            @FieldTranslation.GetLabel("Description", GlobalVariables.LanguageID)
                        </label>
                        <div class="col-md-9">
                            <input type="text" id="typedescradd" name="typedescradd" class="form-control" />
                        </div>
                    </div>

                    <div class="form-group row">
                        <div class="col-md-12">
                            <div class="pull-right">
                                <button class="btn btn-thinkware" id="AddType" onclick="AddType();">Add Type</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@section scripts{
    <script src="~/Scripts/bootbox.min.js"></script>
    <script type="text/javascript">
        function error_handler(e) {
            if (e.errors) {
                var message = "Errors:\n";
                $.each(e.errors, function (key, value) {
                    if ('errors' in value) {
                        $.each(value.errors, function () {
                            message += this + "\n";
                        });
                    }
                });
                alert(message);
            }
        }
    </script>
    <script>

    $("#addModal").on("show.bs.modal", function (e) {
        var link = $(e.relatedTarget);
        $(this).find(".modal-body").load(link.attr("href"));
    });
        $(function () {
            $('#grid').on('change', '#Description', function () {
                var dataItem = $('#grid').data().kendoGrid.dataItem($(this).closest('tr'));
                var descr = $(this).val();
                console.log(dataItem);
                    var data = {
                        PayrollTeamID: dataItem.TeamType,
                        Description: descr,
                        CanDelete: dataItem.CanDelete
                    };
                    $.ajax({
                        url: '@Url.Action("UpdateTypeDescription", "PayrollTeam")',
                        type: "POST",
                        data: data
                    });
            })
        });
    $(document).on("click", "td #delete", function (e) {
        var deletedID = $(this).attr('data-id');
        var url = "@Url.Action("DeleteTeamType", "PayrollTeam")";
        bootbox.dialog({
            message: "Are you sure you want to delete type: " + "<strong>" + deletedID + "</strong>",
            title: "Delete Team Type",
            buttons: {
                main: {
                    label: "Cancel",
                    className: "btn-primary",
                    callback: function () {
                        //Example.show("Primary button");
                    }
                },
                danger: {
                    label: "Delete",
                    className: "btn-danger",
                    callback: function () {
                        window.location.href = url + "?type=" + deletedID;
                    }
                }
            }
        });
    });

        function AddType() {
            var url = "@Url.Action("AddTeamType", "PayrollTeam")";
            url = url + "?type=" + $('#teamtypeadd').val() + "&descr=" + $('#typedescradd').val();
            console.log(url);
            window.location.href = url;

        }
    </script>
}