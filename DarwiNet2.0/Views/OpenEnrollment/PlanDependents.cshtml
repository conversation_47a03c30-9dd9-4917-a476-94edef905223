@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DarwiNet2._0.Data

@model DarwiNet2._0.ViewModels.OpenEnrollment.PlanDependentsVM

@{
    ViewBag.Title = "PlanDependents";
}

<link href="~/Content/OpenEnrollment.css" rel="stylesheet" />
<div class="container-fluid" id="home-features">
    @Html.Partial("_wizardPartial", Model.Employee)
    <div class="row">
        <div class="col-md-12">
            @if (Model.InstructionsEnabled)
            {
                <p style="font-size: 22px;">@Html.Raw(Model.InstructionsTitle)</p>
                <div class="colored-line-left"></div>
            }
            
            <p>@if (Model.InstructionsEnabled)
            {@Html.Raw(Model.InstructionsText)}<span class="pull-right" style="padding-bottom: 10px;"><button data-toggle="modal" data-target="#startOverModal" class="btn btn-lg btn-thinkware">+ Add Dependent</button></span></p>
        </div>
        <div class="col-md-12" style="padding-bottom: 15px;">
            <table class="table" id="depTable">
                <tr>
                    <th>
                        Name
                    </th>
                    <th>
                        SSN
                    </th>
                    <th>
                        Age
                    </th>
                    <th>
                        Relationship
                    </th>
                    @*@if (Model.MemberRated)
                    {
                        <th>
                            Per Check Amount
                        </th>
                        <th>
                            Per Month Amount
                        </th>
                        <th>
                            Employer Monthly Contribution
                        </th>
                    }*@
                    
                    <th>
                        Covered
                    </th>
                </tr>

                @foreach (var item in Model.Dependents)
                {
                    <tr>
                        <td>
                            @Html.DisplayFor(modelItem => item.DependentName)
                        </td>
                        <td>
                            @FieldTranslation.GetMaskSSN(item.DependentSSN)
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.Age)
                        </td>
                        <td>
                           @Html.DisplayFor(modelItem => item.Relationship)
                        </td>
                        @*@if (Model.MemberRated)
                        {
                            <td>
                                @Html.DisplayFor(modelItem => item.PlanAmount)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.PlanAgencyAmount)
                            </td>
                            <td>
                                @Html.DisplayFor(modelItem => item.PlanMonthlyCost)
                            </td>
                        }*@
                        <td>
                            <div class="input-group">
                                <div class="btn-group radioBtn" id="<EMAIL>">
                                    <a class="btn btn-success btn-sm @((!item.Covered) ? "active" : "notActive")" data-toggle="<EMAIL>" data-id="@item.DependentSSN" data-year="@item.YEAR" data-title="false">NO</a>
                                    <a class="btn btn-success btn-sm  @((item.Covered) ? "active" : "notActive")" data-toggle="<EMAIL>" data-id="@item.DependentSSN" data-year="@item.YEAR" data-title="true">YES</a>
                                </div>
                                <input type="hidden" name="<EMAIL>" id="<EMAIL>" value="@item.Covered" data-id="@item.DependentSSN" data-year="@item.YEAR">
                            </div>
                            @*@Html.DisplayFor(modelItem => item.Covered)*@
                        </td>
                    </tr>
                }
            </table>
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-12">
            <a href="@Url.Action("PlanSelect", "OpenEnrollment", new {bt = @Model.BenefitType})" class="btn btn-thinkware btn-lg"><i class="fa fa-arrow-left fa-fw"></i>Back</a>
            <div class="pull-right">
                <a href="@Url.Action("PlanDependentsDone", "OpenEnrollment", new {bt = @Model.BenefitType, pn = @Model.PlanName})" class="btn btn-success btn-lg">Next<i class="fa fa-arrow-right fa-fw"></i></a>
            </div>
        </div>
    </div>
    <div style="margin-bottom: 75px;"></div>
</div>
<div class="modal fade" id="startOverModal" tabindex="-1" role="dialog" aria-labelledby="startOverModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="startOverModalLabel">ADD DEPENDENT</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12 text-center">
                        <i style="color: yellow;" class="fa fa-exclamation-triangle fa-4x"></i>
                    </div>
                    <div class="col-md-12">
                        <div>
                            <p>In order to add a dependent, you must completely restart the Open Enrollment Process. All previously made plan selections will be deleted.<br/>
                            </p>
                            <p>Are you sure you want to do this?</p>
                        </div>
                        <div class="text-center" style="font-weight: bold; color: red;">This action CANNOT be undone</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">NO</button>
                <a href="@Url.Action("ResetOE", "OpenEnrollment", new {y=0})" class="btn btn-success">YES</a>
            </div>
        </div>
    </div>
</div>
@if (Model.InstructionsModalEnabled)
{
<div class="modal fade" id="pageModal" tabindex="-1" role="dialog" aria-labelledby="pageModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="pageModalLabel">@Html.Raw(Model.InstructionsModalTitle)</h4>
            </div>
            <div class="modal-body">
                @Html.Raw(Model.InstructionsModalText)
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {
        $('#pageModal').modal('toggle');
    });
</script>
    }

<script>
    $(document)
        .ready(function() {
            var grid = $("#depTable")
                .kendoGrid({
                    dataSource: {
                        pageSize: 15
                    },
                    //sortable: true,
                    //pageable: true,
                    //filterable: true,
                    scrollable: false,
                    //groupable: true,
                    resizable: true,
                })
                .data("kendoGrid");

        });

    $(document)
        .on('click',
            '.radioBtn a',
            function() {
                var sel = $(this).data('title');
                var tog = $(this).data('toggle');
                $('#' + tog).prop('value', sel);

                $('a[data-toggle="' + tog + '"]')
                    .not('[data-title="' + sel + '"]')
                    .removeClass('active')
                    .addClass('notActive');
                $('a[data-toggle="' + tog + '"][data-title="' + sel + '"]').removeClass('notActive').addClass('active');

                var ssn = $(this).attr('data-id');
                var year = $(this).attr('data-year');
                var url = '';
                if (sel) {
                    url = '@Url.Action("CoverDependent", "OpenEnrollment")';
                } else {
                    url = '@Url.Action("UnCoverDependent", "OpenEnrollment")';
                }
                //url = url + "?ssn=" + ssn + "&year=" + year + "&pn=" + "@Model.PlanName";
                $.ajax({
                        method: "GET",
                        url: url,
                        data: {
                            ssn: ssn,
                            year: year,
                            pn: "@Model.PlanName"
                        }
                    })
                    .done(function(msg) {
                        if (msg == "Success") {
                        }
                    });

            });
</script>

