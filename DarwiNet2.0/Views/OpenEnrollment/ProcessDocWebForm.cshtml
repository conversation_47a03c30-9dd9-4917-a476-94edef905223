@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@*@if (ViewBag.MultiDoc == null || ViewBag.MultiDoc == false)
{
    @Html.Raw(ViewBag.WebForm)
}*@

    <script type="text/javascript">
    $(function () {
        $("#btnIframe").click(function () {
            var docNum = @ViewBag.DocId;
            var planName = encodeURIComponent("@ViewBag.PlanName");
            var submitURL = "@Url.Action("SubmitPDF", "OpenEnrollment", null, Request.Url.Scheme)" + "/?docId=" + docNum + "&year=" + @ViewBag.PlanYear + "&plan=" + planName;
            var url = "@Url.Action("ShowWebForm", "OpenEnrollment")" + "/?id=" + @ViewBag.DocId + "&year=" + @ViewBag.PlanYear + "&plan=" + @ViewBag.PlanName + "&hideView=1";
            $('#pdfDiv').load(url, null, function (response, status, xhr) {
                if (response.indexOf('NoForm') >= 0) {
                    var planName = encodeURIComponent("@ViewBag.PlanName");
                    var redirectURL = "@Url.Action("ProcessPDF", "OpenEnrollment")" + "/?id=" + docNum + "&year=" + @ViewBag.PlanYear + "&plan=" + planName;
                    window.parent.location.href = redirectURL;
                }
            });
        });
    });
    $(function () {
        @*$("#btnDefIframe").click(function () {
            var docNum = $("#W4DefDoc").val();
            var submitURL = "@Url.Action("SubmitPDF", "OpenEnrollment", null, Request.Url.Scheme)" + "/?docId=" + docNum;
            var url = "@Url.Action("ShowForm", "OpenEnrollment")" + "/?docId=" + docNum + "&url=" + submitURL;
                    var html = "<iframe src=" + url + " style='width: 100%; height: 700px' ></iframe>";
                    $('#pdfDiv').html(html);
                });
            });*@
        $('#btnDefIframe').click(function () {
            var docNum = @ViewBag.DocId;
            var planName = encodeURIComponent("@ViewBag.PlanName");
            var submitURL = "@Url.Action("SubmitPDF", "OpenEnrollment", null, Request.Url.Scheme)" + "/?docId=" + docNum + "&year=" + @ViewBag.PlanYear + "&plan=" + planName;
            var url = "@Url.Action("ShowWebForm", "OpenEnrollment")" + "/?id=" + @ViewBag.DocId + "&year=" + @ViewBag.PlanYear + "&plan=" + @ViewBag.PlanName + "&hideView=1";
            $('#pdfDiv').load(url, null, function (response, status, xhr) {
                if (response.indexOf('NoForm') >= 0) {
                    var planName = encodeURIComponent("@ViewBag.PlanName");
                    var redirectURL = "@Url.Action("ProcessPDF", "OpenEnrollment")" + "/?id=" + docNum + "&year=" + @ViewBag.PlanYear + "&plan=" + planName;
                    window.parent.location.href = redirectURL;
                }
            });
        });
    });
</script>

<!--START HERE-->
<div class="row">
    
<!--Dynamic Table-->
<div id="pdfview" class="col-md-12 instructions-box-dynamic">
   @{
       if (!String.IsNullOrEmpty(ViewBag.Instructions))
       {
        <div class="dynamic-box">
            <div>
                <p style="font-size: 22px;">Instructions</p>
                <div class="colored-line-left"></div>
                <div class="instructions-box">
                    @Html.Raw(ViewBag.Instructions)
                </div>
            </div>
        </div>
       }
}
    <div class="div-10"></div>
    @{
        if (ViewBag.MultiDoc != null)
        {
            <div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success alert-dismissible" role="alert">
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <p>
                                <strong>Note:</strong> This state has multiple available documents. please review/complete all necessary documents.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="col-md-3">
                            <label class="control-label">Select Default Document:</label>
                        </div>
                        <div class="col-md-4">
                            <select id="W4DefDoc" name=" W4DefDoc" class="form-control">
                                @foreach (var doc in ViewBag.AvailableDefDocs)
                                {
                                    <option value="@doc.Code">@doc.Description</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button id="btnDefIframe" name="btnDefIframe" class="btn btn-thinkware">View/Complete This Document</button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success" id="showDocs">Show Additional Documents</button>
                    </div>
                </div>
                <div class="row" style="padding-top: 15px; display: none" id="otherDocs">
                    <div class="col-md-12">
                        <div class="col-md-3">
                            <label class="control-label"> Select Other Document:</label>
                        </div>
                        <div class="col-md-4">
                            <select id="W4Doc" name=" W4Doc" class="form-control">
                                @foreach (var doc in ViewBag.AvailableDocs)
                                {
                                    <option value="@doc.Code">@doc.Description</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button id="btnIframe" name="btnIframe" class="btn btn-thinkware">View/Complete This Document</button>
                        </div>
                    </div>
                </div>
                <br/><br/>
            </div>
        }
        if (ViewBag.Waived != null)
        {
            if (ViewBag.Waived)
            {
                <div><input type="checkbox" id="waive" name="waive" checked  /> I wish to Waive </div>
            }
            else
            {
                <div><input type="checkbox" id="waive" name="waive" /> I wish to Waive</div>
            }
        }
}
@{
    if (ViewBag.Waived != null)
    {
    <div class="waive-top">
        <p class="" id="waived">
            <a href="@Url.Action("Waive", "OpenEnrollment" , new { id=ViewBag.DocId, tid=ViewBag.TaskId })" title="Waive and Continue" data-toggle="modal" class="btn btn-success btn-lg">Waive and Continue&nbsp;&nbsp;<i class="fa fa-arrow-right fa-fw"></i></a>
        </p>
    </div>
    }
}
    <div class="create-pad pull-right doc-bottom" id="doc-done">
    @{
        <a href="@Url.Action("Summary", "OpenEnrollment")" title="Return To Task" data-toggle="modal" class="btn btn-thinkware">Back To Task</a>
        if (ViewBag.CanContinue)
        {
            if (ViewBag.CanUnlock && ViewBag.FinalizeStatus != 2)
            {
            <a href="@Url.Action("DocumentUnDone", "OpenEnrollment", new { id = ViewBag.DocId, plan = ViewBag.PlanName, year = ViewBag.Planyear })" style="width: 200px; "title="Unlock Document for Edits" data-toggle="modal" class="btn btn-thinkware"><i class="fa fa-fw fa-unlock fa-lg"></i>&nbsp; Unlock Document to Edit</a>
            }
            <a href="@Url.Action("DocumentDone", "OpenEnrollment", new { id = ViewBag.DocId, plan = ViewBag.PlanName, year = ViewBag.Planyear })" title="Continue" data-toggle="modal" class="btn btn-success">Continue&nbsp;&nbsp;<i class="fa fa-arrow-right fa-fw"></i></a>
        }
}
    </div>
    <div id="pdfDiv" name="pdfDiv">
        @if (ViewBag.DocId != 0)
        {
            @Html.Action("ShowWebForm", "OpenEnrollment", new { id = @ViewBag.DocId, plan = @ViewBag.PlanName, year = @ViewBag.PlanYear })
        }
    </div>
</div>
<!--End SideBar Nav Loop-->
</div>


<!--Slider-->
@if (ViewBag.TaskTip != string.Empty)
{

        <!-- Tip Sidebar -->
    <aside class="tip-sidebar tip-sidebar-dark">


        <div class="tab-content">
            <div class="inner">
                <p style="font-size: 22px;">Additional Tips</p>
                <div class="colored-line-left"></div>
                <div class="text">
                    @Html.Raw(ViewBag.TaskTip)
                </div>
                <div>
                    <a href="@ViewBag.TipImage" data-lightbox="image-1"><img class="img-responsive" src="@ViewBag.TipImage"></a>
                </div>
            </div>

        </div>
    </aside><!-- /.tip-sidebar -->
    <!-- Add the sidebar's background. This div must be placed
     immediately after the control sidebar -->
    <div class="tip-sidebar-bg"></div>

}
<script src="~/Scripts/bootbox.min.js"></script>
<script src="~/Scripts/MaskedInput.js"></script>
<script>
    $('input[type="checkbox"]').on('click', function() {
        $(this).val(this.checked ? "True" : "False");
        console.log($(this).val());
    });
    $('#showhide').on('click', function () {
        var $this = $(this);
        $('#stepnavigation').toggle();
        $('#pdfview').toggleClass("col-md-9 col-md-12");
        $($this).toggleClass("steps-shown steps-hidden");
        if ($this.hasClass('steps-shown')) {
            $this.text('Hide Steps');
        } else {
            $this.text('Show Steps');
        }
        
    });
</script>
<script>
    $('.handle').click(function () {
        $("i", this).toggleClass("fa-arrow-circle-o-left fa-arrow-circle-o-right ");
    });
</script>

<script>
    $(document).ready(function () {
        $("#SocialSecNumber").mask("?***********");
    });
</script>

<script>
    $(document).ready(function () {
        $("#EmployeeID").mask("?**-***-**********");
    });
    $('#showDocs')
        .click(function () {
            var $this = $(this);
            $this.toggleClass('showDocsButton');
            $('#otherDocs').toggle();
            if ($this.hasClass('showDocsButton')) {
                $this.text('Hide Additional Documents');
            } else {
                $this.text('Show Additional Documents');
            }
        });
</script>
<script>
    $(document).on("click", "#waive", function (e) {
        var val = $(this).val();
        if (val == "True") {
            var url = "@Url.Action("Waive", "OpenEnrollment")";
            bootbox.dialog({
                message: "Are you sure you want to waive this form?",
                title: "Waive Form",
                buttons: {
                    main: {
                        label: "Cancel",
                        className: "btn-default",
                        callback: function () {
                            $('input:checkbox').removeAttr('checked');
                            $('#pdfDiv').show();
                            $('#waived').hide();
                            $('#doc-done').show();
                        }
                    },
                    danger: {
                        label: "Waive and Continue",
                        className: "btn-danger",
                        callback: function () {
                            window.location.href = url;
                        }
                    }
                }
            });
        }
    });
</script>


