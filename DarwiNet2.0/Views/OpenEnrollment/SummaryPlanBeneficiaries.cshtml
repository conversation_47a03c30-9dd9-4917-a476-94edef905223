@using DarwiNet2._0.Data

@model IEnumerable<DarwiNet2._0.Data.EmployeeEnrollmentPlanBeneficiary>

@{
    Layout = null;
}

    <table class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Relationship</th>
                <th>Benefit Percent</th>
            </tr>
        </thead>
        <tbody>
            @foreach (EmployeeEnrollmentPlanBeneficiary thisBen in Model)
            {
                <tr>
                    <td>@thisBen.FirstName</td>
                    <td>@thisBen.LastName</td>
                    <td>@thisBen.Relationship</td>
                    <td align="right">@(thisBen.BenefitPercent) %</td>
                </tr>
            }
        </tbody>
    </table>

