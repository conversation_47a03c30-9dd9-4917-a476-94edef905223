@using System.Activities.Statements
@using DarwiNet2._0.Data

@model DarwiNet2._0.ViewModels.OpenEnrollment.LifePlanVM

<link href="~/Content/OpenEnrollment.css" rel="stylesheet" />

@{
    int counter = 0;
    int thinCounter = 0;
    int subcounter = 0;
    int subthinCounter = 0;
}
<style>
    .ui-widget {
        font-family: Verdana,Arial,sans-serif;
        font-size: 1.6em !important;
        margin-bottom: 15px;
    }

    .ui-state-default, .ui-widget-content .ui-state-default {
        border: none !important;
        outline: none !important;
        background: #5cb85c !important;
        font-weight: normal;
        color: #fff !important;
    }

    .ui-state-focus, .ui-widget-content .ui-state-focus {
        border: none !important;
        outline: none !important;
        background: #3d8b3d !important;
    }
</style>
<div class="container-fluid" style="overflow-x: auto;">
    @Html.Partial("_wizardPartial", Model.Employee)
    <div class="row">
        <div class="col-md-2">
            @Html.Action("SideNav", "OpenEnrollment", new { bt = Model.BenefitType })
        </div>
        <div class="col-md-10">
            <div class="row">
                <div class="col-md-12">

                </div>
            </div>
            <div id="lifeSelect">
                <div class="row">
                    <div class="col-md-12">
                        @if (Model.InstructionsEnabled)
                        {
                            <p style="font-size: 22px;">@Html.Raw(Model.InstructionsTitle)</p>
                            <div class="colored-line-left"></div>
                            <p>
                                @Html.Raw(Model.InstructionsText)
                            </p>
                        }

                        @if (!string.IsNullOrEmpty(Model.PlanDescription))
                        {
                            <p>
                                @Html.Raw(Model.PlanDescription)
                            </p>
                        }
                    </div>
                </div>
                @if (Model.ShowDependents)
                {
                    <div class="row" style="margin-top: 15px;">
                        <div class="col-md-12">
                            <div style="padding-left: 10px;">
                                <button class="btn btn-thinkware" data-toggle="modal" data-target="#depModal">Select Dependents</button>
                            </div>
                        </div>
                    </div>
                }
                <div class="row">
                    <div class="col-md-12">
                        <div style="padding-top: 20px;">
                            <div class="col-md-2">
                                <div>
                                    Employee Coverage
                                </div>
                            </div>
                            <div class="col-md-10">
                                <div style="margin-top: 12px;">
                                    <div id="slider"></div>
                                </div>
                                <p>
                                    <label for="amount" style="font-size: 22px;">Total amount (@string.Format("{0:c}", Model.Max) max amount; @string.Format("{0:c}", Model.EmployeeIncrement) increments):</label>
                                    <input type="text" id="amount" readonly style="border: 0; color: #f6931f; font-weight: bold; font-size: 22px;">
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                @if (Model.ShowDependents)
                {
                    if (Model.SpouseIncrement > 0 && Model.SpousePremium > 0)
                    {
                        <div class="row">
                            <div class="col-md-12">
                                <div style="padding-top: 20px;">
                                    <div class="col-md-2">
                                        <div>
                                            Spouse Coverage
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div style="margin-top: 12px;">
                                            <div id="slider1"></div>
                                        </div>
                                        <p>
                                            <label for="amount">Total amount (@string.Format("{0:c}", Model.SpouseMax) max amount; @string.Format("{0:c}", Model.SpouseIncrement) increments):</label>
                                            <input type="text" id="amount1" readonly style="border: 0; color: #f6931f; font-weight: bold;">
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    if (Model.ChildIncrement > 0 && Model.ChildPremium > 0)
                    {
                        <div class="row">
                            <div class="col-md-12">
                                <div style="padding-top: 20px;">
                                    <div class="col-md-2">
                                        <div>
                                            Dependent Coverage
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div style="margin-top: 12px;">
                                            <div id="slider2"></div>
                                        </div>
                                        <p>
                                            <label for="amount">Total amount (@string.Format("{0:c}", Model.ChildMax) max amount; @string.Format("{0:c}", Model.ChildIncrement) increments):</label>
                                            <input type="text" id="amount2" readonly style="border: 0; color: #f6931f; font-weight: bold;">
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }
                <div class="row" style="padding-top: 25px;">
                    <div class="pull-right">
                        <div class="col-md-6 col-md-offset-6">
                            <table class="table table-stripe table-bordered">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>Coverage Amount</th>
                                        <th>Cost Per Month</th>
                                        <th>Cost Per Check</th>
                                        @if (Model.UseGuaranteeAmount)
                                        {
                                            <th>Elected Amount</th>
                                            <th>Guarantee Amount</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            Employee
                                        </td>
                                        <td style="text-align: right">
                                            <span id="eeAmount">@((Model.EmployeePlan.EmployeeLifeAmount ?? 0).ToString("C2"))</span>
                                        </td>
                                        <td style="text-align: right">
                                            <span id="eePremium">@((Model.EmployeePlan.EmployeeLifePremium ?? 0).ToString("C2"))</span>
                                        </td>
                                        <td style="text-align: right">
                                            <span id="eePremiumPerCheck">@((Model.EmployeePerCheckPremium ?? 0).ToString("C2"))</span>
                                        </td>
                                        @if (Model.UseGuaranteeAmount)
                                        {
                                            <td style="text-align: right">
                                                <span id="eeElected">@((Model.EmployeePlan.EmployeeRequestAmount ?? 0).ToString("C2"))</span>
                                            </td>
                                            <td style="text-align: right">
                                                <span id="eeGuarantee">@((Model.EEGuaranteeMaxAmount).ToString("C2"))</span>
                                            </td>
                                        }
                                    </tr>
                                    @if (Model.ShowDependents)
                                    {
                                        if (Model.SpouseIncrement > 0 && Model.SpousePremium > 0)
                                        {
                                            <tr>
                                                <td>
                                                    Spouse
                                                </td>
                                                <td style="text-align: right">
                                                    <span id="spAmount">@((Model.EmployeePlan.SpouseLifeAmount ?? 0).ToString("C2"))</span>
                                                </td>
                                                <td style="text-align: right">
                                                    <span id="spPremium">@((Model.EmployeePlan.SpouseLifePremium ?? 0).ToString("C2"))</span>
                                                </td>
                                                <td style="text-align: right">
                                                    <span id="spPremiumPerCheck">@((Model.EmployeeSpousePerCheckPremium ?? 0).ToString("C2"))</span>
                                                </td>
                                                @if (Model.UseGuaranteeAmount)
                                                {
                                                    <td style="text-align: right">
                                                        <span id="spElected">@((Model.EmployeePlan.SpouseRequestAmount ?? 0).ToString("C2"))</span>
                                                    </td>
                                                    <td style="text-align: right">
                                                        <span id="spGuarantee">@((Model.SpouseGuaranteeMaxAmount).ToString("C2"))</span>
                                                    </td>
                                                }
                                            </tr>
                                        }
                                        if (Model.ChildIncrement > 0 && Model.ChildPremium > 0)
                                        {
                                            <tr>
                                                <td>
                                                    Children
                                                </td>
                                                <td style="text-align: right">
                                                    <span id="depAmount">@((Model.EmployeePlan.DependentLifeAmount ?? 0).ToString("C2"))</span>
                                                </td>
                                                <td style="text-align: right">
                                                    <span id="depPremium">@((Model.EmployeePlan.DependentLifePremium ?? 0).ToString("C2"))</span>
                                                </td>
                                                <td style="text-align: right">
                                                    <span id="depPremiumPerCheck">@((Model.EmployeeDependentPerCheckPremium ?? 0).ToString("C2"))</span>
                                                </td>
                                                @if (Model.UseGuaranteeAmount)
                                                {
                                                    <td></td>
                                                    <td></td>
                                                }
                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <div class="col-md-12">
                            <a href="@Url.Action("DependentsIndex", "OpenEnrollment")" class="btn btn-thinkware btn-lg"><i class="fa fa-arrow-left fa-fw"></i>Back</a>
                            <div class="pull-right">
                                <a href="@Url.Action("PlanAmountEntryDone", "OpenEnrollment", new {bt = Model.BenefitType, pn = Model.PlanName})" class="btn btn-success btn-lg">Next<i class="fa fa-arrow-right fa-fw"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div class="modal fade" id="depModal" tabindex="-1" role="dialog" aria-labelledby="depModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="depModalLabel">Select Dependents</h4>
            </div>
            <div class="modal-body">
                <ul class="list-unstyled">
                    @foreach (EmployeeEnrollmentPlanDependent thisDep in Model.Dependents)
                    {
                        <li><input type="checkbox" class="memDepChk" id="@thisDep.DependentSSN" data-id="@Model.RowID" @((thisDep.Covered) ? "checked" : string.Empty) /> @thisDep.DependentName</li>
                    }
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" data-dismiss="modal">Save changes</button>
            </div>
        </div>
    </div>
</div>
@if (Model.UseGuaranteeAmount)
{
    <input type="hidden" id="useGA" name="useGA" value="1" />
}
else
{
    <input type="hidden" id="useGA" name="useGA" value="0" />
}
<input type="hidden" id="depCount" name="depCount" value="@Model.DependentCount" />
<link rel="stylesheet" href="//code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">
@section scripts{
    <script type="text/javascript">
    function formatCurrency(total) {
        var neg = false;
        if(total < 0) {
            neg = true;
            total = Math.abs(total);
        }
        return (neg ? "-$" : '$') + parseFloat(total, 10).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, "$1,").toString();
    }
    $('.radioBtn a')
        .on('click',
            function() {
                var sel = $(this).data('title');
                var tog = $(this).data('toggle');
                $('#' + tog).prop('value', sel);

                $('a[data-toggle="' + tog + '"]')
                    .not('[data-title="' + sel + '"]')
                    .removeClass('active')
                    .addClass('notActive');
                $('a[data-toggle="' + tog + '"][data-title="' + sel + '"]')
                    .removeClass('notActive')
                    .addClass('active');
            });

        var sliderEEAmount;
        $(document).ready(function () {
            var eePrem = @Model.EmployeePremium;
            var currentStep = @Model.EELifeAmount / @Model.EmployeeIncrement;
            eePrem = eePrem * currentStep;
            var empAmount = getPerCheckAmount(eePrem);
            $("#eePremium").html(formatCurrency(eePrem));
            $("#eePremiumPerCheck").html(formatCurrency(empAmount));
        });
        $(document).ready(function () {
            var spPrem = @Model.SpousePremium;
            var currentStep = @Model.SpouseLifeAmount / @Model.SpouseIncrement;
            spPrem = spPrem * currentStep;
            var empAmount = getPerCheckAmount(spPrem);
            $("#spPremium").html(formatCurrency(spPrem));
            $("#eePremiumPerCheck").html(formatCurrency(empAmount));
            //$("#spAmount").html(formatCurrency(spAmount));
        });
        $(document).ready(function () {
            var depPrem = @Model.ChildPremium;
            var depCount = $("#depCount").val();
            var currentStep = @Model.ChildLifeAmount / @Model.ChildIncrement;
            depPrem = depPrem * currentStep * depCount;
            $("#depPremium").html(formatCurrency(depPrem));
            //$("#depAmount").html(formatCurrency(ui.value));
        });
    $(function() {
        $("#slider")
            .slider({
                value: @Model.EELifeAmount,
                min: 0,
                max: @Model.Max,
                step: @Model.EmployeeIncrement,
                slide: function (event, ui) {
                    var eeElected = ui.value;
                    var eeAmount = eeElected;
                    var gAmount = @Model.EEGuaranteeMaxAmount;
                    var useGA = $("#useGA").val() == "1";
                    if (eeElected > gAmount && useGA) eeAmount = gAmount;
                    $("#amount").val(formatCurrency(eeAmount));
                    var eePrem = @Model.EmployeePremium;
                    var currentStep = eeAmount / @Model.EmployeeIncrement;
                    eePrem = eePrem * currentStep;
                    $("#eePremium").html(formatCurrency(eePrem));
                    $("#eeAmount").html(formatCurrency(eeAmount));
                    // get the per check amount
                    var empAmount = getPerCheckAmount(eePrem);
                    $("#eePremiumPerCheck").html(formatCurrency(empAmount));
                    if (useGA) $("#eeElected").html(formatCurrency(eeElected))
                },
                stop: function(event, ui) {
                    var t = 1;
                    var el = ui.value;
                    var amt = el;
                    var gamt = @Model.EEGuaranteeMaxAmount;
                    var useGA = $("#useGA").val() == "1";

                    if (useGA && amt > gamt) amt = gamt;
                    var eePrem = @Model.EmployeePremium;
                    var currentStep = ui.value / @Model.EmployeeIncrement;
                    eePrem = eePrem * currentStep;
                    SaveChange(t, amt, eePrem, el);
                }
            });
        $("#slider1")
            .slider({
                value: @Model.SpouseLifeAmount,
                min: 0,
                max: @Model.SpouseMax,
                step: @Model.SpouseIncrement,
                slide: function(event, ui) {
                    var spGuarantee = @Model.SpouseGuaranteeMaxAmount;
                    var spElected = ui.value;
                    var spAmount = spElected;
                    var useGA = $("#useGA").val() == "1";
                    if (spElected > spAmount && useGA) spAmount = spGuarantee;
                    $("#amount1").val(formatCurrency(spAmount));
                    var spPrem = @Model.SpousePremium;
                    var currentStep = ui.value / @Model.SpouseIncrement;
                    spPrem = spPrem * currentStep;
                    $("#spPremium").html(formatCurrency(spPrem));
                    $("#spAmount").html(formatCurrency(spAmount));
                    // get the per check amount
                    var empAmount = getPerCheckAmount(spPrem);
                    $("#spPremiumPerCheck").html(formatCurrency(empAmount));
                    if (useGA) $("#spElected").html(formatCurrency(spElected));
                },
                stop: function(event, ui) {
                    var t = 2;
                    var el = ui.value;
                    var amt = el;
                    var gamt = @Model.SpouseGuaranteeMaxAmount;
                    var useGA = $("#useGA").val() == "1";

                    if (useGA && amt > gamt) amt = gamt;
                    var spPrem = @Model.SpousePremium;
                    var currentStep = ui.value / @Model.SpouseIncrement;
                    spPrem = spPrem * currentStep;
                    SaveChange(t, amt, spPrem, el);
                }
            });
        $("#slider2")
            .slider({
                value: @Model.ChildLifeAmount,
                min: 0,
                max: @Model.ChildMax,
                step: @Model.ChildIncrement,
                slide: function(event, ui) {
                    $("#amount2").val(formatCurrency(ui.value));
                    var depPrem = @Model.ChildPremium;
                    var depCount = $("#depCount").val();
                    var currentStep = ui.value / @Model.ChildIncrement;
                    depPrem = depPrem * currentStep * depCount;
                    $("#depPremium").html(formatCurrency(depPrem));
                    $("#depAmount").html(formatCurrency(ui.value));
                    // get the per check amount
                    var empAmount = getPerCheckAmount(depPrem);
                    $("#depPremiumPerCheck").html(formatCurrency(empAmount))
                },
                stop: function(event, ui) {
                    var t = 3;
                    var amt = ui.value;
                    var depPrem = @Model.ChildPremium;
                    var depCount = $("#depCount").val();
                    var currentStep = ui.value / @Model.ChildIncrement;
                    depPrem = depPrem * currentStep * depCount;
                    SaveChange(t, amt, depPrem, amt);
                }
            });
        $("#amount").val("$" + $("#slider").slider("value"));
        $("#amount1").val("$" + $("#slider1").slider("value"));
        $("#amount2").val("$" + $("#slider2").slider("value"));



        function SaveChange(t, amt, pre, elc) {
            var url = "@Url.Action("ChangeLifeAmount", "OpenEnrollment", new {pn = Model.PlanName})";
            var y = @Model.Year;
            url = url + "&pre=" + pre + "&t=" + t + "&amt=" + amt + "&y=" + y + "&eamt=" + elc;
            $.get(url, function (data) {});
        }
    });

    function getPerCheckAmount(amount) {
        var y = @Model.Year;
        var pn = "@Model.PlanName";
        var am = amount;
        var bt = "@Model.BenefitType";
        var url = "@Url.Action("GetCostPerCheck", "OpenEnrollment")"
        url = url + "?amount=" + am + "&planName=" + pn + "&planType=" + bt + "&planYear=" + y;
        $.ajax({
            url: url,
            type: 'get',
            dataType: 'html',
            async: false,
            success: function(data) {
                result = data;
            }
        });
        return result;
    }

    $(".memDepChk").click(function () {
        var rid = $(this).attr("data-id");
        var ssn = $(this).attr("id");
        var ImChecked = $(this).is(':checked');
        var url = '';
        if (ImChecked) {
            url = '@Url.Action("CoverLifeDependent", "OpenEnrollment")';
        } else {
            url = '@Url.Action("UnCoverLifeDependent", "OpenEnrollment")';
        }
        $.ajax({
            method: "GET",
            url: url,
            data: {
                rid: rid,
                ssn: ssn
            }
        })
            .done(function (msg) {
                if (msg.Code == "Success") {
                    var depTotal = msg.Description;
                    $("#depCount").val(depTotal);
                    //alert(depTotal);
                }
            });

    });
    $('#waiverCheck')
        .change(function() {
            if ($("#waiverCheck").is(":checked")) {
                $('#lifeSelect').hide();
            } else {
                $('#lifeSelect').show();
            }
        });
    </script>
}
