@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@using DataDrivenViewEngine.Models.Core
@model DarwiNet2._0.Data.EmployeeDependent
@using Gender = DataDrivenViewEngine.Models.Core.enGenders
@using Relationship = DataDrivenViewEngine.Models.Core.enRelationship
@using NoSpouseRelationship = DataDrivenViewEngine.Models.Core.enNoSpouseRelationship
@{
    Layout = null;
}
@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.ID)
        @Html.HiddenFor(model=> model.EmployeeID)
        <div class="form-group">
            @Html.LabelFor(model => model.FirstName, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.TextBoxFor(model => model.FirstName, new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.MiddleName, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.TextBoxFor(model => model.MiddleName, new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.LastName, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.TextBoxFor(model => model.LastName, new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.SSN, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <input type="text" readonly class="form-control ssn-type" value="@FieldTranslation.GetMaskSSN(Model.SSN)"/>
            </div>
        </div>

        <div class="form-group field-required-wrap">
            @Html.LabelFor(model => model.BirthDate, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Birthdate", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @*Html.TextBoxFor(model => model.BirthDate, new { @class = "BirthDate field-required", required = "required" })*@
                <input type="text" class="" value="@FieldTranslation.ToShortDate(Model.BirthDate.ToString())" id="BirthDate" name="BirthDate" />
                <div class="error-msg"></div>
                @Html.ValidationMessageFor(model => model.BirthDate, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.Gender, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownList("Gender", EnumHelper.GetSelectList(typeof(enGetGender)), new { id = "Gender", name = "Gender", @class = "form-control" })
                @Html.ValidationMessageFor(model => model.Gender, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.EmployeeRelationship, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Relationship", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @if(ViewBag.SpouseExists != true || Model.EmployeeRelationship == 1)
                {
                    @Html.DropDownList("EmployeeRelationship", EnumHelper.GetSelectList(typeof(Relationship)), new { id = "EmployeeRelationship", name = "EmployeeRelationship", @class = "form-control" })
                }
                else
                {
                    @Html.DropDownList("EmployeeRelationship", EnumHelper.GetSelectList(typeof(NoSpouseRelationship)), new { id = "EmployeeRelationship", name = "EmployeeRelationship", @class = "form-control" })
                }
                @Html.ValidationMessageFor(model => model.EmployeeRelationship, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.LivesWithEmployee, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("LivesWithEE", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @if (Model.LivesWithEmployee)
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="LivesWithEmployeeToggle">
                            <a class="btn btn-success btn-sm notActive" data-toggle="LivesWithEmployee" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm active" data-toggle="LivesWithEmployee" data-title="true" checked="checked">YES</a>
                        </div>
                        <input type="hidden" name="LivesWithEmployee" id="LivesWithEmployee" value="true">
                    </div>
                }
                else
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="LivesWithEmployeeToggle">
                            <a class="btn btn-success btn-sm active" data-toggle="LivesWithEmployee" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm notActive" data-toggle="LivesWithEmployee" data-title="true">YES</a>
                        </div>
                        <input type="hidden" name="LivesWithEmployee" id="LivesWithEmployee" value="false">
                    </div>
                }

                @*@Html.EditorFor(model => model.LivesWithEmployee, new {htmlAttributes = new {@class = "form-control"}})*@
                @Html.ValidationMessageFor(model => model.LivesWithEmployee, "", new {@class = "text-danger"})
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.OverageStudent, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("OverageStudent", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @if (Model.OverageStudent)
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="OverageStudentToggle">
                            <a class="btn btn-success btn-sm notActive" data-toggle="OverageStudent" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm active" data-toggle="OverageStudent" data-title="true" checked="checked">YES</a>
                        </div>
                        <input type="hidden" name="OverageStudent" id="OverageStudent" value="true">
                    </div>
                }
                else
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="OverageStudentToggle">
                            <a class="btn btn-success btn-sm active" data-toggle="OverageStudent" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm notActive" data-toggle="OverageStudent" data-title="true">YES</a>
                        </div>
                        <input type="hidden" name="OverageStudent" id="OverageStudent" value="false">
                    </div>
                }
                @*@Html.EditorFor(model => model.OverageStudent, new {htmlAttributes = new {@class = "form-control"}})*@
                @Html.ValidationMessageFor(model => model.OverageStudent, "", new {@class = "text-danger"})
            </div>
            @Html.LabelFor(model => model.Disabled, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Disabled", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @if (Model.Disabled)
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="DisabledToggle">
                            <a class="btn btn-success btn-sm notActive" data-toggle="Disabled" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm active" data-toggle="Disabled" data-title="true" checked="checked">YES</a>
                        </div>
                        <input type="hidden" name="Disabled" id="Disabled" value="true">
                    </div>
                }
                else
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="DisabledToggle">
                            <a class="btn btn-success btn-sm active" data-toggle="Disabled" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm notActive" data-toggle="Disabled" data-title="true">YES</a>
                        </div>
                        <input type="hidden" name="Disabled" id="Disabled" value="false">
                    </div>
                }
                @*@Html.EditorFor(model => model.Disabled, new {htmlAttributes = new {@class = "form-control"}})*@
                @Html.ValidationMessageFor(model => model.Disabled, "", new {@class = "text-danger"})
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Smoker, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Smoker", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @if (Model.Smoker)
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="SmokerToggle">
                            <a class="btn btn-success btn-sm notActive" data-toggle="Smoker" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm active" data-toggle="Smoker" data-title="true" checked="checked">YES</a>
                        </div>
                        <input type="hidden" name="Smoker" id="Smoker" value="true">
                    </div>
                }
                else
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="SmokerToggle">
                            <a class="btn btn-success btn-sm active" data-toggle="Smoker" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm notActive" data-toggle="Smoker" data-title="true">YES</a>
                        </div>
                        <input type="hidden" name="Smoker" id="Smoker" value="false">
                    </div>
                }
                @*@Html.EditorFor(model => model.OverageStudent, new {htmlAttributes = new {@class = "form-control"}})*@
                @Html.ValidationMessageFor(model => model.Smoker, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.Wellness, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Wellness", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @if (Model.Wellness)
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="WellnessToggle">
                            <a class="btn btn-success btn-sm notActive" data-toggle="Wellness" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm active" data-toggle="Wellness" data-title="true" checked="checked">YES</a>
                        </div>
                        <input type="hidden" name="Wellness" id="Wellness" value="true">
                    </div>
                }
                else
                {
                    <div class="input-group">
                        <div class="btn-group radioBtn" id="WellnessToggle">
                            <a class="btn btn-success btn-sm active" data-toggle="Wellness" data-title="false">NO</a>
                            <a class="btn btn-success btn-sm notActive" data-toggle="Wellness" data-title="true">YES</a>
                        </div>
                        <input type="hidden" name="Wellness" id="Wellness" value="false">
                    </div>
                }
                @*@Html.EditorFor(model => model.Disabled, new {htmlAttributes = new {@class = "form-control"}})*@
                @Html.ValidationMessageFor(model => model.Wellness, "", new { @class = "text-danger" })
            </div>
        </div>
        <div id="doesntlive" style="display:none">
            <div class="form-group">
                @Html.LabelFor(model => model.Address1, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Address1", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.Address1, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Address1, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Address2, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Address2", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.Address2, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Address2, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.City, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("City", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.City, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.City, "", new { @class = "text-danger" })
                </div>
                <!-- 06/26/2017 DS TFS# 2612-->
                @Html.Label(FieldTranslation.GetLabel("State", GlobalVariables.LanguageID), new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <select class="form-control" name="State" id="State">
                        <option></option>
                        @foreach (Code_Description item in ViewBag.States)
                        {
                            <option value="@item.Code" @((item.Code == Model.State) ? "selected" : string.Empty)>@item.Description</option>
                        }
                    </select>
                    @Html.ValidationMessage("State", new { @class = "text-danger" })
                </div>
                <!-- end TFS# 2612-->
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.Zip, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Zip", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.Zip, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Zip, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Phone1, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Phone1", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.Phone1, new { @class = "form-control phone-type-masking" })
                    @Html.ValidationMessageFor(model => model.Phone1, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.Phone2, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Phone2", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.Phone2, new { @class = "form-control phone-type-masking" })
                    @Html.ValidationMessageFor(model => model.Phone2, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("Email", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.Email, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        

        <div class="form-group">
            <div class="col-md-12">
                <div class="pull-right">
                    <button type="button" class="btn btn-thinkware" data-dismiss="modal">Cancel</button>
                    <input type="submit" value="Save" class="btn btn-thinkware"/>
                </div>
            </div>
        </div>
    </div>
}

<script src="~/Scripts/Kendo.MaskedDatePicker.js"></script>
<script src="~/Scripts/MaskedInput.js"></script>
<script type="text/javascript">
    $(document).ready(function() {
        $('#BirthDate').kendoMaskedDatePicker();
        $(".phone-type-masking").mask("(*************");
        $(".ssn-type").mask("***********", { placeholder: " " });
        $('.ssn-type').attr({ 'type': 'password' });
    });

    $('.radioBtn a').on('click', function() {
        var sel = $(this).data('title');
        var tog = $(this).data('toggle');
        $('#' + tog).prop('value', sel);

        $('a[data-toggle="' + tog + '"]').not('[data-title="' + sel + '"]').removeClass('active').addClass('notActive');
        $('a[data-toggle="' + tog + '"][data-title="' + sel + '"]').removeClass('notActive').addClass('active');
    });
    $('#LivesWithEmployeeToggle a').on('click', function() {
        var sel = $(this).data('title');
        if (sel === true) {
            $('#doesntlive').hide();
        } else {
            $('#doesntlive').show();
        }
    });
    $(document).ready(function() {
        var val = $('#LivesWithEmployee').val();
        if (val === 'false') {
            $('#doesntlive').show();
        }
    });
    $("input[type=submit]").click(function() {
        $("#dependentCreate").submit(function() {
            $('.ssn-type').mask('999999999');
            $(".phone-type-masking").mask("9999999999");
        });
    });
</script>
@if (GlobalVariables.MaskSSN)
{
    <script>
        $('.ssn-type').on('blur', function () {
            $('.ssn-type').attr({ 'type': 'password' });
        });
        $('.ssn-type').on('focus', function () {
            $('.ssn-type').attr({ 'type': 'text' });
            $('.ssn-type').val('');
        });
    </script>
}
else
{
    <script>
        $('.ssn-type').on('blur', function () {
            $('.ssn-type').attr({ 'type': 'password' });
        });
        $('.ssn-type').on('focus', function () {
            $('.ssn-type').attr({ 'type': 'text' });
        });
    </script>
}
