@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch
@model DarwiNet2._0.Data.OpenEnrollmentClientNotification

@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "obprofilenotificationpartial" }))
{
    @*@Html.AntiForgeryToken()*@

    <div class="form-horizontal plain-editor">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.Type, new { htmlAttributes = new { @class = "form-control" } })
        @Html.HiddenFor(model => model.Assigned, new { htmlAttributes = new { @class = "form-control" } })
        @Html.HiddenFor(model => model.CompanyID, new { htmlAttributes = new { @class = "form-control" } })
        @Html.HiddenFor(model => model.ClientID, new { htmlAttributes = new { @class = "form-control" } })
        <div class="form-group">
            @Html.LabelFor(model => model.Enabled, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("Enabled", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @Html.EditorFor(model => model.Enabled, new {htmlAttributes = new {@class = "form-control"}})
                @Html.ValidationMessageFor(model => model.Enabled, "", new {@class = "text-danger"})
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.UseDefault, htmlAttributes: new {@class = "control-label col-md-2"}, labelText: FieldTranslation.GetLabel("UseDefault", GlobalVariables.LanguageID))
            <div class="col-md-4">
                @*<input type="checkbox" id="UseDefault" name="UseDefault" @((Model.UseDefault) ? "checked" : string.Empty)/>*@
                @Html.EditorFor(model => model.UseDefault, new {htmlAttributes = new {@class = "form-control"}})
                @Html.ValidationMessageFor(model => model.UseDefault, "", new {@class = "text-danger"})
            </div>
        </div>
            <div id="defaultValues" style="display: none;" >
                <div class="form-group">
                    <label class="control-label col-md-2">@FieldTranslation.GetLabel("EmailSubject",GlobalVariables.LanguageID)</label>
                    <div class="col-md-4">
                        <input type="text" class="form-control" disabled value="@((ViewBag.defaultNotification!= null) ? ViewBag.defaultNotification.EmailSubject : string.Empty) "/>
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.EmailBody, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EmailBody", GlobalVariables.LanguageID))
                    <div class="col-md-10">
                        @*<textarea class="form-control" rows="10" id="editor">@((ViewBag.defaultNotification != null) ? ViewBag.defaultNotification.EmailBody : string.Empty)</textarea>*@
                        <div class="well">
                            @((ViewBag.defaultNotification != null) ? Html.Raw(ViewBag.defaultNotification.EmailBody) : string.Empty)
                        </div>
                    </div>
                </div>
            </div>
        <div id="nonDefaultValues" style="display: none;">
            <div class="form-group">
                @Html.LabelFor(model => model.EmailSubject, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EmailSubject", GlobalVariables.LanguageID))
                <div class="col-md-4">
                    @Html.TextBoxFor(model => model.EmailSubject, new { @class = "field-required form-control", @maxlength = "100", @required = "required" })
                    @Html.ValidationMessageFor(model => model.EmailSubject, "", new { @class = "text-danger" })

                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.EmailBody, htmlAttributes: new { @class = "control-label col-md-2" }, labelText: FieldTranslation.GetLabel("EmailBody", GlobalVariables.LanguageID))
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.EmailBody, new { @class = "form-control", rows = "10", id = "editor" })
                    @Html.ValidationMessageFor(model => model.EmailBody, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <div class="pull-right">
                    @*@Html.ActionLink("Return to Profiles", "Index", "OBProfile", null, new { @class = "btn btn-thinkware" })*@
                    <input type="submit" value="Save Changes" class="btn btn-thinkware" />
                </div>
            </div>
        </div>
    </div>
}

<script>
    $(document).ready(function () {
        var useDef = $("#UseDefault").is(':checked');
        if (useDef) {
            $("#nonDefaultValues").hide();
            $("#defaultValues").show();
        } else {
            $("#nonDefaultValues").show();
            $("#defaultValues").hide();
        }
        $("#UseDefault").change(function () {
            var isChecked = $("#UseDefault").is(':checked');
            //alert(curVal);
            if (isChecked) {
                    $("#nonDefaultValues").hide();
                    $("#defaultValues").show();
                } else {
                    $("#nonDefaultValues").show();
                    $("#defaultValues").hide();
                }
            });
            
        });
</script>


@section scripts{
    <script>
        $("#obprofilenotificationpartial").validate();
    </script>

}
