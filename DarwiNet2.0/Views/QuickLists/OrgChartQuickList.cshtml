@using DarwiNet2._0.Controllers
@using DarwiNet2._0.DNetSynch

@{
    ViewBag.Title = "Org List Chart - Active Employees";
    ViewBag.ParentCrumb = "Reporting";
    //Breadcrumb Requiredness
    //var profileid = Url.RequestContext.RouteData.Values["id"];
    //var taskid = HttpContext.Current.Request.QueryString["tid"];
    ViewBag.CrumbName = "Quicklist Home";
    ViewBag.Crumb = Url.Action("Index", "QuickLists");
    //Breadcrumb End
}
<script src="~/Scripts/kendo/2015.2.624/jquery.min.js"></script>
<script src="~/Scripts/kendo/2015.2.624/kendo.all.min.js"></script>
<script src="~/Scripts/kendo/2015.2.624/jszip.min.js"></script>
<p style="font-size: 22px;">@FieldTranslation.GetLabel(ViewBag.Title, GlobalVariables.LanguageID)</p>
<div class="colored-line-left"></div>

@*<div class="row">
        <div class="pull-right create-pad">
            <a href="@Url.Action("Index", "QuickLists")" class="btn btn-thinkware">@FieldTranslation.GetLabel("Back to List", GlobalVariables.LanguageID)</a>
        </div>
    </div>*@
<div class="row" style="margin-bottom: 15px;">
    <div class="contacts-toolbar">
        <div class="toolbar">
            <div class="">
                <div class="pull-right create-pad">
                    <a href="@Url.Action("Index", "QuickLists")" class="btn btn-thinkware">@FieldTranslation.GetLabel("Back to List", GlobalVariables.LanguageID)</a>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="text-right">
    <button type="button" class="btn btn-thinkware" title="Export to Excel" onclick="ExportRequests()">Export to Excel</button>
</div>
<div class="table-bottom">
    @(Html.Kendo().Grid<DarwiNet2._0.Data.QuicklistGroup>()
        .Name("grid")
        .Columns(columns =>
        {
            columns.Bound(e => e.GroupDescription).HeaderHtmlAttributes(new { @title = @FieldTranslation.GetLabel("Group", GlobalVariables.LanguageID) }).Title(FieldTranslation.GetLabel("Group", GlobalVariables.LanguageID)).Width(150).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Group", GlobalVariables.LanguageID) });
            columns.Bound(e => e.GroupName).Hidden(true);
        })
        .ColumnMenu()
        .Sortable()
        .Pageable()
            //.ToolBar(tools => tools.Excel())
            //.Excel(e => e.AllPages(true))
            //.Excel(excel => excel
            //    .FileName("Kendo UI Grid Export.xlsx")
            //    .Filterable(true)
            //    .ProxyURL(Url.Action("Excel_Export_Save", "QuickLists"))
            //    )
            .Groupable()
            .Filterable()
            .Scrollable()
            .ClientDetailTemplateId("template")
            .Events(e => e.ExcelExport("parent_excelExport"))
            .HtmlAttributes(new { style = "height: 647px" })
            .DataSource(dataSource => dataSource
                .Ajax()
                .PageSize(15)
                .Read(read => read.Action("GetSupervisors_Read", "QuickLists"))
                )
            .Events(events => events.DataBound("dataBound").DetailExpand("detailExpanded"))
    )
    <script id="template" type="text/kendo-tmpl">
        @(Html.Kendo().Grid<DarwiNet2._0.Data.OrgChartQL>()
            .Name("grid_#=GroupName#") // template expression, to be evaluated in the master context
            .Columns(columns =>
            {
                //columns.Bound(o => o.EmployeeID).Width(120).ClientTemplate("<a href='" + Url.Action("Index", "Employees", new { E = "#: EmployeeID#", M = "Info" }) + "' >#: EmployeeID#</a>").Title(FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID) });
                columns.Bound(o => o.EmployeeID).Title(FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeID", GlobalVariables.LanguageID) });
                columns.Bound(o => o.EmployeeName).Title(FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("EmployeeName", GlobalVariables.LanguageID) });
                columns.Bound(o => o.Division).Title(FieldTranslation.GetLabel("Division", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Division", GlobalVariables.LanguageID) });
                columns.Bound(o => o.Department).Title(FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Department", GlobalVariables.LanguageID) });
                columns.Bound(o => o.SupervisorName).Title(FieldTranslation.GetLabel("Supervisor", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Supervisor", GlobalVariables.LanguageID) });
                columns.Bound(o => o.Position).Title(FieldTranslation.GetLabel("Position", GlobalVariables.LanguageID)).HeaderHtmlAttributes(new { title = FieldTranslation.GetLabel("Position", GlobalVariables.LanguageID) });
            })
            .DataSource(dataSource => dataSource
                .Ajax()
                .PageSize(10)
                .Read(read => read.Action("GetGroupEE_Read", "QuickLists", new { id = "#=GroupName#" }))
                )
                .Events(e => e.ExcelExport("child_excelExport").Change("OnChange"))

                .Pageable()
                .Sortable()
                .Selectable()
                .ToClientTemplate()
        )
    </script>
    <script>
            function dataBound() {
                // this.expandRow(this.tbody.find("tr.k-master-row").first());
            }
            function OnChange(arg) {
                var selected = $.map(this.select(), function (item) {
                    return $(item).text();
                });
                var EmployeeId = this.dataItem(this.select()).EmployeeID;


                var url = '@Url.Action("Index", "Employees", new {M = "Info"})';
                url = url + "&E=" + EmployeeId;
                window.location.href = url;
            }
    </script>
    <script>
    var detailExportPromises = [];
    var dataSource = new kendo.data.DataSource({
        transport: {
            read: {
                url: "@Url.Action("GetGroupEE_Read", "Quicklists")",
                datatype: "json",
                data: { id: "Dummy", AllDetail: true }
            }
        },
        schema: {
            data: "Data",
            total: "Total",
            error: "Errors"
        }
    });

    dataSource.read();

    function dataBound() {
        detailExportPromises = [];

        //this.expandRow(this.tbody.find("tr.k-master-row").first());
    }

    function child_excelExport(e) {
        e.preventDefault();
    }

    function parent_excelExport(e) {
        e.preventDefault();

        var workbook = e.workbook;

        detailExportPromises = [];

        var masterData = e.data;

        for (var rowIndex = 0; rowIndex < masterData.length; rowIndex++) {
            exportChildData(masterData[rowIndex].GroupName, rowIndex);
        }

        // wait for all detail grids to finish exporting
        $.when.apply(null, detailExportPromises)
        .then(function () {
            // get the export results
            var detailExports = $.makeArray(arguments);

            // sort by masterRowIndex
            detailExports.sort(function (a, b) {
                return a.masterRowIndex - b.masterRowIndex;
            });
            for (var c = 0; c < workbook.sheets[0].columns.length; c++) {
                workbook.sheets[0].columns[c].autoWidth = true;
                workbook.sheets[0].columns[c].width = null;
            }
            // add an empty column
            workbook.sheets[0].columns.unshift({ width: 30 });

            // colSpan the Header rows.
            for (var i = 0; i < workbook.sheets[0].rows.length; i++) {
                workbook.sheets[0].rows[i].cells[0].colSpan = 2;
            }
            // prepend an empty cell to each row
            for (var i = 0; i < workbook.sheets[0].rows.length; i++) {
                workbook.sheets[0].rows[i].cells.unshift({});
                for (var j = 0; j < workbook.sheets[0].columns.length; j++) {
                    workbook.sheets[0].rows[i].cells[j].background = "#244061";
                    workbook.sheets[0].rows[i].cells[j].format = "[White]";
                }
                workbook.sheets[0].rows[i].cells[1].colSpan = 2;
            }

            // merge the detail export sheet rows with the master sheet rows
            // loop backwards so the masterRowIndex doesn't need to be updated
            for (var i = detailExports.length - 1; i >= 0; i--) {
                var masterRowIndex = detailExports[i].masterRowIndex + 1;

                var sheet = detailExports[i].sheet;
                for (var c = 0; c < sheet.columns.length; c++) {
                    sheet.columns[c].autoWidth = true;
                    sheet.columns[c].width = null;
                }

                // prepend TWO empty cells to each row
                for (var ci = 0; ci < sheet.rows.length; ci++) {
                    if (sheet.rows[ci].cells[0].value) {
                        sheet.rows[ci].cells.unshift({ width: 15 });
                        sheet.rows[ci].cells.unshift({});
                    }
                }

                // insert the detail sheet rows after the master row
                [].splice.apply(workbook.sheets[0].rows, [masterRowIndex + 1, 0].concat(sheet.rows));
            }

            // save the workbook
            var date = new Date;
            var day = date.getDate();
            var month = date.getMonth() + 1;
            var year = date.getFullYear();
            var name = "@ViewBag.title" + " - " + day + month + year + ".xlsx";
            workbook.sheets[0].filter = null;

            workbook.sheets[0].title = "Employee Group";
            kendo.saveAs({
                dataURI: new kendo.ooxml.Workbook(workbook).toDataURL(),
                fileName: name
            });
        });
    }

    function exportChildData(GroupName, rowIndex) {
        console.log("Excel Export: Group - " + GroupName + ", index - " + rowIndex);
        var deferred = $.Deferred();
        detailExportPromises.push(deferred);

        var rows = [{
            cells: [
                { value: "EmployeeID" },
                { value: "EmployeeName" },
                { value: "Division" },
                { value: "Position" },
                { value: "Department" },
                { value: "SupervisorName" },
            ]
        }];
        dataSource.filter({ field: "Group", operator: "eq", value: GroupName });

        var exporter = new kendo.ExcelExporter({
            columns: [
                { field: "EmployeeID" },
                { field: "EmployeeName" },
                { field: "Division" },
                { field: "Department" },
                { field: "SupervisorName" },
                { field: "Position" }
            ],
            dataSource: dataSource
        });
        console.log("Data Source - " + dataSource.filter + " (" + dataSource.data.length + ")");
        exporter.workbook().then(function (book, data) {
            deferred.resolve({
                masterRowIndex: rowIndex,
                sheet: book.sheets[0]
            });
        });
    }
    </script>
    <script>


        function detailExpanded(e) {
            UpdateFilter(ShowInactive());
        }

        function childGridDataBound(e) {
            //debugger;
            //var filters = null;
            //var datasource = e.sender.dataSource;
            //if (dataSource._filter != null) {
            //    filters = dataSource._filter.filters;
            //}
            //if (ShowInactive()){
            //    if (filters != null){
            //        for (i = 0; i < filters.length; i++) {
            //            if (filters[i].field == "Inactive") {
            //                filters.splice(i, 1);
            //                //removeFilter(filters, "Inactive");
            //                e.sender.dataSource._filter(filters);
            //            }
            //        }
            //    }
            //}
            //else {
            //    if (filters == null) {
            //        var newFilter = { field: "Inactive", operator: "eq", value: false };
            //        filters = [newFilter];
            //        //filters.push(newFilter);
            //        e.sender.dataSource._filter(filters);
            //    }
            //}
        }

        function ShowInactive() {
            var retValue = false;
            var className = $('#chkShowInactive').attr('class');
            if (contains(className, 'glyphicon-unchecked'))
                retValue = false;
            else if (contains(className, 'glyphicon-check'))
                retValue = true;

            return retValue;
        }

        function UpdateFilter(showInActive) {
            var grid = $("#grid").data("kendoGrid");
            var rowCount = grid._data.length;
            for (var rowIndex = 0; rowIndex < rowCount; rowIndex++) {
                filterChildData(grid._data[rowIndex].GroupName, showInActive);
            }
        }

        function contains(value, searchFor) {
            var v = (value || '').toLowerCase();
            var v2 = searchFor;
            if (v2) {
                v2 = v2.toLowerCase();
            }
            return v.indexOf(v2) > -1;
        }

        function filterChildData(GroupName, showInActive) {
            var gridName = 'grid_' + GroupName;
            var grid1 = $("#" + gridName).data("kendoGrid");
            console.log("child data (group " + GroupName + "):" + grid1);
            /*    if (grid1 != null) {
                    var newFilter = { field: "Inactive", operator: "eq", value: false };
                    var dataSource = grid1.dataSource;
                    var filters = null;
                    if (dataSource.filter() != null) {
                        filters = dataSource.filter().filters;
                    }
                    if (!showInActive) {

                        if (filters == null) {
                            filters = [newFilter];
                        }
                        else {
                            var isNew = true;
                            var index = 0;
                            for (index = 0; index < filters.length; index++) {
                                if (filters[index].field == "Inactive") {
                                    isNew = false;
                                    break;
                                }
                            }
                            if (isNew) {
                                filters.push(newFilter);
                            }
                            else {
                                filters[index] = newFilter;
                            }
                        }
                        dataSource.filter(filters);
                    }
                    else {
                        if (filters != null) {
                            for (i = 0; i < filters.length; i++) {
                                if (filters[i].field == "Inactive") {
                                    filters.splice(i, 1);
                                    dataSource.filter(filters);
                                }
                            }
                        }
                    }
                }*/
        }

        function updateSearchFilters(grid, field, operator, value) {
            debugger;
            var newFilter = { field: field, operator: operator, value: value };
            var dataSource = grid.data("kendoGrid").dataSource;
            var filters = null;
            if (dataSource.filter() != null) {
                filters = dataSource.filter().filters;
            }
            if (filters == null) {
                filters = [newFilter];
            }
            else {
                var isNew = true;
                var index = 0;
                for (index = 0; index < filters.length; index++) {
                    if (filters[index].field == field) {
                        isNew = false;
                        break;
                    }
                }
                if (isNew) {
                    filters.push(newFilter);
                }
                else {
                    filters[index] = newFilter;
                }
            }
            dataSource.filter(filters);
        }

        function removeFilter(filter, searchFor) {
            if (filter == null)
                return [];
            for (var x = 0; x < filter.length; x++) {

                if (filter[x].filters != null && filter[x].filters.length >= 0) {

                    if (filter[x].filters.length == 0) {

                        filter = filter.splice(x, 1);
                    }
                    filter[x].filters = removeFilter(filter[x].filters, searchFor);
                }
                else {
                    if (filter[x].field == searchFor) {
                        filter = filter.splice(x, 1);
                    }
                }
            }
            return filter;
        }

    </script>
    @using (Html.BeginForm("ExportFileOrg", "QuickLists", FormMethod.Post, new { name = "exportFileForm", id = "exportFileForm" }))
    {
        <input type="hidden" id="filePath" name="filePath" />
    }
    @*DG -DNET-276-10/17/2022-START *@
    <script>
    function ExportRequests() {
        $('#loadingSpinner').show(100);
        $.ajax({
            traditional: true,
            url: "@Url.Action("ExportRequestsOrgQuickList", "QuickLists")",
            type: "POST",
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data) {
                    $('#filePath').val(data);
                    $("#exportFileForm").submit();
                }
                $('#loadingSpinner').hide(250);
            },
            failure: function (response) {
                $('#loadingSpinner').hide(250);
                alert(response.responseText);
            },
            error: function (errorData) {
                $('#loadingSpinner').hide(250);
                $(".alert-danger").show();
                $(".alert-danger").delay(2750).fadeOut(250);
            }
        });
    }

    </script>
    @*DG - DNET-276-10/17/2022-END*@
</div>
