/*
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[FK_PayrollApprovals_PayrollApprovalSetupSequences]') AND type in (N'F'))
ALTER TABLE PayrollApprovals DROP CONSTRAINT FK_PayrollApprovals_PayrollApprovalSetupSequences

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PayrollApprovals]') AND type in (N'U'))
	DROP TABLE PayrollApprovals
*/

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PayrollApprovals]') AND type in (N'U'))

	CREATE TABLE dbo.PayrollApprovals
	(
		PayrollNumber					NVARCHAR(255)		NOT NULL,
		PayrollApprovalSetupSequenceID	INT					NOT NULL,
		Approved						BIT					NOT NULL DEFAULT 0,
		Rejected						BIT					NOT NULL DEFAULT 0,
		RejectedMessage					NVARCHAR(MAX),

		CONSTRAINT PK_PayrollApprovals PRIMARY KEY CLUSTERED (PayrollNumber, PayrollApprovalSetupSequenceID),
		--CONSTRAINT FK_PayrollApprovals_ClientPayrollSchedules FOREIGN KEY (PayrollNumber) REFERENCES ClientPayrollSchedules(PayrollNumber),
		CONSTRAINT FK_PayrollApprovals_PayrollApprovalSetupSequences FOREIGN KEY (PayrollApprovalSetupSequenceID) REFERENCES PayrollApprovalSetupSequences(PayrollApprovalSetupSequenceID)
	)
	GO