<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301880
-->
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="DarwiNet2._0.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <section name="bugsnag" type="Bugsnag.ConfigurationSection.Configuration, Bugsnag.ConfigurationSection" />
    <section name="kronos" type="System.Configuration.NameValueSectionHandler"/>
    <section name="transamerica" type="System.Configuration.NameValueSectionHandler"/>
    <section name="featureFlags" type="DarwiNet2._0.Configuration.FeatureFlagsSection, DarwiNet2.0"/>
    <section name="mobilesdk" type="System.Configuration.NameValueSectionHandler" />
    <section name="twilio" type="System.Configuration.NameValueSectionHandler" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <!-- 05/26/21 disabled bugsnag notification in development environment, add "development" to string array in "notifyReleaseStages" to enable temporarily for debugging purposes, otherwise leave disabled to reduce unnecessary quota usage -->
  <!-- <bugsnag apiKey="9a0417831ee8883bb0adb3fb792b7126" releaseStage="development" notifyReleaseStages="development,testing,production" /> -->
  <bugsnag apiKey="9a0417831ee8883bb0adb3fb792b7126" releaseStage="development" appVersion="2.3" notifyReleaseStages="testing,production" />
  <!--BugSnag: https://docs.bugsnag.com/platforms/dotnet/mvc/configuration-options/ -->
  <!--<bugsnag apiKey="7a6e6862ae443cbcac714531d1fbfbbf" appVersion="1.5.5" releaseStage="production" notifyReleaseStages="development,production">
    <assemblyQualifiedIgnoreClasses>
      <class name="System.Web.HttpException" />-->
  <!--<class name="Microsoft.Reporting.WebForms.AspNetSessionExpiredException" /> TODO: prevent these from reporting in BugSnag-->
  <!--</assemblyQualifiedIgnoreClasses>
  </bugsnag>-->
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="clientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add value="::1" key="AuthorizeIPAddresses" />
    <!--Dnet Test URLs-->
    <add key="DnetTestEnvironmentUrl" value="http://localhost:50833/" />
    <add key="DnetLocalHostUrl" value="http://localhost:50833/" />
    <add key="DnetD15Url" value="https://d15.darwinet.com" />
    <!--Billing API-->
    <add key="BillingAPIUrl" value="https://billingapi.darwinet.com" />
    <!-- Mailer Service API URL -->
    <add key="MailerServiceUrl" value="https://localhost:5001/" />
    <!--Triad SSRS Info-->
	<add key="CustomSsrsUserName" value="COH_TEST_svc" />
    <add key="CustomSsrsPassword" value="tlSLQUcC12n3!" />
    <add key="CustomSsrsDomain" value="EFLEX" />
    <add key="CustomSsrsUrl" value="http://efx-coh-d-db02/ReportServer" />
	<add key="CustomReportsPath" value="/Custom Reports"/>
    <!--Multi Web Servers-->
    <add key="WebServer" value="" />
    <!--Izenda Settings-->
    <add key="IzendaKey" value="T370 Thinkware +DASH +MAPS +LANG +FORMS +VISION +FUSION|Enterprise|6.8|08/03/2024|0|10|10|M|YEX1G2F" />
    <!--Twilio Info-->
    <add key="TwilioReceivingNumber" value="5136574221" />
	  <add key="ElectionLeftDays" value ="25"></add>
	  <!--Time Web Services (NetTime) Info-->
    <add key="TimeWebServicesCustomerAlias" value="Thinkware" />
    <add key="TimeWebServicesSharedKey" value="c1069eab-f595-47e1-bfc2-779fc6a49ac1" />
    <add key="TimeWebServicesUsername" value="wsuser" />
    <add key="TimeWebServicesPassword" value="wsuser" />
    <!--PrimeTime Info-->
    <add key="PrimeTimeUsername" value="Vineet" />
    <add key="PrimeTimePassword" value="Demo1!" />
    <add key="PrimeTimeCompanyCode" value="PrimeT" />
    <!--I-9 instruction Url-->
    <add key="I9InstrUrl" value="https://www.uscis.gov/sites/default/files/document/forms/i-9instr.pdf" />
    <!--Use TimeSheet Log file: value - true or false (empty is false too-->
    <add key="TimeSheetLog" value="false" />
    <add key="DBValidationLog" value="true" />
    <!-- JSPrintManager License Info -->
    <add key="JSPM_owner" value="Thinkware inc. - 1 WebApp Lic - 1 WebServer Lic - (Basic Edition)" />
    <add key="JSPM_key" value="7A86D59479451F4B1FC5C72F34FED0B2BDDFFD75" />
    <!-- API Information -->
    <add key="JWTSecret" value="thisisasecretthisisasecretthisisasecretthisisasecret" />
    <add key="JWTExperationInMinutes" value="44640" />
    <add key="JWTIssuer" value="DNET" />
    <add key="Microsoft.VisualStudio.Enterprise.AspNetHelper.VsInstrLocation" value="c:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\Team Tools\Performance Tools\vsinstr.exe" />
    <add key="owin:AutomaticAppStartup" value="true" />
    <add key="PayrollApprovalProcessorToken" value="this_is_a_test_token_123" />
    <!-- RabbitMQ-->
    <add key="QueueConfig" value="amqp://cohesion:cohesion@localhost:5672/TW" />
    <add key="QueueConnectionName" value="Pay360Web" />
    <add key="QueueName" value="Pay360Web" />
    <add key="CustomerId" value="QA1" />
	<add key="ConsumerTimeout" value="60000"/>
    <add key="DefaultSystemUser" value="System" />
    <add key="DBValidationLog" value="false" />
    <add key="aspnet:MaxJsonDeserializerMembers" value="150000" />
    <!-- Dev Env uses the development version of Vue -->
    <add key="Env" value="Dev" />
    <!-- Change value of TimezoneID to match server TZ -->
    <add key="TimezoneID" value="America/New_York" />
    <!-- Timeco API Endpoint -->
    <add key="TimecoAPIEndpoint" value="https://vendorservice.sandbox.timeco.com/Timeco.Integration.Vendor.Service/VendorIntegrationService/json/" />
    <add key="TimecoSSOURL" value="https://sandbox.timeco.com/auth/login.aspx" />
    <add key="UseTwilioVerify" value="true" />
    <!--Trigger for enabling Add Employee button on List page-->
    <add key="ShowAddEEButton" value="false" />
    <!-- Error monitoring system. Possible values: BugSnag, ApplicationInsights. If nothing is set BugSnag is default -->
    <add key="LoggerService" value="ApplicationInsights" />
    <add key ="EnableIpRestriction" value="false"/>
	<!-- Error monitoring system. Possible values: BugSnag, ApplicationInsights. If nothing is set BugSnag is default -->
	<add key="LoggerService" value="ApplicationInsights" />
	<add key="HireologyBaseUrl" value="https://api.hireology.com/" />
    <!--For Users Lock Functionality -->
    <add key="MaxUserAttempts" value="5"/>
    <!--User Lock Time in Minutes-->
    <add key="UserLockTime" value="30"/>
  </appSettings>
  
    <!-- Kronos configuration -->
  <kronos>
    <add key="BaseUri" value=""/>
    <add key="ApiKey" value=""/>
    <add key="UserName" value=""/>
    <add key="Password" value=""/>
    <add key="Company" value=""/>
    <add key="UseConfigFile" value="false"/>
    <add key="SingleSignOnUri" value=""/>
    <add key="SingleLogoutServiceUri" value=""/>
  </kronos>
  
  <!-- TransAmerica configuration -->
  <transamerica>
    <add key="Url" value=""/>
    <add key="AudienceUrl" value=""/>
    <add key="SingleLogOutServiceUrl" value="https://hris.employerflexibility.com/" />
  </transamerica>

  <!--Feature Flags-->
  <featureFlags>

    <!--Use this format when you want to override ([OverrideWithFeature("Feature1")]) an existing feature w/ a different route, ex PlansIndex for open enrollment-->
    <add name="Feature1" enabled="true" route="/Some/Route" />
    <add name="Benefits" enabled="true" route="/OpenEnrollment/Benefits" />

    <!--Use this format when you want to flag ([FeatureFlag("Feature2")]) a completely new feature-->
    <add name="Feature2" enabled="true"/>

  </featureFlags>

  <!-- mobile configuration -->
  <mobilesdk>
    <add key="BaseUri" value="https://devcohesionapi.employerflexible.com/" />
    <add key="ApiClient" value="MYHR20" />
    <add key="ApiClientSecret" value="LJpQ2Nm2X0uIxlmWkZe3MQ9vUFa5KwCb5Wz3HqX9lTU=" />
  </mobilesdk>
  
  <!-- default twilio configuration -->
  <twilio>
    <add key="UseAsFallback" value="true" />
    <add key="EnableOnNonProd" value="true" />
    <add key="VerifyServiceSid" value="" />
    <add key="AccountSid" value="" />
    <add key="AuthToken" value="" />
  </twilio>
  
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5.1" />
      </system.Web>
  -->
  <system.web>
    <customErrors mode="Off" defaultRedirect="~/Error">
      <error statusCode="404" redirect="~/Error/NotFound" />
    </customErrors>
    <compilation targetFramework="4.7.2" debug="true">
      <assemblies>
        <add assembly="Microsoft.ReportViewer.WebForms, Version=1*******, culture=neutral, PublicKeyToken=89845DcD8080cc91" />
        <add assembly="Microsoft.ReportViewer.common, Version=1*******, culture=neutral, PublicKeyToken=89845DcD8080cc91" />
        <add assembly="Microsoft.Build.Framework, Version=*******, culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      </assemblies>
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=1*******, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
      </buildProviders>
    </compilation>
    <httpRuntime targetFramework="4.5.1" executionTimeout="9999" maxRequestLength="2097151" requestPathInvalidCharacters="&lt;,&gt;,*,%,&amp;,\,?" />
    <pages enableSessionState="true">
      <namespaces>
        <add namespace="Kendo.Mvc.UI" />
      </namespaces>
    </pages>
    <httpHandlers>
      <add path="Reserved.ReportViewerWebcontrol.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=1*******, Culture=neutral, PublicKeyToken=89845dcd8080cc91" validate="false" />
      <add verb="*" path="wcp.axd" type="Neodynamic.SDK.Web.WebClientPrint, Neodynamic.SDK.WebClientPrint" />
    </httpHandlers>
    <sessionState mode="InProc" timeout="480" />
    <httpModules>
      <add name="Bugsnag" type="Bugsnag.AspNet.HttpModule, Bugsnag.AspNet" />
    </httpModules>
  </system.web>
  <runtime>
    <AppContextSwitchOverrides value="Switch.System.Net.DontEnableSystemDefaultTlsVersions=false" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.ReportAppServer.DataDefModel" publicKeyToken="692FBEA5521E1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.ReportAppServer.Controllers" publicKeyToken="692FBEA5521E1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="itextsharp" publicKeyToken="8354ae6d2174ddca" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.1.2.0" newVersion="5.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.8.0" newVersion="2.0.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.VisualStudio.Enterprise.AspNetHelper" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <codeBase version="15.0.0.0" href="file:///c:/Program%20Files%20(x86)/Microsoft%20Visual%20Studio/Shared/common/VSPerfcollectionTools/Microsoft.VisualStudio.Enterprise.AspNetHelper.DLL" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="VsWebSite.Interop" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <codeBase version="8.0.0.0" href="file:///C:/Program%20Files%20(x86)/Microsoft%20Visual%20Studio/Shared/Common/VSPerfCollectionTools/VsWebSite.Interop.DLL" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Autofac" publicKeyToken="17863af14b0044da" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.9.4.0" newVersion="4.9.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="DocumentFormat.OpenXml" publicKeyToken="8fb06cb64d019a17" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.7.2.0" newVersion="2.7.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="*******-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AspNet.SignalR.Core" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.4.3.0" newVersion="2.4.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.9.0" newVersion="5.2.9.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.Shared" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.ReportSource" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.CrystalReports.Engine" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.Shared" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.ReportSource" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="CrystalDecisions.CrystalReports.Engine" publicKeyToken="692fbea5521e1304" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.4000.0" newVersion="13.0.4000.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.5" newVersion="8.0.0.5" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.1.0" newVersion="4.2.1.0" />
      </dependentAssembly>
    </assemblyBinding>
    <gcServer enabled="true" />
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="CyclicTextWriter" />
      </listeners>
    </trace>
    <sources>
      <source name="ComponentSpace.SAML2" switchValue="Verbose">
        <listeners>
          <add name="CyclicTextWriter" />
        </listeners>
      </source>
    </sources>
    <sharedListeners>
      <!-- Ensure IIS has create/write file permissions for the log folder. -->
      <add name="CyclicTextWriter" type="ComponentSpace.SAML2.Utility.CyclicTraceListener,ComponentSpace.SAML2" initializeData="logs" />
    </sharedListeners>
  </system.diagnostics>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="ScriptModule" />
      <remove name="UrlRoutingModule" />
      <add name="ScriptModule" preCondition="managedHandler" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="UrlRoutingModule" type="System.Web.Routing.UrlRoutingModule, System.Web.Routing, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    </modules>
    <handlers>
      <remove name="WebServiceHandlerFactory-Integrated" />
      <remove name="ScriptHandlerFactory" />
      <remove name="ScriptHandlerFactoryAppServices" />
      <remove name="ScriptResource" />
      <remove name="MvcHttpHandler" />
      <remove name="UrlRoutingHandler" />
      <remove name="ReportViewerWebcontrolHandler" />
      <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="MvcHttpHandler" preCondition="integratedMode" verb="*" path="*.mvc" type="System.Web.Mvc.MvcHttpHandler, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      <add name="UrlRoutingHandler" preCondition="integratedMode" verb="*" path="UrlRouting.axd" type="System.Web.HttpForbiddenHandler, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
      <add name="ReportViewerWebcontrolHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebcontrol.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=1*******, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <staticContent>
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
    </staticContent>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="2147483644" />
      </requestFiltering>
    </security>
    <validation validateIntegratedModeConfiguration="false" />
  </system.webServer>
  <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="2147483644" />
      </webServices>
    </scripting>
  </system.web.extensions>
  <applicationSettings>
    <DarwiNet2._0.Properties.Settings>
      <setting name="DarwiNet2_0_TriadReportService_ReportingService2010" serializeAs="String">
        <value>http://efx-coh-d-db02/ReportServer/ReportService2010.asmx</value>
      </setting>
    </DarwiNet2._0.Properties.Settings>
  </applicationSettings>
  <system.codedom>
    <compilers>
      <compiler extension=".cs" language="c#;cs;csharp" warningLevel="4" compilerOptions="/langversion:7.3 /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
      <compiler extension=".vb" language="vb;vbs;visualbasic;vbscript" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
    </compilers>
  </system.codedom>
  <connectionStrings>
<add name="DnetEntities" connectionString="metadata=res://*/Data.DnetEntities.csdl|res://*/Data.DnetEntities.ssdl|res://*/Data.DnetEntities.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=EFX-COH-D-DB02;initial catalog=COH_New;persist security info=True;user id=COH_DEV1_svc;password=*************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />  
  </connectionStrings>
</configuration>