/* Copyright (c) 2005-2013 <PERSON>zenda, L.L.C. - ALL RIGHTS RESERVED */

table.ReportTable
{	
	border-style:none;
	border-width:0;
	border-collapse:collapse;
	padding-left:4;
	padding-right:4;
	padding-top:1;
	padding-bottom:1;
}
table.ReportTable td
{
	font-family: tahoma; 
	border-width:0px;
	border-style:solid;
}
table.ReportTable td.EmptyCell
{
	border-style:none !important;
	background:none !important;
	filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
	background-color:white !important;
	outline:none !important;
}
table.ReportTable td.GridCell
{
	vertical-align:top;
	font-size: 8pt;
	border-width:1px;
	border-style:solid;
}
table.ReportTable td.GridCellAGTotal
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	font-weight:bold;
	font-style:italic;
	border-color: White;
	border-bottom: none;
	border-right: none;
}
table.ReportTable td.GridCellAG
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	font-style:italic;
	border-color: White;
	border-right: none;
	border-bottom: none;
}
table.ReportTable td.EmptyCellAG
{
	border-left: 1px solid White;
	border-top: none;
	border-bottom: none;
}
table.ReportTable td.EmptyCellAG-Line
{
	border-left: none;
	border-right: none;
	border-top: 1px solid white;
}

table.ReportTable td.ValueCellAG
{
	border-bottom: 1px solid Gainsboro;
}
table.ReportTable tr.ReportItemAGExpanded > td.ValueCellAG
{
    display: none;
}
table.ReportTable tr.ReportItemAGExpanded > td.EmptyCellAG
{
    display: table-cell;
}
table.ReportTable tr.ReportItemAGCollapsed > td.ValueCellAG
{
    display: table-cell;
}
table.ReportTable tr.ReportItemAGCollapsed > td.EmptyCellAG
{
    display: none;
}
table.ReportTable td.GridCellAGLvl0
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	text-align:left;
	font-weight:bold;
	border-color: White;
	border-right: none;
	border-bottom: none;
}
table.ReportTable td.GridCellHG0
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	/*text-align:left;*/
	font-style:italic;
	font-weight:bold;
}
table.ReportTable td.GridCellHG1
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	/*text-align:left;*/
	font-style:normal;
	font-weight:bold;
}
table.ReportTable td.GridCellHG2
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	/*text-align:left;*/
	font-style:italic;
	font-weight:normal;
}
table.ReportTable td.GridCellHG3
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	/*text-align:left;*/
	font-style:normal;
	font-weight:normal;
}

tr.ReportHeader td
{
	font-size: 9pt;
	font-weight:normal; 
	border-width:1px;
	border-style:solid;
	border-bottom: solid 1px #AFAFAF!important;
}
tr.ReportFooter, tr.ReportFooter td
{
	border-top-color: gainsboro !important;
	border-top-width: 1px !important;
	color: Black;
}

tr.ReportItem td
{
	vertical-align:top;
	font-size: 8pt;
	border-width:1px;
	border-style:solid;
}
tr.ReportItemAGTotal td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-weight:bold;
	font-style:italic;
}
tr.ReportItemAG td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:italic;
}
tr.ReportItemAGLvl0 td,
tr.ReportItemAGLocalTotal td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	border-top-width:2px;
	font-weight:bold;
}
tr.ReportItemHG0 td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:italic;
	font-weight:bold;
}
tr.ReportItemHG1 td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:normal;
	font-weight:bold;
}
tr.ReportItemHG2 td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:italic;
	font-weight:normal;
}
tr.ReportItemHG3 td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:normal;
	font-weight:normal;
}
tr.AlternatingItem td
{
	vertical-align:top;
	font-size: 8pt;
	border-width:1px;
	border-style:solid;	
}
tr.AlternatingItemAGTotal td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-weight:bold;
	font-style:italic;
}
tr.AlternatingItemAG td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:italic;
}
tr.AlternatingItemAGLvl0 td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;	
	border-top-width:2px;	
	font-weight:bold;
}
tr.AlternatingItemHG1 td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:normal;
	font-weight:bold;
}
tr.AlternatingItemHG0 td
{
	vertical-align:top;
	font-size: 10pt;
	border-width:1px;
	border-style:solid;
	border-left:none;
	border-right:none;
	font-style:normal;
	font-weight:normal;
}
tr.ReportFooter td
{
	vertical-align:top;
	font-size: 8pt;
	font-weight:bold; 
	border-width:1px;
	border-style:solid;
	border-top-style: double;
}
span.ReportTitle
{
	font-size: 18pt;
	font-weight: normal;
	white-space: pre-wrap;
	background-color: #F0F0F0;
}
/*span.Header
{
	white-space: nowrap;
}
span.Description
{
	white-space: nowrap;
}
span.Footer
{
	white-space: nowrap;
}*/

tr.VisualGroup
{
	border-width: 0px;
	border-style: none;
	border: none;
	cursor: pointer;
	height: 30px;
	vertical-align: bottom;
}

tr.VisualGroup tr
{
	border-width:3px;
	cursor:pointer;
	border-style:solid;
}

tr.VisualGroupCollapsed
{
	border-width: 0px;
	border-style: none;
	border: none;
	cursor: pointer;
	height: 30px;
	vertical-align: bottom;
	border-top: 1px solid #D4D4D4;
}

table.ReportTable:not(.fvTable) tr.VisualGroup td
{
	text-align:left;
	padding-top:10px;
	border-right-style:solid;
	border-right-width:0px;
	padding-left: 16px;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAIAAAD9iXMrAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAESSURBVChTfY9NTgJBEIXnFMbgTNh6BpZuPYOJF+oeXBoSNqwUt5zAuHIHTA/EmAkLFkBQE38iMF3Fq+pRMCG+9HRedX31pjvq9t9O7Cg2WZKOazaDQZlYd2TzOHV1MzxrF8XyK2Lim8ESRzUjDVkmi20fJmm6885z7+nDM0XErOgLRmOTh2wsZF90p9ls9V16IOA8e4GRepyOQAuausb1+L54RwSjTeBIPJKx3Q5fwwUu76YPk08PKSd5+NRJgcnW4+L0KnfzlQAQ8VodOMkqq0NIysqEC0FUhrzQEInBV6FqqcQOboM5zdv8EHtJKiKK9AVyHhoY0j/+qhr4+w7RPrSTvAOxocBV/uFESkCHIWbeAkF+vuxobxzWAAAAAElFTkSuQmCC);
	background-repeat: no-repeat;
	background-position: 0px 15px;
	padding-bottom: 4px;
}

table.ReportTable:not(.fvTable)  tr.VisualGroupCollapsed td {
	padding-left: 16px;
	background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAIAAAD9iXMrAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEESURBVChTfZA9bsJAEIXnGIhABAV34AIpcogoJ/KilKSgoAqhzgFSpEsDjo0RkUCUIFkoUX4UszN5OwOOFMk82bOz+56/3TW95t8sIuzZY6gUXQwWD/P3wu8lxCtFrWh6OVwmm09MkNTwH/k49dR0adNlV6PV4/IjbO1hAX6whbVlCbkzlzWipNtfPK1yXRZmhheqhpj3dO6SWi+tu6TRS6/H69nmi+XHsCZrqB7NAAMSDQ7QuclggKS2HRS7C7VdDBKi7SgF+/Z5Gwj2HKQ57Ggw1PHkDctloAwz7msw3Ob+JbdPUW1QhRYvAYPQKEbohPT/IaSUkvFfcOgu3mlfaK2QFL9Y878B1uRkcwAAAABJRU5ErkJggg==);
	background-repeat: no-repeat;
	background-position: 0px 12px;
	padding-bottom: 4px;
}

div.autoWide 
{
	display:inline-table;
}

.funnel-border {
    background-image: -webkit-gradient(linear,left top,right top,color-stop(0%,rgba(255,255,255,0)),color-stop(100%,rgba(255,255,255,1)));
    background-image: -webkit-linear-gradient(left,rgba(255,255,255,0),rgba(255,255,255,1));
    background-image: -moz-linear-gradient(left,rgba(255,255,255,0),rgba(255,255,255,1));
    background-image: -ms-linear-gradient(left,rgba(255,255,255,0),rgba(255,255,255,1));
    background-image: -o-linear-gradient(left,rgba(255,255,255,0),rgba(255,255,255,1));
    background: linear-gradient(to right,rgba(255,255,255,0),rgba(255,255,255,1));
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=1,StartColorStr='#00ffffff',EndColorStr='#ffffff');
}