/*
 * jQuery UI CSS Framework
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 */

/* Layout helpers
----------------------------------*/
.iz-ui-helper-hidden { display: none; }
.iz-ui-helper-hidden-accessible { position: absolute !important; clip: rect(1px 1px 1px 1px); clip: rect(1px,1px,1px,1px); }
.iz-ui-helper-reset { margin: 0; padding: 0; border: 0; outline: 0; line-height: 1.3; text-decoration: none; font-size: 100%; list-style: none; }
.iz-ui-helper-clearfix:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; }
.iz-ui-helper-clearfix { display: inline-block; }
/* required comment for clearfix to work in Opera \*/
* html .iz-ui-helper-clearfix { height:1%; }
.iz-ui-helper-clearfix { display:block; }
/* end clearfix */
.iz-ui-helper-zfix { width: 100%; height: 100%; top: 0; left: 0; position: absolute; opacity: 0; filter:Alpha(Opacity=0); }


/* Interaction Cues
----------------------------------*/
.iz-ui-state-disabled { cursor: default !important; }


/* Icons
----------------------------------*/

/* states and images */
.iz-ui-icon { display: block; text-indent: -99999px; overflow: hidden; background-repeat: no-repeat; }


/* Misc visuals
----------------------------------*/

/* Overlays */
.iz-ui-widget-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }


/*
 * jQuery UI CSS Framework 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Theming/API
 *
 * To view and modify this theme, visit http://jquit.com/builder/#gcc=ffffff&gcb=d4d4d4&gct=525252&gci=d4d4d4&pcc=d4d4d4&acb=525252&o=ffffff&ghc=3399ff&ght=ffffff&ghi=ffffff&gdc=d4d4d4&gdb=d4d4d4&gdt=525252&tdc=3399ff&tdb=3399ff&tdt=ffffff&tdi=ffffff&ddc=d4d4d4&ddb=d4d4d4&ddt=525252&pxd=000000&asc=3399ff&ast=ffffff&goc=66B3FF&gob=66B3FF&got=ffffff&toc=66B3FF&tob=66B3FF&tot=ffffff&toi=ffffff&doc=66B3FF&dob=66B3FF&dot=ffffff&pxa=525252&gac=3399ff&gab=3399ff&gat=ffffff&tac=ffffff&tab=d4d4d4&tat=525252&tai=d4d4d4&dac=3399ff&dab=3399ff&dat=ffffff&dic=d4d4d4&dib=3399ff&dit=ffffff&gic=efdca9&gib=efdca9&git=525252&gii=525252&ec=c31d1d&eb=c31d1d&et=ffffff&ei=ffffff
 */


/* Component containers
----------------------------------*/
.iz-ui-widget { font-family: Helvetica, Arial, sans-serif; font-size: 1.1em; }
.iz-ui-widget .iz-ui-widget { font-size: 1em; }
.iz-ui-widget input, .iz-ui-widget select, .iz-ui-widget textarea, .iz-ui-widget button { font-family: Helvetica, Arial, sans-serif; font-size: 1em; }
.iz-ui-widget-content { border: 1px solid #dddddd; background: #ffffff; color: #444444; }
.iz-ui-widget-content a { color: #444444; }
.iz-ui-widget-header { border: 1px solid #dddddd; background: #dddddd; color: #444444; font-weight: bold; }
.iz-ui-widget-header a { color: #444444; }

/* Interaction Cues
----------------------------------*/
.iz-ui-state-highlight, .iz-ui-widget-content .iz-ui-state-highlight, .iz-ui-widget-header .iz-ui-state-highlight  {border: 1px solid #cccccc; background: #ffffff; color: #444444; }
.iz-ui-state-highlight a, .iz-ui-widget-content .iz-ui-state-highlight a,.iz-ui-widget-header .iz-ui-state-highlight a { color: #444444; }
.iz-ui-state-error, .iz-ui-widget-content .iz-ui-state-error, .iz-ui-widget-header .iz-ui-state-error {border: 1px solid #ff0084; background: #ffffff; color: #222222; }
.iz-ui-state-error a, .iz-ui-widget-content .iz-ui-state-error a, .iz-ui-widget-header .iz-ui-state-error a { color: #222222; }
.iz-ui-state-error-text, .iz-ui-widget-content .iz-ui-state-error-text, .iz-ui-widget-header .iz-ui-state-error-text { color: #222222; }
.iz-ui-priority-primary, .iz-ui-widget-content .iz-ui-priority-primary, .iz-ui-widget-header .iz-ui-priority-primary { font-weight: normal; }
.iz-ui-priority-secondary, .iz-ui-widget-content .iz-ui-priority-secondary,  .iz-ui-widget-header .iz-ui-priority-secondary { opacity: .7; filter:Alpha(Opacity=70); font-weight: normal; }
.iz-ui-state-disabled, .iz-ui-widget-content .iz-ui-state-disabled, .iz-ui-widget-header .iz-ui-state-disabled { opacity: .35; filter:Alpha(Opacity=35); background-image: none; }

/* positioning */
.iz-ui-icon-carat-1-n { background-position: 0 0; }
.iz-ui-icon-carat-1-ne { background-position: -16px 0; }
.iz-ui-icon-carat-1-e { background-position: -32px 0; }
.iz-ui-icon-carat-1-se { background-position: -48px 0; }
.iz-ui-icon-carat-1-s { background-position: -64px 0; }
.iz-ui-icon-carat-1-sw { background-position: -80px 0; }
.iz-ui-icon-carat-1-w { background-position: -96px 0; }
.iz-ui-icon-carat-1-nw { background-position: -112px 0; }
.iz-ui-icon-carat-2-n-s { background-position: -128px 0; }
.iz-ui-icon-carat-2-e-w { background-position: -144px 0; }
.iz-ui-icon-triangle-1-n { background-position: 0 -16px; }
.iz-ui-icon-triangle-1-ne { background-position: -16px -16px; }
.iz-ui-icon-triangle-1-e { background-position: -32px -16px; }
.iz-ui-icon-triangle-1-se { background-position: -48px -16px; }
.iz-ui-icon-triangle-1-s { background-position: -64px -16px; }
.iz-ui-icon-triangle-1-sw { background-position: -80px -16px; }
.iz-ui-icon-triangle-1-w { background-position: -96px -16px; }
.iz-ui-icon-triangle-1-nw { background-position: -112px -16px; }
.iz-ui-icon-triangle-2-n-s { background-position: -128px -16px; }
.iz-ui-icon-triangle-2-e-w { background-position: -144px -16px; }
.iz-ui-icon-arrow-1-n { background-position: 0 -32px; }
.iz-ui-icon-arrow-1-ne { background-position: -16px -32px; }
.iz-ui-icon-arrow-1-e { background-position: -32px -32px; }
.iz-ui-icon-arrow-1-se { background-position: -48px -32px; }
.iz-ui-icon-arrow-1-s { background-position: -64px -32px; }
.iz-ui-icon-arrow-1-sw { background-position: -80px -32px; }
.iz-ui-icon-arrow-1-w { background-position: -96px -32px; }
.iz-ui-icon-arrow-1-nw { background-position: -112px -32px; }
.iz-ui-icon-arrow-2-n-s { background-position: -128px -32px; }
.iz-ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }
.iz-ui-icon-arrow-2-e-w { background-position: -160px -32px; }
.iz-ui-icon-arrow-2-se-nw { background-position: -176px -32px; }
.iz-ui-icon-arrowstop-1-n { background-position: -192px -32px; }
.iz-ui-icon-arrowstop-1-e { background-position: -208px -32px; }
.iz-ui-icon-arrowstop-1-s { background-position: -224px -32px; }
.iz-ui-icon-arrowstop-1-w { background-position: -240px -32px; }
.iz-ui-icon-arrowthick-1-n { background-position: 0 -48px; }
.iz-ui-icon-arrowthick-1-ne { background-position: -16px -48px; }
.iz-ui-icon-arrowthick-1-e { background-position: -32px -48px; }
.iz-ui-icon-arrowthick-1-se { background-position: -48px -48px; }
.iz-ui-icon-arrowthick-1-s { background-position: -64px -48px; }
.iz-ui-icon-arrowthick-1-sw { background-position: -80px -48px; }
.iz-ui-icon-arrowthick-1-w { background-position: -96px -48px; }
.iz-ui-icon-arrowthick-1-nw { background-position: -112px -48px; }
.iz-ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }
.iz-ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }
.iz-ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }
.iz-ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }
.iz-ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }
.iz-ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }
.iz-ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }
.iz-ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }
.iz-ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }
.iz-ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }
.iz-ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }
.iz-ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }
.iz-ui-icon-arrowreturn-1-w { background-position: -64px -64px; }
.iz-ui-icon-arrowreturn-1-n { background-position: -80px -64px; }
.iz-ui-icon-arrowreturn-1-e { background-position: -96px -64px; }
.iz-ui-icon-arrowreturn-1-s { background-position: -112px -64px; }
.iz-ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }
.iz-ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }
.iz-ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }
.iz-ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }
.iz-ui-icon-arrow-4 { background-position: 0 -80px; }
.iz-ui-icon-arrow-4-diag { background-position: -16px -80px; }
.iz-ui-icon-extlink { background-position: -32px -80px; }
.iz-ui-icon-newwin { background-position: -48px -80px; }
.iz-ui-icon-refresh { background-position: -64px -80px; }
.iz-ui-icon-shuffle { background-position: -80px -80px; }
.iz-ui-icon-transfer-e-w { background-position: -96px -80px; }
.iz-ui-icon-transferthick-e-w { background-position: -112px -80px; }
.iz-ui-icon-folder-collapsed { background-position: 0 -96px; }
.iz-ui-icon-folder-open { background-position: -16px -96px; }
.iz-ui-icon-document { background-position: -32px -96px; }
.iz-ui-icon-document-b { background-position: -48px -96px; }
.iz-ui-icon-note { background-position: -64px -96px; }
.iz-ui-icon-mail-closed { background-position: -80px -96px; }
.iz-ui-icon-mail-open { background-position: -96px -96px; }
.iz-ui-icon-suitcase { background-position: -112px -96px; }
.iz-ui-icon-comment { background-position: -128px -96px; }
.iz-ui-icon-person { background-position: -144px -96px; }
.iz-ui-icon-print { background-position: -160px -96px; }
.iz-ui-icon-trash { background-position: -176px -96px; }
.iz-ui-icon-locked { background-position: -192px -96px; }
.iz-ui-icon-unlocked { background-position: -208px -96px; }
.iz-ui-icon-bookmark { background-position: -224px -96px; }
.iz-ui-icon-tag { background-position: -240px -96px; }
.iz-ui-icon-home { background-position: 0 -112px; }
.iz-ui-icon-flag { background-position: -16px -112px; }
.iz-ui-icon-calendar { background-position: -32px -112px; }
.iz-ui-icon-cart { background-position: -48px -112px; }
.iz-ui-icon-pencil { background-position: -64px -112px; }
.iz-ui-icon-clock { background-position: -80px -112px; }
.iz-ui-icon-disk { background-position: -96px -112px; }
.iz-ui-icon-calculator { background-position: -112px -112px; }
.iz-ui-icon-zoomin { background-position: -128px -112px; }
.iz-ui-icon-zoomout { background-position: -144px -112px; }
.iz-ui-icon-search { background-position: -160px -112px; }
.iz-ui-icon-wrench { background-position: -176px -112px; }
.iz-ui-icon-gear { background-position: -192px -112px; }
.iz-ui-icon-heart { background-position: -208px -112px; }
.iz-ui-icon-star { background-position: -224px -112px; }
.iz-ui-icon-link { background-position: -240px -112px; }
.iz-ui-icon-cancel { background-position: 0 -128px; }
.iz-ui-icon-plus { background-position: -16px -128px; }
.iz-ui-icon-plusthick { background-position: -32px -128px; }
.iz-ui-icon-minus { background-position: -48px -128px; }
.iz-ui-icon-minusthick { background-position: -64px -128px; }
.iz-ui-icon-close { background-position: -80px -128px; }
.iz-ui-icon-closethick { background-position: -96px -128px; }
.iz-ui-icon-key { background-position: -112px -128px; }
.iz-ui-icon-lightbulb { background-position: -128px -128px; }
.iz-ui-icon-scissors { background-position: -144px -128px; }
.iz-ui-icon-clipboard { background-position: -160px -128px; }
.iz-ui-icon-copy { background-position: -176px -128px; }
.iz-ui-icon-contact { background-position: -192px -128px; }
.iz-ui-icon-image { background-position: -208px -128px; }
.iz-ui-icon-video { background-position: -224px -128px; }
.iz-ui-icon-script { background-position: -240px -128px; }
.iz-ui-icon-alert { background-position: 0 -144px; }
.iz-ui-icon-info { background-position: -16px -144px; }
.iz-ui-icon-notice { background-position: -32px -144px; }
.iz-ui-icon-help { background-position: -48px -144px; }
.iz-ui-icon-check { background-position: -64px -144px; }
.iz-ui-icon-bullet { background-position: -80px -144px; }
.iz-ui-icon-radio-off { background-position: -96px -144px; }
.iz-ui-icon-radio-on { background-position: -112px -144px; }
.iz-ui-icon-pin-w { background-position: -128px -144px; }
.iz-ui-icon-pin-s { background-position: -144px -144px; }
.iz-ui-icon-play { background-position: 0 -160px; }
.iz-ui-icon-pause { background-position: -16px -160px; }
.iz-ui-icon-seek-next { background-position: -32px -160px; }
.iz-ui-icon-seek-prev { background-position: -48px -160px; }
.iz-ui-icon-seek-end { background-position: -64px -160px; }
.iz-ui-icon-seek-start { background-position: -80px -160px; }
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.iz-ui-icon-seek-first { background-position: -80px -160px; }
.iz-ui-icon-stop { background-position: -96px -160px; }
.iz-ui-icon-eject { background-position: -112px -160px; }
.iz-ui-icon-volume-off { background-position: -128px -160px; }
.iz-ui-icon-volume-on { background-position: -144px -160px; }
.iz-ui-icon-power { background-position: 0 -176px; }
.iz-ui-icon-signal-diag { background-position: -16px -176px; }
.iz-ui-icon-signal { background-position: -32px -176px; }
.iz-ui-icon-battery-0 { background-position: -48px -176px; }
.iz-ui-icon-battery-1 { background-position: -64px -176px; }
.iz-ui-icon-battery-2 { background-position: -80px -176px; }
.iz-ui-icon-battery-3 { background-position: -96px -176px; }
.iz-ui-icon-circle-plus { background-position: 0 -192px; }
.iz-ui-icon-circle-minus { background-position: -16px -192px; }
.iz-ui-icon-circle-close { background-position: -32px -192px; }
.iz-ui-icon-circle-triangle-e { background-position: -48px -192px; }
.iz-ui-icon-circle-triangle-s { background-position: -64px -192px; }
.iz-ui-icon-circle-triangle-w { background-position: -80px -192px; }
.iz-ui-icon-circle-triangle-n { background-position: -96px -192px; }
.iz-ui-icon-circle-arrow-e { background-position: -112px -192px; }
.iz-ui-icon-circle-arrow-s { background-position: -128px -192px; }
.iz-ui-icon-circle-arrow-w { background-position: -144px -192px; }
.iz-ui-icon-circle-arrow-n { background-position: -160px -192px; }
.iz-ui-icon-circle-zoomin { background-position: -176px -192px; }
.iz-ui-icon-circle-zoomout { background-position: -192px -192px; }
.iz-ui-icon-circle-check { background-position: -208px -192px; }
.iz-ui-icon-circlesmall-plus { background-position: 0 -208px; }
.iz-ui-icon-circlesmall-minus { background-position: -16px -208px; }
.iz-ui-icon-circlesmall-close { background-position: -32px -208px; }
.iz-ui-icon-squaresmall-plus { background-position: -48px -208px; }
.iz-ui-icon-squaresmall-minus { background-position: -64px -208px; }
.iz-ui-icon-squaresmall-close { background-position: -80px -208px; }
.iz-ui-icon-grip-dotted-vertical { background-position: 0 -224px; }
.iz-ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }
.iz-ui-icon-grip-solid-vertical { background-position: -32px -224px; }
.iz-ui-icon-grip-solid-horizontal { background-position: -48px -224px; }
.iz-ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }
.iz-ui-icon-grip-diagonal-se { background-position: -80px -224px; }




/* Misc visuals
----------------------------------*/

/* Corner radius */
.iz-ui-corner-all, .iz-ui-corner-top, .iz-ui-corner-left, .iz-ui-corner-tl { -moz-border-radius-topleft: 0; -webkit-border-top-left-radius: 0; -khtml-border-top-left-radius: 0; border-top-left-radius: 0; }
.iz-ui-corner-all, .iz-ui-corner-top, .iz-ui-corner-right, .iz-ui-corner-tr { -moz-border-radius-topright: 0; -webkit-border-top-right-radius: 0; -khtml-border-top-right-radius: 0; border-top-right-radius: 0; }
.iz-ui-corner-all, .iz-ui-corner-bottom, .iz-ui-corner-left, .iz-ui-corner-bl { -moz-border-radius-bottomleft: 0; -webkit-border-bottom-left-radius: 0; -khtml-border-bottom-left-radius: 0; border-bottom-left-radius: 0; }
.iz-ui-corner-all, .iz-ui-corner-bottom, .iz-ui-corner-right, .iz-ui-corner-br { -moz-border-radius-bottomright: 0; -webkit-border-bottom-right-radius: 0; -khtml-border-bottom-right-radius: 0; border-bottom-right-radius: 0; }

/* Overlays */
.iz-ui-widget-overlay { background: #eeeeee; opacity: .80;filter:Alpha(Opacity=80); }
.iz-ui-widget-shadow { margin: -4px 0 0 -4px; padding: 4px; background: #aaaaaa; opacity: .60;filter:Alpha(Opacity=60); -moz-border-radius: 0px; -khtml-border-radius: 0px; -webkit-border-radius: 0px; border-radius: 0px; }/*
 * jQuery UI Resizable 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Resizable#theming
 */
.iz-ui-resizable { position: relative;}
.iz-ui-resizable-handle { position: absolute;font-size: 0.1px;z-index: 99999; display: block; }
.iz-ui-resizable-disabled .iz-ui-resizable-handle, .iz-ui-resizable-autohide .iz-ui-resizable-handle { display: none; }
.iz-ui-resizable-n { cursor: n-resize; height: 7px; width: 100%; top: -5px; left: 0; }
.iz-ui-resizable-s { cursor: s-resize; height: 7px; width: 100%; bottom: -5px; left: 0; }
.iz-ui-resizable-e { cursor: e-resize; width: 7px; right: -5px; top: 0; height: 100%; }
.iz-ui-resizable-w { cursor: w-resize; width: 7px; left: -5px; top: 0; height: 100%; }
.iz-ui-resizable-se { cursor: se-resize; width: 12px; height: 12px; right: 1px; bottom: 1px; }
.iz-ui-resizable-sw { cursor: sw-resize; width: 9px; height: 9px; left: -5px; bottom: -5px; }
.iz-ui-resizable-nw { cursor: nw-resize; width: 9px; height: 9px; left: -5px; top: -5px; }
.iz-ui-resizable-ne { cursor: ne-resize; width: 9px; height: 9px; right: -5px; top: -5px;}/*
 * jQuery UI Selectable 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Selectable#theming
 */
.iz-ui-selectable-helper { position: absolute; z-index: 100; border:1px dotted black; }
/*
 * jQuery UI Accordion 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Accordion#theming
 */
/* IE/Win - Fix animation bug - #4615 */
.iz-ui-accordion { width: 100%; }
.iz-ui-accordion .iz-ui-accordion-header { cursor: pointer; position: relative; margin-top: 1px; zoom: 1; }
.iz-ui-accordion .iz-ui-accordion-li-fix { display: inline; }
.iz-ui-accordion .iz-ui-accordion-header-active { border-bottom: 0 !important; }
.iz-ui-accordion .iz-ui-accordion-header a { display: block; font-size: 1em; padding: .5em .5em .5em .7em; }
.iz-ui-accordion-icons .iz-ui-accordion-header a { padding-left: 2.2em; }
.iz-ui-accordion .iz-ui-accordion-header .iz-ui-icon { position: absolute; left: 2px; top: 50%; margin-top: -8px; }
.iz-ui-accordion .iz-ui-accordion-content { padding: 1em 2.2em; border-top: 0; margin-top: -2px; position: relative; top: 1px; margin-bottom: 2px; overflow: auto; display: none; zoom: 1; }
.iz-ui-accordion .iz-ui-accordion-content-active { display: block; }
/*
 * jQuery UI Autocomplete 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Autocomplete#theming
 */
.iz-ui-autocomplete { position: absolute; cursor: default; }	

/* workarounds */
* html .iz-ui-autocomplete { width:1px; } /* without this, the menu expands to 100% in IE6 */

/*
 * jQuery UI Menu 1.8.16
 *
 * Copyright 2010, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Menu#theming
 */
.iz-ui-menu {
	list-style:none;
	padding: 2px;
	margin: 0;
	display:block;
	float: left;
}
.iz-ui-menu .iz-ui-menu {
	margin-top: -3px;
}
.iz-ui-menu .iz-ui-menu-item {
	margin:0;
	padding: 0;
	zoom: 1;
	float: left;
	clear: left;
	width: 100%;
}
.iz-ui-menu .iz-ui-menu-item a {
	text-decoration:none;
	display:block;
	padding:.2em .4em;
	line-height:1.5;
	zoom:1;
}
.iz-ui-menu .iz-ui-menu-item a.iz-ui-state-hover,
.iz-ui-menu .iz-ui-menu-item a.iz-ui-state-active {
	font-weight: normal;
	margin: -1px;
}
/*
 * jQuery UI Button 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Button#theming
 */
.iz-ui-button { display: inline-block; position: relative; padding: 0; margin-right: .1em; text-decoration: none !important; cursor: pointer; text-align: center; zoom: 1; overflow: visible; } /* the overflow property removes extra width in IE */
.iz-ui-button-icon-only { width: 2.2em; } /* to make room for the icon, a width needs to be set here */
button.iz-ui-button-icon-only { width: 2.4em; } /* button elements seem to need a little more width */
.iz-ui-button-icons-only { width: 3.4em; } 
button.iz-ui-button-icons-only { width: 3.7em; } 

/*button text element */
.iz-ui-button .iz-ui-button-text { display: block; line-height: 1.4;  }
.iz-ui-button-text-only .iz-ui-button-text { padding: .4em 1em; }
.iz-ui-button-icon-only .iz-ui-button-text, .iz-ui-button-icons-only .iz-ui-button-text { padding: .4em; text-indent: -9999999px; }
.iz-ui-button-text-icon-primary .iz-ui-button-text, .iz-ui-button-text-icons .iz-ui-button-text { padding: .4em 1em .4em 2.1em; }
.iz-ui-button-text-icon-secondary .iz-ui-button-text, .iz-ui-button-text-icons .iz-ui-button-text { padding: .4em 2.1em .4em 1em; }
.iz-ui-button-text-icons .iz-ui-button-text { padding-left: 2.1em; padding-right: 2.1em; }
/* no icon support for input elements, provide padding by default */
input.iz-ui-button { padding: .4em 1em; }

/*button icon element(s) */
/*.iz-ui-button-icon-only .iz-ui-icon, .iz-ui-button-text-icon-primary .iz-ui-icon, .iz-ui-button-text-icon-secondary .iz-ui-icon, .iz-ui-button-text-icons .iz-ui-icon, .iz-ui-button-icons-only .iz-ui-icon { position: absolute; top: 50%; margin-top: -8px; }
.iz-ui-button-icon-only .iz-ui-icon { left: 50%; margin-left: -8px; }
.iz-ui-button-text-icon-primary .iz-ui-button-icon-primary, .iz-ui-button-text-icons .iz-ui-button-icon-primary, .iz-ui-button-icons-only .iz-ui-button-icon-primary { left: .5em; }
.iz-ui-button-text-icon-secondary .iz-ui-button-icon-secondary, .iz-ui-button-text-icons .iz-ui-button-icon-secondary, .iz-ui-button-icons-only .iz-ui-button-icon-secondary { right: .5em; }
.iz-ui-button-text-icons .iz-ui-button-icon-secondary, .iz-ui-button-icons-only .iz-ui-button-icon-secondary { right: .5em; }
*/

 /*button icon element(s) */
.iz-ui-button-icon-only .iz-ui-icon, .iz-ui-button-text-icon-primary .iz-ui-icon, .iz-ui-button-text-icon-secondary .iz-ui-icon, .iz-ui-button-text-icons .iz-ui-icon, .iz-ui-button-icons-only .iz-ui-icon { position: absolute; top: 50%; margin-top: -12px; }
.iz-ui-button-icon-only .iz-ui-icon { left: 50%; margin-left: -12px; }
.iz-ui-button-text-icon-primary .iz-ui-button-icon-primary, .iz-ui-button-text-icons .iz-ui-button-icon-primary, .iz-ui-button-icons-only .iz-ui-button-icon-primary { left: .35em; }
.iz-ui-button-text-icon-secondary .iz-ui-button-icon-secondary, .iz-ui-button-text-icons .iz-ui-button-icon-secondary, .iz-ui-button-icons-only .iz-ui-button-icon-secondary { right: .35em; }
.iz-ui-button-text-icons .iz-ui-button-icon-secondary, .iz-ui-button-icons-only .iz-ui-button-icon-secondary { right: .35em; }














/*button sets*/
.iz-ui-buttonset { margin-right: 7px; }
.iz-ui-buttonset .iz-ui-button { margin-left: 0; margin-right: -.3em; }

/* workarounds */
button.iz-ui-button::-moz-focus-inner { border: 0; padding: 0; } /* reset extra padding in Firefox */
/*
 * jQuery UI Dialog 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Dialog#theming
 */
.iz-ui-dialog { position: absolute; padding: .2em; width: 300px; overflow: hidden; }
.iz-ui-dialog .iz-ui-dialog-titlebar { padding: .4em 1em; position: relative;  }
.iz-ui-dialog .iz-ui-dialog-title { float: left; margin: .1em 16px .1em 0; } 
.iz-ui-dialog .iz-ui-dialog-titlebar-close { position: absolute; right: .3em; top: 50%; width: 19px; margin: -10px 0 0 0; padding: 1px; height: 18px; }
.iz-ui-dialog .iz-ui-dialog-titlebar-close span { display: block; margin: 1px; }
.iz-ui-dialog .iz-ui-dialog-titlebar-close:hover, .iz-ui-dialog .iz-ui-dialog-titlebar-close:focus { padding: 0; }
.iz-ui-dialog .iz-ui-dialog-content { position: relative; border: 0; padding: .5em 1em; background: none; overflow: auto; zoom: 1; }
.iz-ui-dialog .iz-ui-dialog-buttonpane { text-align: left; border-width: 1px 0 0 0; background-image: none; margin: .5em 0 0 0; padding: .3em 1em .5em .4em; }
.iz-ui-dialog .iz-ui-dialog-buttonpane .iz-ui-dialog-buttonset { float: right; }
.iz-ui-dialog .iz-ui-dialog-buttonpane button { margin: .5em .4em .5em 0; cursor: pointer; }
.iz-ui-dialog .iz-ui-resizable-se { width: 14px; height: 14px; right: 2px; bottom: 2px; }
.iz-ui-draggable .iz-ui-dialog-titlebar { cursor: move; }
/*
 * jQuery UI Slider 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Slider#theming
 */
.iz-ui-slider { position: relative; text-align: left; }
.iz-ui-slider .iz-ui-slider-handle { position: absolute; z-index: 2; width: 1.2em; height: 1.2em; cursor: default; }
.iz-ui-slider .iz-ui-slider-range { position: absolute; z-index: 1; font-size: .7em; display: block; border: 0; background-position: 0 0; }

.iz-ui-slider-horizontal { height: .8em; }
.iz-ui-slider-horizontal .iz-ui-slider-handle { top: -.3em; margin-left: -.6em; }
.iz-ui-slider-horizontal .iz-ui-slider-range { top: 0; height: 100%; }
.iz-ui-slider-horizontal .iz-ui-slider-range-min { left: 0; }
.iz-ui-slider-horizontal .iz-ui-slider-range-max { right: 0; }

.iz-ui-slider-vertical { width: .8em; height: 100px; }
.iz-ui-slider-vertical .iz-ui-slider-handle { left: -.3em; margin-left: 0; margin-bottom: -.6em; }
.iz-ui-slider-vertical .iz-ui-slider-range { left: 0; width: 100%; }
.iz-ui-slider-vertical .iz-ui-slider-range-min { bottom: 0; }
.iz-ui-slider-vertical .iz-ui-slider-range-max { top: 0; }/*
 * jQuery UI Tabs 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Tabs#theming
 */
.iz-ui-tabs { position: relative; padding: .2em; zoom: 1; } /* position: relative prevents IE scroll bug (element with position: relative inside container with overflow: auto appear as "fixed") */
.iz-ui-tabs .iz-ui-tabs-nav { margin: 0; padding: .2em .2em 0; }
.iz-ui-tabs .iz-ui-tabs-nav li { list-style: none; float: left; position: relative; top: 1px; margin: 0 .2em 1px 0; border-bottom: 0 !important; padding: 0; white-space: nowrap; }
.iz-ui-tabs .iz-ui-tabs-nav li a { float: left; padding: .5em 1em; text-decoration: none; }
.iz-ui-tabs .iz-ui-tabs-nav li.iz-ui-tabs-selected { margin-bottom: 0; padding-bottom: 1px; }
.iz-ui-tabs .iz-ui-tabs-nav li.iz-ui-tabs-selected a, .iz-ui-tabs .iz-ui-tabs-nav li.iz-ui-state-disabled a, .iz-ui-tabs .iz-ui-tabs-nav li.iz-ui-state-processing a { cursor: text; }
.iz-ui-tabs .iz-ui-tabs-nav li a, .iz-ui-tabs.iz-ui-tabs-collapsible .iz-ui-tabs-nav li.iz-ui-tabs-selected a { cursor: pointer; } /* first selector in group seems obsolete, but required to overcome bug in Opera applying cursor: text overall if defined elsewhere... */
.iz-ui-tabs .iz-ui-tabs-panel { display: block; border-width: 0; background: none; }
.iz-ui-tabs .iz-ui-tabs-hide { display: none !important; }
/*
 * jQuery UI Datepicker 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Datepicker#theming
 */
.iz-ui-datepicker { width: 17em; padding: .2em .2em 0; display: none; }
.iz-ui-datepicker .iz-ui-datepicker-header { position:relative; padding:.2em 0; }
.iz-ui-datepicker .iz-ui-datepicker-prev, .iz-ui-datepicker .iz-ui-datepicker-next { position:absolute; top: 2px; width: 1.8em; height: 1.8em; }
.iz-ui-datepicker .iz-ui-datepicker-prev-hover, .iz-ui-datepicker .iz-ui-datepicker-next-hover { top: 1px; }
.iz-ui-datepicker .iz-ui-datepicker-prev { left:2px; }
.iz-ui-datepicker .iz-ui-datepicker-next { right:2px; }
.iz-ui-datepicker .iz-ui-datepicker-prev-hover { left:1px; }
.iz-ui-datepicker .iz-ui-datepicker-next-hover { right:1px; }
.iz-ui-datepicker .iz-ui-datepicker-prev span, .iz-ui-datepicker .iz-ui-datepicker-next span { display: block; position: absolute; left: 50%; margin-left: -8px; top: 50%; margin-top: -8px;  }
.iz-ui-datepicker .iz-ui-datepicker-title { margin: 0 2.3em; line-height: 1.8em; text-align: center; }
.iz-ui-datepicker .iz-ui-datepicker-title select { font-size:1em; margin:1px 0; }
.iz-ui-datepicker select.iz-ui-datepicker-month-year {width: 100%;}
.iz-ui-datepicker select.iz-ui-datepicker-month, 
.iz-ui-datepicker select.iz-ui-datepicker-year { width: 49%;}
.iz-ui-datepicker table {width: 100%; font-size: .9em; border-collapse: collapse; margin:0 0 .4em; }
.iz-ui-datepicker th { padding: .7em .3em; text-align: center; font-weight: normal; border: 0;  }
.iz-ui-datepicker td { border: 0; padding: 1px; }
.iz-ui-datepicker td span, .iz-ui-datepicker td a { display: block; padding: .2em; text-align: right; text-decoration: none; }
.iz-ui-datepicker .iz-ui-datepicker-buttonpane { background-image: none; margin: .7em 0 0 0; padding:0 .2em; border-left: 0; border-right: 0; border-bottom: 0; }
.iz-ui-datepicker .iz-ui-datepicker-buttonpane button { float: right; margin: .5em .2em .4em; cursor: pointer; padding: .2em .6em .3em .6em; width:auto; overflow:visible; }
.iz-ui-datepicker .iz-ui-datepicker-buttonpane button.iz-ui-datepicker-current { float:left; }

/* with multiple calendars */
.iz-ui-datepicker.iz-ui-datepicker-multi { width:auto; }
.iz-ui-datepicker-multi .iz-ui-datepicker-group { float:left; }
.iz-ui-datepicker-multi .iz-ui-datepicker-group table { width:95%; margin:0 auto .4em; }
.iz-ui-datepicker-multi-2 .iz-ui-datepicker-group { width:50%; }
.iz-ui-datepicker-multi-3 .iz-ui-datepicker-group { width:33.3%; }
.iz-ui-datepicker-multi-4 .iz-ui-datepicker-group { width:25%; }
.iz-ui-datepicker-multi .iz-ui-datepicker-group-last .iz-ui-datepicker-header { border-left-width:0; }
.iz-ui-datepicker-multi .iz-ui-datepicker-group-middle .iz-ui-datepicker-header { border-left-width:0; }
.iz-ui-datepicker-multi .iz-ui-datepicker-buttonpane { clear:left; }
.iz-ui-datepicker-row-break { clear:both; width:100%; font-size:0em; }

/* RTL support */
.iz-ui-datepicker-rtl { direction: rtl; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-prev { right: 2px; left: auto; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-next { left: 2px; right: auto; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-prev:hover { right: 1px; left: auto; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-next:hover { left: 1px; right: auto; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-buttonpane { clear:right; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-buttonpane button { float: left; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-buttonpane button.iz-ui-datepicker-current { float:right; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-group { float:right; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-group-last .iz-ui-datepicker-header { border-right-width:0; border-left-width:1px; }
.iz-ui-datepicker-rtl .iz-ui-datepicker-group-middle .iz-ui-datepicker-header { border-right-width:0; border-left-width:1px; }

/* IE6 IFRAME FIX (taken from datepicker 1.5.3 */
.iz-ui-datepicker-cover {
    display: none; /*sorry for IE5*/
    display/**/: block; /*sorry for IE5*/
    position: absolute; /*must have*/
    z-index: -1; /*must have*/
    filter: mask(); /*must have*/
    top: -4px; /*must have*/
    left: -4px; /*must have*/
    width: 200px; /*must have*/
    height: 200px; /*must have*/
}/*
 * jQuery UI Progressbar 1.8.16
 *
 * Copyright 2011, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://docs.jquery.com/UI/Progressbar#theming
 */
.iz-ui-progressbar { height:2em; text-align: left; }
.iz-ui-progressbar .iz-ui-progressbar-value {margin: -1px; height:100%; }

/* General */
html, body{background-color:#ffffff;}
.iz-ui-widget{font-family:"Segoe UI", Helvetica, Verdana;}

/* Interaction states
----------------------------------*/
.iz-ui-state-default, .iz-ui-widget-content .iz-ui-state-default, .iz-ui-widget-header .iz-ui-state-default { border:2px solid #dddddd;background:inherit; font-weight: bold; text-decoration:none; }
.iz-ui-state-default a, .iz-ui-state-default a:link, .iz-ui-state-default a:visited { }
.iz-ui-state-hover, .iz-ui-widget-content .iz-ui-state-hover, .iz-ui-widget-header .iz-ui-state-hover, .iz-ui-state-focus, .iz-ui-widget-content .iz-ui-state-focus, .iz-ui-widget-header .iz-ui-state-focus { font-weight: bold; color: #ffffff; border-width:2px;}
.iz-ui-state-hover a, .iz-ui-state-hover a:hover { color: #ffffff; text-decoration: none; }
.iz-ui-state-active, .iz-ui-widget-content .iz-ui-state-active, .iz-ui-widget-header .iz-ui-state-active { border: 1px solid #dddddd; font-weight: bold; color: #ff0084;}
.iz-ui-state-active a, .iz-ui-state-active a:link, .iz-ui-state-active a:visited { color: #ff0084; }
.iz-ui-widget :active { outline: none; }

/* Accordion */
.iz-ui-accordion-header{border:2px solid;}
.iz-ui-accordion .iz-ui-accordion-header{margin-top:2px;}
.iz-ui-accordion-content{border:2px solid;border-top:none;margin-bottom:3px !important;}
.iz-ui-accordion .iz-ui-accordion-header a { font-size: 1.0em; padding: .6em .3em .5em 2.9em; font-weight:bold;}
.iz-ui-accordion > .iz-ui-state-active a, .iz-ui-accordion > div > h3.iz-ui-state-active a  { padding-bottom:.8em;}
/*.iz-ui-accordion .iz-ui-accordion-header .iz-ui-icon{margin-top:-12px;}*/
.iz-ui-accordion > .iz-ui-state-active, .iz-ui-accordion > div > h3.iz-ui-state-active{border-bottom:none !important;}
.iz-ui-accordion-header > .iz-ui-icon{margin:4px;}

/* Tabs */
.iz-ui-tabs{padding:0;border:none;position:relative;top:-3px;}
.iz-ui-tabs-nav{padding:0px 0px 0px 0px !important;border:none;border-bottom:2px solid;background-color:#ffffff;}
.iz-ui-tabs-nav .iz-ui-state-default{border:none;padding:0px !important;margin-right:2px !important;background:none !important;}
.iz-ui-tabs-nav .iz-ui-state-default a{border:2px solid;position:relative;top:2px;font-weight:bold;margin-bottom:4px;height:16px;}
.iz-ui-tabs-nav .iz-ui-state-active a{border:2px solid;border-bottom:none !important;margin-bottom:0;height:22px;}
.iz-ui-tabs .iz-ui-widget-content{border-top:none !important;}
.iz-ui-tabs .iz-ui-widget-content .iz-ui-tabs {border:none !important;}
.iz-ui-tabs-nav .iz-ui-state-hover{border:none;}


/* Dialog */
.iz-ui-dialog{border-width:2px;padding:0;}
.iz-ui-dialog-titlebar {border:none;border-bottom-width:2px;padding:.6em 1em .6em 1em !important;font-weight:bold;font-size:1.1em;}
.iz-ui-dialog-buttonpane{border-top-width:2px !important;margin-top:0 !important;}
.iz-ui-dialog .iz-ui-dialog-titlebar-close{width:24px;height:24px;margin:-13px 0px 0px 0px;padding:0;}
.iz-ui-dialog-titlebar-close > .iz-ui-icon{margin:4px !important;}

/* Datepicker*/
.iz-ui-datepicker {border:2px solid;padding:0 !important;}
.iz-ui-datepicker .iz-ui-datepicker-header{border:none;padding:.4em 1em .4em 1em;}
.iz-ui-datepicker .iz-ui-datepicker-header .iz-ui-state-default, .iz-ui-datepicker .iz-ui-datepicker-header .iz-ui-state-hover{background:none;border:none;padding:0;}
.iz-ui-datepicker .iz-ui-datepicker-prev, .iz-ui-datepicker .iz-ui-datepicker-next{width:24px;height:24px;margin:0;}
.iz-ui-datepicker .iz-ui-datepicker-prev span, .iz-ui-datepicker .iz-ui-datepicker-next span{position:relative;top:0;left:0;margin:0;}
.iz-ui-datepicker .iz-ui-datepicker-prev-hover { left:2px;top:2px; }
.iz-ui-datepicker .iz-ui-datepicker-next-hover { right:2px;top:2px; }
.iz-ui-datepicker table {margin:0px;border:1px solid;}
.iz-ui-datepicker-calendar .iz-ui-state-default, .iz-ui-datepicker-calendar .iz-ui-state-hover, .iz-ui-datepicker-calendar .iz-ui-state-highlight, .iz-ui-datepicker-calendar .iz-ui-state-active{border-width:2px;}
.iz-ui-datepicker .iz-ui-icon{margin:4px !important;}

/* Button */
.iz-ui-button{border-width:2px !important;border-bottom-style:solid;font-weight:bold !important;}

.iz-ui-buttonset .iz-ui-button { margin-left: 0; margin-right: -1px; }

/* Additional states */
.iz-ui-state-highlight{border:2px solid;}
.iz-ui-state-error{border:2px solid;}
/*.iz-ui-state-highlight .iz-ui-icon, .iz-ui-state-error .iz-ui-icon{position:relative;top:-4px;}*/

/* Slider */
.iz-ui-slider{border:none;}
.iz-ui-slider .iz-ui-slider-handle{width:.8em;height:.8em;border:none;margin:-.4em;}
.iz-ui-slider-horizontal .iz-ui-slider-handle{margin-top:0; top:0;}
.iz-ui-slider-vertical .iz-ui-slider-handle{margin-left:0;left:0;}

/* Progressbar */
.iz-ui-progressbar{border:none;height:0.8em;}
.iz-ui-progressbar .iz-ui-progressbar-value{border:none;}

/* Autocomplete */
.iz-ui-autocomplete {border:solid 2px #bbbbbb;padding:0;}
.iz-ui-autocomplete .iz-ui-menu-item a{padding:5px;border:none;margin:0 !important;}

/* Icon states */
.iz-ui-icon { width: 16px; height: 16px; }
/* General */
.iz-ui-icon, .iz-ui-widget-content .iz-ui-icon, .iz-ui-widget-header .iz-ui-icon  {background-image:url(###RS###image=ModernImages.iz-ui-icons_d4d4d4_0.png)}
.iz-ui-icon-light, .iz-ui-widget-content .iz-ui-icon-light, .iz-ui-widget-header .iz-ui-icon-light  {background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png)}
.iz-ui-state-default .iz-ui-icon {background-image:url(images/ui-icons_525252_0.png)}
.iz-ui-state-active .iz-ui-icon, .iz-ui-state-hover .iz-ui-icon{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png);}
.iz-ui-widget-overlay{background:#ffffff;}
.iz-ui-widget-content{color:#525252;}





/* Button states */
.iz-ui-state-default, .iz-ui-widget-content .iz-ui-state-default{background-color:#d4d4d4;border-color:#d4d4d4;color:#525252;}
.iz-ui-state-focus, .iz-ui-widget-content .iz-ui-state-focus{background-color:#66B3FF;border-color:#66B3FF;color:#ffffff;}
.iz-ui-state-hover, .iz-ui-widget-content .iz-ui-state-hover{background-color:#66B3FF;border-color:#66B3FF;color:#ffffff;}
.iz-ui-state-active, .iz-ui-widget-content .iz-ui-state-active{background-color:#3399ff;border-color:#3399ff;color:#ffffff;}


/* Accordion */
.iz-ui-accordion-content{border-color:#d4d4d4;background:#ffffff}
.iz-ui-accordion-header.iz-ui-state-active, .iz-ui-accordion-header.iz-ui-state-active.iz-ui-state-hover{background-color:#ffffff !important;color:#525252;border-color:#d4d4d4 !important;background-image:url(###RS###image=ModernImages.iz-ui-icons_d4d4d4_0.png) !important;background-position: -230px -213px !important;}
.iz-ui-accordion-header.iz-ui-state-active a{ color: #525252 !important;}
.iz-ui-accordion-header.iz-ui-state-default{border-color:#3399ff;background:#3399ff;}
.iz-ui-accordion-header.iz-ui-state-default a{ color: #ffffff;}
.iz-ui-accordion-header.iz-ui-state-hover{background:#66B3FF;border-color:#66B3FF;}
.iz-ui-accordion-header.iz-ui-state-hover a{color:#ffffff;}
.iz-ui-accordion-header.iz-ui-state-active .iz-ui-icon{background-image:url(###RS###image=ModernImages.iz-ui-icons_d4d4d4_0.png) !important;}
.iz-ui-accordion-header.iz-ui-state-default .iz-ui-icon{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png);}
.iz-ui-accordion-header.iz-ui-state-hover .iz-ui-icon{background-image:url(images/ui-icons_66B3FF_0.png);}
.iz-ui-accordion-header{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png) !important;background-position: -230px -214px !important;background-repeat:no-repeat !important;}
.iz-ui-accordion-header.iz-ui-state-hover{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png) !important;background-position: -230px -166px !important;}



/* Tabs states */
.iz-ui-tabs-nav > .iz-ui-state-default a{border-color:#3399ff;background:#3399ff;color:#ffffff;}
.iz-ui-tabs-nav > .iz-ui-state-active a{border-color:#d4d4d4 !important;background:#ffffff !important;color:#525252 !important;}
.iz-ui-tabs-nav > .iz-ui-state-hover a{background:#66B3FF;border-color:#66B3FF;color:#ffffff;}
.iz-ui-tabs-nav > .iz-ui-state-focus a{background:#66B3FF;border-color:#66B3FF;color:#ffffff;}
.iz-ui-tabs .iz-ui-widget-content{border-color:#d4d4d4 !important;background-color:#ffffff;}
.iz-ui-tabs-nav{border-color:#d4d4d4;}

/* Dialog states */
.iz-ui-dialog{border-color:#3399ff;background-color:#ffffff !important;}
.iz-ui-dialog-titlebar{border-color:#3399ff;color:#ffffff;background-color:#3399ff;}
.iz-ui-dialog-buttonpane{border-color:#3399ff !important;background-color:#ffffff;}
.iz-ui-dialog-content{background-color:#ffffff !important;}
.iz-ui-dialog-titlebar .iz-ui-icon{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png);}
.iz-ui-dialog-titlebar .iz-ui-state-hover .iz-ui-icon{background-image:url(###RS###image=ModernImages.iz-ui-icons_3399ff_0.png) !important;}
.iz-ui-dialog-titlebar-close{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png) !important;background-position: -232px -216px !important;}
.iz-ui-dialog-titlebar-close.iz-ui-state-hover{background:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png) !important;background-position: -232px -168px !important;}





/* Slider */
.iz-ui-slider{background:#d4d4d4;}
.iz-ui-slider .iz-ui-slider-range{background:#3399ff;}
.iz-ui-slider .iz-ui-state-default{background-color:#000000;}
.iz-ui-slider .iz-ui-state-active{background-color:#525252 !important;}
.iz-ui-slider .iz-ui-state-focus{background-color:#000000;}
.iz-ui-slider .iz-ui-state-hover{background-color:#525252;}

/* Progressbar */
.iz-ui-progressbar{background:#d4d4d4;}
.iz-ui-progressbar .iz-ui-progressbar-value{background-color:#3399ff;}

/* Autocomplete */

.iz-ui-autocomplete {border-color:#525252;}
.iz-ui-autocomplete a{background-color:#ffffff;color:#525252;}
.iz-ui-autocomplete .iz-ui-state-hover{background:#3399ff;color:#ffffff;}

/* Datepicker */
.iz-ui-datepicker {border-color:#3399ff;}
.iz-ui-datepicker .iz-ui-datepicker-header{background-color:#3399ff;color:#ffffff;}
.iz-ui-datepicker table {border-color:#ffffff; }
.iz-ui-datepicker-calendar{background:#ffffff;}
.iz-ui-datepicker-calendar .iz-ui-state-default{background-color:#d4d4d4;border-color:#d4d4d4;color:#525252;}
.iz-ui-datepicker-calendar .iz-ui-state-hover{background-color:#66B3FF !important;border-color:#66B3FF !important;color:#ffffff !important;}
.iz-ui-datepicker-calendar .iz-ui-state-highlight{background-color:#d4d4d4;border-color:#3399ff;color:#ffffff;}
.iz-ui-datepicker-calendar .iz-ui-state-active{background-color:#3399ff;border-color:#3399ff;color:#ffffff;}
.iz-ui-datepicker .iz-ui-icon {background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png);}

.iz-ui-datepicker .iz-ui-state-hover .iz-ui-icon {background-image:url(###RS###image=ModernImages.iz-ui-icons_3399ff_0.png)}
.iz-ui-datepicker-next, .iz-ui-datepicker-prev{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png);background-position: -232px -216px;}
.iz-ui-datepicker-next-hover, .iz-ui-datepicker-prev-hover{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png) !important;background-position: -232px -168px !important;opacity:1;}
.iz-ui-datepicker-next.iz-ui-state-disabled, .iz-ui-datepicker-prev.iz-ui-state-disabled{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png);background-position: -232px -216px;}



/* Highlight */
.iz-ui-state-highlight{border-color:#efdca9;color:#525252;background:#efdca9;}
.iz-ui-state-highlight .iz-ui-icon{background-image:url(images/ui-icons_525252_0.png);}

/* Error */
.iz-ui-state-error{border-color:#c31d1d;color:#ffffff;background:#c31d1d;}
.iz-ui-state-error .iz-ui-icon{background-image:url(###RS###image=ModernImages.iz-ui-icons_ffffff_0.png);}


