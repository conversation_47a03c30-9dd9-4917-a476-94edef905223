{"id": "ng", "generated": "Tue Feb 24 2015 11:05:23 GMT-0800 (PST)", "errors": {"$http": {"badreq": "Http request configuration must be an object.  Received: {0}"}, "ngRepeat": {"badident": "alias '{0}' is invalid --- must be a valid JS identifier which is not a reserved name.", "iexp": "Expected expression in form of '_item_ in _collection_[ track by _id_]' but got '{0}'.", "dupes": "Duplicates in a repeater are not allowed. Use 'track by' expression to specify unique keys. Repeater: {0}, Duplicate key: {1}, Duplicate value: {2}", "iidexp": "'_item_' in '_item_ in _collection_' should be an identifier or '(_key_, _value_)' expression, but got '{0}'."}, "$sce": {"imatcher": "Matchers may only be \"self\", string patterns or RegExp objects", "icontext": "Attempted to trust a value in invalid context. Context: {0}; Value: {1}", "iwcard": "Illegal sequence *** in string matcher.  String: {0}", "insecurl": "Blocked loading resource from url not allowed by $sceDelegate policy.  URL: {0}", "iequirks": "Strict Contextual Escaping does not support Internet Explorer version < 11 in quirks mode.  You can fix this by adding the text <!doctype html> to the top of your HTML document.  See http://docs.angularjs.org/api/ng.$sce for more information.", "unsafe": "Attempting to use an unsafe value in a safe context.", "itype": "Attempted to trust a non-string value in a content requiring a string: Context: {0}"}, "ngPattern": {"noregexp": "Expected {0} to be a RegExp but was {1}. Element: {2}"}, "$controller": {"ctrlfmt": "Badly formed controller string '{0}'. Must match `__name__ as __id__` or `__name__`.", "noscp": "Cannot export controller '{0}' as '{1}'! No $scope object provided via `locals`."}, "$ngModel": {"nonassign": "Expression '{0}' is non-assignable. Element: {1}", "datefmt": "Expected `{0}` to be a date", "$asyncValidators": "Expected asynchronous validator to return a promise but got '{0}' instead.", "numfmt": "Expected `{0}` to be a number"}, "$parse": {"isecfn": "Referencing Function in Angular expressions is disallowed! Expression: {0}", "isecwindow": "Referencing the Window in Angular expressions is disallowed! Expression: {0}", "ueoe": "Unexpected end of expression: {0}", "isecdom": "Referencing DOM nodes in Angular expressions is disallowed! Expression: {0}", "lexerr": "<PERSON>er Error: {0} at column{1} in expression [{2}].", "isecobj": "Referencing Object in Angular expressions is disallowed! Expression: {0}", "isecff": "Referencing call, apply or bind in Angular expressions is disallowed! Expression: {0}", "syntax": "Syntax Error: Token '{0}' {1} at column {2} of the expression [{3}] starting at [{4}].", "isecfld": "Attempting to access a disallowed field in Angular expressions! Expression: {0}"}, "jqLite": {"offargs": "jqLite#off() does not support the `selector` argument", "onargs": "jqLite#on() does not support the `selector` or `eventData` parameters", "nosel": "Looking up elements via selectors is not supported by jqLite! See: http://docs.angularjs.org/api/angular.element"}, "$animate": {"notcsel": "Expecting class selector starting with '.' got '{0}'."}, "$q": {"norslvr": "Expected resolverFn, got '{0}'", "qcycle": "Expected promise to be resolved with value other than itself '{0}'"}, "$injector": {"pget": "Provider '{0}' must define $get factory method.", "cdep": "Circular dependency found: {0}", "nomod": "Module '{0}' is not available! You either misspelled the module name or forgot to load it. If registering a module ensure that you specify the dependencies as the second argument.", "strictdi": "{0} is not using explicit annotation and cannot be invoked in strict mode", "modulerr": "Failed to instantiate module {0} due to:\n{1}", "undef": "Provider '{0}' must return a value from $get factory method.", "unpr": "Unknown provider: {0}", "itkn": "Incorrect injection token! Expected service name as string, got {0}"}, "ngTransclude": {"orphan": "Illegal use of ngTransclude directive in the template! No parent directive that requires a transclusion found. Element: {0}"}, "ngModel": {"constexpr": "Expected constant expression for `{0}`, but saw `{1}`."}, "$location": {"nostate": "History API state support is available only in HTML5 mode and only in browsers supporting HTML5 History API", "ipthprfx": "Invalid url \"{0}\", missing path prefix \"{1}\".", "isrcharg": "The first argument of the `$location#search()` call must be a string or an object.", "nobase": "$location in HTML5 mode requires a <base> tag to be present!"}, "ng": {"areq": "Argument '{0}' is {1}", "test": "no injector found for element argument to getTestability", "cpws": "Can't copy! Making copies of Window or Scope instances is not supported.", "btstrpd": "App Already Bootstrapped with this Element '{0}'", "cpi": "Can't copy! Source and destination are identical.", "badname": "hasOwnProperty is not a valid {0} name"}, "$cacheFactory": {"iid": "CacheId '{0}' is already taken!"}, "$interpolate": {"noconcat": "Error while interpolating: {0}\nStrict Contextual Escaping disallows interpolations that concatenate multiple expressions when a trusted value is required.  See http://docs.angularjs.org/api/ng.$sce", "interr": "Can't interpolate: {0}\n{1}"}, "ngOptions": {"iexp": "Expected expression in form of '_select_ (as _label_)? for (_key_,)?_value_ in _collection_' but got '{0}'. Element: {1}"}, "$rootScope": {"inprog": "{0} already in progress", "infdig": "{0} $digest() iterations reached. Aborting!\nWatchers fired in the last 5 iterations: {1}"}, "$compile": {"selmulti": "Binding to the 'multiple' attribute is not supported. Element: {0}", "nodomevents": "Interpolations for HTML DOM event attributes are disallowed.  Please use the ng- versions (such as ng-click instead of onclick) instead.", "ctreq": "Controller '{0}', required by directive '{1}', can't be found!", "nonassign": "Expression '{0}' used with directive '{1}' is non-assignable!", "tplrt": "Template for directive '{0}' must have exactly one root element. {1}", "iscp": "Invalid isolate scope definition for directive '{0}'. Definition: {... {1}: '{2}' ...}", "multidir": "Multiple directives [{0}, {1}] asking for {2} on: {3}", "tpload": "Failed to load template: {0}", "uterdir": "Unterminated attribute, found '{0}' but no matching '{1}' found."}, "$resource": {"badargs": "Expected up to 4 arguments [params, data, success, error], got {0} arguments", "badmember": "Dotted member path \"@{0}\" is invalid.", "badname": "hasOwnProperty is not a valid parameter name.", "badcfg": "Error in resource configuration for action `{0}`. Expected response to contain an {1} but got an {2}"}, "$route": {"norout": "Tried updating route when with no current route"}, "$sanitize": {"badparse": "The sanitizer was unable to parse the following block of html: {0}"}}}