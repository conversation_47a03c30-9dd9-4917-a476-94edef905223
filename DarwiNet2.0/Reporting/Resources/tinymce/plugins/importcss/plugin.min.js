tinymce.PluginManager.add("importcss",function(a){function b(a){return"string"==typeof a?function(b){return-1!==b.indexOf(a)}:a instanceof RegExp?function(b){return a.test(b)}:a}function c(b,c){function d(a,b){var g,h=a.href;if(h&&c(h,b)){f(a.imports,function(a){d(a,!0)});try{g=a.cssRules||a.rules}catch(i){}f(g,function(a){a.styleSheet?d(a.styleSheet,!0):a.selectorText&&f(a.selectorText.split(","),function(a){e.push(tinymce.trim(a))})})}}var e=[],g={};f(a.contentCSS,function(a){g[a]=!0}),c||(c=function(a,b){return b||g[a]});try{f(b.styleSheets,function(a){d(a)})}catch(h){}return e}function d(b){var c,d=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(b);if(d){var e=d[1],f=d[2].substr(1).split(".").join(" "),g=tinymce.makeMap("a,img");return d[1]?(c={title:b},a.schema.getTextBlockElements()[e]?c.block=e:a.schema.getBlockElements()[e]||g[e.toLowerCase()]?c.selector=e:c.inline=e):d[2]&&(c={inline:"span",title:b.substr(1),classes:f}),a.settings.importcss_merge_classes!==!1?c.classes=f:c.attributes={"class":f},c}}var e=this,f=tinymce.each;a.on("renderFormatsMenu",function(g){var h=a.settings,i={},j=h.importcss_selector_converter||d,k=b(h.importcss_selector_filter),l=g.control;a.settings.importcss_append||l.items().remove();var m=[];tinymce.each(h.importcss_groups,function(a){a=tinymce.extend({},a),a.filter=b(a.filter),m.push(a)}),f(c(g.doc||a.getDoc(),b(h.importcss_file_filter)),function(b){if(-1===b.indexOf(".mce-")&&!i[b]&&(!k||k(b))){var c,d=j.call(e,b);if(d){var f=d.name||tinymce.DOM.uniqueId();if(m)for(var g=0;g<m.length;g++)if(!m[g].filter||m[g].filter(b)){m[g].item||(m[g].item={text:m[g].title,menu:[]}),c=m[g].item.menu;break}a.formatter.register(f,d);var h=tinymce.extend({},l.settings.itemDefaults,{text:d.title,format:f});c?c.push(h):l.add(h)}i[b]=!0}}),f(m,function(a){l.add(a.item)}),g.control.renderNew()}),e.convertSelectorToFormat=d});