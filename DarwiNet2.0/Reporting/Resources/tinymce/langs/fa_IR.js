tinymce.addI18n('fa_IR',{
"Cut": "\u0628\u0631\u0634",
"Heading 5": "\u0639\u0646\u0648\u0627\u0646 5",
"Header 2": "\u0633\u0631 \u0622\u0645\u062f 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u0645\u0631\u0648\u0631\u06af\u0631 \u0634\u0645\u0627 \u062f\u0633\u062a\u0631\u0633\u06cc \u0645\u0633\u062a\u0642\u06cc\u0645 \u0628\u0647 \u06a9\u0644\u06cc\u067e \u0628\u0648\u0631\u062f \u0631\u0627 \u067e\u0634\u062a\u06cc\u0628\u0627\u0646\u06cc \u0646\u0645\u06cc \u06a9\u0646\u062f\u060c \u0644\u0637\u0641\u0627 \u0627\u0632 \u0645\u06cc\u0627\u0646\u0628\u0631\u0647\u0627\u06cc  Ctrl+X\/C\/V  \u0635\u0641\u062d\u0647 \u06a9\u0644\u06cc\u062f \u0627\u0633\u062a\u0641\u0627\u062f\u0647 \u0646\u0645\u0627\u06cc\u06cc\u062f . ",
"Heading 4": "\u0639\u0646\u0648\u0627\u0646 4",
"Div": "\u0628\u0644\u0648\u06a9 \u062c\u062f\u0627 \u0633\u0627\u0632 (\u062a\u06af Div)",
"Heading 2": "\u0639\u0646\u0648\u0627\u0646 2",
"Paste": "\u0686\u0633\u0628\u0627\u0646\u062f\u0646",
"Close": "\u0628\u0633\u062a\u0646",
"Font Family": "\u0646\u0648\u0639 \u0642\u0644\u0645",
"Pre": "\u0628\u0644\u0648\u06a9 \u0645\u062a\u0646 \u0642\u0627\u0644\u0628 \u062f\u0627\u0631 (\u062a\u06af Pre)",
"Align right": "\u0631\u0627\u0633\u062a \u0686\u06cc\u0646",
"New document": "\u0633\u0646\u062f \u062c\u062f\u06cc\u062f",
"Blockquote": "\u0628\u0644\u0648\u06a9 \u0646\u0642\u0644 \u0642\u0648\u0644 (\u062a\u06af BlockQuote)",
"Numbered list": "\u0641\u0647\u0631\u0633\u062a \u0634\u0645\u0627\u0631\u0647 \u062f\u0627\u0631",
"Heading 1": "\u0639\u0646\u0648\u0627\u0646 1",
"Headings": "\u0639\u0646\u0627\u0648\u06cc\u0646",
"Increase indent": "\u0627\u0641\u0632\u0627\u06cc\u0634 \u062a\u0648\u0631\u0641\u062a\u06af\u06cc",
"Formats": "\u0642\u0627\u0644\u0628 \u0647\u0627",
"Headers": "\u0633\u0631 \u0622\u0645\u062f\u0647\u0627",
"Select all": "\u0627\u0646\u062a\u062e\u0627\u0628 \u0647\u0645\u0647",
"Header 3": "\u0633\u0631 \u0622\u0645\u062f 3",
"Blocks": "\u0628\u0644\u0648\u06a9 \u0647\u0627",
"Undo": "\u0628\u0627\u0632 \u06af\u0631\u062f\u0627\u0646",
"Strikethrough": "\u062e\u0637 \u062e\u0648\u0631\u062f\u0647",
"Bullet list": "\u0641\u0647\u0631\u0633\u062a \u0646\u0634\u0627\u0646\u0647 \u062f\u0627\u0631",
"Header 1": "\u0633\u0631 \u0622\u0645\u062f 1",
"Superscript": "\u0646\u0645\u0627",
"Clear formatting": "\u067e\u0627\u06a9 \u06a9\u0631\u062f\u0646 \u0642\u0627\u0644\u0628 \u0628\u0646\u062f\u06cc",
"Font Sizes": "\u0627\u0646\u062f\u0627\u0632\u0647\u0621 \u0642\u0644\u0645",
"Subscript": "\u067e\u0627\u06cc\u0647",
"Header 6": "\u0633\u0631 \u0622\u0645\u062f 6",
"Redo": "\u0628\u0627\u0632 \u0646\u0634\u0627\u0646",
"Paragraph": "\u067e\u0627\u0631\u0627\u06af\u0631\u0627\u0641 (\u062a\u06af P)",
"Ok": "\u062a\u0627\u06cc\u06cc\u062f",
"Bold": "\u062f\u0631\u0634\u062a",
"Code": "\u0628\u0644\u0648\u06a9 \u06a9\u062f\u0646\u0648\u06cc\u0633\u06cc (\u062a\u06a9 Code)",
"Italic": "\u06a9\u062c",
"Align center": "\u0648\u0633\u0637 \u0686\u06cc\u0646",
"Header 5": "\u0633\u0631 \u0622\u0645\u062f 5",
"Heading 6": "\u0639\u0646\u0648\u0627\u0646 6",
"Heading 3": "\u0639\u0646\u0648\u0627\u0646 3",
"Decrease indent": "\u06a9\u0627\u0647\u0634 \u062a\u0648\u0631\u0641\u062a\u06af\u06cc",
"Header 4": "\u0633\u0631 \u0622\u0645\u062f 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u0627\u0645\u06a9\u0627\u0646 \u0686\u0633\u0628\u0627\u0646\u062f\u0646\u060c \u062f\u0631 \u062d\u0627\u0644\u062a \u0645\u062a\u0646 \u062e\u0627\u0644\u0635 \u062a\u0646\u0638\u06cc\u0645 \u06af\u0634\u062a\u0647. \u062a\u0627 \u0632\u0645\u0627\u0646 \u062a\u063a\u06cc\u06cc\u0631 \u0627\u06cc\u0646 \u062d\u0627\u0644\u062a\u060c \u0645\u062d\u062a\u0648\u0627\u06cc \u0645\u0648\u0631\u062f \u0686\u0633\u0628\u0627\u0646\u062f\u0646\u060c \u0628\u0647 \u0635\u0648\u0631\u062a \u0645\u062a\u0646 \u062e\u0627\u0644\u0635 \u062e\u0648\u0627\u0647\u062f \u0686\u0633\u0628\u06cc\u062f.",
"Underline": "\u0632\u06cc\u0631 \u062e\u0637",
"Cancel": "\u0627\u0646\u0635\u0631\u0627\u0641",
"Justify": "\u062a\u0631\u0627\u0632 \u062f\u0648 \u0637\u0631\u0641\u0647",
"Inline": "\u0631\u0648 \u062e\u0637",
"Copy": "\u0631\u0648\u0646\u0648\u06cc\u0633\u06cc",
"Align left": "\u0686\u067e \u0686\u06cc\u0646",
"Visual aids": "\u06a9\u0645\u06a9 \u0628\u0635\u0631\u06cc",
"Lower Greek": "\u062d\u0631\u0648\u0641 \u06a9\u0648\u0686\u06a9 \u06cc\u0648\u0646\u0627\u0646\u06cc",
"Square": "\u0686\u0647\u0627\u0631 \u06af\u0648\u0634",
"Default": "\u067e\u06cc\u0634 \u0641\u0631\u0636",
"Lower Alpha": "\u062d\u0631\u0648\u0641 \u06a9\u0648\u0686\u06a9",
"Circle": "\u062f\u0627\u06cc\u0631\u0647",
"Disc": "\u062f\u0627\u06cc\u0631\u0647\u0621 \u062a\u0648\u067e\u0631",
"Upper Alpha": "\u062d\u0631\u0648\u0641 \u0628\u0632\u0631\u06af",
"Upper Roman": "\u0627\u0631\u0642\u0627\u0645 \u0628\u0632\u0631\u06af \u0631\u0648\u0645\u06cc",
"Lower Roman": "\u0627\u0631\u0642\u0627\u0645 \u06a9\u0648\u0686\u06a9 \u0631\u0648\u0645\u06cc",
"Name": "\u0646\u0627\u0645",
"Anchor": "\u0642\u0644\u0627\u0628",
"You have unsaved changes are you sure you want to navigate away?": "\u062a\u063a\u06cc\u06cc\u0631\u0627\u062a \u0634\u0645\u0627 \u0630\u062e\u06cc\u0631\u0647 \u0646\u0634\u062f\u0647 \u0627\u0646\u062f\u060c \u0622\u06cc\u0627 \u062c\u0647\u062a \u062e\u0631\u0648\u062c \u0627\u0637\u0645\u06cc\u0646\u0627\u0646 \u062f\u0627\u0631\u06cc\u062f\u061f",
"Restore last draft": "\u0628\u0627\u0632\u06cc\u0627\u0628\u06cc \u0622\u062e\u0631\u06cc\u0646 \u067e\u06cc\u0634 \u0646\u0648\u06cc\u0633",
"Special character": "\u0646\u0648\u06cc\u0633\u0647 \u0647\u0627\u06cc \u062e\u0627\u0635",
"Source code": "\u0645\u062a\u0646 \u06a9\u062f \u0645\u0646\u0628\u0639",
"B": "\u0622\u0628\u06cc",
"R": "\u0642\u0631\u0645\u0632",
"G": "\u0633\u0628\u0632",
"Color": "\u0631\u0646\u06af",
"Right to left": "\u0631\u0627\u0633\u062a \u0628\u0647 \u0686\u067e",
"Left to right": "\u0686\u067e \u0628\u0647 \u0631\u0627\u0633\u062a",
"Emoticons": "\u0635\u0648\u0631\u062a\u06a9 \u0647\u0627",
"Robots": "\u0631\u0648\u0628\u0627\u062a\u0647\u0627",
"Document properties": "\u062a\u0646\u0638\u06cc\u0645\u0627\u062a \u0633\u0646\u062f",
"Title": "\u0639\u0646\u0648\u0627\u0646",
"Keywords": "\u0648\u0627\u0698\u06af\u0627\u0646 \u06a9\u0644\u06cc\u062f\u06cc",
"Encoding": "\u06a9\u062f\u06af\u0632\u0627\u0631\u06cc \u0645\u062a\u0646",
"Description": "\u062a\u0648\u0636\u06cc\u062d",
"Author": "\u0645\u0648\u0644\u0641",
"Fullscreen": "\u062a\u0645\u0627\u0645 \u0635\u0641\u062d\u0647",
"Horizontal line": "\u062e\u0637 \u0627\u0641\u0642\u06cc",
"Horizontal space": "\u0641\u0636\u0627\u06cc \u0627\u0641\u0642\u06cc",
"Insert\/edit image": "\u062f\u0631\u062c\/\u0648\u06cc\u0631\u0627\u06cc\u0634 \u062a\u0635\u0648\u06cc\u0631",
"General": "\u0639\u0645\u0648\u0645\u06cc",
"Advanced": "\u067e\u06cc\u0634\u0631\u0641\u062a\u0647",
"Source": "\u0645\u0646\u0628\u0639",
"Border": "\u0644\u0628\u0647",
"Constrain proportions": "\u062d\u0641\u0638 \u062a\u0646\u0627\u0633\u0628",
"Vertical space": "\u0641\u0636\u0627\u06cc \u0639\u0645\u0648\u062f\u06cc",
"Image description": "\u062a\u0648\u0635\u06cc\u0641 \u062a\u0635\u0648\u06cc\u0631",
"Style": "\u0633\u0628\u06a9",
"Dimensions": "\u0627\u0628\u0639\u0627\u062f",
"Insert image": "\u062f\u0631\u062c \u062a\u0635\u0648\u06cc\u0631",
"Zoom in": "\u0628\u0632\u0631\u06af \u0646\u0645\u0627\u06cc\u06cc",
"Contrast": "\u062a\u0636\u0627\u062f \u0631\u0646\u06af",
"Back": "\u0628\u0627\u0632\u06af\u0634\u062a",
"Gamma": "\u06af\u0627\u0645\u0627",
"Flip horizontally": "\u0642\u0631\u06cc\u0646\u0647 \u0627\u0641\u0642\u06cc",
"Resize": "\u062a\u063a\u06cc\u06cc\u0631 \u0627\u0646\u062f\u0627\u0632\u0647",
"Sharpen": "\u0628\u0647\u0628\u0648\u062f \u0644\u0628\u0647",
"Zoom out": "\u06a9\u0648\u0686\u06a9 \u0646\u0645\u0627\u06cc\u06cc",
"Image options": "\u062a\u0646\u0638\u06cc\u0645\u0627\u062a \u062a\u0635\u0648\u06cc\u0631",
"Apply": "\u0627\u0650\u0639\u0645\u0627\u0644",
"Brightness": "\u0631\u0648\u0634\u0646\u0627\u06cc\u06cc",
"Rotate clockwise": "\u062f\u064e\u0648\u064e\u0631\u0627\u0646 \u0633\u0627\u0639\u062a \u06af\u0631\u062f",
"Rotate counterclockwise": "\u062f\u064e\u0648\u064e\u0631\u0627\u0646 \u067e\u0627\u062f \u0633\u0627\u0639\u062a \u06af\u0631\u062f",
"Edit image": "\u0648\u06cc\u0631\u0627\u0633\u062a \u062a\u0635\u0648\u06cc\u0631",
"Color levels": "\u0633\u0637\u0648\u062d \u0631\u0646\u06af",
"Crop": "\u0628\u064f\u0631\u0634 \u062f\u064f\u0648\u0631",
"Orientation": "\u06af\u0650\u0631\u0627",
"Flip vertically": "\u0642\u0631\u06cc\u0646\u0647 \u0639\u0645\u0648\u062f\u06cc",
"Invert": "\u0628\u0631\u06af\u0634\u062a \u0631\u0646\u06af",
"Insert date\/time": "\u062f\u0631\u062c \u062a\u0627\u0631\u06cc\u062e\/\u0632\u0645\u0627\u0646",
"Remove link": "\u062d\u0630\u0641 \u067e\u06cc\u0648\u0646\u062f",
"Url": "\u0622\u062f\u0631\u0633",
"Text to display": "\u0645\u062a\u0646 \u0646\u0645\u0627\u06cc\u0634\u06cc",
"Anchors": "\u0642\u0644\u0627\u0628 \u0647\u0627",
"Insert link": "\u062f\u0631\u062c \u067e\u06cc\u0648\u0646\u062f",
"New window": "\u067e\u0646\u062c\u0631\u0647\u0621 \u062c\u062f\u06cc\u062f",
"None": "\u0647\u06cc\u0686",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u0628\u0647 \u0646\u0638\u0631 \u0645\u06cc \u0631\u0633\u062f \u0622\u062f\u0631\u0633 \u0648\u0631\u0648\u062f\u06cc \u0627\u0631\u062c\u0627\u0639\u06cc \u0628\u0647 \u062e\u0627\u0631\u062c \u0627\u0632 \u0627\u06cc\u0646 \u0633\u0627\u06cc\u062a \u0645\u06cc \u0628\u0627\u0634\u062f. \u0622\u06cc\u0627 \u062a\u0645\u0627\u06cc\u0644 \u0628\u0647 \u0627\u0641\u0632\u0648\u0631\u062f\u0646 \u067e\u06cc\u0634\u0648\u0646\u062f http:\/\/ \u062f\u0627\u0631\u06cc\u062f\u061f",
"Target": "\u0645\u0642\u0635\u062f",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u0628\u0647 \u0646\u0638\u0631 \u0645\u06cc \u0631\u0633\u062f \u0622\u062f\u0631\u0633 \u0648\u0631\u0648\u062f\u06cc \u06cc\u06a9 \u0631\u0627\u06cc\u0627\u0646\u0627\u0645\u0647 \u0628\u0627\u0634\u062f. \u0622\u06cc\u0627 \u062a\u0645\u0627\u06cc\u0644 \u0628\u0647 \u0627\u0641\u0632\u0648\u0631\u062f\u0646 \u067e\u06cc\u0634\u0648\u0646\u062f mailto: \u062f\u0627\u0631\u06cc\u062f\u061f",
"Insert\/edit link": "\u062f\u0631\u062c\/\u0648\u06cc\u0631\u0627\u06cc\u0634 \u067e\u06cc\u0648\u0646\u062f",
"Insert\/edit video": "\u062f\u0631\u062c\/\u0648\u06cc\u0631\u0627\u06cc\u0634 \u0648\u06cc\u062f\u06cc\u0648",
"Poster": "\u067e\u0648\u0633\u062a\u0631",
"Alternative source": "\u0645\u0646\u0628\u0639 \u062c\u0627\u06cc\u06af\u0632\u06cc\u0646",
"Paste your embed code below:": "\u0686\u0633\u0628\u0627\u0646\u062f\u0646 \u06a9\u062f \u062c\u0627\u0633\u0627\u0632\u06cc \u0634\u0645\u0627 \u062f\u0631 \u0632\u06cc\u0631: ",
"Insert video": "\u062f\u0631\u062c \u0648\u06cc\u062f\u06cc\u0648",
"Embed": "\u062c\u0627\u0633\u0627\u0632\u06cc",
"Nonbreaking space": "\u0641\u0636\u0627\u06cc \u062e\u0627\u0644\u06cc \u0628\u0631\u0634 \u0646\u0627\u067e\u0630\u06cc\u0631",
"Page break": "\u0628\u0631\u0634 \u0635\u0641\u062d\u0647",
"Paste as text": "\u0686\u0633\u0628\u0627\u0646\u062f\u0646 \u0645\u062a\u0646",
"Preview": "\u067e\u06cc\u0634 \u0646\u0645\u0627\u06cc\u0634",
"Print": "\u0686\u0627\u067e",
"Save": "\u0630\u062e\u06cc\u0631\u0647",
"Could not find the specified string.": "\u0631\u0634\u062a\u0647\u0621 \u0645\u0648\u0631\u062f \u0646\u0638\u0631 \u06cc\u0627\u0641\u062a \u0646\u06af\u0631\u062f\u06cc\u062f.",
"Replace": "\u062c\u0627\u06cc\u06af\u0632\u06cc\u0646\u06cc",
"Next": "\u0628\u0639\u062f\u06cc",
"Whole words": "\u062a\u0645\u0627\u0645 \u0648\u0627\u0698\u06af\u0627\u0646",
"Find and replace": "\u062c\u0633\u062a\u062c\u0648 \u0648 \u062c\u0627\u06cc\u06af\u0632\u06cc\u0646\u06cc",
"Replace with": "\u062c\u0627\u06cc\u06af\u0632\u06cc\u0646\u06cc \u0628\u0627",
"Find": "\u062c\u0633\u062a\u062c\u0648",
"Replace all": "\u062c\u0627\u06cc\u06af\u0632\u06cc\u0646 \u0647\u0645\u0647",
"Match case": "\u062a\u0637\u0627\u0628\u0642 \u062d\u0631\u0648\u0641",
"Prev": "\u0642\u0628\u0644\u06cc",
"Spellcheck": "\u0628\u0631\u0631\u0633\u06cc \u0627\u0645\u0644\u0627\u0621",
"Finish": "\u0627\u062a\u0645\u0627\u0645",
"Ignore all": "\u0628\u06cc \u062e\u06cc\u0627\u0644 \u0647\u0645\u0647",
"Ignore": "\u0628\u06cc \u062e\u06cc\u0627\u0644",
"Add to Dictionary": "\u0628\u0647 \u0648\u0627\u0698\u0647 \u0646\u0627\u0645\u0647 \u0628\u06cc \u0627\u0641\u0632\u0627",
"Insert row before": "\u062f\u0631\u062c \u0633\u0637\u0631 \u062f\u0631 \u0628\u0627\u0644\u0627",
"Rows": "\u0633\u0637\u0631 \u0647\u0627",
"Height": "\u0627\u0631\u062a\u0641\u0627\u0639",
"Paste row after": "\u0686\u0633\u0628\u0627\u0646\u062f\u0646 \u0633\u0637\u0631 \u062f\u0631 \u067e\u0627\u06cc\u06cc\u0646",
"Alignment": "\u0647\u0645 \u062a\u0631\u0627\u0632\u06cc",
"Border color": "\u0631\u0646\u06af \u0644\u0628\u0647",
"Column group": "\u06af\u0631\u0648\u0647 \u0633\u062a\u0648\u0646\u06cc",
"Row": "\u0633\u0637\u0631",
"Insert column before": "\u062f\u0631\u062c \u0633\u062a\u0648\u0646 \u0642\u0628\u0644",
"Split cell": "\u062c\u062f\u0627 \u0633\u0627\u0632\u06cc \u0633\u0644\u0648\u0644",
"Cell padding": "\u062d\u0627\u0634\u06cc\u0647 \u062f\u0631\u0648\u0646 \u0633\u0644\u0648\u0644\u06cc",
"Cell spacing": "\u0641\u0627\u0635\u0644\u0647 \u0645\u06cc\u0627\u0646 \u0633\u0644\u0648\u0644\u06cc",
"Row type": "\u0646\u0648\u0639 \u0633\u0637\u0631",
"Insert table": "\u062f\u0631\u062c \u062c\u062f\u0648\u0644",
"Body": "\u0628\u062f\u0646\u0647",
"Caption": "\u0639\u0646\u0648\u0627\u0646",
"Footer": "\u067e\u0627 \u0646\u0648\u0634\u062a",
"Delete row": "\u062d\u0630\u0641 \u0633\u0637\u0631",
"Paste row before": "\u0686\u0633\u0628\u0627\u0646\u062f\u0646 \u0633\u0637\u0631 \u062f\u0631 \u0628\u0627\u0644\u0627",
"Scope": "\u062d\u0648\u0632\u0647",
"Delete table": "\u062d\u0630\u0641 \u062c\u062f\u0648\u0644",
"H Align": "\u062a\u0631\u0627\u0632 \u0627\u0641\u0642\u06cc",
"Top": "\u0628\u0627\u0644\u0627",
"Header cell": "\u0633\u0644\u0648\u0644 \u0633\u0631 \u0633\u062a\u0648\u0646",
"Column": "\u0633\u062a\u0648\u0646",
"Row group": "\u06af\u0631\u0648\u0647 \u0633\u0637\u0631\u06cc",
"Cell": "\u0633\u0644\u0648\u0644",
"Middle": "\u0645\u06cc\u0627\u0646\u0647",
"Cell type": "\u0646\u0648\u0639 \u0633\u0644\u0648\u0644",
"Copy row": "\u0631\u0648\u0646\u0648\u06cc\u0633\u06cc \u0633\u0637\u0631",
"Row properties": "\u062a\u0646\u0638\u06cc\u0645\u0627\u062a \u0633\u0637\u0631",
"Table properties": "\u062a\u0646\u0638\u06cc\u0645\u0627\u062a \u062c\u062f\u0648\u0644",
"Bottom": "\u067e\u0627\u06cc\u06cc\u0646",
"V Align": "\u062a\u0631\u0627\u0632 \u0639\u0645\u0648\u062f\u06cc",
"Header": "\u0633\u0631 \u0622\u0645\u062f",
"Right": "\u0631\u0627\u0633\u062a",
"Insert column after": "\u062f\u0631\u062c \u0633\u062a\u0648\u0646 \u0628\u0639\u062f",
"Cols": "\u0633\u062a\u0648\u0646 \u0647\u0627",
"Insert row after": "\u062f\u0631\u062c \u0633\u0637\u0631 \u062f\u0631 \u067e\u0627\u06cc\u06cc\u0646",
"Width": "\u0639\u0631\u0636",
"Cell properties": "\u062a\u0646\u0638\u06cc\u0645\u0627\u062a \u0633\u0644\u0648\u0644",
"Left": "\u0686\u067e",
"Cut row": "\u0628\u0631\u0634 \u0633\u0637\u0631",
"Delete column": "\u062d\u0630\u0641 \u0633\u062a\u0648\u0646",
"Center": "\u0645\u06cc\u0627\u0646\u0647",
"Merge cells": "\u067e\u06cc\u0648\u0646\u062f \u0633\u0644\u0648\u0644 \u0647\u0627",
"Insert template": "\u062f\u0631\u062c \u0627\u0644\u06af\u0648",
"Templates": "\u0627\u0644\u06af\u0648\u0647\u0627",
"Background color": "\u0631\u0646\u06af \u067e\u0633 \u0632\u0645\u06cc\u0646\u0647",
"Custom...": "\u062f\u0644\u062e\u0648\u0627\u0647...",
"Custom color": "\u0631\u0646\u06af \u062f\u0644\u062e\u0648\u0627\u0647",
"No color": "\u0628\u062f\u0648\u0646 \u0631\u0646\u06af",
"Text color": "\u0631\u0646\u06af \u0645\u062a\u0646",
"Show blocks": "\u0646\u0645\u0627\u06cc\u0634 \u0628\u0644\u0648\u06a9 \u0647\u0627",
"Show invisible characters": "\u0646\u0645\u0627\u06cc\u0634 \u0646\u0648\u06cc\u0633\u0647 \u0647\u0627\u06cc \u0646\u0627\u067e\u06cc\u062f\u0627",
"Words: {0}": "\u0648\u0627\u0698\u0647 \u0647\u0627: {0}",
"Insert": "\u062f\u0631\u062c",
"File": "\u067e\u0631\u0648\u0646\u062f\u0647",
"Edit": "\u0648\u06cc\u0631\u0627\u06cc\u0634",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u0646\u0627\u062d\u06cc\u0647 \u0645\u062a\u0646 \u063a\u0646\u06cc.\n\u062c\u0647\u062a \u0645\u0634\u0627\u0647\u062f\u0647 \u0645\u0646\u0648 \u0627\u0632 \u06a9\u0644\u06cc\u062f\u0647\u0627\u06cc \u062a\u0631\u06a9\u06cc\u0628\u06cc ALT + F9 \u0627\u0633\u062a\u0641\u0627\u062f\u0647 \u0646\u0645\u0627\u06cc\u06cc\u062f.\n\u062c\u0647\u062a \u0645\u0634\u0627\u0647\u062f\u0647 \u0646\u0648\u0627\u0631 \u0627\u0628\u0632\u0627\u0631 \u0627\u0632 \u06a9\u0644\u06cc\u062f\u0647\u0627\u06cc \u062a\u0631\u06a9\u06cc\u0628\u06cc ALT + F10 \u0627\u0633\u062a\u0641\u0627\u062f\u0647 \u0646\u0645\u0627\u06cc\u06cc\u062f.\n\u062c\u0647\u062a \u0645\u0634\u0627\u0647\u062f\u0647 \u0631\u0627\u0647\u0646\u0645\u0627 \u0627\u0632 \u06a9\u0644\u06cc\u062f\u0647\u0627\u06cc \u062a\u0631\u06a9\u06cc\u0628\u06cc ALT + 0 \u0627\u0633\u062a\u0641\u0627\u062f\u0647 \u0646\u0645\u0627\u06cc\u06cc\u062f.",
"Tools": "\u0627\u0628\u0632\u0627\u0631\u0647\u0627",
"View": "\u0646\u0645\u0627\u06cc\u0634",
"Table": "\u062c\u062f\u0648\u0644",
"Format": "\u0642\u0627\u0644\u0628",
"_dir": "rtl"
});