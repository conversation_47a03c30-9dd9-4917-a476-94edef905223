tinymce.addI18n('si_LK',{
"Cut": "\u0d9a\u0db4\u0db1\u0dca\u0db1",
"Header 2": "Header 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u0d9c\u0dca\u200d\u0dbb\u0dcf\u0dc4\u0d9a \u0db4\u0dd4\u0dc0\u0dbb\u0dd4\u0dc0\u0da7 \u0d8d\u0da2\u0dd4 \u0db4\u0dca\u200d\u0dbb\u0dc0\u0dda\u0dc1\u0dba\u0d9a\u0dca \u0dbd\u0db6\u0dcf\u0daf\u0dd3\u0db8\u0da7 \u0d94\u0db6\u0d9c\u0dda \u0db6\u0dca\u200d\u0dbb\u0dc0\u0dd4\u0dc3\u0dbb\u0dba \u0dc3\u0dc4\u0dba\u0d9a\u0dca \u0db1\u0ddc\u0daf\u0d9a\u0dca\u0dc0\u0dba\u0dd3. \u0d9a\u0dbb\u0dd4\u0dab\u0dcf\u0d9a\u0dbb \u0d92\u0dc0\u0dd9\u0db1\u0dd4\u0dc0\u0da7 Ctrl+X\/C\/V \u0dba\u0db1 \u0dba\u0dad\u0dd4\u0dbb\u0dd4\u0db4\u0dd4\u0dc0\u0dbb\u0dd4 \u0d9a\u0dd9\u0da7\u0dd2\u0db8\u0d9f \u0db7\u0dcf\u0dc0\u0dd2\u0dad\u0dcf \u0d9a\u0dbb\u0db1\u0dca\u0db1.",
"Div": "Div",
"Paste": "\u0d85\u0dbd\u0dc0\u0db1\u0dca\u0db1",
"Close": "\u0dc0\u0dc3\u0db1\u0dca\u0db1",
"Font Family": "Font Family",
"Pre": "Pre",
"Align right": "\u0daf\u0d9a\u0dd4\u0dab\u0dd4\u0db4\u0dc3\u0da7 \u0db4\u0dd9\u0dc5\u0d9c\u0dc3\u0dca\u0dc0\u0db1\u0dca\u0db1",
"New document": "\u0db1\u0dc0 \u0dbd\u0dda\u0d9b\u0db1\u0dba\u0d9a\u0dca",
"Blockquote": "Blockquote",
"Numbered list": "\u0d85\u0d82\u0d9a\u0db1\u0dba \u0d9a\u0dbd \u0dbd\u0dd0\u0dba\u0dd2\u0dc3\u0dca\u0dad\u0dd4\u0dc0",
"Increase indent": "\u0dc0\u0dd0\u0da9\u0dd2\u0dc0\u0db1 \u0d91\u0db6\u0dd4\u0db8",
"Formats": "\u0d86\u0d9a\u0dd8\u0dad\u0dd2",
"Headers": "Headers",
"Select all": "\u0dc3\u0dd2\u0dba\u0dbd\u0dca\u0dbd \u0dad\u0ddd\u0dbb\u0db1\u0dca\u0db1",
"Header 3": "Header 3",
"Blocks": "Blocks",
"Undo": "\u0db1\u0dd2\u0dc2\u0dca\u0db4\u0dca\u200d\u0dbb\u0db7\u0dcf \u0d9a\u0dbb\u0db1\u0dc0\u0dcf",
"Strikethrough": "\u0db8\u0dd0\u0daf\u0dd2 \u0d89\u0dbb\u0dd0\u0dad\u0dd2",
"Bullet list": "\u0dbd\u0dd0\u0dba\u0dd2\u0dc3\u0dca\u0dad\u0dd4\u0dc0",
"Header 1": "Header 1",
"Superscript": "\u0d8b\u0da9\u0dd4\u0dbd\u0d9a\u0dd4\u0dab\u0dd4",
"Clear formatting": "\u0db4\u0dd0\u0dc4\u0dd0\u0daf\u0dd2\u0dbd\u0dd2 \u0d86\u0d9a\u0dd8\u0dad\u0dd2\u0d9a\u0dbb\u0dab\u0dba",
"Font Sizes": "Font Sizes",
"Subscript": "\u0dba\u0da7\u0dd2\u0dbd\u0d9a\u0dd4\u0dab\u0dd4",
"Header 6": "Header 6",
"Redo": "\t\u0db1\u0dd0\u0dc0\u0dad \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Paragraph": "Paragraph",
"Ok": "\u0d85\u0db1\u0dd4\u0db8\u0dad \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Bold": "\u0db4\u0dd0\u0dc4\u0dd0\u0daf\u0dd2\u0dbd\u0dd2 \u0dc3\u0dda \u0db4\u0dd9\u0db1\u0dd9\u0db1",
"Code": "Code",
"Italic": "\u0d87\u0dbd\u0d9a\u0dd4\u0dbb\u0dd4",
"Align center": "\u0db8\u0dd0\u0daf\u0dd2 \u0d9a\u0ddc\u0da7 \u0db4\u0dd9\u0dc5\u0d9c\u0dc3\u0dca\u0dc0\u0db1\u0dca\u0db1",
"Header 5": "Header 5",
"Decrease indent": "\u0d85\u0da9\u0dd4\u0dc0\u0db1 \u0d91\u0db6\u0dd4\u0db8",
"Header 4": "Header 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.",
"Underline": "\u0dba\u0da7\u0dd2\u0db1\u0dca \u0d89\u0dbb\u0d9a\u0dca \u0d85\u0db3\u0dd2\u0db1\u0dca\u0db1",
"Cancel": "\u0d85\u0dc4\u0ddd\u0dc3\u0dd2 \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Justify": "\u0dc3\u0db8\u0dc0 \u0db4\u0dd9\u0dc5\u0d9c\u0dc3\u0dca\u0dc0\u0db1\u0dca\u0db1",
"Inline": "Inline",
"Copy": "\u0db4\u0dd2\u0da7\u0db4\u0dad\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Align left": "\u0dc0\u0db8\u0dca\u0db4\u0dc3\u0da7 \u0db4\u0dd9\u0dc5\u0d9c\u0dc3\u0dca\u0dc0\u0db1\u0dca\u0db1",
"Visual aids": "\u0daf\u0dd8\u0dc1\u0dca\u200d\u0dba\u0dcf\u0db0\u0dcf\u0dbb",
"Lower Greek": "\u0d9a\u0dd4\u0da9\u0dcf \u0d9c\u0dca\u200d\u0dbb\u0dd3\u0d9a ",
"Square": "\u0d9a\u0ddc\u0da7\u0dd4\u0dc0",
"Default": "\u0db4\u0dd9\u0dbb\u0db1\u0dd2\u0db8\u0dd2\u0dba ",
"Lower Alpha": "\u0d9a\u0dd4\u0da9\u0dcf \u0d87\u0dbd\u0dca\u0dc6\u0dcf ",
"Circle": "\u0dc0\u0d9a\u0dca\u200d\u0dbb\u0dba",
"Disc": "\u0dad\u0dd0\u0da7\u0dd2\u0dba ",
"Upper Alpha": "\u0dc0\u0dd2\u0dc1\u0dcf\u0dbd \u0d87\u0dbd\u0dca\u0dc6\u0dcf ",
"Upper Roman": "\u0dc0\u0dd2\u0dc1\u0dcf\u0dbd \u0dbb\u0ddd\u0db8\u0dcf\u0db1\u0dd4 ",
"Lower Roman": "\u0d9a\u0dd4\u0da9\u0dcf \u0dbb\u0ddd\u0db8\u0dcf\u0db1\u0dd4 ",
"Name": "\u0db1\u0dcf\u0db8\u0dba ",
"Anchor": "\u0d87\u0db1\u0dca\u0d9a\u0dbb\u0dba",
"You have unsaved changes are you sure you want to navigate away?": "\u0d94\u0db6\u0d9c\u0dda \u0dc3\u0dd4\u0dbb\u0d9a\u0dd2\u0db1 \u0db1\u0ddc\u0dbd\u0daf \u0dc0\u0dd9\u0db1\u0dc3\u0dca\u0d9a\u0dd2\u0dbb\u0dd3\u0db8\u0dca \u0d87\u0dad,\u0d94\u0db6\u0da7 \u0dc0\u0dd2\u0dc1\u0dca\u0dc0\u0dcf\u0dc3\u0daf \u0d89\u0dc0\u0dad\u0da7 \u0dba\u0dcf\u0dba\u0dd4\u0dad\u0dd4\u0dba\u0dd2 \u0d9a\u0dd2\u0dba\u0dcf?",
"Restore last draft": "\u0d85\u0dc0\u0dc3\u0dcf\u0db1\u0dba\u0da7 \u0db7\u0dcf\u0dc0\u0dd2\u0dad\u0dcf\u0d9a\u0dc5 \u0d9a\u0dd9\u0da7\u0dd4\u0db8\u0dca\u0db4\u0dad \u0db4\u0dd2\u0dc5\u0dd2\u0db1\u0d9c\u0db1\u0dca\u0db1 ",
"Special character": "\u0dc0\u0dd2\u0dc1\u0dda\u0dc2 \u0d85\u0db1\u0dd4\u0dbd\u0d9a\u0dd4\u0dab ",
"Source code": "\u0db8\u0dd6\u0dbd \u0d9a\u0dda\u0dad\u0dba ",
"Right to left": "\u0daf\u0d9a\u0dd4\u0dab\u0dd4\u0db4\u0dc3 \u0dc3\u0dd2\u0da7 \u0dc0\u0db8\u0dca\u0db4\u0dc3\u0da7 ",
"Left to right": "\u0dc0\u0db8\u0dca\u0db4\u0dc3 \u0dc3\u0dd2\u0da7 \u0daf\u0d9a\u0dd4\u0db1\u0dd4\u0db4\u0dc3\u0da7 ",
"Emoticons": "\u0db7\u0dcf\u0dc0 \u0db1\u0dd2\u0dbb\u0dd4\u0db4\u0d9a",
"Robots": "\u0dbb\u0ddc\u0db6\u0ddd",
"Document properties": "\u0dbd\u0dda\u0d9b\u0db1\u0dba\u0dda \u0d9c\u0dd4\u0dab\u0dcf\u0d82\u0d9c ",
"Title": "\u0db8\u0dcf\u0dad\u0dd8\u0d9a\u0dcf\u0dc0",
"Keywords": "\u0db8\u0dd6\u0dbd \u0db4\u0daf\u0dba ",
"Encoding": "\u0d9a\u0dda\u0dad\u0db1\u0dba",
"Description": "\u0dc0\u0dd2\u0dc3\u0dca\u0dad\u0dbb\u0dba ",
"Author": "\u0d9a\u0dad\u0dd8 ",
"Fullscreen": "\u0db4\u0dd6\u0dbb\u0dca\u0dab \u0dad\u0dd2\u0dbb\u0dba ",
"Horizontal line": "\u0dad\u0dd2\u0dbb\u0dc3\u0dca \u0d89\u0dbb  ",
"Horizontal space": "\u0dad\u0dd2\u0dbb\u0dc3\u0dca \u0dc4\u0dd2\u0dc3\u0dca \u0d89\u0da9",
"Insert\/edit image": "\u0db4\u0dd2\u0db1\u0dca\u0dad\u0dd4\u0dbb\u0dba \u0d87\u0dad\u0dd4\u0dbd\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1 \/ \u0dc3\u0d9a\u0dc3\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1 ",
"General": "\u0db4\u0ddc\u0daf\u0dd4",
"Advanced": "\u0db4\u0dca\u200d\u0dbb\u0d9c\u0dad",
"Source": "\u0db8\u0dd6\u0dbd\u0dba  ",
"Border": "\u0dc3\u0dd3\u0db8\u0dcf\u0dc0 ",
"Constrain proportions": "\u0dc3\u0d82\u0dbb\u0ddd\u0daf\u0d9a \u0db4\u0dca\u200d\u0dbb\u0db8\u0dcf\u0dab\u0db1",
"Vertical space": "\u0dc3\u0dd2\u0dbb\u0dc3\u0dca \u0dc4\u0dd2\u0dc3\u0dca \u0d89\u0da9",
"Image description": "\u0db4\u0dd2\u0db1\u0dca\u0dad\u0dd4\u0dbb\u0dba\u0dda \u0dc0\u0dd2\u0dc3\u0dca\u0dad\u0dbb\u0dba ",
"Style": "\u0dc0\u0dd2\u0dbd\u0dcf\u0dc3\u0dba",
"Dimensions": "\u0db8\u0dcf\u0db1",
"Insert image": "Insert image",
"Insert date\/time": "\u0daf\u0dd2\u0db1\u0dba \/ \u0dc0\u0dda\u0dbd\u0dcf\u0dc0 \u0d87\u0dad\u0dd4\u0dbd\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Remove link": "Remove link",
"Url": "Url",
"Text to display": "\u0db4\u0dd9\u0dc5 - \u0dc3\u0d82\u0daf\u0dbb\u0dca\u0dc1\u0d9a\u0dba",
"Anchors": "Anchors",
"Insert link": "\u0dc3\u0db6\u0dd0\u0db3\u0dd2\u0dba \u0d87\u0dad\u0dd4\u0dbd\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1",
"New window": "\u0db1\u0dc0 \u0d9a\u0dc0\u0dd4\u0dc5\u0dd4\u0dc0\u0d9a\u0dca",
"None": "\u0d9a\u0dd2\u0dc3\u0dd2\u0dc0\u0d9a\u0dca \u0db1\u0dd0\u0dad",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?",
"Target": "\u0d89\u0dbd\u0d9a\u0dca\u0d9a\u0dba",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",
"Insert\/edit link": "\u0dc3\u0db6\u0dd0\u0db3\u0dd2\u0dba \u0d87\u0dad\u0dd4\u0dbd\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1 \/ \u0dc0\u0dd9\u0db1\u0dc3\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Insert\/edit video": "\u0dc0\u0dd3\u0da9\u0dd2\u0dba\u0ddd\u0dc0 \u0d87\u0dad\u0dd4\u0dbd\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1 \/ \u0dc0\u0dd9\u0db1\u0dc3\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Poster": "\u0db4\u0ddd\u0dc3\u0dca\u0da7\u0dbb\u0dba",
"Alternative source": "\u0dc0\u0dd2\u0d9a\u0dbd\u0dca\u0db4 \u0db8\u0dd6\u0dbd\u0dba",
"Paste your embed code below:": "\u0d94\u0db6\u0d9c\u0dda \u0d9a\u0dcf\u0dc0\u0dd0\u0daf\u0dca\u0daf\u0dd6 \u0d9a\u0dda\u0dad\u0dba \u0db4\u0dc4\u0dad\u0dd2\u0db1\u0dca \u0daf\u0db8\u0db1\u0dca\u0db1",
"Insert video": "\u0dc0\u0dd3\u0da9\u0dd2\u0dba\u0ddd\u0dc0 \u0d87\u0dad\u0dd4\u0dbd\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Embed": "\u0d9a\u0dcf\u0dc0\u0daf\u0dca\u0daf\u0db1\u0dca\u0db1",
"Nonbreaking space": "\u0db1\u0ddc\u0d9a\u0dd0\u0da9\u0dd4\u0dab\u0dd4 \u0dc4\u0dd2\u0dc3\u0dca \u0d89\u0dbb",
"Page break": "\u0db4\u0dd2\u0da7\u0dd4 \u0d9a\u0da9\u0db1\u0dba",
"Paste as text": "Paste as text",
"Preview": "\u0db4\u0dd9\u0dbb\u0daf\u0dc3\u0dd4\u0db1",
"Print": "\u0db8\u0dd4\u0daf\u0dca\u200d\u0dbb\u0dab\u0dba \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Save": "\u0dc3\u0dd4\u0dbb\u0d9a\u0dd2\u0db1\u0dca\u0db1",
"Could not find the specified string.": "\u0db1\u0dd2\u0dbb\u0dd6\u0db4\u0dd2\u0dad \u0d85\u0db1\u0dd4\u0dbd\u0d9a\u0dd4\u0dab\u0dd4 \u0dc0\u0dd0\u0dbd \u0dc3\u0ddc\u0dba\u0dcf \u0d9c\u0dad \u0db1\u0ddc\u0dc4\u0dd0\u0d9a\u0dd2 \u0dc0\u0dd2\u0dba",
"Replace": "\u0db4\u0dca\u200d\u0dbb\u0dad\u0dd2\u0dc3\u0dca\u0dae\u0dcf\u0db4\u0db1\u0dba \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Next": "\u0db4\u0dc3\u0dd4",
"Whole words": "\u0dc3\u0db8\u0dc3\u0dca\u0dad \u0db4\u0daf",
"Find and replace": "\u0dc3\u0ddc\u0dba\u0dcf \u0db4\u0dc3\u0dd4\u0dc0 \u0db4\u0dca\u200d\u0dbb\u0dad\u0dd2\u0dc3\u0dca\u0dae\u0dcf\u0db4\u0db1\u0dba \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Replace with": "\u0db8\u0dd9\u0dba \u0dc3\u0db8\u0d9f \u0db4\u0dca\u200d\u0dbb\u0dad\u0dd2\u0dc3\u0dca\u0dae\u0dcf\u0db4\u0db1\u0dba \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Find": "\u0dc3\u0ddc\u0dba\u0db1\u0dca\u0db1",
"Replace all": "\u0dc3\u0dd2\u0dba\u0dbd\u0dca\u0dbd\u0db8 \u0db4\u0dca\u200d\u0dbb\u0dad\u0dd2\u0dc3\u0dca\u0dae\u0dcf\u0db4\u0db1\u0dba \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Match case": "\u0d9a\u0dcf\u0dbb\u0dab\u0dba \u0d9c\u0dbd\u0db4\u0db1\u0dca\u0db1",
"Prev": "\u0db4\u0dd9\u0dbb",
"Spellcheck": "\u0d85\u0d9a\u0dca\u0dc2\u0dbb \u0dc0\u0dd2\u0db1\u0dca\u200d\u0dba\u0dcf\u0dc3\u0dba \u0db4\u0dbb\u0dd3\u0d9a\u0dca\u0dc2\u0dcf \u0d9a\u0dbb \u0db6\u0dd0\u0dbd\u0dd3\u0db8",
"Finish": "\u0d85\u0dc0\u0dc3\u0db1\u0dca",
"Ignore all": "\u0dc3\u0dd2\u0dba\u0dbd\u0dca\u0dbd\u0db8 \u0db1\u0ddc\u0dc3\u0dbd\u0d9a\u0dcf \u0dc4\u0dbb\u0dd2\u0db1\u0dca\u0db1",
"Ignore": "\u0db1\u0ddc\u0dc3\u0dbd\u0d9a\u0dcf \u0dc4\u0dd0\u0dbb\u0dd3\u0db8",
"Insert row before": "\u0db8\u0dda \u0dad\u0dd0\u0db1\u0da7 \u0db4\u0dd9\u0dbb \u0db4\u0dda\u0dc5\u0dd2\u0dba\u0d9a\u0dca \u0d91\u0d9a\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Rows": "\u0db4\u0dda\u0dc5\u0dd2",
"Height": "\u0d8b\u0dc3 ",
"Paste row after": "\u0db8\u0dda \u0dad\u0dd0\u0db1\u0da7 \u0db4\u0dc3\u0dd4 \u0db4\u0dda\u0dc5\u0dd2\u0dba \u0d85\u0db8\u0dd4\u0dab\u0db1\u0dca\u0db1 ",
"Alignment": "\u0db4\u0dd9\u0dc5 \u0d9c\u0dd0\u0dc3\u0dd4\u0db8",
"Column group": "\u0dad\u0dd3\u0dbb\u0dd4 \u0d9a\u0dcf\u0dab\u0dca\u0da9\u0dba",
"Row": "\u0db4\u0dda\u0dc5\u0dd2\u0dba ",
"Insert column before": "\u0db8\u0dda \u0dad\u0dd0\u0db1\u0da7 \u0db4\u0dd9\u0dbb \u0dad\u0dd3\u0dbb\u0dd4\u0dc0 \u0d91\u0d9a\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Split cell": "\u0d9a\u0ddc\u0da7\u0dd4 \u0dc0\u0dd9\u0db1\u0dca\u0d9a\u0dbb\u0db1\u0dca\u0db1 ",
"Cell padding": "\u0d9a\u0ddc\u0da7\u0dd4\u0dc0\u0dd9\u0dc4\u0dd2 \u0db4\u0dd2\u0dbb\u0dc0\u0dd4\u0db8",
"Cell spacing": "\u0d9a\u0ddc\u0da7\u0dd4\u0dc0\u0dd9\u0dc4\u0dd2 \u0d89\u0da9 \u0dc3\u0dd3\u0db8\u0dcf\u0dc0 ",
"Row type": "\u0db4\u0dda\u0dc5\u0dd2\u0dba\u0dd9\u0dc4\u0dd2 \u0dc0\u0dbb\u0dca\u0d9c\u0dba",
"Insert table": "\u0dc0\u0d9c\u0dd4\u0dc0\u0da7 \u0d87\u0dad\u0dd4\u0dbd\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1 ",
"Body": "\u0db4\u0dca\u200d\u0dbb\u0db0\u0dcf\u0db1 \u0d9a\u0ddc\u0da7\u0dc3",
"Caption": "\u0dba\u0da7\u0dd2 \u0dbd\u0dd2\u0dba\u0db8\u0db1 ",
"Footer": "\u0db4\u0dcf\u0daf\u0d9a\u0dba",
"Delete row": "\u0db4\u0dda\u0dc5\u0dd2\u0dba \u0db8\u0d9a\u0db1\u0dca\u0db1 ",
"Paste row before": "\u0db8\u0dda \u0dad\u0dd0\u0db1\u0da7 \u0db4\u0dd9\u0dbb \u0db4\u0dda\u0dc5\u0dd2\u0dba \u0d85\u0db8\u0dd4\u0dab\u0db1\u0dca\u0db1 ",
"Scope": "\u0dc0\u0dd2\u0dc2\u0dba\u0db4\u0dae\u0dba",
"Delete table": "\u0dc0\u0d9c\u0dd4\u0dc0 \u0db8\u0d9a\u0db1\u0dca\u0db1 ",
"Header cell": "\u0dc1\u0dd3\u0dbb\u0dca\u0dc2 \u0d9a\u0ddc\u0da7\u0dd4\u0dc0",
"Column": "\u0dad\u0dd3\u0dbb\u0dd4\u0dc0",
"Cell": "\u0d9a\u0ddc\u0da7\u0dd4\u0dc0 ",
"Header": "\u0dc1\u0dd3\u0dbb\u0dca\u0dc2\u0d9a\u0dba",
"Cell type": "\u0d9a\u0ddc\u0da7\u0dd4\u0dc0\u0dd9\u0dc4\u0dd2 \u0dc0\u0dbb\u0dca\u0d9c\u0dba",
"Copy row": "\u0db4\u0dda\u0dc5\u0dd2\u0dba \u0db4\u0dd2\u0da7\u0db4\u0dad\u0dca \u0d9a\u0dbb\u0d9c\u0db1\u0dca\u0db1 ",
"Row properties": "\u0db4\u0dda\u0dc5\u0dd2\u0dba\u0dd9\u0dc4\u0dd2 \u0d9c\u0dd4\u0dab\u0dcf\u0d82\u0d9c ",
"Table properties": "\u0dc0\u0d9c\u0dd4\u0dc0\u0dd9\u0dc4\u0dd2 \u0d9c\u0dd4\u0dab\u0dcf\u0d82\u0d9c ",
"Row group": "\u0db4\u0dda\u0dc5\u0dd2 \u0d9a\u0dcf\u0dab\u0dca\u0da9\u0dba",
"Right": "\u0daf\u0d9a\u0dd4\u0dab",
"Insert column after": "\u0db8\u0dda \u0dad\u0dd0\u0db1\u0da7 \u0db4\u0dc3\u0dd4 \u0dad\u0dd3\u0dbb\u0dd4\u0dc0 \u0d91\u0d9a\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1 ",
"Cols": "\u0dad\u0dd3\u0dbb\u0dd4 ",
"Insert row after": "\u0db8\u0dda \u0dad\u0dd0\u0db1\u0da7 \u0db4\u0dc3\u0dd4 \u0db4\u0dda\u0dc5\u0dd2\u0dba\u0d9a\u0dca \u0d91\u0d9a\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1 ",
"Width": "\u0db4\u0dc5\u0dbd",
"Cell properties": "\u0d9a\u0ddc\u0da7\u0dd4\u0dc0\u0dd9\u0dc4\u0dd2 \u0d9c\u0dd4\u0dab\u0dcf\u0d82\u0d9c ",
"Left": "\u0dc0\u0db8",
"Cut row": "\u0db4\u0dda\u0dc5\u0dd2\u0dba \u0d9a\u0db4\u0dcf\u0d9c\u0db1\u0dca\u0db1 ",
"Delete column": "\u0dad\u0dd3\u0dbb\u0dd4\u0dc0 \u0db8\u0d9a\u0db1\u0dca\u0db1 ",
"Center": "\u0db8\u0dd0\u0daf",
"Merge cells": "\u0d9a\u0ddc\u0da7\u0dd4 \u0d91\u0d9a\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1 ",
"Insert template": "\u0d85\u0da0\u0dca\u0da0\u0dd4\u0dc0 \u0d87\u0dad\u0dd4\u0dbd\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"Templates": "\u0d85\u0da0\u0dca\u0da0\u0dd4",
"Background color": "\u0db4\u0dc3\u0dd4\u0db6\u0dd2\u0db8\u0dd9\u0dc4\u0dd2 \u0dc0\u0dbb\u0dca\u0dab\u0dba",
"Text color": "\u0db4\u0dd9\u0dc5 \u0dc3\u0da7\u0dc4\u0db1\u0dda \u0dc0\u0dbb\u0dca\u0dab\u0dba",
"Show blocks": "\u0d9a\u0ddc\u0da7\u0dc3\u0dca \u0db4\u0dd9\u0db1\u0dca\u0dc0\u0db1\u0dca\u0db1",
"Show invisible characters": "\u0db1\u0ddc\u0db4\u0dd9\u0db1\u0dd9\u0db1 \u0d85\u0db1\u0dd4\u0dbd\u0d9a\u0dd4\u0dab\u0dd4 \u0db4\u0dd9\u0db1\u0dca\u0dc0\u0db1\u0dca\u0db1",
"Words: {0}": "\u0dc0\u0da0\u0db1: {0}",
"Insert": "\u0d91\u0d9a\u0dca \u0d9a\u0dbb\u0db1\u0dca\u0db1",
"File": "\u0d9c\u0ddc\u0db1\u0dd4\u0dc0",
"Edit": "\u0dc3\u0d9a\u0dc3\u0db1\u0dca\u0db1",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u0db4\u0dd9\u0dc5 \u0dc3\u0da7\u0dc4\u0db1\u0dca \u0db6\u0dc4\u0dd4\u0dbd \u0db4\u0dca\u200d\u0dbb\u0daf\u0dda\u0dc1\u0dba. \u0db8\u0dd9\u0db1\u0dd4\u0dc0 \u0dc3\u0db3\u0dc4\u0dcf ALT-F9  \u0d94\u0db6\u0db1\u0dca\u0db1. \u0db8\u0dd9\u0dc0\u0dbd\u0db8\u0dca \u0dad\u0dd3\u0dbb\u0dd4\u0dc0 \u0dc3\u0db3\u0dc4\u0dcf ALT-F10  \u0d94\u0db6\u0db1\u0dca\u0db1. \u0dc3\u0dc4\u0dba \u0dbd\u0db6\u0dcf\u0d9c\u0dd0\u0db1\u0dd3\u0db8 \u0dc3\u0db3\u0dc4\u0dcf ALT-0  \u0d94\u0db6\u0db1\u0dca\u0db1.",
"Tools": "\u0db8\u0dd9\u0dc0\u0dbd\u0db8\u0dca",
"View": "\u0db4\u0dd9\u0db1\u0dca\u0dc0\u0db1\u0dca\u0db1",
"Table": "\u0dc0\u0d9c\u0dd4\u0dc0",
"Format": "\u0dc4\u0dd0\u0da9\u0dad\u0dbd\u0dba"
});