tinymce.addI18n('ja',{
"Cut": "\u5207\u308a\u53d6\u308a",
"Heading 5": "\u898b\u51fa\u3057 5",
"Header 2": "\u30d8\u30c3\u30c0\u30fc 2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u304a\u4f7f\u3044\u306e\u30d6\u30e9\u30a6\u30b6\u3067\u306f\u30af\u30ea\u30c3\u30d7\u30dc\u30fc\u30c9\u6a5f\u80fd\u3092\u5229\u7528\u3059\u308b\u3053\u3068\u304c\u3067\u304d\u307e\u305b\u3093\u3002\u30ad\u30fc\u30dc\u30fc\u30c9\u306e\u30b7\u30e7\u30fc\u30c8\u30ab\u30c3\u30c8\uff08Ctrl+X, Ctrl+C, Ctrl+V\uff09\u3092\u304a\u4f7f\u3044\u4e0b\u3055\u3044\u3002",
"Heading 4": "\u898b\u51fa\u3057 4",
"Div": "Div",
"Heading 2": "\u898b\u51fa\u3057 2",
"Paste": "\u8cbc\u308a\u4ed8\u3051",
"Close": "\u9589\u3058\u308b",
"Font Family": "\u30d5\u30a9\u30f3\u30c8\u30d5\u30a1\u30df\u30ea\u30fc",
"Pre": "Pre",
"Align right": "\u53f3\u5bc4\u305b",
"New document": "\u65b0\u898f\u30c9\u30ad\u30e5\u30e1\u30f3\u30c8",
"Blockquote": "\u5f15\u7528",
"Numbered list": "\u756a\u53f7\u4ed8\u304d\u7b87\u6761\u66f8\u304d",
"Heading 1": "\u898b\u51fa\u3057 1",
"Headings": "\u898b\u51fa\u3057",
"Increase indent": "\u30a4\u30f3\u30c7\u30f3\u30c8\u3092\u5897\u3084\u3059",
"Formats": "\u66f8\u5f0f",
"Headers": "\u30d8\u30c3\u30c0\u30fc",
"Select all": "\u5168\u3066\u3092\u9078\u629e",
"Header 3": "\u30d8\u30c3\u30c0\u30fc 3",
"Blocks": "\u30d6\u30ed\u30c3\u30af",
"Undo": "\u5143\u306b\u623b\u3059",
"Strikethrough": "\u53d6\u308a\u6d88\u3057\u7dda",
"Bullet list": "\u7b87\u6761\u66f8\u304d",
"Header 1": "\u30d8\u30c3\u30c0\u30fc 1",
"Superscript": "\u4e0a\u4ed8\u304d\u6587\u5b57",
"Clear formatting": "\u66f8\u5f0f\u3092\u30af\u30ea\u30a2",
"Font Sizes": "\u30d5\u30a9\u30f3\u30c8\u30b5\u30a4\u30ba",
"Subscript": "\u4e0b\u4ed8\u304d\u6587\u5b57",
"Header 6": "\u30d8\u30c3\u30c0\u30fc 6",
"Redo": "\u3084\u308a\u76f4\u3059",
"Paragraph": "\u6bb5\u843d",
"Ok": "OK",
"Bold": "\u592a\u5b57",
"Code": "\u30b3\u30fc\u30c9",
"Italic": "\u659c\u4f53",
"Align center": "\u4e2d\u592e\u63c3\u3048",
"Header 5": "\u30d8\u30c3\u30c0\u30fc 5",
"Heading 6": "\u898b\u51fa\u3057 6",
"Heading 3": "\u898b\u51fa\u3057 3",
"Decrease indent": "\u30a4\u30f3\u30c7\u30f3\u30c8\u3092\u6e1b\u3089\u3059",
"Header 4": "\u30d8\u30c3\u30c0\u30fc 4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u8cbc\u308a\u4ed8\u3051\u306f\u73fe\u5728\u30d7\u30ec\u30fc\u30f3\u30c6\u30ad\u30b9\u30c8\u30e2\u30fc\u30c9\u3067\u3059\u3002\u3053\u306e\u30aa\u30d7\u30b7\u30e7\u30f3\u3092\u30aa\u30d5\u306b\u3057\u306a\u3044\u9650\u308a\u5185\u5bb9\u306f\u30d7\u30ec\u30fc\u30f3\u30c6\u30ad\u30b9\u30c8\u3068\u3057\u3066\u8cbc\u308a\u4ed8\u3051\u3089\u308c\u307e\u3059\u3002",
"Underline": "\u4e0b\u7dda",
"Cancel": "\u30ad\u30e3\u30f3\u30bb\u30eb",
"Justify": "\u4e21\u7aef\u63c3\u3048",
"Inline": "\u30a4\u30f3\u30e9\u30a4\u30f3",
"Copy": "\u30b3\u30d4\u30fc",
"Align left": "\u5de6\u5bc4\u305b",
"Visual aids": "\u8868\u306e\u67a0\u7dda\u3092\u70b9\u7dda\u3067\u8868\u793a",
"Lower Greek": "\u5c0f\u6587\u5b57\u306e\u30ae\u30ea\u30b7\u30e3\u6587\u5b57",
"Square": "\u56db\u89d2",
"Default": "\u30c7\u30d5\u30a9\u30eb\u30c8",
"Lower Alpha": "\u5c0f\u6587\u5b57\u306e\u30a2\u30eb\u30d5\u30a1\u30d9\u30c3\u30c8",
"Circle": "\u5186",
"Disc": "\u70b9",
"Upper Alpha": "\u5927\u6587\u5b57\u306e\u30a2\u30eb\u30d5\u30a1\u30d9\u30c3\u30c8",
"Upper Roman": "\u5927\u6587\u5b57\u306e\u30ed\u30fc\u30de\u6570\u5b57",
"Lower Roman": "\u5c0f\u6587\u5b57\u306e\u30ed\u30fc\u30de\u6570\u5b57",
"Name": "\u30a2\u30f3\u30ab\u30fc\u540d",
"Anchor": "\u30a2\u30f3\u30ab\u30fc\uff08\u30ea\u30f3\u30af\u306e\u5230\u9054\u70b9\uff09",
"You have unsaved changes are you sure you want to navigate away?": "\u307e\u3060\u4fdd\u5b58\u3057\u3066\u3044\u306a\u3044\u5909\u66f4\u304c\u3042\u308a\u307e\u3059\u304c\u3001\u672c\u5f53\u306b\u3053\u306e\u30da\u30fc\u30b8\u3092\u96e2\u308c\u307e\u3059\u304b\uff1f",
"Restore last draft": "\u524d\u56de\u306e\u4e0b\u66f8\u304d\u3092\u5fa9\u6d3b\u3055\u305b\u308b",
"Special character": "\u7279\u6b8a\u6587\u5b57",
"Source code": "\u30bd\u30fc\u30b9\u30b3\u30fc\u30c9",
"B": "B",
"R": "R",
"G": "G",
"Color": "\u30ab\u30e9\u30fc",
"Right to left": "\u53f3\u304b\u3089\u5de6",
"Left to right": "\u5de6\u304b\u3089\u53f3",
"Emoticons": "\u7d75\u6587\u5b57",
"Robots": "\u30ed\u30dc\u30c3\u30c4",
"Document properties": "\u30c9\u30ad\u30e5\u30e1\u30f3\u30c8\u306e\u30d7\u30ed\u30d1\u30c6\u30a3",
"Title": "\u30bf\u30a4\u30c8\u30eb",
"Keywords": "\u30ad\u30fc\u30ef\u30fc\u30c9",
"Encoding": "\u30a8\u30f3\u30b3\u30fc\u30c7\u30a3\u30f3\u30b0",
"Description": "\u30c6\u30f3\u30d7\u30ec\u30fc\u30c8\u306e\u5185\u5bb9",
"Author": "\u8457\u8005",
"Fullscreen": "\u5168\u753b\u9762\u8868\u793a",
"Horizontal line": "\u6c34\u5e73\u7f6b\u7dda",
"Horizontal space": "\u6a2a\u65b9\u5411\u306e\u4f59\u767d",
"Insert\/edit image": "\u753b\u50cf\u306e\u633f\u5165\u30fb\u7de8\u96c6",
"General": "\u4e00\u822c",
"Advanced": "\u8a73\u7d30\u8a2d\u5b9a",
"Source": "\u753b\u50cf\u306e\u30bd\u30fc\u30b9",
"Border": "\u67a0\u7dda",
"Constrain proportions": "\u7e26\u6a2a\u6bd4\u3092\u4fdd\u6301\u3059\u308b",
"Vertical space": "\u7e26\u65b9\u5411\u306e\u4f59\u767d",
"Image description": "\u753b\u50cf\u306e\u8aac\u660e\u6587",
"Style": "\u30b9\u30bf\u30a4\u30eb",
"Dimensions": "\u753b\u50cf\u30b5\u30a4\u30ba\uff08\u6a2a\u30fb\u7e26\uff09",
"Insert image": "\u753b\u50cf\u306e\u633f\u5165",
"Zoom in": "\u30ba\u30fc\u30e0\u30a4\u30f3",
"Contrast": "\u30b3\u30f3\u30c8\u30e9\u30b9\u30c8",
"Back": "\u623b\u308b",
"Gamma": "\u30ac\u30f3\u30de",
"Flip horizontally": "\u6c34\u5e73\u306b\u53cd\u8ee2",
"Resize": "\u30ea\u30b5\u30a4\u30ba",
"Sharpen": "\u30b7\u30e3\u30fc\u30d7\u306b",
"Zoom out": "\u30ba\u30fc\u30e0\u30a2\u30a6\u30c8",
"Image options": "\u753b\u50cf\u30aa\u30d7\u30b7\u30e7\u30f3",
"Apply": "\u9069\u7528",
"Brightness": "\u660e\u308b\u3055",
"Rotate clockwise": "\u6642\u8a08\u56de\u308a\u306b\u56de\u8ee2",
"Rotate counterclockwise": "\u53cd\u6642\u8a08\u56de\u308a\u306b\u56de\u8ee2",
"Edit image": "\u753b\u50cf\u306e\u7de8\u96c6",
"Color levels": "\u30ab\u30e9\u30fc\u30ec\u30d9\u30eb",
"Crop": "\u30af\u30ed\u30c3\u30d7",
"Orientation": "\u5411\u304d",
"Flip vertically": "\u4e0a\u4e0b\u306b\u53cd\u8ee2",
"Invert": "\u53cd\u8ee2",
"Insert date\/time": "\u65e5\u4ed8\u30fb\u6642\u523b",
"Remove link": "\u30ea\u30f3\u30af\u306e\u524a\u9664",
"Url": "\u30ea\u30f3\u30af\u5148URL",
"Text to display": "\u30ea\u30f3\u30af\u5143\u30c6\u30ad\u30b9\u30c8",
"Anchors": "\u30a2\u30f3\u30ab\u30fc\uff08\u30ea\u30f3\u30af\u306e\u5230\u9054\u70b9\uff09",
"Insert link": "\u30ea\u30f3\u30af",
"New window": "\u65b0\u898f\u30a6\u30a3\u30f3\u30c9\u30a6",
"None": "\u306a\u3057",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u5165\u529b\u3055\u308c\u305fURL\u306f\u5916\u90e8\u30ea\u30f3\u30af\u306e\u3088\u3046\u3067\u3059\u3002\u300chttp:\/\/\u300d\u30d7\u30ec\u30d5\u30a3\u30c3\u30af\u30b9\u3092\u8ffd\u52a0\u3057\u307e\u3059\u304b\uff1f",
"Target": "\u30bf\u30fc\u30b2\u30c3\u30c8\u5c5e\u6027",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u5165\u529b\u3055\u308c\u305f\u5024\u306f\u30e1\u30fc\u30eb\u30a2\u30c9\u30ec\u30b9\u306e\u3088\u3046\u3067\u3059\u3002\u300cmailto:\u300d\u30d7\u30ec\u30d5\u30a3\u30c3\u30af\u30b9\u3092\u8ffd\u52a0\u3057\u307e\u3059\u304b\uff1f",
"Insert\/edit link": "\u30ea\u30f3\u30af\u306e\u633f\u5165\u30fb\u7de8\u96c6",
"Insert\/edit video": "\u52d5\u753b\u306e\u633f\u5165\u30fb\u7de8\u96c6",
"Poster": "\u4ee3\u66ff\u753b\u50cf\u306e\u5834\u6240",
"Alternative source": "\u4ee3\u66ff\u52d5\u753b\u306e\u5834\u6240",
"Paste your embed code below:": "\u57cb\u3081\u8fbc\u307f\u7528\u30b3\u30fc\u30c9\u3092\u4e0b\u8a18\u306b\u8cbc\u308a\u4ed8\u3051\u3066\u304f\u3060\u3055\u3044\u3002",
"Insert video": "\u52d5\u753b",
"Embed": "\u57cb\u3081\u8fbc\u307f",
"Nonbreaking space": "\u56fa\u5b9a\u30b9\u30da\u30fc\u30b9\uff08&nbsp;\uff09",
"Page break": "\u30da\u30fc\u30b8\u533a\u5207\u308a",
"Paste as text": "\u30c6\u30ad\u30b9\u30c8\u3068\u3057\u3066\u8cbc\u308a\u4ed8\u3051",
"Preview": "\u30d7\u30ec\u30d3\u30e5\u30fc",
"Print": "\u5370\u5237",
"Save": "\u4fdd\u5b58",
"Could not find the specified string.": "\u304a\u63a2\u3057\u306e\u6587\u5b57\u304c\u898b\u3064\u304b\u308a\u307e\u305b\u3093\u3067\u3057\u305f\u3002",
"Replace": "\u7f6e\u304d\u63db\u3048",
"Next": "\u6b21",
"Whole words": "\u5358\u8a9e\u5358\u4f4d\u3067\u691c\u7d22\u3059\u308b",
"Find and replace": "\u691c\u7d22\u3068\u7f6e\u304d\u63db\u3048",
"Replace with": "\u7f6e\u304d\u63db\u3048\u308b\u6587\u5b57",
"Find": "\u691c\u7d22",
"Replace all": "\u5168\u3066\u3092\u7f6e\u304d\u63db\u3048\u308b",
"Match case": "\u5927\u6587\u5b57\u30fb\u5c0f\u6587\u5b57\u3092\u533a\u5225\u3059\u308b",
"Prev": "\u524d",
"Spellcheck": "\u30b9\u30da\u30eb\u30c1\u30a7\u30c3\u30af",
"Finish": "\u7d42\u4e86",
"Ignore all": "\u5168\u3066\u3092\u7121\u8996",
"Ignore": "\u7121\u8996",
"Add to Dictionary": "\u8f9e\u66f8\u306b\u8ffd\u52a0",
"Insert row before": "\u4e0a\u5074\u306b\u884c\u3092\u633f\u5165",
"Rows": "\u884c\u6570",
"Height": "\u9ad8\u3055",
"Paste row after": "\u4e0b\u5074\u306b\u884c\u3092\u8cbc\u308a\u4ed8\u3051",
"Alignment": "\u914d\u7f6e",
"Border color": "\u67a0\u7dda\u306e\u8272",
"Column group": "\u5217\u30b0\u30eb\u30fc\u30d7",
"Row": "\u884c",
"Insert column before": "\u5de6\u5074\u306b\u5217\u3092\u633f\u5165",
"Split cell": "\u30bb\u30eb\u306e\u5206\u5272",
"Cell padding": "\u30bb\u30eb\u5185\u4f59\u767d\uff08\u30d1\u30c7\u30a3\u30f3\u30b0\uff09",
"Cell spacing": "\u30bb\u30eb\u306e\u9593\u9694",
"Row type": "\u884c\u30bf\u30a4\u30d7",
"Insert table": "\u8868\u306e\u633f\u5165",
"Body": "\u30dc\u30c7\u30a3\u30fc",
"Caption": "\u8868\u984c",
"Footer": "\u30d5\u30c3\u30bf\u30fc",
"Delete row": "\u884c\u306e\u524a\u9664",
"Paste row before": "\u4e0a\u5074\u306b\u884c\u3092\u8cbc\u308a\u4ed8\u3051",
"Scope": "\u30b9\u30b3\u30fc\u30d7",
"Delete table": "\u8868\u306e\u524a\u9664",
"H Align": "\u6c34\u5e73\u65b9\u5411\u306e\u914d\u7f6e",
"Top": "\u4e0a",
"Header cell": "\u30d8\u30c3\u30c0\u30fc\u30bb\u30eb",
"Column": "\u5217",
"Row group": "\u884c\u30b0\u30eb\u30fc\u30d7",
"Cell": "\u30bb\u30eb",
"Middle": "\u4e2d\u592e",
"Cell type": "\u30bb\u30eb\u30bf\u30a4\u30d7",
"Copy row": "\u884c\u306e\u30b3\u30d4\u30fc",
"Row properties": "\u884c\u306e\u8a73\u7d30\u8a2d\u5b9a",
"Table properties": "\u8868\u306e\u8a73\u7d30\u8a2d\u5b9a",
"Bottom": "\u4e0b",
"V Align": "\u5782\u76f4\u65b9\u5411\u306e\u914d\u7f6e",
"Header": "\u30d8\u30c3\u30c0\u30fc",
"Right": "\u53f3\u5bc4\u305b",
"Insert column after": "\u53f3\u5074\u306b\u5217\u3092\u633f\u5165",
"Cols": "\u5217\u6570",
"Insert row after": "\u4e0b\u5074\u306b\u884c\u3092\u633f\u5165",
"Width": "\u5e45",
"Cell properties": "\u30bb\u30eb\u306e\u8a73\u7d30\u8a2d\u5b9a",
"Left": "\u5de6\u5bc4\u305b",
"Cut row": "\u884c\u306e\u5207\u308a\u53d6\u308a",
"Delete column": "\u5217\u306e\u524a\u9664",
"Center": "\u4e2d\u592e\u63c3\u3048",
"Merge cells": "\u30bb\u30eb\u306e\u7d50\u5408",
"Insert template": "\u30c6\u30f3\u30d7\u30ec\u30fc\u30c8\u306e\u633f\u5165",
"Templates": "\u30c6\u30f3\u30d7\u30ec\u30fc\u30c8\u540d",
"Background color": "\u80cc\u666f\u8272",
"Custom...": "\u30ab\u30b9\u30bf\u30e0...",
"Custom color": "\u30ab\u30b9\u30bf\u30e0\u30ab\u30e9\u30fc",
"No color": "\u30ab\u30e9\u30fc\u306a\u3057",
"Text color": "\u6587\u5b57\u306e\u8272",
"Show blocks": "\u6587\u7ae0\u306e\u533a\u5207\u308a\u3092\u70b9\u7dda\u3067\u8868\u793a",
"Show invisible characters": "\u4e0d\u53ef\u8996\u6587\u5b57\u3092\u8868\u793a",
"Words: {0}": "\u5358\u8a9e\u6570: {0}",
"Insert": "\u633f\u5165",
"File": "\u30d5\u30a1\u30a4\u30eb",
"Edit": "\u7de8\u96c6",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u66f8\u5f0f\u4ed8\u304d\u30c6\u30ad\u30b9\u30c8\u306e\u7de8\u96c6\u753b\u9762\u3002ALT-F9\u3067\u30e1\u30cb\u30e5\u30fc\u3001ALT-F10\u3067\u30c4\u30fc\u30eb\u30d0\u30fc\u3001ALT-0\u3067\u30d8\u30eb\u30d7\u304c\u8868\u793a\u3055\u308c\u307e\u3059\u3002",
"Tools": "\u30c4\u30fc\u30eb",
"View": "\u8868\u793a",
"Table": "\u8868",
"Format": "\u66f8\u5f0f"
});