tinymce.addI18n('ml_IN',{
"Cut": "\u0d2e\u0d41\u0d31\u0d3f\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15 ",
"Header 2": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d\u200c2",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u0d28\u0d3f\u0d19\u0d4d\u0d19\u0d33\u0d41\u0d1f\u0d46 \u0d2c\u0d4d\u0d30\u0d4c\u0d38\u0d30\u0d4d\u200d \u0d15\u0d4d\u0d32\u0d3f\u0d2a\u0d4d\u0d2a\u0d4d\u0d2c\u0d4b\u0d30\u0d4d\u200d\u0d21\u0d3f\u0d32\u0d47\u0d15\u0d4d\u0d15\u0d4d \u0d2a\u0d4d\u0d30\u0d35\u0d47\u0d36\u0d28\u0d02 \u0d28\u0d32\u0d4d\u200d\u0d15\u0d41\u0d28\u0d4d\u0d28\u0d3f\u0d32\u0d4d\u0d32. \u0d26\u0d2f\u0d35\u0d41 \u0d1a\u0d46\u0d2f\u0d4d\u0d24 CTRL+X\/C\/V \u0d37\u0d4b\u0d30\u0d4d\u200d\u0d1f\u0d4d\u0d1f\u0d4d\u0d15\u0d1f\u0d4d\u0d1f\u0d41\u0d15\u0d33\u0d4d\u200d \u0d09\u0d2a\u0d2f\u0d4b\u0d17\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Div": "\u0d21\u0d3f\u0d35\u0d4d",
"Paste": "\u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Close": "\u0d05\u0d1f\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Font Family": "\u0d2b\u0d4b\u0d23\u0d4d\u0d1f\u0d4d \u0d15\u0d41\u0d1f\u0d41\u0d02\u0d2c\u0d02",
"Pre": "\u0d2a\u0d4d\u0d30\u0d40",
"Align right": "\u0d35\u0d32\u0d24\u0d4d\u0d24\u0d4b\u0d1f\u0d4d\u0d1f\u0d41\u0d4d \u0d2a\u0d3f\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"New document": "\u0d2a\u0d41\u0d24\u0d3f\u0d2f \u0d30\u0d1a\u0d28",
"Blockquote": "\u0d15\u0d42\u0d1f\u0d4d\u0d1f\u0d09\u0d26\u0d4d\u0d27\u0d30\u0d23\u0d3f",
"Numbered list": "\u0d0e\u0d23\u0d4d\u0d23\u0d2e\u0d3f\u0d1f\u0d4d\u0d1f \u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15",
"Increase indent": "\u0d35\u0d3f\u0d1f\u0d35\u0d41\u0d4d \u0d15\u0d42\u0d1f\u0d4d\u0d1f\u0d41\u0d15",
"Formats": "\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d41\u0d02\u0d2e\u0d1f\u0d4d\u0d1f\u0d41\u0d02",
"Headers": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d41\u0d15\u0d33\u0d4d\u200d",
"Select all": "\u0d0e\u0d32\u0d4d\u0d32\u0d3e\u0d02 \u0d24\u0d3f\u0d30\u0d1e\u0d4d\u0d1e\u0d46\u0d1f\u0d41\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Header 3": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d\u200c3",
"Blocks": "\u0d15\u0d42\u0d1f\u0d4d\u0d1f\u0d02",
"Undo": "\u0d1a\u0d46\u0d2f\u0d4d\u0d24\u0d24\u0d4d \u0d24\u0d3f\u0d30\u0d3f\u0d1a\u0d4d\u0d1a\u0d46\u0d1f\u0d41\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Strikethrough": "\u0d35\u0d46\u0d1f\u0d4d\u0d1f\u0d41\u0d15",
"Bullet list": "\u0d05\u0d1f\u0d2f\u0d3e\u0d33\u0d2e\u0d3f\u0d1f\u0d4d\u0d1f \u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15",
"Header 1": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d\u200c1",
"Superscript": "\u0d38\u0d42\u0d2a\u0d4d\u0d2a\u0d30\u0d4d\u200d\u0d38\u0d4d\u0d15\u0d4d\u0d30\u0d3f\u0d2a\u0d4d\u0d31\u0d4d\u0d31\u0d4d",
"Clear formatting": "\u0d35\u0d46\u0d1f\u0d3f\u0d2a\u0d4d\u0d2a\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Font Sizes": "\u0d2b\u0d4b\u0d23\u0d4d\u0d1f\u0d4d \u0d35\u0d32\u0d3f\u0d2a\u0d4d\u0d2a\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Subscript": "\u0d38\u0d2c\u0d4d\u200c\u0d38\u0d4d\u0d15\u0d4d\u0d30\u0d3f\u0d2a\u0d4d\u0d31\u0d4d\u0d31\u0d4d",
"Header 6": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d\u200c6",
"Redo": "\u0d35\u0d40\u0d23\u0d4d\u0d1f\u0d41\u0d02 \u0d1a\u0d46\u0d2f\u0d4d\u0d2f\u0d41\u0d15",
"Paragraph": "\u0d16\u0d23\u0d4d\u200c\u0d21\u0d3f\u0d15",
"Ok": "\u0d36\u0d30\u0d3f",
"Bold": "\u0d15\u0d28\u0d2a\u0d4d\u0d2a\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Code": "\u0d15\u0d4b\u0d21\u0d4d",
"Italic": "\u0d1a\u0d46\u0d30\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Align center": "\u0d28\u0d1f\u0d41\u0d35\u0d3f\u0d32\u0d4b\u0d1f\u0d4d\u0d1f\u0d41\u0d4d \u0d2a\u0d3f\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Header 5": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d\u200c5",
"Decrease indent": "\u0d35\u0d3f\u0d1f\u0d35\u0d41\u0d4d \u0d15\u0d41\u0d31\u0d2f\u0d4d\u0d15\u0d4d\u0d15\u0d41\u0d15 ",
"Header 4": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d\u200c4",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d2a\u0d4d\u0d2a\u0d4d \u0d07\u0d2a\u0d4d\u0d2a\u0d4b\u0d33\u0d4d\u200d \u0d32\u0d33\u0d3f\u0d24\u0d2e\u0d3e\u0d2f \u0d1f\u0d46\u0d15\u0d4d\u0d38\u0d4d\u0d31\u0d4d\u0d31\u0d4d\u200c  \u0d06\u0d2f\u0d3f\u0d1f\u0d4d\u0d1f\u0d3e\u0d23\u0d4d. \u0d28\u0d3f\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d \u0d08 \u0d38\u0d57\u0d15\u0d30\u0d4d\u0d2f\u0d02 \u0d2e\u0d3e\u0d31\u0d4d\u0d31\u0d41\u0d28\u0d4d\u0d28\u0d24\u0d4d \u0d35\u0d30\u0d46 \u0d09\u0d33\u0d4d\u0d33\u0d1f\u0d15\u0d4d\u0d15\u0d02 \u0d32\u0d33\u0d3f\u0d24 \u0d1f\u0d46\u0d15\u0d4d\u0d38\u0d4d\u0d31\u0d4d\u0d31\u0d4d\u200c \u0d06\u0d2f\u0d3f\u0d1f\u0d4d\u0d1f\u0d3e\u0d2f\u0d3f\u0d30\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d02 \u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d28\u0d4d\u0d28\u0d24\u0d4d. ",
"Underline": "\u0d05\u0d1f\u0d3f\u0d35\u0d30\u0d2f\u0d3f\u0d1f\u0d41\u0d15",
"Cancel": "\u0d31\u0d26\u0d4d\u0d26\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Justify": "\u0d38\u0d28\u0d4d\u0d24\u0d41\u0d32\u0d3f\u0d24\u0d2e\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Inline": "\u0d35\u0d30\u0d3f\u0d2f\u0d3f\u0d32\u0d4d\u200d",
"Copy": "\u0d2a\u0d15\u0d30\u0d4d\u200d\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Align left": "\u0d07\u0d1f\u0d24\u0d4d\u0d24\u0d4b\u0d1f\u0d4d\u0d1f\u0d41\u0d4d \u0d2a\u0d3f\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Visual aids": "\u0d26\u0d43\u0d36\u0d4d\u0d2f\u0d38\u0d39\u0d3e\u0d2f\u0d3f\u0d15\u0d33\u0d4d\u200d",
"Lower Greek": "\u0d1a\u0d46\u0d31\u0d3f\u0d2f \u0d17\u0d4d\u0d30\u0d40\u0d15\u0d4d\u0d15\u0d4d\u200c \u0d05\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Square": "\u0d38\u0d2e\u0d1a\u0d24\u0d41\u0d30\u0d02",
"Default": "\u0d09\u0d2a\u0d47\u0d15\u0d4d\u0d37",
"Lower Alpha": "\u0d1a\u0d46\u0d31\u0d3f\u0d2f \u0d05\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Circle": "\u0d35\u0d1f\u0d4d\u0d1f\u0d02",
"Disc": "\u0d1a\u0d15\u0d4d\u0d30\u0d02",
"Upper Alpha": "\u0d35\u0d32\u0d3f\u0d2f \u0d06\u0d32\u0d4d\u0d2b\u0d3e \u0d05\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Upper Roman": "\u0d35\u0d32\u0d3f\u0d2f \u0d31\u0d4b\u0d2e\u0d28\u0d4d\u200d \u0d05\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Lower Roman": "\u0d1a\u0d46\u0d31\u0d3f\u0d2f \u0d31\u0d4b\u0d2e\u0d28\u0d4d\u200d \u0d05\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Name": "\u0d2a\u0d47\u0d30\u0d4d",
"Anchor": "\u0d28\u0d19\u0d4d\u0d15\u0d42\u0d30\u0d02",
"You have unsaved changes are you sure you want to navigate away?": "\u0d30\u0d15\u0d4d\u0d37\u0d2a\u0d4d\u0d2a\u0d46\u0d1f\u0d41\u0d24\u0d4d\u0d24\u0d3e\u0d24\u0d4d\u0d24 \u0d2e\u0d3e\u0d31\u0d4d\u0d31\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d \u0d28\u0d3f\u0d32\u0d28\u0d3f\u0d32\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d28\u0d4d\u0d28\u0d41. \u0d2a\u0d41\u0d31\u0d24\u0d4d\u0d24\u0d41 \u0d15\u0d1f\u0d15\u0d4d\u0d15\u0d23\u0d4b?",
"Restore last draft": "\u0d2a\u0d34\u0d2f \u0d21\u0d4d\u0d30\u0d3e\u0d2b\u0d4d\u0d31\u0d4d\u0d31\u0d4d\u200c \u0d24\u0d3f\u0d30\u0d3f\u0d1a\u0d4d\u0d1a\u0d41 \u0d15\u0d4a\u0d23\u0d4d\u0d1f\u0d4d \u0d35\u0d30\u0d3f\u0d15",
"Special character": "\u0d2a\u0d4d\u0d30\u0d24\u0d4d\u0d2f\u0d47\u0d15\u0d3e\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Source code": "\u0d38\u0d4b\u0d34\u0d4d\u0d38\u0d4d \u0d15\u0d4b\u0d21\u0d4d",
"Right to left": "\u0d35\u0d32\u0d24\u0d4d\u0d24\u0d41 \u0d28\u0d3f\u0d28\u0d4d\u0d28\u0d41\u0d02 \u0d07\u0d1f\u0d24\u0d4d\u0d24\u0d47\u0d15\u0d4d\u0d15\u0d4d",
"Left to right": "\u0d07\u0d1f\u0d24\u0d4d\u0d24\u0d4d \u0d28\u0d3f\u0d28\u0d4d\u0d28\u0d41\u0d02 \u0d35\u0d32\u0d24\u0d4d\u0d24\u0d47\u0d15\u0d4d\u0d15\u0d4d",
"Emoticons": "\u0d1a\u0d3f\u0d39\u0d4d\u0d28 \u0d2d\u0d3e\u0d37",
"Robots": "\u0d2f\u0d28\u0d4d\u0d24\u0d4d\u0d30\u0d2e\u0d28\u0d41\u0d37\u0d4d\u0d2f\u0d28\u0d4d\u200d",
"Document properties": "\u0d21\u0d4b\u0d15\u0d4d\u0d15\u0d41\u0d2e\u0d46\u0d28\u0d4d\u0d31\u0d4d \u0d17\u0d41\u0d23\u0d35\u0d3f\u0d36\u0d47\u0d37\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Title": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d",
"Keywords": "\u0d38\u0d42\u0d1a\u0d15\u0d2a\u0d26\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Encoding": "\u0d0e\u0d7b\u0d15\u0d4b\u0d21\u0d3f\u0d02\u0d17\u0d4d",
"Description": "\u0d35\u0d3f\u0d35\u0d30\u0d23\u0d02",
"Author": "\u0d32\u0d47\u0d16\u0d15\u0d28\u0d4d\u200d",
"Fullscreen": "\u0d2b\u0d41\u0d33\u0d4d\u200d\u0d38\u0d4d\u0d15\u0d4d\u0d30\u0d40\u0d28\u0d4d\u200d",
"Horizontal line": "\u0d36\u0d3e\u0d16\u0d3e\u0d2a\u0d3e\u0d24",
"Horizontal space": "\u0d24\u0d3f\u0d30\u0d36\u0d4d\u0d1a\u0d40\u0d28\u0d2e\u0d3e\u0d2f \u0d36\u0d42\u0d28\u0d4d\u0d2f\u0d38\u0d4d\u0d25\u0d32\u0d02",
"Insert\/edit image": "\u0d1a\u0d3f\u0d24\u0d4d\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15\/ \u0d24\u0d3f\u0d30\u0d41\u0d24\u0d4d\u0d24\u0d41\u0d15",
"General": "\u0d2a\u0d4a\u0d24\u0d41\u0d35\u0d3e\u0d2f",
"Advanced": "\u0d2a\u0d41\u0d30\u0d47\u0d3e\u0d17\u0d2e\u0d3f\u0d1a\u0d4d\u0d1a",
"Source": "\u0d09\u0d31\u0d35\u0d3f\u0d1f\u0d02",
"Border": "\u0d05\u0d24\u0d3f\u0d30\u0d4d",
"Constrain proportions": "\u0d28\u0d3f\u0d30\u0d4d\u200d\u0d2c\u0d28\u0d4d\u0d27\u0d3e\u0d28\u0d41\u0d2a\u0d3e\u0d24\u0d02",
"Vertical space": "\u0d32\u0d02\u0d2c\u0d2e\u0d3e\u0d28\u0d2e\u0d3e\u0d2f \u0d36\u0d42\u0d28\u0d4d\u0d2f\u0d38\u0d4d\u0d25\u0d32\u0d02",
"Image description": "\u0d1a\u0d3f\u0d24\u0d4d\u0d30 \u0d35\u0d3f\u0d35\u0d30\u0d23\u0d02",
"Style": "\u0d36\u0d48\u0d32\u0d3f",
"Dimensions": "\u0d05\u0d33\u0d35\u0d41\u0d15\u0d33\u0d4d\u200d",
"Insert image": "\u0d1a\u0d3f\u0d24\u0d4d\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Insert date\/time": "\u0d38\u0d2e\u0d2f\u0d02\/\u0d24\u0d40\u0d2f\u0d24\u0d3f \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Remove link": "\u0d15\u0d23\u0d4d\u0d23\u0d3f \u0d12\u0d34\u0d3f\u0d35\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Url": "\u0d2f\u0d42\u0d06\u0d30\u0d4d\u200d\u0d0e\u0d32\u0d4d\u200d",
"Text to display": "\u0d15\u0d3e\u0d23\u0d3f\u0d15\u0d4d\u0d15\u0d3e\u0d28\u0d41\u0d33\u0d4d\u0d33 \u0d05\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Anchors": "\u0d28\u0d19\u0d4d\u0d15\u0d42\u0d30\u0d19\u0d4d\u0d19\u0d7e",
"Insert link": "\u0d15\u0d23\u0d4d\u0d23\u0d3f \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"New window": "\u0d2a\u0d41\u0d24\u0d3f\u0d2f \u0d1c\u0d3e\u0d32\u0d15\u0d02",
"None": "\u0d12\u0d28\u0d4d\u0d28\u0d41\u0d2e\u0d3f\u0d32\u0d4d\u0d32",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?",
"Target": "\u0d32\u0d15\u0d4d\u0d37\u0d4d\u0d2f\u0d02",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",
"Insert\/edit link": "\u0d15\u0d23\u0d4d\u0d23\u0d3f \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15\/ \u0d15\u0d23\u0d4d\u0d23\u0d3f \u0d24\u0d3f\u0d30\u0d41\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Insert\/edit video": "\u0d35\u0d40\u0d21\u0d3f\u0d2f\u0d4b \u0d1a\u0d46\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15\/\u0d35\u0d40\u0d21\u0d3f\u0d2f\u0d4b \u0d24\u0d3f\u0d30\u0d41\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Poster": "\u0d1a\u0d3f\u0d24\u0d4d\u0d30\u0d02",
"Alternative source": "\u0d07\u0d24\u0d30 \u0d38\u0d4d\u0d30\u0d4b\u0d24\u0d38\u0d4d\u0d38\u0d4d\u200c",
"Paste your embed code below:": "\u0d28\u0d3f\u0d19\u0d33\u0d41\u0d1f\u0d46 \u0d0e\u0d02\u0d2c\u0d21\u0d4d \u0d15\u0d4b\u0d21\u0d4d \u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Insert video": "\u0d35\u0d40\u0d21\u0d3f\u0d2f\u0d4b \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Embed": "\u0d0e\u0d02\u0d2c\u0d46\u0d21\u0d4d\u200c",
"Nonbreaking space": "\u0d2d\u0d02\u0d17\u0d2e\u0d3f\u0d32\u0d4d\u0d32\u0d3e\u0d24\u0d4d\u0d24 \u0d36\u0d42\u0d28\u0d4d\u0d2f\u0d38\u0d4d\u0d25\u0d32\u0d02",
"Page break": "\u0d24\u0d3e\u0d33\u0d4d\u200d \u0d2d\u0d02\u0d17\u0d02",
"Paste as text": "\u0d1f\u0d46\u0d15\u0d4d\u0d38\u0d4d\u0d31\u0d4d\u0d31\u0d4d\u200c \u0d06\u0d2f\u0d3f \u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Preview": "\u0d15\u0d30\u0d1f\u0d41\u0d2a\u0d24\u0d3f\u0d2a\u0d4d\u0d2a\u0d4d",
"Print": "\u0d05\u0d1a\u0d4d\u0d1a\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Save": "\u0d30\u0d15\u0d4d\u0d37\u0d2a\u0d4d\u0d2a\u0d46\u0d1f\u0d41\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Could not find the specified string.": "\u0d09\u0d26\u0d4d\u0d26\u0d47\u0d36\u0d3f\u0d1a\u0d4d\u0d1a \u0d35\u0d3e\u0d1a\u0d15\u0d02 \u0d15\u0d23\u0d4d\u0d1f\u0d41\u0d2a\u0d3f\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d3e\u0d28\u0d3e\u0d2f\u0d3f\u0d32\u0d4d\u0d32.",
"Replace": "\u0d2e\u0d3e\u0d31\u0d4d\u0d31\u0d3f\u0d35\u0d2f\u0d4d\u200c\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Next": "\u0d2e\u0d41\u0d28\u0d4d\u0d28\u0d4b\u0d1f\u0d4d\u0d1f\u0d4d",
"Whole words": "\u0d0e\u0d32\u0d4d\u0d32\u0d3e \u0d35\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15\u0d33\u0d41\u0d02",
"Find and replace": "\u0d15\u0d23\u0d4d\u0d1f\u0d41\u0d2a\u0d3f\u0d1f\u0d3f\u0d1a\u0d4d\u0d1a\u0d41 \u0d2e\u0d3e\u0d31\u0d4d\u0d31\u0d3f\u0d35\u0d2f\u0d4d\u200c\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Replace with": "\u0d2e\u0d31\u0d4d\u0d31\u0d4a\u0d28\u0d4d\u0d28\u0d3f\u0d28\u0d4b\u0d1f\u0d4d \u0d2e\u0d3e\u0d31\u0d4d\u0d31\u0d3f\u0d35\u0d2f\u0d4d\u200c\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Find": "\u0d24\u0d3f\u0d30\u0d2f\u0d41\u0d15",
"Replace all": "\u0d0e\u0d32\u0d4d\u0d32\u0d3e\u0d02 \u0d2e\u0d3e\u0d31\u0d4d\u0d31\u0d3f\u0d35\u0d2f\u0d4d\u200c\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Match case": "\u0d24\u0d41\u0d32\u0d4d\u0d2f\u0d2e\u0d3e\u0d2f\u0d24\u0d4d",
"Prev": "\u0d2a\u0d3f\u0d28\u0d4d\u0d28\u0d4b\u0d1f\u0d4d\u0d1f\u0d4d",
"Spellcheck": "\u0d05\u0d15\u0d4d\u0d37\u0d30\u0d35\u0d3f\u0d28\u0d4d\u0d2f\u0d3e\u0d38\u0d02 \u0d2a\u0d30\u0d3f\u0d36\u0d4b\u0d27\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Finish": "\u0d05\u0d35\u0d38\u0d3e\u0d28\u0d3f\u0d2a\u0d4d\u0d2a\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Ignore all": "\u0d0e\u0d32\u0d4d\u0d32\u0d3e\u0d02 \u0d05\u0d35\u0d17\u0d23\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Ignore": "\u0d05\u0d35\u0d17\u0d23\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Insert row before": "\u0d2e\u0d41\u0d2e\u0d4d\u0d2a\u0d3f\u0d32\u0d4d\u200d \u0d2a\u0d41\u0d24\u0d3f\u0d2f \u0d28\u0d3f\u0d30 \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Rows": "\u0d28\u0d3f\u0d30\u0d15\u0d33\u0d4d\u200d",
"Height": "\u0d09\u0d2f\u0d30\u0d02",
"Paste row after": "\u0d28\u0d3f\u0d30 \u0d36\u0d47\u0d37\u0d02 \u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Alignment": "\u0d05\u0d23\u0d3f\u0d28\u0d3f\u0d30\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Column group": "\u0d35\u0d30\u0d3f \u0d17\u0d23\u0d02",
"Row": "\u0d28\u0d3f\u0d30",
"Insert column before": "\u0d2e\u0d41\u0d2e\u0d4d\u0d2a\u0d3f\u0d32\u0d4d\u200d \u0d2a\u0d41\u0d24\u0d3f\u0d2f \u0d35\u0d30\u0d3f \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Split cell": "\u0d05\u0d31\u0d15\u0d33\u0d4d\u200d \u0d35\u0d3f\u0d2d\u0d1c\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Cell padding": "\u0d05\u0d31 \u0d2a\u0d3e\u0d21\u0d3f\u0d02\u0d17\u0d4d",
"Cell spacing": "\u0d05\u0d31\u0d15\u0d33\u0d4d\u200d \u0d24\u0d2e\u0d4d\u0d2e\u0d3f\u0d32\u0d41\u0d33\u0d4d\u0d33 \u0d05\u0d15\u0d32\u0d02",
"Row type": "\u0d28\u0d3f\u0d30 \u0d2e\u0d3e\u0d24\u0d43\u0d15",
"Insert table": "\u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15 \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Body": "\u0d36\u0d30\u0d40\u0d30\u0d02",
"Caption": "\u0d24\u0d32\u0d35\u0d3e\u0d1a\u0d15\u0d02",
"Footer": "\u0d05\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d31\u0d3f\u0d2a\u0d4d\u0d2a\u0d4d\u200c",
"Delete row": "\u0d28\u0d3f\u0d30 \u0d24\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d33\u0d2f\u0d41\u0d15",
"Paste row before": "\u0d28\u0d3f\u0d30 \u0d2e\u0d41\u0d2e\u0d4d\u0d2a\u0d3e\u0d2f\u0d3f \u0d12\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Scope": "\u0d35\u0d4d\u0d2f\u0d3e\u0d2a\u0d4d\u200c\u0d24\u0d3f",
"Delete table": "\u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15 \u0d15\u0d33\u0d2f\u0d41\u0d15",
"Header cell": "\u0d24\u0d32 \u0d05\u0d31",
"Column": "\u0d35\u0d30\u0d3f",
"Cell": "\u0d05\u0d31",
"Header": "\u0d24\u0d32\u0d15\u0d4d\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d4d\u200c",
"Cell type": "\u0d05\u0d31\u0d2f\u0d41\u0d1f\u0d46 \u0d2e\u0d3e\u0d24\u0d43\u0d15",
"Copy row": "\u0d28\u0d3f\u0d30 \u0d2a\u0d15\u0d30\u0d4d\u200d\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Row properties": "\u0d28\u0d3f\u0d30\u0d2f\u0d41\u0d1f\u0d46 \u0d38\u0d4d\u0d35\u0d2d\u0d3e\u0d35\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Table properties": "\u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d2f\u0d41\u0d1f\u0d46 \u0d38\u0d4d\u0d35\u0d2d\u0d3e\u0d35\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Row group": "\u0d28\u0d3f\u0d30 \u0d17\u0d23\u0d02",
"Right": "\u0d35\u0d32\u0d24\u0d4d",
"Insert column after": "\u0d2a\u0d3f\u0d31\u0d15\u0d3f\u0d32\u0d4d\u200d \u0d2a\u0d41\u0d24\u0d3f\u0d2f \u0d35\u0d30\u0d3f \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Cols": "\u0d35\u0d30\u0d3f\u0d15\u0d33\u0d4d\u200d",
"Insert row after": "\u0d2a\u0d3f\u0d31\u0d15\u0d3f\u0d32\u0d4d\u200d \u0d2a\u0d41\u0d24\u0d3f\u0d2f \u0d28\u0d3f\u0d30 \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Width": "\u0d28\u0d40\u0d33\u0d02",
"Cell properties": "\u0d05\u0d31\u0d2f\u0d41\u0d1f\u0d46 \u0d38\u0d4d\u0d35\u0d2d\u0d3e\u0d35\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d",
"Left": "\u0d07\u0d1f\u0d24\u0d4d",
"Cut row": "\u0d28\u0d3f\u0d30 \u0d35\u0d46\u0d1f\u0d4d\u0d1f\u0d3f\u0d2e\u0d3e\u0d31\u0d4d\u0d31\u0d41\u0d15",
"Delete column": "\u0d35\u0d30\u0d3f \u0d24\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d33\u0d2f\u0d41\u0d15",
"Center": "\u0d28\u0d1f\u0d41\u0d35\u0d3f\u0d32\u0d4d\u200d",
"Merge cells": "\u0d05\u0d31\u0d15\u0d33\u0d4d\u200d \u0d15\u0d42\u0d1f\u0d4d\u0d1f\u0d3f\u0d2f\u0d4b\u0d1c\u0d3f\u0d2a\u0d4d\u0d2a\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Insert template": "\u0d05\u0d1a\u0d4d\u0d1a\u0d41\u0d15\u0d33\u0d4d\u200d \u0d1a\u0d47\u0d30\u0d4d\u200d\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Templates": "\u0d05\u0d1a\u0d4d\u0d1a\u0d41\u0d15\u0d33\u0d4d\u200d",
"Background color": "\u0d2a\u0d36\u0d4d\u0d1a\u0d3e\u0d24\u0d4d\u0d24\u0d32 \u0d28\u0d3f\u0d31\u0d02",
"Text color": "\u0d05\u0d15\u0d4d\u0d37\u0d30 \u0d28\u0d3f\u0d31\u0d02",
"Show blocks": "\u0d2c\u0d4d\u0d32\u0d4b\u0d15\u0d4d\u0d15\u0d41\u0d15\u0d33\u0d4d\u200d \u0d15\u0d3e\u0d23\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Show invisible characters": "\u0d05\u0d26\u0d43\u0d36\u0d4d\u0d2f\u0d2e\u0d3e\u0d2f \u0d05\u0d15\u0d4d\u0d37\u0d30\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d \u0d15\u0d3e\u0d23\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"Words: {0}": "\u0d35\u0d3e\u0d15\u0d4d\u0d15\u0d41\u0d15\u0d33\u0d4d\u200d: {0}",
"Insert": "\u0d2a\u0d24\u0d3f\u0d15\u0d4d\u0d15\u0d41\u0d15",
"File": "\u0d2b\u0d2f\u0d32\u0d4d\u200d",
"Edit": "\u0d24\u0d3f\u0d30\u0d41\u0d24\u0d4d\u0d24\u0d41\u0d15",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u0d31\u0d3f\u0d1a\u0d4d\u0d1a\u0d4d \u0d1f\u0d46\u0d15\u0d4d\u0d38\u0d4d\u0d31\u0d4d\u0d31\u0d4d\u200c \u0d2e\u0d47\u0d16\u0d32.  \u0d35\u0d3f\u0d37\u0d2f \u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15 \u0d15\u0d4d\u0d15\u0d3e\u0d2f\u0d3f ALT-F9  \u0d05\u0d2e\u0d30\u0d4d\u0d24\u0d4d\u0d24\u0d41\u0d15. \u0d09\u0d2a\u0d15\u0d30\u0d23 \u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15\u0d15\u0d4d\u0d15\u0d3e\u0d2f\u0d3f ALT-F10 \u0d05\u0d2e\u0d30\u0d4d\u200d\u0d24\u0d4d\u0d24\u0d41\u0d15.  \u0d38\u0d39\u0d3e\u0d2f\u0d24\u0d4d\u0d24\u0d3f\u0d28\u0d41 ALT-0 \u0d09\u0d02",
"Tools": "\u0d09\u0d2a\u0d15\u0d30\u0d23\u0d19\u0d4d\u0d19\u0d33\u0d4d\u200d ",
"View": "\u0d26\u0d30\u0d4d\u200d\u0d36\u0d28\u0d02",
"Table": "\u0d2a\u0d1f\u0d4d\u0d1f\u0d3f\u0d15",
"Format": "\u0d15\u0d46\u0d1f\u0d4d\u0d1f\u0d41\u0d02\u0d2e\u0d1f\u0d4d\u0d1f\u0d41\u0d02"
});