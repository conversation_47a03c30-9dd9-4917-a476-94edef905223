<style type="text/css">
	#VIS_ID_CONTAINER .chart {
		shape-rendering: crispEdges;
	}

	#VIS_ID_CONTAINER .mini text {
		font: 9px sans-serif;
	}

	#VIS_ID_CONTAINER .main text {
		font: 12px sans-serif;
	}

	#VIS_ID_CONTAINER .miniItem0 {
		fill: darksalmon;
		fill-opacity: .3;
		stroke-width: 6;
	}

	#VIS_ID_CONTAINER .miniItem1 {
		fill: darkolivegreen;
		fill-opacity: .3;
		stroke-width: 6;
	}

	#VIS_ID_CONTAINER .miniItem2 {
		fill: slategray;
		fill-opacity: .3;
		stroke-width: 6;
	}

	#VIS_ID_CONTAINER .brush .extent {
		stroke: gray;
		fill: dodgerblue;
		fill-opacity: .365;
	}

	#VIS_ID_CONTAINER .axisX path, .axisX line {
		fill: none;
		stroke: black;
		shape-rendering: crispEdges;
	}

	#VIS_ID_CONTAINER .axisX text {
		font-size: 10px;
	}
</style>

<div id="VIS_ID_CONTAINER">
	<div id="VIS_ID_HOLDER">
		<div id="VIS_ID"></div>
	</div>
</div>

<script type="text/javascript" src="d3.v3.min.js"></script>

<script type="text/javascript">
	(function ExecuteVIS_ID() {
		function validate() {
			return VIS_COLUMNS.length > 3
				&& VIS_COLUMNS[2].type == 'DateTime'
				&& VIS_COLUMNS[3].type == 'DateTime';
		}

		var width = 720, height = 500;

		var util = window.ReportScripting, vis,
			isThumbnails = (document.URL === 'about:blank');

		if (!util.validate("VIS_ID", VIS_FORMJSASTATUS, VIS_CONTEXT, { d3: true, svg: true, input: validate }))
			return;

		var lanes = [], items = [], laneLength;
		var timeBegin, timeEnd, laneIndex = -1;

		vis = new util("VIS_ID", VIS_FORMJSASTATUS, VIS_COLUMNS, VIS_ROWS, VIS_CONTEXT);

		var rows = vis.getRows(true);
		for (var i = 0; i < rows.length; i++) {
			if (lanes.indexOf(rows[i][0]) < 0) {
				lanes.push(rows[i][0]);
				laneIndex++;
			}

			var obj = { "lane": laneIndex };
			for (var j = 1; j < VIS_COLUMNS.length; j++)
				vis.unitValue(obj, j, { ri: i });

			obj.start = Number(rows[i][2]);
			if (!timeBegin || timeBegin > obj.start)
				timeBegin = obj.start;

			obj.end = Number(rows[i][3]);
			if (!timeEnd || timeEnd < obj.end)
				timeEnd = obj.end;

			obj._identity = (Math.random() * 1000000) | 0;

			items.push(obj);
		}

		laneLength = lanes.length;

		width = vis.getWidth();
		height = vis.getHeight();

		//bounds
		function getW() { return width - m[1] - m[3]; }
		function getH() { return height - m[0] - m[2]; }

		var m = [16, 5, 15, 60], //top right bottom left
			minLH = 7, lPortion = 5;


		if (!VIS_CONTEXT.isDashboard && ((getH() / lPortion) / laneLength < minLH)) {
			height = minLH * laneLength * lPortion;
		}

		if (getH() / lPortion > minLH) {
			minLH = (height / lPortion) / laneLength;
		}

		jq$("#VIS_ID").width(width).height(height);

		var w = getW(), h = getH(),
			miniHeight = laneLength * minLH,
			mainHeight = h - miniHeight;

		var x = d3.time.scale()
				.domain([new Date(timeBegin), new Date(timeEnd)])
				.range([0, w]);
		var y1 = d3.scale.linear()
			.domain([0, laneLength])
			.range([0, mainHeight]);
		var y2 = d3.scale.linear()
			.domain([0, laneLength])
			.range([0, miniHeight]);

		var chart = d3.select("#VIS_ID")
			.append("svg")
			.attr("width", width)
			.attr("height", height)
			.attr("class", "chart");

		chart.append("defs").append("clipPath")
			.attr("id", "VIS_ID_CLIP")
			.append("rect")
			.attr("width", w)
			.attr("height", mainHeight);

		function createAxislabel(pane) {
			return pane.append("g").attr("class", "axisX axisX2").append("text");
		}

		var box1 = util.labelBBox(chart, "XX XXXXXXX XXXX", createAxislabel).width,
			box2 = util.labelBBox(chart, "XX XXX 'XX", createAxislabel).width,
			box3 = util.labelBBox(chart, "XX/XX/XX", createAxislabel).width,
			box4 = util.labelBBox(chart, "XXX XXXX", createAxislabel).width,
			box5 = util.labelBBox(chart, "XXX 'XX", createAxislabel).width,
			box6 = util.labelBBox(chart, "XX/XX", createAxislabel).width;

		var sformat = (w / box1) > 9
				? "%e %B %Y"
				: (w / box2) > 9
					? "%e %b '%y"
					: (w / box3) > 9
						? "%m/%e/%y" : "%m/%y",
			sformat2 = (w / box4) > 9
				? "%b %Y"
				: (w / box5) > 9
					? "%b '%y"
					: (w / box6) > 9
						? "%m/%y" : "'%y";

		var m3 = 0;
		for (i = 0; i < lanes.length; i++) {
			var t = util.labelBBox(chart, lanes[i], function (pane) { return pane.append("g").attr("class", "main").append("text").attr("class", "laneText"); }).width;
			m3 = m3 > t ? m3 : t;
		}
		m[3] = m3 + 5;

		var main = chart.append("g")
			.attr("transform", "translate(" + m[3] + "," + m[0] + ")")
			.attr("width", w)
			.attr("height", mainHeight)
			.attr("class", "main");

		var mini = chart.append("g")
			.attr("transform", "translate(" + m[3] + "," + (mainHeight + m[0]) + ")")
			.attr("width", w)
			.attr("height", miniHeight)
			.attr("class", "mini");

		//main lanes and texts
		main.append("g").selectAll(".laneLines")
			.data(items)
			.enter().append("line")
			.attr("x1", 0)
			.attr("y1", function(d) { return y1(d.lane); })
			.attr("x2", w)
			.attr("y2", function(d) { return y1(d.lane); })
			.attr("stroke", "lightgray");

		main.append("g").selectAll(".laneText")
			.data(lanes)
			.enter().append("text")
			.text(function (d) { return d; })
			.attr("x", -m[1])
			.attr("y", function (d, i2) { return y1(i2 + .5); })
			.attr("dy", ".5ex")
			.attr("text-anchor", "end")
			.attr("class", "laneText");

		//mini lanes and texts
		mini.append("g").selectAll(".laneLines")
			.data(items)
			.enter().append("line")
			.attr("x1", 0)
			.attr("y1", function (d) { return y2(d.lane); })
			.attr("x2", w)
			.attr("y2", function (d) { return y2(d.lane); })
			.attr("stroke", "lightgray");

		mini.append("g").selectAll(".laneText")
			.data(lanes)
			.enter().append("text")
			.text(function () { return ""; })
			.attr("x", -m[1])
			.attr("y", function (d, i2) { return y2(i2 + .5); })
			.attr("dy", ".5ex")
			.attr("text-anchor", "end")
			.attr("class", "laneText");

		var itemRects = main.append("g")
			.attr("clip-path", "url(#VIS_ID_CLIP)");

		//mini item rects
		mini.append("g").selectAll("miniItems")
			.data(items)
			.enter().append("rect")
			.attr("class", function (d) { return "miniItem" + (d.lane % 3); })
			.attr("x", function (d) { return x(d.start); })
			.attr("y", function (d) { return y2(d.lane + .5) - (.8 * minLH) / 2; })
			.attr("width", function (d) { var r = x(d.end) - x(d.start); return r > 0 ? r : 0; })
			.attr("height", .8 * minLH);

		// mini lane axis
		var axisX = d3.svg.axis()
			.scale(x)
			.orient("bottom")
			.ticks(5)
			.tickFormat(function(d) { return d3.time.format(sformat2)(new Date(d)); });
		chart.append("g")
			.call(axisX)
			.attr('class', 'axisX')
			.attr('transform', 'translate(' + (m[3]) + ', ' + (height - 16) + ')');

		var x2 = d3.time.scale().range([0, w]);
		var axisX2 = d3.svg.axis()
			.scale(x2)
			.orient("top")
			.ticks(5)
			.tickFormat(function(d) { return d3.time.format(sformat)(new Date(d)); });

		chart.append("g")
			.call(axisX2)
			.attr('class', 'axisX axisX2')
			.attr('transform', 'translate(' + (m[3]) + ', ' + (16) + ')');

		function display() {
			var rects, labels,
				minExtent = brush.extent()[0],
				maxExtent = brush.extent()[1];

			if (minExtent.valueOf() == maxExtent.valueOf()) {
				return;
			}

			var visItems = items.filter(function (d) { return d.start < maxExtent && d.end > minExtent; });

			mini.select(".brush")
				.call(brush.extent([minExtent, maxExtent]));

			var x1 = d3.time.scale()
				.range([0, w])
				.domain([minExtent, maxExtent]);
			x2.domain([new Date(minExtent), new Date(maxExtent)]);

			chart.selectAll(".axisX2")
				.call(axisX2);

			//update main item rects
			rects = itemRects.selectAll("rect")
				.data(visItems, function (d) { return d.id ? d.id : d._identity; })
				.attr("x", function (d) { return x1(d.start); })
				.attr("width", function (d) { var r = x1(d.end) - x1(d.start); return r > 0 ? r : 0; });

			rects.enter().append("rect")
				.attr("class", function (d) { return "miniItem" + (d.lane % 3); })
				.attr("x", function (d) { return x1(d.start); })
				.attr("y", function (d) { return y1(d.lane + 0.5) - (.8 * y1(1)) / 2; })
				.attr("width", function (d) { var r = x1(d.end) - x1(d.start); return r > 0 ? r : 0; })
				.attr("height", function (d) { return .8 * y1(1); })
				.on("mouseover", function (d) {
					util.showTooltip(d, { exclusion: ["lane", "id", "start", "end", "_identity", "link"] });
					util.tip.removeClass("miniItem0 miniItem1 miniItem2")
						.addClass("miniItem" + (d.lane % 3));
				})
				.on("mouseout", function () { util.hideTooltip(); });

			rects.exit().remove();

			//update the item labels
			labels = itemRects.selectAll("text")
				.data(visItems, function (d) { return d.id ? d.id : d._identity; })
				.attr("x", function (d) { return x1(Math.max(d.start, minExtent) + 2); });

			labels.enter().append("text")
				.text(function (d) { return d.id; })
				.attr("x", function (d) { return x1(Math.max(d.start, minExtent)); })
				.attr("y", function (d) { return y1(d.lane + .5); })
				.attr("text-anchor", "start");

			labels.exit().remove();
		}

		var brush = d3.svg.brush()
			.x(x)
			.on("brush", display)
			.extent([timeBegin + (timeEnd - timeBegin) / 3, timeBegin + 2 / 3 * (timeEnd - timeBegin)]);

		mini.append("g")
			.attr("class", "x brush")
			.call(brush)
			.selectAll("rect")
			.attr("y", 1)
			.attr("height", miniHeight - 1);

		util.registerResize("VIS_ID", ExecuteVIS_ID, function () {
			jq$("#VIS_ID").empty();
		});

		display();
	})();
</script>
