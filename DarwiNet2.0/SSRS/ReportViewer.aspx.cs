using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.UI;
using System.Web.UI.WebControls;
using DarwiNet2._0.Controllers;
using DarwiNet2._0.DNetSynch;
using DarwiNet2._0.Data;
using Microsoft.Reporting.WebForms;

namespace DarwiNet2._0.SSRS
{
    public partial class ReportViewer : System.Web.UI.Page
    {
        private DnetEntities _dbContext;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                var i = Convert.ToInt32(Request.QueryString["i"]);
                var r = Convert.ToInt32(Request.QueryString["r"]);
                _dbContext = new DnetEntities();
                string serverAddress = _dbContext.ProjectSetups.Select(ps => ps.SSRSServerAddress).FirstOrDefault();
                if (string.IsNullOrEmpty(serverAddress)) serverAddress = string.Empty;
                ReportViewer1.ID = "ReportViewer1";
                ReportViewer1.ProcessingMode = ProcessingMode.Remote;
                ReportViewer1.SizeToReportContent = true;
                ReportViewer1.Width = Unit.Percentage(100);
                ReportViewer1.Height = Unit.Percentage(100);
                ReportViewer1.ServerReport.Timeout = -1;
                ReportViewer1.ShowCredentialPrompts = false;
                ReportViewer1.ShowParameterPrompts = false;
                ReportViewer1.ServerReport.ReportPath = "/DarwinNetReports/" + _dbContext.SSRSReports.First(s => s.rid == r).ReportName;
                ReportViewer1.ServerReport.DisplayName = _dbContext.SSRSReports.First(s => s.rid == r).DisplayName;
                ReportViewer1.ServerReport.ReportServerUrl = new Uri(serverAddress);
                ReportViewer1.ServerReport.SetParameters(GetParametersServer(i));    
            }
            
           // ViewBag.ReportViewer = reportViewer;
        }

        private ReportParameter[] GetParametersServer(int i)
        {
            Services.GetDB getDB = new Services.GetDB();
            var connString = getDB.GetConnection();

            var strArray = connString.Split(';');


            ReportParameter db = new ReportParameter("db", strArray[1].Replace("Database=", ""));
            ReportParameter srv = new ReportParameter("server", strArray[0].Replace("Server=", ""));
            ReportParameter usr = new ReportParameter("user", strArray[2].Replace("UID=", ""));
            ReportParameter pwd = new ReportParameter("pwd", strArray[3].Replace("PWD=", ""));

            ReportParameter p1 = new ReportParameter("company", GlobalVariables.CompanyID.ToString());
            ReportParameter p2 = new ReportParameter("client", GlobalVariables.Client.ToString());
            ReportParameter p3 = new ReportParameter("invoice", i.ToString());
            
            return new ReportParameter[] { db, srv, usr, pwd, p1, p2, p3 };

        }
    }
}